/**
 * Simple socket connection test without authentication
 */

const io = require('socket.io-client');

// Test configuration
const SOCKET_URL = 'http://localhost:3001';

async function testSimpleSocket() {
  console.log('🧪 Testing Simple Socket Connection');
  console.log('=' * 50);
  
  let socket;
  
  try {
    // Create socket connection without auth
    console.log('\n🔌 Creating socket connection...');
    socket = io(SOCKET_URL, {
      transports: ['websocket'],
      forceNew: true
    });

    // Set up event listeners
    socket.on('connect', () => {
      console.log('✅ Socket connected:', socket.id);
      
      // Test basic health check
      console.log('\n📡 Testing basic socket communication...');
      socket.emit('ping', { timestamp: Date.now() }, (response) => {
        console.log('📡 Ping response:', response);
        
        // Disconnect after successful test
        setTimeout(() => {
          console.log('\n🔌 Disconnecting...');
          socket.disconnect();
          
          setTimeout(() => {
            console.log('\n✅ Simple socket test completed successfully!');
            process.exit(0);
          }, 1000);
        }, 2000);
      });
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Connection error:', error.message);
      process.exit(1);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
    });

    socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Test interrupted, disconnecting...');
      if (socket) socket.disconnect();
      process.exit(0);
    });

    // Timeout after 15 seconds
    setTimeout(() => {
      console.log('\n⏰ Test timeout after 15 seconds');
      console.log('❌ Socket connection test failed');
      if (socket) socket.disconnect();
      process.exit(1);
    }, 15000);

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    if (socket) socket.disconnect();
    process.exit(1);
  }
}

// Run the test
console.log('🚀 Starting simple socket test...');
testSimpleSocket();
