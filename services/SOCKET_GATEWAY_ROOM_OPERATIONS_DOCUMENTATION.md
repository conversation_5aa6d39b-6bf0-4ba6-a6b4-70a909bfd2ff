# Socket Gateway Room Join/Leave Operations Documentation

## Overview

This document provides comprehensive documentation for the Socket Gateway's room join/leave functionality, including flow diagrams, Socket.io event structures, example payloads, and integration points with the Manager Service and Game Service.

## Architecture Overview

```
Client (Browser/App)
    ↓ Socket.io Events
Socket Gateway (Port 3001)
    ↓ HTTP/gRPC
Manager Service (Port 3002) ← Room State Management
    ↓ gRPC
Game Service (Port 8080) ← Game Logic
```

## 1. Room Join Process Flow

### Step-by-Step Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant SG as Socket Gateway
    participant MS as Manager Service
    participant GS as Game Service
    participant R as Redis

    C->>SG: join_room event
    SG->>SG: Validate authentication & input
    SG->>GS: Execute join with Game Service
    GS-->>SG: Join result
    SG->>SG: Update local state management
    SG->>R: Update room tracking
    SG->>SG: Join Socket.io room
    SG-->>C: Success response
    SG->>C: Broadcast to room participants
```

### Detailed Steps

1. **Client Request**: Client sends `join_room` event with room details
2. **Authentication Validation**: Socket Gateway validates user authentication
3. **Input Validation**: Validates required fields (roomId, optional password, betAmount)
4. **Game Service Integration**: Executes join operation with Game Service via gRPC
5. **Local State Management**: Updates Socket Gateway's local room tracking
6. **Socket.io Room Join**: Adds socket to Socket.io room for broadcasting
7. **Success Response**: Sends confirmation to client
8. **Broadcast Updates**: Notifies other room participants

### Error Handling Scenarios

- **Authentication Failed**: Returns `AUTHENTICATION_REQUIRED` error
- **Invalid Room ID**: Returns `ROOM_ID_REQUIRED` error
- **Room Full**: Returns `ROOM_FULL` error (from Game Service)
- **Insufficient Balance**: Returns `INSUFFICIENT_BALANCE` error
- **Invalid Password**: Returns `INVALID_PASSWORD` error (for private rooms)
- **Game Service Unavailable**: Returns `SERVICE_UNAVAILABLE` error

## 2. Room Leave Process Flow

### Step-by-Step Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant SG as Socket Gateway
    participant GS as Game Service
    participant R as Redis

    C->>SG: leave_room event
    SG->>SG: Validate input
    SG->>GS: Execute leave with Game Service
    GS-->>SG: Leave result
    SG->>SG: Update local state management
    SG->>R: Remove from room tracking
    SG->>SG: Leave Socket.io room
    SG->>SG: Auto-subscribe to lobby
    SG-->>C: Success response
    SG->>C: Broadcast to remaining participants
```

### Detailed Steps

1. **Client Request**: Client sends `leave_room` event
2. **Input Validation**: Validates roomId is provided
3. **Game Service Integration**: Executes leave operation with Game Service
4. **Local State Management**: Updates Socket Gateway's local room tracking
5. **Socket.io Room Leave**: Removes socket from Socket.io room
6. **Auto-lobby Subscription**: Automatically subscribes user back to lobby
7. **Success Response**: Sends confirmation to client
8. **Broadcast Updates**: Notifies remaining room participants

## 3. Socket.io Event Documentation

### Join Room Events

#### Client-to-Server: `join_room`

**Event Name**: `join_room`

**Payload Structure**:
```javascript
{
  roomId: string,        // Required: Room ID to join
  password?: string,     // Optional: Room password (for private rooms)
  betAmount?: number     // Optional: Bet amount (if different from room default)
}
```

**Example Request**:
```javascript
socket.emit('join_room', {
  roomId: '68412c9af494b684c1c18ecf',
  password: 'secret123',
  betAmount: 100
}, (response) => {
  console.log('Join response:', response);
});
```

#### Server-to-Client: Join Success Response

**Response Structure**:
```javascript
{
  success: true,
  message: "Successfully joined room",
  roomId: string,
  room: {
    id: string,
    name: string,
    game_type: string,
    current_players: number,
    max_players: number,
    status: string,
    bet_amount: number
  },
  player: {
    userId: string,
    username: string
  }
}
```

**Example Success Response**:
```javascript
{
  success: true,
  message: "Successfully joined room",
  roomId: "68412c9af494b684c1c18ecf",
  room: {
    id: "68412c9af494b684c1c18ecf",
    name: "High Stakes Amidakuji",
    game_type: "amidakuji",
    current_players: 2,
    max_players: 4,
    status: "waiting",
    bet_amount: 100
  },
  player: {
    userId: "683b07882d7dbd11e92bf29d",
    username: "player123"
  }
}
```

#### Server-to-Client: Join Error Response

**Error Response Structure**:
```javascript
{
  success: false,
  error: string,
  code: string,
  details?: object
}
```

**Example Error Responses**:
```javascript
// Room Full Error
{
  success: false,
  error: "Room is full",
  code: "ROOM_FULL"
}

// Insufficient Balance Error
{
  success: false,
  error: "Insufficient balance to join room",
  code: "INSUFFICIENT_BALANCE",
  details: {
    required: 100,
    available: 50
  }
}

// Invalid Password Error
{
  success: false,
  error: "Invalid room password",
  code: "INVALID_PASSWORD"
}
```

#### Broadcast Events: Room Updates

**Event Name**: `room_info_updated`

**Broadcast Payload**:
```javascript
{
  action: "player_joined",
  roomId: string,
  room: {
    id: string,
    name: string,
    current_players: number,
    max_players: number,
    status: string
  },
  player: {
    userId: string,
    username: string
  },
  timestamp: string
}
```

**Example Broadcast**:
```javascript
{
  action: "player_joined",
  roomId: "68412c9af494b684c1c18ecf",
  room: {
    id: "68412c9af494b684c1c18ecf",
    name: "High Stakes Amidakuji",
    current_players: 3,
    max_players: 4,
    status: "waiting"
  },
  player: {
    userId: "683b07882d7dbd11e92bf29d",
    username: "player123"
  },
  timestamp: "2025-06-12T22:15:30.123Z"
}
```

### Leave Room Events

#### Client-to-Server: `leave_room`

**Event Name**: `leave_room`

**Payload Structure**:
```javascript
{
  roomId: string         // Required: Room ID to leave
}
```

**Example Request**:
```javascript
socket.emit('leave_room', {
  roomId: '68412c9af494b684c1c18ecf'
}, (response) => {
  console.log('Leave response:', response);
});
```

#### Server-to-Client: Leave Success Response

**Response Structure**:
```javascript
{
  success: true,
  message: "Successfully left room",
  roomId: string
}
```

**Example Success Response**:
```javascript
{
  success: true,
  message: "Successfully left room",
  roomId: "68412c9af494b684c1c18ecf"
}
```

#### Server-to-Client: Leave Error Response

**Error Response Structure**:
```javascript
{
  success: false,
  error: string,
  code: string
}
```

**Example Error Response**:
```javascript
{
  success: false,
  error: "Room ID is required",
  code: "ROOM_ID_REQUIRED"
}
```

#### Broadcast Events: Player Left

**Event Name**: `room_info_updated`

**Broadcast Payload**:
```javascript
{
  action: "player_left",
  roomId: string,
  room: {
    id: string,
    name: string,
    current_players: number,
    max_players: number,
    status: string
  },
  player: {
    userId: string,
    username: string
  },
  timestamp: string
}
```

**Example Broadcast**:
```javascript
{
  action: "player_left",
  roomId: "68412c9af494b684c1c18ecf",
  room: {
    id: "68412c9af494b684c1c18ecf",
    name: "High Stakes Amidakuji",
    current_players: 2,
    max_players: 4,
    status: "waiting"
  },
  player: {
    userId: "683b07882d7dbd11e92bf29d",
    username: "player123"
  },
  timestamp: "2025-06-12T22:20:15.456Z"
}
```

## 4. Integration Points

### Manager Service Integration (Port 3002)

The Socket Gateway integrates with the Manager Service for room state management:

#### Room Data Retrieval
- **Endpoint**: `GET /rooms`
- **Purpose**: Fetch current room list for lobby updates
- **Integration**: Used in lobby subscription to get real-time room data

#### Room State Synchronization
- **Purpose**: Ensure Socket Gateway has current room information
- **Method**: HTTP REST API calls
- **Data Flow**: Manager Service → Socket Gateway → Clients

**Example Manager Service Room Data**:
```javascript
{
  "success": true,
  "data": {
    "rooms": [
      {
        "id": "68412c9af494b684c1c18ecf",
        "name": "High Stakes Amidakuji",
        "game_type": "amidakuji",
        "status": "waiting",
        "current_players": 2,
        "max_players": 4,
        "bet_amount": 100,
        "is_private": false,
        "has_space": true,
        "created_at": "2025-06-12T20:30:00Z",
        "updated_at": "2025-06-12T22:15:30Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 1,
      "total_count": 4,
      "per_page": 25
    }
  }
}
```

### Game Service Integration (Port 8080)

The Socket Gateway integrates with the Game Service for game-specific logic:

#### Room Join Operation
- **Method**: gRPC call to `join_room`
- **Purpose**: Handle game logic, balance deduction, and room state updates
- **Data Flow**: Socket Gateway → Game Service → Database

#### Room Leave Operation
- **Method**: gRPC call to `leave_room`
- **Purpose**: Handle game cleanup, balance refund, and room state updates
- **Data Flow**: Socket Gateway → Game Service → Database

**Example Game Service Integration**:
```javascript
// Join Room gRPC Call
const joinRequest = {
  roomId: "68412c9af494b684c1c18ecf",
  userId: "683b07882d7dbd11e92bf29d",
  password: "secret123",
  betAmount: 100
};

const joinResult = await gameServiceClient.joinRoom(joinRequest);
```

### Authentication Requirements

#### JWT Token Validation
- **Requirement**: Valid JWT token in socket authentication
- **Validation**: Token verified against Auth Service
- **User Data**: userId, username, role extracted from token

#### Authorization Checks
- **Room Access**: Validate user can join specific room
- **Balance Verification**: Ensure sufficient balance for bet amount
- **Permission Checks**: Validate user permissions for room operations

### Redis Integration

#### Room State Tracking
- **Purpose**: Track active room participants for broadcasting
- **Keys**: `room_players:{roomId}`, `room_meta:{roomId}`
- **Data**: Socket IDs, user IDs, room metadata

#### Subscription Management
- **Purpose**: Manage user subscriptions between lobby and rooms
- **Keys**: `user_subscription:{userId}`, `socket_subscription:{socketId}`
- **Data**: Current subscription state, room/lobby tracking

## 5. Error Codes Reference

### Authentication Errors
- `AUTHENTICATION_REQUIRED`: User not authenticated
- `TOKEN_EXPIRED`: JWT token has expired
- `INVALID_TOKEN`: JWT token is invalid

### Validation Errors
- `ROOM_ID_REQUIRED`: Room ID not provided
- `INVALID_ROOM_ID`: Room ID format is invalid
- `INVALID_PASSWORD`: Incorrect room password

### Business Logic Errors
- `ROOM_FULL`: Room has reached maximum capacity
- `ROOM_NOT_FOUND`: Specified room does not exist
- `INSUFFICIENT_BALANCE`: User balance too low for bet amount
- `ALREADY_IN_ROOM`: User already in this room
- `NOT_IN_ROOM`: User not currently in specified room

### Service Errors
- `SERVICE_UNAVAILABLE`: Game Service or Manager Service unavailable
- `INTERNAL_ERROR`: Unexpected server error
- `TIMEOUT_ERROR`: Operation timed out

## 6. Performance Considerations

### Connection Management
- **Socket.io Rooms**: Efficient broadcasting to room participants
- **Memory Management**: Automatic cleanup of empty rooms
- **Connection Pooling**: Optimized connections to backend services

### Caching Strategy
- **Room State Cache**: 30-second cache for room information
- **User Session Cache**: Redis-based session management
- **Intelligent Invalidation**: Cache invalidation on room updates

### Scalability Features
- **Horizontal Scaling**: Support for multiple Socket Gateway instances
- **Load Balancing**: Distribute connections across instances
- **Redis Adapter**: Shared state across multiple instances

## 7. Monitoring and Logging

### Event Logging
- **Join/Leave Events**: Comprehensive logging of all room operations
- **Error Tracking**: Detailed error logs with context
- **Performance Metrics**: Response times and success rates

### Health Checks
- **Service Health**: Monitor Manager Service and Game Service availability
- **Connection Health**: Track active connections and room subscriptions
- **Resource Monitoring**: Memory usage and performance optimization

### Analytics
- **Room Usage**: Track room join/leave patterns
- **User Behavior**: Monitor user engagement and session duration
- **Error Analysis**: Identify common failure patterns

## 8. Implementation Status

### Current Implementation
- ✅ **Socket.io Event Handlers**: `join_room`, `leave_room`, `subscribe_room`
- ✅ **Authentication Integration**: JWT token validation
- ✅ **Local State Management**: Room tracking and subscription management
- ✅ **Redis Integration**: Session and subscription state management
- ✅ **Manager Service Integration**: Room data retrieval for lobby
- ⚠️ **Game Service Integration**: Mock implementation (needs actual gRPC calls)

### Pending Implementation
- 🔄 **Manager Service Room Operations**: Direct room join/leave via Manager Service
- 🔄 **Real-time Room Updates**: Broadcasting room state changes
- 🔄 **Enhanced Error Handling**: Comprehensive error scenarios
- 🔄 **Performance Optimization**: Advanced caching and connection management

### Architecture Notes
- **Current Flow**: Socket Gateway → Game Service (for join/leave operations)
- **Room Data Source**: Manager Service (for lobby and room information)
- **State Management**: Hybrid approach with local tracking and Redis persistence
- **Broadcasting**: Socket.io rooms for efficient real-time updates

This documentation reflects the current working implementation where room data flows from Manager Service (port 3002) through Socket Gateway (port 3001) to clients, maintaining proper microservices architecture boundaries.
