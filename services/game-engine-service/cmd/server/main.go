package main

import (
"context"
"fmt"
"log"
"net"
"os"
"os/signal"
"syscall"
"time"

"github.com/sirupsen/logrus"
"google.golang.org/grpc"
"google.golang.org/grpc/health"
"google.golang.org/grpc/health/grpc_health_v1"
"google.golang.org/grpc/reflection"

"github.com/xzgame/game-engine-service/internal/config"
"github.com/xzgame/game-engine-service/internal/repositories"
"github.com/xzgame/game-engine-service/internal/services"
"github.com/xzgame/game-engine-service/pkg/redis"
pb "github.com/xzgame/game-engine-service/proto"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Invalid configuration: %v", err)
	}

	logger.WithFields(logrus.Fields{
"port":        cfg.Port,
"environment": cfg.Environment,
"log_level":   cfg.LogLevel,
}).Info("Starting Game Engine Service")

	// Set log level
	if level, err := logrus.ParseLevel(cfg.LogLevel); err == nil {
		logger.SetLevel(level)
	}

	// Initialize Redis client
	redisClient, err := redis.NewRedisClient(cfg.RedisURL, cfg.RedisPassword, cfg.RedisDB)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to Redis")
	}
	defer redisClient.Close()

	// Test Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := redisClient.Ping(ctx); err != nil {
		logger.WithError(err).Fatal("Failed to ping Redis")
	}
	logger.Info("Successfully connected to Redis")

	// Initialize repository
	repoConfig := repositories.RepositoryConfig{
		DatabaseURL:  cfg.MongoURL,
		DatabaseName: cfg.DatabaseName,
		CacheEnabled: cfg.CacheEnabled,
		CacheURL:     cfg.RedisURL,
	}

	sessionRepo, err := repositories.NewGameSessionRepository(repoConfig)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize session repository")
	}

	// Initialize game engine service
	gameEngineService := services.NewGameEngineService(
sessionRepo,
redisClient,
cfg,
logger,
)

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Create gRPC service wrapper
	grpcService := services.NewGRPCServer(gameEngineService, logger)

	// Register game engine service
	pb.RegisterGameEngineServiceServer(grpcServer, grpcService)

	// Register health check service
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(grpcServer, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// Enable reflection for development
	if cfg.IsDevelopment() {
		reflection.Register(grpcServer)
	}

	// Start gRPC server
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		logger.WithError(err).Fatal("Failed to create listener")
	}

	// Start server in goroutine
	go func() {
		logger.WithField("address", listener.Addr().String()).Info("Game Engine Service gRPC server starting")
		if err := grpcServer.Serve(listener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC server")
		}
	}()

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("Shutting down Game Engine Service...")

	// Graceful shutdown
	grpcServer.GracefulStop()
	
	logger.Info("Game Engine Service shutdown complete")
}
