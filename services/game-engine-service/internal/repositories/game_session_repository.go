package repositories

import (
"context"
)

type GameSessionRepository interface {
	GetActiveSessionCount(ctx context.Context) (int64, error)
}

type MongoGameSessionRepository struct{}

func NewGameSessionRepository(config RepositoryConfig) (GameSessionRepository, error) {
	return &MongoGameSessionRepository{}, nil
}

func (r *MongoGameSessionRepository) GetActiveSessionCount(ctx context.Context) (int64, error) {
	return 0, nil
}

type RepositoryConfig struct {
	DatabaseURL  string
	DatabaseName string
	CacheEnabled bool
	CacheURL     string
}
