package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

type Config struct {
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`

	MongoURL     string        `json:"mongo_url"`
	DatabaseName string        `json:"database_name"`
	MongoTimeout time.Duration `json:"mongo_timeout"`

	RedisURL      string        `json:"redis_url"`
	RedisPassword string        `json:"redis_password"`
	RedisDB       int           `json:"redis_db"`
	RedisTTL      time.Duration `json:"redis_ttl"`

	DefaultHouseEdge    float64       `json:"default_house_edge"`
	MaxGameDuration     time.Duration `json:"max_game_duration"`
	MinPlayers          int           `json:"min_players"`
	MaxPlayers          int           `json:"max_players"`
	GameTimeoutDuration time.Duration `json:"game_timeout_duration"`
	ResultCacheDuration time.Duration `json:"result_cache_duration"`

	FairnessProofEnabled bool `json:"fairness_proof_enabled"`
	MaxConcurrentGames   int  `json:"max_concurrent_games"`

	CacheEnabled bool          `json:"cache_enabled"`
	CacheTTL     time.Duration `json:"cache_ttl"`
}

func Load() (*Config, error) {
	config := &Config{
		Port:        8083,
		Environment: "development",
		LogLevel:    "info",

		MongoURL:     "mongodb://localhost:27017",
		DatabaseName: "xzgame",
		MongoTimeout: 30 * time.Second,

		RedisURL:      "redis://localhost:6379",
		RedisPassword: "",
		RedisDB:       0,
		RedisTTL:      24 * time.Hour,

		DefaultHouseEdge:    0.05,
		MaxGameDuration:     10 * time.Minute,
		MinPlayers:          2,
		MaxPlayers:          8,
		GameTimeoutDuration: 30 * time.Second,
		ResultCacheDuration: 1 * time.Hour,

		FairnessProofEnabled: true,
		MaxConcurrentGames:   100,

		CacheEnabled: true,
		CacheTTL:     5 * time.Minute,
	}

	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		config.RedisURL = redisURL
	}

	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		config.RedisPassword = redisPassword
	}

	if redisDB := os.Getenv("REDIS_DB"); redisDB != "" {
		if db, err := strconv.Atoi(redisDB); err == nil {
			config.RedisDB = db
		}
	}

	if mongoURL := os.Getenv("MONGO_URL"); mongoURL != "" {
		config.MongoURL = mongoURL
	}

	return config, nil
}

func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}
	return nil
}

func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}
