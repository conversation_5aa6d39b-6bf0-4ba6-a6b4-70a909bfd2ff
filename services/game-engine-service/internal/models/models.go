package models

import (
	"fmt"
	"time"
)

// GameType represents the type of game
type GameType string

const (
	GameTypePrizeWheel GameType = "prizewheel"
	GameTypeAmidakuji  GameType = "amidakuji"
)

// GameExecutionRequest represents a request to execute a game
type GameExecutionRequest struct {
	SessionID     string                 `json:"session_id"`
	GameType      GameType               `json:"game_type"`
	Players       []GamePlayer           `json:"players"`
	Configuration GameConfiguration      `json:"configuration"`
	GameData      map[string]interface{} `json:"game_data,omitempty"`
}

// GameResult represents the result of a game execution
type GameResult struct {
	SessionID       string                 `json:"session_id"`
	GameType        GameType               `json:"game_type"`
	WinnerUserID    string                 `json:"winner_user_id"`
	WinningPosition int                    `json:"winning_position"`
	TotalBetPool    int64                  `json:"total_bet_pool"`
	PrizePool       int64                  `json:"prize_pool"`
	HouseTake       int64                  `json:"house_take"`
	Payouts         []PlayerPayout         `json:"payouts"`
	GameData        map[string]interface{} `json:"game_data"`
	CompletedAt     time.Time              `json:"completed_at"`
}

// GamePlayer represents a player in a game session
type GamePlayer struct {
	UserID    string                 `json:"user_id"`
	Username  string                 `json:"username"`
	Position  int                    `json:"position"`
	BetAmount int64                  `json:"bet_amount"`
	GameData  map[string]interface{} `json:"game_data,omitempty"`
	JoinedAt  time.Time              `json:"joined_at"`
}

// GameConfiguration represents game configuration settings
type GameConfiguration struct {
	HouseEdge          float64                `json:"house_edge"`
	MaxDurationSeconds int64                  `json:"max_duration_seconds"`
	GameSpecific       map[string]interface{} `json:"game_specific,omitempty"`
}

// PlayerPayout represents a payout to a player
type PlayerPayout struct {
	UserID    string `json:"user_id"`
	Amount    int64  `json:"amount"`
	Reason    string `json:"reason"`
	Processed bool   `json:"processed"`
}

// Random generation models

// RandomRequest represents a request for random number generation
type RandomRequest struct {
	SessionID string `json:"session_id"`
	Min       int64  `json:"min"`
	Max       int64  `json:"max"`
	Count     int    `json:"count"`
	MinValue  int64  `json:"min_value"`
	MaxValue  int64  `json:"max_value"`
	Seed      string `json:"seed"`
}

// RandomResponse represents a response with random numbers
type RandomResponse struct {
	SessionID   string       `json:"session_id"`
	Numbers     []int64      `json:"numbers"`
	Value       int64        `json:"value"`
	MinValue    int64        `json:"min_value"`
	MaxValue    int64        `json:"max_value"`
	Seed        string       `json:"seed"`
	Proof       *RandomProof `json:"proof"`
	GeneratedAt time.Time    `json:"generated_at"`
}

// GameSeed represents a cryptographic seed for a game
type GameSeed struct {
	SessionID   string    `json:"session_id"`
	Seed        string    `json:"seed"`
	SeedBytes   []byte    `json:"seed_bytes"`
	SeedHash    string    `json:"seed_hash"`
	PlayerCount int       `json:"player_count"`
	GeneratedAt time.Time `json:"generated_at"`
}

// Result calculation models

// CalculationRequest represents a request for result calculation
type CalculationRequest struct {
	SessionID     string                 `json:"session_id"`
	GameType      GameType               `json:"game_type"`
	Players       []GamePlayer           `json:"players"`
	GameResults   map[string]interface{} `json:"game_results"`
	HouseEdge     float64                `json:"house_edge"`
	Configuration GameConfiguration      `json:"configuration"`
}

// CalculationResponse represents a response with calculated results
type CalculationResponse struct {
	SessionID    string          `json:"session_id"`
	GameType     GameType        `json:"game_type"`
	TotalBetPool int64           `json:"total_bet_pool"`
	PrizePool    int64           `json:"prize_pool"`
	HouseTake    int64           `json:"house_take"`
	Payouts      []PlayerPayout  `json:"payouts"`
	Statistics   *GameStatistics `json:"statistics"`
	CalculatedAt time.Time       `json:"calculated_at"`
}

// PayoutRequest represents a request for payout calculation
type PayoutRequest struct {
	SessionID  string       `json:"session_id"`
	PrizePool  int64        `json:"prize_pool"`
	Winners    []GamePlayer `json:"winners"`
	PayoutType string       `json:"payout_type"` // "equal_split", "proportional"
}

// PayoutResponse represents a response with calculated payouts
type PayoutResponse struct {
	SessionID     string         `json:"session_id"`
	Payouts       []PlayerPayout `json:"payouts"`
	TotalPayout   int64          `json:"total_payout"`
	RemainingPool int64          `json:"remaining_pool"`
	CalculatedAt  time.Time      `json:"calculated_at"`
}

// GameStatistics represents game statistics
type GameStatistics struct {
	TotalPlayers     int     `json:"total_players"`
	PlayerCount      int     `json:"player_count"`
	WinnerCount      int     `json:"winner_count"`
	TotalBetPool     int64   `json:"total_bet_pool"`
	PrizePool        int64   `json:"prize_pool"`
	HouseTake        int64   `json:"house_take"`
	TotalPayout      int64   `json:"total_payout"`
	RemainingPool    int64   `json:"remaining_pool"`
	AverageBet       float64 `json:"average_bet"`
	AveragePayout    float64 `json:"average_payout"`
	MaxPayout        int64   `json:"max_payout"`
	HouseEdge        float64 `json:"house_edge"`
	HouseEdgeApplied float64 `json:"house_edge_applied"`
	WinRate          float64 `json:"win_rate"`
	GameDuration     int64   `json:"game_duration_ms"`
}

// Error handling

// GameEngineError represents a game engine error
type GameEngineError struct {
	Message string                 `json:"message"`
	Code    string                 `json:"code,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// Error implements the error interface
func (e *GameEngineError) Error() string {
	if e.Code != "" {
		return fmt.Sprintf("[%s] %s", e.Code, e.Message)
	}
	return e.Message
}

// NewGameEngineError creates a new game engine error
func NewGameEngineError(message string, details map[string]interface{}) *GameEngineError {
	return &GameEngineError{
		Message: message,
		Details: details,
	}
}

// NewGameEngineErrorWithCode creates a new game engine error with a code
func NewGameEngineErrorWithCode(code, message string, details map[string]interface{}) *GameEngineError {
	return &GameEngineError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// Fairness proof models

// FairnessProofRequest represents a request for fairness proof generation
type FairnessProofRequest struct {
	SessionID   string                 `json:"session_id"`
	GameType    GameType               `json:"game_type"`
	Players     []GamePlayer           `json:"players"`
	GameResults map[string]interface{} `json:"game_results"`
	GameData    map[string]interface{} `json:"game_data"`
	Seed        string                 `json:"seed"`
	RandomSeed  string                 `json:"random_seed"`
}

// FairnessProof represents a cryptographic proof of game fairness
type FairnessProof struct {
	SessionID         string                 `json:"session_id"`
	GameType          GameType               `json:"game_type"`
	Algorithm         string                 `json:"algorithm"`
	Version           string                 `json:"version"`
	ProofHash         string                 `json:"proof_hash"`
	Seed              string                 `json:"seed"`
	RandomSeed        string                 `json:"random_seed"`
	GameData          map[string]interface{} `json:"game_data"`
	GameSpecificData  interface{}            `json:"game_specific_data"`
	Components        map[string]interface{} `json:"components"`
	ComponentHashes   map[string]string      `json:"component_hashes"`
	VerificationSteps []VerificationStep     `json:"verification_steps"`
	IsVerifiable      bool                   `json:"is_verifiable"`
	Signature         string                 `json:"signature"`
	GeneratedAt       time.Time              `json:"generated_at"`
	VerificationURL   string                 `json:"verification_url"`
}

// VerificationResult represents the result of fairness proof verification
type VerificationResult struct {
	SessionID    string       `json:"session_id"`
	ProofHash    string       `json:"proof_hash"`
	IsValid      bool         `json:"is_valid"`
	Verified     bool         `json:"verified"`
	VerifiedAt   time.Time    `json:"verified_at"`
	StepResults  []StepResult `json:"step_results"`
	ErrorDetails []string     `json:"error_details"`
	ErrorReason  string       `json:"error_reason,omitempty"`
}

// Additional types for engines

// VerificationStep represents a step in the verification process
type VerificationStep struct {
	Step        int    `json:"step"`
	Description string `json:"description"`
	Input       string `json:"input"`
	Expected    string `json:"expected"`
	Method      string `json:"method"`
}

// StepResult represents the result of a verification step
type StepResult struct {
	Step         int       `json:"step"`
	Description  string    `json:"description"`
	IsValid      bool      `json:"is_valid"`
	ActualHash   string    `json:"actual_hash"`
	ExpectedHash string    `json:"expected_hash"`
	VerifiedAt   time.Time `json:"verified_at"`
}

// ProofStats represents statistics about proof generation
type ProofStats struct {
	TotalProofs      int64     `json:"total_proofs"`
	SuccessfulProofs int64     `json:"successful_proofs"`
	AverageTime      float64   `json:"average_time_ms"`
	LastGenerated    time.Time `json:"last_generated"`
	Algorithm        string    `json:"algorithm"`
	HashFunction     string    `json:"hash_function"`
	KeySize          int       `json:"key_size"`
	Verified         bool      `json:"verified"`
	SupportedGames   []string  `json:"supported_games"`
}

// RandomProof represents proof of random number generation
type RandomProof struct {
	Algorithm   string    `json:"algorithm"`
	Seed        string    `json:"seed"`
	Input       string    `json:"input"`
	Output      string    `json:"output"`
	Hash        string    `json:"hash"`
	RandomBytes []byte    `json:"random_bytes"`
	ProofData   string    `json:"proof_data"`
	Signature   string    `json:"signature"`
	GeneratedAt time.Time `json:"generated_at"`
}

// RandomStats represents statistics about random number generation
type RandomStats struct {
	TotalGenerated int64     `json:"total_generated"`
	LastGenerated  time.Time `json:"last_generated"`
	AverageTime    float64   `json:"average_time_ms"`
	EntropySource  string    `json:"entropy_source"`
	Algorithm      string    `json:"algorithm"`
	KeySize        int       `json:"key_size"`
	HashFunction   string    `json:"hash_function"`
	Verified       bool      `json:"verified"`
}

// PayoutType constants
const (
	PayoutTypeWinnerTakesAll = "winner_takes_all"
	PayoutTypeEqualSplit     = "equal_split"
	PayoutTypeProportional   = "proportional"
)

// EngineStats represents comprehensive engine statistics
type EngineStats struct {
	ActiveGames    int64         `json:"active_games"`
	SupportedGames []string      `json:"supported_games"`
	RandomStats    *RandomStats  `json:"random_stats"`
	ProofStats     *ProofStats   `json:"proof_stats"`
	Uptime         time.Duration `json:"uptime"`
	Version        string        `json:"version"`
}
