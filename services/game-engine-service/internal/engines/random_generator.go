package engines

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/models"
)

// RandomGenerator provides cryptographically secure random number generation
type RandomGenerator struct {
	logger *logrus.Logger
}

// NewRandomGenerator creates a new random generator
func NewRandomGenerator(logger *logrus.Logger) *RandomGenerator {
	return &RandomGenerator{
		logger: logger,
	}
}

// GenerateSecureRandom generates a cryptographically secure random number
func (rg *RandomGenerator) GenerateSecureRandom(request *models.RandomRequest) (*models.RandomResponse, error) {
	rg.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"min_value":  request.MinValue,
		"max_value":  request.MaxValue,
		"seed":       request.Seed,
	}).Debug("Generating secure random number")

	// Validate request
	if err := rg.validateRandomRequest(request); err != nil {
		return nil, err
	}

	// Generate random value
	randomValue, proof, err := rg.generateWithProof(request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random with proof: %w", err)
	}

	response := &models.RandomResponse{
		SessionID:    request.SessionID,
		Value:        randomValue,
		MinValue:     request.MinValue,
		MaxValue:     request.MaxValue,
		Seed:         request.Seed,
		Proof:        proof,
		GeneratedAt:  time.Now(),
	}

	rg.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"value":      randomValue,
		"proof_hash": proof.Hash,
	}).Info("Generated secure random number")

	return response, nil
}

// GenerateGameSeed generates a cryptographically secure seed for a game
func (rg *RandomGenerator) GenerateGameSeed(sessionID string, playerCount int) (*models.GameSeed, error) {
	rg.logger.WithFields(logrus.Fields{
		"session_id":   sessionID,
		"player_count": playerCount,
	}).Debug("Generating game seed")

	// Generate random bytes for seed
	seedBytes := make([]byte, 32) // 256-bit seed
	_, err := rand.Read(seedBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to generate seed bytes: %w", err)
	}

	// Create seed hash
	hasher := sha256.New()
	hasher.Write(seedBytes)
	hasher.Write([]byte(sessionID))
	hasher.Write([]byte(fmt.Sprintf("%d", playerCount)))
	hasher.Write([]byte(fmt.Sprintf("%d", time.Now().UnixNano())))
	seedHash := hasher.Sum(nil)

	seed := &models.GameSeed{
		SessionID:   sessionID,
		SeedBytes:   seedBytes,
		SeedHash:    fmt.Sprintf("%x", seedHash),
		PlayerCount: playerCount,
		GeneratedAt: time.Now(),
	}

	rg.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"seed_hash":  seed.SeedHash,
	}).Info("Generated game seed")

	return seed, nil
}

// GenerateRandomFloat generates a cryptographically secure random float
func (rg *RandomGenerator) GenerateRandomFloat(min, max float64) (float64, error) {
	if min >= max {
		return 0, fmt.Errorf("min must be less than max")
	}

	// Generate 8 random bytes
	randomBytes := make([]byte, 8)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to uint64
	randomUint64 := binary.BigEndian.Uint64(randomBytes)
	
	// Normalize to [0, 1)
	normalized := float64(randomUint64) / float64(math.MaxUint64)
	
	// Scale to [min, max)
	return min + normalized*(max-min), nil
}

// GenerateRandomInt generates a cryptographically secure random integer
func (rg *RandomGenerator) GenerateRandomInt(min, max int) (int, error) {
	if min >= max {
		return 0, fmt.Errorf("min must be less than max")
	}

	range_ := max - min
	
	// Generate enough random bytes
	randomBytes := make([]byte, 4)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to uint32
	randomUint32 := binary.BigEndian.Uint32(randomBytes)
	
	// Scale to range
	return min + int(randomUint32)%range_, nil
}

// GenerateRandomBool generates a cryptographically secure random boolean
func (rg *RandomGenerator) GenerateRandomBool() (bool, error) {
	randomBytes := make([]byte, 1)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return false, fmt.Errorf("failed to generate random byte: %w", err)
	}

	return randomBytes[0]%2 == 1, nil
}

// GenerateRandomBytes generates cryptographically secure random bytes
func (rg *RandomGenerator) GenerateRandomBytes(length int) ([]byte, error) {
	if length <= 0 {
		return nil, fmt.Errorf("length must be positive")
	}

	randomBytes := make([]byte, length)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random bytes: %w", err)
	}

	return randomBytes, nil
}

// validateRandomRequest validates a random generation request
func (rg *RandomGenerator) validateRandomRequest(request *models.RandomRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if request.MinValue >= request.MaxValue {
		return models.NewGameEngineError("min value must be less than max value", map[string]interface{}{
			"min_value": request.MinValue,
			"max_value": request.MaxValue,
		})
	}

	return nil
}

// generateWithProof generates a random value with cryptographic proof
func (rg *RandomGenerator) generateWithProof(request *models.RandomRequest) (int64, *models.RandomProof, error) {
	// Generate random bytes
	randomBytes := make([]byte, 8)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to uint64
	randomUint64 := binary.BigEndian.Uint64(randomBytes)

	// Scale to requested range
	range_ := request.MaxValue - request.MinValue
	scaledValue := request.MinValue + int64(randomUint64)%range_

	// Create proof
	proof, err := rg.createRandomProof(request, randomBytes, scaledValue)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create proof: %w", err)
	}

	return scaledValue, proof, nil
}

// createRandomProof creates a cryptographic proof for the random generation
func (rg *RandomGenerator) createRandomProof(request *models.RandomRequest, randomBytes []byte, value int64) (*models.RandomProof, error) {
	// Create proof data
	proofData := fmt.Sprintf("session:%s|seed:%s|min:%d|max:%d|bytes:%x|value:%d|timestamp:%d",
		request.SessionID,
		request.Seed,
		request.MinValue,
		request.MaxValue,
		randomBytes,
		value,
		time.Now().UnixNano(),
	)

	// Hash the proof data
	hasher := sha256.New()
	hasher.Write([]byte(proofData))
	proofHash := hasher.Sum(nil)

	proof := &models.RandomProof{
		Hash:        fmt.Sprintf("%x", proofHash),
		RandomBytes: randomBytes,
		ProofData:   proofData,
		Algorithm:   "crypto/rand + SHA256",
		GeneratedAt: time.Now(),
	}

	return proof, nil
}

// VerifyRandomProof verifies a random generation proof
func (rg *RandomGenerator) VerifyRandomProof(proof *models.RandomProof, expectedValue int64) (bool, error) {
	// Recreate the hash from proof data
	hasher := sha256.New()
	hasher.Write([]byte(proof.ProofData))
	expectedHash := fmt.Sprintf("%x", hasher.Sum(nil))

	// Compare hashes
	if proof.Hash != expectedHash {
		rg.logger.WithFields(logrus.Fields{
			"expected_hash": expectedHash,
			"actual_hash":   proof.Hash,
		}).Warn("Random proof hash mismatch")
		return false, nil
	}

	rg.logger.WithFields(logrus.Fields{
		"proof_hash":     proof.Hash,
		"expected_value": expectedValue,
	}).Debug("Random proof verified successfully")

	return true, nil
}

// GetRandomStats returns statistics about random generation
func (rg *RandomGenerator) GetRandomStats() *models.RandomStats {
	return &models.RandomStats{
		Algorithm:     "crypto/rand",
		EntropySource: "OS entropy pool",
		KeySize:       256, // bits
		HashFunction:  "SHA256",
		Verified:      true,
	}
}

// SeedFromString creates a deterministic seed from a string (for testing)
func (rg *RandomGenerator) SeedFromString(input string) []byte {
	hasher := sha256.New()
	hasher.Write([]byte(input))
	return hasher.Sum(nil)
}

// GenerateSequence generates a sequence of random numbers
func (rg *RandomGenerator) GenerateSequence(count int, min, max int64) ([]int64, error) {
	if count <= 0 {
		return nil, fmt.Errorf("count must be positive")
	}

	sequence := make([]int64, count)
	for i := 0; i < count; i++ {
		value, err := rg.GenerateRandomInt(int(min), int(max))
		if err != nil {
			return nil, fmt.Errorf("failed to generate random int at index %d: %w", i, err)
		}
		sequence[i] = int64(value)
	}

	return sequence, nil
}
