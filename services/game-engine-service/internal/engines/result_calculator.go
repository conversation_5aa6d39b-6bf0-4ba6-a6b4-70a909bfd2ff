package engines

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/models"
)

// ResultCalculator handles game result calculations and payout logic
type ResultCalculator struct {
	logger *logrus.Logger
}

// NewResultCalculator creates a new result calculator
func NewResultCalculator(logger *logrus.Logger) *ResultCalculator {
	return &ResultCalculator{
		logger: logger,
	}
}

// CalculateGameResults calculates comprehensive game results
func (rc *ResultCalculator) CalculateGameResults(request *models.CalculationRequest) (*models.CalculationResponse, error) {
	rc.logger.WithFields(logrus.Fields{
		"session_id":   request.SessionID,
		"game_type":    request.GameType,
		"player_count": len(request.Players),
	}).Info("Calculating game results")

	// Validate request
	if err := rc.validateCalculationRequest(request); err != nil {
		return nil, err
	}

	// Calculate basic financial metrics
	totalBetPool := rc.calculateTotalBetPool(request.Players)
	houseTake := rc.calculateHouseTake(totalBetPool, request.Configuration.HouseEdge)
	prizePool := totalBetPool - houseTake

	// Calculate payouts based on game type and results
	payouts, err := rc.calculatePayouts(request, prizePool)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate payouts: %w", err)
	}

	// Calculate statistics
	stats := rc.calculateGameStatistics(request, totalBetPool, prizePool, houseTake, payouts)

	// Create response
	response := &models.CalculationResponse{
		SessionID:    request.SessionID,
		GameType:     request.GameType,
		TotalBetPool: totalBetPool,
		HouseTake:    houseTake,
		PrizePool:    prizePool,
		Payouts:      payouts,
		Statistics:   stats,
		CalculatedAt: time.Now(),
	}

	rc.logger.WithFields(logrus.Fields{
		"session_id":     request.SessionID,
		"total_bet_pool": totalBetPool,
		"prize_pool":     prizePool,
		"house_take":     houseTake,
		"payout_count":   len(payouts),
	}).Info("Game results calculated successfully")

	return response, nil
}

// CalculatePayouts calculates player payouts for a game
func (rc *ResultCalculator) CalculatePayouts(request *models.PayoutRequest) (*models.PayoutResponse, error) {
	rc.logger.WithFields(logrus.Fields{
		"session_id":   request.SessionID,
		"prize_pool":   request.PrizePool,
		"winner_count": len(request.Winners),
	}).Info("Calculating payouts")

	// Validate request
	if err := rc.validatePayoutRequest(request); err != nil {
		return nil, err
	}

	var payouts []models.PlayerPayout

	switch request.PayoutType {
	case models.PayoutTypeWinnerTakesAll:
		payouts = rc.calculateWinnerTakesAllPayouts(request)
	case models.PayoutTypeEqualSplit:
		payouts = rc.calculateEqualSplitPayouts(request)
	case models.PayoutTypeProportional:
		payouts = rc.calculateProportionalPayouts(request)
	default:
		return nil, models.NewGameEngineError("unsupported payout type", map[string]interface{}{
			"payout_type": request.PayoutType,
		})
	}

	// Validate total payout doesn't exceed prize pool
	totalPayout := rc.calculateTotalPayout(payouts)
	if totalPayout > request.PrizePool {
		return nil, models.NewGameEngineError("total payout exceeds prize pool", map[string]interface{}{
			"total_payout": totalPayout,
			"prize_pool":   request.PrizePool,
		})
	}

	response := &models.PayoutResponse{
		SessionID:     request.SessionID,
		Payouts:       payouts,
		TotalPayout:   totalPayout,
		RemainingPool: request.PrizePool - totalPayout,
		CalculatedAt:  time.Now(),
	}

	rc.logger.WithFields(logrus.Fields{
		"session_id":     request.SessionID,
		"total_payout":   totalPayout,
		"remaining_pool": response.RemainingPool,
	}).Info("Payouts calculated successfully")

	return response, nil
}

// validateCalculationRequest validates a calculation request
func (rc *ResultCalculator) validateCalculationRequest(request *models.CalculationRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if len(request.Players) == 0 {
		return models.NewGameEngineError("no players provided", nil)
	}

	if request.Configuration.HouseEdge < 0 || request.Configuration.HouseEdge > 1 {
		return models.NewGameEngineError("invalid house edge", map[string]interface{}{
			"house_edge": request.Configuration.HouseEdge,
		})
	}

	return nil
}

// validatePayoutRequest validates a payout request
func (rc *ResultCalculator) validatePayoutRequest(request *models.PayoutRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if request.PrizePool <= 0 {
		return models.NewGameEngineError("prize pool must be positive", map[string]interface{}{
			"prize_pool": request.PrizePool,
		})
	}

	if len(request.Winners) == 0 {
		return models.NewGameEngineError("no winners provided", nil)
	}

	return nil
}

// calculateTotalBetPool calculates the total bet pool from all players
func (rc *ResultCalculator) calculateTotalBetPool(players []models.GamePlayer) int64 {
	total := int64(0)
	for _, player := range players {
		total += player.BetAmount
	}
	return total
}

// calculateHouseTake calculates the house take based on total bet pool and house edge
func (rc *ResultCalculator) calculateHouseTake(totalBetPool int64, houseEdge float64) int64 {
	return int64(float64(totalBetPool) * houseEdge)
}

// calculatePayouts calculates payouts based on game type and results
func (rc *ResultCalculator) calculatePayouts(request *models.CalculationRequest, prizePool int64) ([]models.PlayerPayout, error) {
	switch request.GameType {
	case models.GameTypePrizeWheel:
		return rc.calculatePrizeWheelPayouts(request, prizePool)
	case models.GameTypeAmidakuji:
		return rc.calculateAmidakujiPayouts(request, prizePool)
	default:
		return nil, models.NewGameEngineError("unsupported game type for payout calculation", map[string]interface{}{
			"game_type": request.GameType,
		})
	}
}

// calculatePrizeWheelPayouts calculates payouts for Prize Wheel game
func (rc *ResultCalculator) calculatePrizeWheelPayouts(request *models.CalculationRequest, prizePool int64) ([]models.PlayerPayout, error) {
	winningColor, ok := request.GameResults["winning_color"].(string)
	if !ok {
		return nil, models.NewGameEngineError("winning color not found in game results", nil)
	}

	// Find all players who selected the winning color
	winners := make([]models.GamePlayer, 0)
	for _, player := range request.Players {
		if selectedColor, exists := player.GameData["selected_color"]; exists {
			if colorStr, ok := selectedColor.(string); ok && colorStr == winningColor {
				winners = append(winners, player)
			}
		}
	}

	var payouts []models.PlayerPayout
	if len(winners) > 0 {
		prizePerWinner := prizePool / int64(len(winners))
		for _, winner := range winners {
			payouts = append(payouts, models.PlayerPayout{
				UserID:    winner.UserID,
				Amount:    prizePerWinner,
				Reason:    "prize_wheel_winner",
				Processed: false,
			})
		}
	}

	return payouts, nil
}

// calculateAmidakujiPayouts calculates payouts for Amidakuji game
func (rc *ResultCalculator) calculateAmidakujiPayouts(request *models.CalculationRequest, prizePool int64) ([]models.PlayerPayout, error) {
	winnerUserID, ok := request.GameResults["winner_user_id"].(string)
	if !ok {
		return nil, models.NewGameEngineError("winner user ID not found in game results", nil)
	}

	payouts := []models.PlayerPayout{
		{
			UserID:    winnerUserID,
			Amount:    prizePool,
			Reason:    "amidakuji_winner",
			Processed: false,
		},
	}

	return payouts, nil
}

// calculateWinnerTakesAllPayouts calculates winner-takes-all payouts
func (rc *ResultCalculator) calculateWinnerTakesAllPayouts(request *models.PayoutRequest) []models.PlayerPayout {
	if len(request.Winners) == 0 {
		return []models.PlayerPayout{}
	}

	// Give entire prize pool to first winner
	return []models.PlayerPayout{
		{
			UserID:    request.Winners[0].UserID,
			Amount:    request.PrizePool,
			Reason:    "winner_takes_all",
			Processed: false,
		},
	}
}

// calculateEqualSplitPayouts calculates equal split payouts
func (rc *ResultCalculator) calculateEqualSplitPayouts(request *models.PayoutRequest) []models.PlayerPayout {
	if len(request.Winners) == 0 {
		return []models.PlayerPayout{}
	}

	prizePerWinner := request.PrizePool / int64(len(request.Winners))
	payouts := make([]models.PlayerPayout, len(request.Winners))

	for i, winner := range request.Winners {
		payouts[i] = models.PlayerPayout{
			UserID:    winner.UserID,
			Amount:    prizePerWinner,
			Reason:    "equal_split_winner",
			Processed: false,
		}
	}

	return payouts
}

// calculateProportionalPayouts calculates proportional payouts based on bet amounts
func (rc *ResultCalculator) calculateProportionalPayouts(request *models.PayoutRequest) []models.PlayerPayout {
	if len(request.Winners) == 0 {
		return []models.PlayerPayout{}
	}

	// Calculate total bet amount from winners
	totalWinnerBets := int64(0)
	for _, winner := range request.Winners {
		totalWinnerBets += winner.BetAmount
	}

	if totalWinnerBets == 0 {
		// Fall back to equal split if no bet amounts
		return rc.calculateEqualSplitPayouts(request)
	}

	payouts := make([]models.PlayerPayout, len(request.Winners))
	for i, winner := range request.Winners {
		proportion := float64(winner.BetAmount) / float64(totalWinnerBets)
		payout := int64(float64(request.PrizePool) * proportion)

		payouts[i] = models.PlayerPayout{
			UserID:    winner.UserID,
			Amount:    payout,
			Reason:    "proportional_winner",
			Processed: false,
		}
	}

	return payouts
}

// calculateTotalPayout calculates the total payout amount
func (rc *ResultCalculator) calculateTotalPayout(payouts []models.PlayerPayout) int64 {
	total := int64(0)
	for _, payout := range payouts {
		total += payout.Amount
	}
	return total
}

// calculateGameStatistics calculates comprehensive game statistics
func (rc *ResultCalculator) calculateGameStatistics(request *models.CalculationRequest, totalBetPool, prizePool, houseTake int64, payouts []models.PlayerPayout) *models.GameStatistics {
	totalPayout := rc.calculateTotalPayout(payouts)

	stats := &models.GameStatistics{
		PlayerCount:   len(request.Players),
		WinnerCount:   len(payouts),
		TotalBetPool:  totalBetPool,
		PrizePool:     prizePool,
		HouseTake:     houseTake,
		TotalPayout:   totalPayout,
		RemainingPool: prizePool - totalPayout,
		HouseEdge:     request.Configuration.HouseEdge,
		AverageBet:    float64(totalBetPool) / float64(len(request.Players)),
		WinRate:       float64(len(payouts)) / float64(len(request.Players)),
	}

	if len(payouts) > 0 {
		stats.AveragePayout = float64(totalPayout) / float64(len(payouts))

		// Find max payout
		maxPayout := int64(0)
		for _, payout := range payouts {
			if payout.Amount > maxPayout {
				maxPayout = payout.Amount
			}
		}
		stats.MaxPayout = maxPayout
	}

	return stats
}
