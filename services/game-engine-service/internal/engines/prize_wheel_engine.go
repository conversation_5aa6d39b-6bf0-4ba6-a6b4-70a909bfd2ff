package engines

import (
	"context"
	"crypto/rand"
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/models"
)

// PrizeWheelEngine handles Prize Wheel game logic
type PrizeWheelEngine struct {
	logger *logrus.Logger
}

// NewPrizeWheelEngine creates a new Prize Wheel engine
func NewPrizeWheelEngine(logger *logrus.Logger) *PrizeWheelEngine {
	return &PrizeWheelEngine{
		logger: logger,
	}
}

// ExecuteGame executes a Prize Wheel game
func (pwe *PrizeWheelEngine) ExecuteGame(ctx context.Context, request *models.GameExecutionRequest) (*models.GameResult, error) {
	pwe.logger.WithFields(logrus.Fields{
		"session_id":    request.SessionID,
		"player_count":  len(request.Players),
		"game_type":     "prize_wheel",
	}).Info("Executing Prize Wheel game")

	// Validate request
	if err := pwe.validateRequest(request); err != nil {
		return nil, err
	}

	// Get player color selections
	playerColors := pwe.extractPlayerColors(request.Players)

	// Generate wheel spin result
	wheelResult, err := pwe.generateWheelSpinResult(ctx, playerColors)
	if err != nil {
		pwe.logger.WithError(err).Error("Failed to generate wheel spin result")
		return nil, err
	}

	// Calculate payouts
	payouts, err := pwe.calculatePayouts(request.Players, wheelResult, request.Configuration)
	if err != nil {
		pwe.logger.WithError(err).Error("Failed to calculate payouts")
		return nil, err
	}

	// Create game result
	result := &models.GameResult{
		SessionID:       request.SessionID,
		GameType:        models.GameTypePrizeWheel,
		WinnerUserID:    wheelResult.WinnerUserID,
		WinningPosition: wheelResult.WinningPosition,
		TotalBetPool:    pwe.calculateTotalBetPool(request.Players),
		PrizePool:       pwe.calculatePrizePool(request.Players, request.Configuration.HouseEdge),
		HouseTake:       pwe.calculateHouseTake(request.Players, request.Configuration.HouseEdge),
		Payouts:         payouts,
		GameData: map[string]interface{}{
			"winning_color":    wheelResult.WinningColor,
			"final_angle":      wheelResult.FinalAngle,
			"spin_duration":    wheelResult.SpinDuration,
			"animation_data":   wheelResult.AnimationData,
			"wheel_segments":   wheelResult.WheelSegments,
		},
		CompletedAt: time.Now(),
	}

	pwe.logger.WithFields(logrus.Fields{
		"session_id":     request.SessionID,
		"winner":         result.WinnerUserID,
		"winning_color":  wheelResult.WinningColor,
		"prize_pool":     result.PrizePool,
	}).Info("Prize Wheel game executed successfully")

	return result, nil
}

// WheelSpinResult represents the result of a wheel spin
type WheelSpinResult struct {
	WinningColor    string                 `json:"winning_color"`
	WinningPosition int                    `json:"winning_position"`
	WinnerUserID    string                 `json:"winner_user_id"`
	FinalAngle      float64                `json:"final_angle"`
	SpinDuration    int                    `json:"spin_duration"`
	AnimationData   map[string]interface{} `json:"animation_data"`
	WheelSegments   []string               `json:"wheel_segments"`
}

// validateRequest validates the game execution request
func (pwe *PrizeWheelEngine) validateRequest(request *models.GameExecutionRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if len(request.Players) == 0 {
		return models.NewGameEngineError("no players in session", nil)
	}

	if len(request.Players) > 8 {
		return models.NewGameEngineError("too many players for prize wheel", map[string]interface{}{
			"player_count": len(request.Players),
			"max_players":  8,
		})
	}

	return nil
}

// extractPlayerColors extracts color selections from players
func (pwe *PrizeWheelEngine) extractPlayerColors(players []models.GamePlayer) map[string]string {
	playerColors := make(map[string]string)
	
	for _, player := range players {
		if color, exists := player.GameData["selected_color"]; exists {
			if colorStr, ok := color.(string); ok {
				playerColors[player.UserID] = colorStr
			}
		}
	}

	return playerColors
}

// generateWheelSpinResult generates a cryptographically secure wheel spin result
func (pwe *PrizeWheelEngine) generateWheelSpinResult(ctx context.Context, playerColors map[string]string) (*WheelSpinResult, error) {
	// Default wheel colors
	availableColors := []string{"red", "blue", "green", "yellow", "purple", "orange", "pink", "teal"}
	
	// Use selected colors or default colors
	wheelColors := make([]string, 0)
	usedColors := make(map[string]bool)
	
	// Add player-selected colors first
	for _, color := range playerColors {
		if !usedColors[color] {
			wheelColors = append(wheelColors, color)
			usedColors[color] = true
		}
	}
	
	// Fill remaining slots with available colors
	for _, color := range availableColors {
		if !usedColors[color] && len(wheelColors) < 8 {
			wheelColors = append(wheelColors, color)
			usedColors[color] = true
		}
	}
	
	// Ensure minimum of 4 colors
	if len(wheelColors) < 4 {
		for _, color := range availableColors {
			if len(wheelColors) >= 4 {
				break
			}
			if !usedColors[color] {
				wheelColors = append(wheelColors, color)
				usedColors[color] = true
			}
		}
	}

	// Generate cryptographically secure random winning color
	winningIndex, err := pwe.generateSecureRandomInt(len(wheelColors))
	if err != nil {
		return nil, fmt.Errorf("failed to generate secure random: %w", err)
	}
	
	winningColor := wheelColors[winningIndex]

	// Calculate final angle based on winning color
	segmentAngle := 360.0 / float64(len(wheelColors))
	baseAngle := float64(winningIndex) * segmentAngle
	
	// Add random offset within the segment (cryptographically secure)
	offsetRange := segmentAngle * 0.8 // 80% of segment width
	randomOffset, err := pwe.generateSecureRandomFloat(offsetRange)
	if err != nil {
		return nil, fmt.Errorf("failed to generate secure random offset: %w", err)
	}
	
	finalAngle := baseAngle + randomOffset - (offsetRange / 2)
	if finalAngle < 0 {
		finalAngle += 360
	}

	// Generate spin duration (3-5 seconds)
	spinDuration, err := pwe.generateSecureRandomInt(2000)
	if err != nil {
		return nil, fmt.Errorf("failed to generate spin duration: %w", err)
	}
	spinDuration += 3000 // 3000-5000ms

	// Find winner user ID
	var winnerUserID string
	for userID, color := range playerColors {
		if color == winningColor {
			winnerUserID = userID
			break
		}
	}

	// Generate rotation count for animation
	rotations, err := pwe.generateSecureRandomFloat(3.0)
	if err != nil {
		return nil, fmt.Errorf("failed to generate rotations: %w", err)
	}
	rotations += 5.0 // 5-8 full rotations

	// Create animation data
	animationData := map[string]interface{}{
		"duration":      spinDuration,
		"rotations":     rotations,
		"easing":        "ease-out",
		"finalPosition": finalAngle,
		"segments":      len(wheelColors),
		"colors":        wheelColors,
		"segmentAngle":  segmentAngle,
	}

	return &WheelSpinResult{
		WinningColor:    winningColor,
		WinningPosition: winningIndex,
		WinnerUserID:    winnerUserID,
		FinalAngle:      finalAngle,
		SpinDuration:    spinDuration,
		AnimationData:   animationData,
		WheelSegments:   wheelColors,
	}, nil
}

// calculatePayouts calculates payouts for the game
func (pwe *PrizeWheelEngine) calculatePayouts(players []models.GamePlayer, wheelResult *WheelSpinResult, config models.GameConfiguration) ([]models.PlayerPayout, error) {
	totalBetPool := pwe.calculateTotalBetPool(players)
	prizePool := pwe.calculatePrizePool(players, config.HouseEdge)
	
	var payouts []models.PlayerPayout
	
	// Find all winners (players who selected the winning color)
	winners := make([]models.GamePlayer, 0)
	for _, player := range players {
		if color, exists := player.GameData["selected_color"]; exists {
			if colorStr, ok := color.(string); ok && colorStr == wheelResult.WinningColor {
				winners = append(winners, player)
			}
		}
	}
	
	// Calculate prize per winner
	if len(winners) > 0 {
		prizePerWinner := prizePool / int64(len(winners))
		for _, winner := range winners {
			payouts = append(payouts, models.PlayerPayout{
				UserID:    winner.UserID,
				Amount:    prizePerWinner,
				Reason:    "prize_wheel_winner",
				Processed: false,
			})
		}
	}
	
	pwe.logger.WithFields(logrus.Fields{
		"total_bet_pool": totalBetPool,
		"prize_pool":     prizePool,
		"winner_count":   len(winners),
		"prize_per_winner": func() int64 {
			if len(winners) > 0 {
				return prizePool / int64(len(winners))
			}
			return 0
		}(),
	}).Info("Calculated Prize Wheel payouts")
	
	return payouts, nil
}

// Helper methods for calculations
func (pwe *PrizeWheelEngine) calculateTotalBetPool(players []models.GamePlayer) int64 {
	total := int64(0)
	for _, player := range players {
		total += player.BetAmount
	}
	return total
}

func (pwe *PrizeWheelEngine) calculatePrizePool(players []models.GamePlayer, houseEdge float64) int64 {
	totalBetPool := pwe.calculateTotalBetPool(players)
	houseTake := int64(float64(totalBetPool) * houseEdge)
	return totalBetPool - houseTake
}

func (pwe *PrizeWheelEngine) calculateHouseTake(players []models.GamePlayer, houseEdge float64) int64 {
	totalBetPool := pwe.calculateTotalBetPool(players)
	return int64(float64(totalBetPool) * houseEdge)
}

// generateSecureRandomInt generates a cryptographically secure random integer
func (pwe *PrizeWheelEngine) generateSecureRandomInt(max int) (int, error) {
	if max <= 0 {
		return 0, fmt.Errorf("max must be positive")
	}
	
	// Generate random bytes
	randomBytes := make([]byte, 4)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, err
	}
	
	// Convert to uint32 and mod by max
	randomValue := uint32(randomBytes[0])<<24 | uint32(randomBytes[1])<<16 | uint32(randomBytes[2])<<8 | uint32(randomBytes[3])
	return int(randomValue) % max, nil
}

// generateSecureRandomFloat generates a cryptographically secure random float in range [0, max)
func (pwe *PrizeWheelEngine) generateSecureRandomFloat(max float64) (float64, error) {
	randomBytes := make([]byte, 8)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, err
	}
	
	// Convert to uint64 and normalize to [0, 1)
	randomValue := uint64(randomBytes[0])<<56 | uint64(randomBytes[1])<<48 | uint64(randomBytes[2])<<40 | uint64(randomBytes[3])<<32 |
		uint64(randomBytes[4])<<24 | uint64(randomBytes[5])<<16 | uint64(randomBytes[6])<<8 | uint64(randomBytes[7])
	
	normalized := float64(randomValue) / float64(math.MaxUint64)
	return normalized * max, nil
}
