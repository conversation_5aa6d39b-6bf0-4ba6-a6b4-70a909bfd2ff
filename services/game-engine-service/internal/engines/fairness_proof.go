package engines

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/models"
)

// FairnessProofGenerator generates cryptographic proofs for game fairness
type FairnessProofGenerator struct {
	logger *logrus.Logger
}

// NewFairnessProofGenerator creates a new fairness proof generator
func NewFairnessProofGenerator(logger *logrus.Logger) *FairnessProofGenerator {
	return &FairnessProofGenerator{
		logger: logger,
	}
}

// GenerateGameProof generates a comprehensive fairness proof for a game
func (fpg *FairnessProofGenerator) GenerateGameProof(request *models.FairnessProofRequest) (*models.FairnessProof, error) {
	fpg.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"game_type":  request.GameType,
	}).Info("Generating fairness proof")

	// Validate request
	if err := fpg.validateProofRequest(request); err != nil {
		return nil, err
	}

	// Generate proof based on game type
	var proof *models.FairnessProof
	var err error

	switch request.GameType {
	case models.GameTypePrizeWheel:
		proof, err = fpg.generatePrizeWheelProof(request)
	case models.GameTypeAmidakuji:
		proof, err = fpg.generateAmidakujiProof(request)
	default:
		return nil, models.NewGameEngineError("unsupported game type for fairness proof", map[string]interface{}{
			"game_type": request.GameType,
		})
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate game-specific proof: %w", err)
	}

	// Add common proof elements
	proof.SessionID = request.SessionID
	proof.GameType = request.GameType
	proof.GeneratedAt = time.Now()
	proof.Algorithm = "SHA256 + crypto/rand"
	proof.Version = "1.0"

	// Generate overall proof hash
	proofHash, err := fpg.generateProofHash(proof)
	if err != nil {
		return nil, fmt.Errorf("failed to generate proof hash: %w", err)
	}
	proof.ProofHash = proofHash

	fpg.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"proof_hash": proof.ProofHash,
		"game_type":  request.GameType,
	}).Info("Generated fairness proof")

	return proof, nil
}

// generatePrizeWheelProof generates fairness proof for Prize Wheel game
func (fpg *FairnessProofGenerator) generatePrizeWheelProof(request *models.FairnessProofRequest) (*models.FairnessProof, error) {
	// Extract wheel-specific data
	wheelData, ok := request.GameData["wheel_result"]
	if !ok {
		return nil, models.NewGameEngineError("wheel result not found in game data", nil)
	}

	// Create proof components
	components := map[string]interface{}{
		"random_seed":      request.RandomSeed,
		"wheel_segments":   request.GameData["wheel_segments"],
		"winning_color":    request.GameData["winning_color"],
		"final_angle":      request.GameData["final_angle"],
		"spin_duration":    request.GameData["spin_duration"],
		"player_selections": request.GameData["player_selections"],
		"timestamp":        time.Now().UnixNano(),
	}

	// Generate component hashes
	componentHashes := make(map[string]string)
	for key, value := range components {
		hash, err := fpg.hashComponent(key, value)
		if err != nil {
			return nil, fmt.Errorf("failed to hash component %s: %w", key, err)
		}
		componentHashes[key] = hash
	}

	// Create verification steps
	verificationSteps := []models.VerificationStep{
		{
			Step:        1,
			Description: "Verify random seed integrity",
			Input:       request.RandomSeed,
			Expected:    componentHashes["random_seed"],
			Method:      "SHA256 hash verification",
		},
		{
			Step:        2,
			Description: "Verify wheel segment configuration",
			Input:       fmt.Sprintf("%v", request.GameData["wheel_segments"]),
			Expected:    componentHashes["wheel_segments"],
			Method:      "Configuration hash verification",
		},
		{
			Step:        3,
			Description: "Verify winning position calculation",
			Input:       fmt.Sprintf("%v", request.GameData["final_angle"]),
			Expected:    componentHashes["final_angle"],
			Method:      "Angle calculation verification",
		},
		{
			Step:        4,
			Description: "Verify player selections integrity",
			Input:       fmt.Sprintf("%v", request.GameData["player_selections"]),
			Expected:    componentHashes["player_selections"],
			Method:      "Selection hash verification",
		},
	}

	proof := &models.FairnessProof{
		GameSpecificData:  wheelData,
		Components:        components,
		ComponentHashes:   componentHashes,
		VerificationSteps: verificationSteps,
		RandomSeed:        request.RandomSeed,
		IsVerifiable:      true,
	}

	return proof, nil
}

// generateAmidakujiProof generates fairness proof for Amidakuji game
func (fpg *FairnessProofGenerator) generateAmidakujiProof(request *models.FairnessProofRequest) (*models.FairnessProof, error) {
	// Extract Amidakuji-specific data
	amidakujiData, ok := request.GameData["amidakuji_result"]
	if !ok {
		return nil, models.NewGameEngineError("amidakuji result not found in game data", nil)
	}

	// Create proof components
	components := map[string]interface{}{
		"random_seed":      request.RandomSeed,
		"pattern":          request.GameData["pattern"],
		"winning_position": request.GameData["winning_position"],
		"path_trace":       request.GameData["path_trace"],
		"player_count":     request.GameData["player_count"],
		"timestamp":        time.Now().UnixNano(),
	}

	// Generate component hashes
	componentHashes := make(map[string]string)
	for key, value := range components {
		hash, err := fpg.hashComponent(key, value)
		if err != nil {
			return nil, fmt.Errorf("failed to hash component %s: %w", key, err)
		}
		componentHashes[key] = hash
	}

	// Create verification steps
	verificationSteps := []models.VerificationStep{
		{
			Step:        1,
			Description: "Verify random seed integrity",
			Input:       request.RandomSeed,
			Expected:    componentHashes["random_seed"],
			Method:      "SHA256 hash verification",
		},
		{
			Step:        2,
			Description: "Verify pattern generation",
			Input:       fmt.Sprintf("%v", request.GameData["pattern"]),
			Expected:    componentHashes["pattern"],
			Method:      "Pattern hash verification",
		},
		{
			Step:        3,
			Description: "Verify path tracing",
			Input:       fmt.Sprintf("%v", request.GameData["path_trace"]),
			Expected:    componentHashes["path_trace"],
			Method:      "Path trace verification",
		},
		{
			Step:        4,
			Description: "Verify winner determination",
			Input:       fmt.Sprintf("%v", request.GameData["winning_position"]),
			Expected:    componentHashes["winning_position"],
			Method:      "Winner position verification",
		},
	}

	proof := &models.FairnessProof{
		GameSpecificData:  amidakujiData,
		Components:        components,
		ComponentHashes:   componentHashes,
		VerificationSteps: verificationSteps,
		RandomSeed:        request.RandomSeed,
		IsVerifiable:      true,
	}

	return proof, nil
}

// VerifyGameProof verifies a fairness proof
func (fpg *FairnessProofGenerator) VerifyGameProof(proof *models.FairnessProof) (*models.VerificationResult, error) {
	fpg.logger.WithFields(logrus.Fields{
		"session_id": proof.SessionID,
		"proof_hash": proof.ProofHash,
	}).Info("Verifying fairness proof")

	result := &models.VerificationResult{
		SessionID:    proof.SessionID,
		ProofHash:    proof.ProofHash,
		IsValid:      true,
		VerifiedAt:   time.Now(),
		StepResults:  make([]models.StepResult, 0),
		ErrorDetails: make([]string, 0),
	}

	// Verify each step
	for _, step := range proof.VerificationSteps {
		stepResult, err := fpg.verifyStep(step, proof.Components)
		if err != nil {
			result.IsValid = false
			result.ErrorDetails = append(result.ErrorDetails, fmt.Sprintf("Step %d failed: %v", step.Step, err))
		}
		result.StepResults = append(result.StepResults, *stepResult)
	}

	// Verify overall proof hash
	expectedHash, err := fpg.generateProofHash(proof)
	if err != nil {
		result.IsValid = false
		result.ErrorDetails = append(result.ErrorDetails, fmt.Sprintf("Failed to generate expected hash: %v", err))
	} else if proof.ProofHash != expectedHash {
		result.IsValid = false
		result.ErrorDetails = append(result.ErrorDetails, "Proof hash mismatch")
	}

	fpg.logger.WithFields(logrus.Fields{
		"session_id": proof.SessionID,
		"is_valid":   result.IsValid,
		"step_count": len(result.StepResults),
	}).Info("Fairness proof verification completed")

	return result, nil
}

// validateProofRequest validates a fairness proof request
func (fpg *FairnessProofGenerator) validateProofRequest(request *models.FairnessProofRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if request.RandomSeed == "" {
		return models.NewGameEngineError("random seed cannot be empty", nil)
	}

	if request.GameData == nil || len(request.GameData) == 0 {
		return models.NewGameEngineError("game data cannot be empty", nil)
	}

	return nil
}

// hashComponent generates a hash for a proof component
func (fpg *FairnessProofGenerator) hashComponent(key string, value interface{}) (string, error) {
	// Convert value to JSON for consistent hashing
	jsonBytes, err := json.Marshal(value)
	if err != nil {
		return "", fmt.Errorf("failed to marshal component value: %w", err)
	}

	// Create hash input
	hashInput := fmt.Sprintf("%s:%s", key, string(jsonBytes))
	
	// Generate hash
	hasher := sha256.New()
	hasher.Write([]byte(hashInput))
	hash := hasher.Sum(nil)

	return fmt.Sprintf("%x", hash), nil
}

// generateProofHash generates the overall proof hash
func (fpg *FairnessProofGenerator) generateProofHash(proof *models.FairnessProof) (string, error) {
	// Create proof summary for hashing
	proofSummary := map[string]interface{}{
		"session_id":         proof.SessionID,
		"game_type":          proof.GameType,
		"random_seed":        proof.RandomSeed,
		"component_hashes":   proof.ComponentHashes,
		"verification_steps": len(proof.VerificationSteps),
		"algorithm":          proof.Algorithm,
		"version":            proof.Version,
	}

	// Convert to JSON
	jsonBytes, err := json.Marshal(proofSummary)
	if err != nil {
		return "", fmt.Errorf("failed to marshal proof summary: %w", err)
	}

	// Generate hash
	hasher := sha256.New()
	hasher.Write(jsonBytes)
	hash := hasher.Sum(nil)

	return fmt.Sprintf("%x", hash), nil
}

// verifyStep verifies a single verification step
func (fpg *FairnessProofGenerator) verifyStep(step models.VerificationStep, components map[string]interface{}) (*models.StepResult, error) {
	// Generate hash for the input
	hasher := sha256.New()
	hasher.Write([]byte(step.Input))
	actualHash := fmt.Sprintf("%x", hasher.Sum(nil))

	// Compare with expected hash
	isValid := actualHash == step.Expected

	result := &models.StepResult{
		Step:        step.Step,
		Description: step.Description,
		IsValid:     isValid,
		ActualHash:  actualHash,
		ExpectedHash: step.Expected,
		VerifiedAt:  time.Now(),
	}

	if !isValid {
		return result, fmt.Errorf("hash mismatch: expected %s, got %s", step.Expected, actualHash)
	}

	return result, nil
}

// GetProofStats returns statistics about proof generation
func (fpg *FairnessProofGenerator) GetProofStats() *models.ProofStats {
	return &models.ProofStats{
		Algorithm:     "SHA256",
		HashFunction:  "SHA256",
		KeySize:       256, // bits
		Verified:      true,
		SupportedGames: []string{"prize_wheel", "amidakuji"},
	}
}
