package engines

import (
	"context"
	"crypto/rand"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/models"
)

// AmidakujiEngine handles Amidakuji (Ghost Leg) game logic
type AmidakujiEngine struct {
	logger *logrus.Logger
}

// NewAmidakujiEngine creates a new Amidakuji engine
func NewAmidakujiEngine(logger *logrus.Logger) *AmidakujiEngine {
	return &AmidakujiEngine{
		logger: logger,
	}
}

// ExecuteGame executes an Amidakuji game
func (ae *AmidakujiEngine) ExecuteGame(ctx context.Context, request *models.GameExecutionRequest) (*models.GameResult, error) {
	ae.logger.WithFields(logrus.Fields{
		"session_id":   request.SessionID,
		"player_count": len(request.Players),
		"game_type":    "amidakuji",
	}).Info("Executing Amidakuji game")

	// Validate request
	if err := ae.validateRequest(request); err != nil {
		return nil, err
	}

	// Generate Amidakuji pattern
	pattern, err := ae.generateAmidakujiPattern(len(request.Players))
	if err != nil {
		ae.logger.WithError(err).Error("Failed to generate Amidakuji pattern")
		return nil, err
	}

	// Trace paths to determine winner
	winnerPosition, pathTrace, err := ae.traceWinningPath(pattern, len(request.Players))
	if err != nil {
		ae.logger.WithError(err).Error("Failed to trace winning path")
		return nil, err
	}

	// Get winner player
	if winnerPosition >= len(request.Players) {
		return nil, models.NewGameEngineError("invalid winner position", map[string]interface{}{
			"winner_position": winnerPosition,
			"player_count":    len(request.Players),
		})
	}

	winnerPlayer := request.Players[winnerPosition]

	// Calculate payouts
	payouts, err := ae.calculatePayouts(request.Players, winnerPlayer, request.Configuration)
	if err != nil {
		ae.logger.WithError(err).Error("Failed to calculate payouts")
		return nil, err
	}

	// Create game result
	result := &models.GameResult{
		SessionID:       request.SessionID,
		GameType:        models.GameTypeAmidakuji,
		WinnerUserID:    winnerPlayer.UserID,
		WinningPosition: winnerPosition,
		TotalBetPool:    ae.calculateTotalBetPool(request.Players),
		PrizePool:       ae.calculatePrizePool(request.Players, request.Configuration.HouseEdge),
		HouseTake:       ae.calculateHouseTake(request.Players, request.Configuration.HouseEdge),
		Payouts:         payouts,
		GameData: map[string]interface{}{
			"pattern":          pattern,
			"winning_position": winnerPosition,
			"path_trace":       pathTrace,
			"ladder_count":     len(pattern),
			"player_count":     len(request.Players),
		},
		CompletedAt: time.Now(),
	}

	ae.logger.WithFields(logrus.Fields{
		"session_id":      request.SessionID,
		"winner":          result.WinnerUserID,
		"winning_position": winnerPosition,
		"prize_pool":      result.PrizePool,
		"pattern_rows":    len(pattern),
	}).Info("Amidakuji game executed successfully")

	return result, nil
}

// AmidakujiPattern represents the ladder pattern
type AmidakujiPattern [][]bool

// PathTrace represents the path taken through the ladder
type PathTrace []PathStep

// PathStep represents a single step in the path
type PathStep struct {
	Row      int  `json:"row"`
	Position int  `json:"position"`
	Crossed  bool `json:"crossed"`
}

// validateRequest validates the game execution request
func (ae *AmidakujiEngine) validateRequest(request *models.GameExecutionRequest) error {
	if request.SessionID == "" {
		return models.NewGameEngineError("session ID cannot be empty", nil)
	}

	if len(request.Players) == 0 {
		return models.NewGameEngineError("no players in session", nil)
	}

	if len(request.Players) > 10 {
		return models.NewGameEngineError("too many players for Amidakuji", map[string]interface{}{
			"player_count": len(request.Players),
			"max_players":  10,
		})
	}

	if len(request.Players) < 2 {
		return models.NewGameEngineError("need at least 2 players for Amidakuji", map[string]interface{}{
			"player_count": len(request.Players),
			"min_players":  2,
		})
	}

	return nil
}

// generateAmidakujiPattern generates a cryptographically secure Amidakuji pattern
func (ae *AmidakujiEngine) generateAmidakujiPattern(playerCount int) (AmidakujiPattern, error) {
	// Calculate number of rows (typically 3-5 times the player count)
	minRows := playerCount * 3
	maxRows := playerCount * 5
	
	rowCount, err := ae.generateSecureRandomInt(maxRows - minRows + 1)
	if err != nil {
		return nil, fmt.Errorf("failed to generate row count: %w", err)
	}
	rowCount += minRows

	pattern := make(AmidakujiPattern, rowCount)
	
	// Generate each row
	for i := 0; i < rowCount; i++ {
		row, err := ae.generatePatternRow(playerCount)
		if err != nil {
			return nil, fmt.Errorf("failed to generate pattern row %d: %w", i, err)
		}
		pattern[i] = row
	}

	ae.logger.WithFields(logrus.Fields{
		"player_count": playerCount,
		"row_count":    rowCount,
		"pattern_size": fmt.Sprintf("%dx%d", rowCount, playerCount-1),
	}).Debug("Generated Amidakuji pattern")

	return pattern, nil
}

// generatePatternRow generates a single row of the Amidakuji pattern
func (ae *AmidakujiEngine) generatePatternRow(playerCount int) ([]bool, error) {
	row := make([]bool, playerCount-1) // playerCount-1 possible horizontal lines
	
	// Randomly place horizontal lines with constraints
	// Constraint: no two adjacent horizontal lines in the same row
	for i := 0; i < len(row); i++ {
		// Generate random decision for this position
		hasLine, err := ae.generateSecureRandomBool()
		if err != nil {
			return nil, fmt.Errorf("failed to generate random bool for position %d: %w", i, err)
		}
		
		// Apply constraint: if previous position has a line, this position cannot
		if i > 0 && row[i-1] {
			hasLine = false
		}
		
		row[i] = hasLine
		
		// If this position has a line, skip the next position to avoid adjacency
		if hasLine && i < len(row)-1 {
			i++ // Skip next position
		}
	}
	
	return row, nil
}

// traceWinningPath traces the path through the Amidakuji pattern to find the winner
func (ae *AmidakujiEngine) traceWinningPath(pattern AmidakujiPattern, playerCount int) (int, PathTrace, error) {
	// Generate random starting position (cryptographically secure)
	startPosition, err := ae.generateSecureRandomInt(playerCount)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to generate start position: %w", err)
	}

	currentPosition := startPosition
	pathTrace := make(PathTrace, 0, len(pattern)+1)
	
	// Add starting position to trace
	pathTrace = append(pathTrace, PathStep{
		Row:      -1, // Starting position
		Position: currentPosition,
		Crossed:  false,
	})

	// Trace through each row
	for rowIndex, row := range pattern {
		crossed := false
		
		// Check if there's a horizontal line to the left
		if currentPosition > 0 && row[currentPosition-1] {
			currentPosition--
			crossed = true
		} else if currentPosition < len(row) && row[currentPosition] {
			// Check if there's a horizontal line to the right
			currentPosition++
			crossed = true
		}
		
		// Add step to trace
		pathTrace = append(pathTrace, PathStep{
			Row:      rowIndex,
			Position: currentPosition,
			Crossed:  crossed,
		})
	}

	ae.logger.WithFields(logrus.Fields{
		"start_position":  startPosition,
		"winner_position": currentPosition,
		"path_length":     len(pathTrace),
	}).Debug("Traced Amidakuji path")

	return currentPosition, pathTrace, nil
}

// calculatePayouts calculates payouts for the Amidakuji game
func (ae *AmidakujiEngine) calculatePayouts(players []models.GamePlayer, winner models.GamePlayer, config models.GameConfiguration) ([]models.PlayerPayout, error) {
	prizePool := ae.calculatePrizePool(players, config.HouseEdge)
	
	payouts := []models.PlayerPayout{
		{
			UserID:    winner.UserID,
			Amount:    prizePool,
			Reason:    "amidakuji_winner",
			Processed: false,
		},
	}
	
	ae.logger.WithFields(logrus.Fields{
		"winner":     winner.UserID,
		"prize_pool": prizePool,
	}).Info("Calculated Amidakuji payouts")
	
	return payouts, nil
}

// Helper methods for calculations
func (ae *AmidakujiEngine) calculateTotalBetPool(players []models.GamePlayer) int64 {
	total := int64(0)
	for _, player := range players {
		total += player.BetAmount
	}
	return total
}

func (ae *AmidakujiEngine) calculatePrizePool(players []models.GamePlayer, houseEdge float64) int64 {
	totalBetPool := ae.calculateTotalBetPool(players)
	houseTake := int64(float64(totalBetPool) * houseEdge)
	return totalBetPool - houseTake
}

func (ae *AmidakujiEngine) calculateHouseTake(players []models.GamePlayer, houseEdge float64) int64 {
	totalBetPool := ae.calculateTotalBetPool(players)
	return int64(float64(totalBetPool) * houseEdge)
}

// generateSecureRandomInt generates a cryptographically secure random integer
func (ae *AmidakujiEngine) generateSecureRandomInt(max int) (int, error) {
	if max <= 0 {
		return 0, fmt.Errorf("max must be positive")
	}
	
	randomBytes := make([]byte, 4)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return 0, err
	}
	
	randomValue := uint32(randomBytes[0])<<24 | uint32(randomBytes[1])<<16 | uint32(randomBytes[2])<<8 | uint32(randomBytes[3])
	return int(randomValue) % max, nil
}

// generateSecureRandomBool generates a cryptographically secure random boolean
func (ae *AmidakujiEngine) generateSecureRandomBool() (bool, error) {
	randomBytes := make([]byte, 1)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return false, err
	}
	
	return randomBytes[0]%2 == 1, nil
}
