package services

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/internal/config"
	"github.com/xzgame/game-engine-service/internal/engines"
	"github.com/xzgame/game-engine-service/internal/models"
	"github.com/xzgame/game-engine-service/internal/repositories"
	"github.com/xzgame/game-engine-service/pkg/redis"
)

// GameEngineService defines the interface for game engine operations
type GameEngineService interface {
	// Game execution
	ExecuteGame(ctx context.Context, request *models.GameExecutionRequest) (*models.GameResult, error)

	// Random generation
	GenerateRandom(ctx context.Context, request *models.RandomRequest) (*models.RandomResponse, error)
	GenerateGameSeed(ctx context.Context, sessionID string, playerCount int) (*models.GameSeed, error)

	// Result calculation
	CalculateResults(ctx context.Context, request *models.CalculationRequest) (*models.CalculationResponse, error)
	CalculatePayouts(ctx context.Context, request *models.PayoutRequest) (*models.PayoutResponse, error)

	// Fairness verification
	GenerateFairnessProof(ctx context.Context, request *models.FairnessProofRequest) (*models.FairnessProof, error)
	VerifyFairnessProof(ctx context.Context, proof *models.FairnessProof) (*models.VerificationResult, error)

	// Statistics
	GetActiveGameCount(ctx context.Context) (int64, error)
	GetEngineStats(ctx context.Context) (*models.EngineStats, error)
}

// gameEngineService implements the GameEngineService interface using modular engines
type gameEngineService struct {
	sessionRepo repositories.GameSessionRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger

	// Modular engines
	prizeWheelEngine *engines.PrizeWheelEngine
	amidakujiEngine  *engines.AmidakujiEngine
	randomGenerator  *engines.RandomGenerator
	fairnessProof    *engines.FairnessProofGenerator
	resultCalculator *engines.ResultCalculator
}

// NewGameEngineService creates a new game engine service with modular engines
func NewGameEngineService(
	sessionRepo repositories.GameSessionRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
) GameEngineService {
	// Create modular engines
	prizeWheelEngine := engines.NewPrizeWheelEngine(logger)
	amidakujiEngine := engines.NewAmidakujiEngine(logger)
	randomGenerator := engines.NewRandomGenerator(logger)
	fairnessProof := engines.NewFairnessProofGenerator(logger)
	resultCalculator := engines.NewResultCalculator(logger)

	return &gameEngineService{
		sessionRepo:      sessionRepo,
		redisClient:      redisClient,
		config:           config,
		logger:           logger,
		prizeWheelEngine: prizeWheelEngine,
		amidakujiEngine:  amidakujiEngine,
		randomGenerator:  randomGenerator,
		fairnessProof:    fairnessProof,
		resultCalculator: resultCalculator,
	}
}

// Game execution implementation

// ExecuteGame executes a game using the appropriate engine
func (s *gameEngineService) ExecuteGame(ctx context.Context, request *models.GameExecutionRequest) (*models.GameResult, error) {
	s.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"game_type":  request.GameType,
	}).Info("Executing game")

	switch request.GameType {
	case models.GameTypePrizeWheel:
		return s.prizeWheelEngine.ExecuteGame(ctx, request)
	case models.GameTypeAmidakuji:
		return s.amidakujiEngine.ExecuteGame(ctx, request)
	default:
		return nil, models.NewGameEngineError("unsupported game type", map[string]interface{}{
			"game_type": request.GameType,
		})
	}
}

// Random generation implementation

// GenerateRandom generates a cryptographically secure random number
func (s *gameEngineService) GenerateRandom(ctx context.Context, request *models.RandomRequest) (*models.RandomResponse, error) {
	return s.randomGenerator.GenerateSecureRandom(request)
}

// GenerateGameSeed generates a cryptographically secure seed for a game
func (s *gameEngineService) GenerateGameSeed(ctx context.Context, sessionID string, playerCount int) (*models.GameSeed, error) {
	return s.randomGenerator.GenerateGameSeed(sessionID, playerCount)
}

// Result calculation implementation

// CalculateResults calculates comprehensive game results
func (s *gameEngineService) CalculateResults(ctx context.Context, request *models.CalculationRequest) (*models.CalculationResponse, error) {
	return s.resultCalculator.CalculateGameResults(request)
}

// CalculatePayouts calculates player payouts
func (s *gameEngineService) CalculatePayouts(ctx context.Context, request *models.PayoutRequest) (*models.PayoutResponse, error) {
	return s.resultCalculator.CalculatePayouts(request)
}

// Fairness verification implementation

// GenerateFairnessProof generates a fairness proof for a game
func (s *gameEngineService) GenerateFairnessProof(ctx context.Context, request *models.FairnessProofRequest) (*models.FairnessProof, error) {
	return s.fairnessProof.GenerateGameProof(request)
}

// VerifyFairnessProof verifies a fairness proof
func (s *gameEngineService) VerifyFairnessProof(ctx context.Context, proof *models.FairnessProof) (*models.VerificationResult, error) {
	return s.fairnessProof.VerifyGameProof(proof)
}

// Statistics implementation

// GetActiveGameCount returns the number of active game sessions
func (s *gameEngineService) GetActiveGameCount(ctx context.Context) (int64, error) {
	return s.sessionRepo.GetActiveSessionCount(ctx)
}

// GetEngineStats returns comprehensive engine statistics
func (s *gameEngineService) GetEngineStats(ctx context.Context) (*models.EngineStats, error) {
	activeCount, err := s.GetActiveGameCount(ctx)
	if err != nil {
		return nil, err
	}

	stats := &models.EngineStats{
		ActiveGames:    activeCount,
		SupportedGames: []string{"prize_wheel", "amidakuji"},
		RandomStats:    s.randomGenerator.GetRandomStats(),
		ProofStats:     s.fairnessProof.GetProofStats(),
		Version:        "1.0.0",
		Uptime:         time.Since(time.Now()), // This should be tracked properly
	}

	return stats, nil
}
