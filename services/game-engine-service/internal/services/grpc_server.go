package services

import (
"github.com/sirupsen/logrus"
pb "github.com/xzgame/game-engine-service/proto"
)

// GRPCServer implements the gRPC GameEngineService interface
type GRPCServer struct {
	pb.UnimplementedGameEngineServiceServer
	gameEngineService GameEngineService
	logger            *logrus.Logger
}

// NewGRPCServer creates a new gRPC server
func NewGRPCServer(gameEngineService GameEngineService, logger *logrus.Logger) *GRPCServer {
	return &GRPCServer{
		gameEngineService: gameEngineService,
		logger:            logger,
	}
}
