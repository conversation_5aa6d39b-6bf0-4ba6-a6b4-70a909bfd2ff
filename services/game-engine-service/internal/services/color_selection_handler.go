package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-engine-service/pkg/redis"
)

// ColorSelectionHandler handles color selection logic for prize wheel games
type ColorSelectionHandler struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewColorSelectionHandler creates a new color selection handler
func NewColorSelectionHandler(redisClient *redis.RedisClient, logger *logrus.Logger) *ColorSelectionHandler {
	return &ColorSelectionHandler{
		redisClient: redisClient,
		logger:      logger,
	}
}

// ColorSelectionRequest represents a color selection request
type ColorSelectionRequest struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Color     string `json:"color"`
	Timestamp string `json:"timestamp"`
}

// ColorStateData represents the current color state for a room
type ColorStateData struct {
	PlayerColors map[string]interface{} `json:"playerColors"`
	LastUpdated  time.Time              `json:"lastUpdated"`
}

// HandleColorSelection processes color selection requests
func (h *ColorSelectionHandler) HandleColorSelection(ctx context.Context, request ColorSelectionRequest) error {
	h.logger.WithFields(logrus.Fields{
		"roomId":   request.RoomID,
		"userId":   request.UserID,
		"username": request.Username,
		"color":    request.Color,
	}).Info("Processing color selection")

	// Validate color
	if !h.isValidColor(request.Color) {
		return fmt.Errorf("invalid color: %s", request.Color)
	}

	// Get current color state
	colorState, err := h.getColorState(ctx, request.RoomID)
	if err != nil {
		return fmt.Errorf("failed to get color state: %w", err)
	}

	// Check if color is already taken
	if h.isColorTaken(colorState, request.Color) {
		return fmt.Errorf("color %s is already taken", request.Color)
	}

	// Remove user's previous color selection if any
	delete(colorState.PlayerColors, request.UserID)

	// Add new color selection
	colorState.PlayerColors[request.UserID] = map[string]interface{}{
		"userId":     request.UserID,
		"username":   request.Username,
		"colorId":    request.Color,
		"selectedAt": time.Now().Format(time.RFC3339),
	}

	// Update timestamp
	colorState.LastUpdated = time.Now()

	// Save updated color state
	if err := h.saveColorState(ctx, request.RoomID, colorState); err != nil {
		return fmt.Errorf("failed to save color state: %w", err)
	}

	// Broadcast color events
	h.broadcastColorEvents(ctx, request.RoomID, request.UserID, request.Username, request.Color, colorState)

	h.logger.WithFields(logrus.Fields{
		"roomId":          request.RoomID,
		"userId":          request.UserID,
		"color":           request.Color,
		"totalSelections": len(colorState.PlayerColors),
	}).Info("Color selection processed successfully")

	return nil
}

// GetColorState returns the current color state for a room
func (h *ColorSelectionHandler) GetColorState(ctx context.Context, roomID string) (*ColorStateData, error) {
	return h.getColorState(ctx, roomID)
}

// GetAvailableColors returns available colors for a room
func (h *ColorSelectionHandler) GetAvailableColors(ctx context.Context, roomID string) ([]map[string]interface{}, error) {
	colorState, err := h.getColorState(ctx, roomID)
	if err != nil {
		return nil, err
	}

	return h.getAvailableColors(colorState), nil
}

// Private helper methods

func (h *ColorSelectionHandler) getColorState(ctx context.Context, roomID string) (*ColorStateData, error) {
	key := fmt.Sprintf("room:%s:color_state", roomID)

	var colorState ColorStateData
	err := h.redisClient.Get(ctx, key, &colorState)
	if err != nil {
		// Initialize empty color state if not found
		colorState = ColorStateData{
			PlayerColors: make(map[string]interface{}),
			LastUpdated:  time.Now(),
		}
	}

	return &colorState, nil
}

func (h *ColorSelectionHandler) saveColorState(ctx context.Context, roomID string, colorState *ColorStateData) error {
	key := fmt.Sprintf("room:%s:color_state", roomID)
	return h.redisClient.Set(ctx, key, colorState, 3600) // 1 hour TTL
}

func (h *ColorSelectionHandler) isValidColor(color string) bool {
	validColors := map[string]bool{
		"red":    true,
		"blue":   true,
		"green":  true,
		"yellow": true,
		"purple": true,
		"orange": true,
		"pink":   true,
		"teal":   true,
	}
	return validColors[color]
}

func (h *ColorSelectionHandler) isColorTaken(colorState *ColorStateData, color string) bool {
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]interface{}); ok {
			if colorID, ok := colorMap["colorId"].(string); ok && colorID == color {
				return true
			}
		}
	}
	return false
}

func (h *ColorSelectionHandler) getAllColors() []map[string]interface{} {
	return []map[string]interface{}{
		{"id": "red", "name": "Red", "hex": "#FF0000"},
		{"id": "blue", "name": "Blue", "hex": "#0000FF"},
		{"id": "green", "name": "Green", "hex": "#00FF00"},
		{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
		{"id": "purple", "name": "Purple", "hex": "#800080"},
		{"id": "orange", "name": "Orange", "hex": "#FFA500"},
		{"id": "pink", "name": "Pink", "hex": "#FFC0CB"},
		{"id": "teal", "name": "Teal", "hex": "#008080"},
	}
}

func (h *ColorSelectionHandler) getAvailableColors(colorState *ColorStateData) []map[string]interface{} {
	allColors := h.getAllColors()
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]interface{}); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter available colors
	var availableColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && !takenColorIds[colorID] {
			availableColors = append(availableColors, color)
		}
	}

	return availableColors
}

func (h *ColorSelectionHandler) getSelectedColors(colorState *ColorStateData) []map[string]interface{} {
	var selectedColors []map[string]interface{}
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]interface{}); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				selectedColors = append(selectedColors, map[string]interface{}{
					"id":         colorID,
					"name":       h.getColorName(colorID),
					"hex":        h.getColorHex(colorID),
					"userId":     userID,
					"username":   colorMap["username"],
					"selectedAt": colorMap["selectedAt"],
				})
			}
		}
	}
	return selectedColors
}

func (h *ColorSelectionHandler) getColorName(colorID string) string {
	colorNames := map[string]string{
		"red":    "Red",
		"blue":   "Blue",
		"green":  "Green",
		"yellow": "Yellow",
		"purple": "Purple",
		"orange": "Orange",
		"pink":   "Pink",
		"teal":   "Teal",
	}
	if name, exists := colorNames[colorID]; exists {
		return name
	}
	return colorID
}

func (h *ColorSelectionHandler) getColorHex(colorID string) string {
	colorHex := map[string]string{
		"red":    "#FF0000",
		"blue":   "#0000FF",
		"green":  "#00FF00",
		"yellow": "#FFFF00",
		"purple": "#800080",
		"orange": "#FFA500",
		"pink":   "#FFC0CB",
		"teal":   "#008080",
	}
	if hex, exists := colorHex[colorID]; exists {
		return hex
	}
	return "#000000"
}

// broadcastColorEvents broadcasts color events to notification service
func (h *ColorSelectionHandler) broadcastColorEvents(ctx context.Context, roomID, userID, username, color string, colorState *ColorStateData) {
	// Broadcast color availability update
	h.broadcastColorAvailabilityUpdate(ctx, roomID, colorState)

	h.logger.WithFields(logrus.Fields{
		"roomId": roomID,
		"userId": userID,
		"color":  color,
	}).Debug("Broadcasted color events")
}

// broadcastColorAvailabilityUpdate broadcasts color availability update
func (h *ColorSelectionHandler) broadcastColorAvailabilityUpdate(ctx context.Context, roomID string, colorState *ColorStateData) {
	// Build color availability event data
	colorAvailabilityData := map[string]interface{}{
		"roomId":    roomID,
		"timestamp": time.Now().Format(time.RFC3339),
		"colors": map[string]interface{}{
			"available": h.getAvailableColors(colorState),
			"selected":  h.getSelectedColors(colorState),
			"statistics": map[string]interface{}{
				"totalColors":    8,
				"availableCount": len(h.getAvailableColors(colorState)),
				"selectedCount":  len(colorState.PlayerColors),
				"selectionRate":  h.calculateSelectionRate(colorState),
			},
		},
		"message": "Color availability updated after selection",
	}

	// Create Redis message for notification service
	redisMessage := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "color_availability_updated",
			"payload":   colorAvailabilityData,
			"timestamp": time.Now().Format(time.RFC3339),
		},
		"metadata": map[string]interface{}{
			"serviceId": "game-engine-service",
			"version":   "1.0.0",
			"priority":  1,
		},
	}

	// Publish to notification service
	channel := "notification:events"
	messageBytes, err := json.Marshal(redisMessage)
	if err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to marshal color availability update")
		return
	}

	if err := h.redisClient.Publish(ctx, channel, messageBytes); err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to publish color availability update")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":         roomID,
		"availableCount": len(h.getAvailableColors(colorState)),
		"selectedCount":  len(colorState.PlayerColors),
	}).Info("Broadcasted color availability update")
}

// calculateSelectionRate calculates the percentage of colors that have been selected
func (h *ColorSelectionHandler) calculateSelectionRate(colorState *ColorStateData) float64 {
	totalColors := 8.0
	selectedColors := float64(len(colorState.PlayerColors))
	if totalColors == 0 {
		return 0.0
	}
	return (selectedColors / totalColors) * 100.0
}
