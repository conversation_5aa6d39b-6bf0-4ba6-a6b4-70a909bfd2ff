syntax = "proto3";

package game_engine;

option go_package = "github.com/xzgame/game-engine-service/proto";

import "google/protobuf/timestamp.proto";

// GameEngineService provides game logic execution and management
service GameEngineService {
  // Session management
  rpc CreateGameSession(CreateGameSessionRequest) returns (CreateGameSessionResponse);
  rpc GetGameSession(GetGameSessionRequest) returns (GetGameSessionResponse);
  rpc StartGame(StartGameRequest) returns (StartGameResponse);
  rpc ExecuteGame(ExecuteGameRequest) returns (ExecuteGameResponse);
  rpc EndGame(EndGameRequest) returns (EndGameResponse);
  rpc CancelGame(CancelGameRequest) returns (CancelGameResponse);
  
  // Game state and results
  rpc GetGameState(GetGameStateRequest) returns (GetGameStateResponse);
  rpc GetGameResults(GetGameResultsRequest) returns (GetGameResultsResponse);
  rpc CalculatePayouts(CalculatePayoutsRequest) returns (CalculatePayoutsResponse);
  
  // Fairness and verification
  rpc GenerateFairnessProof(GenerateFairnessProofRequest) returns (GenerateFairnessProofResponse);
  rpc VerifyFairnessProof(VerifyFairnessProofRequest) returns (VerifyFairnessProofResponse);
  
  // Configuration and validation
  rpc ValidateGameConfiguration(ValidateGameConfigurationRequest) returns (ValidateGameConfigurationResponse);
  rpc GetSupportedGameTypes(GetSupportedGameTypesRequest) returns (GetSupportedGameTypesResponse);
  
  // Statistics and monitoring
  rpc GetActiveGameCount(GetActiveGameCountRequest) returns (GetActiveGameCountResponse);
  rpc GetGameStatistics(GetGameStatisticsRequest) returns (GetGameStatisticsResponse);
}

// Enums
enum GameType {
  GAME_TYPE_UNSPECIFIED = 0;
  GAME_TYPE_PRIZE_WHEEL = 1;
  GAME_TYPE_AMIDAKUJI = 2;
}

enum SessionStatus {
  SESSION_STATUS_UNSPECIFIED = 0;
  SESSION_STATUS_PENDING = 1;
  SESSION_STATUS_WAITING = 2;
  SESSION_STATUS_ACTIVE = 3;
  SESSION_STATUS_COMPLETED = 4;
  SESSION_STATUS_CANCELLED = 5;
}

enum PlayerStatus {
  PLAYER_STATUS_UNSPECIFIED = 0;
  PLAYER_STATUS_WAITING = 1;
  PLAYER_STATUS_READY = 2;
  PLAYER_STATUS_PLAYING = 3;
  PLAYER_STATUS_FINISHED = 4;
  PLAYER_STATUS_DISCONNECTED = 5;
}

enum GamePhase {
  GAME_PHASE_UNSPECIFIED = 0;
  GAME_PHASE_PENDING = 1;
  GAME_PHASE_WAITING = 2;
  GAME_PHASE_ACTIVE = 3;
  GAME_PHASE_COMPLETED = 4;
  GAME_PHASE_CANCELLED = 5;
}

// Core data structures
message GameSession {
  string id = 1;
  string room_id = 2;
  GameType game_type = 3;
  SessionStatus status = 4;
  repeated SessionPlayer players = 5;
  google.protobuf.Timestamp start_time = 6;
  google.protobuf.Timestamp end_time = 7;
  GameResults results = 8;
  SessionConfiguration configuration = 9;
  FairnessProof fairness_proof = 10;
  int64 version = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

message SessionPlayer {
  string user_id = 1;
  string username = 2;
  int32 position = 3;
  int64 bet_amount = 4;
  int64 win_amount = 5;
  google.protobuf.Timestamp joined_at = 6;
  PlayerStatus status = 7;
}

message SessionConfiguration {
  GameType game_type = 1;
  double house_edge = 2;
  int64 max_duration_seconds = 3;
  map<string, string> game_specific = 4;
}

message GameResults {
  string winner_user_id = 1;
  int32 winner_position = 2;
  int64 total_bet_pool = 3;
  int64 house_take = 4;
  int64 prize_pool = 5;
  repeated PlayerPayout payouts = 6;
  map<string, string> game_data = 7;
  google.protobuf.Timestamp completed_at = 8;
}

message PlayerPayout {
  string user_id = 1;
  int64 amount = 2;
  string reason = 3;
  bool processed = 4;
}

message FairnessProof {
  string server_seed = 1;
  string client_seed = 2;
  int64 nonce = 3;
  string hash = 4;
  string result = 5;
  bool verifiable = 6;
  google.protobuf.Timestamp generated_at = 7;
}

message GameState {
  string session_id = 1;
  GamePhase phase = 2;
  repeated SessionPlayer players = 3;
  google.protobuf.Timestamp start_time = 4;
  map<string, string> game_data = 5;
  GameResults results = 6;
  google.protobuf.Timestamp last_updated = 7;
}

message PayoutCalculation {
  GameType game_type = 1;
  int64 total_bet_pool = 2;
  double house_edge = 3;
  int64 house_take = 4;
  int64 prize_pool = 5;
  repeated PlayerPayout payouts = 6;
  google.protobuf.Timestamp calculated_at = 7;
}

// Request/Response messages

// CreateGameSession
message CreateGameSessionRequest {
  string room_id = 1;
  GameType game_type = 2;
  repeated SessionPlayer players = 3;
  SessionConfiguration configuration = 4;
}

message CreateGameSessionResponse {
  GameSession session = 1;
  string error = 2;
}

// GetGameSession
message GetGameSessionRequest {
  string session_id = 1;
}

message GetGameSessionResponse {
  GameSession session = 1;
  string error = 2;
}

// StartGame
message StartGameRequest {
  string session_id = 1;
}

message StartGameResponse {
  GameSession session = 1;
  string error = 2;
}

// ExecuteGame
message ExecuteGameRequest {
  string session_id = 1;
  GameType game_type = 2;
  map<string, string> game_data = 3;
}

message ExecuteGameResponse {
  GameResults results = 1;
  string error = 2;
}

// EndGame
message EndGameRequest {
  string session_id = 1;
  GameResults results = 2;
}

message EndGameResponse {
  GameSession session = 1;
  string error = 2;
}

// CancelGame
message CancelGameRequest {
  string session_id = 1;
  string reason = 2;
}

message CancelGameResponse {
  GameSession session = 1;
  string error = 2;
}

// GetGameState
message GetGameStateRequest {
  string session_id = 1;
}

message GetGameStateResponse {
  GameState state = 1;
  string error = 2;
}

// GetGameResults
message GetGameResultsRequest {
  string session_id = 1;
}

message GetGameResultsResponse {
  GameResults results = 1;
  string error = 2;
}

// CalculatePayouts
message CalculatePayoutsRequest {
  string session_id = 1;
  GameResults results = 2;
}

message CalculatePayoutsResponse {
  PayoutCalculation calculation = 1;
  string error = 2;
}

// GenerateFairnessProof
message GenerateFairnessProofRequest {
  string session_id = 1;
}

message GenerateFairnessProofResponse {
  FairnessProof proof = 1;
  string error = 2;
}

// VerifyFairnessProof
message VerifyFairnessProofRequest {
  string session_id = 1;
  FairnessProof proof = 2;
}

message VerifyFairnessProofResponse {
  bool valid = 1;
  string error = 2;
}

// ValidateGameConfiguration
message ValidateGameConfigurationRequest {
  GameType game_type = 1;
  map<string, string> configuration = 2;
}

message ValidateGameConfigurationResponse {
  bool valid = 1;
  string error = 2;
}

// GetSupportedGameTypes
message GetSupportedGameTypesRequest {
}

message GetSupportedGameTypesResponse {
  repeated GameType game_types = 1;
  string error = 2;
}

// GetActiveGameCount
message GetActiveGameCountRequest {
}

message GetActiveGameCountResponse {
  int64 count = 1;
  string error = 2;
}

// GetGameStatistics
message GetGameStatisticsRequest {
  GameType game_type = 1;
}

message GetGameStatisticsResponse {
  map<string, string> statistics = 1;
  string error = 2;
}
