// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/game_engine.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GameEngineService_CreateGameSession_FullMethodName         = "/game_engine.GameEngineService/CreateGameSession"
	GameEngineService_GetGameSession_FullMethodName            = "/game_engine.GameEngineService/GetGameSession"
	GameEngineService_StartGame_FullMethodName                 = "/game_engine.GameEngineService/StartGame"
	GameEngineService_ExecuteGame_FullMethodName               = "/game_engine.GameEngineService/ExecuteGame"
	GameEngineService_EndGame_FullMethodName                   = "/game_engine.GameEngineService/EndGame"
	GameEngineService_CancelGame_FullMethodName                = "/game_engine.GameEngineService/CancelGame"
	GameEngineService_GetGameState_FullMethodName              = "/game_engine.GameEngineService/GetGameState"
	GameEngineService_GetGameResults_FullMethodName            = "/game_engine.GameEngineService/GetGameResults"
	GameEngineService_CalculatePayouts_FullMethodName          = "/game_engine.GameEngineService/CalculatePayouts"
	GameEngineService_GenerateFairnessProof_FullMethodName     = "/game_engine.GameEngineService/GenerateFairnessProof"
	GameEngineService_VerifyFairnessProof_FullMethodName       = "/game_engine.GameEngineService/VerifyFairnessProof"
	GameEngineService_ValidateGameConfiguration_FullMethodName = "/game_engine.GameEngineService/ValidateGameConfiguration"
	GameEngineService_GetSupportedGameTypes_FullMethodName     = "/game_engine.GameEngineService/GetSupportedGameTypes"
	GameEngineService_GetActiveGameCount_FullMethodName        = "/game_engine.GameEngineService/GetActiveGameCount"
	GameEngineService_GetGameStatistics_FullMethodName         = "/game_engine.GameEngineService/GetGameStatistics"
)

// GameEngineServiceClient is the client API for GameEngineService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GameEngineService provides game logic execution and management
type GameEngineServiceClient interface {
	// Session management
	CreateGameSession(ctx context.Context, in *CreateGameSessionRequest, opts ...grpc.CallOption) (*CreateGameSessionResponse, error)
	GetGameSession(ctx context.Context, in *GetGameSessionRequest, opts ...grpc.CallOption) (*GetGameSessionResponse, error)
	StartGame(ctx context.Context, in *StartGameRequest, opts ...grpc.CallOption) (*StartGameResponse, error)
	ExecuteGame(ctx context.Context, in *ExecuteGameRequest, opts ...grpc.CallOption) (*ExecuteGameResponse, error)
	EndGame(ctx context.Context, in *EndGameRequest, opts ...grpc.CallOption) (*EndGameResponse, error)
	CancelGame(ctx context.Context, in *CancelGameRequest, opts ...grpc.CallOption) (*CancelGameResponse, error)
	// Game state and results
	GetGameState(ctx context.Context, in *GetGameStateRequest, opts ...grpc.CallOption) (*GetGameStateResponse, error)
	GetGameResults(ctx context.Context, in *GetGameResultsRequest, opts ...grpc.CallOption) (*GetGameResultsResponse, error)
	CalculatePayouts(ctx context.Context, in *CalculatePayoutsRequest, opts ...grpc.CallOption) (*CalculatePayoutsResponse, error)
	// Fairness and verification
	GenerateFairnessProof(ctx context.Context, in *GenerateFairnessProofRequest, opts ...grpc.CallOption) (*GenerateFairnessProofResponse, error)
	VerifyFairnessProof(ctx context.Context, in *VerifyFairnessProofRequest, opts ...grpc.CallOption) (*VerifyFairnessProofResponse, error)
	// Configuration and validation
	ValidateGameConfiguration(ctx context.Context, in *ValidateGameConfigurationRequest, opts ...grpc.CallOption) (*ValidateGameConfigurationResponse, error)
	GetSupportedGameTypes(ctx context.Context, in *GetSupportedGameTypesRequest, opts ...grpc.CallOption) (*GetSupportedGameTypesResponse, error)
	// Statistics and monitoring
	GetActiveGameCount(ctx context.Context, in *GetActiveGameCountRequest, opts ...grpc.CallOption) (*GetActiveGameCountResponse, error)
	GetGameStatistics(ctx context.Context, in *GetGameStatisticsRequest, opts ...grpc.CallOption) (*GetGameStatisticsResponse, error)
}

type gameEngineServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGameEngineServiceClient(cc grpc.ClientConnInterface) GameEngineServiceClient {
	return &gameEngineServiceClient{cc}
}

func (c *gameEngineServiceClient) CreateGameSession(ctx context.Context, in *CreateGameSessionRequest, opts ...grpc.CallOption) (*CreateGameSessionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGameSessionResponse)
	err := c.cc.Invoke(ctx, GameEngineService_CreateGameSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetGameSession(ctx context.Context, in *GetGameSessionRequest, opts ...grpc.CallOption) (*GetGameSessionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameSessionResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetGameSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) StartGame(ctx context.Context, in *StartGameRequest, opts ...grpc.CallOption) (*StartGameResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartGameResponse)
	err := c.cc.Invoke(ctx, GameEngineService_StartGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) ExecuteGame(ctx context.Context, in *ExecuteGameRequest, opts ...grpc.CallOption) (*ExecuteGameResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExecuteGameResponse)
	err := c.cc.Invoke(ctx, GameEngineService_ExecuteGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) EndGame(ctx context.Context, in *EndGameRequest, opts ...grpc.CallOption) (*EndGameResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EndGameResponse)
	err := c.cc.Invoke(ctx, GameEngineService_EndGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) CancelGame(ctx context.Context, in *CancelGameRequest, opts ...grpc.CallOption) (*CancelGameResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelGameResponse)
	err := c.cc.Invoke(ctx, GameEngineService_CancelGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetGameState(ctx context.Context, in *GetGameStateRequest, opts ...grpc.CallOption) (*GetGameStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameStateResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetGameState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetGameResults(ctx context.Context, in *GetGameResultsRequest, opts ...grpc.CallOption) (*GetGameResultsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameResultsResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetGameResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) CalculatePayouts(ctx context.Context, in *CalculatePayoutsRequest, opts ...grpc.CallOption) (*CalculatePayoutsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculatePayoutsResponse)
	err := c.cc.Invoke(ctx, GameEngineService_CalculatePayouts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GenerateFairnessProof(ctx context.Context, in *GenerateFairnessProofRequest, opts ...grpc.CallOption) (*GenerateFairnessProofResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateFairnessProofResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GenerateFairnessProof_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) VerifyFairnessProof(ctx context.Context, in *VerifyFairnessProofRequest, opts ...grpc.CallOption) (*VerifyFairnessProofResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyFairnessProofResponse)
	err := c.cc.Invoke(ctx, GameEngineService_VerifyFairnessProof_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) ValidateGameConfiguration(ctx context.Context, in *ValidateGameConfigurationRequest, opts ...grpc.CallOption) (*ValidateGameConfigurationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateGameConfigurationResponse)
	err := c.cc.Invoke(ctx, GameEngineService_ValidateGameConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetSupportedGameTypes(ctx context.Context, in *GetSupportedGameTypesRequest, opts ...grpc.CallOption) (*GetSupportedGameTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSupportedGameTypesResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetSupportedGameTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetActiveGameCount(ctx context.Context, in *GetActiveGameCountRequest, opts ...grpc.CallOption) (*GetActiveGameCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveGameCountResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetActiveGameCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameEngineServiceClient) GetGameStatistics(ctx context.Context, in *GetGameStatisticsRequest, opts ...grpc.CallOption) (*GetGameStatisticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameStatisticsResponse)
	err := c.cc.Invoke(ctx, GameEngineService_GetGameStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameEngineServiceServer is the server API for GameEngineService service.
// All implementations must embed UnimplementedGameEngineServiceServer
// for forward compatibility.
//
// GameEngineService provides game logic execution and management
type GameEngineServiceServer interface {
	// Session management
	CreateGameSession(context.Context, *CreateGameSessionRequest) (*CreateGameSessionResponse, error)
	GetGameSession(context.Context, *GetGameSessionRequest) (*GetGameSessionResponse, error)
	StartGame(context.Context, *StartGameRequest) (*StartGameResponse, error)
	ExecuteGame(context.Context, *ExecuteGameRequest) (*ExecuteGameResponse, error)
	EndGame(context.Context, *EndGameRequest) (*EndGameResponse, error)
	CancelGame(context.Context, *CancelGameRequest) (*CancelGameResponse, error)
	// Game state and results
	GetGameState(context.Context, *GetGameStateRequest) (*GetGameStateResponse, error)
	GetGameResults(context.Context, *GetGameResultsRequest) (*GetGameResultsResponse, error)
	CalculatePayouts(context.Context, *CalculatePayoutsRequest) (*CalculatePayoutsResponse, error)
	// Fairness and verification
	GenerateFairnessProof(context.Context, *GenerateFairnessProofRequest) (*GenerateFairnessProofResponse, error)
	VerifyFairnessProof(context.Context, *VerifyFairnessProofRequest) (*VerifyFairnessProofResponse, error)
	// Configuration and validation
	ValidateGameConfiguration(context.Context, *ValidateGameConfigurationRequest) (*ValidateGameConfigurationResponse, error)
	GetSupportedGameTypes(context.Context, *GetSupportedGameTypesRequest) (*GetSupportedGameTypesResponse, error)
	// Statistics and monitoring
	GetActiveGameCount(context.Context, *GetActiveGameCountRequest) (*GetActiveGameCountResponse, error)
	GetGameStatistics(context.Context, *GetGameStatisticsRequest) (*GetGameStatisticsResponse, error)
	mustEmbedUnimplementedGameEngineServiceServer()
}

// UnimplementedGameEngineServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGameEngineServiceServer struct{}

func (UnimplementedGameEngineServiceServer) CreateGameSession(context.Context, *CreateGameSessionRequest) (*CreateGameSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGameSession not implemented")
}
func (UnimplementedGameEngineServiceServer) GetGameSession(context.Context, *GetGameSessionRequest) (*GetGameSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameSession not implemented")
}
func (UnimplementedGameEngineServiceServer) StartGame(context.Context, *StartGameRequest) (*StartGameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartGame not implemented")
}
func (UnimplementedGameEngineServiceServer) ExecuteGame(context.Context, *ExecuteGameRequest) (*ExecuteGameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteGame not implemented")
}
func (UnimplementedGameEngineServiceServer) EndGame(context.Context, *EndGameRequest) (*EndGameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EndGame not implemented")
}
func (UnimplementedGameEngineServiceServer) CancelGame(context.Context, *CancelGameRequest) (*CancelGameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelGame not implemented")
}
func (UnimplementedGameEngineServiceServer) GetGameState(context.Context, *GetGameStateRequest) (*GetGameStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameState not implemented")
}
func (UnimplementedGameEngineServiceServer) GetGameResults(context.Context, *GetGameResultsRequest) (*GetGameResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameResults not implemented")
}
func (UnimplementedGameEngineServiceServer) CalculatePayouts(context.Context, *CalculatePayoutsRequest) (*CalculatePayoutsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePayouts not implemented")
}
func (UnimplementedGameEngineServiceServer) GenerateFairnessProof(context.Context, *GenerateFairnessProofRequest) (*GenerateFairnessProofResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateFairnessProof not implemented")
}
func (UnimplementedGameEngineServiceServer) VerifyFairnessProof(context.Context, *VerifyFairnessProofRequest) (*VerifyFairnessProofResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyFairnessProof not implemented")
}
func (UnimplementedGameEngineServiceServer) ValidateGameConfiguration(context.Context, *ValidateGameConfigurationRequest) (*ValidateGameConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateGameConfiguration not implemented")
}
func (UnimplementedGameEngineServiceServer) GetSupportedGameTypes(context.Context, *GetSupportedGameTypesRequest) (*GetSupportedGameTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportedGameTypes not implemented")
}
func (UnimplementedGameEngineServiceServer) GetActiveGameCount(context.Context, *GetActiveGameCountRequest) (*GetActiveGameCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveGameCount not implemented")
}
func (UnimplementedGameEngineServiceServer) GetGameStatistics(context.Context, *GetGameStatisticsRequest) (*GetGameStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameStatistics not implemented")
}
func (UnimplementedGameEngineServiceServer) mustEmbedUnimplementedGameEngineServiceServer() {}
func (UnimplementedGameEngineServiceServer) testEmbeddedByValue()                           {}

// UnsafeGameEngineServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameEngineServiceServer will
// result in compilation errors.
type UnsafeGameEngineServiceServer interface {
	mustEmbedUnimplementedGameEngineServiceServer()
}

func RegisterGameEngineServiceServer(s grpc.ServiceRegistrar, srv GameEngineServiceServer) {
	// If the following call pancis, it indicates UnimplementedGameEngineServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GameEngineService_ServiceDesc, srv)
}

func _GameEngineService_CreateGameSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).CreateGameSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_CreateGameSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).CreateGameSession(ctx, req.(*CreateGameSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetGameSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetGameSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetGameSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetGameSession(ctx, req.(*GetGameSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_StartGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).StartGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_StartGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).StartGame(ctx, req.(*StartGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_ExecuteGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).ExecuteGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_ExecuteGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).ExecuteGame(ctx, req.(*ExecuteGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_EndGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).EndGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_EndGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).EndGame(ctx, req.(*EndGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_CancelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).CancelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_CancelGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).CancelGame(ctx, req.(*CancelGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetGameState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetGameState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetGameState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetGameState(ctx, req.(*GetGameStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetGameResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetGameResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetGameResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetGameResults(ctx, req.(*GetGameResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_CalculatePayouts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePayoutsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).CalculatePayouts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_CalculatePayouts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).CalculatePayouts(ctx, req.(*CalculatePayoutsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GenerateFairnessProof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateFairnessProofRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GenerateFairnessProof(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GenerateFairnessProof_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GenerateFairnessProof(ctx, req.(*GenerateFairnessProofRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_VerifyFairnessProof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyFairnessProofRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).VerifyFairnessProof(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_VerifyFairnessProof_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).VerifyFairnessProof(ctx, req.(*VerifyFairnessProofRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_ValidateGameConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateGameConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).ValidateGameConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_ValidateGameConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).ValidateGameConfiguration(ctx, req.(*ValidateGameConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetSupportedGameTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportedGameTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetSupportedGameTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetSupportedGameTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetSupportedGameTypes(ctx, req.(*GetSupportedGameTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetActiveGameCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveGameCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetActiveGameCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetActiveGameCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetActiveGameCount(ctx, req.(*GetActiveGameCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameEngineService_GetGameStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameEngineServiceServer).GetGameStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameEngineService_GetGameStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameEngineServiceServer).GetGameStatistics(ctx, req.(*GetGameStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GameEngineService_ServiceDesc is the grpc.ServiceDesc for GameEngineService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameEngineService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_engine.GameEngineService",
	HandlerType: (*GameEngineServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGameSession",
			Handler:    _GameEngineService_CreateGameSession_Handler,
		},
		{
			MethodName: "GetGameSession",
			Handler:    _GameEngineService_GetGameSession_Handler,
		},
		{
			MethodName: "StartGame",
			Handler:    _GameEngineService_StartGame_Handler,
		},
		{
			MethodName: "ExecuteGame",
			Handler:    _GameEngineService_ExecuteGame_Handler,
		},
		{
			MethodName: "EndGame",
			Handler:    _GameEngineService_EndGame_Handler,
		},
		{
			MethodName: "CancelGame",
			Handler:    _GameEngineService_CancelGame_Handler,
		},
		{
			MethodName: "GetGameState",
			Handler:    _GameEngineService_GetGameState_Handler,
		},
		{
			MethodName: "GetGameResults",
			Handler:    _GameEngineService_GetGameResults_Handler,
		},
		{
			MethodName: "CalculatePayouts",
			Handler:    _GameEngineService_CalculatePayouts_Handler,
		},
		{
			MethodName: "GenerateFairnessProof",
			Handler:    _GameEngineService_GenerateFairnessProof_Handler,
		},
		{
			MethodName: "VerifyFairnessProof",
			Handler:    _GameEngineService_VerifyFairnessProof_Handler,
		},
		{
			MethodName: "ValidateGameConfiguration",
			Handler:    _GameEngineService_ValidateGameConfiguration_Handler,
		},
		{
			MethodName: "GetSupportedGameTypes",
			Handler:    _GameEngineService_GetSupportedGameTypes_Handler,
		},
		{
			MethodName: "GetActiveGameCount",
			Handler:    _GameEngineService_GetActiveGameCount_Handler,
		},
		{
			MethodName: "GetGameStatistics",
			Handler:    _GameEngineService_GetGameStatistics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/game_engine.proto",
}
