// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/game_engine.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type GameType int32

const (
	GameType_GAME_TYPE_UNSPECIFIED GameType = 0
	GameType_GAME_TYPE_PRIZE_WHEEL GameType = 1
	GameType_GAME_TYPE_AMIDAKUJI   GameType = 2
)

// Enum value maps for GameType.
var (
	GameType_name = map[int32]string{
		0: "GAME_TYPE_UNSPECIFIED",
		1: "GAME_TYPE_PRIZE_WHEEL",
		2: "GAME_TYPE_AMIDAKUJI",
	}
	GameType_value = map[string]int32{
		"GAME_TYPE_UNSPECIFIED": 0,
		"GAME_TYPE_PRIZE_WHEEL": 1,
		"GAME_TYPE_AMIDAKUJI":   2,
	}
)

func (x GameType) Enum() *GameType {
	p := new(GameType)
	*p = x
	return p
}

func (x GameType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_engine_proto_enumTypes[0].Descriptor()
}

func (GameType) Type() protoreflect.EnumType {
	return &file_proto_game_engine_proto_enumTypes[0]
}

func (x GameType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameType.Descriptor instead.
func (GameType) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{0}
}

type SessionStatus int32

const (
	SessionStatus_SESSION_STATUS_UNSPECIFIED SessionStatus = 0
	SessionStatus_SESSION_STATUS_PENDING     SessionStatus = 1
	SessionStatus_SESSION_STATUS_WAITING     SessionStatus = 2
	SessionStatus_SESSION_STATUS_ACTIVE      SessionStatus = 3
	SessionStatus_SESSION_STATUS_COMPLETED   SessionStatus = 4
	SessionStatus_SESSION_STATUS_CANCELLED   SessionStatus = 5
)

// Enum value maps for SessionStatus.
var (
	SessionStatus_name = map[int32]string{
		0: "SESSION_STATUS_UNSPECIFIED",
		1: "SESSION_STATUS_PENDING",
		2: "SESSION_STATUS_WAITING",
		3: "SESSION_STATUS_ACTIVE",
		4: "SESSION_STATUS_COMPLETED",
		5: "SESSION_STATUS_CANCELLED",
	}
	SessionStatus_value = map[string]int32{
		"SESSION_STATUS_UNSPECIFIED": 0,
		"SESSION_STATUS_PENDING":     1,
		"SESSION_STATUS_WAITING":     2,
		"SESSION_STATUS_ACTIVE":      3,
		"SESSION_STATUS_COMPLETED":   4,
		"SESSION_STATUS_CANCELLED":   5,
	}
)

func (x SessionStatus) Enum() *SessionStatus {
	p := new(SessionStatus)
	*p = x
	return p
}

func (x SessionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_engine_proto_enumTypes[1].Descriptor()
}

func (SessionStatus) Type() protoreflect.EnumType {
	return &file_proto_game_engine_proto_enumTypes[1]
}

func (x SessionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionStatus.Descriptor instead.
func (SessionStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{1}
}

type PlayerStatus int32

const (
	PlayerStatus_PLAYER_STATUS_UNSPECIFIED  PlayerStatus = 0
	PlayerStatus_PLAYER_STATUS_WAITING      PlayerStatus = 1
	PlayerStatus_PLAYER_STATUS_READY        PlayerStatus = 2
	PlayerStatus_PLAYER_STATUS_PLAYING      PlayerStatus = 3
	PlayerStatus_PLAYER_STATUS_FINISHED     PlayerStatus = 4
	PlayerStatus_PLAYER_STATUS_DISCONNECTED PlayerStatus = 5
)

// Enum value maps for PlayerStatus.
var (
	PlayerStatus_name = map[int32]string{
		0: "PLAYER_STATUS_UNSPECIFIED",
		1: "PLAYER_STATUS_WAITING",
		2: "PLAYER_STATUS_READY",
		3: "PLAYER_STATUS_PLAYING",
		4: "PLAYER_STATUS_FINISHED",
		5: "PLAYER_STATUS_DISCONNECTED",
	}
	PlayerStatus_value = map[string]int32{
		"PLAYER_STATUS_UNSPECIFIED":  0,
		"PLAYER_STATUS_WAITING":      1,
		"PLAYER_STATUS_READY":        2,
		"PLAYER_STATUS_PLAYING":      3,
		"PLAYER_STATUS_FINISHED":     4,
		"PLAYER_STATUS_DISCONNECTED": 5,
	}
)

func (x PlayerStatus) Enum() *PlayerStatus {
	p := new(PlayerStatus)
	*p = x
	return p
}

func (x PlayerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_engine_proto_enumTypes[2].Descriptor()
}

func (PlayerStatus) Type() protoreflect.EnumType {
	return &file_proto_game_engine_proto_enumTypes[2]
}

func (x PlayerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerStatus.Descriptor instead.
func (PlayerStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{2}
}

type GamePhase int32

const (
	GamePhase_GAME_PHASE_UNSPECIFIED GamePhase = 0
	GamePhase_GAME_PHASE_PENDING     GamePhase = 1
	GamePhase_GAME_PHASE_WAITING     GamePhase = 2
	GamePhase_GAME_PHASE_ACTIVE      GamePhase = 3
	GamePhase_GAME_PHASE_COMPLETED   GamePhase = 4
	GamePhase_GAME_PHASE_CANCELLED   GamePhase = 5
)

// Enum value maps for GamePhase.
var (
	GamePhase_name = map[int32]string{
		0: "GAME_PHASE_UNSPECIFIED",
		1: "GAME_PHASE_PENDING",
		2: "GAME_PHASE_WAITING",
		3: "GAME_PHASE_ACTIVE",
		4: "GAME_PHASE_COMPLETED",
		5: "GAME_PHASE_CANCELLED",
	}
	GamePhase_value = map[string]int32{
		"GAME_PHASE_UNSPECIFIED": 0,
		"GAME_PHASE_PENDING":     1,
		"GAME_PHASE_WAITING":     2,
		"GAME_PHASE_ACTIVE":      3,
		"GAME_PHASE_COMPLETED":   4,
		"GAME_PHASE_CANCELLED":   5,
	}
)

func (x GamePhase) Enum() *GamePhase {
	p := new(GamePhase)
	*p = x
	return p
}

func (x GamePhase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GamePhase) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_engine_proto_enumTypes[3].Descriptor()
}

func (GamePhase) Type() protoreflect.EnumType {
	return &file_proto_game_engine_proto_enumTypes[3]
}

func (x GamePhase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GamePhase.Descriptor instead.
func (GamePhase) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{3}
}

// Core data structures
type GameSession struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RoomId        string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	GameType      GameType               `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	Status        SessionStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=game_engine.SessionStatus" json:"status,omitempty"`
	Players       []*SessionPlayer       `protobuf:"bytes,5,rep,name=players,proto3" json:"players,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Results       *GameResults           `protobuf:"bytes,8,opt,name=results,proto3" json:"results,omitempty"`
	Configuration *SessionConfiguration  `protobuf:"bytes,9,opt,name=configuration,proto3" json:"configuration,omitempty"`
	FairnessProof *FairnessProof         `protobuf:"bytes,10,opt,name=fairness_proof,json=fairnessProof,proto3" json:"fairness_proof,omitempty"`
	Version       int64                  `protobuf:"varint,11,opt,name=version,proto3" json:"version,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameSession) Reset() {
	*x = GameSession{}
	mi := &file_proto_game_engine_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameSession) ProtoMessage() {}

func (x *GameSession) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameSession.ProtoReflect.Descriptor instead.
func (*GameSession) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{0}
}

func (x *GameSession) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GameSession) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GameSession) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *GameSession) GetStatus() SessionStatus {
	if x != nil {
		return x.Status
	}
	return SessionStatus_SESSION_STATUS_UNSPECIFIED
}

func (x *GameSession) GetPlayers() []*SessionPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *GameSession) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GameSession) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GameSession) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GameSession) GetConfiguration() *SessionConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *GameSession) GetFairnessProof() *FairnessProof {
	if x != nil {
		return x.FairnessProof
	}
	return nil
}

func (x *GameSession) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *GameSession) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GameSession) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SessionPlayer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Position      int32                  `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
	BetAmount     int64                  `protobuf:"varint,4,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	WinAmount     int64                  `protobuf:"varint,5,opt,name=win_amount,json=winAmount,proto3" json:"win_amount,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	Status        PlayerStatus           `protobuf:"varint,7,opt,name=status,proto3,enum=game_engine.PlayerStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionPlayer) Reset() {
	*x = SessionPlayer{}
	mi := &file_proto_game_engine_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionPlayer) ProtoMessage() {}

func (x *SessionPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionPlayer.ProtoReflect.Descriptor instead.
func (*SessionPlayer) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{1}
}

func (x *SessionPlayer) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SessionPlayer) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SessionPlayer) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *SessionPlayer) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *SessionPlayer) GetWinAmount() int64 {
	if x != nil {
		return x.WinAmount
	}
	return 0
}

func (x *SessionPlayer) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *SessionPlayer) GetStatus() PlayerStatus {
	if x != nil {
		return x.Status
	}
	return PlayerStatus_PLAYER_STATUS_UNSPECIFIED
}

type SessionConfiguration struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	GameType           GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	HouseEdge          float64                `protobuf:"fixed64,2,opt,name=house_edge,json=houseEdge,proto3" json:"house_edge,omitempty"`
	MaxDurationSeconds int64                  `protobuf:"varint,3,opt,name=max_duration_seconds,json=maxDurationSeconds,proto3" json:"max_duration_seconds,omitempty"`
	GameSpecific       map[string]string      `protobuf:"bytes,4,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SessionConfiguration) Reset() {
	*x = SessionConfiguration{}
	mi := &file_proto_game_engine_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionConfiguration) ProtoMessage() {}

func (x *SessionConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionConfiguration.ProtoReflect.Descriptor instead.
func (*SessionConfiguration) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{2}
}

func (x *SessionConfiguration) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *SessionConfiguration) GetHouseEdge() float64 {
	if x != nil {
		return x.HouseEdge
	}
	return 0
}

func (x *SessionConfiguration) GetMaxDurationSeconds() int64 {
	if x != nil {
		return x.MaxDurationSeconds
	}
	return 0
}

func (x *SessionConfiguration) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

type GameResults struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	WinnerUserId   string                 `protobuf:"bytes,1,opt,name=winner_user_id,json=winnerUserId,proto3" json:"winner_user_id,omitempty"`
	WinnerPosition int32                  `protobuf:"varint,2,opt,name=winner_position,json=winnerPosition,proto3" json:"winner_position,omitempty"`
	TotalBetPool   int64                  `protobuf:"varint,3,opt,name=total_bet_pool,json=totalBetPool,proto3" json:"total_bet_pool,omitempty"`
	HouseTake      int64                  `protobuf:"varint,4,opt,name=house_take,json=houseTake,proto3" json:"house_take,omitempty"`
	PrizePool      int64                  `protobuf:"varint,5,opt,name=prize_pool,json=prizePool,proto3" json:"prize_pool,omitempty"`
	Payouts        []*PlayerPayout        `protobuf:"bytes,6,rep,name=payouts,proto3" json:"payouts,omitempty"`
	GameData       map[string]string      `protobuf:"bytes,7,rep,name=game_data,json=gameData,proto3" json:"game_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CompletedAt    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GameResults) Reset() {
	*x = GameResults{}
	mi := &file_proto_game_engine_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameResults) ProtoMessage() {}

func (x *GameResults) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameResults.ProtoReflect.Descriptor instead.
func (*GameResults) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{3}
}

func (x *GameResults) GetWinnerUserId() string {
	if x != nil {
		return x.WinnerUserId
	}
	return ""
}

func (x *GameResults) GetWinnerPosition() int32 {
	if x != nil {
		return x.WinnerPosition
	}
	return 0
}

func (x *GameResults) GetTotalBetPool() int64 {
	if x != nil {
		return x.TotalBetPool
	}
	return 0
}

func (x *GameResults) GetHouseTake() int64 {
	if x != nil {
		return x.HouseTake
	}
	return 0
}

func (x *GameResults) GetPrizePool() int64 {
	if x != nil {
		return x.PrizePool
	}
	return 0
}

func (x *GameResults) GetPayouts() []*PlayerPayout {
	if x != nil {
		return x.Payouts
	}
	return nil
}

func (x *GameResults) GetGameData() map[string]string {
	if x != nil {
		return x.GameData
	}
	return nil
}

func (x *GameResults) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

type PlayerPayout struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Processed     bool                   `protobuf:"varint,4,opt,name=processed,proto3" json:"processed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerPayout) Reset() {
	*x = PlayerPayout{}
	mi := &file_proto_game_engine_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerPayout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerPayout) ProtoMessage() {}

func (x *PlayerPayout) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerPayout.ProtoReflect.Descriptor instead.
func (*PlayerPayout) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{4}
}

func (x *PlayerPayout) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PlayerPayout) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PlayerPayout) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *PlayerPayout) GetProcessed() bool {
	if x != nil {
		return x.Processed
	}
	return false
}

type FairnessProof struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServerSeed    string                 `protobuf:"bytes,1,opt,name=server_seed,json=serverSeed,proto3" json:"server_seed,omitempty"`
	ClientSeed    string                 `protobuf:"bytes,2,opt,name=client_seed,json=clientSeed,proto3" json:"client_seed,omitempty"`
	Nonce         int64                  `protobuf:"varint,3,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Hash          string                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	Result        string                 `protobuf:"bytes,5,opt,name=result,proto3" json:"result,omitempty"`
	Verifiable    bool                   `protobuf:"varint,6,opt,name=verifiable,proto3" json:"verifiable,omitempty"`
	GeneratedAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=generated_at,json=generatedAt,proto3" json:"generated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FairnessProof) Reset() {
	*x = FairnessProof{}
	mi := &file_proto_game_engine_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FairnessProof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FairnessProof) ProtoMessage() {}

func (x *FairnessProof) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FairnessProof.ProtoReflect.Descriptor instead.
func (*FairnessProof) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{5}
}

func (x *FairnessProof) GetServerSeed() string {
	if x != nil {
		return x.ServerSeed
	}
	return ""
}

func (x *FairnessProof) GetClientSeed() string {
	if x != nil {
		return x.ClientSeed
	}
	return ""
}

func (x *FairnessProof) GetNonce() int64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *FairnessProof) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *FairnessProof) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *FairnessProof) GetVerifiable() bool {
	if x != nil {
		return x.Verifiable
	}
	return false
}

func (x *FairnessProof) GetGeneratedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.GeneratedAt
	}
	return nil
}

type GameState struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Phase         GamePhase              `protobuf:"varint,2,opt,name=phase,proto3,enum=game_engine.GamePhase" json:"phase,omitempty"`
	Players       []*SessionPlayer       `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	GameData      map[string]string      `protobuf:"bytes,5,rep,name=game_data,json=gameData,proto3" json:"game_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Results       *GameResults           `protobuf:"bytes,6,opt,name=results,proto3" json:"results,omitempty"`
	LastUpdated   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameState) Reset() {
	*x = GameState{}
	mi := &file_proto_game_engine_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameState) ProtoMessage() {}

func (x *GameState) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameState.ProtoReflect.Descriptor instead.
func (*GameState) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{6}
}

func (x *GameState) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *GameState) GetPhase() GamePhase {
	if x != nil {
		return x.Phase
	}
	return GamePhase_GAME_PHASE_UNSPECIFIED
}

func (x *GameState) GetPlayers() []*SessionPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *GameState) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GameState) GetGameData() map[string]string {
	if x != nil {
		return x.GameData
	}
	return nil
}

func (x *GameState) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GameState) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

type PayoutCalculation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	TotalBetPool  int64                  `protobuf:"varint,2,opt,name=total_bet_pool,json=totalBetPool,proto3" json:"total_bet_pool,omitempty"`
	HouseEdge     float64                `protobuf:"fixed64,3,opt,name=house_edge,json=houseEdge,proto3" json:"house_edge,omitempty"`
	HouseTake     int64                  `protobuf:"varint,4,opt,name=house_take,json=houseTake,proto3" json:"house_take,omitempty"`
	PrizePool     int64                  `protobuf:"varint,5,opt,name=prize_pool,json=prizePool,proto3" json:"prize_pool,omitempty"`
	Payouts       []*PlayerPayout        `protobuf:"bytes,6,rep,name=payouts,proto3" json:"payouts,omitempty"`
	CalculatedAt  *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=calculated_at,json=calculatedAt,proto3" json:"calculated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayoutCalculation) Reset() {
	*x = PayoutCalculation{}
	mi := &file_proto_game_engine_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayoutCalculation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayoutCalculation) ProtoMessage() {}

func (x *PayoutCalculation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayoutCalculation.ProtoReflect.Descriptor instead.
func (*PayoutCalculation) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{7}
}

func (x *PayoutCalculation) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *PayoutCalculation) GetTotalBetPool() int64 {
	if x != nil {
		return x.TotalBetPool
	}
	return 0
}

func (x *PayoutCalculation) GetHouseEdge() float64 {
	if x != nil {
		return x.HouseEdge
	}
	return 0
}

func (x *PayoutCalculation) GetHouseTake() int64 {
	if x != nil {
		return x.HouseTake
	}
	return 0
}

func (x *PayoutCalculation) GetPrizePool() int64 {
	if x != nil {
		return x.PrizePool
	}
	return 0
}

func (x *PayoutCalculation) GetPayouts() []*PlayerPayout {
	if x != nil {
		return x.Payouts
	}
	return nil
}

func (x *PayoutCalculation) GetCalculatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CalculatedAt
	}
	return nil
}

// CreateGameSession
type CreateGameSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	Players       []*SessionPlayer       `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`
	Configuration *SessionConfiguration  `protobuf:"bytes,4,opt,name=configuration,proto3" json:"configuration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGameSessionRequest) Reset() {
	*x = CreateGameSessionRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGameSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameSessionRequest) ProtoMessage() {}

func (x *CreateGameSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateGameSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{8}
}

func (x *CreateGameSessionRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CreateGameSessionRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *CreateGameSessionRequest) GetPlayers() []*SessionPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *CreateGameSessionRequest) GetConfiguration() *SessionConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

type CreateGameSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGameSessionResponse) Reset() {
	*x = CreateGameSessionResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGameSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameSessionResponse) ProtoMessage() {}

func (x *CreateGameSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateGameSessionResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{9}
}

func (x *CreateGameSessionResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *CreateGameSessionResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetGameSession
type GetGameSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameSessionRequest) Reset() {
	*x = GetGameSessionRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameSessionRequest) ProtoMessage() {}

func (x *GetGameSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameSessionRequest.ProtoReflect.Descriptor instead.
func (*GetGameSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{10}
}

func (x *GetGameSessionRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type GetGameSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameSessionResponse) Reset() {
	*x = GetGameSessionResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameSessionResponse) ProtoMessage() {}

func (x *GetGameSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameSessionResponse.ProtoReflect.Descriptor instead.
func (*GetGameSessionResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{11}
}

func (x *GetGameSessionResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *GetGameSessionResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// StartGame
type StartGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameRequest) Reset() {
	*x = StartGameRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameRequest) ProtoMessage() {}

func (x *StartGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameRequest.ProtoReflect.Descriptor instead.
func (*StartGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{12}
}

func (x *StartGameRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type StartGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameResponse) Reset() {
	*x = StartGameResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameResponse) ProtoMessage() {}

func (x *StartGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameResponse.ProtoReflect.Descriptor instead.
func (*StartGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{13}
}

func (x *StartGameResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *StartGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// ExecuteGame
type ExecuteGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	GameData      map[string]string      `protobuf:"bytes,3,rep,name=game_data,json=gameData,proto3" json:"game_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteGameRequest) Reset() {
	*x = ExecuteGameRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteGameRequest) ProtoMessage() {}

func (x *ExecuteGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteGameRequest.ProtoReflect.Descriptor instead.
func (*ExecuteGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{14}
}

func (x *ExecuteGameRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *ExecuteGameRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *ExecuteGameRequest) GetGameData() map[string]string {
	if x != nil {
		return x.GameData
	}
	return nil
}

type ExecuteGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Results       *GameResults           `protobuf:"bytes,1,opt,name=results,proto3" json:"results,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteGameResponse) Reset() {
	*x = ExecuteGameResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteGameResponse) ProtoMessage() {}

func (x *ExecuteGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteGameResponse.ProtoReflect.Descriptor instead.
func (*ExecuteGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{15}
}

func (x *ExecuteGameResponse) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *ExecuteGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// EndGame
type EndGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Results       *GameResults           `protobuf:"bytes,2,opt,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndGameRequest) Reset() {
	*x = EndGameRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndGameRequest) ProtoMessage() {}

func (x *EndGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndGameRequest.ProtoReflect.Descriptor instead.
func (*EndGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{16}
}

func (x *EndGameRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *EndGameRequest) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

type EndGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndGameResponse) Reset() {
	*x = EndGameResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndGameResponse) ProtoMessage() {}

func (x *EndGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndGameResponse.ProtoReflect.Descriptor instead.
func (*EndGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{17}
}

func (x *EndGameResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *EndGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// CancelGame
type CancelGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelGameRequest) Reset() {
	*x = CancelGameRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelGameRequest) ProtoMessage() {}

func (x *CancelGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelGameRequest.ProtoReflect.Descriptor instead.
func (*CancelGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{18}
}

func (x *CancelGameRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *CancelGameRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type CancelGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelGameResponse) Reset() {
	*x = CancelGameResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelGameResponse) ProtoMessage() {}

func (x *CancelGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelGameResponse.ProtoReflect.Descriptor instead.
func (*CancelGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{19}
}

func (x *CancelGameResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *CancelGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetGameState
type GetGameStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStateRequest) Reset() {
	*x = GetGameStateRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStateRequest) ProtoMessage() {}

func (x *GetGameStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStateRequest.ProtoReflect.Descriptor instead.
func (*GetGameStateRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{20}
}

func (x *GetGameStateRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type GetGameStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         *GameState             `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStateResponse) Reset() {
	*x = GetGameStateResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStateResponse) ProtoMessage() {}

func (x *GetGameStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStateResponse.ProtoReflect.Descriptor instead.
func (*GetGameStateResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{21}
}

func (x *GetGameStateResponse) GetState() *GameState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *GetGameStateResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetGameResults
type GetGameResultsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameResultsRequest) Reset() {
	*x = GetGameResultsRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameResultsRequest) ProtoMessage() {}

func (x *GetGameResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameResultsRequest.ProtoReflect.Descriptor instead.
func (*GetGameResultsRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{22}
}

func (x *GetGameResultsRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type GetGameResultsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Results       *GameResults           `protobuf:"bytes,1,opt,name=results,proto3" json:"results,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameResultsResponse) Reset() {
	*x = GetGameResultsResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameResultsResponse) ProtoMessage() {}

func (x *GetGameResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameResultsResponse.ProtoReflect.Descriptor instead.
func (*GetGameResultsResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{23}
}

func (x *GetGameResultsResponse) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GetGameResultsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// CalculatePayouts
type CalculatePayoutsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Results       *GameResults           `protobuf:"bytes,2,opt,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculatePayoutsRequest) Reset() {
	*x = CalculatePayoutsRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePayoutsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePayoutsRequest) ProtoMessage() {}

func (x *CalculatePayoutsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePayoutsRequest.ProtoReflect.Descriptor instead.
func (*CalculatePayoutsRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{24}
}

func (x *CalculatePayoutsRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *CalculatePayoutsRequest) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

type CalculatePayoutsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Calculation   *PayoutCalculation     `protobuf:"bytes,1,opt,name=calculation,proto3" json:"calculation,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculatePayoutsResponse) Reset() {
	*x = CalculatePayoutsResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePayoutsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePayoutsResponse) ProtoMessage() {}

func (x *CalculatePayoutsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePayoutsResponse.ProtoReflect.Descriptor instead.
func (*CalculatePayoutsResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{25}
}

func (x *CalculatePayoutsResponse) GetCalculation() *PayoutCalculation {
	if x != nil {
		return x.Calculation
	}
	return nil
}

func (x *CalculatePayoutsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GenerateFairnessProof
type GenerateFairnessProofRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateFairnessProofRequest) Reset() {
	*x = GenerateFairnessProofRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateFairnessProofRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateFairnessProofRequest) ProtoMessage() {}

func (x *GenerateFairnessProofRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateFairnessProofRequest.ProtoReflect.Descriptor instead.
func (*GenerateFairnessProofRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{26}
}

func (x *GenerateFairnessProofRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type GenerateFairnessProofResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proof         *FairnessProof         `protobuf:"bytes,1,opt,name=proof,proto3" json:"proof,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateFairnessProofResponse) Reset() {
	*x = GenerateFairnessProofResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateFairnessProofResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateFairnessProofResponse) ProtoMessage() {}

func (x *GenerateFairnessProofResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateFairnessProofResponse.ProtoReflect.Descriptor instead.
func (*GenerateFairnessProofResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{27}
}

func (x *GenerateFairnessProofResponse) GetProof() *FairnessProof {
	if x != nil {
		return x.Proof
	}
	return nil
}

func (x *GenerateFairnessProofResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// VerifyFairnessProof
type VerifyFairnessProofRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Proof         *FairnessProof         `protobuf:"bytes,2,opt,name=proof,proto3" json:"proof,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyFairnessProofRequest) Reset() {
	*x = VerifyFairnessProofRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyFairnessProofRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyFairnessProofRequest) ProtoMessage() {}

func (x *VerifyFairnessProofRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyFairnessProofRequest.ProtoReflect.Descriptor instead.
func (*VerifyFairnessProofRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{28}
}

func (x *VerifyFairnessProofRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *VerifyFairnessProofRequest) GetProof() *FairnessProof {
	if x != nil {
		return x.Proof
	}
	return nil
}

type VerifyFairnessProofResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Valid         bool                   `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyFairnessProofResponse) Reset() {
	*x = VerifyFairnessProofResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyFairnessProofResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyFairnessProofResponse) ProtoMessage() {}

func (x *VerifyFairnessProofResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyFairnessProofResponse.ProtoReflect.Descriptor instead.
func (*VerifyFairnessProofResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{29}
}

func (x *VerifyFairnessProofResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *VerifyFairnessProofResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// ValidateGameConfiguration
type ValidateGameConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	Configuration map[string]string      `protobuf:"bytes,2,rep,name=configuration,proto3" json:"configuration,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateGameConfigurationRequest) Reset() {
	*x = ValidateGameConfigurationRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateGameConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateGameConfigurationRequest) ProtoMessage() {}

func (x *ValidateGameConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateGameConfigurationRequest.ProtoReflect.Descriptor instead.
func (*ValidateGameConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{30}
}

func (x *ValidateGameConfigurationRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *ValidateGameConfigurationRequest) GetConfiguration() map[string]string {
	if x != nil {
		return x.Configuration
	}
	return nil
}

type ValidateGameConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Valid         bool                   `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateGameConfigurationResponse) Reset() {
	*x = ValidateGameConfigurationResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateGameConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateGameConfigurationResponse) ProtoMessage() {}

func (x *ValidateGameConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateGameConfigurationResponse.ProtoReflect.Descriptor instead.
func (*ValidateGameConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{31}
}

func (x *ValidateGameConfigurationResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidateGameConfigurationResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetSupportedGameTypes
type GetSupportedGameTypesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportedGameTypesRequest) Reset() {
	*x = GetSupportedGameTypesRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportedGameTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportedGameTypesRequest) ProtoMessage() {}

func (x *GetSupportedGameTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportedGameTypesRequest.ProtoReflect.Descriptor instead.
func (*GetSupportedGameTypesRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{32}
}

type GetSupportedGameTypesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameTypes     []GameType             `protobuf:"varint,1,rep,packed,name=game_types,json=gameTypes,proto3,enum=game_engine.GameType" json:"game_types,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSupportedGameTypesResponse) Reset() {
	*x = GetSupportedGameTypesResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSupportedGameTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportedGameTypesResponse) ProtoMessage() {}

func (x *GetSupportedGameTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportedGameTypesResponse.ProtoReflect.Descriptor instead.
func (*GetSupportedGameTypesResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{33}
}

func (x *GetSupportedGameTypesResponse) GetGameTypes() []GameType {
	if x != nil {
		return x.GameTypes
	}
	return nil
}

func (x *GetSupportedGameTypesResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetActiveGameCount
type GetActiveGameCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveGameCountRequest) Reset() {
	*x = GetActiveGameCountRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveGameCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveGameCountRequest) ProtoMessage() {}

func (x *GetActiveGameCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveGameCountRequest.ProtoReflect.Descriptor instead.
func (*GetActiveGameCountRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{34}
}

type GetActiveGameCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveGameCountResponse) Reset() {
	*x = GetActiveGameCountResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveGameCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveGameCountResponse) ProtoMessage() {}

func (x *GetActiveGameCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveGameCountResponse.ProtoReflect.Descriptor instead.
func (*GetActiveGameCountResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{35}
}

func (x *GetActiveGameCountResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetActiveGameCountResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetGameStatistics
type GetGameStatisticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=game_engine.GameType" json:"game_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStatisticsRequest) Reset() {
	*x = GetGameStatisticsRequest{}
	mi := &file_proto_game_engine_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStatisticsRequest) ProtoMessage() {}

func (x *GetGameStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStatisticsRequest.ProtoReflect.Descriptor instead.
func (*GetGameStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{36}
}

func (x *GetGameStatisticsRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

type GetGameStatisticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    map[string]string      `protobuf:"bytes,1,rep,name=statistics,proto3" json:"statistics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStatisticsResponse) Reset() {
	*x = GetGameStatisticsResponse{}
	mi := &file_proto_game_engine_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStatisticsResponse) ProtoMessage() {}

func (x *GetGameStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_engine_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStatisticsResponse.ProtoReflect.Descriptor instead.
func (*GetGameStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_engine_proto_rawDescGZIP(), []int{37}
}

func (x *GetGameStatisticsResponse) GetStatistics() map[string]string {
	if x != nil {
		return x.Statistics
	}
	return nil
}

func (x *GetGameStatisticsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

var File_proto_game_engine_proto protoreflect.FileDescriptor

const file_proto_game_engine_proto_rawDesc = "" +
	"\n" +
	"\x17proto/game_engine.proto\x12\vgame_engine\x1a\x1fgoogle/protobuf/timestamp.proto\"\x96\x05\n" +
	"\vGameSession\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\aroom_id\x18\x02 \x01(\tR\x06roomId\x122\n" +
	"\tgame_type\x18\x03 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x122\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1a.game_engine.SessionStatusR\x06status\x124\n" +
	"\aplayers\x18\x05 \x03(\v2\x1a.game_engine.SessionPlayerR\aplayers\x129\n" +
	"\n" +
	"start_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x122\n" +
	"\aresults\x18\b \x01(\v2\x18.game_engine.GameResultsR\aresults\x12G\n" +
	"\rconfiguration\x18\t \x01(\v2!.game_engine.SessionConfigurationR\rconfiguration\x12A\n" +
	"\x0efairness_proof\x18\n" +
	" \x01(\v2\x1a.game_engine.FairnessProofR\rfairnessProof\x12\x18\n" +
	"\aversion\x18\v \x01(\x03R\aversion\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x8a\x02\n" +
	"\rSessionPlayer\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bposition\x18\x03 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\x04 \x01(\x03R\tbetAmount\x12\x1d\n" +
	"\n" +
	"win_amount\x18\x05 \x01(\x03R\twinAmount\x127\n" +
	"\tjoined_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\x121\n" +
	"\x06status\x18\a \x01(\x0e2\x19.game_engine.PlayerStatusR\x06status\"\xb6\x02\n" +
	"\x14SessionConfiguration\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x12\x1d\n" +
	"\n" +
	"house_edge\x18\x02 \x01(\x01R\thouseEdge\x120\n" +
	"\x14max_duration_seconds\x18\x03 \x01(\x03R\x12maxDurationSeconds\x12X\n" +
	"\rgame_specific\x18\x04 \x03(\v23.game_engine.SessionConfiguration.GameSpecificEntryR\fgameSpecific\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb6\x03\n" +
	"\vGameResults\x12$\n" +
	"\x0ewinner_user_id\x18\x01 \x01(\tR\fwinnerUserId\x12'\n" +
	"\x0fwinner_position\x18\x02 \x01(\x05R\x0ewinnerPosition\x12$\n" +
	"\x0etotal_bet_pool\x18\x03 \x01(\x03R\ftotalBetPool\x12\x1d\n" +
	"\n" +
	"house_take\x18\x04 \x01(\x03R\thouseTake\x12\x1d\n" +
	"\n" +
	"prize_pool\x18\x05 \x01(\x03R\tprizePool\x123\n" +
	"\apayouts\x18\x06 \x03(\v2\x19.game_engine.PlayerPayoutR\apayouts\x12C\n" +
	"\tgame_data\x18\a \x03(\v2&.game_engine.GameResults.GameDataEntryR\bgameData\x12=\n" +
	"\fcompleted_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x1a;\n" +
	"\rGameDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\fPlayerPayout\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x03R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x1c\n" +
	"\tprocessed\x18\x04 \x01(\bR\tprocessed\"\xf2\x01\n" +
	"\rFairnessProof\x12\x1f\n" +
	"\vserver_seed\x18\x01 \x01(\tR\n" +
	"serverSeed\x12\x1f\n" +
	"\vclient_seed\x18\x02 \x01(\tR\n" +
	"clientSeed\x12\x14\n" +
	"\x05nonce\x18\x03 \x01(\x03R\x05nonce\x12\x12\n" +
	"\x04hash\x18\x04 \x01(\tR\x04hash\x12\x16\n" +
	"\x06result\x18\x05 \x01(\tR\x06result\x12\x1e\n" +
	"\n" +
	"verifiable\x18\x06 \x01(\bR\n" +
	"verifiable\x12=\n" +
	"\fgenerated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vgeneratedAt\"\xbc\x03\n" +
	"\tGameState\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12,\n" +
	"\x05phase\x18\x02 \x01(\x0e2\x16.game_engine.GamePhaseR\x05phase\x124\n" +
	"\aplayers\x18\x03 \x03(\v2\x1a.game_engine.SessionPlayerR\aplayers\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12A\n" +
	"\tgame_data\x18\x05 \x03(\v2$.game_engine.GameState.GameDataEntryR\bgameData\x122\n" +
	"\aresults\x18\x06 \x01(\v2\x18.game_engine.GameResultsR\aresults\x12=\n" +
	"\flast_updated\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\x1a;\n" +
	"\rGameDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc0\x02\n" +
	"\x11PayoutCalculation\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x12$\n" +
	"\x0etotal_bet_pool\x18\x02 \x01(\x03R\ftotalBetPool\x12\x1d\n" +
	"\n" +
	"house_edge\x18\x03 \x01(\x01R\thouseEdge\x12\x1d\n" +
	"\n" +
	"house_take\x18\x04 \x01(\x03R\thouseTake\x12\x1d\n" +
	"\n" +
	"prize_pool\x18\x05 \x01(\x03R\tprizePool\x123\n" +
	"\apayouts\x18\x06 \x03(\v2\x19.game_engine.PlayerPayoutR\apayouts\x12?\n" +
	"\rcalculated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\fcalculatedAt\"\xe6\x01\n" +
	"\x18CreateGameSessionRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x122\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x124\n" +
	"\aplayers\x18\x03 \x03(\v2\x1a.game_engine.SessionPlayerR\aplayers\x12G\n" +
	"\rconfiguration\x18\x04 \x01(\v2!.game_engine.SessionConfigurationR\rconfiguration\"e\n" +
	"\x19CreateGameSessionResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.game_engine.GameSessionR\asession\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"6\n" +
	"\x15GetGameSessionRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\"b\n" +
	"\x16GetGameSessionResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.game_engine.GameSessionR\asession\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"1\n" +
	"\x10StartGameRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\"]\n" +
	"\x11StartGameResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.game_engine.GameSessionR\asession\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xf0\x01\n" +
	"\x12ExecuteGameRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x122\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x12J\n" +
	"\tgame_data\x18\x03 \x03(\v2-.game_engine.ExecuteGameRequest.GameDataEntryR\bgameData\x1a;\n" +
	"\rGameDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"_\n" +
	"\x13ExecuteGameResponse\x122\n" +
	"\aresults\x18\x01 \x01(\v2\x18.game_engine.GameResultsR\aresults\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"c\n" +
	"\x0eEndGameRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x122\n" +
	"\aresults\x18\x02 \x01(\v2\x18.game_engine.GameResultsR\aresults\"[\n" +
	"\x0fEndGameResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.game_engine.GameSessionR\asession\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"J\n" +
	"\x11CancelGameRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"^\n" +
	"\x12CancelGameResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.game_engine.GameSessionR\asession\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"4\n" +
	"\x13GetGameStateRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\"Z\n" +
	"\x14GetGameStateResponse\x12,\n" +
	"\x05state\x18\x01 \x01(\v2\x16.game_engine.GameStateR\x05state\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"6\n" +
	"\x15GetGameResultsRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\"b\n" +
	"\x16GetGameResultsResponse\x122\n" +
	"\aresults\x18\x01 \x01(\v2\x18.game_engine.GameResultsR\aresults\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"l\n" +
	"\x17CalculatePayoutsRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x122\n" +
	"\aresults\x18\x02 \x01(\v2\x18.game_engine.GameResultsR\aresults\"r\n" +
	"\x18CalculatePayoutsResponse\x12@\n" +
	"\vcalculation\x18\x01 \x01(\v2\x1e.game_engine.PayoutCalculationR\vcalculation\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"=\n" +
	"\x1cGenerateFairnessProofRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\"g\n" +
	"\x1dGenerateFairnessProofResponse\x120\n" +
	"\x05proof\x18\x01 \x01(\v2\x1a.game_engine.FairnessProofR\x05proof\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"m\n" +
	"\x1aVerifyFairnessProofRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x120\n" +
	"\x05proof\x18\x02 \x01(\v2\x1a.game_engine.FairnessProofR\x05proof\"I\n" +
	"\x1bVerifyFairnessProofResponse\x12\x14\n" +
	"\x05valid\x18\x01 \x01(\bR\x05valid\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\x80\x02\n" +
	" ValidateGameConfigurationRequest\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\x12f\n" +
	"\rconfiguration\x18\x02 \x03(\v2@.game_engine.ValidateGameConfigurationRequest.ConfigurationEntryR\rconfiguration\x1a@\n" +
	"\x12ConfigurationEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"O\n" +
	"!ValidateGameConfigurationResponse\x12\x14\n" +
	"\x05valid\x18\x01 \x01(\bR\x05valid\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\x1e\n" +
	"\x1cGetSupportedGameTypesRequest\"k\n" +
	"\x1dGetSupportedGameTypesResponse\x124\n" +
	"\n" +
	"game_types\x18\x01 \x03(\x0e2\x15.game_engine.GameTypeR\tgameTypes\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\x1b\n" +
	"\x19GetActiveGameCountRequest\"H\n" +
	"\x1aGetActiveGameCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"N\n" +
	"\x18GetGameStatisticsRequest\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.game_engine.GameTypeR\bgameType\"\xc8\x01\n" +
	"\x19GetGameStatisticsResponse\x12V\n" +
	"\n" +
	"statistics\x18\x01 \x03(\v26.game_engine.GetGameStatisticsResponse.StatisticsEntryR\n" +
	"statistics\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x1a=\n" +
	"\x0fStatisticsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*Y\n" +
	"\bGameType\x12\x19\n" +
	"\x15GAME_TYPE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15GAME_TYPE_PRIZE_WHEEL\x10\x01\x12\x17\n" +
	"\x13GAME_TYPE_AMIDAKUJI\x10\x02*\xbe\x01\n" +
	"\rSessionStatus\x12\x1e\n" +
	"\x1aSESSION_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16SESSION_STATUS_PENDING\x10\x01\x12\x1a\n" +
	"\x16SESSION_STATUS_WAITING\x10\x02\x12\x19\n" +
	"\x15SESSION_STATUS_ACTIVE\x10\x03\x12\x1c\n" +
	"\x18SESSION_STATUS_COMPLETED\x10\x04\x12\x1c\n" +
	"\x18SESSION_STATUS_CANCELLED\x10\x05*\xb8\x01\n" +
	"\fPlayerStatus\x12\x1d\n" +
	"\x19PLAYER_STATUS_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15PLAYER_STATUS_WAITING\x10\x01\x12\x17\n" +
	"\x13PLAYER_STATUS_READY\x10\x02\x12\x19\n" +
	"\x15PLAYER_STATUS_PLAYING\x10\x03\x12\x1a\n" +
	"\x16PLAYER_STATUS_FINISHED\x10\x04\x12\x1e\n" +
	"\x1aPLAYER_STATUS_DISCONNECTED\x10\x05*\xa2\x01\n" +
	"\tGamePhase\x12\x1a\n" +
	"\x16GAME_PHASE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12GAME_PHASE_PENDING\x10\x01\x12\x16\n" +
	"\x12GAME_PHASE_WAITING\x10\x02\x12\x15\n" +
	"\x11GAME_PHASE_ACTIVE\x10\x03\x12\x18\n" +
	"\x14GAME_PHASE_COMPLETED\x10\x04\x12\x18\n" +
	"\x14GAME_PHASE_CANCELLED\x10\x052\xa7\v\n" +
	"\x11GameEngineService\x12b\n" +
	"\x11CreateGameSession\x12%.game_engine.CreateGameSessionRequest\x1a&.game_engine.CreateGameSessionResponse\x12Y\n" +
	"\x0eGetGameSession\x12\".game_engine.GetGameSessionRequest\x1a#.game_engine.GetGameSessionResponse\x12J\n" +
	"\tStartGame\x12\x1d.game_engine.StartGameRequest\x1a\x1e.game_engine.StartGameResponse\x12P\n" +
	"\vExecuteGame\x12\x1f.game_engine.ExecuteGameRequest\x1a .game_engine.ExecuteGameResponse\x12D\n" +
	"\aEndGame\x12\x1b.game_engine.EndGameRequest\x1a\x1c.game_engine.EndGameResponse\x12M\n" +
	"\n" +
	"CancelGame\x12\x1e.game_engine.CancelGameRequest\x1a\x1f.game_engine.CancelGameResponse\x12S\n" +
	"\fGetGameState\x12 .game_engine.GetGameStateRequest\x1a!.game_engine.GetGameStateResponse\x12Y\n" +
	"\x0eGetGameResults\x12\".game_engine.GetGameResultsRequest\x1a#.game_engine.GetGameResultsResponse\x12_\n" +
	"\x10CalculatePayouts\x12$.game_engine.CalculatePayoutsRequest\x1a%.game_engine.CalculatePayoutsResponse\x12n\n" +
	"\x15GenerateFairnessProof\x12).game_engine.GenerateFairnessProofRequest\x1a*.game_engine.GenerateFairnessProofResponse\x12h\n" +
	"\x13VerifyFairnessProof\x12'.game_engine.VerifyFairnessProofRequest\x1a(.game_engine.VerifyFairnessProofResponse\x12z\n" +
	"\x19ValidateGameConfiguration\x12-.game_engine.ValidateGameConfigurationRequest\x1a..game_engine.ValidateGameConfigurationResponse\x12n\n" +
	"\x15GetSupportedGameTypes\x12).game_engine.GetSupportedGameTypesRequest\x1a*.game_engine.GetSupportedGameTypesResponse\x12e\n" +
	"\x12GetActiveGameCount\x12&.game_engine.GetActiveGameCountRequest\x1a'.game_engine.GetActiveGameCountResponse\x12b\n" +
	"\x11GetGameStatistics\x12%.game_engine.GetGameStatisticsRequest\x1a&.game_engine.GetGameStatisticsResponseB-Z+github.com/xzgame/game-engine-service/protob\x06proto3"

var (
	file_proto_game_engine_proto_rawDescOnce sync.Once
	file_proto_game_engine_proto_rawDescData []byte
)

func file_proto_game_engine_proto_rawDescGZIP() []byte {
	file_proto_game_engine_proto_rawDescOnce.Do(func() {
		file_proto_game_engine_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_game_engine_proto_rawDesc), len(file_proto_game_engine_proto_rawDesc)))
	})
	return file_proto_game_engine_proto_rawDescData
}

var file_proto_game_engine_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_proto_game_engine_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_proto_game_engine_proto_goTypes = []any{
	(GameType)(0),                             // 0: game_engine.GameType
	(SessionStatus)(0),                        // 1: game_engine.SessionStatus
	(PlayerStatus)(0),                         // 2: game_engine.PlayerStatus
	(GamePhase)(0),                            // 3: game_engine.GamePhase
	(*GameSession)(nil),                       // 4: game_engine.GameSession
	(*SessionPlayer)(nil),                     // 5: game_engine.SessionPlayer
	(*SessionConfiguration)(nil),              // 6: game_engine.SessionConfiguration
	(*GameResults)(nil),                       // 7: game_engine.GameResults
	(*PlayerPayout)(nil),                      // 8: game_engine.PlayerPayout
	(*FairnessProof)(nil),                     // 9: game_engine.FairnessProof
	(*GameState)(nil),                         // 10: game_engine.GameState
	(*PayoutCalculation)(nil),                 // 11: game_engine.PayoutCalculation
	(*CreateGameSessionRequest)(nil),          // 12: game_engine.CreateGameSessionRequest
	(*CreateGameSessionResponse)(nil),         // 13: game_engine.CreateGameSessionResponse
	(*GetGameSessionRequest)(nil),             // 14: game_engine.GetGameSessionRequest
	(*GetGameSessionResponse)(nil),            // 15: game_engine.GetGameSessionResponse
	(*StartGameRequest)(nil),                  // 16: game_engine.StartGameRequest
	(*StartGameResponse)(nil),                 // 17: game_engine.StartGameResponse
	(*ExecuteGameRequest)(nil),                // 18: game_engine.ExecuteGameRequest
	(*ExecuteGameResponse)(nil),               // 19: game_engine.ExecuteGameResponse
	(*EndGameRequest)(nil),                    // 20: game_engine.EndGameRequest
	(*EndGameResponse)(nil),                   // 21: game_engine.EndGameResponse
	(*CancelGameRequest)(nil),                 // 22: game_engine.CancelGameRequest
	(*CancelGameResponse)(nil),                // 23: game_engine.CancelGameResponse
	(*GetGameStateRequest)(nil),               // 24: game_engine.GetGameStateRequest
	(*GetGameStateResponse)(nil),              // 25: game_engine.GetGameStateResponse
	(*GetGameResultsRequest)(nil),             // 26: game_engine.GetGameResultsRequest
	(*GetGameResultsResponse)(nil),            // 27: game_engine.GetGameResultsResponse
	(*CalculatePayoutsRequest)(nil),           // 28: game_engine.CalculatePayoutsRequest
	(*CalculatePayoutsResponse)(nil),          // 29: game_engine.CalculatePayoutsResponse
	(*GenerateFairnessProofRequest)(nil),      // 30: game_engine.GenerateFairnessProofRequest
	(*GenerateFairnessProofResponse)(nil),     // 31: game_engine.GenerateFairnessProofResponse
	(*VerifyFairnessProofRequest)(nil),        // 32: game_engine.VerifyFairnessProofRequest
	(*VerifyFairnessProofResponse)(nil),       // 33: game_engine.VerifyFairnessProofResponse
	(*ValidateGameConfigurationRequest)(nil),  // 34: game_engine.ValidateGameConfigurationRequest
	(*ValidateGameConfigurationResponse)(nil), // 35: game_engine.ValidateGameConfigurationResponse
	(*GetSupportedGameTypesRequest)(nil),      // 36: game_engine.GetSupportedGameTypesRequest
	(*GetSupportedGameTypesResponse)(nil),     // 37: game_engine.GetSupportedGameTypesResponse
	(*GetActiveGameCountRequest)(nil),         // 38: game_engine.GetActiveGameCountRequest
	(*GetActiveGameCountResponse)(nil),        // 39: game_engine.GetActiveGameCountResponse
	(*GetGameStatisticsRequest)(nil),          // 40: game_engine.GetGameStatisticsRequest
	(*GetGameStatisticsResponse)(nil),         // 41: game_engine.GetGameStatisticsResponse
	nil,                                       // 42: game_engine.SessionConfiguration.GameSpecificEntry
	nil,                                       // 43: game_engine.GameResults.GameDataEntry
	nil,                                       // 44: game_engine.GameState.GameDataEntry
	nil,                                       // 45: game_engine.ExecuteGameRequest.GameDataEntry
	nil,                                       // 46: game_engine.ValidateGameConfigurationRequest.ConfigurationEntry
	nil,                                       // 47: game_engine.GetGameStatisticsResponse.StatisticsEntry
	(*timestamppb.Timestamp)(nil),             // 48: google.protobuf.Timestamp
}
var file_proto_game_engine_proto_depIdxs = []int32{
	0,  // 0: game_engine.GameSession.game_type:type_name -> game_engine.GameType
	1,  // 1: game_engine.GameSession.status:type_name -> game_engine.SessionStatus
	5,  // 2: game_engine.GameSession.players:type_name -> game_engine.SessionPlayer
	48, // 3: game_engine.GameSession.start_time:type_name -> google.protobuf.Timestamp
	48, // 4: game_engine.GameSession.end_time:type_name -> google.protobuf.Timestamp
	7,  // 5: game_engine.GameSession.results:type_name -> game_engine.GameResults
	6,  // 6: game_engine.GameSession.configuration:type_name -> game_engine.SessionConfiguration
	9,  // 7: game_engine.GameSession.fairness_proof:type_name -> game_engine.FairnessProof
	48, // 8: game_engine.GameSession.created_at:type_name -> google.protobuf.Timestamp
	48, // 9: game_engine.GameSession.updated_at:type_name -> google.protobuf.Timestamp
	48, // 10: game_engine.SessionPlayer.joined_at:type_name -> google.protobuf.Timestamp
	2,  // 11: game_engine.SessionPlayer.status:type_name -> game_engine.PlayerStatus
	0,  // 12: game_engine.SessionConfiguration.game_type:type_name -> game_engine.GameType
	42, // 13: game_engine.SessionConfiguration.game_specific:type_name -> game_engine.SessionConfiguration.GameSpecificEntry
	8,  // 14: game_engine.GameResults.payouts:type_name -> game_engine.PlayerPayout
	43, // 15: game_engine.GameResults.game_data:type_name -> game_engine.GameResults.GameDataEntry
	48, // 16: game_engine.GameResults.completed_at:type_name -> google.protobuf.Timestamp
	48, // 17: game_engine.FairnessProof.generated_at:type_name -> google.protobuf.Timestamp
	3,  // 18: game_engine.GameState.phase:type_name -> game_engine.GamePhase
	5,  // 19: game_engine.GameState.players:type_name -> game_engine.SessionPlayer
	48, // 20: game_engine.GameState.start_time:type_name -> google.protobuf.Timestamp
	44, // 21: game_engine.GameState.game_data:type_name -> game_engine.GameState.GameDataEntry
	7,  // 22: game_engine.GameState.results:type_name -> game_engine.GameResults
	48, // 23: game_engine.GameState.last_updated:type_name -> google.protobuf.Timestamp
	0,  // 24: game_engine.PayoutCalculation.game_type:type_name -> game_engine.GameType
	8,  // 25: game_engine.PayoutCalculation.payouts:type_name -> game_engine.PlayerPayout
	48, // 26: game_engine.PayoutCalculation.calculated_at:type_name -> google.protobuf.Timestamp
	0,  // 27: game_engine.CreateGameSessionRequest.game_type:type_name -> game_engine.GameType
	5,  // 28: game_engine.CreateGameSessionRequest.players:type_name -> game_engine.SessionPlayer
	6,  // 29: game_engine.CreateGameSessionRequest.configuration:type_name -> game_engine.SessionConfiguration
	4,  // 30: game_engine.CreateGameSessionResponse.session:type_name -> game_engine.GameSession
	4,  // 31: game_engine.GetGameSessionResponse.session:type_name -> game_engine.GameSession
	4,  // 32: game_engine.StartGameResponse.session:type_name -> game_engine.GameSession
	0,  // 33: game_engine.ExecuteGameRequest.game_type:type_name -> game_engine.GameType
	45, // 34: game_engine.ExecuteGameRequest.game_data:type_name -> game_engine.ExecuteGameRequest.GameDataEntry
	7,  // 35: game_engine.ExecuteGameResponse.results:type_name -> game_engine.GameResults
	7,  // 36: game_engine.EndGameRequest.results:type_name -> game_engine.GameResults
	4,  // 37: game_engine.EndGameResponse.session:type_name -> game_engine.GameSession
	4,  // 38: game_engine.CancelGameResponse.session:type_name -> game_engine.GameSession
	10, // 39: game_engine.GetGameStateResponse.state:type_name -> game_engine.GameState
	7,  // 40: game_engine.GetGameResultsResponse.results:type_name -> game_engine.GameResults
	7,  // 41: game_engine.CalculatePayoutsRequest.results:type_name -> game_engine.GameResults
	11, // 42: game_engine.CalculatePayoutsResponse.calculation:type_name -> game_engine.PayoutCalculation
	9,  // 43: game_engine.GenerateFairnessProofResponse.proof:type_name -> game_engine.FairnessProof
	9,  // 44: game_engine.VerifyFairnessProofRequest.proof:type_name -> game_engine.FairnessProof
	0,  // 45: game_engine.ValidateGameConfigurationRequest.game_type:type_name -> game_engine.GameType
	46, // 46: game_engine.ValidateGameConfigurationRequest.configuration:type_name -> game_engine.ValidateGameConfigurationRequest.ConfigurationEntry
	0,  // 47: game_engine.GetSupportedGameTypesResponse.game_types:type_name -> game_engine.GameType
	0,  // 48: game_engine.GetGameStatisticsRequest.game_type:type_name -> game_engine.GameType
	47, // 49: game_engine.GetGameStatisticsResponse.statistics:type_name -> game_engine.GetGameStatisticsResponse.StatisticsEntry
	12, // 50: game_engine.GameEngineService.CreateGameSession:input_type -> game_engine.CreateGameSessionRequest
	14, // 51: game_engine.GameEngineService.GetGameSession:input_type -> game_engine.GetGameSessionRequest
	16, // 52: game_engine.GameEngineService.StartGame:input_type -> game_engine.StartGameRequest
	18, // 53: game_engine.GameEngineService.ExecuteGame:input_type -> game_engine.ExecuteGameRequest
	20, // 54: game_engine.GameEngineService.EndGame:input_type -> game_engine.EndGameRequest
	22, // 55: game_engine.GameEngineService.CancelGame:input_type -> game_engine.CancelGameRequest
	24, // 56: game_engine.GameEngineService.GetGameState:input_type -> game_engine.GetGameStateRequest
	26, // 57: game_engine.GameEngineService.GetGameResults:input_type -> game_engine.GetGameResultsRequest
	28, // 58: game_engine.GameEngineService.CalculatePayouts:input_type -> game_engine.CalculatePayoutsRequest
	30, // 59: game_engine.GameEngineService.GenerateFairnessProof:input_type -> game_engine.GenerateFairnessProofRequest
	32, // 60: game_engine.GameEngineService.VerifyFairnessProof:input_type -> game_engine.VerifyFairnessProofRequest
	34, // 61: game_engine.GameEngineService.ValidateGameConfiguration:input_type -> game_engine.ValidateGameConfigurationRequest
	36, // 62: game_engine.GameEngineService.GetSupportedGameTypes:input_type -> game_engine.GetSupportedGameTypesRequest
	38, // 63: game_engine.GameEngineService.GetActiveGameCount:input_type -> game_engine.GetActiveGameCountRequest
	40, // 64: game_engine.GameEngineService.GetGameStatistics:input_type -> game_engine.GetGameStatisticsRequest
	13, // 65: game_engine.GameEngineService.CreateGameSession:output_type -> game_engine.CreateGameSessionResponse
	15, // 66: game_engine.GameEngineService.GetGameSession:output_type -> game_engine.GetGameSessionResponse
	17, // 67: game_engine.GameEngineService.StartGame:output_type -> game_engine.StartGameResponse
	19, // 68: game_engine.GameEngineService.ExecuteGame:output_type -> game_engine.ExecuteGameResponse
	21, // 69: game_engine.GameEngineService.EndGame:output_type -> game_engine.EndGameResponse
	23, // 70: game_engine.GameEngineService.CancelGame:output_type -> game_engine.CancelGameResponse
	25, // 71: game_engine.GameEngineService.GetGameState:output_type -> game_engine.GetGameStateResponse
	27, // 72: game_engine.GameEngineService.GetGameResults:output_type -> game_engine.GetGameResultsResponse
	29, // 73: game_engine.GameEngineService.CalculatePayouts:output_type -> game_engine.CalculatePayoutsResponse
	31, // 74: game_engine.GameEngineService.GenerateFairnessProof:output_type -> game_engine.GenerateFairnessProofResponse
	33, // 75: game_engine.GameEngineService.VerifyFairnessProof:output_type -> game_engine.VerifyFairnessProofResponse
	35, // 76: game_engine.GameEngineService.ValidateGameConfiguration:output_type -> game_engine.ValidateGameConfigurationResponse
	37, // 77: game_engine.GameEngineService.GetSupportedGameTypes:output_type -> game_engine.GetSupportedGameTypesResponse
	39, // 78: game_engine.GameEngineService.GetActiveGameCount:output_type -> game_engine.GetActiveGameCountResponse
	41, // 79: game_engine.GameEngineService.GetGameStatistics:output_type -> game_engine.GetGameStatisticsResponse
	65, // [65:80] is the sub-list for method output_type
	50, // [50:65] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_proto_game_engine_proto_init() }
func file_proto_game_engine_proto_init() {
	if File_proto_game_engine_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_game_engine_proto_rawDesc), len(file_proto_game_engine_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_game_engine_proto_goTypes,
		DependencyIndexes: file_proto_game_engine_proto_depIdxs,
		EnumInfos:         file_proto_game_engine_proto_enumTypes,
		MessageInfos:      file_proto_game_engine_proto_msgTypes,
	}.Build()
	File_proto_game_engine_proto = out.File
	file_proto_game_engine_proto_goTypes = nil
	file_proto_game_engine_proto_depIdxs = nil
}
