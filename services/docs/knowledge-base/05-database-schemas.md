# Database Schemas and Data Models

**Status**: ✅ **Production Ready** - All schemas implemented and optimized

## Database Architecture

### Primary Databases
- **MongoDB**: Game data, user profiles, rooms, sessions
- **Redis**: Caching, sessions, pub/sub messaging
- **PostgreSQL**: Manager Service transactional data

### ✅ **Recent Schema Optimizations**
- **Modular Models**: Comprehensive data structures across all services
- **Index Optimization**: Performance-optimized database queries
- **Data Consistency**: Atomic operations and transactions
- **Schema Validation**: Input validation and type safety

## MongoDB Collections

### Users Collection

#### Schema Definition
```javascript
{
  _id: ObjectId,
  username: String, // unique, required, 3-30 chars
  email: String, // unique, required, lowercase
  passwordHash: String, // required
  role: String, // enum: ['player', 'admin', 'moderator'], default: 'player'
  balance: Number, // default: 0, min: 0
  currency: String, // enum: ['USD', 'EUR', 'GBP'], default: 'USD'
  profile: {
    firstName: String,
    lastName: String,
    avatar: String,
    dateOfBirth: Date,
    country: String,
    timezone: String
  },
  preferences: {
    language: String, // default: 'en'
    notifications: {
      email: Boolean, // default: true
      push: Boolean, // default: true
      sms: Boolean // default: false
    },
    privacy: {
      showOnline: Boolean, // default: true
      showStats: Boolean // default: true
    }
  },
  stats: {
    gamesPlayed: Number, // default: 0
    gamesWon: Number, // default: 0
    totalWinnings: Number, // default: 0
    totalLosses: Number, // default: 0
    favoriteGameType: String
  },
  security: {
    lastLogin: Date,
    lastLoginIP: String,
    loginAttempts: Number, // default: 0
    lockedUntil: Date,
    twoFactorEnabled: Boolean, // default: false
    twoFactorSecret: String
  },
  status: String, // enum: ['active', 'suspended', 'banned'], default: 'active'
  createdAt: Date, // default: Date.now
  updatedAt: Date, // default: Date.now
  deletedAt: Date // soft delete
}
```

#### Indexes
```javascript
// Unique indexes
db.users.createIndex({ username: 1 }, { unique: true })
db.users.createIndex({ email: 1 }, { unique: true })

// Query optimization indexes
db.users.createIndex({ status: 1, createdAt: -1 })
db.users.createIndex({ role: 1 })
db.users.createIndex({ "security.lastLogin": -1 })
```

### Rooms Collection

#### Schema Definition
```javascript
{
  _id: ObjectId,
  name: String, // required, max: 100 chars
  gameType: String, // enum: ['prizewheel', 'amidakuji'], required
  status: String, // enum: ['waiting', 'starting', 'playing', 'finished', 'archived']
  maxPlayers: Number, // required, min: 2, max: 8
  minPlayers: Number, // required, min: 2
  currentPlayers: Number, // default: 0
  players: [{
    userId: ObjectId, // ref: 'User'
    username: String,
    joinedAt: Date,
    isReady: Boolean, // default: false
    betAmount: Number,
    position: Number, // for games requiring positions
    gameSpecificData: Mixed // flexible data for game-specific info
  }],
  betLimits: {
    min: Number, // required
    max: Number // required
  },
  gameConfig: {
    autoStart: Boolean, // default: true
    autoStartDelay: Number, // seconds, default: 30
    maxGameDuration: Number, // seconds, default: 300
    gameSpecific: Mixed // game-specific configuration
  },
  prizePool: {
    total: Number, // default: 0
    currency: String, // default: 'USD'
    breakdown: [{
      userId: ObjectId,
      amount: Number
    }]
  },
  gameState: {
    phase: String, // enum: ['waiting', 'betting', 'playing', 'finished']
    startedAt: Date,
    finishedAt: Date,
    currentGameId: ObjectId, // ref: 'GameSession'
    lastActivity: Date
  },
  settings: {
    isPrivate: Boolean, // default: false
    password: String, // hashed if private
    allowSpectators: Boolean, // default: true
    chatEnabled: Boolean, // default: true
    maxSpectators: Number // default: 50
  },
  metadata: {
    createdBy: ObjectId, // ref: 'User'
    createdAt: Date, // default: Date.now
    updatedAt: Date, // default: Date.now
    archivedAt: Date,
    version: Number // for optimistic locking
  }
}
```

#### Indexes
```javascript
// Query optimization
db.rooms.createIndex({ gameType: 1, status: 1 })
db.rooms.createIndex({ status: 1, "metadata.createdAt": -1 })
db.rooms.createIndex({ "players.userId": 1 })
db.rooms.createIndex({ "metadata.createdBy": 1 })

// Compound indexes for common queries
db.rooms.createIndex({ 
  gameType: 1, 
  status: 1, 
  currentPlayers: 1, 
  maxPlayers: 1 
})
```

### Game Sessions Collection

#### Schema Definition
```javascript
{
  _id: ObjectId,
  roomId: ObjectId, // ref: 'Room', required
  gameType: String, // enum: ['prizewheel', 'amidakuji'], required
  sessionId: String, // unique identifier
  status: String, // enum: ['created', 'started', 'finished', 'cancelled']
  players: [{
    userId: ObjectId, // ref: 'User'
    username: String,
    betAmount: Number,
    position: Number,
    gameSpecificData: Mixed,
    result: {
      won: Boolean,
      winAmount: Number,
      finalPosition: Number
    }
  }],
  gameData: {
    // Prize Wheel specific
    wheelPosition: Number, // final wheel position in degrees
    winningColor: String,
    spinDuration: Number,
    
    // Amidakuji specific
    pattern: [[Boolean]], // 2D array of connections
    pathTraces: [{
      playerId: ObjectId,
      startPosition: Number,
      endPosition: Number,
      path: [Number] // positions at each level
    }],
    
    // Common
    randomSeed: String, // for fairness verification
    fairnessProof: String
  },
  results: {
    winner: {
      userId: ObjectId,
      username: String,
      winAmount: Number
    },
    prizePool: Number,
    payouts: [{
      userId: ObjectId,
      amount: Number,
      type: String // 'win', 'refund'
    }]
  },
  timing: {
    createdAt: Date,
    startedAt: Date,
    finishedAt: Date,
    duration: Number // milliseconds
  },
  metadata: {
    version: String, // game engine version
    serverInstance: String,
    fairnessHash: String,
    auditTrail: [Mixed] // for compliance
  }
}
```

### Transactions Collection

#### Schema Definition
```javascript
{
  _id: ObjectId,
  userId: ObjectId, // ref: 'User', required
  type: String, // enum: ['deposit', 'withdrawal', 'bet_placed', 'bet_won', 'bet_refund']
  status: String, // enum: ['pending', 'completed', 'failed', 'cancelled']
  amount: Number, // required
  currency: String, // default: 'USD'
  balanceBefore: Number,
  balanceAfter: Number,
  description: String,
  reference: {
    type: String, // 'game_session', 'room', 'manual'
    id: ObjectId, // reference to related entity
    externalId: String // external payment processor ID
  },
  payment: {
    method: String, // 'credit_card', 'bank_transfer', 'crypto'
    processor: String, // 'stripe', 'paypal', 'coinbase'
    processorTransactionId: String,
    processorFee: Number
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    adminUserId: ObjectId, // if admin-initiated
    notes: String,
    tags: [String]
  },
  timing: {
    createdAt: Date,
    processedAt: Date,
    completedAt: Date,
    expiresAt: Date
  },
  auditTrail: [{
    action: String,
    userId: ObjectId,
    timestamp: Date,
    details: Mixed
  }]
}
```

## Redis Data Structures

### Session Storage

#### User Sessions
```
Key: session:{sessionId}
Type: Hash
TTL: 24 hours

Fields:
- userId: string
- username: string
- role: string
- permissions: JSON array
- createdAt: timestamp
- lastActivity: timestamp
- ipAddress: string
- userAgent: string
```

#### Active Connections
```
Key: connections:{userId}
Type: Set
TTL: 1 hour

Members: socketId1, socketId2, ...
```

### Room State Cache

#### Room Information
```
Key: room:{roomId}:info
Type: Hash
TTL: 30 minutes

Fields:
- name: string
- gameType: string
- status: string
- playerCount: number
- maxPlayers: number
- lastUpdated: timestamp
```

#### Room Players
```
Key: room:{roomId}:players
Type: Hash
TTL: 30 minutes

Fields:
- {userId}: JSON player data
```

#### Prize Wheel Color State
```
Key: room:{roomId}:colors
Type: Hash
TTL: Until game ends

Fields:
- {userId}: colorId

Key: room:{roomId}:available_colors
Type: Set
TTL: Until game ends

Members: red, blue, green, yellow, purple, orange, pink, cyan
```

### Game State Cache

#### Active Games
```
Key: game:{gameId}:state
Type: Hash
TTL: 1 hour

Fields:
- status: string
- startedAt: timestamp
- players: JSON array
- gameData: JSON object
```

#### Game Results
```
Key: game:{gameId}:results
Type: Hash
TTL: 24 hours

Fields:
- winner: JSON object
- prizePool: number
- payouts: JSON array
- finishedAt: timestamp
```

### Pub/Sub Channels

#### Channel Patterns
```
room:{roomId}:events - Room-specific events
user:{userId}:notifications - User notifications
game:global - Global game events
admin:notifications - Admin notifications
socket:events - Socket gateway events
```

### Rate Limiting

#### User Rate Limits
```
Key: rate_limit:{userId}:{endpoint}
Type: String (counter)
TTL: Based on rate limit window

Value: request_count
```

#### IP Rate Limits
```
Key: rate_limit:ip:{ipAddress}
Type: String (counter)
TTL: Based on rate limit window

Value: request_count
```

### Caching Strategy

#### Cache Hierarchy
1. **L1 Cache**: In-memory application cache (5 minutes TTL)
2. **L2 Cache**: Redis cache (30 minutes TTL)
3. **L3 Storage**: MongoDB (persistent)

#### Cache Invalidation Patterns
```javascript
// Write-through pattern for critical data
async updateRoom(roomId, data) {
  // Update database
  await db.rooms.updateOne({ _id: roomId }, data);
  
  // Update cache
  await redis.hset(`room:${roomId}:info`, data);
  
  // Publish invalidation event
  await redis.publish('cache:invalidate', `room:${roomId}`);
}

// Cache-aside pattern for read-heavy data
async getRoom(roomId) {
  // Try cache first
  let room = await redis.hgetall(`room:${roomId}:info`);
  
  if (!room || Object.keys(room).length === 0) {
    // Cache miss - fetch from database
    room = await db.rooms.findOne({ _id: roomId });
    
    if (room) {
      // Populate cache
      await redis.hset(`room:${roomId}:info`, room);
      await redis.expire(`room:${roomId}:info`, 1800); // 30 minutes
    }
  }
  
  return room;
}
```

## Data Relationships

### Entity Relationship Diagram
```
Users (1) ←→ (N) Transactions
Users (N) ←→ (N) Rooms (through players array)
Rooms (1) ←→ (N) GameSessions
Users (N) ←→ (N) GameSessions (through players array)
Users (1) ←→ (N) UserSessions (Redis)
Rooms (1) ←→ (1) RoomState (Redis)
```

### Foreign Key Constraints
While MongoDB doesn't enforce foreign key constraints, the application layer maintains referential integrity through:

1. **Validation middleware** before database operations
2. **Cascade operations** for related data cleanup
3. **Consistency checks** in background jobs
4. **Audit trails** for data integrity monitoring

### Data Consistency Patterns

#### Eventual Consistency
- Room state updates across multiple services
- User balance synchronization between services
- Game statistics aggregation

#### Strong Consistency
- Financial transactions (ACID properties)
- Game result calculations
- User authentication state

#### Optimistic Locking
```javascript
// Version-based optimistic locking
const updateRoom = async (roomId, updates) => {
  const room = await db.rooms.findOne({ _id: roomId });
  
  const result = await db.rooms.updateOne(
    { 
      _id: roomId, 
      'metadata.version': room.metadata.version 
    },
    { 
      ...updates, 
      'metadata.version': room.metadata.version + 1,
      'metadata.updatedAt': new Date()
    }
  );
  
  if (result.modifiedCount === 0) {
    throw new Error('Concurrent modification detected');
  }
};
```
