# API Documentation

**Status**: ✅ **Production Ready** - All endpoints implemented and tested

## REST API Endpoints (API Gateway)

### System Endpoints

#### GET /health
Health check endpoint for service monitoring.

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0",
    "dependencies": {
      "redis": "connected",
      "mongodb": "connected"
    }
  }
}
```

#### GET /metrics
Prometheus metrics endpoint (when enabled).

#### GET /info
Service information and configuration.

#### GET /api-docs
Swagger/OpenAPI documentation interface.

### Authentication Endpoints

#### POST /api/v1/auth/register
Register a new user account.

**Rate Limit**: 10 requests per minute per IP

**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "role": "player"
    },
    "tokens": {
      "accessToken": "string",
      "refreshToken": "string"
    }
  },
  "meta": {
    "timestamp": "2024-12-01T10:00:00.000Z",
    "request_id": "uuid"
  }
}
```

#### POST /api/v1/auth/login
Authenticate user and receive tokens.

**Rate Limit**: 10 requests per minute per IP

**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "role": "player"
    },
    "tokens": {
      "accessToken": "string",
      "refreshToken": "string"
    }
  }
}
```

#### POST /api/v1/auth/logout
Invalidate user session and tokens.

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "message": "Successfully logged out"
}
```

#### POST /api/v1/auth/refresh
Refresh access token using refresh token.

**Request Body**:
```json
{
  "refreshToken": "string"
}
```

### User Management Endpoints

#### GET /api/v1/users/profile
Get current user profile information.

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "string",
    "username": "string",
    "email": "string",
    "balance": 1000.00,
    "currency": "USD",
    "profile": {
      "firstName": "string",
      "lastName": "string",
      "avatar": "string"
    }
  }
}
```

#### PUT /api/v1/users/profile
Update user profile information.

#### GET /api/v1/users/balance
Get current user balance.

### Room Management Endpoints

#### GET /api/v1/rooms
List available rooms with filtering options.

**Query Parameters**:
- `gameType`: Filter by game type (prizewheel, amidakuji)
- `status`: Filter by room status (waiting, playing)
- `limit`: Number of results (default: 20)
- `offset`: Pagination offset

#### POST /api/v1/rooms
Create a new game room.

**Request Body**:
```json
{
  "name": "string",
  "gameType": "prizewheel|amidakuji",
  "maxPlayers": 8,
  "minPlayers": 2,
  "betLimits": {
    "min": 10.00,
    "max": 1000.00
  },
  "autoStart": true,
  "isPrivate": false,
  "gameSpecific": {}
}
```

#### POST /api/v1/rooms/:id/join
Join a specific room.

#### POST /api/v1/rooms/:id/leave
Leave a specific room.

### Transaction Endpoints

#### GET /api/v1/transactions/history
Get user transaction history.

**Query Parameters**:
- `type`: Filter by transaction type
- `status`: Filter by status
- `startDate`: Date range start
- `endDate`: Date range end
- `limit`: Results limit
- `offset`: Pagination offset

#### GET /api/v1/transactions/:id
Get specific transaction details.

### Game History Endpoints

#### GET /api/v1/games/history
Get user game history.

#### GET /api/v1/games/:id
Get specific game details and results.

### System Endpoints

#### GET /health
Service health check.

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "services": {
      "database": {"status": "healthy"},
      "redis": {"status": "healthy"},
      "gameService": {"status": "healthy"}
    }
  }
}
```

#### GET /metrics
Prometheus metrics endpoint.

#### GET /api-docs
Swagger API documentation.

## gRPC Service Contracts

### Game Service

#### Service Definition
```protobuf
service GameService {
  rpc CreateRoom(CreateRoomRequest) returns (CreateRoomResponse);
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
  rpc StartGame(StartGameRequest) returns (StartGameResponse);
  rpc GetGameState(GetGameStateRequest) returns (GetGameStateResponse);
  rpc GetGameHistory(GetGameHistoryRequest) returns (GetGameHistoryResponse);
}
```

#### Key Messages
```protobuf
message JoinRoomRequest {
  string room_id = 1;
  string user_id = 2;
  double bet_amount = 3;
}

message JoinRoomResponse {
  Room room = 1;
  string error = 2;
  bool success = 3;
}
```

### Auth Service

#### Service Definition
```protobuf
service AuthService {
  rpc VerifyToken(VerifyTokenRequest) returns (VerifyTokenResponse);
  rpc CreateSession(CreateSessionRequest) returns (CreateSessionResponse);
  rpc InvalidateSession(InvalidateSessionRequest) returns (InvalidateSessionResponse);
  rpc GetSession(GetSessionRequest) returns (GetSessionResponse);
}
```

### Room Service

#### Service Definition
```protobuf
service RoomService {
  rpc CreateRoom(CreateRoomRequest) returns (CreateRoomResponse);
  rpc GetRoom(GetRoomRequest) returns (GetRoomResponse);
  rpc UpdateRoom(UpdateRoomRequest) returns (UpdateRoomResponse);
  rpc DeleteRoom(DeleteRoomRequest) returns (DeleteRoomResponse);
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse);
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
}
```

### Notification Service

#### Service Definition
```protobuf
service NotificationService {
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc BroadcastNotification(BroadcastNotificationRequest) returns (BroadcastNotificationResponse);
  rpc Subscribe(SubscribeRequest) returns (SubscribeResponse);
  rpc Unsubscribe(UnsubscribeRequest) returns (UnsubscribeResponse);
}
```

## Authentication and Authorization

### JWT Token Structure
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "string",
    "username": "string",
    "role": "player|admin|moderator",
    "permissions": ["array"],
    "iss": "xzgame-auth-service",
    "aud": ["xzgame-api", "xzgame-game-service"],
    "exp": 1234567890,
    "iat": 1234567890
  }
}
```

### Role-Based Access Control

#### Roles and Permissions
- **Player**: Basic game access, profile management
- **Moderator**: Room management, user moderation
- **Admin**: Full system access, user management, financial operations

#### Protected Endpoints
All endpoints except authentication require valid JWT tokens.
Admin endpoints require admin role verification.

### Rate Limiting
- **Default**: 100 requests per minute per IP
- **Authentication**: 10 requests per minute per IP
- **Admin endpoints**: 50 requests per minute per user

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00.000Z",
    "request_id": "uuid"
  }
}
```

### Common Error Codes
- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions
- `VALIDATION_ERROR`: Request validation failed
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Server error
