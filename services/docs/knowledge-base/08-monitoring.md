# Monitoring and Observability

## Logging Strategies

### Structured Logging Implementation

#### Node.js Services (Winston)
```javascript
const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = {
      timestamp,
      level,
      message,
      service: 'api-gateway',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV,
      ...meta
    };
    
    if (stack) {
      log.stack = stack;
    }
    
    return JSON.stringify(log);
  })
);

// Create logger with multiple transports
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    
    // File transport with rotation
    new DailyRotateFile({
      filename: 'logs/app-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d'
    }),
    
    // Error-only file
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d'
    })
  ]
});

// Enhanced logging methods
logger.logRequest = (req, res, responseTime) => {
  logger.info('HTTP Request', {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id,
    requestId: req.requestId
  });
};

logger.logError = (error, context = {}) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    code: error.code,
    ...context
  });
};

logger.logSocketEvent = (eventType, socketId, userId, data = {}) => {
  logger.info('Socket Event', {
    eventType,
    socketId,
    userId,
    ...data
  });
};
```

#### Go Services (Logrus)
```go
package logging

import (
    "os"
    "time"
    
    "github.com/sirupsen/logrus"
    "gopkg.in/natefinch/lumberjack.v2"
)

type Logger struct {
    *logrus.Logger
}

func NewLogger(serviceName, version, environment string) *Logger {
    log := logrus.New()
    
    // Set log level
    level, err := logrus.ParseLevel(os.Getenv("LOG_LEVEL"))
    if err != nil {
        level = logrus.InfoLevel
    }
    log.SetLevel(level)
    
    // JSON formatter for structured logging
    log.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: time.RFC3339Nano,
        FieldMap: logrus.FieldMap{
            logrus.FieldKeyTime:  "timestamp",
            logrus.FieldKeyLevel: "level",
            logrus.FieldKeyMsg:   "message",
        },
    })
    
    // File output with rotation
    if os.Getenv("LOG_FILE_ENABLED") == "true" {
        log.SetOutput(&lumberjack.Logger{
            Filename:   "logs/app.log",
            MaxSize:    20, // MB
            MaxBackups: 14,
            MaxAge:     30, // days
            Compress:   true,
        })
    }
    
    // Add default fields
    log = log.WithFields(logrus.Fields{
        "service":     serviceName,
        "version":     version,
        "environment": environment,
    }).Logger
    
    return &Logger{log}
}

func (l *Logger) LogRequest(method, path string, statusCode int, duration time.Duration, userID string) {
    l.WithFields(logrus.Fields{
        "method":       method,
        "path":         path,
        "status_code":  statusCode,
        "duration_ms":  duration.Milliseconds(),
        "user_id":      userID,
        "request_type": "grpc",
    }).Info("Request processed")
}

func (l *Logger) LogGameEvent(eventType, roomID, userID string, data map[string]interface{}) {
    l.WithFields(logrus.Fields{
        "event_type": eventType,
        "room_id":    roomID,
        "user_id":    userID,
        "data":       data,
    }).Info("Game event")
}

func (l *Logger) LogError(err error, context map[string]interface{}) {
    l.WithFields(context).WithError(err).Error("Application error")
}
```

### Log Aggregation and Analysis

#### ELK Stack Configuration
```yaml
# Elasticsearch configuration
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    container_name: logstash
    ports:
      - "5044:5044"
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./logstash/config:/usr/share/logstash/config
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
```

#### Logstash Pipeline Configuration
```ruby
# logstash/pipeline/xzgame.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service_name" => "%{[fields][service]}" }
    }
  }
  
  # Parse JSON logs
  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
    }
  }
  
  # Parse timestamp
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601" ]
    }
  }
  
  # Extract user information
  if [userId] {
    mutate {
      add_field => { "user_id" => "%{userId}" }
    }
  }
  
  # Categorize log levels
  if [level] == "error" {
    mutate {
      add_tag => [ "error" ]
    }
  }
  
  # Extract request information
  if [method] and [url] {
    mutate {
      add_field => { "request_info" => "%{method} %{url}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "xzgame-logs-%{+YYYY.MM.dd}"
  }
  
  # Output errors to separate index
  if "error" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "xzgame-errors-%{+YYYY.MM.dd}"
    }
  }
}
```

## Metrics Collection (Prometheus)

### Prometheus Configuration

#### Prometheus Config
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # API Gateway metrics
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Socket Gateway metrics
  - job_name: 'socket-gateway'
    static_configs:
      - targets: ['socket-gateway:3001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Game Service metrics
  - job_name: 'game-service'
    static_configs:
      - targets: ['game-service:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Auth Service metrics
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Manager Service metrics
  - job_name: 'manager-service'
    static_configs:
      - targets: ['manager-service:3002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # MongoDB metrics
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb-exporter:9216']

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### Custom Metrics Implementation

#### Node.js Services Metrics
```javascript
const promClient = require('prom-client');

// Create a Registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({ register });

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeConnections = new promClient.Gauge({
  name: 'websocket_connections_active',
  help: 'Number of active WebSocket connections',
  labelNames: ['room_id']
});

const gameEvents = new promClient.Counter({
  name: 'game_events_total',
  help: 'Total number of game events',
  labelNames: ['event_type', 'game_type']
});

const roomPlayers = new promClient.Gauge({
  name: 'room_players_count',
  help: 'Number of players in rooms',
  labelNames: ['room_id', 'game_type']
});

// Register metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeConnections);
register.registerMetric(gameEvents);
register.registerMetric(roomPlayers);

// Middleware to collect HTTP metrics
const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });
  
  next();
};

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

// Helper functions to update metrics
const updateConnectionMetrics = (roomId, count) => {
  activeConnections.labels(roomId).set(count);
};

const recordGameEvent = (eventType, gameType) => {
  gameEvents.labels(eventType, gameType).inc();
};

const updateRoomPlayerCount = (roomId, gameType, count) => {
  roomPlayers.labels(roomId, gameType).set(count);
};
```

#### Go Services Metrics
```go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // HTTP metrics
    httpRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "Duration of HTTP requests in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint", "status"},
    )
    
    httpRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    // Game-specific metrics
    activeGames = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "games_active_total",
            Help: "Number of active games",
        },
        []string{"game_type"},
    )
    
    gameSessionDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "game_session_duration_seconds",
            Help: "Duration of game sessions",
            Buckets: []float64{10, 30, 60, 120, 300, 600, 1200},
        },
        []string{"game_type"},
    )
    
    roomOperations = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "room_operations_total",
            Help: "Total number of room operations",
        },
        []string{"operation", "status"},
    )
    
    // Database metrics
    dbOperationDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "db_operation_duration_seconds",
            Help: "Duration of database operations",
        },
        []string{"operation", "collection"},
    )
    
    redisOperationDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "redis_operation_duration_seconds",
            Help: "Duration of Redis operations",
        },
        []string{"operation"},
    )
)

// Helper functions
func RecordHTTPRequest(method, endpoint, status string, duration float64) {
    httpRequestDuration.WithLabelValues(method, endpoint, status).Observe(duration)
    httpRequestsTotal.WithLabelValues(method, endpoint, status).Inc()
}

func UpdateActiveGames(gameType string, count float64) {
    activeGames.WithLabelValues(gameType).Set(count)
}

func RecordGameSession(gameType string, duration float64) {
    gameSessionDuration.WithLabelValues(gameType).Observe(duration)
}

func RecordRoomOperation(operation, status string) {
    roomOperations.WithLabelValues(operation, status).Inc()
}

func RecordDBOperation(operation, collection string, duration float64) {
    dbOperationDuration.WithLabelValues(operation, collection).Observe(duration)
}

func RecordRedisOperation(operation string, duration float64) {
    redisOperationDuration.WithLabelValues(operation).Observe(duration)
}
```

## Error Handling Patterns

### Centralized Error Handling

#### Node.js Error Handler
```javascript
class AppError extends Error {
  constructor(message, statusCode, code = null, isOperational = true) {
    super(message);
    
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Global error handler middleware
const globalErrorHandler = (err, req, res, next) => {
  // Set default values
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  
  // Log error
  logger.logError(err, {
    requestId: req.requestId,
    userId: req.user?.id,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });
  
  // Update error metrics
  errorMetrics.labels(err.code || 'unknown', err.statusCode).inc();
  
  // Send error response
  if (process.env.NODE_ENV === 'development') {
    res.status(err.statusCode).json({
      success: false,
      error: {
        message: err.message,
        code: err.code,
        stack: err.stack,
        statusCode: err.statusCode
      },
      meta: {
        timestamp: err.timestamp,
        requestId: req.requestId
      }
    });
  } else {
    // Production error response
    const message = err.isOperational ? err.message : 'Something went wrong';
    
    res.status(err.statusCode).json({
      success: false,
      error: {
        message,
        code: err.code
      },
      meta: {
        timestamp: err.timestamp,
        requestId: req.requestId
      }
    });
  }
};

// Async error wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Unhandled rejection handler
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Uncaught exception handler
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});
```

#### Go Error Handling
```go
package errors

import (
    "fmt"
    "runtime"
    "time"
)

type AppError struct {
    Message    string            `json:"message"`
    Code       string            `json:"code"`
    StatusCode int               `json:"status_code"`
    Timestamp  time.Time         `json:"timestamp"`
    Context    map[string]interface{} `json:"context,omitempty"`
    Stack      string            `json:"stack,omitempty"`
}

func (e *AppError) Error() string {
    return e.Message
}

func NewAppError(message, code string, statusCode int) *AppError {
    _, file, line, _ := runtime.Caller(1)
    
    return &AppError{
        Message:    message,
        Code:       code,
        StatusCode: statusCode,
        Timestamp:  time.Now(),
        Stack:      fmt.Sprintf("%s:%d", file, line),
    }
}

func (e *AppError) WithContext(key string, value interface{}) *AppError {
    if e.Context == nil {
        e.Context = make(map[string]interface{})
    }
    e.Context[key] = value
    return e
}

// Error types
var (
    ErrUserNotFound     = NewAppError("User not found", "USER_NOT_FOUND", 404)
    ErrRoomNotFound     = NewAppError("Room not found", "ROOM_NOT_FOUND", 404)
    ErrRoomFull         = NewAppError("Room is full", "ROOM_FULL", 400)
    ErrInsufficientFunds = NewAppError("Insufficient funds", "INSUFFICIENT_FUNDS", 400)
    ErrGameInProgress   = NewAppError("Game is in progress", "GAME_IN_PROGRESS", 400)
    ErrInvalidGameState = NewAppError("Invalid game state", "INVALID_GAME_STATE", 400)
)

// Error handler middleware
func ErrorHandler(logger *logrus.Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err
            
            var appErr *AppError
            if e, ok := err.(*AppError); ok {
                appErr = e
            } else {
                appErr = NewAppError("Internal server error", "INTERNAL_ERROR", 500)
            }
            
            // Log error
            logger.WithFields(logrus.Fields{
                "error_code":   appErr.Code,
                "status_code":  appErr.StatusCode,
                "context":      appErr.Context,
                "stack":        appErr.Stack,
            }).Error(appErr.Message)
            
            // Update metrics
            errorMetrics.WithLabelValues(appErr.Code, fmt.Sprintf("%d", appErr.StatusCode)).Inc()
            
            c.JSON(appErr.StatusCode, gin.H{
                "success": false,
                "error": gin.H{
                    "message": appErr.Message,
                    "code":    appErr.Code,
                },
                "meta": gin.H{
                    "timestamp": appErr.Timestamp,
                },
            })
        }
    }
}
```

## Performance Monitoring

### Application Performance Monitoring (APM)

#### Performance Metrics Collection
```javascript
// Performance monitoring middleware
const performanceMonitoring = (req, res, next) => {
  const start = process.hrtime.bigint();
  
  res.on('finish', () => {
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    
    // Record performance metrics
    performanceMetrics.observe({
      method: req.method,
      route: req.route?.path || req.path,
      status: res.statusCode
    }, duration);
    
    // Alert on slow requests
    if (duration > 1000) { // > 1 second
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.url,
        duration: `${duration}ms`,
        userId: req.user?.id
      });
    }
  });
  
  next();
};
```

### Database Performance Monitoring

#### MongoDB Performance Tracking
```javascript
// MongoDB performance monitoring
mongoose.set('debug', (collectionName, method, query, doc) => {
  const start = Date.now();
  
  // Log slow queries
  if (Date.now() - start > 100) { // > 100ms
    logger.warn('Slow MongoDB query', {
      collection: collectionName,
      method,
      query,
      duration: `${Date.now() - start}ms`
    });
  }
  
  // Update metrics
  dbMetrics.labels(method, collectionName).observe((Date.now() - start) / 1000);
});
```
