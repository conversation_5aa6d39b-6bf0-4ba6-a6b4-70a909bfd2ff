# Real-time Features Documentation

**Status**: ✅ **Production Ready** - Complete event system with race condition prevention

## WebSocket Event System

The real-time communication system is built on Socket.io with Redis pub/sub for scalable event broadcasting. All real-time events flow through the Socket Gateway service.

### ✅ **Recent Optimizations**
- **Event Ordering**: `process.nextTick()` for proper event sequence
- **Race Condition Prevention**: Atomic operations with Redis transactions
- **Room Subscription Broadcasting**: All subscribed players receive updates
- **Connection Cleanup**: Automatic cleanup on disconnect
- **Error Handling**: Comprehensive error wrapping and logging

## WebSocket Connection Flow

### 1. Authentication
```javascript
// Client connects with JWT token
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'jwt_token_here'
  }
});

// Server validates token and establishes connection
socket.on('connect', () => {
  console.log('Connected to Socket Gateway');
});
```

### 2. Room Subscription
```javascript
// Subscribe to room events
socket.emit('subscribe_room', { roomId: 'room_123' }, (response) => {
  if (response.success) {
    console.log('Subscribed to room:', response.data.roomId);
  }
});
```

## Core WebSocket Events

### Connection Events

#### `connect`
Fired when client successfully connects to Socket Gateway.

#### `disconnect`
Fired when client disconnects from Socket Gateway.

#### `ping` / `pong`
Heartbeat mechanism for connection health monitoring.

### Room Management Events

#### `subscribe_room`
**Direction**: Client → Server
**Purpose**: Subscribe to room-specific events

**Payload**:
```javascript
{
  roomId: "string"
}
```

**Response**:
```javascript
{
  success: true,
  data: {
    roomId: "string",
    subscriptionId: "string"
  }
}
```

#### `unsubscribe_room`
**Direction**: Client → Server
**Purpose**: Unsubscribe from room events

#### `join_room`
**Direction**: Client → Server
**Purpose**: Join a game room

**Payload**:
```javascript
{
  roomId: "string",
  betAmount: 100.00
}
```

#### `leave_room`
**Direction**: Client → Server
**Purpose**: Leave a game room

### Game Events

#### `room_info_updated`
**Direction**: Server → Client
**Purpose**: Broadcast room state changes to all subscribed players

**Payload**:
```javascript
{
  room: {
    id: "string",
    name: "string",
    gameType: "prizewheel",
    status: "waiting",
    maxPlayers: 8,
    currentPlayers: 3
  },
  roomState: {
    playerCount: 3,
    readyCount: 1,
    canStartGame: false,
    gameInProgress: false
  },
  players: [
    {
      userId: "user1",
      username: "Player1",
      isReady: true,
      betAmount: 100.00,
      colorId: "red",
      colorName: "Red",
      colorHex: "#FF0000"
    }
  ],
  gameConfig: {
    minBet: 10.00,
    maxBet: 1000.00,
    autoStart: true
  },
  gameSpecificData: {
    // Prize Wheel specific data
    availableColors: [...],
    colorSelections: {...}
  },
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

#### `player_ready_changed`
**Direction**: Server → Client
**Purpose**: Notify when a player's ready status changes

**Payload**:
```javascript
{
  roomId: "string",
  userId: "string",
  username: "string",
  isReady: true,
  readyCount: 2,
  canStartGame: false
}
```

#### `game_starting`
**Direction**: Server → Client
**Purpose**: Notify that game is about to start

#### `countdown_update`
**Direction**: Server → Client
**Purpose**: Countdown timer updates

**Payload**:
```javascript
{
  roomId: "string",
  countdown: 5,
  message: "Game starting in 5 seconds..."
}
```

#### `game_started`
**Direction**: Server → Client
**Purpose**: Game has officially begun

#### `game_finished`
**Direction**: Server → Client
**Purpose**: Game completed with results

**Payload**:
```javascript
{
  roomId: "string",
  gameId: "string",
  results: {
    winner: {
      userId: "string",
      username: "string",
      winAmount: 800.00
    },
    participants: [...],
    gameData: {...}
  }
}
```

### Prize Wheel Specific Events

#### `color_selected`
**Direction**: Client → Server
**Purpose**: Player selects a color

**Payload**:
```javascript
{
  roomId: "string",
  colorId: "red"
}
```

#### `color_unselected`
**Direction**: Client → Server
**Purpose**: Player unselects their color

#### `color_state_sync`
**Direction**: Server → Client
**Purpose**: Synchronize color selection state

#### `room_color_state_updated`
**Direction**: Server → Client
**Purpose**: Broadcast color state changes to all players

**Payload**:
```javascript
{
  roomId: "string",
  selectorUserId: "string",
  selectorUsername: "string",
  selectedColorId: "red",
  playerColors: {
    "user1": {
      "colorId": "red",
      "username": "Player1",
      "isReady": true,
      "colorName": "Red",
      "colorHex": "#ef4444"
    }
  },
  availableColors: [
    {
      "id": "blue",
      "name": "Blue",
      "hex": "#3b82f6",
      "isAvailable": true
    }
  ],
  totalPlayers: 3,
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

#### `wheel_spinning`
**Direction**: Server → Client
**Purpose**: Wheel animation started

**Payload**:
```javascript
{
  gameId: "string",
  animation: {
    duration: 5000,
    finalPosition: 180,
    rotations: 5
  },
  participants: [...],
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

#### `wheel_stopped`
**Direction**: Server → Client
**Purpose**: Wheel animation completed

**Payload**:
```javascript
{
  gameId: "string",
  finalPosition: 180,
  winningColor: "red",
  winnerPosition: 1,
  winner: {
    userId: "string",
    username: "string",
    winAmount: 800.00
  },
  results: {...}
}
```

### Balance Events

#### `balance_updated`
**Direction**: Server → Client
**Purpose**: User balance changed

**Payload**:
```javascript
{
  userId: "string",
  newBalance: 1200.00,
  previousBalance: 1000.00,
  change: 200.00,
  reason: "game_win",
  transactionId: "string"
}
```

## Redis Pub/Sub Patterns

### Channel Naming Convention
- `room:{roomId}:events` - Room-specific events
- `user:{userId}:notifications` - User-specific notifications
- `game:global` - Global game events
- `admin:notifications` - Administrative notifications
- `socket:events` - Game service to socket gateway events

### Event Publishing (Game Service)
```go
// Publish room event to Redis
channel := fmt.Sprintf("room:%s:events", roomID)
eventData := map[string]interface{}{
  "event": map[string]interface{}{
    "type": "room_info_updated",
    "payload": roomData,
  },
  "metadata": map[string]interface{}{
    "serviceId": "game-service",
    "version": "1.0.0",
    "timestamp": time.Now().Format(time.RFC3339),
  },
}
redisClient.Publish(ctx, channel, eventData)
```

### Event Subscription (Socket Gateway)
```javascript
// Subscribe to room events pattern
redisClient.pSubscribe('room:*:events', (message, channel) => {
  const roomId = channel.split(':')[1];
  const eventData = JSON.parse(message);
  
  // Broadcast to all clients in the room
  io.to(`room:${roomId}`).emit(eventData.event.type, eventData.event.payload);
});
```

## Event Ordering and Synchronization

### Critical Event Ordering
The system implements specific ordering mechanisms to prevent race conditions:

1. **Room Join Flow**:
   ```
   join_room_response → subscribe_room_response → room_info_updated
   ```

2. **Color Selection Flow**:
   ```
   color_selected → color_state_sync → room_color_state_updated
   ```

3. **Game Start Flow**:
   ```
   game_starting → countdown_update → game_started
   ```

### Synchronization Mechanisms

#### Process.nextTick() for Event Ordering
```javascript
// Ensure proper event ordering in Socket Gateway
socket.emit('subscribe_room_response', response);
process.nextTick(() => {
  // Broadcast room info after response is sent
  this.requestGameServiceRoomInfoBroadcast(roomId, userId, username);
});
```

#### Atomic Operations with Mutexes
```go
// Prevent race conditions in Game Service
roomMutex := s.getRoomMutex(roomID)
roomMutex.Lock()
defer roomMutex.Unlock()

// Perform atomic room operations
room, err := s.atomicJoinRoom(ctx, request)
```

#### Redis Transactions for State Consistency
```go
// Atomic color selection with Redis
pipe := redisClient.TxPipeline()
pipe.HSet(ctx, colorKey, playerID, colorData)
pipe.SAdd(ctx, availableColorsKey, colorID)
_, err := pipe.Exec(ctx)
```

## Error Handling and Recovery

### Connection Recovery
```javascript
socket.on('disconnect', (reason) => {
  if (reason === 'io server disconnect') {
    // Server initiated disconnect, don't reconnect
    return;
  }
  
  // Automatic reconnection with exponential backoff
  setTimeout(() => {
    socket.connect();
  }, Math.min(1000 * Math.pow(2, reconnectAttempts), 30000));
});
```

### Event Delivery Guarantees
- **At-least-once delivery** for critical events
- **Idempotent event handlers** to handle duplicates
- **Event replay** capability for missed events during disconnection
