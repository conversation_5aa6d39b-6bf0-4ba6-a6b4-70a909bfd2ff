# Configuration Management

**Status**: ✅ **Updated** - All environment configurations standardized and consistent

## Environment Configuration Overview

All services use environment variables for configuration. Each service has its own `.env` file with service-specific settings that follow the Makefile configuration standards.

### ✅ **Recent Updates (December 2024)**
- **Standardized Configuration**: All `.env` files updated to match Makefile
- **Consistent Database URLs**: MongoDB with `admin:password` credentials
- **Unified Redis Configuration**: Standard Redis URL without password
- **JWT Secret Consistency**: Same JWT secret across all services
- **Port Standardization**: Consistent port assignments
- **CORS Configuration**: Updated to include all service ports

### 🔧 **Common Configuration**
```bash
# Database
MONGODB_URL=****************************************************************

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=dev_jwt_secret_key_change_in_production
```

### 📋 **Service Port Assignments**
- **API Gateway**: 3000
- **Socket Gateway**: 3001
- **Manager Service**: 3002
- **Dashboard Service**: 3004
- **Game Service**: 8080
- **Auth Service**: 8081
- **Room Service**: 8082
- **Notification Service**: 8083
- **Game Engine Service**: 8084

## Environment Variables

### API Gateway Service

#### Core Configuration
```bash
# Application
NODE_ENV=development|production
PORT=3000

# Database
MONGODB_URL=mongodb://localhost:27017/xzgame

# Redis
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=xzgame-api-gateway
JWT_AUDIENCE=api-gateway,socket-gateway

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# gRPC Services
GAME_SERVICE_URL=localhost:50051
MANAGER_SERVICE_URL=localhost:50052

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILENAME=logs/api-gateway.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
```

### Socket Gateway Service

#### Core Configuration
```bash
# Application
NODE_ENV=development|production
PORT=3001

# Database
MONGODB_URL=mongodb://localhost:27017/xzgame

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_ISSUER=xzgame-socket-gateway
JWT_AUDIENCE=socket-gateway

# Socket.io Configuration
SOCKET_CORS_ORIGINS=http://localhost:3000,http://localhost:3001
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Game Service Integration
GAME_SERVICE_URL=http://game-service:8080
GAME_SERVICE_GRPC_URL=game-service:50051

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILENAME=logs/socket-gateway.log

# Debug Options
DEBUG_REDIS_LOGS=false
DEBUG_SOCKET_EVENTS=false
```

### Game Service (Go)

#### Core Configuration
```bash
# Server Configuration
GO_ENV=development|production
PORT=8080
METRICS_PORT=9090
READ_TIMEOUT=30s
WRITE_TIMEOUT=30s

# Database Configuration
MONGODB_URL=mongodb://localhost:27017/xzgame
REDIS_URL=redis://localhost:6379
DATABASE_NAME=xzgame

# Security Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
RANDOM_SEED_SECRET=your_random_seed_secret_change_in_production
ENCRYPTION_KEY=your_encryption_key_here_change_in_production

# Game Configuration
MAX_CONCURRENT_GAMES=1000
DEFAULT_TIMEOUT=60s
MAX_PLAYERS_PER_ROOM=8
SESSION_CLEANUP_INTERVAL=5m
ARCHIVE_COMPLETED_AFTER=24h

# Performance Configuration
WORKER_POOL_SIZE=10
MAX_QUEUE_SIZE=1000
BATCH_SIZE=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
```

### Auth Service (Go)

#### Core Configuration
```bash
# Server
PORT=8081
ENVIRONMENT=development|production

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_ISSUER=xzgame-auth-service
JWT_AUDIENCE=xzgame-api,xzgame-game-service
SESSION_TTL=24h
TOKEN_BLACKLIST_TTL=24h
MAX_SESSIONS_PER_USER=5

# Logging
LOG_LEVEL=info
```

### Manager Service (Ruby)

#### Core Configuration
```bash
# Rails Environment
RAILS_ENV=development|production
PORT=3002

# Database
DATABASE_URL=mongodb://localhost:27017/xzgame
REDIS_URL=redis://localhost:6379

# External Services
GAME_SERVICE_URL=http://game-service:8080
NOTIFICATION_SERVICE_URL=http://notification-service:8083

# Security
SECRET_KEY_BASE=your_secret_key_base_here
JWT_SECRET=your_jwt_secret_here

# Background Jobs
SIDEKIQ_REDIS_URL=redis://localhost:6379/1
SIDEKIQ_CONCURRENCY=5

# Logging
LOG_LEVEL=info
LOG_TO_STDOUT=true
```

### Dashboard Service (Next.js)

#### Core Configuration
```bash
# Application
NODE_ENV=development|production
PORT=3003
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3003

# API Integration
API_GATEWAY_URL=http://api-gateway:3000
MANAGER_SERVICE_URL=http://manager-service:3002

# Features
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true
```

## Service Configuration Patterns

### Configuration Loading Priority

1. **Environment Variables** (highest priority)
2. **Configuration Files** (.env, config.json)
3. **Default Values** (lowest priority)

#### Node.js Services Configuration Loading
```javascript
const config = {
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT) || 3000,
  
  mongodb: {
    url: process.env.MONGODB_URL || 'mongodb://localhost:27017/xzgame',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE) || 10,
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_TIMEOUT) || 5000,
    }
  },
  
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    options: {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    }
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    issuer: process.env.JWT_ISSUER || 'xzgame',
    audience: (process.env.JWT_AUDIENCE || '').split(',').filter(Boolean),
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      filename: process.env.LOG_FILENAME || 'logs/app.log',
      maxSize: process.env.LOG_MAX_SIZE || '20m',
      maxFiles: process.env.LOG_MAX_FILES || '14d',
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
      colorize: process.env.LOG_COLORIZE !== 'false',
    }
  }
};
```

#### Go Services Configuration Loading
```go
type Config struct {
    Environment string `env:"GO_ENV" envDefault:"development"`
    Port        int    `env:"PORT" envDefault:"8080"`
    
    MongoDB struct {
        URL      string `env:"MONGODB_URL" envDefault:"mongodb://localhost:27017/xzgame"`
        Database string `env:"DATABASE_NAME" envDefault:"xzgame"`
        Timeout  int    `env:"MONGODB_TIMEOUT" envDefault:"5"`
    }
    
    Redis struct {
        URL      string `env:"REDIS_URL" envDefault:"redis://localhost:6379"`
        Password string `env:"REDIS_PASSWORD"`
        DB       int    `env:"REDIS_DB" envDefault:"0"`
    }
    
    JWT struct {
        Secret   string `env:"JWT_SECRET" envDefault:"default-secret"`
        Issuer   string `env:"JWT_ISSUER" envDefault:"xzgame"`
        Audience string `env:"JWT_AUDIENCE" envDefault:"xzgame"`
    }
    
    Game struct {
        MaxConcurrentGames int           `env:"MAX_CONCURRENT_GAMES" envDefault:"1000"`
        DefaultTimeout     time.Duration `env:"DEFAULT_TIMEOUT" envDefault:"60s"`
        MaxPlayersPerRoom  int           `env:"MAX_PLAYERS_PER_ROOM" envDefault:"8"`
    }
    
    Logging struct {
        Level  string `env:"LOG_LEVEL" envDefault:"info"`
        Format string `env:"LOG_FORMAT" envDefault:"json"`
        Output string `env:"LOG_OUTPUT" envDefault:"stdout"`
    }
}

func LoadConfig() (*Config, error) {
    cfg := &Config{}
    if err := env.Parse(cfg); err != nil {
        return nil, fmt.Errorf("failed to parse config: %w", err)
    }
    return cfg, nil
}
```

## Security Configurations

### JWT Configuration Best Practices

#### Production JWT Settings
```bash
# Use strong, randomly generated secrets (minimum 32 characters)
JWT_SECRET=$(openssl rand -base64 32)

# Set appropriate expiration times
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Configure proper issuer and audience
JWT_ISSUER=xzgame-production
JWT_AUDIENCE=api-gateway,socket-gateway,game-service

# Enable token blacklisting
JWT_BLACKLIST_ENABLED=true
JWT_BLACKLIST_TTL=24h
```

#### Redis Security Configuration
```bash
# Use password authentication
REDIS_PASSWORD=your_strong_redis_password

# Use dedicated databases for different purposes
REDIS_SESSION_DB=0
REDIS_CACHE_DB=1
REDIS_PUBSUB_DB=2

# Configure connection limits
REDIS_MAX_CONNECTIONS=100
REDIS_CONNECTION_TIMEOUT=5s
```

### Database Security

#### MongoDB Security Settings
```bash
# Use authentication
MONGODB_URL=*******************************************************************

# Enable SSL/TLS in production
MONGODB_SSL_ENABLED=true
MONGODB_SSL_CERT_PATH=/path/to/cert.pem

# Connection pool limits
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2
MONGODB_MAX_IDLE_TIME=30s
```

## Feature Flags

### Feature Flag Configuration
```bash
# Game Features
FEATURE_PRIZE_WHEEL_ENABLED=true
FEATURE_AMIDAKUJI_ENABLED=true
FEATURE_SPECTATOR_MODE_ENABLED=false

# Real-time Features
FEATURE_REAL_TIME_NOTIFICATIONS=true
FEATURE_ROOM_CHAT_ENABLED=true
FEATURE_PLAYER_PRESENCE=true

# Administrative Features
FEATURE_ADMIN_DASHBOARD=true
FEATURE_USER_ANALYTICS=true
FEATURE_TRANSACTION_MONITORING=true

# Performance Features
FEATURE_CACHING_ENABLED=true
FEATURE_RATE_LIMITING=true
FEATURE_METRICS_COLLECTION=true

# Debug Features (development only)
FEATURE_DEBUG_LOGGING=false
FEATURE_MOCK_PAYMENTS=false
FEATURE_TEST_USERS=false
```

### Feature Flag Implementation
```javascript
class FeatureFlags {
  constructor() {
    this.flags = {
      prizeWheelEnabled: process.env.FEATURE_PRIZE_WHEEL_ENABLED === 'true',
      amidakujiEnabled: process.env.FEATURE_AMIDAKUJI_ENABLED === 'true',
      spectatorMode: process.env.FEATURE_SPECTATOR_MODE_ENABLED === 'true',
      realTimeNotifications: process.env.FEATURE_REAL_TIME_NOTIFICATIONS === 'true',
      adminDashboard: process.env.FEATURE_ADMIN_DASHBOARD === 'true',
      cachingEnabled: process.env.FEATURE_CACHING_ENABLED === 'true',
    };
  }
  
  isEnabled(flagName) {
    return this.flags[flagName] || false;
  }
  
  requireFeature(flagName) {
    if (!this.isEnabled(flagName)) {
      throw new Error(`Feature ${flagName} is not enabled`);
    }
  }
}

// Usage
const featureFlags = new FeatureFlags();

if (featureFlags.isEnabled('prizeWheelEnabled')) {
  // Enable Prize Wheel game routes
  app.use('/api/v1/games/prizewheel', prizeWheelRoutes);
}
```

## Environment-Specific Configurations

### Development Environment
```bash
# Relaxed security for development
JWT_SECRET=dev-secret-not-for-production
CORS_ORIGINS=*
RATE_LIMIT_MAX=1000

# Enhanced logging
LOG_LEVEL=debug
DEBUG_REDIS_LOGS=true
DEBUG_SOCKET_EVENTS=true

# Local services
MONGODB_URL=mongodb://localhost:27017/xzgame_dev
REDIS_URL=redis://localhost:6379

# Feature flags for testing
FEATURE_MOCK_PAYMENTS=true
FEATURE_TEST_USERS=true
```

### Production Environment
```bash
# Strong security
JWT_SECRET=${PRODUCTION_JWT_SECRET}
CORS_ORIGINS=https://xzgame.com,https://admin.xzgame.com
RATE_LIMIT_MAX=100

# Production logging
LOG_LEVEL=info
DEBUG_REDIS_LOGS=false
DEBUG_SOCKET_EVENTS=false

# Production services
MONGODB_URL=${PRODUCTION_MONGODB_URL}
REDIS_URL=${PRODUCTION_REDIS_URL}

# Production features only
FEATURE_MOCK_PAYMENTS=false
FEATURE_TEST_USERS=false
FEATURE_DEBUG_LOGGING=false
```

### Staging Environment
```bash
# Production-like security with some debug features
JWT_SECRET=${STAGING_JWT_SECRET}
CORS_ORIGINS=https://staging.xzgame.com
RATE_LIMIT_MAX=200

# Enhanced logging for testing
LOG_LEVEL=debug
DEBUG_REDIS_LOGS=false
DEBUG_SOCKET_EVENTS=true

# Staging services
MONGODB_URL=${STAGING_MONGODB_URL}
REDIS_URL=${STAGING_REDIS_URL}

# Mixed feature flags
FEATURE_MOCK_PAYMENTS=true
FEATURE_TEST_USERS=true
FEATURE_DEBUG_LOGGING=true
```

## Configuration Validation

### Environment Variable Validation
```javascript
const Joi = require('joi');

const configSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'staging', 'production').required(),
  PORT: Joi.number().port().required(),
  MONGODB_URL: Joi.string().uri().required(),
  REDIS_URL: Joi.string().uri().required(),
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_EXPIRES_IN: Joi.string().pattern(/^\d+[smhd]$/).required(),
  CORS_ORIGINS: Joi.string().required(),
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').required(),
});

const { error, value } = configSchema.validate(process.env, {
  allowUnknown: true,
  stripUnknown: false,
});

if (error) {
  throw new Error(`Configuration validation error: ${error.message}`);
}
```

### Runtime Configuration Checks
```javascript
class ConfigValidator {
  static validateDatabaseConnection(config) {
    // Test MongoDB connection
    return mongoose.connect(config.mongodb.url, config.mongodb.options);
  }
  
  static validateRedisConnection(config) {
    // Test Redis connection
    const redis = new Redis(config.redis.url);
    return redis.ping();
  }
  
  static validateExternalServices(config) {
    // Test gRPC service connections
    const promises = [];
    
    if (config.gameService.url) {
      promises.push(this.testGrpcConnection(config.gameService.url));
    }
    
    return Promise.all(promises);
  }
  
  static async validateAll(config) {
    try {
      await Promise.all([
        this.validateDatabaseConnection(config),
        this.validateRedisConnection(config),
        this.validateExternalServices(config),
      ]);
      
      console.log('✅ All configuration validations passed');
    } catch (error) {
      console.error('❌ Configuration validation failed:', error.message);
      process.exit(1);
    }
  }
}
```
