# Game-Specific Features

## Prize Wheel Game

### Overview
The Prize Wheel is a multiplayer game where players select colors and bet on the outcome of a spinning wheel. The player whose color the wheel lands on wins the entire prize pool.

### Color Selection System

#### Available Colors
```javascript
const WHEEL_COLORS = [
  { id: "red", name: "Red", hex: "#ef4444" },
  { id: "blue", name: "Blue", hex: "#3b82f6" },
  { id: "green", name: "Green", hex: "#10b981" },
  { id: "yellow", name: "Yellow", hex: "#f59e0b" },
  { id: "purple", name: "<PERSON>", hex: "#8b5cf6" },
  { id: "orange", name: "Orange", hex: "#f97316" },
  { id: "pink", name: "Pink", hex: "#ec4899" },
  { id: "cyan", name: "<PERSON><PERSON>", hex: "#06b6d4" }
];
```

#### Color Selection Flow
1. **Player Joins Room**: Automatically assigned to room without color
2. **Color Selection**: Player chooses available color
3. **Color Validation**: System validates color availability
4. **State Synchronization**: All players receive color state update
5. **Ready Status**: Player can mark ready after color selection

#### Color Selection Implementation

**Client Side**:
```javascript
// Select a color
socket.emit('select_color', {
  roomId: 'room_123',
  colorId: 'red'
}, (response) => {
  if (response.success) {
    updateColorUI(response.data.selectedColor);
  }
});

// Listen for color state updates
socket.on('room_color_state_updated', (data) => {
  updateColorSelectionUI(data.playerColors, data.availableColors);
});
```

**Server Side (Game Service)**:
```go
func (s *PrizeWheelService) SelectColor(ctx context.Context, roomID, userID, colorID string) error {
    // Atomic color selection with Redis
    colorKey := fmt.Sprintf("room:%s:colors", roomID)
    
    // Check if color is available
    isAvailable, err := s.redisClient.SIsMember(ctx, 
        fmt.Sprintf("room:%s:available_colors", roomID), colorID).Result()
    if err != nil || !isAvailable {
        return errors.New("color not available")
    }
    
    // Atomic operation: assign color and remove from available
    pipe := s.redisClient.TxPipeline()
    pipe.HSet(ctx, colorKey, userID, colorID)
    pipe.SRem(ctx, fmt.Sprintf("room:%s:available_colors", roomID), colorID)
    _, err = pipe.Exec(ctx)
    
    if err == nil {
        // Broadcast color state update
        s.broadcastColorStateUpdate(ctx, roomID)
    }
    
    return err
}
```

#### Color Release Mechanisms

**Automatic Release on Player Leave**:
```go
func (s *PrizeWheelService) HandlePlayerLeave(ctx context.Context, roomID, userID string) error {
    // Get player's selected color
    colorKey := fmt.Sprintf("room:%s:colors", roomID)
    selectedColor, err := s.redisClient.HGet(ctx, colorKey, userID).Result()
    
    if err == nil && selectedColor != "" {
        // Release color back to available pool
        pipe := s.redisClient.TxPipeline()
        pipe.HDel(ctx, colorKey, userID)
        pipe.SAdd(ctx, fmt.Sprintf("room:%s:available_colors", roomID), selectedColor)
        pipe.Exec(ctx)
        
        // Broadcast updated color state
        s.broadcastColorStateUpdate(ctx, roomID)
    }
    
    return nil
}
```

**Manual Color Unselection**:
```javascript
// Client unselects color
socket.emit('unselect_color', {
  roomId: 'room_123'
}, (response) => {
  if (response.success) {
    clearSelectedColor();
  }
});
```

### Game Flow

#### 1. Room Setup
- Room created with Prize Wheel configuration
- Color pool initialized with available colors
- Betting limits and player capacity set

#### 2. Player Joining
```javascript
// Complete join flow
socket.emit('join_room', {
  roomId: 'room_123',
  betAmount: 100.00
}, (response) => {
  if (response.success) {
    // Player successfully joined
    socket.emit('subscribe_room', { roomId: 'room_123' });
  }
});
```

#### 3. Color Selection Phase
- Players select unique colors
- Real-time color availability updates
- Visual feedback for selection conflicts

#### 4. Ready Phase
```javascript
// Player marks ready
socket.emit('player_ready', {
  roomId: 'room_123'
}, (response) => {
  if (response.success) {
    console.log('Player marked as ready');
  }
});
```

#### 5. Game Start Conditions
- Minimum players reached (configurable)
- All players have selected colors
- All players marked as ready
- OR auto-start timer expires

#### 6. Wheel Spinning
```javascript
// Wheel spinning animation
socket.on('wheel_spinning', (data) => {
  startWheelAnimation({
    duration: data.animation.duration,
    finalPosition: data.animation.finalPosition,
    rotations: data.animation.rotations
  });
});
```

#### 7. Result Determination
```go
func (s *PrizeWheelService) DetermineWinner(ctx context.Context, gameSession *models.GameSession) (*models.GameResult, error) {
    // Generate cryptographically secure random number
    randomBytes := make([]byte, 8)
    _, err := rand.Read(randomBytes)
    if err != nil {
        return nil, err
    }
    
    // Convert to wheel position (0-360 degrees)
    randomValue := binary.BigEndian.Uint64(randomBytes)
    wheelPosition := float64(randomValue%360000) / 1000.0
    
    // Determine winning color based on wheel segments
    winningColor := s.calculateWinningColor(wheelPosition, gameSession.Players)
    
    // Calculate payouts
    totalPrizePool := s.calculateTotalBets(gameSession.Players)
    
    return &models.GameResult{
        WinningColor: winningColor,
        WheelPosition: wheelPosition,
        PrizePool: totalPrizePool,
        Winner: s.getPlayerByColor(gameSession.Players, winningColor),
    }, nil
}
```

## Room Subscription/Unsubscription Mechanics

### Subscription Flow

#### Enhanced Subscription with Broadcasting
```javascript
async handleSubscribeRoom(socket, data, callback) {
  const { roomId } = data;
  const { userId, username } = socket;
  
  // Process subscription
  const result = await this.subscriptionService.subscribeRoom(userId, roomId);
  
  if (result.success) {
    // Join Socket.io room
    socket.join(`room:${roomId}`);
    
    // Send immediate response
    callback({
      success: true,
      data: { roomId, subscriptionId: result.subscriptionId }
    });
    
    // Broadcast room info to all players (including new subscriber)
    process.nextTick(() => {
      this.requestGameServiceRoomInfoBroadcast(roomId, userId, username, 'player_subscribed');
    });
  }
}
```

#### Game Service Room Info Broadcasting
```go
func (h *RequestHandler) handleRoomInfoBroadcastWithColorState(ctx context.Context, request map[string]interface{}) error {
    roomID := request["roomId"].(string)
    
    // Get current room state with color information
    room, err := h.roomService.GetRoomWithColorState(ctx, roomID)
    if err != nil {
        return err
    }
    
    // Build comprehensive room info event
    event := h.buildRoomInfoUpdatedEvent(room)
    
    // Publish to all subscribed players
    channel := fmt.Sprintf("room:%s:events", roomID)
    return h.redisClient.Publish(ctx, channel, event)
}
```

### Unsubscription Flow

#### Graceful Unsubscription
```javascript
async handleUnsubscribeRoom(socket, data, callback) {
  const { roomId } = data;
  const { userId } = socket;
  
  // Process unsubscription
  const result = await this.subscriptionService.unsubscribeRoom(userId, roomId);
  
  if (result.success) {
    // Leave Socket.io room
    socket.leave(`room:${roomId}`);
    
    // Notify remaining players
    this.broadcastRoomInfoToAllSubscribers(roomId, userId, 'player_unsubscribed');
    
    callback({ success: true });
  }
}
```

### Player Join/Leave Workflows

#### Complete Join Workflow
```
1. Client → API Gateway: POST /api/v1/rooms/:id/join
2. API Gateway → Game Service: JoinRoom gRPC call
3. Game Service: Validate player balance and room capacity
4. Game Service: Add player to room in MongoDB
5. Game Service → Redis: Publish room_info_updated event
6. Socket Gateway ← Redis: Receive room update
7. Socket Gateway → All Clients: Broadcast room_info_updated
8. Client: Update UI with new player information
```

#### Complete Leave Workflow
```
1. Client → Socket Gateway: leave_room event
2. Socket Gateway → Game Service: LeaveRoom gRPC call
3. Game Service: Remove player from room
4. Game Service: Release player's color (if Prize Wheel)
5. Game Service: Update room state in MongoDB
6. Game Service → Redis: Publish room_info_updated event
7. Socket Gateway ← Redis: Receive room update
8. Socket Gateway → All Clients: Broadcast updated room state
```

## Event Ordering and Race Condition Prevention

### Critical Event Sequences

#### Room Subscription Event Order
```
1. subscribe_room_response (to subscribing player)
2. room_info_updated (to all players including subscriber)
```

**Implementation**:
```javascript
// Send response first
callback({ success: true, data: subscriptionData });

// Then broadcast to all players
process.nextTick(() => {
  this.requestGameServiceRoomInfoBroadcast(roomId, userId, username);
});
```

#### Color Selection Event Order
```
1. color_selected (acknowledgment to selector)
2. color_state_sync (immediate state to selector)
3. room_color_state_updated (broadcast to all players)
```

### Race Condition Prevention Mechanisms

#### Atomic Room Operations
```go
type AtomicRoomService struct {
    roomMutexes sync.Map // map[string]*sync.RWMutex
}

func (s *AtomicRoomService) AtomicJoinRoom(ctx context.Context, request models.JoinRoomRequest) (*models.Room, error) {
    roomMutex := s.getRoomMutex(request.RoomID)
    roomMutex.Lock()
    defer roomMutex.Unlock()
    
    // Perform atomic room join operations
    return s.performJoinOperations(ctx, request)
}
```

#### Redis Transactions for Color State
```go
func (s *ColorService) AtomicColorSelection(ctx context.Context, roomID, userID, colorID string) error {
    // Use Redis WATCH for optimistic locking
    err := s.redisClient.Watch(ctx, func(tx *redis.Tx) error {
        // Check color availability
        available, err := tx.SIsMember(ctx, availableColorsKey, colorID).Result()
        if err != nil || !available {
            return errors.New("color not available")
        }
        
        // Atomic transaction
        _, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
            pipe.HSet(ctx, playerColorsKey, userID, colorID)
            pipe.SRem(ctx, availableColorsKey, colorID)
            return nil
        })
        
        return err
    }, availableColorsKey)
    
    return err
}
```

#### Event Deduplication
```javascript
class EventDeduplicator {
  constructor() {
    this.processedEvents = new Map();
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }
  
  isProcessed(eventId) {
    return this.processedEvents.has(eventId);
  }
  
  markProcessed(eventId) {
    this.processedEvents.set(eventId, Date.now());
  }
  
  cleanup() {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    
    for (const [eventId, timestamp] of this.processedEvents) {
      if (now - timestamp > maxAge) {
        this.processedEvents.delete(eventId);
      }
    }
  }
}
```

## Amidakuji (Ghost Leg) Game

### Overview
Amidakuji is a traditional Japanese lottery game where players select starting positions and follow paths down a ladder-like structure to determine the winner.

### Game Mechanics

#### Position Selection
```javascript
// Player selects starting position
socket.emit('select_position', {
  roomId: 'room_123',
  position: 1
}, (response) => {
  if (response.success) {
    console.log('Position selected:', response.data.position);
  }
});
```

#### Pattern Generation
```go
func (s *AmidakujiService) GeneratePattern(ctx context.Context, playerCount int) (*AmidakujiPattern, error) {
    // Generate cryptographically secure random pattern
    pattern := &AmidakujiPattern{
        PlayerCount: playerCount,
        Levels: make([][]bool, s.config.LevelCount),
    }
    
    for level := 0; level < s.config.LevelCount; level++ {
        pattern.Levels[level] = s.generateLevelConnections(playerCount)
    }
    
    return pattern, nil
}
```

#### Path Tracing
```go
func (s *AmidakujiService) TracePath(pattern *AmidakujiPattern, startPosition int) int {
    currentPosition := startPosition
    
    for level := 0; level < len(pattern.Levels); level++ {
        connections := pattern.Levels[level]
        
        // Check for horizontal connection to the right
        if currentPosition < len(connections) && connections[currentPosition] {
            currentPosition++
        } else if currentPosition > 0 && connections[currentPosition-1] {
            // Check for horizontal connection to the left
            currentPosition--
        }
        // Otherwise, continue straight down
    }
    
    return currentPosition
}
```
