# Infrastructure and Deployment

## Docker Containerization

### Service Dockerfiles

#### Node.js Services (API Gateway, Socket Gateway)

**Development Dockerfile**:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["yarn", "dev"]
```

**Production Dockerfile**:
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json yarn.lock ./
RUN yarn install --frozen-lockfile --production=false
COPY . .
RUN yarn build

FROM node:18-alpine AS production

WORKDIR /app
COPY package*.json yarn.lock ./
RUN yarn install --frozen-lockfile --production=true && yarn cache clean

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
RUN mkdir -p logs && chown nodejs:nodejs logs

USER nodejs

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

CMD ["node", "dist/app.js"]
```

#### Go Services (Game Service, Auth Service, etc.)

**Multi-stage Dockerfile**:
```dockerfile
# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/server

# Production stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

# Copy binary from builder
COPY --from=builder /app/main .

# Create non-root user
RUN adduser -D -s /bin/sh appuser
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run binary
CMD ["./main"]
```

#### Ruby Service (Manager Service)

**Dockerfile**:
```dockerfile
FROM ruby:3.1-alpine

WORKDIR /app

# Install dependencies
RUN apk add --no-cache \
  build-base \
  postgresql-dev \
  git \
  tzdata

# Copy Gemfile
COPY Gemfile Gemfile.lock ./

# Install gems
RUN bundle config --global frozen 1 && \
    bundle install --without development test

# Copy application code
COPY . .

# Create non-root user
RUN adduser -D -s /bin/sh appuser && \
    chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Start application
CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]
```

### Docker Compose Configuration

#### Development Environment
```yaml
version: '3.8'

services:
  # Databases
  mongodb:
    image: mongo:6.0
    container_name: xzgame-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: xzgame
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - xzgame-network

  redis:
    image: redis:7-alpine
    container_name: xzgame-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redispassword
    volumes:
      - redis_data:/data
    networks:
      - xzgame-network

  # Core Services
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile.dev
    container_name: xzgame-api-gateway
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      MONGODB_URL: **************************************************************
      REDIS_URL: redis://:redispassword@redis:6379
      GAME_SERVICE_URL: game-service:50051
      MANAGER_SERVICE_URL: manager-service:50052
    depends_on:
      - mongodb
      - redis
      - game-service
      - manager-service
    volumes:
      - ./api-gateway:/app
      - /app/node_modules
    networks:
      - xzgame-network

  socket-gateway:
    build:
      context: ./socket-gateway
      dockerfile: Dockerfile.dev
    container_name: xzgame-socket-gateway
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      MONGODB_URL: **************************************************************
      REDIS_URL: redis://:redispassword@redis:6379
      GAME_SERVICE_URL: game-service:8080
    depends_on:
      - mongodb
      - redis
      - game-service
    volumes:
      - ./socket-gateway:/app
      - /app/node_modules
    networks:
      - xzgame-network

  game-service:
    build:
      context: ./game-service
      dockerfile: Dockerfile.dev
    container_name: xzgame-game-service
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "50051:50051"
    environment:
      GO_ENV: development
      MONGODB_URL: **************************************************************
      REDIS_URL: redis://:redispassword@redis:6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./game-service:/app
    networks:
      - xzgame-network

  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: xzgame-auth-service
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ENVIRONMENT: development
      REDIS_URL: redis://:redispassword@redis:6379
    depends_on:
      - redis
    networks:
      - xzgame-network

  manager-service:
    build:
      context: ./manager-service
      dockerfile: Dockerfile
    container_name: xzgame-manager-service
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      RAILS_ENV: development
      DATABASE_URL: **************************************************************
      REDIS_URL: redis://:redispassword@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - xzgame-network

  dashboard-service:
    build:
      context: ./dashboard-service
      dockerfile: Dockerfile
    container_name: xzgame-dashboard
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3000/api/v1
      API_GATEWAY_URL: http://api-gateway:3000
    depends_on:
      - api-gateway
    networks:
      - xzgame-network

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: xzgame-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - xzgame-network

volumes:
  mongodb_data:
  redis_data:
  prometheus_data:

networks:
  xzgame-network:
    driver: bridge
```

## Service Deployment Architecture

### Deployment Patterns

#### Blue-Green Deployment
```bash
#!/bin/bash
# Blue-Green deployment script

ENVIRONMENT=${1:-staging}
NEW_VERSION=${2:-latest}

echo "Starting Blue-Green deployment for $ENVIRONMENT"

# Deploy to green environment
docker-compose -f docker-compose.$ENVIRONMENT.yml -p xzgame-green up -d

# Health check green environment
echo "Performing health checks..."
for service in api-gateway socket-gateway game-service; do
  echo "Checking $service..."
  for i in {1..30}; do
    if curl -f http://localhost:$(get_port $service)/health; then
      echo "$service is healthy"
      break
    fi
    sleep 2
  done
done

# Switch traffic to green
echo "Switching traffic to green environment..."
./scripts/switch-traffic.sh green

# Stop blue environment
echo "Stopping blue environment..."
docker-compose -f docker-compose.$ENVIRONMENT.yml -p xzgame-blue down

echo "Deployment completed successfully"
```

#### Rolling Deployment
```bash
#!/bin/bash
# Rolling deployment script

SERVICES=("api-gateway" "socket-gateway" "game-service" "auth-service" "manager-service")

for service in "${SERVICES[@]}"; do
  echo "Deploying $service..."
  
  # Scale up new version
  docker-compose up -d --scale $service=2 $service
  
  # Health check
  ./scripts/health-check.sh $service
  
  # Scale down old version
  docker-compose up -d --scale $service=1 $service
  
  echo "$service deployed successfully"
done
```

### Health Checks and Monitoring

#### Health Check Implementation
```javascript
// Health check endpoint implementation
app.get('/health', async (req, res) => {
  const startTime = Date.now();
  
  try {
    // Check database connectivity
    const dbHealth = await checkDatabaseHealth();
    
    // Check Redis connectivity
    const redisHealth = await checkRedisHealth();
    
    // Check external service dependencies
    const serviceHealth = await checkExternalServices();
    
    const responseTime = Date.now() - startTime;
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      checks: {
        database: dbHealth,
        redis: redisHealth,
        services: serviceHealth
      },
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        },
        cpu: process.cpuUsage()
      }
    };
    
    res.status(200).json(healthData);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});
```

#### Kubernetes Health Checks
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: xzgame/api-gateway:latest
        ports:
        - containerPort: 3000
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### Scaling Considerations

#### Horizontal Scaling
```yaml
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### Load Balancing Configuration
```nginx
# Nginx load balancer configuration
upstream api_gateway {
    least_conn;
    server api-gateway-1:3000 max_fails=3 fail_timeout=30s;
    server api-gateway-2:3000 max_fails=3 fail_timeout=30s;
    server api-gateway-3:3000 max_fails=3 fail_timeout=30s;
}

upstream socket_gateway {
    ip_hash;  # Sticky sessions for WebSocket
    server socket-gateway-1:3001 max_fails=3 fail_timeout=30s;
    server socket-gateway-2:3001 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.xzgame.com;
    
    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health check
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

server {
    listen 80;
    server_name socket.xzgame.com;
    
    location / {
        proxy_pass http://socket_gateway;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific
        proxy_buffering off;
        proxy_cache off;
    }
}
```

### Database Deployment

#### MongoDB Replica Set
```yaml
# MongoDB replica set configuration
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
spec:
  serviceName: mongodb
  replicas: 3
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:6.0
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /etc/mongo
  volumeClaimTemplates:
  - metadata:
      name: mongodb-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
```

#### Redis Cluster
```yaml
# Redis cluster configuration
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: redis
  replicas: 6
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        - containerPort: 16379
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
```
