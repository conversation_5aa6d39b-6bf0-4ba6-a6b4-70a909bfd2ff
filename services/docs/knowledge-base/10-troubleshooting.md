# Troubleshooting Guide

## Common Issues and Solutions

### Service Startup Issues

#### MongoDB Connection Failures

**Symptoms**:
- Services fail to start with MongoDB connection errors
- "MongoNetworkError: failed to connect to server"
- "Authentication failed" errors

**Diagnosis**:
```bash
# Check MongoDB status
docker ps | grep mongo
docker logs mongodb-container

# Test connection manually
mongo mongodb://localhost:27017/xzgame
```

**Solutions**:
1. **Check MongoDB is running**:
   ```bash
   docker-compose up mongodb
   ```

2. **Verify connection string**:
   ```bash
   # Correct format
   MONGODB_URL=*******************************************************************
   ```

3. **Check authentication credentials**:
   ```bash
   # Reset MongoDB with new credentials
   docker-compose down -v
   docker-compose up mongodb
   ```

4. **Network connectivity**:
   ```bash
   # Test from container
   docker exec -it api-gateway-container ping mongodb
   ```

#### Redis Connection Issues

**Symptoms**:
- "Redis connection failed"
- "ECONNREFUSED" errors
- Session/cache operations failing

**Diagnosis**:
```bash
# Check Redis status
redis-cli ping
docker logs redis-container

# Test Redis connection
redis-cli -h localhost -p 6379 -a password ping
```

**Solutions**:
1. **Start Redis service**:
   ```bash
   docker-compose up redis
   ```

2. **Check Redis configuration**:
   ```bash
   # Verify password and URL
   REDIS_URL=redis://:password@localhost:6379
   ```

3. **Clear Redis data if corrupted**:
   ```bash
   redis-cli FLUSHALL
   ```

### WebSocket Connection Problems

#### Socket.io Connection Failures

**Symptoms**:
- Clients cannot connect to Socket Gateway
- "Connection timeout" errors
- Frequent disconnections

**Diagnosis**:
```bash
# Check Socket Gateway logs
docker logs socket-gateway-container

# Test WebSocket endpoint
curl -I http://localhost:3001/socket.io/
```

**Solutions**:
1. **Check CORS configuration**:
   ```javascript
   // socket-gateway/src/app.js
   const io = new Server(server, {
     cors: {
       origin: process.env.CORS_ORIGINS.split(','),
       methods: ["GET", "POST"],
       credentials: true
     }
   });
   ```

2. **Verify JWT token format**:
   ```javascript
   // Client-side connection
   const socket = io('ws://localhost:3001', {
     auth: {
       token: 'Bearer ' + jwtToken  // Include 'Bearer ' prefix
     }
   });
   ```

3. **Check firewall/proxy settings**:
   ```bash
   # Test direct connection
   telnet localhost 3001
   ```

#### Event Broadcasting Issues

**Symptoms**:
- Events not reaching all clients
- Delayed event delivery
- Missing room updates

**Diagnosis**:
```bash
# Check Redis pub/sub
redis-cli MONITOR

# Check Socket Gateway event logs
grep "Broadcasting" logs/socket-gateway.log
```

**Solutions**:
1. **Verify Redis pub/sub channels**:
   ```bash
   # Subscribe to test channel
   redis-cli PSUBSCRIBE "room:*:events"
   ```

2. **Check room subscription state**:
   ```javascript
   // Debug room subscriptions
   console.log('Room subscriptions:', socketService.getRoomSubscriptions());
   ```

3. **Restart Socket Gateway**:
   ```bash
   docker-compose restart socket-gateway
   ```

### Game Logic Issues

#### Race Conditions in Room Operations

**Symptoms**:
- Players joining full rooms
- Duplicate color selections
- Inconsistent room state

**Diagnosis**:
```bash
# Check for concurrent operations in logs
grep "race condition\|concurrent\|duplicate" logs/game-service.log

# Monitor room state in Redis
redis-cli HGETALL room:ROOM_ID:info
```

**Solutions**:
1. **Enable atomic operations**:
   ```go
   // Use mutex for room operations
   roomMutex := s.getRoomMutex(roomID)
   roomMutex.Lock()
   defer roomMutex.Unlock()
   ```

2. **Check Redis transactions**:
   ```go
   // Use Redis WATCH for optimistic locking
   err := s.redisClient.Watch(ctx, func(tx *redis.Tx) error {
     // Atomic operations here
     return nil
   }, watchKeys...)
   ```

3. **Verify event ordering**:
   ```javascript
   // Use process.nextTick for proper ordering
   callback(response);
   process.nextTick(() => {
     this.broadcastRoomUpdate(roomId);
   });
   ```

#### Color Selection Conflicts

**Symptoms**:
- Multiple players with same color
- Colors not being released
- Inconsistent color state

**Diagnosis**:
```bash
# Check color state in Redis
redis-cli HGETALL room:ROOM_ID:colors
redis-cli SMEMBERS room:ROOM_ID:available_colors
```

**Solutions**:
1. **Reset color state**:
   ```bash
   # Clear color selections for room
   redis-cli DEL room:ROOM_ID:colors
   redis-cli DEL room:ROOM_ID:available_colors
   ```

2. **Verify color release on player leave**:
   ```go
   // Ensure color is released when player leaves
   func (s *PrizeWheelService) HandlePlayerLeave(roomID, userID string) {
     // Release player's color
     s.releasePlayerColor(roomID, userID)
   }
   ```

### Performance Issues

#### High Memory Usage

**Symptoms**:
- Services consuming excessive memory
- Out of memory errors
- Slow response times

**Diagnosis**:
```bash
# Check memory usage
docker stats
top -p $(pgrep node)

# Node.js memory profiling
node --inspect src/app.js
```

**Solutions**:
1. **Optimize database queries**:
   ```javascript
   // Use lean queries for read-only operations
   const rooms = await Room.find({ status: 'waiting' }).lean();
   
   // Add proper indexes
   db.rooms.createIndex({ gameType: 1, status: 1 });
   ```

2. **Implement connection pooling**:
   ```javascript
   // MongoDB connection pooling
   mongoose.connect(mongoUrl, {
     maxPoolSize: 10,
     minPoolSize: 2,
     maxIdleTimeMS: 30000
   });
   ```

3. **Add memory limits**:
   ```yaml
   # docker-compose.yml
   services:
     api-gateway:
       deploy:
         resources:
           limits:
             memory: 512M
   ```

#### Slow Database Queries

**Symptoms**:
- High response times
- Database timeouts
- Query performance warnings

**Diagnosis**:
```bash
# MongoDB slow query profiling
db.setProfilingLevel(2, { slowms: 100 })
db.system.profile.find().sort({ ts: -1 }).limit(5)

# Check query execution plans
db.rooms.find({ gameType: 'prizewheel' }).explain('executionStats')
```

**Solutions**:
1. **Add missing indexes**:
   ```javascript
   // Create compound indexes for common queries
   db.rooms.createIndex({ 
     gameType: 1, 
     status: 1, 
     currentPlayers: 1 
   });
   ```

2. **Optimize aggregation pipelines**:
   ```javascript
   // Use $match early in pipeline
   const pipeline = [
     { $match: { gameType: 'prizewheel', status: 'waiting' } },
     { $lookup: { ... } },
     { $project: { ... } }
   ];
   ```

3. **Implement query caching**:
   ```javascript
   // Cache frequently accessed data
   const getCachedRooms = async (gameType) => {
     const cacheKey = `rooms:${gameType}`;
     let rooms = await redis.get(cacheKey);
     
     if (!rooms) {
       rooms = await Room.find({ gameType }).lean();
       await redis.setex(cacheKey, 300, JSON.stringify(rooms));
     }
     
     return JSON.parse(rooms);
   };
   ```

## Debugging Techniques

### Logging and Monitoring

#### Enable Debug Logging
```bash
# Node.js services
LOG_LEVEL=debug npm run dev

# Go services
LOG_LEVEL=debug go run cmd/server/main.go

# Enable specific debug categories
DEBUG=socket:events,redis:* npm run dev
```

#### Real-time Log Monitoring
```bash
# Follow logs from all services
docker-compose logs -f

# Follow specific service logs
docker-compose logs -f api-gateway

# Filter logs by level
docker-compose logs api-gateway | grep ERROR

# Monitor Redis operations
redis-cli MONITOR | grep "room:"
```

### Performance Profiling

#### Node.js Profiling
```bash
# CPU profiling
node --prof src/app.js
node --prof-process isolate-*.log > profile.txt

# Memory profiling
node --inspect src/app.js
# Open chrome://inspect in Chrome browser

# Heap snapshots
const v8 = require('v8');
const fs = require('fs');

setInterval(() => {
  const heapSnapshot = v8.writeHeapSnapshot();
  console.log('Heap snapshot written to', heapSnapshot);
}, 60000);
```

#### Go Profiling
```go
// Add pprof endpoint
import _ "net/http/pprof"

go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()

// Generate profiles
go tool pprof http://localhost:6060/debug/pprof/profile
go tool pprof http://localhost:6060/debug/pprof/heap
```

### Network Debugging

#### gRPC Connection Issues
```bash
# Test gRPC connectivity
grpcurl -plaintext localhost:50051 list
grpcurl -plaintext localhost:50051 game.GameService/GetGameState

# Enable gRPC logging
export GRPC_GO_LOG_VERBOSITY_LEVEL=99
export GRPC_GO_LOG_SEVERITY_LEVEL=info
```

#### HTTP Request Debugging
```bash
# Test API endpoints
curl -v -H "Authorization: Bearer TOKEN" http://localhost:3000/api/v1/rooms

# Monitor HTTP traffic
tcpdump -i lo0 -A -s 0 port 3000

# Use httpie for better formatting
http GET localhost:3000/api/v1/rooms Authorization:"Bearer TOKEN"
```

## Error Recovery Patterns

### Automatic Recovery

#### Service Health Checks
```bash
#!/bin/bash
# health-check.sh
SERVICE_URL=$1
MAX_RETRIES=3
RETRY_DELAY=5

for i in $(seq 1 $MAX_RETRIES); do
  if curl -f "$SERVICE_URL/health" > /dev/null 2>&1; then
    echo "Service is healthy"
    exit 0
  fi
  
  echo "Health check failed (attempt $i/$MAX_RETRIES)"
  sleep $RETRY_DELAY
done

echo "Service is unhealthy after $MAX_RETRIES attempts"
exit 1
```

#### Circuit Breaker Implementation
```javascript
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.threshold = threshold;
    this.timeout = timeout;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
  }
  
  async call(fn) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }
  
  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

### Manual Recovery Procedures

#### Database Recovery
```bash
# MongoDB recovery
# 1. Stop all services
docker-compose down

# 2. Backup current data
mongodump --uri="mongodb://localhost:27017/xzgame" --out=backup/

# 3. Repair database if needed
mongod --repair --dbpath=/data/db

# 4. Restore from backup if necessary
mongorestore --uri="mongodb://localhost:27017/xzgame" backup/xzgame/

# 5. Restart services
docker-compose up
```

#### Redis Recovery
```bash
# Redis recovery
# 1. Check Redis status
redis-cli ping

# 2. Save current state
redis-cli BGSAVE

# 3. If corrupted, restore from backup
redis-cli FLUSHALL
redis-cli < backup.rdb

# 4. Restart Redis if needed
docker-compose restart redis
```

#### Service Recovery
```bash
# Service recovery script
#!/bin/bash
SERVICE_NAME=$1

echo "Recovering service: $SERVICE_NAME"

# 1. Stop service
docker-compose stop $SERVICE_NAME

# 2. Check logs for errors
docker-compose logs --tail=100 $SERVICE_NAME > recovery-logs.txt

# 3. Clear any corrupted state
if [ "$SERVICE_NAME" = "socket-gateway" ]; then
  redis-cli DEL "connections:*"
fi

# 4. Restart service
docker-compose up -d $SERVICE_NAME

# 5. Wait for health check
sleep 10
./health-check.sh http://localhost:3001/health

echo "Service recovery completed"
```

## Performance Optimization

### Database Optimization

#### Index Optimization
```javascript
// Analyze query patterns
db.rooms.aggregate([
  { $indexStats: {} }
]);

// Create optimal indexes
db.rooms.createIndex(
  { gameType: 1, status: 1, currentPlayers: 1 },
  { background: true }
);

// Remove unused indexes
db.rooms.dropIndex("old_unused_index");
```

#### Query Optimization
```javascript
// Use projection to limit returned fields
const rooms = await Room.find(
  { gameType: 'prizewheel', status: 'waiting' },
  { name: 1, currentPlayers: 1, maxPlayers: 1 }
);

// Use aggregation for complex queries
const roomStats = await Room.aggregate([
  { $match: { gameType: 'prizewheel' } },
  { $group: {
    _id: '$status',
    count: { $sum: 1 },
    avgPlayers: { $avg: '$currentPlayers' }
  }}
]);
```

### Application Optimization

#### Caching Strategies
```javascript
// Multi-level caching
class CacheService {
  constructor() {
    this.l1Cache = new Map(); // In-memory
    this.l2Cache = redis;     // Redis
  }
  
  async get(key) {
    // Try L1 cache first
    if (this.l1Cache.has(key)) {
      return this.l1Cache.get(key);
    }
    
    // Try L2 cache
    const value = await this.l2Cache.get(key);
    if (value) {
      this.l1Cache.set(key, value);
      return value;
    }
    
    return null;
  }
  
  async set(key, value, ttl = 300) {
    this.l1Cache.set(key, value);
    await this.l2Cache.setex(key, ttl, value);
  }
}
```

#### Connection Pooling
```javascript
// Optimize connection pools
const mongoOptions = {
  maxPoolSize: 10,
  minPoolSize: 2,
  maxIdleTimeMS: 30000,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
};

const redisOptions = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
};
```
