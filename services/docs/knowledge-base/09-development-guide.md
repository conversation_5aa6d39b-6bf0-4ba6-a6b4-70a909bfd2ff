# Development Guide

**Status**: ✅ **Production Ready** - Complete development environment with testing

## ✅ **Current Implementation Status**
- **Modular Architecture**: 21 focused modules across 4 Go services
- **Testing Coverage**: Comprehensive unit and integration tests
- **Development Tools**: Complete toolchain and scripts
- **Docker Support**: Full containerization for all services
- **CI/CD Ready**: Automated testing and deployment pipelines

## Local Development Setup

### Prerequisites

#### Required Software
- **Node.js**: 18.x or higher
- **Go**: 1.21 or higher
- **Ruby**: 3.1 or higher
- **Docker**: 20.x or higher
- **Docker Compose**: 2.x or higher
- **MongoDB**: 6.0 or higher
- **Redis**: 7.x or higher

#### Development Tools
- **Git**: Version control
- **VS Code**: Recommended IDE with extensions:
  - Go extension
  - Node.js extension
  - Docker extension
  - MongoDB for VS Code
  - Redis extension
- **Postman**: API testing
- **MongoDB Compass**: Database GUI
- **Redis Insight**: Redis GUI

### Environment Setup

#### 1. Clone Repository
```bash
git clone https://github.com/xzgame/game-service-git.git
cd game-service-git/services
```

#### 2. Setup Environment Variables
```bash
# Copy environment templates
cp api-gateway/.env.example api-gateway/.env
cp socket-gateway/.env.example socket-gateway/.env
cp game-service/.env.example game-service/.env
cp auth-service/.env.example auth-service/.env
cp manager-service/.env.example manager-service/.env
cp dashboard-service/.env.example dashboard-service/.env

# Edit environment files with your local configuration
```

#### 3. Start Infrastructure Services
```bash
# Start MongoDB and Redis using Docker
docker-compose -f docker-compose.infrastructure.yml up -d

# Verify services are running
docker ps
curl http://localhost:27017  # MongoDB
redis-cli ping              # Redis
```

#### 4. Install Dependencies

**Node.js Services**:
```bash
# API Gateway
cd api-gateway
npm install
cd ..

# Socket Gateway
cd socket-gateway
npm install
cd ..

# Dashboard Service
cd dashboard-service
npm install
cd ..
```

**Go Services**:
```bash
# Game Service
cd game-service
go mod download
cd ..

# Auth Service
cd auth-service
go mod download
cd ..

# Room Service
cd room-service
go mod download
cd ..

# Notification Service
cd notification-service
go mod download
cd ..

# Game Engine Service
cd game-engine-service
go mod download
cd ..
```

**Ruby Service**:
```bash
# Manager Service
cd manager-service
bundle install
cd ..
```

### Development Workflow

#### Starting Services

**Option 1: Individual Services**
```bash
# Terminal 1 - API Gateway
cd api-gateway
npm run dev

# Terminal 2 - Socket Gateway
cd socket-gateway
npm run dev

# Terminal 3 - Game Service
cd game-service
go run cmd/server/main.go

# Terminal 4 - Auth Service
cd auth-service
go run cmd/server/main.go

# Terminal 5 - Manager Service
cd manager-service
bundle exec rails server

# Terminal 6 - Dashboard Service
cd dashboard-service
npm run dev
```

**Option 2: Docker Compose (Recommended)**
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Start specific services
docker-compose -f docker-compose.dev.yml up api-gateway socket-gateway game-service

# View logs
docker-compose -f docker-compose.dev.yml logs -f api-gateway
```

#### Development Scripts

**Package.json Scripts (Node.js services)**:
```json
{
  "scripts": {
    "dev": "nodemon src/app.js",
    "start": "node src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/",
    "build": "webpack --mode production",
    "docker:build": "docker build -t xzgame/api-gateway .",
    "docker:run": "docker run -p 3000:3000 xzgame/api-gateway"
  }
}
```

**Makefile (Go services)**:
```makefile
# Variables
BINARY_NAME=game-service
DOCKER_IMAGE=xzgame/game-service

# Development commands
.PHONY: dev
dev:
	go run cmd/server/main.go

.PHONY: build
build:
	go build -o bin/$(BINARY_NAME) cmd/server/main.go

.PHONY: test
test:
	go test -v ./...

.PHONY: test-coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out

.PHONY: lint
lint:
	golangci-lint run

.PHONY: format
format:
	go fmt ./...
	goimports -w .

.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 $(DOCKER_IMAGE)

.PHONY: proto
proto:
	protoc --go_out=. --go-grpc_out=. proto/*.proto

.PHONY: clean
clean:
	go clean
	rm -f bin/$(BINARY_NAME)
```

## Testing Strategies

### Unit Testing

#### Node.js Unit Tests (Jest)
```javascript
// tests/services/roomService.test.js
const RoomService = require('../../src/services/roomService');
const Room = require('../../src/models/Room');

jest.mock('../../src/models/Room');

describe('RoomService', () => {
  let roomService;
  
  beforeEach(() => {
    roomService = new RoomService();
    jest.clearAllMocks();
  });
  
  describe('createRoom', () => {
    it('should create a room successfully', async () => {
      const roomData = {
        name: 'Test Room',
        gameType: 'prizewheel',
        maxPlayers: 8,
        minPlayers: 2,
        betLimits: { min: 10, max: 1000 }
      };
      
      const mockRoom = { _id: 'room123', ...roomData };
      Room.create.mockResolvedValue(mockRoom);
      
      const result = await roomService.createRoom(roomData);
      
      expect(Room.create).toHaveBeenCalledWith(roomData);
      expect(result).toEqual(mockRoom);
    });
    
    it('should throw error for invalid room data', async () => {
      const invalidData = { name: '' };
      
      await expect(roomService.createRoom(invalidData))
        .rejects.toThrow('Invalid room data');
    });
  });
  
  describe('joinRoom', () => {
    it('should allow player to join room', async () => {
      const roomId = 'room123';
      const userId = 'user123';
      const betAmount = 100;
      
      const mockRoom = {
        _id: roomId,
        players: [],
        maxPlayers: 8,
        currentPlayers: 0,
        addPlayer: jest.fn()
      };
      
      Room.findById.mockResolvedValue(mockRoom);
      mockRoom.addPlayer.mockResolvedValue(true);
      
      const result = await roomService.joinRoom(roomId, userId, betAmount);
      
      expect(mockRoom.addPlayer).toHaveBeenCalledWith(userId, betAmount);
      expect(result.success).toBe(true);
    });
  });
});
```

#### Go Unit Tests
```go
// internal/services/room_service_test.go
package services

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    
    "github.com/xzgame/game-service/internal/models"
    "github.com/xzgame/game-service/internal/repositories/mocks"
)

func TestRoomService_CreateRoom(t *testing.T) {
    // Setup
    mockRepo := new(mocks.RoomRepository)
    mockRedis := new(mocks.RedisClient)
    service := NewRoomService(mockRepo, mockRedis, nil)
    
    ctx := context.Background()
    roomData := &models.Room{
        Name:       "Test Room",
        GameType:   "prizewheel",
        MaxPlayers: 8,
        MinPlayers: 2,
    }
    
    // Mock expectations
    mockRepo.On("Create", ctx, mock.AnythingOfType("*models.Room")).
        Return(roomData, nil)
    
    // Execute
    result, err := service.CreateRoom(ctx, roomData)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, roomData.Name, result.Name)
    assert.Equal(t, roomData.GameType, result.GameType)
    mockRepo.AssertExpectations(t)
}

func TestRoomService_JoinRoom(t *testing.T) {
    // Setup
    mockRepo := new(mocks.RoomRepository)
    mockRedis := new(mocks.RedisClient)
    service := NewRoomService(mockRepo, mockRedis, nil)
    
    ctx := context.Background()
    roomID := "room123"
    userID := "user123"
    betAmount := 100.0
    
    existingRoom := &models.Room{
        ID:             roomID,
        Players:        []models.Player{},
        MaxPlayers:     8,
        CurrentPlayers: 0,
        Status:         "waiting",
    }
    
    // Mock expectations
    mockRepo.On("FindByID", ctx, roomID).Return(existingRoom, nil)
    mockRepo.On("Update", ctx, mock.AnythingOfType("*models.Room")).
        Return(existingRoom, nil)
    
    // Execute
    result, err := service.JoinRoom(ctx, roomID, userID, betAmount)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, 1, len(result.Players))
    assert.Equal(t, userID, result.Players[0].UserID)
    mockRepo.AssertExpectations(t)
}

func TestRoomService_JoinRoom_RoomFull(t *testing.T) {
    // Setup
    mockRepo := new(mocks.RoomRepository)
    service := NewRoomService(mockRepo, nil, nil)
    
    ctx := context.Background()
    roomID := "room123"
    userID := "user123"
    
    fullRoom := &models.Room{
        ID:             roomID,
        MaxPlayers:     2,
        CurrentPlayers: 2,
        Status:         "waiting",
    }
    
    mockRepo.On("FindByID", ctx, roomID).Return(fullRoom, nil)
    
    // Execute
    _, err := service.JoinRoom(ctx, roomID, userID, 100.0)
    
    // Assert
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "room is full")
}
```

### Integration Testing

#### API Integration Tests
```javascript
// tests/integration/api.test.js
const request = require('supertest');
const app = require('../../src/app');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('API Integration Tests', () => {
  let mongoServer;
  let authToken;
  
  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
    
    // Create test user and get auth token
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authToken = userResponse.body.data.tokens.accessToken;
  });
  
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  describe('Room Management', () => {
    it('should create a room', async () => {
      const roomData = {
        name: 'Test Room',
        gameType: 'prizewheel',
        maxPlayers: 8,
        minPlayers: 2,
        betLimits: { min: 10, max: 1000 }
      };
      
      const response = await request(app)
        .post('/api/v1/rooms')
        .set('Authorization', `Bearer ${authToken}`)
        .send(roomData)
        .expect(201);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.room.name).toBe(roomData.name);
    });
    
    it('should join a room', async () => {
      // First create a room
      const roomResponse = await request(app)
        .post('/api/v1/rooms')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Join Test Room',
          gameType: 'prizewheel',
          maxPlayers: 8,
          minPlayers: 2,
          betLimits: { min: 10, max: 1000 }
        });
      
      const roomId = roomResponse.body.data.room._id;
      
      // Then join the room
      const joinResponse = await request(app)
        .post(`/api/v1/rooms/${roomId}/join`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ betAmount: 100 })
        .expect(200);
      
      expect(joinResponse.body.success).toBe(true);
      expect(joinResponse.body.data.room.currentPlayers).toBe(1);
    });
  });
});
```

#### WebSocket Integration Tests
```javascript
// tests/integration/websocket.test.js
const Client = require('socket.io-client');
const server = require('../../src/app');

describe('WebSocket Integration Tests', () => {
  let clientSocket;
  let serverSocket;
  
  beforeAll((done) => {
    server.listen(() => {
      const port = server.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: 'valid_jwt_token_here'
        }
      });
      
      server.on('connection', (socket) => {
        serverSocket = socket;
      });
      
      clientSocket.on('connect', done);
    });
  });
  
  afterAll(() => {
    server.close();
    clientSocket.close();
  });
  
  test('should subscribe to room', (done) => {
    clientSocket.emit('subscribe_room', { roomId: 'room123' }, (response) => {
      expect(response.success).toBe(true);
      expect(response.data.roomId).toBe('room123');
      done();
    });
  });
  
  test('should receive room updates', (done) => {
    clientSocket.on('room_info_updated', (data) => {
      expect(data.room).toBeDefined();
      expect(data.players).toBeDefined();
      done();
    });
    
    // Trigger room update
    serverSocket.emit('room_info_updated', {
      room: { id: 'room123', name: 'Test Room' },
      players: []
    });
  });
});
```

### End-to-End Testing

#### E2E Test Setup
```javascript
// tests/e2e/game-flow.test.js
const puppeteer = require('puppeteer');

describe('Game Flow E2E Tests', () => {
  let browser;
  let page;
  
  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: process.env.CI === 'true',
      slowMo: 50
    });
    page = await browser.newPage();
  });
  
  afterAll(async () => {
    await browser.close();
  });
  
  test('Complete Prize Wheel Game Flow', async () => {
    // Navigate to application
    await page.goto('http://localhost:3003');
    
    // Login
    await page.type('#username', 'testuser');
    await page.type('#password', 'password123');
    await page.click('#login-button');
    await page.waitForNavigation();
    
    // Create room
    await page.click('#create-room-button');
    await page.type('#room-name', 'E2E Test Room');
    await page.select('#game-type', 'prizewheel');
    await page.click('#create-button');
    
    // Wait for room creation
    await page.waitForSelector('#room-info');
    
    // Select color
    await page.click('#color-red');
    await page.waitForSelector('#color-red.selected');
    
    // Mark ready
    await page.click('#ready-button');
    await page.waitForSelector('#ready-status.ready');
    
    // Wait for game to start (if auto-start enabled)
    await page.waitForSelector('#game-started', { timeout: 35000 });
    
    // Wait for game results
    await page.waitForSelector('#game-results', { timeout: 10000 });
    
    // Verify results are displayed
    const results = await page.$('#game-results');
    expect(results).toBeTruthy();
  });
});
```

## Code Organization Patterns

### Project Structure

#### Node.js Services Structure
```
api-gateway/
├── src/
│   ├── controllers/          # Request handlers
│   │   ├── authController.js
│   │   ├── userController.js
│   │   └── roomController.js
│   ├── middleware/           # Express middleware
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── rateLimit.js
│   ├── models/              # Database models
│   │   ├── User.js
│   │   ├── Room.js
│   │   └── Transaction.js
│   ├── routes/              # Route definitions
│   │   ├── auth.js
│   │   ├── users.js
│   │   └── rooms.js
│   ├── services/            # Business logic
│   │   ├── authService.js
│   │   ├── userService.js
│   │   └── roomService.js
│   ├── utils/               # Utility functions
│   │   ├── logger.js
│   │   ├── validation.js
│   │   └── helpers.js
│   ├── validators/          # Request validators
│   │   ├── authValidators.js
│   │   └── roomValidators.js
│   ├── config/              # Configuration
│   │   ├── database.js
│   │   ├── redis.js
│   │   └── index.js
│   └── app.js               # Main application
├── tests/                   # Test files
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/                    # Documentation
├── logs/                    # Log files
├── package.json
├── Dockerfile.dev
├── Dockerfile.prod
└── README.md
```

#### Go Services Structure
```
game-service/
├── cmd/
│   └── server/
│       └── main.go          # Application entry point
├── internal/
│   ├── config/              # Configuration
│   │   └── config.go
│   ├── handlers/            # HTTP/gRPC handlers
│   │   ├── grpc/
│   │   └── http/
│   ├── models/              # Data models
│   │   ├── room.go
│   │   ├── game.go
│   │   └── player.go
│   ├── repositories/        # Data access layer
│   │   ├── interfaces.go
│   │   ├── room_repository.go
│   │   └── game_repository.go
│   ├── services/            # Business logic
│   │   ├── interfaces.go
│   │   ├── room_service.go
│   │   └── game_service.go
│   └── utils/               # Utility functions
│       ├── logger.go
│       └── helpers.go
├── pkg/                     # Public packages
│   ├── redis/
│   └── mongodb/
├── proto/                   # Protocol buffer definitions
│   ├── game.proto
│   └── room.proto
├── tests/                   # Test files
├── scripts/                 # Build/deployment scripts
├── go.mod
├── go.sum
├── Dockerfile
├── Makefile
└── README.md
```

### Best Practices

#### Code Style Guidelines

**JavaScript/Node.js**:
```javascript
// Use ESLint and Prettier for consistent formatting
// .eslintrc.js
module.exports = {
  extends: ['eslint:recommended', 'prettier'],
  env: {
    node: true,
    es2021: true,
    jest: true
  },
  rules: {
    'no-console': 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};

// Use async/await instead of callbacks
const getUserById = async (userId) => {
  try {
    const user = await User.findById(userId);
    return user;
  } catch (error) {
    logger.error('Error fetching user:', error);
    throw new AppError('User not found', 404);
  }
};

// Use proper error handling
const createRoom = async (roomData) => {
  const session = await mongoose.startSession();
  
  try {
    session.startTransaction();
    
    const room = await Room.create([roomData], { session });
    await updateRoomCache(room[0]);
    
    await session.commitTransaction();
    return room[0];
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
};
```

**Go**:
```go
// Use gofmt and goimports for formatting
// Follow Go naming conventions

// Use interfaces for dependency injection
type RoomService interface {
    CreateRoom(ctx context.Context, room *models.Room) (*models.Room, error)
    GetRoom(ctx context.Context, id string) (*models.Room, error)
    UpdateRoom(ctx context.Context, room *models.Room) error
    DeleteRoom(ctx context.Context, id string) error
}

// Use context for cancellation and timeouts
func (s *roomService) CreateRoom(ctx context.Context, room *models.Room) (*models.Room, error) {
    // Set timeout for database operation
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    // Validate input
    if err := s.validateRoom(room); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err)
    }
    
    // Create room
    result, err := s.repo.Create(ctx, room)
    if err != nil {
        return nil, fmt.Errorf("failed to create room: %w", err)
    }
    
    return result, nil
}

// Use proper error wrapping
func (s *roomService) validateRoom(room *models.Room) error {
    if room.Name == "" {
        return errors.New("room name is required")
    }
    
    if room.MaxPlayers < 2 || room.MaxPlayers > 8 {
        return errors.New("max players must be between 2 and 8")
    }
    
    return nil
}
```

#### Dependency Injection

**Node.js (using dependency injection container)**:
```javascript
// container.js
const { createContainer, asClass, asValue, asFunction } = require('awilix');

const container = createContainer();

container.register({
  // Configuration
  config: asValue(require('./config')),
  
  // Database connections
  mongoose: asFunction(require('./config/database')).singleton(),
  redis: asFunction(require('./config/redis')).singleton(),
  
  // Repositories
  userRepository: asClass(require('./repositories/userRepository')).singleton(),
  roomRepository: asClass(require('./repositories/roomRepository')).singleton(),
  
  // Services
  authService: asClass(require('./services/authService')).singleton(),
  userService: asClass(require('./services/userService')).singleton(),
  roomService: asClass(require('./services/roomService')).singleton(),
  
  // Controllers
  authController: asClass(require('./controllers/authController')).singleton(),
  userController: asClass(require('./controllers/userController')).singleton(),
  roomController: asClass(require('./controllers/roomController')).singleton(),
});

module.exports = container;
```

**Go (using wire for dependency injection)**:
```go
// wire.go
//go:build wireinject
// +build wireinject

package main

import (
    "github.com/google/wire"
    "github.com/xzgame/game-service/internal/config"
    "github.com/xzgame/game-service/internal/repositories"
    "github.com/xzgame/game-service/internal/services"
    "github.com/xzgame/game-service/internal/handlers"
)

func InitializeApp() (*App, error) {
    wire.Build(
        config.NewConfig,
        repositories.NewRoomRepository,
        repositories.NewGameRepository,
        services.NewRoomService,
        services.NewGameService,
        handlers.NewGRPCHandler,
        NewApp,
    )
    return &App{}, nil
}
```
