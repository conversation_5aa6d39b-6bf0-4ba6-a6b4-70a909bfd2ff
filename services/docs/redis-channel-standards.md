# Redis Channel Standardization

## Overview

This document defines the standardized Redis channel patterns for the XZ Game platform to eliminate confusion and ensure consistent event routing across all services.

## Current State Problems

### Inconsistent Channel Patterns

Currently, the system uses multiple channel patterns that create confusion:

```javascript
// Multiple patterns in use
'socket:events'              // Socket Gateway events
'game:room:{roomId}'         // Legacy room events  
'room:{roomId}:events'       // New room events format
'game:lobby:updates'         // Lobby updates
'game:global:events'         // Global game events
'manager:sync:events'        // Manager service sync
'user:{userId}:notifications' // User notifications
```

### Service Confusion

Services don't know which channel to use for specific purposes, leading to:
- Duplicate event publishing
- Missed events
- Complex routing logic
- Debugging difficulties

## Standardized Channel Patterns

### 1. Room Events

**Pattern**: `room:{roomId}:events`

**Purpose**: All events related to a specific room

**Publishers**: Game Service (primary), Socket Gateway (relay only)

**Subscribers**: Socket Gateway, Manager Service

**Event Types**:
- `player_joined`
- `player_left` 
- `player_ready_changed`
- `game_started`
- `game_finished`
- `room_info_updated`
- `color_selected` (Prize Wheel)
- `color_unselected` (Prize Wheel)
- `position_selected` (Amidakuji)

**Example**:
```json
{
  "event": {
    "type": "player_joined",
    "payload": {
      "roomId": "507f1f77bcf86cd799439011",
      "userId": "user123",
      "username": "player1",
      "position": 1,
      "timestamp": "2024-01-15T10:30:00Z"
    }
  },
  "metadata": {
    "serviceId": "game-service",
    "version": "1.0.0",
    "sequenceNumber": 1234,
    "correlationId": "req-abc123"
  }
}
```

### 2. Lobby Events

**Pattern**: `lobby:events`

**Purpose**: Events affecting the lobby (room list updates)

**Publishers**: Game Service

**Subscribers**: Socket Gateway

**Event Types**:
- `room_created`
- `room_deleted`
- `room_list_updated`
- `room_status_changed`

### 3. User Events

**Pattern**: `user:{userId}:events`

**Purpose**: Events specific to individual users

**Publishers**: Manager Service, Game Service

**Subscribers**: Socket Gateway, Notification Service

**Event Types**:
- `balance_updated`
- `transaction_completed`
- `notification_received`
- `achievement_unlocked`

### 4. Global Events

**Pattern**: `global:events`

**Purpose**: System-wide events and announcements

**Publishers**: Any service

**Subscribers**: All services

**Event Types**:
- `system_maintenance`
- `service_status_change`
- `global_announcement`

### 5. Service Communication

**Pattern**: `service:{targetService}:requests`

**Purpose**: Direct service-to-service communication

**Publishers**: Any service

**Subscribers**: Target service only

**Event Types**:
- `balance_check_request`
- `room_info_request`
- `user_validation_request`

### 6. Health and Monitoring

**Pattern**: `monitoring:events`

**Purpose**: Health checks and monitoring data

**Publishers**: All services

**Subscribers**: Monitoring service

**Event Types**:
- `health_check`
- `performance_metrics`
- `error_report`

## Message Format Standard

### Base Message Structure

```json
{
  "event": {
    "type": "string",
    "payload": {},
    "timestamp": "ISO8601"
  },
  "metadata": {
    "serviceId": "string",
    "version": "string",
    "sequenceNumber": "number",
    "correlationId": "string",
    "priority": "number (1-5)"
  }
}
```

### Required Fields

- **event.type**: Event type identifier
- **event.timestamp**: ISO8601 timestamp
- **metadata.serviceId**: Publishing service identifier
- **metadata.version**: Message format version

### Optional Fields

- **metadata.sequenceNumber**: For event ordering
- **metadata.correlationId**: For request/response tracking
- **metadata.priority**: Event priority (1=highest, 5=lowest)

## Implementation Guidelines

### Channel Naming Rules

1. **Use lowercase**: All channel names in lowercase
2. **Use colons**: Separate namespace levels with colons
3. **Be specific**: Include entity IDs where applicable
4. **Be consistent**: Follow established patterns

### Event Type Naming

1. **Use snake_case**: All event types in snake_case
2. **Be descriptive**: Clear action description
3. **Include entity**: Specify what entity is affected
4. **Use past tense**: Events describe what happened

### Publishing Guidelines

1. **Single Source**: Each event type should have one authoritative publisher
2. **Include Context**: Provide sufficient context in payload
3. **Add Metadata**: Always include service metadata
4. **Handle Failures**: Implement retry logic for critical events

### Subscription Guidelines

1. **Pattern Matching**: Use Redis pattern matching for efficiency
2. **Error Handling**: Handle malformed messages gracefully
3. **Logging**: Log subscription events for debugging
4. **Cleanup**: Unsubscribe when no longer needed

## Migration Plan

### Phase 1: Documentation and Standards (Week 1)

1. **Document Current Usage**: Audit all current channel usage
2. **Define Standards**: Finalize channel patterns and message formats
3. **Create Migration Guide**: Step-by-step migration instructions

### Phase 2: Game Service Updates (Week 2)

1. **Update Publishers**: Migrate Game Service to new patterns
2. **Dual Publishing**: Temporarily publish to both old and new channels
3. **Add Sequence Numbers**: Implement event ordering

### Phase 3: Socket Gateway Updates (Week 3)

1. **Update Subscribers**: Migrate Socket Gateway to new patterns
2. **Remove Legacy Handlers**: Clean up old channel handlers
3. **Test Event Flow**: Verify all events flow correctly

### Phase 4: Manager Service Updates (Week 4)

1. **Update Notifications**: Migrate Manager Service patterns
2. **Remove Dual Publishing**: Stop publishing to legacy channels
3. **Final Testing**: End-to-end testing of new patterns

## Monitoring and Validation

### Channel Usage Metrics

- **Message Volume**: Messages per channel per minute
- **Error Rate**: Failed message processing rate
- **Latency**: Time from publish to process
- **Subscriber Count**: Active subscribers per channel

### Validation Rules

1. **Message Format**: Validate against JSON schema
2. **Required Fields**: Ensure all required fields present
3. **Channel Pattern**: Verify channel follows naming rules
4. **Event Types**: Validate event types against registry

### Alerting

- **High Error Rate**: >1% message processing failures
- **Channel Overflow**: >1000 messages/minute on any channel
- **Missing Subscribers**: Channels with no active subscribers
- **Format Violations**: Invalid message formats

## Tools and Utilities

### Channel Registry CLI

```bash
# List all active channels
redis-cli --pattern "*:events" PUBSUB CHANNELS

# Monitor specific channel
redis-cli MONITOR | grep "room:.*:events"

# Validate message format
node scripts/validate-message.js < message.json
```

### Development Tools

1. **Message Validator**: JSON schema validation
2. **Channel Monitor**: Real-time channel activity
3. **Event Simulator**: Generate test events
4. **Migration Helper**: Assist with channel migration

## Best Practices

### Performance

1. **Batch Events**: Group related events when possible
2. **Compress Payloads**: Use compression for large payloads
3. **TTL Settings**: Set appropriate TTL for channels
4. **Connection Pooling**: Reuse Redis connections

### Security

1. **Validate Input**: Always validate message content
2. **Rate Limiting**: Implement per-service rate limits
3. **Access Control**: Restrict channel access by service
4. **Audit Logging**: Log all channel operations

### Reliability

1. **Retry Logic**: Implement exponential backoff
2. **Dead Letter Queue**: Handle failed messages
3. **Circuit Breaker**: Prevent cascade failures
4. **Health Checks**: Monitor channel health

## Related Documentation

- [Game Flow Analysis](./game-flow-analysis.md)
- [Event System Architecture](./event-system-architecture.md)
- [Performance Optimization Guide](./performance-optimization.md)
- [Troubleshooting Guide](./knowledge-base/10-troubleshooting.md)
