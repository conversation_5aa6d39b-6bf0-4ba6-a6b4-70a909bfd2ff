# Improved Seat Management Architecture

## Current vs Recommended Architecture

### Current Issues
- **Multiple State Sources**: Manager-service, Room-service, Socket-gateway all maintain seat state
- **Synchronous Communication**: Blocking gRPC calls in critical path
- **Race Conditions**: Concurrent seat assignments can conflict
- **No Audit Trail**: Direct state mutations without event history
- **Poor Scalability**: 200-500ms latency per operation

### Recommended: Event Sourcing + CQRS

```mermaid
graph TB
    subgraph "Command Side (Write)"
        CMD[Seat Commands]
        ES[Event Store]
        AGG[Seat Aggregate]
    end
    
    subgraph "Query Side (Read)"
        PROJ[Seat Projections]
        CACHE[Redis Cache]
        API[Query APIs]
    end
    
    subgraph "Event Processing"
        STREAM[Redis Streams]
        PROC[Event Processors]
    end
    
    CMD --> AGG
    AGG --> ES
    ES --> STREAM
    STREAM --> PROC
    PROC --> PROJ
    PROJ --> CACHE
    CACHE --> API
```

## 1. Event Sourcing Implementation

### Seat Events Schema
```go
type SeatEvent struct {
    EventID     string    `json:"event_id"`
    RoomID      string    `json:"room_id"`
    EventType   string    `json:"event_type"`
    Version     int64     `json:"version"`
    Timestamp   time.Time `json:"timestamp"`
    PlayerID    string    `json:"player_id,omitempty"`
    Position    int       `json:"position,omitempty"`
    Metadata    map[string]interface{} `json:"metadata"`
}

// Event Types
const (
    SeatAssignedEvent    = "seat_assigned"
    SeatReleasedEvent    = "seat_released"
    SeatReassignedEvent  = "seat_reassigned"
    RoomCreatedEvent     = "room_created"
    RoomClosedEvent      = "room_closed"
)
```

### Event Store Implementation
```go
type SeatEventStore struct {
    redis   *redis.Client
    streams map[string]string // roomID -> stream name
}

func (es *SeatEventStore) AppendEvent(ctx context.Context, roomID string, event SeatEvent) error {
    streamName := fmt.Sprintf("seat_events:%s", roomID)
    
    // Optimistic concurrency control
    currentVersion, err := es.getCurrentVersion(ctx, roomID)
    if err != nil {
        return err
    }
    
    if event.Version != currentVersion+1 {
        return ErrConcurrentModification
    }
    
    // Append to Redis Stream
    _, err = es.redis.XAdd(ctx, &redis.XAddArgs{
        Stream: streamName,
        Values: map[string]interface{}{
            "event_id":   event.EventID,
            "event_type": event.EventType,
            "version":    event.Version,
            "data":       event,
        },
    }).Result()
    
    return err
}
```

## 2. Seat Aggregate (Domain Logic)

```go
type SeatAggregate struct {
    RoomID      string
    Version     int64
    Seats       map[int]string // position -> playerID
    MaxSeats    int
    Events      []SeatEvent
}

func (sa *SeatAggregate) AssignSeat(playerID string, preferredPosition *int) error {
    // Business logic validation
    if len(sa.Seats) >= sa.MaxSeats {
        return ErrRoomFull
    }
    
    if sa.isPlayerSeated(playerID) {
        return ErrPlayerAlreadySeated
    }
    
    // Calculate position
    position := sa.calculateOptimalPosition(preferredPosition)
    
    // Create event
    event := SeatEvent{
        EventID:   generateEventID(),
        RoomID:    sa.RoomID,
        EventType: SeatAssignedEvent,
        Version:   sa.Version + 1,
        Timestamp: time.Now(),
        PlayerID:  playerID,
        Position:  position,
    }
    
    // Apply event
    sa.applyEvent(event)
    return nil
}

func (sa *SeatAggregate) applyEvent(event SeatEvent) {
    switch event.EventType {
    case SeatAssignedEvent:
        sa.Seats[event.Position] = event.PlayerID
    case SeatReleasedEvent:
        delete(sa.Seats, event.Position)
    case SeatReassignedEvent:
        // Handle reassignment logic
    }
    
    sa.Version = event.Version
    sa.Events = append(sa.Events, event)
}
```

## 3. CQRS Query Side

### Read Model Projections
```go
type SeatProjection struct {
    RoomID         string    `json:"room_id"`
    Version        int64     `json:"version"`
    Seats          []Seat    `json:"seats"`
    AvailableSeats []int     `json:"available_seats"`
    Statistics     SeatStats `json:"statistics"`
    LastUpdated    time.Time `json:"last_updated"`
}

type Seat struct {
    Position  int    `json:"position"`
    PlayerID  string `json:"player_id"`
    Username  string `json:"username"`
    JoinedAt  time.Time `json:"joined_at"`
}

type SeatProjectionService struct {
    redis  *redis.Client
    logger *logrus.Logger
}

func (sps *SeatProjectionService) ProcessEvent(ctx context.Context, event SeatEvent) error {
    projectionKey := fmt.Sprintf("seat_projection:%s", event.RoomID)
    
    // Get current projection
    projection, err := sps.getProjection(ctx, projectionKey)
    if err != nil {
        return err
    }
    
    // Apply event to projection
    switch event.EventType {
    case SeatAssignedEvent:
        projection.addSeat(event.Position, event.PlayerID)
    case SeatReleasedEvent:
        projection.removeSeat(event.Position)
    }
    
    projection.Version = event.Version
    projection.LastUpdated = time.Now()
    
    // Save updated projection
    return sps.saveProjection(ctx, projectionKey, projection)
}
```

## 4. Saga Pattern for Cross-Service Operations

### Seat Assignment Saga
```go
type SeatAssignmentSaga struct {
    SagaID      string
    RoomID      string
    PlayerID    string
    State       SagaState
    Steps       []SagaStep
    CompletedAt *time.Time
}

type SagaState string
const (
    SagaStarted    SagaState = "started"
    SagaCompleted  SagaState = "completed"
    SagaFailed     SagaState = "failed"
    SagaCompensating SagaState = "compensating"
)

func (sas *SeatAssignmentSaga) Execute(ctx context.Context) error {
    steps := []SagaStep{
        {Name: "reserve_balance", Service: "manager-service"},
        {Name: "assign_seat", Service: "room-service"},
        {Name: "update_game_state", Service: "game-service"},
        {Name: "notify_players", Service: "socket-gateway"},
    }
    
    for _, step := range steps {
        err := sas.executeStep(ctx, step)
        if err != nil {
            // Compensate previous steps
            return sas.compensate(ctx)
        }
    }
    
    sas.State = SagaCompleted
    return nil
}
```

## 5. Performance Optimizations

### Async Event Processing
```javascript
// Socket Gateway - Async event processing
class SeatEventProcessor {
  constructor() {
    this.eventQueue = new Queue('seat-events', {
      redis: redisConfig,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: 'exponential'
      }
    });
  }
  
  async processSeatEvent(event) {
    // Non-blocking event processing
    await this.eventQueue.add('process-seat-event', event, {
      priority: this.getEventPriority(event.type),
      delay: 0
    });
  }
  
  getEventPriority(eventType) {
    const priorities = {
      'seat_assigned': 10,
      'seat_released': 8,
      'seat_reassigned': 6
    };
    return priorities[eventType] || 1;
  }
}
```

### Intelligent Caching
```go
type SeatCacheManager struct {
    l1Cache *sync.Map // In-memory cache
    l2Cache *redis.Client // Redis cache
    ttl     time.Duration
}

func (scm *SeatCacheManager) GetSeatState(ctx context.Context, roomID string) (*SeatProjection, error) {
    // L1 Cache (in-memory)
    if cached, ok := scm.l1Cache.Load(roomID); ok {
        if projection, ok := cached.(*SeatProjection); ok {
            return projection, nil
        }
    }
    
    // L2 Cache (Redis)
    projection, err := scm.getFromRedis(ctx, roomID)
    if err == nil {
        scm.l1Cache.Store(roomID, projection)
        return projection, nil
    }
    
    // Rebuild from event store
    return scm.rebuildFromEvents(ctx, roomID)
}
```

## 6. Monitoring and Observability

### Event Metrics
```go
type SeatMetrics struct {
    EventsProcessed   prometheus.Counter
    EventLatency      prometheus.Histogram
    ConcurrentSeats   prometheus.Gauge
    SagaSuccess       prometheus.Counter
    SagaFailures      prometheus.Counter
}

func (sm *SeatMetrics) RecordEvent(eventType string, duration time.Duration) {
    sm.EventsProcessed.WithLabelValues(eventType).Inc()
    sm.EventLatency.WithLabelValues(eventType).Observe(duration.Seconds())
}
```

## 7. Migration Strategy

### Phase 1: Event Store Setup (Week 1)
- Implement Redis Streams for event storage
- Create event schemas and serialization
- Add event versioning and ordering

### Phase 2: CQRS Implementation (Week 2-3)
- Implement seat aggregates with business logic
- Create read model projections
- Add event processors for real-time updates

### Phase 3: Saga Implementation (Week 4)
- Implement saga pattern for cross-service operations
- Add compensation logic for failures
- Integrate with existing services

### Phase 4: Performance Optimization (Week 5-6)
- Implement intelligent caching
- Add async event processing
- Optimize database queries and Redis operations

## Benefits of New Architecture

### Scalability
- **10x Performance**: Async processing reduces latency from 500ms to 50ms
- **Horizontal Scaling**: Event streams can be partitioned across multiple instances
- **Load Distribution**: Read/write separation allows independent scaling

### Reliability
- **Fault Tolerance**: Saga pattern handles partial failures gracefully
- **Event Replay**: Can reconstruct state from events for debugging
- **Audit Trail**: Complete history of all seat changes

### Consistency
- **Strong Consistency**: Event sourcing ensures ordered state changes
- **Eventual Consistency**: Read models eventually consistent across services
- **Conflict Resolution**: Optimistic concurrency control prevents race conditions

### Maintainability
- **Clear Boundaries**: Separation of command/query responsibilities
- **Testability**: Business logic isolated in aggregates
- **Debugging**: Event history provides complete audit trail
