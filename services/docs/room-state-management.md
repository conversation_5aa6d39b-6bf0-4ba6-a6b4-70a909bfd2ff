# Room State Management

## Overview

This document describes the room state management system across the XZ Game platform, including state synchronization, caching strategies, and consistency guarantees.

## Current State Architecture

### State Distribution

```mermaid
graph TB
    subgraph "Game Service"
        GS_DB[(MongoDB)]
        GS_CACHE[(Redis Cache)]
        GS_MEMORY[In-Memory State]
    end
    
    subgraph "Socket Gateway"
        SG_CACHE[Room State Cache]
        SG_RECENT[Recent Room Info]
        SG_SUBS[Subscription State]
    end
    
    subgraph "Manager Service"
        MS_DB[(PostgreSQL)]
        MS_CACHE[Room Cache]
    end
    
    GS_DB --> GS_CACHE
    GS_CACHE --> SG_CACHE
    GS_MEMORY --> SG_RECENT
    MS_DB --> MS_CACHE
    
    SG_CACHE -.-> SG_SUBS
    GS_CACHE -.-> MS_CACHE
```

### Current Issues

#### 1. Multiple Sources of Truth

**Problem**: Room state exists in multiple places with different update patterns

```javascript
// Socket Gateway
this.roomStates = new Map(); // Local cache with 30s TTL
this.recentRoomInfo = new Map(); // Short-term cache for join flow

// Game Service  
roomStateCache := make(map[string]*RoomState) // In-memory cache
redisClient.Set(ctx, "room:state:"+roomID, state) // Redis cache

// Manager Service
Room.find(external_room_id) // Database state
Rails.cache.fetch("room:#{id}") // Rails cache
```

**Impact**: Inconsistent state across services

#### 2. Cache Invalidation Complexity

**Problem**: Different invalidation strategies and timing

```javascript
// Socket Gateway - Time-based invalidation
if (Date.now() - cached.timestamp > 30000) {
  // Cache expired, fetch fresh data
}

// Game Service - Event-based invalidation  
func (s *roomService) invalidateRoomCache(roomID string) {
  delete(s.roomStateCache, roomID)
  s.redisClient.Del(ctx, "room:state:"+roomID)
}

// Manager Service - Manual invalidation
room.touch # Update timestamp to invalidate cache
Rails.cache.delete("room:#{room.id}")
```

**Impact**: Stale data and race conditions

#### 3. State Synchronization Delays

**Problem**: Updates don't propagate consistently

```javascript
// Game Service updates state
room.PlayerCount = newCount
s.roomRepo.UpdateRoom(ctx, room)

// Socket Gateway still has old state
const cachedRoom = this.roomStates.get(roomId);
// cachedRoom.playerCount is still old value
```

**Impact**: UI shows incorrect information

## Proposed Architecture

### Single Source of Truth Pattern

```mermaid
graph TB
    subgraph "Authoritative Layer"
        GS[Game Service]
        GS_DB[(MongoDB)]
    end
    
    subgraph "Cache Layer"
        REDIS[(Redis)]
        REDIS_STREAMS[Redis Streams]
    end
    
    subgraph "Consumer Layer"
        SG[Socket Gateway]
        MS[Manager Service]
        NS[Notification Service]
    end
    
    GS --> GS_DB
    GS --> REDIS
    GS --> REDIS_STREAMS
    
    REDIS --> SG
    REDIS --> MS
    REDIS_STREAMS --> SG
    REDIS_STREAMS --> MS
    REDIS_STREAMS --> NS
```

### State Update Flow

1. **Game Service**: Authoritative source for all room state
2. **Redis**: Centralized cache with event-driven invalidation
3. **Redis Streams**: Ordered state change events
4. **Consumers**: Subscribe to state changes via streams

## State Schema Definition

### Room State Structure

```json
{
  "roomId": "507f1f77bcf86cd799439011",
  "version": 123,
  "lastUpdated": "2024-01-15T10:30:00Z",
  "state": {
    "basic": {
      "name": "Epic Game Room",
      "gameType": "prizewheel",
      "status": "waiting",
      "playerCount": 3,
      "maxPlayers": 8,
      "betAmount": 100,
      "prizePool": 300,
      "isPrivate": false,
      "createdAt": "2024-01-15T10:00:00Z"
    },
    "players": [
      {
        "userId": "user123",
        "username": "player1", 
        "position": 1,
        "isReady": true,
        "betAmount": 100,
        "joinedAt": "2024-01-15T10:05:00Z",
        "status": "active"
      }
    ],
    "gameState": {
      "phase": "waiting",
      "countdown": null,
      "startedAt": null,
      "settings": {
        "wheelSections": 8,
        "spinDuration": 5000
      }
    },
    "gameSpecific": {
      "prizewheel": {
        "colorSelections": {
          "user123": {
            "color": "red",
            "selectedAt": "2024-01-15T10:10:00Z"
          }
        },
        "availableColors": ["red", "blue", "green", "yellow"]
      }
    }
  }
}
```

### State Change Events

```json
{
  "streamId": "room:507f1f77bcf86cd799439011:state",
  "eventId": "1234-5678-9012",
  "eventType": "player_joined",
  "version": 124,
  "timestamp": "2024-01-15T10:30:00Z",
  "changes": {
    "playerCount": {
      "from": 2,
      "to": 3
    },
    "players": {
      "added": [
        {
          "userId": "user456",
          "username": "player2",
          "position": 3
        }
      ]
    },
    "prizePool": {
      "from": 200,
      "to": 300
    }
  },
  "metadata": {
    "triggeredBy": "user456",
    "reason": "player_join",
    "serviceId": "game-service"
  }
}
```

## Implementation Strategy

### 1. Centralized State Management

**Game Service as Authority**:
```go
type RoomStateManager struct {
    repo        repositories.RoomRepository
    cache       *redis.Client
    streams     *redis.Client
    mutex       sync.RWMutex
    subscribers map[string][]chan StateChange
}

func (rsm *RoomStateManager) UpdateRoomState(ctx context.Context, roomID string, changes StateChanges) error {
    rsm.mutex.Lock()
    defer rsm.mutex.Unlock()
    
    // 1. Update database
    err := rsm.repo.UpdateRoom(ctx, roomID, changes)
    if err != nil {
        return err
    }
    
    // 2. Update cache
    newState, err := rsm.repo.GetRoom(ctx, roomID)
    if err != nil {
        return err
    }
    
    err = rsm.cache.Set(ctx, "room:state:"+roomID, newState, time.Hour)
    if err != nil {
        rsm.logger.Error("Failed to update cache", err)
    }
    
    // 3. Publish state change event
    event := StateChangeEvent{
        RoomID:    roomID,
        Version:   newState.Version,
        Changes:   changes,
        Timestamp: time.Now(),
    }
    
    err = rsm.streams.XAdd(ctx, &redis.XAddArgs{
        Stream: "room:" + roomID + ":state",
        Values: map[string]interface{}{
            "event": event,
        },
    })
    
    return err
}
```

### 2. Event-Driven Cache Invalidation

**Socket Gateway Consumer**:
```javascript
class RoomStateConsumer {
  constructor(redisClient) {
    this.redis = redisClient;
    this.localCache = new Map();
  }
  
  async startConsuming() {
    // Subscribe to all room state streams
    const streams = await this.redis.keys('room:*:state');
    
    for (const stream of streams) {
      this.consumeStream(stream);
    }
  }
  
  async consumeStream(streamName) {
    while (true) {
      try {
        const messages = await this.redis.xread(
          'BLOCK', 1000,
          'STREAMS', streamName, '$'
        );
        
        for (const message of messages) {
          await this.processStateChange(message);
        }
      } catch (error) {
        logger.error('Stream consumption error', error);
        await sleep(1000);
      }
    }
  }
  
  async processStateChange(message) {
    const { roomId, version, changes } = message.event;
    
    // Update local cache
    const currentState = this.localCache.get(roomId);
    if (!currentState || currentState.version < version) {
      const freshState = await this.fetchFreshState(roomId);
      this.localCache.set(roomId, freshState);
      
      // Notify subscribers
      this.notifySubscribers(roomId, changes);
    }
  }
  
  async fetchFreshState(roomId) {
    // Always fetch from Redis cache (authoritative)
    const cached = await this.redis.get(`room:state:${roomId}`);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // Fallback to Game Service API
    return await this.requestFromGameService(roomId);
  }
}
```

### 3. Optimistic Locking

**Version-Based Concurrency Control**:
```javascript
async function updateRoomWithOptimisticLock(roomId, updateFn) {
  let retries = 3;
  
  while (retries > 0) {
    // Get current state with version
    const currentState = await getRoomState(roomId);
    
    // Apply changes
    const newState = updateFn(currentState);
    newState.version = currentState.version + 1;
    
    try {
      // Conditional update based on version
      const success = await updateRoomIfVersionMatches(
        roomId, 
        newState, 
        currentState.version
      );
      
      if (success) {
        return newState;
      }
      
      // Version mismatch, retry
      retries--;
      await sleep(Math.random() * 100); // Random jitter
      
    } catch (error) {
      throw error;
    }
  }
  
  throw new Error('Failed to update room after retries');
}
```

## Caching Strategy

### Cache Hierarchy

1. **L1 Cache**: In-memory (Socket Gateway)
   - **TTL**: 30 seconds
   - **Size**: 1000 rooms max
   - **Eviction**: LRU

2. **L2 Cache**: Redis (Shared)
   - **TTL**: 1 hour
   - **Size**: Unlimited
   - **Eviction**: Event-driven

3. **L3 Storage**: Database (Game Service)
   - **TTL**: Permanent
   - **Consistency**: Strong

### Cache Warming

```javascript
class CacheWarmer {
  constructor(redisClient, gameServiceClient) {
    this.redis = redisClient;
    this.gameService = gameServiceClient;
  }
  
  async warmActiveRooms() {
    // Get list of active rooms
    const activeRooms = await this.gameService.getActiveRooms();
    
    // Pre-load into cache
    const promises = activeRooms.map(async (roomId) => {
      const state = await this.gameService.getRoomState(roomId);
      await this.redis.setex(
        `room:state:${roomId}`, 
        3600, 
        JSON.stringify(state)
      );
    });
    
    await Promise.all(promises);
    
    logger.info(`Warmed cache for ${activeRooms.length} rooms`);
  }
  
  // Run every 5 minutes
  startWarming() {
    setInterval(() => {
      this.warmActiveRooms().catch(logger.error);
    }, 5 * 60 * 1000);
  }
}
```

## Consistency Patterns

### 1. Eventually Consistent Reads

**Use Case**: Room list display, non-critical UI updates

```javascript
async function getRoomListEventuallyConsistent() {
  // Try cache first
  const cached = await redis.get('room:list');
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fallback to authoritative source
  const fresh = await gameService.getRoomList();
  
  // Update cache asynchronously
  redis.setex('room:list', 60, JSON.stringify(fresh))
    .catch(logger.error);
  
  return fresh;
}
```

### 2. Strong Consistency

**Use Case**: Game state changes, financial operations

```javascript
async function joinRoomStronglyConsistent(roomId, userId) {
  // Always go to authoritative source
  const result = await gameService.joinRoom(roomId, userId);
  
  // Invalidate all caches immediately
  await Promise.all([
    redis.del(`room:state:${roomId}`),
    redis.del(`room:list`),
    localCache.delete(roomId)
  ]);
  
  return result;
}
```

### 3. Read-Your-Writes Consistency

**Use Case**: User sees their own actions immediately

```javascript
class ReadYourWritesCache {
  constructor() {
    this.userWrites = new Map(); // userId -> Set of roomIds
  }
  
  async getRoomState(roomId, userId) {
    // Check if user has recent writes to this room
    const userRooms = this.userWrites.get(userId) || new Set();
    
    if (userRooms.has(roomId)) {
      // Force fresh read for user's own writes
      return await this.getFreshRoomState(roomId);
    }
    
    // Use cached version for other users' data
    return await this.getCachedRoomState(roomId);
  }
  
  async updateRoomState(roomId, userId, changes) {
    // Record user write
    if (!this.userWrites.has(userId)) {
      this.userWrites.set(userId, new Set());
    }
    this.userWrites.get(userId).add(roomId);
    
    // Clean up after 30 seconds
    setTimeout(() => {
      this.userWrites.get(userId)?.delete(roomId);
    }, 30000);
    
    return await gameService.updateRoom(roomId, changes);
  }
}
```

## Monitoring and Debugging

### Key Metrics

- **Cache Hit Rate**: Percentage of requests served from cache
- **State Consistency**: Version mismatches across services
- **Update Latency**: Time from state change to cache update
- **Memory Usage**: Cache memory consumption

### Debugging Tools

```javascript
// State consistency checker
async function checkStateConsistency(roomId) {
  const [dbState, cacheState, localState] = await Promise.all([
    gameService.getRoomState(roomId),
    redis.get(`room:state:${roomId}`).then(JSON.parse),
    localCache.get(roomId)
  ]);
  
  const inconsistencies = [];
  
  if (dbState.version !== cacheState?.version) {
    inconsistencies.push({
      type: 'version_mismatch',
      db: dbState.version,
      cache: cacheState?.version
    });
  }
  
  if (dbState.playerCount !== localState?.playerCount) {
    inconsistencies.push({
      type: 'player_count_mismatch', 
      db: dbState.playerCount,
      local: localState?.playerCount
    });
  }
  
  return {
    consistent: inconsistencies.length === 0,
    inconsistencies
  };
}
```

## Migration Plan

### Phase 1: Centralization (Week 1-2)
- Designate Game Service as single source of truth
- Implement Redis Streams for state changes
- Add version numbers to all state objects

### Phase 2: Event-Driven Updates (Week 3-4)
- Implement state change consumers in Socket Gateway
- Remove time-based cache invalidation
- Add optimistic locking for updates

### Phase 3: Consistency Guarantees (Week 5-6)
- Implement read-your-writes consistency
- Add state consistency monitoring
- Performance optimization

### Phase 4: Advanced Features (Week 7-8)
- Add state snapshots for debugging
- Implement state replay capability
- Advanced monitoring and alerting

## Related Documentation

- [Event System Architecture](./event-system-architecture.md)
- [Redis Channel Standards](./redis-channel-standards.md)
- [Performance Optimization Guide](./performance-optimization.md)
- [Game Flow Analysis](./game-flow-analysis.md)
