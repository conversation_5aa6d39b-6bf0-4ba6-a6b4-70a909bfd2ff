# Performance Optimization Guide

## Overview

This document provides comprehensive performance optimization strategies for the XZ Game platform, focusing on reducing latency, improving throughput, and optimizing resource utilization.

## Current Performance Issues

### Identified Bottlenecks

#### 1. Event Processing Latency

**Problem**: Complex event routing and processing delays

```javascript
// Current problematic pattern
handleRedisMessage(message, channel) {
  // Multiple conditional checks
  if (channel === 'socket:events') {
    this.handleGameServiceSocketEvent(message);
  } else if (channel.startsWith('game:room:')) {
    this.handleGameServiceRoomEvent(message, roomId);
  } else if (channel.startsWith('room:') && channel.endsWith(':events')) {
    this.handleGameRoomMessage(message, roomId);
  }
  // ... more conditions
}
```

**Impact**: 50-100ms additional latency per event

#### 2. Multiple Cache Layers

**Problem**: Cache misses cascade through multiple layers

```javascript
// Inefficient cache hierarchy
async getRoomState(roomId) {
  // L1: Local cache (30s TTL)
  let state = this.localCache.get(roomId);
  if (state) return state;
  
  // L2: Redis cache (1h TTL)  
  state = await redis.get(`room:state:${roomId}`);
  if (state) {
    this.localCache.set(roomId, state);
    return state;
  }
  
  // L3: Database query
  state = await gameService.getRoomState(roomId);
  // Update all caches...
}
```

**Impact**: 200-500ms for cache misses

#### 3. Synchronous Database Operations

**Problem**: Blocking operations in event handlers

```go
// Blocking database update
func (s *roomService) UpdateRoom(ctx context.Context, room *models.Room) error {
    // Synchronous MongoDB update
    _, err := s.collection.UpdateOne(ctx, bson.M{"_id": room.ID}, room)
    if err != nil {
        return err
    }
    
    // Synchronous cache update
    s.updateCache(room.ID.Hex(), room)
    
    // Synchronous event publishing
    return s.publishRoomEvent(ctx, "room_updated", room)
}
```

**Impact**: 100-300ms per room update

## Optimization Strategies

### 1. Event Processing Optimization

#### Fast Event Routing

```javascript
class OptimizedEventRouter {
  constructor() {
    // Pre-compiled routing table
    this.routes = new Map([
      ['socket:events', this.handleSocketEvent.bind(this)],
      ['lobby:events', this.handleLobbyEvent.bind(this)],
      ['global:events', this.handleGlobalEvent.bind(this)]
    ]);
    
    // Pattern-based routing for room events
    this.roomEventPattern = /^room:([^:]+):events$/;
  }
  
  routeEvent(channel, message) {
    // Direct lookup for exact matches
    const handler = this.routes.get(channel);
    if (handler) {
      return handler(message);
    }
    
    // Pattern matching for room events
    const roomMatch = channel.match(this.roomEventPattern);
    if (roomMatch) {
      return this.handleRoomEvent(roomMatch[1], message);
    }
    
    // Unknown channel
    logger.warn('Unknown channel', { channel });
  }
}
```

#### Async Event Processing

```javascript
class AsyncEventProcessor {
  constructor() {
    this.eventQueue = [];
    this.processing = false;
    this.batchSize = 100;
    this.batchTimeout = 10; // ms
  }
  
  async processEvent(event) {
    this.eventQueue.push(event);
    
    if (!this.processing) {
      this.processing = true;
      // Process in next tick to allow batching
      process.nextTick(() => this.processBatch());
    }
  }
  
  async processBatch() {
    while (this.eventQueue.length > 0) {
      const batch = this.eventQueue.splice(0, this.batchSize);
      
      // Process batch in parallel
      await Promise.all(
        batch.map(event => this.processEventAsync(event))
      );
    }
    
    this.processing = false;
  }
  
  async processEventAsync(event) {
    try {
      await this.handleEvent(event);
    } catch (error) {
      logger.error('Event processing error', { event, error });
      // Add to retry queue
      this.retryQueue.push(event);
    }
  }
}
```

### 2. Caching Optimization

#### Smart Cache Warming

```javascript
class SmartCacheWarmer {
  constructor(redis, gameService) {
    this.redis = redis;
    this.gameService = gameService;
    this.accessPatterns = new Map(); // Track access frequency
  }
  
  // Track cache access patterns
  recordAccess(key) {
    const count = this.accessPatterns.get(key) || 0;
    this.accessPatterns.set(key, count + 1);
  }
  
  // Predictive cache warming
  async warmPredictiveCache() {
    // Get most accessed rooms
    const hotRooms = Array.from(this.accessPatterns.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 50)
      .map(([key]) => key);
    
    // Pre-load hot rooms
    const promises = hotRooms.map(async (roomId) => {
      const state = await this.gameService.getRoomState(roomId);
      await this.redis.setex(
        `room:state:${roomId}`,
        1800, // 30 minutes for hot data
        JSON.stringify(state)
      );
    });
    
    await Promise.all(promises);
  }
  
  // Geographic cache warming
  async warmRegionalCache(region) {
    const regionalRooms = await this.gameService.getRoomsByRegion(region);
    
    // Warm cache for regional rooms
    const promises = regionalRooms.map(async (room) => {
      await this.redis.setex(
        `room:state:${room.id}`,
        3600,
        JSON.stringify(room)
      );
    });
    
    await Promise.all(promises);
  }
}
```

#### Cache Compression

```javascript
const zlib = require('zlib');

class CompressedCache {
  async set(key, value, ttl) {
    const serialized = JSON.stringify(value);
    
    // Compress if data is large
    if (serialized.length > 1024) {
      const compressed = await this.compress(serialized);
      await redis.setex(`${key}:compressed`, ttl, compressed);
    } else {
      await redis.setex(key, ttl, serialized);
    }
  }
  
  async get(key) {
    // Try compressed version first
    const compressed = await redis.get(`${key}:compressed`);
    if (compressed) {
      const decompressed = await this.decompress(compressed);
      return JSON.parse(decompressed);
    }
    
    // Fallback to uncompressed
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  }
  
  async compress(data) {
    return new Promise((resolve, reject) => {
      zlib.gzip(data, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
  }
  
  async decompress(data) {
    return new Promise((resolve, reject) => {
      zlib.gunzip(data, (err, result) => {
        if (err) reject(err);
        else resolve(result.toString());
      });
    });
  }
}
```

### 3. Database Optimization

#### Connection Pooling

```go
type OptimizedRoomService struct {
    dbPool    *mongo.Client
    redisPool *redis.Pool
    writePool chan *WriteOperation
    readPool  chan *ReadOperation
}

func NewOptimizedRoomService() *OptimizedRoomService {
    service := &OptimizedRoomService{
        writePool: make(chan *WriteOperation, 1000),
        readPool:  make(chan *ReadOperation, 2000),
    }
    
    // Start worker pools
    for i := 0; i < 10; i++ {
        go service.writeWorker()
    }
    for i := 0; i < 20; i++ {
        go service.readWorker()
    }
    
    return service
}

func (s *OptimizedRoomService) writeWorker() {
    for op := range s.writePool {
        err := s.executeWrite(op)
        op.result <- err
    }
}

func (s *OptimizedRoomService) readWorker() {
    for op := range s.readPool {
        result, err := s.executeRead(op)
        op.result <- ReadResult{Data: result, Error: err}
    }
}
```

#### Batch Operations

```go
type BatchProcessor struct {
    writes    []WriteOperation
    batchSize int
    timeout   time.Duration
    timer     *time.Timer
}

func (bp *BatchProcessor) AddWrite(op WriteOperation) {
    bp.writes = append(bp.writes, op)
    
    if len(bp.writes) >= bp.batchSize {
        bp.flush()
    } else if bp.timer == nil {
        bp.timer = time.AfterFunc(bp.timeout, bp.flush)
    }
}

func (bp *BatchProcessor) flush() {
    if len(bp.writes) == 0 {
        return
    }
    
    // Execute batch write
    err := bp.executeBatch(bp.writes)
    
    // Notify all operations
    for _, write := range bp.writes {
        write.result <- err
    }
    
    bp.writes = bp.writes[:0] // Clear slice
    if bp.timer != nil {
        bp.timer.Stop()
        bp.timer = nil
    }
}
```

### 4. Network Optimization

#### Connection Multiplexing

```javascript
class ConnectionMultiplexer {
  constructor() {
    this.connections = new Map();
    this.maxConnections = 10;
    this.currentConnection = 0;
  }
  
  getConnection(service) {
    const key = `${service}-${this.currentConnection}`;
    
    if (!this.connections.has(key)) {
      this.connections.set(key, this.createConnection(service));
    }
    
    // Round-robin connection selection
    this.currentConnection = (this.currentConnection + 1) % this.maxConnections;
    
    return this.connections.get(key);
  }
  
  createConnection(service) {
    const connection = new ServiceConnection(service, {
      keepAlive: true,
      maxSockets: 50,
      timeout: 5000
    });
    
    return connection;
  }
}
```

#### Request Batching

```javascript
class RequestBatcher {
  constructor() {
    this.batches = new Map();
    this.batchTimeout = 10; // ms
  }
  
  async batchRequest(service, operation, data) {
    const batchKey = `${service}:${operation}`;
    
    if (!this.batches.has(batchKey)) {
      this.batches.set(batchKey, {
        requests: [],
        timer: setTimeout(() => this.executeBatch(batchKey), this.batchTimeout)
      });
    }
    
    const batch = this.batches.get(batchKey);
    
    return new Promise((resolve, reject) => {
      batch.requests.push({
        data,
        resolve,
        reject
      });
    });
  }
  
  async executeBatch(batchKey) {
    const batch = this.batches.get(batchKey);
    if (!batch) return;
    
    this.batches.delete(batchKey);
    clearTimeout(batch.timer);
    
    try {
      const [service, operation] = batchKey.split(':');
      const results = await this.sendBatchRequest(
        service, 
        operation, 
        batch.requests.map(r => r.data)
      );
      
      // Resolve individual requests
      batch.requests.forEach((request, index) => {
        request.resolve(results[index]);
      });
      
    } catch (error) {
      // Reject all requests
      batch.requests.forEach(request => {
        request.reject(error);
      });
    }
  }
}
```

## Memory Optimization

### 1. Object Pooling

```javascript
class ObjectPool {
  constructor(createFn, resetFn, maxSize = 100) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.pool = [];
    this.maxSize = maxSize;
  }
  
  acquire() {
    if (this.pool.length > 0) {
      return this.pool.pop();
    }
    return this.createFn();
  }
  
  release(obj) {
    if (this.pool.length < this.maxSize) {
      this.resetFn(obj);
      this.pool.push(obj);
    }
  }
}

// Usage for event objects
const eventPool = new ObjectPool(
  () => ({ type: null, payload: null, timestamp: null }),
  (obj) => { obj.type = null; obj.payload = null; obj.timestamp = null; }
);
```

### 2. Memory Monitoring

```javascript
class MemoryMonitor {
  constructor() {
    this.thresholds = {
      warning: 0.8,  // 80% of heap
      critical: 0.9  // 90% of heap
    };
    
    setInterval(() => this.checkMemory(), 30000);
  }
  
  checkMemory() {
    const usage = process.memoryUsage();
    const heapUsedRatio = usage.heapUsed / usage.heapTotal;
    
    if (heapUsedRatio > this.thresholds.critical) {
      logger.error('Critical memory usage', { usage, ratio: heapUsedRatio });
      this.triggerGarbageCollection();
    } else if (heapUsedRatio > this.thresholds.warning) {
      logger.warn('High memory usage', { usage, ratio: heapUsedRatio });
    }
  }
  
  triggerGarbageCollection() {
    if (global.gc) {
      global.gc();
      logger.info('Forced garbage collection');
    }
  }
}
```

## Performance Monitoring

### Key Metrics

```javascript
class PerformanceMetrics {
  constructor() {
    this.metrics = {
      eventProcessingTime: new Histogram(),
      cacheHitRate: new Counter(),
      databaseQueryTime: new Histogram(),
      memoryUsage: new Gauge(),
      connectionCount: new Gauge()
    };
  }
  
  recordEventProcessing(duration) {
    this.metrics.eventProcessingTime.observe(duration);
  }
  
  recordCacheHit(hit) {
    this.metrics.cacheHitRate.inc({ result: hit ? 'hit' : 'miss' });
  }
  
  recordDatabaseQuery(duration) {
    this.metrics.databaseQueryTime.observe(duration);
  }
  
  updateMemoryUsage() {
    const usage = process.memoryUsage();
    this.metrics.memoryUsage.set(usage.heapUsed);
  }
}
```

### Performance Testing

```javascript
class PerformanceTest {
  async testEventThroughput() {
    const events = this.generateTestEvents(10000);
    const startTime = Date.now();
    
    await Promise.all(
      events.map(event => this.processEvent(event))
    );
    
    const duration = Date.now() - startTime;
    const throughput = events.length / (duration / 1000);
    
    logger.info('Event throughput test', {
      events: events.length,
      duration,
      throughput: `${throughput.toFixed(2)} events/sec`
    });
  }
  
  async testCachePerformance() {
    const keys = this.generateTestKeys(1000);
    
    // Warm cache
    await Promise.all(
      keys.map(key => this.cache.set(key, { data: 'test' }))
    );
    
    // Test read performance
    const startTime = Date.now();
    await Promise.all(
      keys.map(key => this.cache.get(key))
    );
    
    const duration = Date.now() - startTime;
    const avgLatency = duration / keys.length;
    
    logger.info('Cache performance test', {
      operations: keys.length,
      duration,
      avgLatency: `${avgLatency.toFixed(2)}ms`
    });
  }
}
```

## Optimization Checklist

### High Impact (Immediate)
- [ ] Implement event routing optimization
- [ ] Add connection pooling
- [ ] Enable Redis pipelining
- [ ] Implement request batching
- [ ] Add cache compression

### Medium Impact (Short-term)
- [ ] Optimize database queries
- [ ] Implement smart cache warming
- [ ] Add object pooling
- [ ] Optimize memory usage
- [ ] Implement circuit breakers

### Low Impact (Long-term)
- [ ] Geographic distribution
- [ ] Advanced caching strategies
- [ ] Machine learning for predictive caching
- [ ] Custom serialization formats
- [ ] Hardware-specific optimizations

## Expected Performance Gains

| Optimization | Latency Improvement | Throughput Improvement |
|--------------|-------------------|----------------------|
| Event Routing | 30-50% | 20-30% |
| Cache Optimization | 40-60% | 25-40% |
| Database Pooling | 20-40% | 30-50% |
| Request Batching | 50-70% | 40-60% |
| Memory Optimization | 10-20% | 15-25% |

## Related Documentation

- [Event System Architecture](./event-system-architecture.md)
- [Room State Management](./room-state-management.md)
- [Redis Channel Standards](./redis-channel-standards.md)
- [Game Flow Analysis](./game-flow-analysis.md)
