# Game Flow TODO List - Prioritized Action Items

## Overview

This document provides a prioritized list of specific fixes needed to improve game flow clarity and reliability based on the comprehensive analysis of the codebase.

## Priority Classification

- **🔴 HIGH**: Critical issues affecting user experience or causing synchronization problems
- **🟡 MEDIUM**: Important improvements for maintainability and reliability
- **🟢 LOW**: Nice-to-have improvements for code quality and operations

---

## 🔴 HIGH PRIORITY ISSUES

### 1. Event Channel Inconsistency and Confusion

**Issue**: Multiple Redis channel patterns are used inconsistently across services

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 189-216)
- `services/game-service/internal/services/room_service.go` (lines 1753-1774)
- `services/game-service/internal/services/request_handler.go` (lines 2894-2899)

**Current Problem**:
```javascript
// Socket Gateway handles multiple channel patterns
if (channel === 'socket:events') {
  this.handleGameServiceSocketEvent(message);
} else if (channel.startsWith('game:room:')) {
  this.handleGameServiceRoomEvent(message, roomId);
} else if (channel.startsWith('room:') && channel.endsWith(':events')) {
  this.handleGameRoomMessage(message, roomId);
}
```

**Recommended Solution**:
1. **Standardize Channel Pattern**: Use single pattern `room:{roomId}:events` for all room events
2. **Create Channel Registry**: Document all channels and their purposes
3. **Migration Plan**: Gradually migrate from old patterns to new standard

**Implementation Steps**:
1. Create `services/docs/redis-channel-standards.md`
2. Update Game Service to use standard pattern only
3. Update Socket Gateway to handle standard pattern
4. Remove legacy channel handlers
5. Update Manager Service notification patterns

**Estimated Effort**: 2-3 days
**Risk Level**: Medium (requires coordination across services)

### 2. Race Condition in Room Info Updates During Join Flow

**Issue**: Complex timing dependencies between join response and room_info_updated events

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 984-1011, 821-858)

**Current Problem**:
```javascript
// Fragile timing workaround
process.nextTick(async () => {
  await this.sendCurrentRoomInfo(socket, roomId);
  await this.broadcastRoomInfoToOtherSubscribers(socket, roomId, 'player_subscribed');
});
```

**Recommended Solution**:
1. **Event Sequence Numbers**: Add sequence numbers to all events
2. **Ordered Event Queue**: Implement Redis Streams for guaranteed ordering
3. **Single Event Source**: Game Service becomes sole publisher of room_info_updated

**Implementation Steps**:
1. Add sequence number field to all events
2. Implement Redis Streams in Game Service
3. Update Socket Gateway to consume ordered streams
4. Remove complex timing workarounds
5. Add event replay capability for missed events

**Estimated Effort**: 3-4 days
**Risk Level**: High (affects core game flow)

### 3. Duplicate Event Sources and Disabled Events

**Issue**: Events are disabled in multiple places indicating architectural problems

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 3318-3324)
- `services/game-service/internal/services/room_service.go` (lines 1555-1560)

**Current Problem**:
```javascript
// Disabled events suggest design issues
case 'player_joined':
  // DISABLED: player_joined events are no longer processed
  logger.debug('player_joined event received but handler disabled');
  break;
```

**Recommended Solution**:
1. **Event Ownership Matrix**: Define which service owns which events
2. **Remove Disabled Handlers**: Clean up all disabled event code
3. **Single Source Publishing**: Each event type published by only one service

**Implementation Steps**:
1. Create event ownership documentation
2. Remove all disabled event handlers
3. Consolidate event publishing to single sources
4. Update tests to reflect new event flow
5. Add monitoring for event consistency

**Estimated Effort**: 2 days
**Risk Level**: Medium (cleanup with potential for missed edge cases)

---

## 🟡 MEDIUM PRIORITY ISSUES

### 4. Complex State Management with Multiple Caches

**Issue**: Room state is cached in multiple places with different TTLs and invalidation strategies

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 30-52, 821-829)
- `services/socket-gateway/src/services/roomStateManager.js` (lines 163-214)

**Current Problem**:
```javascript
// Multiple cache layers with different strategies
this.recentRoomInfo = new Map(); // Short-term cache
this.roomStates = new Map(); // Long-term cache with TTL
// Plus Redis cache in Game Service
```

**Recommended Solution**:
1. **Single Cache Strategy**: Use Redis as single source of truth
2. **Cache Invalidation Events**: Publish cache invalidation events
3. **Consistent TTL**: Standardize cache TTL across all services

**Implementation Steps**:
1. Remove local caches from Socket Gateway
2. Implement Redis-based caching with events
3. Add cache invalidation event publishing
4. Update all services to use consistent caching
5. Add cache hit/miss metrics

**Estimated Effort**: 3 days
**Risk Level**: Medium (performance impact during transition)

### 5. Inconsistent Error Handling Patterns

**Issue**: Different error handling approaches across similar operations

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 591-633, 1012-1027)

**Current Problem**:
```javascript
// Inconsistent error patterns
if (callback) callback(errorResponse); // Sometimes
throw new Error('Something failed'); // Other times
socket.emit('error', errorData); // Sometimes
```

**Recommended Solution**:
1. **Standardize Error Format**: Common error response structure
2. **Error Handling Middleware**: Centralized error processing
3. **Error Classification**: Categorize errors by type and severity

**Implementation Steps**:
1. Define standard error response format
2. Create error handling middleware
3. Update all socket handlers to use standard patterns
4. Add error metrics and monitoring
5. Update client-side error handling

**Estimated Effort**: 2 days
**Risk Level**: Low (mostly refactoring)

### 6. Game-Specific Logic Scattered Across Generic Components

**Issue**: Prize Wheel specific logic mixed with generic room handling

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 918-927, 1140-1152)

**Current Problem**:
```javascript
// Game-specific logic in generic handler
if (roomInfo.room.gameType === 'prizewheel' || roomInfo.room.gameType === 'prize_wheel') {
  // Special Prize Wheel handling
  await this.requestGameServiceRoomInfoWithColorState(socket, roomId);
  return;
}
```

**Recommended Solution**:
1. **Game Handler Registry**: Plugin system for game-specific logic
2. **Extract Game Modules**: Separate modules for each game type
3. **Generic Interface**: Common interface for all game handlers

**Implementation Steps**:
1. Create game handler interface
2. Extract Prize Wheel logic to separate module
3. Create Amidakuji handler module
4. Implement handler registry system
5. Update generic handlers to use registry

**Estimated Effort**: 3 days
**Risk Level**: Low (refactoring with clear interfaces)

---

## 🟢 LOW PRIORITY ISSUES

### 7. Verbose Logging and Debug Code in Production

**Issue**: Extensive debug logging affecting performance

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 181-186)

**Current Problem**:
```javascript
// Always-on debug logging
logger.info('🔍 DEBUG: Redis message received in socket-gateway', {
  channel,
  messageType: message.event?.type,
  eventPayload: message.event?.payload,
});
```

**Recommended Solution**:
1. **Conditional Logging**: Environment-based log levels
2. **Structured Logging**: Consistent log format across services
3. **Log Sampling**: Sample high-frequency logs

**Implementation Steps**:
1. Add log level configuration
2. Update all debug logs to be conditional
3. Implement log sampling for high-frequency events
4. Add log aggregation and analysis tools
5. Create logging best practices guide

**Estimated Effort**: 1 day
**Risk Level**: Very Low (operational improvement)

### 8. Hardcoded Timeouts and Magic Numbers

**Issue**: Hardcoded values make system behavior hard to tune

**Files Affected**:
- `services/socket-gateway/src/services/socketService.js` (lines 28, 44, 866)

**Current Problem**:
```javascript
// Magic numbers throughout code
this.roomInfoDeduplicationTTL = 2000; // 2 seconds
const maxAge = 10000; // 10 seconds
await new Promise(resolve => setTimeout(resolve, 50)); // 50ms delay
```

**Recommended Solution**:
1. **Configuration Management**: Move all timeouts to config files
2. **Environment-Specific Values**: Different values for dev/staging/prod
3. **Runtime Configuration**: Allow runtime configuration updates

**Implementation Steps**:
1. Extract all magic numbers to configuration
2. Create environment-specific config files
3. Add configuration validation
4. Implement runtime configuration updates
5. Document all configuration options

**Estimated Effort**: 1 day
**Risk Level**: Very Low (configuration improvement)

### 9. Inconsistent Subscription State Management

**Issue**: Complex subscription tracking with multiple data structures

**Files Affected**:
- `services/socket-gateway/src/services/subscriptionService.js` (lines 10-21)

**Current Problem**:
```javascript
// Multiple maps for similar data
this.userSubscriptions = new Map(); // userId -> subscription
this.socketSubscriptions = new Map(); // socketId -> subscription  
this.roomSubscribers = new Map(); // roomId -> Set of socketIds
this.lobbySubscribers = new Set(); // Set of socketIds
```

**Recommended Solution**:
1. **Unified Subscription Model**: Single data structure for all subscriptions
2. **Redis-Based State**: Move subscription state to Redis for clustering
3. **Subscription Events**: Publish subscription change events

**Implementation Steps**:
1. Design unified subscription model
2. Implement Redis-based subscription storage
3. Add subscription change events
4. Update all subscription operations
5. Add subscription state monitoring

**Estimated Effort**: 2 days
**Risk Level**: Low (internal refactoring)

---

## Implementation Roadmap

### Phase 1: Critical Fixes (Week 1)
- [ ] Standardize Redis channel patterns
- [ ] Fix race conditions in join flow
- [ ] Remove disabled event handlers

### Phase 2: Architecture Improvements (Week 2)
- [ ] Implement single cache strategy
- [ ] Standardize error handling
- [ ] Extract game-specific logic

### Phase 3: Quality Improvements (Week 3)
- [ ] Optimize logging
- [ ] Move hardcoded values to config
- [ ] Simplify subscription management

### Phase 4: Monitoring and Documentation (Week 4)
- [ ] Add comprehensive monitoring
- [ ] Create operational runbooks
- [ ] Performance optimization

## Success Metrics

- **Event Ordering**: 100% of events received in correct order
- **Cache Hit Rate**: >95% for room state queries
- **Error Rate**: <0.1% for room operations
- **Response Time**: <100ms for room join operations
- **Code Complexity**: Reduce cyclomatic complexity by 30%

## Risk Mitigation

- **Feature Flags**: Use feature flags for major changes
- **Gradual Rollout**: Deploy changes incrementally
- **Monitoring**: Enhanced monitoring during transitions
- **Rollback Plan**: Quick rollback procedures for each change
- **Testing**: Comprehensive testing before production deployment
