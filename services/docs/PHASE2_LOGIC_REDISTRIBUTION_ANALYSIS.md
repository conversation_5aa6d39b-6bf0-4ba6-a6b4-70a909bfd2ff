# Phase 2: Service Logic Redistribution Analysis

## Current State Analysis

### Large Files Requiring Breakdown
1. **game-service/room_service.go** (3,227 lines) ❌
2. **game-service/game_service.go** (1,078 lines) ❌

### Logic Distribution Issues Identified

#### 1. Room Management Logic (Currently in Game Service)
**Should be moved to Room Service:**

**From `room_service.go` (3,227 lines):**
- ✅ Room CRUD operations (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oom, <PERSON>dateRoom, <PERSON>eteRoom)
- ✅ Room validation and capacity management
- ✅ Player join/leave operations
- ✅ Room state management and caching
- ✅ Room lifecycle management
- ❌ **ISSUE**: All this logic is in game-service instead of room-service

**Current Violations:**
- Room Service (separate microservice) is minimal while Game Service handles all room logic
- Single Responsibility Principle violated - Game Service doing room management
- Service boundaries unclear - room operations scattered

#### 2. Game Logic (Currently in Game Service)
**Should be moved to Game Engine Service:**

**From `game_service.go` (1,078 lines):**
- Game algorithms (Prize Wheel, Amidakuji) - Lines 404-415, 500-600+
- Random number generation and fairness - Lines 600-800+
- Game result calculation - Lines 417-464
- Game session execution - Lines 84-134
- Payout calculations - Lines 428-464

**Current Violations:**
- Game Engine Service is minimal while Game Service contains all game algorithms
- Business logic duplication risk
- Game-specific algorithms should be centralized in Game Engine Service

#### 3. Notification Logic (Currently in Game Service)
**Should be moved to Notification Service:**

**From both files:**
- Event broadcasting and publishing - Multiple locations
- Real-time notification management
- Subscription management for room events
- Event routing and delivery

**Current Violations:**
- Notification Service is underutilized
- Event publishing scattered across services
- No centralized notification management

## Redistribution Plan

### Priority 1: Move Room Logic to Room Service

#### Target Files to Create/Enhance:
```
room-service/internal/services/
├── room_management.go (< 800 lines)
├── room_validation.go (< 400 lines)
├── room_lifecycle.go (< 500 lines)
├── player_operations.go (< 600 lines)
├── room_caching.go (< 300 lines)
└── room_events.go (< 400 lines)
```

#### Logic to Move:
1. **Room CRUD Operations** (Lines 55-400 from room_service.go)
2. **Room Validation Logic** (Lines 490-800 from room_service.go)
3. **Player Join/Leave Operations** (Lines 461-1500 from room_service.go)
4. **Room State Management** (Lines 127-254 from room_service.go)
5. **Room Caching Logic** (Lines 175-264 from room_service.go)
6. **Room Event Publishing** (Lines 103-122, 378-396 from room_service.go)

### Priority 2: Move Game Logic to Game Engine Service

#### Target Files to Create/Enhance:
```
game-engine-service/internal/engines/
├── prize_wheel_engine.go (< 400 lines)
├── amidakuji_engine.go (< 400 lines)
├── random_generator.go (< 300 lines)
├── fairness_proof.go (< 300 lines)
└── result_calculator.go (< 400 lines)
```

#### Logic to Move:
1. **Prize Wheel Algorithm** (Lines 500-700 from game_service.go)
2. **Amidakuji Algorithm** (Lines 700-900 from game_service.go)
3. **Random Number Generation** (Lines 900-1000 from game_service.go)
4. **Game Result Calculation** (Lines 417-464 from game_service.go)
5. **Payout Calculation** (Lines 428-464 from game_service.go)

### Priority 3: Move Notification Logic to Notification Service

#### Target Files to Create/Enhance:
```
notification-service/internal/services/
├── event_broadcaster.go (< 400 lines)
├── subscription_manager.go (< 300 lines)
├── notification_router.go (< 300 lines)
└── real_time_notifier.go (< 400 lines)
```

#### Logic to Move:
1. **Event Broadcasting** (Multiple locations in both files)
2. **Subscription Management** (Room event subscriptions)
3. **Real-time Notifications** (Player notifications, room updates)
4. **Event Routing** (Channel-based event distribution)

## Implementation Strategy

### Step 1: Create Service Interfaces
Define clear gRPC interfaces between services:

```protobuf
// room-service/proto/room_service.proto
service RoomService {
  rpc CreateRoom(CreateRoomRequest) returns (Room);
  rpc GetRoom(GetRoomRequest) returns (Room);
  rpc UpdateRoom(UpdateRoomRequest) returns (Room);
  rpc DeleteRoom(DeleteRoomRequest) returns (Empty);
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse);
}

// game-engine-service/proto/game_engine.proto
service GameEngineService {
  rpc ExecuteGame(ExecuteGameRequest) returns (GameResult);
  rpc GenerateRandom(RandomRequest) returns (RandomResponse);
  rpc CalculateResults(CalculateRequest) returns (CalculateResponse);
  rpc ValidateFairness(FairnessRequest) returns (FairnessResponse);
}

// notification-service/proto/notification.proto
service NotificationService {
  rpc BroadcastEvent(BroadcastRequest) returns (BroadcastResponse);
  rpc Subscribe(SubscribeRequest) returns (SubscribeResponse);
  rpc Unsubscribe(UnsubscribeRequest) returns (UnsubscribeResponse);
  rpc SendNotification(NotificationRequest) returns (NotificationResponse);
}
```

### Step 2: Break Down Large Files
Split the large files following the modular pattern established in Phase 1:

#### Room Service Breakdown:
- `room_service.go` (3,227 lines) → 6 focused modules (< 800 lines each)

#### Game Service Breakdown:
- `game_service.go` (1,078 lines) → 3 focused modules (< 400 lines each)

### Step 3: Implement Service Communication
- Update Game Service to call Room Service for room operations
- Update Game Service to call Game Engine Service for game logic
- Update all services to use Notification Service for events

### Step 4: Data Migration and Consistency
- Ensure data models are consistent across services
- Implement proper error handling and timeouts
- Add circuit breakers for service-to-service communication

## Success Metrics

### File Size Compliance
- ✅ All files under 1,000 lines
- ✅ Most files under 500 lines for better maintainability

### Service Responsibility
- ✅ Room Service handles all room-related operations
- ✅ Game Engine Service handles all game algorithms
- ✅ Notification Service handles all event broadcasting
- ✅ Game Service orchestrates between services

### Performance
- ✅ No degradation in response times
- ✅ Proper caching strategies maintained
- ✅ Efficient service-to-service communication

### Testing
- ✅ All existing functionality preserved
- ✅ Integration tests pass
- ✅ Service communication tests added

## Risk Mitigation

### Data Consistency
- Implement distributed transactions where needed
- Use event sourcing for critical operations
- Add compensation patterns for rollbacks

### Service Availability
- Implement circuit breakers
- Add retry mechanisms with exponential backoff
- Ensure graceful degradation

### Performance
- Maintain caching strategies
- Optimize service-to-service calls
- Monitor latency and throughput

## Next Steps

1. **Start with Room Service Migration** (Highest Impact)
2. **Implement Game Engine Service Enhancement** (Medium Impact)
3. **Enhance Notification Service** (Medium Impact)
4. **Update Game Service to Orchestrator Role** (Final Step)
5. **Comprehensive Integration Testing** (Validation)

This redistribution will result in a clean microservices architecture where each service has a single, clear responsibility and all files comply with the 1,000-line limit.
