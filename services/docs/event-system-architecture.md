# Event System Architecture

## Overview

This document describes the event-driven architecture of the XZ Game platform, focusing on event flow, ordering guarantees, and consistency patterns.

## Current Architecture

### Event Flow Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        C1[Client 1]
        C2[Client 2]
        C3[Client N]
    end
    
    subgraph "Socket Gateway"
        SG[Socket Service]
        SS[Subscription Service]
        RS[Room State Manager]
    end
    
    subgraph "Game Service"
        GS[Game Service]
        GSM[Game State Manager]
        RNS[Room Notification Service]
    end
    
    subgraph "Manager Service"
        MS[Manager Service]
        NS[Notification Service]
        BS[Balance Service]
    end
    
    subgraph "Redis Infrastructure"
        R[(Redis)]
        RC[Redis Channels]
        RS_CACHE[Redis Cache]
    end
    
    C1 <--> SG
    C2 <--> SG
    C3 <--> SG
    
    SG <--> R
    GS <--> R
    MS <--> R
    
    SG --> GS
    SG --> MS
    GS --> MS
```

### Event Categories

#### 1. Room Events
- **Scope**: Room-specific operations
- **Examples**: player_joined, player_left, game_started
- **Publishers**: Game Service
- **Subscribers**: Socket Gateway, Manager Service

#### 2. User Events  
- **Scope**: User-specific operations
- **Examples**: balance_updated, notification_received
- **Publishers**: Manager Service
- **Subscribers**: Socket Gateway, Notification Service

#### 3. Game Events
- **Scope**: Game state changes
- **Examples**: wheel_spinning, countdown_started
- **Publishers**: Game Service
- **Subscribers**: Socket Gateway

#### 4. System Events
- **Scope**: System-wide operations
- **Examples**: maintenance_mode, service_restart
- **Publishers**: Any service
- **Subscribers**: All services

## Event Ordering and Consistency

### Current Issues

#### 1. Race Conditions

**Problem**: Events can arrive out of order due to async processing

```javascript
// Problematic pattern
socket.emit('join_room_response', response);
process.nextTick(() => {
  socket.emit('room_info_updated', roomInfo);
});
```

**Impact**: Clients receive events in wrong order

#### 2. Duplicate Events

**Problem**: Multiple services publishing same event types

```javascript
// Game Service publishes
publishRoomEvent('player_joined', payload);

// Socket Gateway also publishes  
socket.to(roomId).emit('player_joined', payload);
```

**Impact**: Clients receive duplicate events

### Proposed Solutions

#### 1. Event Sequencing

**Implementation**: Add sequence numbers to all events

```json
{
  "event": {
    "type": "player_joined",
    "sequenceNumber": 1234,
    "payload": {...}
  },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "serviceId": "game-service"
  }
}
```

**Benefits**:
- Guaranteed event ordering
- Duplicate detection
- Event replay capability

#### 2. Single Source of Truth

**Pattern**: Each event type has one authoritative publisher

| Event Type | Publisher | Reason |
|------------|-----------|---------|
| player_joined | Game Service | Authoritative game state |
| player_left | Game Service | Authoritative game state |
| balance_updated | Manager Service | Authoritative balance |
| room_info_updated | Game Service | Authoritative room state |

#### 3. Event Sourcing

**Implementation**: Store all events in ordered log

```javascript
// Event store structure
{
  "streamId": "room:507f1f77bcf86cd799439011",
  "events": [
    {
      "sequenceNumber": 1,
      "eventType": "room_created",
      "timestamp": "2024-01-15T10:00:00Z",
      "payload": {...}
    },
    {
      "sequenceNumber": 2, 
      "eventType": "player_joined",
      "timestamp": "2024-01-15T10:01:00Z",
      "payload": {...}
    }
  ]
}
```

**Benefits**:
- Complete audit trail
- Event replay for debugging
- State reconstruction
- Temporal queries

## Event Processing Patterns

### 1. Fire and Forget

**Use Case**: Non-critical notifications

```javascript
// Publisher
redis.publish('notifications:events', event);

// Subscriber  
redis.subscribe('notifications:events', (message) => {
  // Process without acknowledgment
  processNotification(message);
});
```

**Characteristics**:
- High performance
- No delivery guarantees
- Suitable for non-critical events

### 2. At-Least-Once Delivery

**Use Case**: Critical game events

```javascript
// Publisher with retry
async function publishWithRetry(channel, event, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await redis.publish(channel, event);
      return;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await sleep(Math.pow(2, i) * 1000); // Exponential backoff
    }
  }
}

// Subscriber with acknowledgment
redis.subscribe('critical:events', async (message) => {
  try {
    await processEvent(message);
    await redis.publish('ack:channel', { messageId: message.id });
  } catch (error) {
    await redis.publish('nack:channel', { messageId: message.id, error });
  }
});
```

**Characteristics**:
- Delivery guarantees
- Duplicate handling required
- Higher latency

### 3. Exactly-Once Delivery

**Use Case**: Financial transactions

```javascript
// Idempotent event processing
async function processTransaction(event) {
  const transactionId = event.payload.transactionId;
  
  // Check if already processed
  const existing = await redis.get(`processed:${transactionId}`);
  if (existing) {
    return existing; // Already processed
  }
  
  // Process transaction
  const result = await executeTransaction(event.payload);
  
  // Mark as processed
  await redis.setex(`processed:${transactionId}`, 3600, result);
  
  return result;
}
```

**Characteristics**:
- No duplicates
- Idempotent processing
- Complex implementation

## Event Schema and Validation

### Schema Definition

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["event", "metadata"],
  "properties": {
    "event": {
      "type": "object",
      "required": ["type", "timestamp"],
      "properties": {
        "type": {
          "type": "string",
          "pattern": "^[a-z_]+$"
        },
        "timestamp": {
          "type": "string",
          "format": "date-time"
        },
        "sequenceNumber": {
          "type": "integer",
          "minimum": 1
        },
        "payload": {
          "type": "object"
        }
      }
    },
    "metadata": {
      "type": "object",
      "required": ["serviceId", "version"],
      "properties": {
        "serviceId": {
          "type": "string",
          "enum": ["socket-gateway", "game-service", "manager-service"]
        },
        "version": {
          "type": "string",
          "pattern": "^\\d+\\.\\d+\\.\\d+$"
        },
        "correlationId": {
          "type": "string"
        },
        "priority": {
          "type": "integer",
          "minimum": 1,
          "maximum": 5
        }
      }
    }
  }
}
```

### Validation Implementation

```javascript
const Ajv = require('ajv');
const addFormats = require('ajv-formats');

const ajv = new Ajv();
addFormats(ajv);

const eventSchema = require('./schemas/event-schema.json');
const validateEvent = ajv.compile(eventSchema);

function publishEvent(channel, event) {
  // Validate event format
  if (!validateEvent(event)) {
    throw new Error(`Invalid event format: ${ajv.errorsText(validateEvent.errors)}`);
  }
  
  // Add metadata if missing
  if (!event.metadata.timestamp) {
    event.metadata.timestamp = new Date().toISOString();
  }
  
  return redis.publish(channel, JSON.stringify(event));
}
```

## Error Handling and Recovery

### Error Categories

#### 1. Transient Errors
- Network timeouts
- Redis connection issues
- Temporary service unavailability

**Handling**: Retry with exponential backoff

#### 2. Permanent Errors
- Invalid message format
- Schema validation failures
- Authorization errors

**Handling**: Dead letter queue

#### 3. Processing Errors
- Business logic failures
- Data consistency issues
- External service errors

**Handling**: Compensating actions

### Dead Letter Queue

```javascript
async function processEventWithDLQ(event) {
  try {
    await processEvent(event);
  } catch (error) {
    if (isRetryableError(error)) {
      // Retry with backoff
      await scheduleRetry(event, error);
    } else {
      // Send to dead letter queue
      await redis.lpush('dlq:events', JSON.stringify({
        originalEvent: event,
        error: error.message,
        timestamp: new Date().toISOString(),
        attempts: event.attempts || 1
      }));
    }
  }
}
```

### Circuit Breaker

```javascript
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.threshold = threshold;
    this.timeout = timeout;
    this.failureCount = 0;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.nextAttempt = Date.now();
  }
  
  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttempt) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }
  
  onFailure() {
    this.failureCount++;
    if (this.failureCount >= this.threshold) {
      this.state = 'OPEN';
      this.nextAttempt = Date.now() + this.timeout;
    }
  }
}
```

## Performance Considerations

### Throughput Optimization

1. **Connection Pooling**: Reuse Redis connections
2. **Batching**: Group related events
3. **Compression**: Compress large payloads
4. **Pipelining**: Use Redis pipelining for bulk operations

### Latency Optimization

1. **Local Caching**: Cache frequently accessed data
2. **Async Processing**: Non-blocking event processing
3. **Priority Queues**: Process high-priority events first
4. **Geographic Distribution**: Regional Redis clusters

### Memory Management

1. **TTL Settings**: Set appropriate expiration times
2. **Event Pruning**: Remove old events from streams
3. **Compression**: Use efficient serialization formats
4. **Monitoring**: Track memory usage patterns

## Monitoring and Observability

### Key Metrics

- **Event Throughput**: Events per second by type
- **Processing Latency**: Time from publish to process
- **Error Rate**: Failed events percentage
- **Queue Depth**: Pending events count
- **Duplicate Rate**: Duplicate events percentage

### Alerting Rules

```yaml
alerts:
  - name: HighEventErrorRate
    condition: error_rate > 0.01
    duration: 5m
    severity: critical
    
  - name: EventProcessingLatency
    condition: p95_latency > 1000ms
    duration: 2m
    severity: warning
    
  - name: EventQueueBacklog
    condition: queue_depth > 1000
    duration: 1m
    severity: warning
```

### Distributed Tracing

```javascript
const opentelemetry = require('@opentelemetry/api');

function publishEventWithTracing(channel, event) {
  const span = opentelemetry.trace.getActiveSpan();
  
  // Add trace context to event
  event.metadata.traceId = span?.spanContext().traceId;
  event.metadata.spanId = span?.spanContext().spanId;
  
  return redis.publish(channel, JSON.stringify(event));
}
```

## Migration Strategy

### Phase 1: Foundation (Week 1-2)
- Implement event schema validation
- Add sequence numbers to events
- Create monitoring infrastructure

### Phase 2: Ordering (Week 3-4)
- Implement event sequencing
- Add duplicate detection
- Create event replay capability

### Phase 3: Consistency (Week 5-6)
- Establish single sources of truth
- Remove duplicate event publishers
- Implement compensating actions

### Phase 4: Optimization (Week 7-8)
- Performance tuning
- Advanced monitoring
- Documentation and training

## Related Documentation

- [Redis Channel Standards](./redis-channel-standards.md)
- [Game Flow Analysis](./game-flow-analysis.md)
- [Performance Optimization Guide](./performance-optimization.md)
- [Troubleshooting Guide](./knowledge-base/10-troubleshooting.md)
