# Game Flow Analysis and Issues

## Overview

This document provides a comprehensive analysis of the game flow logic across all services in the XZ Game platform, identifying potential issues, race conditions, and areas for improvement.

## Executive Summary

The current game flow implementation works but has grown complex with multiple workarounds for timing and consistency issues. The analysis reveals several architectural concerns that impact reliability and maintainability.

### Key Findings

- **Event Channel Inconsistency**: Multiple Redis channel patterns used inconsistently
- **Race Conditions**: Complex timing dependencies in room join/subscription flow
- **Disabled Events**: Multiple event handlers disabled due to duplicate event issues
- **Complex State Management**: Room state cached in multiple places with different strategies
- **Game-Specific Logic Scattered**: Prize Wheel logic mixed with generic components

## Architecture Overview

```mermaid
graph TB
    Client[Client Application]
    SG[Socket Gateway]
    GS[Game Service]
    MS[Manager Service]
    Redis[(Redis)]
    
    Client <--> SG
    SG <--> Redis
    GS <--> Redis
    MS <--> Redis
    
    SG --> |gRPC/HTTP| GS
    SG --> |HTTP| MS
    GS --> |HTTP| MS
```

## Current Event Flow

### Room Join Process

1. **Client Request**: `join_room` event to Socket Gateway
2. **Authentication**: Socket Gateway validates user token
3. **Game Service Request**: Socket Gateway forwards to Game Service via Redis
4. **State Management**: Room state updated in multiple services
5. **Response Flow**: Success response sent before room events
6. **Event Broadcasting**: Room info updates sent to subscribers

### Issues Identified

#### 1. Event Timing Race Conditions

**Problem**: Complex timing dependencies between join response and room_info_updated events

```javascript
// Current problematic pattern
process.nextTick(async () => {
  await this.sendCurrentRoomInfo(socket, roomId);
  await this.broadcastRoomInfoToOtherSubscribers(socket, roomId, 'player_subscribed');
});
```

**Impact**: Clients may receive events out of order

#### 2. Multiple Redis Channel Patterns

**Current Channels**:
- `socket:events` - Socket Gateway events
- `game:room:{roomId}` - Legacy room events
- `room:{roomId}:events` - New room events format
- `game:lobby:updates` - Lobby updates

**Problem**: Services don't know which channel to use for what purpose

## Service-Specific Issues

### Socket Gateway

**File**: `services/socket-gateway/src/services/socketService.js`

**Issues**:
- Complex caching mechanism for room info (lines 30-52)
- Multiple event channel handlers (lines 189-216)
- Game-specific logic in generic handlers (lines 918-927)
- Disabled event handlers (lines 3318-3324)

**Critical Code Patterns**:
```javascript
// Problematic caching pattern
this.recentRoomInfo = new Map(); // roomId -> { room, players, timestamp, userId }

// Complex event routing
if (channel === 'socket:events') {
  this.handleGameServiceSocketEvent(message);
} else if (channel.startsWith('game:room:')) {
  this.handleGameServiceRoomEvent(message, roomId);
} else if (channel.startsWith('room:') && channel.endsWith(':events')) {
  this.handleGameRoomMessage(message, roomId);
}
```

### Game Service

**File**: `services/game-service/internal/services/room_service.go`

**Issues**:
- Disabled room state publishing (lines 1555-1560)
- Multiple event publishing patterns
- Atomic operations with complex mutex handling

**Critical Code Patterns**:
```go
// Disabled events indicating architectural issues
// REMOVED: room state update publishing after ready status change
// All room events are now disabled to prevent duplicate events
s.logger.WithFields(logrus.Fields{
    "roomId":  request.RoomID,
    "message": "Event publishing disabled - preventing duplicate events",
}).Debug("Skipped publishing room state update after ready status change")
```

### Manager Service

**File**: `services/manager-service/app/services/notification_service.rb`

**Issues**:
- Multiple notification channels for same events
- Balance update coordination complexity
- Transaction processing with external service dependencies

## Event Flow Diagrams

### Current Join Room Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant SG as Socket Gateway
    participant GS as Game Service
    participant R as Redis
    
    C->>SG: join_room
    SG->>SG: Validate auth
    SG->>R: Publish join request
    R->>GS: Forward request
    GS->>GS: Process join
    GS->>R: Publish response
    R->>SG: Forward response
    SG->>C: Join success
    
    Note over SG: process.nextTick
    SG->>R: Request room info
    R->>GS: Forward request
    GS->>R: Publish room info
    R->>SG: Forward room info
    SG->>C: room_info_updated
```

### Problematic Event Timing

```mermaid
sequenceDiagram
    participant C as Client
    participant SG as Socket Gateway
    participant GS as Game Service
    
    Note over C,GS: Race Condition Window
    
    SG->>C: join_room_response
    
    par Async Events
        GS->>C: room_info_updated (from Game Service)
    and
        SG->>C: room_info_updated (from Socket Gateway)
    end
    
    Note over C: Client receives duplicate or out-of-order events
```

## Performance Impact

### Current Issues

1. **Multiple Cache Layers**: Room state cached in Socket Gateway, Game Service, and Redis
2. **Event Duplication**: Same events published from multiple sources
3. **Complex State Synchronization**: Multiple services maintaining similar state
4. **Verbose Logging**: Debug logs in production affecting performance

### Metrics

- **Cache Hit Rate**: Variable due to multiple invalidation strategies
- **Event Latency**: Increased due to complex routing
- **Memory Usage**: Higher due to multiple caches
- **Network Traffic**: Increased due to duplicate events

## Security Considerations

### Current Vulnerabilities

1. **Event Injection**: Insufficient validation of Redis messages
2. **State Manipulation**: Multiple state update paths create attack vectors
3. **Race Condition Exploits**: Timing attacks on room join/leave operations

### Recommendations

1. **Message Validation**: Implement strict schema validation for all Redis messages
2. **Single Source of Truth**: Designate authoritative services for each data type
3. **Atomic Operations**: Use Redis transactions for multi-step operations

## Next Steps

See the [Game Flow TODO List](./game-flow-todo.md) for prioritized action items and implementation recommendations.

## Related Documentation

- [Event System Architecture](./event-system-architecture.md)
- [Redis Channel Standardization](./redis-channel-standards.md)
- [Room State Management](./room-state-management.md)
- [Performance Optimization Guide](./performance-optimization.md)
