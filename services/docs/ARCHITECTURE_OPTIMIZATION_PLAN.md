# Architecture Optimization Plan

## Executive Summary

This document outlines a comprehensive plan to optimize the XZ Game microservices architecture by addressing service responsibility violations, eliminating code duplication, breaking down large files, and improving service boundaries.

## Current Architecture Issues

### 1. Large Files Violating 1000-Line Rule
- `game-service/internal/services/request_handler.go` (5,093 lines)
- `game-service/internal/services/room_service.go` (3,226 lines) 
- `game-service/internal/services/game_service.go` (1,077 lines)

### 2. Service Responsibility Violations
- **Game Service**: Handling room management, game logic, request handling, and notifications
- **Room Service**: Minimal implementation while Game Service does room work
- **Game Engine Service**: Underutilized for actual game logic
- **Notification Service**: Minimal implementation with disabled events

### 3. Code Duplication Issues
- Room management logic scattered across Game Service and Manager Service
- Authentication patterns duplicated across services
- Redis communication patterns repeated in multiple services
- Similar validation logic in multiple places

### 4. Communication Pattern Issues
- Disabled event publishing indicating architectural problems
- Multiple cache layers with inconsistent strategies
- Complex Redis pub/sub patterns with race conditions
- Tight coupling between services

## Optimization Strategy

### Phase 1: Service Responsibility Redistribution (Week 1-2)

#### 1.1 Break Down Large Files

**Target: game-service/internal/services/request_handler.go (5,093 lines)**

Split into focused modules:
```
game-service/internal/services/request/
├── handler.go              # Core request handling (300 lines)
├── room_requests.go         # Room-specific requests (800 lines)
├── game_requests.go         # Game-specific requests (600 lines)
├── lobby_requests.go        # Lobby requests (400 lines)
├── auth_requests.go         # Auth requests (300 lines)
├── admin_requests.go        # Admin requests (200 lines)
├── payload_types.go         # Request/response types (400 lines)
└── utils.go                # Utility functions (300 lines)
```

**Target: game-service/internal/services/room_service.go (3,226 lines)**

Split into focused modules:
```
game-service/internal/services/room/
├── service.go              # Core room operations (600 lines)
├── join_leave.go           # Join/leave logic (800 lines)
├── validation.go           # Room validation (400 lines)
├── state_manager.go        # Room state management (500 lines)
├── cache_manager.go        # Room caching (300 lines)
├── event_publisher.go      # Room events (400 lines)
└── manager_integration.go  # Manager service integration (300 lines)
```

**Target: game-service/internal/services/game_service.go (1,077 lines)**

Split into focused modules:
```
game-service/internal/services/game/
├── service.go              # Core game service (400 lines)
├── session_manager.go      # Session management (400 lines)
└── result_calculator.go    # Result calculation (300 lines)
```

#### 1.2 Redistribute Service Responsibilities

**Move Room Management to Room Service:**
- Transfer all room CRUD operations from Game Service to Room Service
- Implement proper room lifecycle management
- Move room state management and caching
- Transfer room validation logic

**Move Game Logic to Game Engine Service:**
- Transfer game algorithms (Prize Wheel, Amidakuji) to Game Engine Service
- Move random number generation and fairness algorithms
- Implement game result calculation
- Move game-specific business logic

**Enhance Notification Service:**
- Move all notification logic from Game Service
- Implement proper event broadcasting
- Handle real-time updates and subscriptions
- Re-enable disabled event publishing with proper architecture

### Phase 2: Service Interface Standardization (Week 2-3)

#### 2.1 Define Clear Service Boundaries

**Game Service (Reduced Scope):**
- Game session orchestration
- Game state coordination
- Integration between Room, Game Engine, and Notification services
- High-level game flow management

**Room Service (Enhanced):**
- Room CRUD operations
- Room state management
- Player join/leave operations
- Room availability and capacity management
- Room configuration management

**Game Engine Service (Enhanced):**
- Pure game logic implementation
- Random number generation
- Game result calculation
- Fairness proof generation
- Game algorithm execution

**Notification Service (Enhanced):**
- Real-time event broadcasting
- Subscription management
- Multi-channel notifications
- Event routing and delivery

**Auth Service (Current + Enhanced):**
- Authentication and authorization
- Session management
- Token validation and blacklisting
- User permission management

#### 2.2 Standardize Communication Patterns

**gRPC Interfaces:**
- Define clear service contracts
- Implement proper error handling
- Add request/response validation
- Standardize timeout and retry policies

### Phase 3: Code Deduplication and Optimization (Week 3-4)

#### 3.1 Create Shared Libraries

**Common Packages:**
```
pkg/
├── models/          # Shared data models
├── errors/          # Common error types
├── auth/           # Authentication utilities
├── redis/          # Redis client wrapper
├── metrics/        # Metrics collection
├── validation/     # Input validation
└── utils/          # Common utilities
```

#### 3.2 Eliminate Code Duplication

- Extract common Redis patterns into shared utilities
- Create shared authentication middleware
- Standardize error handling across services
- Implement common logging patterns
- Create shared validation functions

### Phase 4: Integration Enhancement (Week 4-5)

#### 4.1 Implement Event-Driven Architecture

**Event Flow:**
```
Room Events: Room Service → Notification Service → Socket Gateway
Game Events: Game Engine Service → Notification Service → Socket Gateway  
Auth Events: Auth Service → Notification Service → All Services
```

#### 4.2 Optimize Data Consistency

- Implement Redis Streams for ordered events
- Add event sequence numbers
- Implement optimistic locking for concurrent operations
- Add state version management

### Phase 5: Performance Optimization (Week 5-6)

#### 5.1 Cache Strategy Optimization

- Centralize caching in Redis
- Implement cache invalidation events
- Optimize cache TTL strategies
- Add cache warming for frequently accessed data

#### 5.2 Database Optimization

- Optimize MongoDB queries and indexes
- Implement connection pooling
- Add query performance monitoring
- Optimize data models for access patterns

## Implementation Priority

### High Priority (Week 1)
1. Break down request_handler.go (5,093 lines)
2. Move room management to Room Service
3. Re-enable notification events with proper architecture

### Medium Priority (Week 2-3)
1. Break down room_service.go (3,226 lines)
2. Move game logic to Game Engine Service
3. Standardize gRPC interfaces

### Low Priority (Week 4-6)
1. Create shared libraries
2. Optimize performance
3. Enhance monitoring and metrics

## Success Metrics

- All files under 1000 lines
- Clear service boundaries with single responsibilities
- Eliminated code duplication
- Improved system performance
- Enhanced maintainability and testability
