# Game Service Test Makefile
# Comprehensive testing utilities for the game service

# Variables
GO := go
GOTEST := $(GO) test
GOBENCH := $(GO) test -bench=.
GOCOVER := $(GO) tool cover
GOFMT := $(GO) fmt
GOVET := $(GO) vet
GOLINT := golangci-lint run

# Test directories
TEST_DIRS := ./tests/services/... ./tests/repositories/... ./tests/models/... ./tests/controllers/...
INTEGRATION_TEST_DIRS := ./tests/integration/...
ALL_TEST_DIRS := ./tests/...

# Coverage settings
COVERAGE_DIR := coverage
COVERAGE_FILE := $(COVERAGE_DIR)/coverage.out
COVERAGE_HTML := $(COVERAGE_DIR)/coverage.html
COVERAGE_THRESHOLD := 85

# Test database settings
TEST_DB_NAME := xzgame_test
TEST_MONGODB_URL := mongodb://localhost:27017
TEST_REDIS_URL := redis://localhost:6379

# Docker settings for test dependencies
DOCKER_COMPOSE_TEST := docker-compose -f docker-compose.test.yml

.PHONY: help test test-unit test-integration test-all test-coverage test-coverage-html test-benchmark test-race test-verbose clean-test setup-test teardown-test lint fmt vet check-deps test-watch test-profile

# Default target
help: ## Show this help message
	@echo "Game Service Test Makefile"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Setup and teardown
setup-test: ## Setup test environment (start test dependencies)
	@echo "Setting up test environment..."
	@mkdir -p $(COVERAGE_DIR)
	@$(DOCKER_COMPOSE_TEST) up -d mongodb redis
	@echo "Waiting for services to be ready..."
	@sleep 5
	@echo "Test environment ready"

teardown-test: ## Teardown test environment (stop test dependencies)
	@echo "Tearing down test environment..."
	@$(DOCKER_COMPOSE_TEST) down -v
	@echo "Test environment cleaned up"

# Basic test commands
test: test-unit ## Run unit tests (default)

test-unit: ## Run unit tests only
	@echo "Running unit tests..."
	@$(GOTEST) -v $(TEST_DIRS)

test-integration: setup-test ## Run integration tests only
	@echo "Running integration tests..."
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -v -tags=integration $(INTEGRATION_TEST_DIRS)
	@$(MAKE) teardown-test

test-all: setup-test ## Run all tests (unit + integration)
	@echo "Running all tests..."
	@$(GOTEST) -v $(TEST_DIRS)
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -v -tags=integration $(INTEGRATION_TEST_DIRS)
	@$(MAKE) teardown-test

# Coverage testing
test-coverage: setup-test ## Run tests with coverage
	@echo "Running tests with coverage..."
	@$(GOTEST) -coverprofile=$(COVERAGE_FILE) -covermode=atomic $(ALL_TEST_DIRS)
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -coverprofile=$(COVERAGE_FILE).integration -covermode=atomic -tags=integration $(INTEGRATION_TEST_DIRS)
	@echo "Merging coverage files..."
	@$(GO) run tools/merge-coverage.go $(COVERAGE_FILE) $(COVERAGE_FILE).integration > $(COVERAGE_FILE).merged
	@mv $(COVERAGE_FILE).merged $(COVERAGE_FILE)
	@$(GOCOVER) -func=$(COVERAGE_FILE)
	@echo "Coverage report saved to $(COVERAGE_FILE)"
	@$(MAKE) teardown-test

test-coverage-html: test-coverage ## Generate HTML coverage report
	@echo "Generating HTML coverage report..."
	@$(GOCOVER) -html=$(COVERAGE_FILE) -o $(COVERAGE_HTML)
	@echo "HTML coverage report saved to $(COVERAGE_HTML)"
	@echo "Open $(COVERAGE_HTML) in your browser to view the report"

test-coverage-check: test-coverage ## Check if coverage meets threshold
	@echo "Checking coverage threshold ($(COVERAGE_THRESHOLD)%)..."
	@$(GO) run tools/check-coverage.go $(COVERAGE_FILE) $(COVERAGE_THRESHOLD)

# Performance and race testing
test-benchmark: ## Run performance benchmarks
	@echo "Running performance benchmarks..."
	@$(GOBENCH) -benchmem $(ALL_TEST_DIRS)

test-race: setup-test ## Run tests with race detection
	@echo "Running tests with race detection..."
	@$(GOTEST) -race -v $(TEST_DIRS)
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -race -v -tags=integration $(INTEGRATION_TEST_DIRS)
	@$(MAKE) teardown-test

test-verbose: ## Run tests with verbose output
	@echo "Running tests with verbose output..."
	@$(GOTEST) -v -count=1 $(ALL_TEST_DIRS)

# Specific test suites
test-services: ## Run service layer tests only
	@echo "Running service layer tests..."
	@$(GOTEST) -v ./tests/services/...

test-repositories: ## Run repository layer tests only
	@echo "Running repository layer tests..."
	@$(GOTEST) -v ./tests/repositories/...

test-models: ## Run model tests only
	@echo "Running model tests..."
	@$(GOTEST) -v ./tests/models/...

test-controllers: ## Run controller tests only
	@echo "Running controller tests..."
	@$(GOTEST) -v ./tests/controllers/...

test-game-flow: setup-test ## Run game flow integration tests only
	@echo "Running game flow integration tests..."
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -v -tags=integration ./tests/integration/game_flow_test.go
	@$(MAKE) teardown-test

test-room-lifecycle: setup-test ## Run room lifecycle integration tests only
	@echo "Running room lifecycle integration tests..."
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -v -tags=integration ./tests/integration/room_lifecycle_test.go
	@$(MAKE) teardown-test

# Code quality
lint: ## Run linter
	@echo "Running linter..."
	@$(GOLINT) ./...

fmt: ## Format code
	@echo "Formatting code..."
	@$(GOFMT) ./...

vet: ## Run go vet
	@echo "Running go vet..."
	@$(GOVET) ./...

check-deps: ## Check for missing dependencies
	@echo "Checking dependencies..."
	@$(GO) mod tidy
	@$(GO) mod verify

# Utility targets
clean-test: ## Clean test artifacts
	@echo "Cleaning test artifacts..."
	@rm -rf $(COVERAGE_DIR)
	@$(GO) clean -testcache
	@$(GO) clean -cache

test-watch: ## Watch for changes and run tests automatically
	@echo "Watching for changes and running tests..."
	@which fswatch > /dev/null || (echo "fswatch not found. Install with: brew install fswatch" && exit 1)
	@fswatch -o . | xargs -n1 -I{} make test-unit

test-profile: ## Run tests with CPU profiling
	@echo "Running tests with CPU profiling..."
	@mkdir -p $(COVERAGE_DIR)
	@$(GOTEST) -cpuprofile=$(COVERAGE_DIR)/cpu.prof -memprofile=$(COVERAGE_DIR)/mem.prof $(TEST_DIRS)
	@echo "CPU profile saved to $(COVERAGE_DIR)/cpu.prof"
	@echo "Memory profile saved to $(COVERAGE_DIR)/mem.prof"
	@echo "View with: go tool pprof $(COVERAGE_DIR)/cpu.prof"

# Table-driven test generators
generate-test-fixtures: ## Generate additional test fixtures
	@echo "Generating test fixtures..."
	@$(GO) run tools/generate-fixtures.go

generate-test-cases: ## Generate table-driven test cases
	@echo "Generating table-driven test cases..."
	@$(GO) run tools/generate-test-cases.go

# Test data management
seed-test-data: setup-test ## Seed test database with sample data
	@echo "Seeding test database..."
	@MONGODB_TEST_URL=$(TEST_MONGODB_URL) $(GO) run tools/seed-test-data.go
	@$(MAKE) teardown-test

clean-test-data: setup-test ## Clean test database
	@echo "Cleaning test database..."
	@MONGODB_TEST_URL=$(TEST_MONGODB_URL) $(GO) run tools/clean-test-data.go
	@$(MAKE) teardown-test

# Continuous Integration targets
ci-test: ## Run tests for CI environment
	@echo "Running CI tests..."
	@$(MAKE) check-deps
	@$(MAKE) fmt
	@$(MAKE) vet
	@$(MAKE) lint
	@$(MAKE) test-coverage-check
	@$(MAKE) test-race

ci-integration: ## Run integration tests for CI environment
	@echo "Running CI integration tests..."
	@$(MAKE) setup-test
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GOTEST) -v -tags=integration -timeout=30m $(INTEGRATION_TEST_DIRS)
	@$(MAKE) teardown-test

# Documentation
test-docs: ## Generate test documentation
	@echo "Generating test documentation..."
	@$(GO) run tools/generate-test-docs.go

# Performance testing
test-load: setup-test ## Run load tests
	@echo "Running load tests..."
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GO) run tools/load-test.go
	@$(MAKE) teardown-test

test-stress: setup-test ## Run stress tests
	@echo "Running stress tests..."
	@INTEGRATION_TESTS=true MONGODB_TEST_URL=$(TEST_MONGODB_URL) REDIS_TEST_URL=$(TEST_REDIS_URL) \
		$(GO) run tools/stress-test.go
	@$(MAKE) teardown-test

# Test reporting
test-report: test-coverage ## Generate comprehensive test report
	@echo "Generating comprehensive test report..."
	@$(GO) run tools/generate-test-report.go $(COVERAGE_FILE)

# Quick commands for development
quick-test: ## Quick test run (no setup/teardown)
	@$(GOTEST) -short $(TEST_DIRS)

quick-coverage: ## Quick coverage check (no integration tests)
	@$(GOTEST) -short -coverprofile=$(COVERAGE_FILE) $(TEST_DIRS)
	@$(GOCOVER) -func=$(COVERAGE_FILE)

# Help for specific test patterns
test-pattern: ## Run tests matching a pattern (usage: make test-pattern PATTERN=TestName)
	@echo "Running tests matching pattern: $(PATTERN)"
	@$(GOTEST) -v -run $(PATTERN) $(ALL_TEST_DIRS)

# All-in-one comprehensive test
test-comprehensive: ## Run comprehensive test suite (all tests + quality checks)
	@echo "Running comprehensive test suite..."
	@$(MAKE) check-deps
	@$(MAKE) fmt
	@$(MAKE) vet
	@$(MAKE) lint
	@$(MAKE) test-coverage-check
	@$(MAKE) test-race
	@$(MAKE) test-benchmark
	@$(MAKE) test-coverage-html
	@echo "Comprehensive test suite completed successfully!"

# Operational test targets (replacing removed scripts)
test-room-operations: ## Test room join/leave operations (replaces test_room_join.sh)
	@echo "Testing room operations..."
	@$(GOTEST) -v ./internal/services/ -run TestRoom
	@$(GOTEST) -v ./tests/integration/ -run TestRoomOperations

test-edge-cases: ## Test edge cases and error scenarios (replaces test_kick_scenario.sh)
	@echo "Testing edge cases..."
	@$(GOTEST) -v ./internal/services/ -run TestEdgeCases
	@$(GOTEST) -v ./tests/integration/ -run TestErrorScenarios

test-error-handling: ## Test error handling scenarios (replaces test_room_not_found.sh)
	@echo "Testing error handling..."
	@$(GOTEST) -v ./internal/services/ -run TestErrorHandling
	@$(GOTEST) -v ./internal/models/ -run TestErrorTypes

test-integration-full: ## Run comprehensive integration tests (enhanced)
	@echo "Running full integration test suite..."
	@./test_data_consistency.sh
	@$(MAKE) test-integration

# Operational tools (for scripts we kept)
cleanup-rooms: ## Clean up room duplicates and inconsistencies
	@echo "Cleaning up rooms..."
	@./cleanup_room.sh

remove-user: ## Remove user from all rooms (requires USER_ID)
	@if [ -z "$(USER_ID)" ]; then echo "Usage: make remove-user USER_ID=<user_id>"; exit 1; fi
	@echo "Removing user $(USER_ID) from all rooms..."
	@./remove_user_from_rooms.sh $(USER_ID)

test-room-leave: ## Test room leave functionality
	@echo "Testing room leave functionality..."
	@./simple_leave_room.sh

test-data-consistency: ## Test data consistency across services
	@echo "Testing data consistency..."
	@./test_data_consistency.sh
