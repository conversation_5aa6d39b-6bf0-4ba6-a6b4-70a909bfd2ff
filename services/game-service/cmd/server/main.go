package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/controllers"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/internal/services"
	"github.com/xzgame/game-service/pkg/database"
	"github.com/xzgame/game-service/pkg/redis"
	"github.com/xzgame/game-service/pkg/request"
	pb "github.com/xzgame/game-service/proto"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		logrus.Warn("No .env file found")
	}

	// Initialize configuration
	cfg := config.Load()

	// Setup logging
	setupLogging(cfg.LogLevel)

	logrus.Info("Starting Game Service with performance optimizations...")

	// Initialize metrics (basic Prometheus metrics only)
	// Note: Advanced performance monitoring, circuit breakers, and object pooling removed
	// as they were over-engineered for current scale

	// Initialize database connections
	mongoClient, err := database.NewMongoClient(cfg.MongoURL)
	if err != nil {
		logrus.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer mongoClient.Disconnect(context.Background())

	redisClient, err := redis.NewRedisClient(cfg.RedisURL)
	if err != nil {
		logrus.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Initialize repositories
	gameRepo := repositories.NewGameRepository(mongoClient)
	roomRepo := repositories.NewRoomRepository(mongoClient)
	playerRepo := repositories.NewPlayerRepository(mongoClient)
	configRepo := repositories.NewConfigRepository(mongoClient)

	// Manager client removed - using orchestration pattern

	// Initialize services
	gameService := services.NewGameService(gameRepo, redisClient, cfg)
	roomService := services.NewRoomService(roomRepo, redisClient, nil, cfg)
	playerService := services.NewPlayerService(playerRepo, redisClient, cfg)
	_ = services.NewConfigService(configRepo, redisClient, cfg)

	// Initialize enhanced lobby service with room repository
	lobbyService := services.NewLobbyService(redisClient, roomRepo, cfg)

	// Auth service removed - using orchestration pattern

	// Initialize game state manager
	gameStateManager := services.NewGameStateManager(logrus.New(), redisClient, roomService, gameService, nil)

	// Initialize request handler service (using modular handler)
	requestHandler := request.NewHandler(redisClient, roomService, lobbyService, gameStateManager)

	// Note: Performance monitor removed - using basic Prometheus metrics instead

	// Initialize controllers
	gameController := controllers.NewGameController(gameService, roomService, playerService)
	roomController := controllers.NewRoomController(roomService, gameService)

	// Setup gRPC server
	grpcServer := grpc.NewServer()
	pb.RegisterGameServiceServer(grpcServer, gameController)
	pb.RegisterRoomServiceServer(grpcServer, roomController)

	// Enable reflection for development
	if cfg.Environment == "development" {
		reflection.Register(grpcServer)
	}

	// Setup HTTP server for health checks and metrics
	httpRouter := setupHTTPServer(cfg, mongoClient, redisClient, roomService, requestHandler)

	// Start request handler service
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go func() {
		if err := requestHandler.Start(ctx); err != nil && err != context.Canceled {
			logrus.WithError(err).Error("Request handler service failed")
		}
	}()

	// Start servers
	go startGRPCServer(grpcServer, cfg.Port)
	go startHTTPServer(httpRouter, cfg.MetricsPort)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("Shutting down servers...")

	// Note: Performance monitoring and object pools removed for simplicity

	// Graceful shutdown
	grpcServer.GracefulStop()
	logrus.Info("Game Service stopped")
}

func setupLogging(level string) {
	logrus.SetFormatter(&logrus.JSONFormatter{})
	logrus.SetOutput(os.Stdout)

	switch level {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}
}

func setupHTTPServer(cfg *config.Config, mongoClient *database.MongoClient, redisClient *redis.RedisClient, roomService services.RoomService, requestHandler *request.Handler) *gin.Engine {
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// Health check endpoints
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"service":   "game-service",
		})
	})

	router.GET("/health/detailed", func(c *gin.Context) {
		// Check database connectivity
		mongoHealthy := mongoClient.Ping(context.Background()) == nil
		redisHealthy := redisClient.Ping(context.Background()).Err() == nil

		// Check request handler status
		requestHandlerHealthy := requestHandler.IsHealthy()

		status := "healthy"
		httpStatus := http.StatusOK

		if !mongoHealthy || !redisHealthy {
			status = "degraded"
			httpStatus = http.StatusOK // Still return 200 for degraded state
		}

		if !requestHandlerHealthy {
			status = "unhealthy"
			httpStatus = http.StatusServiceUnavailable
		}

		c.JSON(httpStatus, gin.H{
			"status":    status,
			"timestamp": time.Now().UTC(),
			"service":   "game-service",
			"dependencies": gin.H{
				"mongodb": mongoHealthy,
				"redis":   redisHealthy,
			},
			"checks": gin.H{
				"mongodb": gin.H{
					"status": func() string {
						if mongoHealthy {
							return "healthy"
						}
						return "unhealthy"
					}(),
					"message": func() string {
						if mongoHealthy {
							return "MongoDB connection is healthy"
						}
						return "MongoDB connection failed"
					}(),
				},
				"redis": gin.H{
					"status": func() string {
						if redisHealthy {
							return "healthy"
						}
						return "unhealthy"
					}(),
					"message": func() string {
						if redisHealthy {
							return "Redis connection is healthy"
						}
						return "Redis connection failed"
					}(),
				},
				"request_handler": gin.H{
					"status": func() string {
						if requestHandlerHealthy {
							return "healthy"
						}
						return "unhealthy"
					}(),
					"message": func() string {
						if requestHandlerHealthy {
							return "Request handler is running and processing messages"
						}
						return "Request handler is not running or not processing messages"
					}(),
					"details": map[string]interface{}{
						"status":  "operational",
						"message": "Request handler service is running",
					},
				},
			},
		})
	})

	// Metrics endpoint
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// Debug endpoints (only in development)
	if cfg.Environment == "development" {
		debug := router.Group("/debug")
		{
			// Room sync endpoint
			debug.POST("/sync-room/:id", func(c *gin.Context) {
				roomID := c.Param("id")
				if roomID == "" {
					c.JSON(http.StatusBadRequest, gin.H{
						"success": false,
						"message": "room ID is required",
					})
					return
				}

				logrus.WithField("room_id", roomID).Info("Manual room sync requested via HTTP")

				// Force fresh room data retrieval
				room, err := roomService.GetRoomFresh(c.Request.Context(), roomID)
				if err != nil {
					logrus.WithError(err).Error("Failed to sync room")
					c.JSON(http.StatusInternalServerError, gin.H{
						"success": false,
						"message": "failed to sync room",
						"error":   err.Error(),
					})
					return
				}

				response := gin.H{
					"success": true,
					"message": "Room synchronized successfully",
					"room": gin.H{
						"id":              room.ID.Hex(),
						"name":            room.Name,
						"status":          room.Status,
						"current_players": room.CurrentPlayers,
						"max_players":     room.MaxPlayers,
						"can_join":        room.CanJoin(),
						"game_type":       room.GameType,
						"last_activity":   room.LastActivity,
					},
					"timestamp": time.Now(),
				}

				logrus.WithFields(logrus.Fields{
					"room_id":         roomID,
					"current_players": room.CurrentPlayers,
					"max_players":     room.MaxPlayers,
					"status":          room.Status,
					"can_join":        room.CanJoin(),
				}).Info("Room sync completed via HTTP")

				c.JSON(http.StatusOK, response)
			})

			// Clear room cache endpoint
			debug.DELETE("/cache/room/:id", func(c *gin.Context) {
				roomID := c.Param("id")
				if roomID == "" {
					c.JSON(http.StatusBadRequest, gin.H{
						"success": false,
						"message": "room ID is required",
					})
					return
				}

				logrus.WithField("room_id", roomID).Info("Manual room cache clear requested")

				// Clear cache
				roomKey := fmt.Sprintf("room:state:%s", roomID)
				err := redisClient.Delete(context.Background(), roomKey)
				if err != nil {
					logrus.WithError(err).Error("Failed to clear room cache")
					c.JSON(http.StatusInternalServerError, gin.H{
						"success": false,
						"message": "failed to clear cache",
						"error":   err.Error(),
					})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"success":   true,
					"message":   "Room cache cleared successfully",
					"room_id":   roomID,
					"timestamp": time.Now(),
				})
			})
		}
	}

	return router
}

func startGRPCServer(server *grpc.Server, port string) {
	lis, err := net.Listen("tcp", ":"+port)
	if err != nil {
		logrus.Fatalf("Failed to listen on port %s: %v", port, err)
	}

	logrus.Infof("gRPC server listening on port %s", port)
	if err := server.Serve(lis); err != nil {
		logrus.Fatalf("Failed to serve gRPC server: %v", err)
	}
}

func startHTTPServer(router *gin.Engine, port string) {
	server := &http.Server{
		Addr:    ":" + port,
		Handler: router,
	}

	logrus.Infof("HTTP server listening on port %s", port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logrus.Fatalf("Failed to serve HTTP server: %v", err)
	}
}
