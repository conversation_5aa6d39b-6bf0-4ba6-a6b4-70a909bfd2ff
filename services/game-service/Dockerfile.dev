# Development Dockerfile for Game Service
FROM golang:1.21-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    git \
    make \
    protobuf \
    protobuf-dev \
    curl

# Set working directory
WORKDIR /app

# Install Go tools
RUN go install github.com/cosmtrek/air@latest && \
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Generate protobuf code
RUN make proto-gen

# Expose ports
EXPOSE 8080 9090

# Create air configuration
RUN echo 'root = "."' > .air.toml && \
    echo 'testdata_dir = "testdata"' >> .air.toml && \
    echo 'tmp_dir = "tmp"' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[build]' >> .air.toml && \
    echo '  args_bin = []' >> .air.toml && \
    echo '  bin = "./tmp/main"' >> .air.toml && \
    echo '  cmd = "go build -o ./tmp/main ./cmd/server/main.go"' >> .air.toml && \
    echo '  delay = 1000' >> .air.toml && \
    echo '  exclude_dir = ["assets", "tmp", "vendor", "testdata"]' >> .air.toml && \
    echo '  exclude_file = []' >> .air.toml && \
    echo '  exclude_regex = ["_test.go"]' >> .air.toml && \
    echo '  exclude_unchanged = false' >> .air.toml && \
    echo '  follow_symlink = false' >> .air.toml && \
    echo '  full_bin = ""' >> .air.toml && \
    echo '  include_dir = []' >> .air.toml && \
    echo '  include_ext = ["go", "tpl", "tmpl", "html"]' >> .air.toml && \
    echo '  include_file = []' >> .air.toml && \
    echo '  kill_delay = "0s"' >> .air.toml && \
    echo '  log = "build-errors.log"' >> .air.toml && \
    echo '  poll = false' >> .air.toml && \
    echo '  poll_interval = 0' >> .air.toml && \
    echo '  rerun = false' >> .air.toml && \
    echo '  rerun_delay = 500' >> .air.toml && \
    echo '  send_interrupt = false' >> .air.toml && \
    echo '  stop_on_root = false' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[color]' >> .air.toml && \
    echo '  app = ""' >> .air.toml && \
    echo '  build = "yellow"' >> .air.toml && \
    echo '  main = "magenta"' >> .air.toml && \
    echo '  runner = "green"' >> .air.toml && \
    echo '  watcher = "cyan"' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[log]' >> .air.toml && \
    echo '  main_only = false' >> .air.toml && \
    echo '  time = false' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[misc]' >> .air.toml && \
    echo '  clean_on_exit = false' >> .air.toml

# Run with hot reload
CMD ["air", "-c", ".air.toml"]
