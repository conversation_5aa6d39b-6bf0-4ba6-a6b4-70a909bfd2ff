# 🎮 Game Service Testing Guide

This document provides comprehensive information about the testing infrastructure for the Game Service.

## 🎯 Test Status Overview

### ✅ **Working Tests (100% Passing)**

| Test Suite | Status | Coverage | Description |
|------------|--------|----------|-------------|
| **Models Package** | ✅ PASSING | 18.5% | Core model validation, error handling, business logic |
| **Basic Tests** | ✅ PASSING | 100% | Table-driven testing patterns and model verification |

### 🚧 **Tests Under Development**

| Test Suite | Status | Issue | Next Steps |
|------------|--------|-------|------------|
| Services Tests | 🔧 Compilation Issues | Import cycles, missing mocks | Fix import structure |
| Repository Tests | 🔧 Model Mismatches | Field name changes | Update fixtures |
| Controller Tests | 🔧 Interface Changes | Missing mock methods | Update mocks |

## 🚀 Quick Start

### Run All Working Tests
```bash
./run_tests.sh
```

### Run Specific Test Suites
```bash
./run_tests.sh models    # Models package only
./run_tests.sh basic     # Basic tests only
./run_tests.sh coverage  # With coverage report
./run_tests.sh help      # Show all options
```

### Manual Test Execution
```bash
# Models package tests
go test ./internal/models/... -v

# Basic tests
go test ./tests/basic_test.go -v

# With coverage
go test ./internal/models/... -cover -v
```

## 📊 Test Coverage Details

### **Models Package Tests (18.5% coverage)**

#### ✅ **Error Handling (25+ error codes tested)**
- Game errors (not found, invalid state, timeout)
- Room errors (full, closed, not found)
- Player errors (insufficient balance, not authorized)
- Database errors (connection, timeout)
- Validation errors (required fields, format)

#### ✅ **Model Validation**
- GameSession validation (room ID, player limits)
- SessionPlayer validation (bet amounts, positions)
- BetLimitConfig validation (min/max limits, currency)
- GameResults validation (payouts, calculations)
- Room validation (player limits, configuration)
- TimeoutConfig validation (negative values, ranges)

#### ✅ **String Representations**
- GameType constants (PrizeWheel, Amidakuji)
- PlayerStatus constants (Joined, Ready, Playing, Finished)
- RoomStatus constants (Waiting, Full, Active, Closed, Archived)
- SessionStatus constants (Waiting, Active, Completed, Cancelled)
- GamePhase constants (Pending, Waiting, Active, Completed, Cancelled)

### **Basic Tests (100% coverage)**

#### ✅ **Table-Driven Testing Patterns**
- Game type validation with multiple test cases
- Error creation and handling verification
- Model constant validation
- Business logic testing (bet limits, payouts)

## 🏗️ Test Infrastructure

### **Test Organization**
```
services/game-service/
├── tests/
│   ├── basic_test.go           # ✅ Working basic tests
│   ├── fixtures/               # 🚧 Test data fixtures
│   ├── services/               # 🚧 Service layer tests
│   ├── repositories/           # 🚧 Repository layer tests
│   └── utils/                  # 🚧 Test utilities
├── internal/models/            # ✅ Working model tests
├── run_tests.sh               # ✅ Test runner script
└── TESTING.md                 # This file
```

### **Test Runner Features**
- **Colored output** for easy reading
- **Multiple execution modes** (all, specific, coverage, verbose)
- **Error tracking** and summary reporting
- **Watch mode** for continuous testing (requires fswatch)
- **Help system** with usage examples

### **Test Patterns Used**
- **Table-driven tests** for comprehensive coverage
- **Testify framework** for assertions and mocking
- **Realistic test fixtures** for data setup
- **Error validation** with specific error codes
- **Business logic validation** for game rules

## 🔧 Fixing Compilation Issues

### **Common Issues and Solutions**

#### **1. Import Cycles**
```bash
# Error: import cycle not allowed in test
# Solution: Move mocks to separate package or restructure imports
```

#### **2. Model Field Mismatches**
```bash
# Error: unknown field 'CreatorID' in struct literal
# Solution: Update test fixtures to match current model definitions
```

#### **3. Missing Mock Methods**
```bash
# Error: missing method BroadcastRoomEvent
# Solution: Update mock interfaces to match service interfaces
```

### **Fixing Steps**
1. **Update model fixtures** to match current field names
2. **Fix import cycles** by restructuring test packages
3. **Update mock interfaces** to include all required methods
4. **Verify field types** (pointers vs values, time.Time vs *time.Time)

## 📈 Test Metrics

### **Current Test Statistics**
- **Total Test Functions**: 50+
- **Total Assertions**: 200+
- **Error Codes Tested**: 25+
- **Model Types Validated**: 15+
- **Business Rules Tested**: 10+

### **Test Execution Performance**
- **Models Package**: ~0.3s
- **Basic Tests**: ~0.15s
- **Total Runtime**: <1s
- **Memory Usage**: Minimal

## 🎯 Testing Best Practices

### **1. Table-Driven Tests**
```go
testCases := []struct {
    name      string
    input     models.GameType
    expected  bool
}{
    {"Valid Prize Wheel", models.GameTypePrizeWheel, true},
    {"Valid Amidakuji", models.GameTypeAmidakuji, true},
    {"Invalid Type", models.GameType("invalid"), false},
}

for _, tc := range testCases {
    t.Run(tc.name, func(t *testing.T) {
        // Test implementation
    })
}
```

### **2. Error Validation**
```go
err := service.CreateRoom(ctx, invalidRequest)
assert.Error(t, err)
assert.Contains(t, err.Error(), "VALIDATION_ERROR")
```

### **3. Mock Usage**
```go
mockRepo.On("GetRoom", ctx, roomID).Return(expectedRoom, nil)
result, err := service.GetRoom(ctx, roomID)
assert.NoError(t, err)
assert.Equal(t, expectedRoom.ID, result.ID)
```

## 🚀 Next Steps

### **Immediate Priorities**
1. **Fix compilation issues** in services, repositories, and controllers
2. **Update test fixtures** to match current model definitions
3. **Add integration tests** for end-to-end scenarios
4. **Increase test coverage** to >80%

### **Future Enhancements**
1. **Performance tests** for high-load scenarios
2. **Contract tests** for API compatibility
3. **Property-based tests** for edge case discovery
4. **Mutation testing** for test quality validation

## 📞 Support

For questions about testing:
1. Check this documentation first
2. Run `./run_tests.sh help` for quick reference
3. Review existing test patterns in `internal/models/`
4. Use table-driven tests for new test cases

---

**Happy Testing! 🎉**
