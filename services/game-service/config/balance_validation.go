package config

import (
	"os"
	"strconv"
	"strings"
)

// BalanceValidationConfig contains configuration for balance validation behavior
type BalanceValidationConfig struct {
	// KickForInsufficientBalance determines if players should be kicked when they have insufficient balance
	// If false, players are just prevented from becoming ready but stay in the room
	KickForInsufficientBalance bool

	// DeductOnReady determines when bet amounts should be deducted
	// If true, deduct when player marks ready
	// If false, deduct when game starts (default behavior)
	DeductOnReady bool

	// GracePeriodSeconds is the time in seconds to wait before kicking players with insufficient balance
	// Only applies if KickForInsufficientBalance is true
	GracePeriodSeconds int

	// AllowNegativeBalance determines if players can go into negative balance
	// This is typically false for production environments
	AllowNegativeBalance bool

	// MinimumBalanceThreshold is the minimum balance required to participate in any game
	// This is in addition to the room's bet amount (in cents)
	MinimumBalanceThreshold int64
}

// LoadBalanceValidationConfig loads balance validation configuration from environment variables
func LoadBalanceValidationConfig() *BalanceValidationConfig {
	config := &BalanceValidationConfig{
		// Default values - conservative approach
		KickForInsufficientBalance: getBoolEnv("BALANCE_KICK_INSUFFICIENT", false),
		DeductOnReady:              getBoolEnv("BALANCE_DEDUCT_ON_READY", false),
		GracePeriodSeconds:         getIntEnv("BALANCE_GRACE_PERIOD_SECONDS", 30),
		AllowNegativeBalance:       getBoolEnv("BALANCE_ALLOW_NEGATIVE", false),
		MinimumBalanceThreshold:    getInt64Env("BALANCE_MINIMUM_THRESHOLD_CENTS", 0),
	}

	return config
}

// getBoolEnv gets a boolean environment variable with a default value
func getBoolEnv(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	// Handle various boolean representations
	value = strings.ToLower(strings.TrimSpace(value))
	switch value {
	case "true", "1", "yes", "on", "enabled":
		return true
	case "false", "0", "no", "off", "disabled":
		return false
	default:
		return defaultValue
	}
}

// getIntEnv gets an integer environment variable with a default value
func getIntEnv(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return intValue
}

// getInt64Env gets an int64 environment variable with a default value
func getInt64Env(key string, defaultValue int64) int64 {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	int64Value, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return defaultValue
	}

	return int64Value
}

// IsKickEnabled returns whether players should be kicked for insufficient balance
func (c *BalanceValidationConfig) IsKickEnabled() bool {
	return c.KickForInsufficientBalance
}

// IsDeductOnReadyEnabled returns whether bet amounts should be deducted when marking ready
func (c *BalanceValidationConfig) IsDeductOnReadyEnabled() bool {
	return c.DeductOnReady
}

// GetGracePeriodSeconds returns the grace period in seconds before kicking players
func (c *BalanceValidationConfig) GetGracePeriodSeconds() int {
	return c.GracePeriodSeconds
}

// IsNegativeBalanceAllowed returns whether negative balances are allowed
func (c *BalanceValidationConfig) IsNegativeBalanceAllowed() bool {
	return c.AllowNegativeBalance
}

// GetMinimumBalanceThreshold returns the minimum balance threshold in cents
func (c *BalanceValidationConfig) GetMinimumBalanceThreshold() int64 {
	return c.MinimumBalanceThreshold
}

// ValidateBalance checks if a balance meets the requirements
func (c *BalanceValidationConfig) ValidateBalance(currentBalance, betAmount int64) (bool, int64) {
	requiredAmount := betAmount + c.MinimumBalanceThreshold
	
	if c.AllowNegativeBalance {
		// If negative balance is allowed, always return true
		return true, 0
	}

	if currentBalance >= requiredAmount {
		return true, 0
	}

	deficit := requiredAmount - currentBalance
	return false, deficit
}
