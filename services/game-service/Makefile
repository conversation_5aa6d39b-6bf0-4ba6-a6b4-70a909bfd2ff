# Game Service Makefile

# Variables
BINARY_NAME=game-service
BINARY_PATH=./bin/$(BINARY_NAME)
MAIN_PATH=./cmd/server/main.go
PROTO_PATH=./proto
PROTO_FILES=$(wildcard $(PROTO_PATH)/*.proto)
GO_FILES=$(shell find . -name "*.go" -type f -not -path "./vendor/*")

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt

# Docker parameters
DOCKER_IMAGE=xzgame-game-service
DOCKER_TAG=latest

# Default target
.PHONY: all
all: clean deps proto-gen build

# Install dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Generate protocol buffer code
.PHONY: proto-gen
proto-gen:
	@echo "Generating protobuf code..."
	@mkdir -p $(PROTO_PATH)
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		$(PROTO_FILES)

# Build the application
.PHONY: build
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p bin
	$(GOBUILD) -o $(BINARY_PATH) $(MAIN_PATH)

# Build for production (optimized)
.PHONY: build-prod
build-prod:
	@echo "Building $(BINARY_NAME) for production..."
	@mkdir -p bin
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) -a -installsuffix cgo -ldflags '-extldflags "-static"' -o $(BINARY_PATH) $(MAIN_PATH)

# Run the application
.PHONY: run
run: build
	$(BINARY_PATH)

# Run in development mode with hot reload
.PHONY: dev
dev:
	@echo "Running in development mode..."
	air -c .air.toml

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf bin/
	rm -rf tmp/

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Run tests with race detection
.PHONY: test-race
test-race:
	@echo "Running tests with race detection..."
	$(GOTEST) -race -v ./...

# Run benchmarks
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) -s -w $(GO_FILES)

# Lint code
.PHONY: lint
lint:
	@echo "Linting code..."
	golangci-lint run

# Vet code
.PHONY: vet
vet:
	@echo "Vetting code..."
	$(GOCMD) vet ./...

# Security scan
.PHONY: security
security:
	@echo "Running security scan..."
	gosec ./...

# Check code quality
.PHONY: check
check: fmt vet lint security test

# Docker build for development
.PHONY: docker-build-dev
docker-build-dev:
	@echo "Building Docker image for development..."
	docker build -f Dockerfile.dev -t $(DOCKER_IMAGE):dev .

# Docker build for production
.PHONY: docker-build-prod
docker-build-prod:
	@echo "Building Docker image for production..."
	docker build -f Dockerfile.prod -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

# Docker run development
.PHONY: docker-run-dev
docker-run-dev: docker-build-dev
	@echo "Running Docker container for development..."
	docker run -p 8080:8080 -p 9090:9090 --env-file .env.dev $(DOCKER_IMAGE):dev

# Docker run production
.PHONY: docker-run-prod
docker-run-prod: docker-build-prod
	@echo "Running Docker container for production..."
	docker run -p 8080:8080 -p 9090:9090 --env-file .env.prod $(DOCKER_IMAGE):$(DOCKER_TAG)

# Docker compose up
.PHONY: docker-up
docker-up:
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

# Docker compose down
.PHONY: docker-down
docker-down:
	@echo "Stopping services with Docker Compose..."
	docker-compose down

# Install development tools
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Generate mocks for testing
.PHONY: mocks
mocks:
	@echo "Generating mocks..."
	mockgen -source=internal/repositories/interfaces.go -destination=internal/repositories/mocks/mock_repositories.go
	mockgen -source=internal/services/interfaces.go -destination=internal/services/mocks/mock_services.go

# Database migration (if needed)
.PHONY: migrate
migrate:
	@echo "Running database migrations..."
	# Add migration commands here

# Seed database with test data
.PHONY: seed
seed:
	@echo "Seeding database..."
	$(GOCMD) run scripts/seed.go

# Generate API documentation
.PHONY: docs
docs:
	@echo "Generating API documentation..."
	# Add documentation generation commands here

# Performance profiling
.PHONY: profile-cpu
profile-cpu:
	@echo "Running CPU profiling..."
	$(GOCMD) tool pprof http://localhost:8080/debug/pprof/profile

.PHONY: profile-mem
profile-mem:
	@echo "Running memory profiling..."
	$(GOCMD) tool pprof http://localhost:8080/debug/pprof/heap

# Load testing
.PHONY: load-test
load-test:
	@echo "Running load tests..."
	# Add load testing commands here

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, install deps, generate proto, and build"
	@echo "  deps         - Install Go dependencies"
	@echo "  proto-gen    - Generate protobuf code"
	@echo "  build        - Build the application"
	@echo "  build-prod   - Build for production"
	@echo "  run          - Build and run the application"
	@echo "  dev          - Run in development mode with hot reload"
	@echo "  clean        - Clean build artifacts"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  test-race    - Run tests with race detection"
	@echo "  bench        - Run benchmarks"
	@echo "  fmt          - Format code"
	@echo "  lint         - Lint code"
	@echo "  vet          - Vet code"
	@echo "  security     - Run security scan"
	@echo "  check        - Run all code quality checks"
	@echo "  docker-*     - Docker related commands"
	@echo "  install-tools- Install development tools"
	@echo "  mocks        - Generate mocks for testing"
	@echo "  help         - Show this help message"
