# User Auto-Removal from Rooms

## Problem Solved

When users try to join a room but are already in another room, the system was returning a `USER_ALREADY_IN_ROOM` error. This implementation adds automatic removal of users from other rooms before allowing them to join a new room.

## Solution Overview

### 1. Automatic Room Cleanup on Join

When a user tries to join a room:
1. **Check current room**: If user is already in the target room, treat as reconnection
2. **Check other rooms**: If user is in other rooms, automatically remove them
3. **Proceed with join**: After cleanup, continue with the normal join process

### 2. Enhanced Error Handling

The system now handles `USER_ALREADY_IN_ROOM` errors by:
- Attempting automatic removal from other rooms
- Logging detailed information about the cleanup process
- Providing fallback mechanisms if removal fails

## Implementation Details

### Room Service Changes (`room_service.go`)

#### New Method: `removePlayerFromOtherRooms`

```go
func (s *roomService) removePlayerFromOtherRooms(ctx context.Context, userID, targetRoomID, username string) error
```

**Features:**
- Gets all rooms where the player is present
- Removes player from any room that's not the target room
- Uses both `LeaveRoom` service and direct database removal as fallback
- Invalidates cache after removal
- Comprehensive logging for debugging

#### Enhanced Join Logic

The `JoinRoomWithUsername` method now includes:
- Automatic cleanup before join validation
- Non-blocking cleanup (continues even if cleanup fails)
- Detailed logging of the cleanup process

### Request Handler Changes (`request_handler.go`)

#### New Method: `removeUserFromOtherRooms`

```go
func (s *RequestHandlerService) removeUserFromOtherRooms(ctx context.Context, userID, targetRoomID, username string) error
```

**Features:**
- Handles auto-removal at the request handler level
- Provides detailed statistics (rooms checked, rooms removed)
- Continues processing even if some removals fail

#### Enhanced Validation

The join room validation now:
- Attempts auto-removal when `USER_ALREADY_IN_ROOM` is detected
- Only fails if auto-removal completely fails
- Provides clear logging of the auto-removal process

## Usage

### Automatic Usage

The auto-removal happens automatically when:
1. A user tries to join a room
2. The system detects they're already in another room
3. The system automatically removes them from other rooms
4. The join process continues normally

### Manual Cleanup Script

For manual cleanup, use the provided script:

```bash
# Remove specific user from all rooms
./remove_user_from_rooms.sh 683b07882d7dbd11e92bf29d res2

# Remove user with just user ID
./remove_user_from_rooms.sh 683b07882d7dbd11e92bf29d
```

## Error Handling

### Graceful Degradation

1. **Primary method**: Use `LeaveRoom` service for proper cleanup
2. **Fallback method**: Direct database removal if service fails
3. **Cache invalidation**: Always clear cache after removal
4. **Continue on failure**: Don't block join if cleanup partially fails

### Logging

Comprehensive logging includes:
- User and room information
- Cleanup attempt details
- Success/failure status
- Fallback mechanism usage
- Final statistics

## Configuration

### Environment Variables

- `REDIS_HOST`: Redis server host (default: localhost)
- `REDIS_PORT`: Redis server port (default: 6379)

### Behavior Controls

The auto-removal feature:
- **Always enabled**: Runs automatically on join attempts
- **Non-blocking**: Won't prevent joins if cleanup fails
- **Comprehensive**: Removes from ALL other rooms, not just one

## Monitoring and Debugging

### Log Messages to Watch

```
INFO: "Checking for player in other rooms"
INFO: "Removing player from other room"
INFO: "Successfully removed player from other room"
WARN: "Failed to remove player from other room"
INFO: "Completed removal of player from other rooms"
```

### Key Metrics

- `rooms_checked`: Number of rooms examined
- `rooms_removed`: Number of successful removals
- `fallback_used`: Whether direct database removal was needed

## Testing

### Test the Auto-Removal

1. **Setup**: Have a user in one room
2. **Action**: Try to join another room
3. **Expected**: User is automatically removed from first room and joins second room
4. **Verify**: Check logs for auto-removal messages

### Manual Testing Script

```bash
# Test with the problematic user from the logs
./remove_user_from_rooms.sh 683b07882d7dbd11e92bf29d res2
```

## Benefits

1. **Better UX**: Users don't get blocked by "already in room" errors
2. **Automatic cleanup**: No manual intervention needed
3. **Robust fallbacks**: Multiple mechanisms ensure cleanup works
4. **Detailed logging**: Easy to debug issues
5. **Non-disruptive**: Doesn't break existing functionality

## Files Modified

1. **`internal/services/room_service.go`**
   - Added `removePlayerFromOtherRooms` method
   - Enhanced `JoinRoomWithUsername` with auto-cleanup

2. **`internal/services/request_handler.go`**
   - Added `removeUserFromOtherRooms` method
   - Enhanced join validation with auto-removal

3. **Scripts created:**
   - `remove_user_from_rooms.sh` - Manual cleanup utility
   - `USER_AUTO_REMOVAL.md` - This documentation

## Future Enhancements

1. **Metrics**: Add Prometheus metrics for auto-removal statistics
2. **Configuration**: Make auto-removal behavior configurable
3. **Batch operations**: Optimize for users in many rooms
4. **Notification**: Notify users when they're auto-removed from rooms
5. **Rate limiting**: Prevent abuse of auto-removal feature

## Troubleshooting

### Common Issues

1. **User still shows as in room**: Check cache invalidation
2. **Removal fails**: Check database connectivity and permissions
3. **Partial cleanup**: Review logs for specific room failures

### Debug Commands

```bash
# Check Redis cache for user
redis-cli KEYS "*683b07882d7dbd11e92bf29d*"

# Clear user cache manually
redis-cli DEL "player:683b07882d7dbd11e92bf29d:balance"

# Check room state
redis-cli GET "room:state:ROOM_ID"
```
