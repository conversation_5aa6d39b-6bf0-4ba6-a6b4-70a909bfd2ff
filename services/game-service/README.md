# Game Service

## Overview

The Game Service implements core game logic and manages game state with high performance and concurrency. Built in Go, it handles game rules enforcement, random number generation, result calculation, and game session persistence.

## Technology Stack

- **Language**: Go 1.21+
- **Concurrency**: Goroutines and channels
- **Communication**: gRPC for service communication
- **Cache/PubSub**: Redis for caching and messaging
- **Database**: MongoDB with official Go driver
- **Metrics**: Prometheus metrics
- **Logging**: Structured logging with logrus

## Key Features

- Game state management with atomic operations
- Game rules enforcement and validation
- Cryptographically secure random number generation
- Result calculation and distribution
- Game session persistence
- Real-time event publishing via Redis
- Horizontal scaling support
- Comprehensive metrics and monitoring

## Project Structure

```
game-service/
├── cmd/
│   └── server/            # Main application entry point
├── internal/
│   ├── config/            # Configuration management
│   ├── controllers/       # gRPC controllers
│   ├── models/           # Data models and structures
│   ├── services/         # Business logic services
│   ├── repositories/     # Data access layer
│   ├── games/            # Game-specific implementations
│   │   ├── prizewheel/   # Prize wheel game logic
│   │   └── amidakuji/    # Amidakuji game logic
│   └── utils/            # Utility functions
├── pkg/                  # Public packages
├── proto/                # Protocol buffer definitions
├── tests/                # Test files
├── configs/              # Configuration files
├── scripts/              # Build and deployment scripts
├── go.mod
├── go.sum
├── Dockerfile.dev
├── Dockerfile.prod
└── README.md
```

## Environment Variables

```bash
GO_ENV=development|production
PORT=8080
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/xzgame
JWT_SECRET=your_jwt_secret_here
RANDOM_SEED_SECRET=your_random_seed_secret
LOG_LEVEL=info|debug|error
METRICS_PORT=9090
```

## Game Implementations

### Prize Wheel Game

**Flow**:
1. Players join room and contribute to prize pool
2. System validates player balances
3. When all players ready, countdown begins
4. Wheel spins with cryptographically secure randomization
5. Winner determined and prize pool distributed
6. Results broadcast to all participants

**Key Components**:
- Wheel position calculation
- Fair randomization algorithm
- Animation data generation
- Winner determination logic

### Amidakuji (Ghost Leg) Game

**Flow**:
1. Players join room and contribute to prize pool
2. System validates player balances
3. When all players ready, countdown begins
4. Amidakuji pattern generated using verifiable random function
5. Path tracing animation data sent to clients
6. Winner determined by algorithm
7. Results broadcast to all participants

**Key Components**:
- Pattern generation algorithm
- Path tracing logic
- Animation sequence calculation
- Fairness verification

## gRPC Services

### GameService
```protobuf
service GameService {
  rpc CreateRoom(CreateRoomRequest) returns (CreateRoomResponse);
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
  rpc StartGame(StartGameRequest) returns (StartGameResponse);
  rpc GetGameState(GetGameStateRequest) returns (GetGameStateResponse);
  rpc GetGameHistory(GetGameHistoryRequest) returns (GetGameHistoryResponse);
}
```

### RoomService
```protobuf
service RoomService {
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse);
  rpc GetRoom(GetRoomRequest) returns (GetRoomResponse);
  rpc UpdateRoom(UpdateRoomRequest) returns (UpdateRoomResponse);
  rpc DeleteRoom(DeleteRoomRequest) returns (DeleteRoomResponse);
}
```

## Development

### Setup
```bash
# Install dependencies
go mod download

# Run development server
go run cmd/server/main.go

# Run tests
go test ./...

# Run with race detection
go test -race ./...

# Generate protocol buffers
make proto-gen
```

### Testing
```bash
# Unit tests
go test ./internal/...

# Integration tests
go test ./tests/integration/...

# Benchmark tests
go test -bench=. ./...

# Coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## Game Logic

### Random Number Generation

Uses cryptographically secure random number generation with verifiable seeds:

```go
type RandomGenerator struct {
    seed   string
    hasher hash.Hash
}

func (rg *RandomGenerator) GenerateSecure(max int) int {
    // Implementation with cryptographic security
}
```

### Fairness Verification

Each game includes fairness proof that can be independently verified:

```go
type FairnessProof struct {
    Seed         string    `json:"seed"`
    Algorithm    string    `json:"algorithm"`
    Timestamp    time.Time `json:"timestamp"`
    Hash         string    `json:"hash"`
    Verification string    `json:"verification"`
}
```

### State Management

Game state is managed with atomic operations and optimistic locking:

```go
type GameState struct {
    mu          sync.RWMutex
    Phase       GamePhase
    Players     []Player
    StartTime   time.Time
    GameData    interface{}
    Results     *GameResults
}
```

## Redis Integration

### Pub/Sub Channels
- `game:room:{roomId}` - Room-specific events
- `game:global` - Global game events
- `admin:notifications` - Administrative notifications

### Caching Strategy
- Game configurations (TTL: 1 hour)
- Room states (TTL: 30 minutes)
- Player sessions (TTL: 24 hours)

## Monitoring

### Prometheus Metrics
- `game_rooms_active` - Number of active rooms
- `game_players_connected` - Number of connected players
- `game_duration_seconds` - Game duration histogram
- `game_errors_total` - Total number of game errors
- `random_generation_duration` - RNG performance metrics

### Health Checks
```bash
# Service health
curl http://localhost:8080/health

# Detailed health with dependencies
curl http://localhost:8080/health/detailed

# Metrics endpoint
curl http://localhost:9090/metrics
```

## Performance

### Concurrency
- Goroutines for handling concurrent games
- Channel-based communication
- Worker pools for heavy operations
- Connection pooling for database access

### Optimization
- Memory pooling for frequent allocations
- Efficient data structures
- Minimal garbage collection pressure
- Optimized database queries

## Security

### Game Integrity
- Cryptographically secure randomization
- Verifiable fairness algorithms
- Atomic transaction processing
- Comprehensive audit logging

### Input Validation
- gRPC request validation
- Business rule enforcement
- Rate limiting per player
- Anti-cheat mechanisms

## Deployment

### Docker Development
```bash
docker build -f Dockerfile.dev -t xzgame-game-service:dev .
docker run -p 8080:8080 -p 9090:9090 --env-file .env.dev xzgame-game-service:dev
```

### Docker Production
```bash
docker build -f Dockerfile.prod -t xzgame-game-service:prod .
docker run -p 8080:8080 -p 9090:9090 --env-file .env.prod xzgame-game-service:prod
```

### Binary Deployment
```bash
# Build for production
make build

# Run binary
./bin/game-service
```

## Game Session Management

### Session Lifecycle

The Game Service manages complete game session lifecycles with atomic state transitions and comprehensive persistence:

**Session States:**
- `pending` - Session created, waiting for players
- `waiting` - Minimum players joined, waiting for game start
- `active` - Game in progress
- `completed` - Game finished with results
- `cancelled` - Game cancelled due to timeout or insufficient players

**Lifecycle Flow:**
```go
type GameSession struct {
    ID           string                 `bson:"_id" json:"id"`
    RoomID       string                 `bson:"room_id" json:"room_id"`
    GameType     string                 `bson:"game_type" json:"game_type"`
    Status       SessionStatus          `bson:"status" json:"status"`
    Players      []SessionPlayer        `bson:"players" json:"players"`
    StartTime    time.Time              `bson:"start_time" json:"start_time"`
    EndTime      *time.Time             `bson:"end_time,omitempty" json:"end_time,omitempty"`
    Results      *GameResults           `bson:"results,omitempty" json:"results,omitempty"`
    Configuration SessionConfiguration  `bson:"configuration" json:"configuration"`
    CreatedAt    time.Time              `bson:"created_at" json:"created_at"`
    UpdatedAt    time.Time              `bson:"updated_at" json:"updated_at"`
}

type SessionPlayer struct {
    UserID      string    `bson:"user_id" json:"user_id"`
    Position    int       `bson:"position" json:"position"`
    BetAmount   int64     `bson:"bet_amount" json:"bet_amount"`
    WinAmount   int64     `bson:"win_amount" json:"win_amount"`
    JoinedAt    time.Time `bson:"joined_at" json:"joined_at"`
    Status      string    `bson:"status" json:"status"` // joined, ready, playing, finished
}
```

### Session State Management

**Atomic State Transitions:**
```go
func (s *GameSessionService) TransitionState(sessionID string, newState SessionStatus) error {
    session, err := s.repository.GetSession(sessionID)
    if err != nil {
        return err
    }

    // Validate state transition
    if !s.isValidTransition(session.Status, newState) {
        return ErrInvalidStateTransition
    }

    // Perform atomic update with optimistic locking
    update := bson.M{
        "$set": bson.M{
            "status":     newState,
            "updated_at": time.Now(),
        },
        "$inc": bson.M{"version": 1},
    }

    filter := bson.M{
        "_id":     sessionID,
        "version": session.Version,
    }

    result, err := s.repository.UpdateSession(filter, update)
    if err != nil {
        return err
    }

    if result.ModifiedCount == 0 {
        return ErrConcurrentModification
    }

    // Publish state change event
    s.publishStateChangeEvent(sessionID, session.Status, newState)
    return nil
}
```

### Player Session Tracking

**Player Coordination:**
```go
type PlayerSessionTracker struct {
    mu       sync.RWMutex
    sessions map[string]*PlayerSession
    redis    *redis.Client
}

type PlayerSession struct {
    UserID      string    `json:"user_id"`
    SessionID   string    `json:"session_id"`
    RoomID      string    `json:"room_id"`
    Status      string    `json:"status"`
    LastPing    time.Time `json:"last_ping"`
    Connection  string    `json:"connection_id"`
}

func (pst *PlayerSessionTracker) TrackPlayer(userID, sessionID, roomID string) error {
    pst.mu.Lock()
    defer pst.mu.Unlock()

    // Check for existing sessions
    if existing, exists := pst.sessions[userID]; exists {
        if existing.SessionID != sessionID {
            return ErrPlayerAlreadyInSession
        }
    }

    session := &PlayerSession{
        UserID:    userID,
        SessionID: sessionID,
        RoomID:    roomID,
        Status:    "active",
        LastPing:  time.Now(),
    }

    pst.sessions[userID] = session

    // Persist to Redis for cross-instance coordination
    sessionData, _ := json.Marshal(session)
    return pst.redis.Set(
        fmt.Sprintf("player_session:%s", userID),
        sessionData,
        24*time.Hour,
    ).Err()
}
```

### Session Timeout and Cleanup

**Timeout Management:**
```go
type SessionTimeoutManager struct {
    sessions    map[string]*SessionTimeout
    mu          sync.RWMutex
    cleanupChan chan string
}

type SessionTimeout struct {
    SessionID   string
    TimeoutAt   time.Time
    TimeoutType string // waiting, playing, idle
    Timer       *time.Timer
}

func (stm *SessionTimeoutManager) SetSessionTimeout(sessionID string, duration time.Duration, timeoutType string) {
    stm.mu.Lock()
    defer stm.mu.Unlock()

    // Cancel existing timeout
    if existing, exists := stm.sessions[sessionID]; exists {
        existing.Timer.Stop()
    }

    timeout := &SessionTimeout{
        SessionID:   sessionID,
        TimeoutAt:   time.Now().Add(duration),
        TimeoutType: timeoutType,
        Timer: time.AfterFunc(duration, func() {
            stm.handleTimeout(sessionID, timeoutType)
        }),
    }

    stm.sessions[sessionID] = timeout
}

func (stm *SessionTimeoutManager) handleTimeout(sessionID, timeoutType string) {
    switch timeoutType {
    case "waiting":
        stm.cancelSessionDueToTimeout(sessionID, "Insufficient players")
    case "playing":
        stm.forceCompleteSession(sessionID, "Game timeout")
    case "idle":
        stm.cleanupIdleSession(sessionID)
    }
}
```

**Cleanup Procedures:**
```go
func (s *GameSessionService) CleanupExpiredSessions() error {
    // Find sessions that need cleanup
    expiredSessions, err := s.repository.FindExpiredSessions(time.Now().Add(-1 * time.Hour))
    if err != nil {
        return err
    }

    for _, session := range expiredSessions {
        switch session.Status {
        case "pending", "waiting":
            // Cancel sessions that never started
            s.CancelSession(session.ID, "Session expired")
        case "active":
            // Force complete active sessions
            s.ForceCompleteSession(session.ID, "Session timeout")
        case "completed", "cancelled":
            // Archive old completed sessions
            s.ArchiveSession(session.ID)
        }
    }

    return nil
}

func (s *GameSessionService) ArchiveSession(sessionID string) error {
    session, err := s.repository.GetSession(sessionID)
    if err != nil {
        return err
    }

    // Move to archive collection
    err = s.repository.ArchiveSession(session)
    if err != nil {
        return err
    }

    // Remove from active sessions
    return s.repository.DeleteSession(sessionID)
}
```

## Game Configuration & Settings

### Game Type Configurations

**Prize Wheel Configuration:**
```go
type PrizeWheelConfig struct {
    Sections        int                    `bson:"sections" json:"sections"`
    SpinDuration    time.Duration          `bson:"spin_duration" json:"spin_duration"`
    AnimationSteps  int                    `bson:"animation_steps" json:"animation_steps"`
    PayoutStructure map[int]PayoutConfig   `bson:"payout_structure" json:"payout_structure"`
    MinPlayers      int                    `bson:"min_players" json:"min_players"`
    MaxPlayers      int                    `bson:"max_players" json:"max_players"`
    BetLimits       BetLimitConfig         `bson:"bet_limits" json:"bet_limits"`
    Timeouts        TimeoutConfig          `bson:"timeouts" json:"timeouts"`
}

type PayoutConfig struct {
    Position    int     `bson:"position" json:"position"`
    Multiplier  float64 `bson:"multiplier" json:"multiplier"`
    Probability float64 `bson:"probability" json:"probability"`
}

type BetLimitConfig struct {
    MinBet      int64 `bson:"min_bet" json:"min_bet"`
    MaxBet      int64 `bson:"max_bet" json:"max_bet"`
    Currency    string `bson:"currency" json:"currency"`
}

type TimeoutConfig struct {
    WaitingTimeout time.Duration `bson:"waiting_timeout" json:"waiting_timeout"`
    PlayingTimeout time.Duration `bson:"playing_timeout" json:"playing_timeout"`
    IdleTimeout    time.Duration `bson:"idle_timeout" json:"idle_timeout"`
}
```

**Amidakuji Configuration:**
```go
type AmidakujiConfig struct {
    MinPlayers       int                  `bson:"min_players" json:"min_players"`
    MaxPlayers       int                  `bson:"max_players" json:"max_players"`
    RowsPerPlayer    int                  `bson:"rows_per_player" json:"rows_per_player"`
    LineGeneration   LineGenerationConfig `bson:"line_generation" json:"line_generation"`
    AnimationConfig  AnimationConfig      `bson:"animation_config" json:"animation_config"`
    BetLimits        BetLimitConfig       `bson:"bet_limits" json:"bet_limits"`
    Timeouts         TimeoutConfig        `bson:"timeouts" json:"timeouts"`
}

type LineGenerationConfig struct {
    MinLines        int     `bson:"min_lines" json:"min_lines"`
    MaxLines        int     `bson:"max_lines" json:"max_lines"`
    LineProbability float64 `bson:"line_probability" json:"line_probability"`
    Seed            string  `bson:"seed" json:"seed"`
}

type AnimationConfig struct {
    StepDuration   time.Duration `bson:"step_duration" json:"step_duration"`
    TotalDuration  time.Duration `bson:"total_duration" json:"total_duration"`
    ShowPath       bool          `bson:"show_path" json:"show_path"`
}
```

### Configurable Game Parameters

**Runtime Parameter Management:**
```go
type GameParameterManager struct {
    cache      map[string]interface{}
    mu         sync.RWMutex
    repository GameConfigRepository
    redis      *redis.Client
}

func (gpm *GameParameterManager) GetGameConfig(gameType string) (*GameConfig, error) {
    gpm.mu.RLock()
    if cached, exists := gpm.cache[gameType]; exists {
        gpm.mu.RUnlock()
        return cached.(*GameConfig), nil
    }
    gpm.mu.RUnlock()

    // Load from database
    config, err := gpm.repository.GetGameConfig(gameType)
    if err != nil {
        return nil, err
    }

    // Cache for future use
    gpm.mu.Lock()
    gpm.cache[gameType] = config
    gpm.mu.Unlock()

    return config, nil
}

func (gpm *GameParameterManager) UpdateGameConfig(gameType string, config *GameConfig) error {
    // Validate configuration
    if err := gpm.validateConfig(gameType, config); err != nil {
        return err
    }

    // Update in database
    if err := gpm.repository.UpdateGameConfig(gameType, config); err != nil {
        return err
    }

    // Update cache
    gpm.mu.Lock()
    gpm.cache[gameType] = config
    gpm.mu.Unlock()

    // Notify other instances via Redis
    configData, _ := json.Marshal(config)
    return gpm.redis.Publish(
        fmt.Sprintf("game_config_update:%s", gameType),
        configData,
    ).Err()
}
```

### Runtime Game Settings Management

**Dynamic Settings Updates:**
```go
type RuntimeSettingsManager struct {
    settings map[string]*RuntimeSettings
    mu       sync.RWMutex
    pubsub   *redis.PubSub
}

type RuntimeSettings struct {
    MaxConcurrentGames int           `json:"max_concurrent_games"`
    MaintenanceMode    bool          `json:"maintenance_mode"`
    GameTypeEnabled    map[string]bool `json:"game_type_enabled"`
    GlobalBetLimits    BetLimitConfig  `json:"global_bet_limits"`
    UpdatedAt          time.Time       `json:"updated_at"`
}

func (rsm *RuntimeSettingsManager) UpdateSettings(settings *RuntimeSettings) error {
    rsm.mu.Lock()
    defer rsm.mu.Unlock()

    settings.UpdatedAt = time.Now()
    rsm.settings["global"] = settings

    // Broadcast to all instances
    settingsData, _ := json.Marshal(settings)
    return rsm.pubsub.Publish("runtime_settings_update", settingsData).Err()
}

func (rsm *RuntimeSettingsManager) IsGameTypeEnabled(gameType string) bool {
    rsm.mu.RLock()
    defer rsm.mu.RUnlock()

    if settings, exists := rsm.settings["global"]; exists {
        if enabled, exists := settings.GameTypeEnabled[gameType]; exists {
            return enabled
        }
    }
    return true // Default to enabled
}
```

### Game Rules and Payout Structure Configuration

**Payout Structure Management:**
```go
type PayoutStructureManager struct {
    structures map[string]*PayoutStructure
    mu         sync.RWMutex
    repository PayoutRepository
}

type PayoutStructure struct {
    GameType     string                 `bson:"game_type" json:"game_type"`
    Version      int                    `bson:"version" json:"version"`
    Rules        map[string]interface{} `bson:"rules" json:"rules"`
    Payouts      []PayoutRule           `bson:"payouts" json:"payouts"`
    HouseEdge    float64                `bson:"house_edge" json:"house_edge"`
    CreatedAt    time.Time              `bson:"created_at" json:"created_at"`
    ActivatedAt  *time.Time             `bson:"activated_at,omitempty" json:"activated_at,omitempty"`
}

type PayoutRule struct {
    Condition   string  `bson:"condition" json:"condition"`
    Multiplier  float64 `bson:"multiplier" json:"multiplier"`
    Probability float64 `bson:"probability" json:"probability"`
    Description string  `bson:"description" json:"description"`
}

func (psm *PayoutStructureManager) CalculatePayout(gameType string, gameResult *GameResult) (*PayoutCalculation, error) {
    structure, err := psm.GetActivePayoutStructure(gameType)
    if err != nil {
        return nil, err
    }

    calculation := &PayoutCalculation{
        GameType:      gameType,
        TotalBetPool:  gameResult.TotalBetPool,
        HouseEdge:     structure.HouseEdge,
        Payouts:       make([]PlayerPayout, 0),
    }

    // Calculate house take
    houseTake := int64(float64(gameResult.TotalBetPool) * structure.HouseEdge)
    prizePool := gameResult.TotalBetPool - houseTake

    // Apply payout rules
    for _, player := range gameResult.Players {
        payout := psm.calculatePlayerPayout(structure, player, prizePool)
        calculation.Payouts = append(calculation.Payouts, payout)
    }

    return calculation, nil
}
```

## Configuration

### Game Configuration
Game parameters are configurable via database with real-time updates:
- Player limits (min/max per game type)
- Bet amounts (min/max with currency support)
- Timeouts and durations (waiting, playing, idle)
- Payout structures (multipliers, probabilities)
- Fairness algorithms (RNG seeds, verification methods)
- Animation settings (durations, visual effects)

### Runtime Configuration
```yaml
server:
  port: 8080
  read_timeout: 30s
  write_timeout: 30s

database:
  mongodb_url: "mongodb://localhost:27017/xzgame"
  max_pool_size: 100
  timeout: 10s

redis:
  url: "redis://localhost:6379"
  pool_size: 50
  timeout: 5s

games:
  max_concurrent_games: 1000
  default_timeout: 60s
  max_players_per_room: 8
  session_cleanup_interval: 300s
  archive_completed_after: 24h
```

## Troubleshooting

### Common Issues
1. **Game State Corruption**: Check for race conditions and atomic operations
2. **Random Number Issues**: Verify seed generation and algorithm implementation
3. **Performance Degradation**: Monitor goroutine leaks and memory usage
4. **Database Timeouts**: Check connection pool settings and query optimization

### Debug Mode
```bash
# Enable debug logging
GO_ENV=development LOG_LEVEL=debug go run cmd/server/main.go

# Enable race detection
go run -race cmd/server/main.go

# Profile CPU usage
go tool pprof http://localhost:8080/debug/pprof/profile
```

### Logs
```bash
# View real-time logs
tail -f logs/game-service.log

# Search for game-specific errors
grep "game_error" logs/game-service.log

# Monitor performance metrics
curl -s http://localhost:9090/metrics | grep game_
```
