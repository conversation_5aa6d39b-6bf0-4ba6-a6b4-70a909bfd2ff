// MongoDB Initialization Script for Game Service

// Switch to the xzgame database
db = db.getSiblingDB('xzgame');

// Create collections with validation schemas
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'username', 'created_at'],
      properties: {
        email: {
          bsonType: 'string',
          description: 'must be a string and is required'
        },
        username: {
          bsonType: 'string',
          description: 'must be a string and is required'
        },
        balance: {
          bsonType: 'long',
          minimum: 0,
          description: 'must be a non-negative integer'
        },
        created_at: {
          bsonType: 'date',
          description: 'must be a date and is required'
        }
      }
    }
  }
});

db.createCollection('rooms', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name', 'game_type', 'status', 'created_at'],
      properties: {
        name: {
          bsonType: 'string',
          description: 'must be a string and is required'
        },
        game_type: {
          enum: ['prizewheel', 'amidakuji'],
          description: 'must be a valid game type'
        },
        status: {
          enum: ['waiting', 'active', 'full', 'closed', 'archived'],
          description: 'must be a valid room status'
        },
        current_players: {
          bsonType: 'int',
          minimum: 0,
          description: 'must be a non-negative integer'
        },
        max_players: {
          bsonType: 'int',
          minimum: 2,
          maximum: 8,
          description: 'must be between 2 and 8'
        }
      }
    }
  }
});

db.createCollection('game_sessions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['room_id', 'game_type', 'status', 'created_at'],
      properties: {
        room_id: {
          bsonType: 'string',
          description: 'must be a string and is required'
        },
        game_type: {
          enum: ['prizewheel', 'amidakuji'],
          description: 'must be a valid game type'
        },
        status: {
          enum: ['pending', 'waiting', 'active', 'completed', 'cancelled'],
          description: 'must be a valid session status'
        }
      }
    }
  }
});

db.createCollection('game_history');
db.createCollection('game_configurations');
db.createCollection('player_sessions');
db.createCollection('transactions');
db.createCollection('audit_logs');

// Create indexes for optimal performance
print('Creating indexes...');

// Users collection indexes
db.users.createIndex({ 'email': 1 }, { unique: true });
db.users.createIndex({ 'username': 1 }, { unique: true });
db.users.createIndex({ 'created_at': 1 });

// Rooms collection indexes
db.rooms.createIndex({ 'status': 1, 'created_at': -1 });
db.rooms.createIndex({ 'game_type': 1, 'status': 1 });
db.rooms.createIndex({ 'created_at': 1 }, { expireAfterSeconds: 86400 }); // 24 hours TTL

// Game sessions collection indexes
db.game_sessions.createIndex({ 'room_id': 1 });
db.game_sessions.createIndex({ 'status': 1, 'created_at': -1 });
db.game_sessions.createIndex({ 'players.user_id': 1 });
db.game_sessions.createIndex({ 'created_at': 1 }, { expireAfterSeconds: 604800 }); // 7 days TTL

// Game history collection indexes
db.game_history.createIndex({ 'session_id': 1 }, { unique: true });
db.game_history.createIndex({ 'game_type': 1, 'completed_at': -1 });
db.game_history.createIndex({ 'players.user_id': 1, 'completed_at': -1 });

// Game configurations collection indexes
db.game_configurations.createIndex({ 'game_type': 1, 'version': 1 }, { unique: true });
db.game_configurations.createIndex({ 'game_type': 1, 'is_active': 1 });

// Player sessions collection indexes
db.player_sessions.createIndex({ 'last_ping': 1 }, { expireAfterSeconds: 3600 }); // 1 hour TTL
db.player_sessions.createIndex({ 'room_id': 1 });
db.player_sessions.createIndex({ 'session_id': 1 });

// Transactions collection indexes
db.transactions.createIndex({ 'user_id': 1, 'created_at': -1 });
db.transactions.createIndex({ 'type': 1, 'created_at': -1 });
db.transactions.createIndex({ 'status': 1 });

// Audit logs collection indexes
db.audit_logs.createIndex({ 'user_id': 1, 'timestamp': -1 });
db.audit_logs.createIndex({ 'activity_type': 1, 'timestamp': -1 });
db.audit_logs.createIndex({ 'timestamp': 1 }, { expireAfterSeconds: 2592000 }); // 30 days TTL

// Insert default game configurations
print('Inserting default game configurations...');

db.game_configurations.insertMany([
  {
    game_type: 'prizewheel',
    version: 1,
    rules: {
      wheel_segments: 8,
      animation_duration: 3000
    },
    payouts: [
      {
        condition: 'winner',
        multiplier: 0.95,
        probability: 1.0,
        description: 'Winner takes the prize pool'
      }
    ],
    house_edge: 0.05,
    min_players: 2,
    max_players: 8,
    bet_limits: {
      min_bet: NumberLong(100),
      max_bet: NumberLong(10000),
      currency: 'USD'
    },
    timeouts: {
      waiting_timeout: NumberLong(300000), // 5 minutes in milliseconds
      playing_timeout: NumberLong(600000), // 10 minutes in milliseconds
      idle_timeout: NumberLong(1800000)    // 30 minutes in milliseconds
    },
    is_active: true,
    created_at: new Date(),
    activated_at: new Date()
  },
  {
    game_type: 'amidakuji',
    version: 1,
    rules: {
      ladder_height: 10,
      animation_duration: 5000
    },
    payouts: [
      {
        condition: 'winner',
        multiplier: 0.95,
        probability: 1.0,
        description: 'Winner takes the prize pool'
      }
    ],
    house_edge: 0.05,
    min_players: 2,
    max_players: 8,
    bet_limits: {
      min_bet: NumberLong(100),
      max_bet: NumberLong(10000),
      currency: 'USD'
    },
    timeouts: {
      waiting_timeout: NumberLong(300000), // 5 minutes in milliseconds
      playing_timeout: NumberLong(600000), // 10 minutes in milliseconds
      idle_timeout: NumberLong(1800000)    // 30 minutes in milliseconds
    },
    is_active: true,
    created_at: new Date(),
    activated_at: new Date()
  }
]);

// Create a test user for development
print('Creating test user...');

db.users.insertOne({
  email: '<EMAIL>',
  username: 'testuser',
  balance: NumberLong(100000), // $1000 in cents
  created_at: new Date(),
  updated_at: new Date()
});

print('MongoDB initialization completed successfully!');
print('Collections created: ' + db.getCollectionNames().length);
print('Test user created with username: testuser');
print('Default game configurations inserted for prizewheel and amidakuji');
