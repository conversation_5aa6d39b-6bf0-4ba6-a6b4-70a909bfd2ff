// MongoDB Script to Seed Test Rooms for Game Service
// Run this script to create test rooms in the Game Service database

// Switch to the xzgame database
db = db.getSiblingDB('xzgame');

print('Seeding test rooms for Game Service...');

// Clear existing rooms (optional - remove this line if you want to keep existing rooms)
db.rooms.deleteMany({});

// Create test rooms with different configurations
const testRooms = [
  {
    name: "Prizewheel Beginner Room",
    game_type: "PRIZEWHEEL",
    status: "waiting",
    current_players: 2,
    max_players: 8,
    min_players: 2,
    players: [
      {
        user_id: "user1",
        username: "<PERSON>",
        position: 1,
        status: "joined",
        bet_amount: NumberLong(100),
        joined_at: new Date()
      },
      {
        user_id: "user2", 
        username: "<PERSON>",
        position: 2,
        status: "joined",
        bet_amount: NumberLong(100),
        joined_at: new Date()
      }
    ],
    configuration: {
      game_type: "PRIZEWHEEL",
      bet_limits: {
        min_bet: <PERSON><PERSON><PERSON>(50),
        max_bet: <PERSON><PERSON><PERSON>(500),
        currency: "USD"
      },
      timeouts: {
        waiting_timeout: Number<PERSON><PERSON>(300000),
        playing_timeout: Number<PERSON><PERSON>(600000),
        idle_timeout: Number<PERSON><PERSON>(1800000)
      },
      auto_start: true,
      is_private: false,
      password: "",
      game_specific: {}
    },
    last_activity: new Date(),
    created_by: "admin",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: "Amidakuji High Stakes",
    game_type: "AMIDAKUJI", 
    status: "waiting",
    current_players: 1,
    max_players: 6,
    min_players: 2,
    players: [
      {
        user_id: "user3",
        username: "Charlie",
        position: 1,
        status: "joined",
        bet_amount: NumberLong(1000),
        joined_at: new Date()
      }
    ],
    configuration: {
      game_type: "AMIDAKUJI",
      bet_limits: {
        min_bet: NumberLong(500),
        max_bet: NumberLong(5000),
        currency: "USD"
      },
      timeouts: {
        waiting_timeout: NumberLong(300000),
        playing_timeout: NumberLong(600000), 
        idle_timeout: NumberLong(1800000)
      },
      auto_start: false,
      is_private: false,
      password: "",
      game_specific: {
        ladder_height: 10,
        animation_speed: "normal"
      }
    },
    last_activity: new Date(),
    created_by: "admin",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: "Private Prizewheel VIP",
    game_type: "PRIZEWHEEL",
    status: "waiting",
    current_players: 0,
    max_players: 4,
    min_players: 2,
    players: [],
    configuration: {
      game_type: "PRIZEWHEEL",
      bet_limits: {
        min_bet: NumberLong(1000),
        max_bet: NumberLong(10000),
        currency: "USD"
      },
      timeouts: {
        waiting_timeout: NumberLong(600000),
        playing_timeout: NumberLong(900000),
        idle_timeout: NumberLong(3600000)
      },
      auto_start: true,
      is_private: true,
      password: "vip123",
      game_specific: {
        wheel_segments: 12,
        animation_duration: 5000
      }
    },
    last_activity: new Date(),
    created_by: "admin",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: "Quick Amidakuji Express",
    game_type: "AMIDAKUJI",
    status: "waiting", 
    current_players: 3,
    max_players: 8,
    min_players: 2,
    players: [
      {
        user_id: "user4",
        username: "Diana",
        position: 1,
        status: "joined",
        bet_amount: NumberLong(200),
        joined_at: new Date()
      },
      {
        user_id: "user5",
        username: "Eve",
        position: 2,
        status: "joined", 
        bet_amount: NumberLong(200),
        joined_at: new Date()
      },
      {
        user_id: "user6",
        username: "Frank",
        position: 3,
        status: "joined",
        bet_amount: NumberLong(200),
        joined_at: new Date()
      }
    ],
    configuration: {
      game_type: "AMIDAKUJI",
      bet_limits: {
        min_bet: NumberLong(100),
        max_bet: NumberLong(1000),
        currency: "USD"
      },
      timeouts: {
        waiting_timeout: NumberLong(180000),
        playing_timeout: NumberLong(300000),
        idle_timeout: NumberLong(900000)
      },
      auto_start: true,
      is_private: false,
      password: "",
      game_specific: {
        ladder_height: 6,
        animation_speed: "fast"
      }
    },
    last_activity: new Date(),
    created_by: "admin",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: "Mega Prizewheel Tournament",
    game_type: "PRIZEWHEEL",
    status: "waiting",
    current_players: 5,
    max_players: 8,
    min_players: 4,
    players: [
      {
        user_id: "user7",
        username: "Grace",
        position: 1,
        status: "joined",
        bet_amount: NumberLong(2000),
        joined_at: new Date()
      },
      {
        user_id: "user8",
        username: "Henry",
        position: 2,
        status: "joined",
        bet_amount: NumberLong(2000),
        joined_at: new Date()
      },
      {
        user_id: "user9",
        username: "Ivy",
        position: 3,
        status: "joined",
        bet_amount: NumberLong(2000),
        joined_at: new Date()
      },
      {
        user_id: "user10",
        username: "Jack",
        position: 4,
        status: "joined",
        bet_amount: NumberLong(2000),
        joined_at: new Date()
      },
      {
        user_id: "user11",
        username: "Kate",
        position: 5,
        status: "joined",
        bet_amount: NumberLong(2000),
        joined_at: new Date()
      }
    ],
    configuration: {
      game_type: "PRIZEWHEEL",
      bet_limits: {
        min_bet: NumberLong(2000),
        max_bet: NumberLong(20000),
        currency: "USD"
      },
      timeouts: {
        waiting_timeout: NumberLong(900000),
        playing_timeout: NumberLong(1200000),
        idle_timeout: NumberLong(3600000)
      },
      auto_start: false,
      is_private: false,
      password: "",
      game_specific: {
        wheel_segments: 16,
        animation_duration: 8000,
        tournament_mode: true
      }
    },
    last_activity: new Date(),
    created_by: "admin",
    created_at: new Date(),
    updated_at: new Date()
  }
];

// Insert the test rooms
const result = db.rooms.insertMany(testRooms);

print(`Successfully inserted ${result.insertedIds.length} test rooms:`);
testRooms.forEach((room, index) => {
  print(`  ${index + 1}. ${room.name} (${room.game_type}) - ${room.current_players}/${room.max_players} players`);
});

// Verify the rooms were created
const roomCount = db.rooms.countDocuments();
print(`\nTotal rooms in database: ${roomCount}`);

// Show room summary by game type
const prizeWheelCount = db.rooms.countDocuments({ game_type: "PRIZEWHEEL" });
const amidakujiCount = db.rooms.countDocuments({ game_type: "AMIDAKUJI" });

print(`\nRoom summary:`);
print(`  PRIZEWHEEL rooms: ${prizeWheelCount}`);
print(`  AMIDAKUJI rooms: ${amidakujiCount}`);

print('\nTest rooms seeding completed successfully!');
