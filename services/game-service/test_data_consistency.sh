#!/bin/bash

# Data Consistency Test Script
# Tests the enhanced room leave functionality and data consistency

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}
TEST_USER_ID="test-user-consistency-$(date +%s)"
TEST_USERNAME="TestUser$(date +%s)"
TEST_ROOM_ID=""

echo -e "${BLUE}🧪 Starting Data Consistency Test${NC}"
echo -e "${BLUE}User ID: $TEST_USER_ID${NC}"
echo -e "${BLUE}Username: $TEST_USERNAME${NC}"

# Function to check if Redis is available
check_redis() {
    if ! redis-cli -h $REDIS_HOST -p $REDIS_PORT ping >/dev/null 2>&1; then
        echo -e "${RED}❌ Redis is not available at $REDIS_HOST:$REDIS_PORT${NC}"
        exit 1
    fi
    echo -e "${GREEN}✓ Redis connection verified${NC}"
}

# Function to get room list and find a suitable room
find_test_room() {
    echo -e "${YELLOW}🔍 Finding a suitable test room...${NC}"
    
    # Get room list from Game Service
    local correlation_id="test-room-list-$(date +%s%3N)"
    local response_channel="game:room:list:response:$correlation_id"
    
    # Subscribe to response channel
    redis-cli -h $REDIS_HOST -p $REDIS_PORT --csv PSUBSCRIBE "$response_channel" &
    local redis_pid=$!
    
    # Give Redis time to subscribe
    sleep 1
    
    # Request room list
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "consistency-test",
    "version": "1.0",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "list_rooms",
    "payload": {
      "gameType": "prize_wheel",
      "status": "waiting",
      "hasSpace": true,
      "limit": 5
    }
  }
}
EOF
)
    
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "game:requests" >/dev/null
    
    # Wait for response
    sleep 3
    
    # Kill the Redis subscription
    kill $redis_pid 2>/dev/null || true
    
    # For now, use a hardcoded room ID - in a real test, parse the response
    TEST_ROOM_ID="68412c9af494b684c1c18ecf"
    echo -e "${GREEN}✓ Using test room: $TEST_ROOM_ID${NC}"
}

# Function to join room
join_room() {
    echo -e "${YELLOW}🚪 Joining room $TEST_ROOM_ID...${NC}"
    
    local correlation_id="test-join-$(date +%s%3N)"
    local response_channel="room:join:response:$correlation_id"
    
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "consistency-test",
    "version": "1.0",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "join_room",
    "payload": {
      "roomId": "$TEST_ROOM_ID",
      "userId": "$TEST_USER_ID",
      "username": "$TEST_USERNAME",
      "betAmount": 10,
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "room:join" >/dev/null
    
    echo -e "${GREEN}✓ Join room request sent${NC}"
    sleep 2
}

# Function to check player status in room
check_player_status() {
    echo -e "${YELLOW}🔍 Checking player status in room...${NC}"
    
    local correlation_id="test-status-$(date +%s%3N)"
    local response_channel="game:player:status:response:$correlation_id"
    
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "consistency-test",
    "version": "1.0",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "get_player_status",
    "payload": {
      "roomId": "$TEST_ROOM_ID",
      "userId": "$TEST_USER_ID",
      "checkConsistency": true,
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "game:requests" >/dev/null
    
    echo -e "${GREEN}✓ Player status check sent${NC}"
    sleep 2
}

# Function to leave room with enhanced consistency
leave_room_enhanced() {
    echo -e "${YELLOW}🚪 Leaving room with enhanced consistency...${NC}"
    
    local correlation_id="test-leave-enhanced-$(date +%s%3N)"
    local response_channel="room:leave:response:$correlation_id"
    
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "consistency-test",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "leave_room",
    "payload": {
      "roomId": "$TEST_ROOM_ID",
      "userId": "$TEST_USER_ID",
      "username": "$TEST_USERNAME",
      "reason": "consistency_test",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "room:leave" >/dev/null
    
    echo -e "${GREEN}✓ Enhanced leave room request sent${NC}"
    sleep 3
}

# Function to verify player is completely removed
verify_removal() {
    echo -e "${YELLOW}🔍 Verifying player removal...${NC}"
    
    # Check player status again
    check_player_status
    
    # Check if player is in any rooms
    local correlation_id="test-verify-$(date +%s%3N)"
    local response_channel="game:player:rooms:response:$correlation_id"
    
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "consistency-test",
    "version": "1.0",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "get_player_rooms",
    "payload": {
      "userId": "$TEST_USER_ID",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "game:requests" >/dev/null
    
    echo -e "${GREEN}✓ Player rooms verification sent${NC}"
    sleep 2
}

# Function to monitor Redis events
monitor_events() {
    echo -e "${YELLOW}📡 Monitoring Redis events for 10 seconds...${NC}"
    
    # Monitor multiple channels
    timeout 10s redis-cli -h $REDIS_HOST -p $REDIS_PORT --csv PSUBSCRIBE \
        "room:$TEST_ROOM_ID:events" \
        "game:global:events" \
        "socket:events" \
        "room:leave:response:*" \
        "game:player:status:response:*" 2>/dev/null || true
    
    echo -e "${GREEN}✓ Event monitoring completed${NC}"
}

# Main test execution
main() {
    echo -e "${BLUE}🚀 Starting Data Consistency Test Suite${NC}"
    
    check_redis
    find_test_room
    
    echo -e "${BLUE}📋 Test Plan:${NC}"
    echo -e "  1. Join room"
    echo -e "  2. Check player status"
    echo -e "  3. Leave room with enhanced consistency"
    echo -e "  4. Verify complete removal"
    echo -e "  5. Monitor events"
    
    # Start event monitoring in background
    monitor_events &
    local monitor_pid=$!
    
    # Execute test steps
    join_room
    check_player_status
    leave_room_enhanced
    verify_removal
    
    # Wait for monitoring to complete
    wait $monitor_pid 2>/dev/null || true
    
    echo -e "${GREEN}✅ Data Consistency Test Completed${NC}"
    echo -e "${BLUE}Check the Game Service and Socket Gateway logs for detailed results${NC}"
}

# Run the test
main "$@"
