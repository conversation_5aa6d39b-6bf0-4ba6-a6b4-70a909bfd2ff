version: '3.8'

services:
  # MongoDB for testing
  mongodb:
    image: mongo:6.0
    container_name: game-service-test-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: testuser
      MONGO_INITDB_ROOT_PASSWORD: testpass
      MONGO_INITDB_DATABASE: xzgame_test
    volumes:
      - mongodb_test_data:/data/db
      - ./tests/fixtures/mongodb-init.js:/docker-entrypoint-initdb.d/init.js:ro
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for testing
  redis:
    image: redis:7-alpine
    container_name: game-service-test-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass testpass
    volumes:
      - redis_test_data:/data
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Test runner service (optional - for running tests in container)
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: game-service-test-runner
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - MONGODB_TEST_URL=*****************************************/xzgame_test?authSource=admin
      - REDIS_TEST_URL=redis://:testpass@redis:6379
      - INTEGRATION_TESTS=true
      - GO_ENV=test
    volumes:
      - .:/app
      - test_cache:/go/pkg/mod
      - test_build_cache:/root/.cache/go-build
    working_dir: /app
    networks:
      - test-network
    profiles:
      - test-container
    command: ["make", "test-all"]

  # MongoDB Express for test database inspection (optional)
  mongo-express:
    image: mongo-express:latest
    container_name: game-service-test-mongo-express
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: testuser
      ME_CONFIG_MONGODB_ADMINPASSWORD: testpass
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - test-network
    profiles:
      - debug

  # Redis Commander for Redis inspection (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: game-service-test-redis-commander
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:testpass
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - test-network
    profiles:
      - debug

volumes:
  mongodb_test_data:
    driver: local
  redis_test_data:
    driver: local
  test_cache:
    driver: local
  test_build_cache:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
