# Prize Wheel Color Cleanup and Room State Synchronization Implementation

This document describes the implementation of proper cleanup of selected wheel colors and comprehensive room state synchronization when players leave or get kicked from rooms during the Prize Wheel game color selection phase.

## Overview

When a player leaves a room (voluntarily or through disconnect) or gets kicked by an admin, their selected color should be removed from the room state and made available for other players to select again. Additionally, all remaining players in the room should receive comprehensive real-time updates about the complete room state, including player information, color selections, ready states, and game configuration.

## Implementation Details

### Game Service Changes

#### 1. New Method: `removePlayerColorSelectionAndBroadcast`

**Location**: `services/game-service/internal/services/request_handler.go`

This method handles both color cleanup and broadcasting the updated state to all remaining players:

```go
func (s *RequestHandlerService) removePlayerColorSelectionAndBroadcast(ctx context.Context, roomID, userID, username, reason string) error
```

**Features**:
- Removes the player's color selection from Redis cache
- Gets updated room data for broadcasting
- Generates new color state data with the player removed
- Broadcasts `room_color_state_updated` event to all remaining players
- Handles errors gracefully without failing the leave/kick operation

#### 2. Enhanced Leave Room Handling

**Location**: `services/game-service/internal/services/request_handler.go` - `handleLeaveRoom` method

- Now uses `removePlayerColorSelectionAndBroadcast` instead of the basic `removePlayerColorSelection`
- Ensures color cleanup happens with real-time broadcasting for voluntary leaves

#### 3. Enhanced Kick Player Handling

**Location**: `services/game-service/internal/services/request_handler.go` - `handlePlayerKicked` method

- Added color cleanup logic using `removePlayerColorSelectionAndBroadcast`
- Ensures color cleanup happens with real-time broadcasting for kicked players
- Handles both admin kicks and system kicks (e.g., insufficient balance)

#### 4. Comprehensive Room State Generation

**Location**: `services/game-service/internal/services/request_handler.go` - `generateComprehensiveRoomState` method

- Creates detailed room state with all player information
- Includes color selections, ready states, and game configuration
- Provides action context and timestamps
- Generates available colors information with selection status

#### 5. Room State Broadcasting

**Location**: `services/game-service/internal/services/request_handler.go` - `broadcastComprehensiveRoomStateUpdate` method

- Broadcasts complete room state to all remaining players via `room_state_updated` events
- Triggered after both leave and kick operations
- Ensures all clients have synchronized room information
- Publishes to both primary and legacy channels for compatibility

### Socket Gateway Integration

**Location**: `services/socket-gateway/src/services/socketService.js`

The socket gateway already has the necessary infrastructure to handle color state updates:

#### 1. Event Handling

- `handleRoomColorStateUpdated` method processes `room_color_state_updated` events
- Broadcasts enhanced color state to all clients in the room
- Maintains backward compatibility with `wheel_color_selected` events

#### 2. Auto-Leave Integration

- `executeAutoLeave` method uses `requestGameServiceLeaveRoom`
- This triggers the game service's leave room handling with color cleanup
- Ensures color cleanup happens for disconnect scenarios

## Event Flow

### Voluntary Leave Scenario

1. Player initiates leave room request
2. Socket gateway forwards request to game service
3. Game service processes leave room:
   - Removes player from room
   - Calls `removePlayerColorSelectionAndBroadcast`
   - Publishes `room_color_state_updated` event
4. Socket gateway receives and broadcasts color state update
5. All remaining players receive updated color state

### Player Kick Scenario

1. Admin or system initiates player kick
2. Manager service notifies game service via Redis
3. Game service processes kick:
   - Calls `removePlayerColorSelectionAndBroadcast`
   - Removes player from room cache
   - Publishes `room_color_state_updated` event
4. Socket gateway receives and broadcasts color state update
5. All remaining players receive updated color state

### Disconnect Scenario

1. Player disconnects from socket
2. Socket gateway detects disconnect
3. If player is not ready, auto-leave is triggered
4. Auto-leave follows the same flow as voluntary leave
5. Color cleanup happens automatically

## Color State Update Event Structure

```javascript
{
  "event": {
    "type": "room_color_state_updated",
    "payload": {
      "roomId": "room_id",
      "selectorUserId": "leaving_player_id",
      "selectorUsername": "leaving_player_name",
      "selectedColorId": "", // Empty for leave/kick events
      "playerColors": {
        // Only remaining players
        "player1": {
          "colorId": "red",
          "username": "Player1",
          "isReady": true,
          "colorName": "Red",
          "colorHex": "#ef4444"
        }
      },
      "availableColors": [
        // Includes the color that was freed up
        {
          "id": "blue",
          "name": "Blue", 
          "hex": "#3b82f6",
          "isAvailable": true
        }
      ],
      "totalPlayers": 1,
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## Comprehensive Room State Update Event Structure

```javascript
{
  "event": {
    "type": "room_state_updated",
    "payload": {
      "roomId": "room_id",
      "action": "player_left", // or "player_kicked"
      "actionUserId": "leaving_player_id",
      "actionUsername": "leaving_player_name",
      "roomState": {
        // Basic room information
        "roomId": "room_id",
        "roomName": "Test Room",
        "status": "waiting",
        "gameType": "prize_wheel",
        "playerCount": 2,
        "maxPlayers": 4,
        "minPlayers": 2,
        "readyCount": 1,
        "canStartGame": false,
        "gameInProgress": false,
        "prizePool": 0,

        // Game configuration
        "configuration": {
          "betAmount": 100,
          "autoStart": true,
          "gameSpecific": {},
          "betLimits": {
            "minBet": 50,
            "maxBet": 500
          },
          "timeouts": {
            "readyTimeout": 30000
          },
          "isPrivate": false
        },

        // Player information (updated after leave/kick)
        "players": [
          {
            "userId": "player1",
            "username": "Player1",
            "isReady": true,
            "colorId": "red",
            "colorName": "Red",
            "colorHex": "#ef4444"
          }
        ],

        // Color selection state
        "colorState": {
          "availableColors": [
            {
              "id": "blue",
              "name": "Blue",
              "hex": "#3b82f6",
              "isAvailable": true,
              "selectedBy": null,
              "selectedByUsername": ""
            }
          ],
          "playerColors": {
            "player1": "red"
          }
        },

        // Action context
        "lastAction": {
          "action": "left",
          "userId": "leaving_player_id",
          "timestamp": "2024-01-01T00:00:00.000Z"
        },

        // Timestamps
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## Key Features

### 1. Real-time Synchronization
- All remaining players receive immediate updates when a player leaves/gets kicked
- Color state is synchronized across all clients
- No manual refresh required

### 2. Color Availability
- Removed player's color becomes available for selection again
- Available colors list is updated in real-time
- Prevents color conflicts

### 3. State Preservation
- Other players' color selections and ready states are preserved
- Only the leaving/kicked player is removed from the state
- Room state remains consistent

### 4. Error Handling
- Color cleanup failures don't prevent leave/kick operations
- Graceful fallbacks for missing room data
- Comprehensive logging for debugging

### 5. Multi-scenario Support
- Voluntary leave
- Admin kick
- System kick (e.g., insufficient balance)
- Auto-leave on disconnect

### 6. Comprehensive Room State Synchronization
- Complete room information broadcasted to all remaining players
- Includes player details, color selections, ready states, and game configuration
- Action context provides information about what triggered the update
- Timestamps for tracking state changes
- Fallback room state for error scenarios

## Testing

### Color Cleanup Tests

Comprehensive test suite in `services/game-service/internal/services/color_cleanup_test.go`:

- `TestColorCleanupOnVoluntaryLeave`: Tests color cleanup when player leaves voluntarily
- `TestColorCleanupOnPlayerKick`: Tests color cleanup when player gets kicked
- `TestColorCleanupPreservesOtherPlayerStates`: Ensures other players' states are preserved

### Room State Synchronization Tests

Comprehensive test suite in `services/game-service/internal/services/room_sync_test.go`:

- `TestComprehensiveRoomStateSyncOnLeave`: Tests comprehensive room state sync when player leaves
- `TestComprehensiveRoomStateSyncOnKick`: Tests comprehensive room state sync when player gets kicked

Run tests with:
```bash
cd services/game-service
go test -v ./internal/services -run TestColorCleanupSuite
go test -v ./internal/services -run TestRoomSyncSuite
```

## Integration Points

### 1. Manager Service
- Kick notifications trigger color cleanup in game service
- No changes required in manager service

### 2. Socket Gateway
- Existing event handling infrastructure supports color cleanup
- No additional changes required

### 3. Client Applications
- Existing `room_color_state_updated` event handlers will receive cleanup updates
- Prize Wheel UI will automatically update to reflect color changes

## Backward Compatibility

- Maintains existing event structure and naming
- Existing clients will continue to work without changes
- Additional fields in events are optional and don't break existing functionality

## Performance Considerations

- Color cleanup is asynchronous and doesn't block leave/kick operations
- Redis operations are optimized with appropriate TTL values
- Broadcasting is efficient using room-specific channels
