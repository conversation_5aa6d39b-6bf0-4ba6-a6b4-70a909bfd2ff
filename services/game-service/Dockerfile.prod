# Production Dockerfile for Game Service
# Multi-stage build for optimized production image

# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    git \
    make \
    protobuf \
    protobuf-dev

# Set working directory
WORKDIR /app

# Install protobuf generators
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@latest && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Generate protobuf code
RUN make proto-gen

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags '-extldflags "-static" -s -w' \
    -o game-service \
    ./cmd/server/main.go

# Production stage
FROM alpine:3.18

# Install runtime dependencies
RUN apk --no-cache add \
    ca-certificates \
    tzdata

# Create non-root user
RUN addgroup -g 1001 -S gameservice && \
    adduser -u 1001 -S gameservice -G gameservice

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/game-service .

# Copy configuration files if any
COPY --from=builder /app/configs ./configs

# Create necessary directories
RUN mkdir -p logs tmp && \
    chown -R gameservice:gameservice /app

# Switch to non-root user
USER gameservice

# Expose ports
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./game-service"]
