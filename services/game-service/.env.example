# Game Service Environment Configuration

# Server Configuration
GO_ENV=development
PORT=8080
METRICS_PORT=9090
READ_TIMEOUT=30s
WRITE_TIMEOUT=30s

# Database Configuration
MONGODB_URL=mongodb://localhost:27017/xzgame
REDIS_URL=redis://localhost:6379
DATABASE_NAME=xzgame

# Security Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_ISSUER=xzgame-auth-service
JWT_AUDIENCE=xzgame-api,xzgame-game-service
RANDOM_SEED_SECRET=your_random_seed_secret_change_in_production
ENCRYPTION_KEY=your_encryption_key_here_change_in_production

# Game Configuration
MAX_CONCURRENT_GAMES=1000
DEFAULT_TIMEOUT=60s
MAX_PLAYERS_PER_ROOM=8
SESSION_CLEANUP_INTERVAL=5m
ARCHIVE_COMPLETED_AFTER=24h

# Redis Configuration (Optimized)
REDIS_POOL_SIZE=100
REDIS_MIN_IDLE_CONNS=20
REDIS_TIMEOUT=3s
REDIS_TTL=30m
REDIS_MAX_RETRIES=3
REDIS_RETRY_BACKOFF_MIN=8ms
REDIS_RETRY_BACKOFF_MAX=512ms

# Database Configuration (Optimized)
MONGODB_MAX_POOL_SIZE=200
MONGODB_MIN_POOL_SIZE=20
MONGODB_MAX_CONN_IDLE_TIME=15m
MONGODB_SOCKET_TIMEOUT=30s
MONGODB_HEARTBEAT_INTERVAL=10s

# Logging Configuration
LOG_LEVEL=info

# Monitoring Configuration
METRICS_ENABLED=true
TRACING_ENABLED=false
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_MONITORING_INTERVAL=30s

# Rate Limiting Configuration
RATE_LIMIT_WINDOW=60s
RATE_LIMIT_MAX=100

# Performance Configuration
WORKER_POOL_SIZE=50
WORKER_QUEUE_SIZE=1000
OBJECT_POOL_ENABLED=true
CIRCUIT_BREAKER_ENABLED=true
BATCH_OPERATIONS_ENABLED=true

# Circuit Breaker Configuration
CIRCUIT_BREAKER_MAX_REQUESTS=3
CIRCUIT_BREAKER_INTERVAL=60s
CIRCUIT_BREAKER_TIMEOUT=30s
CIRCUIT_BREAKER_FAILURE_THRESHOLD=0.6

# External Services Configuration
MANAGER_SERVICE_URL=http://localhost:3002
EXTERNAL_SERVICE_TIMEOUT=10s
EXTERNAL_SERVICE_MAX_RETRIES=3
