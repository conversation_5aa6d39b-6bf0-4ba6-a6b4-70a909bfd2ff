package controllers

import (
	"context"
	"strconv"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/services"
	pb "github.com/xzgame/game-service/proto"
)

// GameController implements the GameService gRPC interface
type GameController struct {
	pb.UnimplementedGameServiceServer
	gameService   services.GameService
	roomService   services.RoomService
	playerService services.PlayerService
	logger        *logrus.Logger
}

// NewGameController creates a new game controller instance
func NewGameController(
	gameService services.GameService,
	roomService services.RoomService,
	playerService services.PlayerService,
) *GameController {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &GameController{
		gameService:   gameService,
		roomService:   roomService,
		playerService: playerService,
		logger:        logger,
	}
}

// Create<PERSON>oom creates a new game room
func (c *GameController) CreateRoom(ctx context.Context, req *pb.CreateRoomRequest) (*pb.CreateRoomResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"name":        req.Name,
		"game_type":   req.GameType,
		"max_players": req.MaxPlayers,
		"user_id":     req.UserId,
	}).Info("Creating room")

	// Convert protobuf request to internal model
	createReq := models.CreateRoomRequest{
		Name:       req.Name,
		GameType:   convertGameType(req.GameType),
		MaxPlayers: int(req.MaxPlayers),
		MinPlayers: int(req.MinPlayers),
		BetLimits: models.BetLimitConfig{
			MinBet:   req.BetLimits.MinBet,
			MaxBet:   req.BetLimits.MaxBet,
			Currency: req.BetLimits.Currency,
		},
		AutoStart:    req.AutoStart,
		IsPrivate:    req.IsPrivate,
		Password:     req.Password,
		GameSpecific: convertStringMapToInterface(req.GameSpecific),
	}

	room, err := c.roomService.CreateRoom(ctx, createReq)
	if err != nil {
		c.logger.WithError(err).Error("Failed to create room")
		return &pb.CreateRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert room to protobuf
	pbRoom := convertRoomToProto(room)

	return &pb.CreateRoomResponse{
		Room:    pbRoom,
		Success: true,
		Message: "Room created successfully",
	}, nil
}

// JoinRoom adds a player to a room
func (c *GameController) JoinRoom(ctx context.Context, req *pb.JoinRoomRequest) (*pb.JoinRoomResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id":    req.RoomId,
		"user_id":    req.UserId,
		"bet_amount": req.BetAmount,
	}).Info("Player joining room")

	// Convert protobuf request to internal model
	joinReq := models.JoinRoomRequest{
		RoomID:    req.RoomId,
		UserID:    req.UserId,
		Password:  req.Password,
		BetAmount: req.BetAmount,
	}

	room, err := c.roomService.JoinRoom(ctx, joinReq)
	if err != nil {
		c.logger.WithError(err).Error("Failed to join room")
		return &pb.JoinRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert room to protobuf
	pbRoom := convertRoomToProto(room)

	return &pb.JoinRoomResponse{
		Room:    pbRoom,
		Success: true,
		Message: "Joined room successfully",
	}, nil
}

// LeaveRoom removes a player from a room
func (c *GameController) LeaveRoom(ctx context.Context, req *pb.LeaveRoomRequest) (*pb.LeaveRoomResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id": req.RoomId,
		"user_id": req.UserId,
	}).Info("Player leaving room")

	// Convert protobuf request to internal model
	leaveReq := models.LeaveRoomRequest{
		RoomID: req.RoomId,
		UserID: req.UserId,
	}

	err := c.roomService.LeaveRoom(ctx, leaveReq)
	if err != nil {
		c.logger.WithError(err).Error("Failed to leave room")
		return &pb.LeaveRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.LeaveRoomResponse{
		Success: true,
		Message: "Left room successfully",
	}, nil
}

// StartGame starts a game in a room
func (c *GameController) StartGame(ctx context.Context, req *pb.StartGameRequest) (*pb.StartGameResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id": req.RoomId,
		"user_id": req.UserId,
	}).Info("Starting game")

	// Check if room is ready to start
	ready, err := c.roomService.CheckRoomReadiness(ctx, req.RoomId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to check room readiness")
		return &pb.StartGameResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	if !ready {
		return &pb.StartGameResponse{
			Success: false,
			Message: "Room is not ready to start a game",
		}, status.Error(codes.FailedPrecondition, "room not ready")
	}

	// Start game in room
	session, err := c.roomService.StartGameInRoom(ctx, req.RoomId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to start game")
		return &pb.StartGameResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Start the actual game logic
	err = c.gameService.StartGame(ctx, session.ID.Hex())
	if err != nil {
		c.logger.WithError(err).Error("Failed to execute game logic")
		return &pb.StartGameResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert session to protobuf
	pbSession := convertSessionToProto(session)

	return &pb.StartGameResponse{
		Session: pbSession,
		Success: true,
		Message: "Game started successfully",
	}, nil
}

// GetGameState retrieves the current game state
func (c *GameController) GetGameState(ctx context.Context, req *pb.GetGameStateRequest) (*pb.GetGameStateResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"session_id": req.SessionId,
		"user_id":    req.UserId,
	}).Info("Getting game state")

	gameState, err := c.gameService.GetGameState(ctx, req.SessionId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get game state")
		return &pb.GetGameStateResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert game state to session for protobuf
	session := &models.GameSession{
		ID:        convertStringToObjectID(gameState.SessionID),
		Status:    models.SessionStatus(gameState.Phase),
		Players:   gameState.Players,
		StartTime: gameState.StartTime,
		Results:   gameState.Results,
		UpdatedAt: gameState.LastUpdated,
	}

	pbSession := convertSessionToProto(session)

	// Convert game data to string map
	gameDataMap := make(map[string]string)
	for k, v := range gameState.GameData {
		gameDataMap[k] = convertToString(v)
	}

	return &pb.GetGameStateResponse{
		Session:  pbSession,
		GameData: gameDataMap,
		Success:  true,
		Message:  "Game state retrieved successfully",
	}, nil
}

// GetGameHistory retrieves game history for a player
func (c *GameController) GetGameHistory(ctx context.Context, req *pb.GetGameHistoryRequest) (*pb.GetGameHistoryResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"user_id":   req.UserId,
		"game_type": req.GameType,
		"page":      req.Page,
		"limit":     req.Limit,
	}).Info("Getting game history")

	var gameType *models.GameType
	if req.GameType != pb.GameType_GAME_TYPE_UNSPECIFIED {
		gt := convertGameType(req.GameType)
		gameType = &gt
	}

	page := int(req.Page)
	limit := int(req.Limit)
	if limit <= 0 {
		limit = 20
	}

	history, err := c.gameService.GetGameHistory(ctx, req.UserId, gameType, page, limit)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get game history")
		return &pb.GetGameHistoryResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert history to sessions
	var sessions []*pb.GameSession
	for _, h := range history {
		session := &models.GameSession{
			ID:        convertStringToObjectID(h.SessionID),
			RoomID:    h.RoomID,
			GameType:  h.GameType,
			Status:    models.SessionStatusCompleted,
			Players:   h.Players,
			Results:   &h.Results,
			CreatedAt: h.CreatedAt,
		}
		sessions = append(sessions, convertSessionToProto(session))
	}

	// Create pagination info
	pagination := &pb.Pagination{
		CurrentPage: int32(page),
		PerPage:     int32(limit),
		TotalCount:  int64(len(history)), // This should be actual total count
		TotalPages:  int32((len(history) + limit - 1) / limit),
	}

	return &pb.GetGameHistoryResponse{
		Sessions:   sessions,
		Pagination: pagination,
		Success:    true,
		Message:    "Game history retrieved successfully",
	}, nil
}

// SetPlayerReady sets a player's ready status
func (c *GameController) SetPlayerReady(ctx context.Context, req *pb.SetPlayerReadyRequest) (*pb.SetPlayerReadyResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id": req.RoomId,
		"user_id": req.UserId,
		"ready":   req.Ready,
	}).Info("Setting player ready status")

	// Convert protobuf request to internal model
	readyReq := models.PlayerReadyRequest{
		RoomID: req.RoomId,
		UserID: req.UserId,
		Ready:  req.Ready,
	}

	_, err := c.roomService.SetPlayerReady(ctx, readyReq)
	if err != nil {
		c.logger.WithError(err).Error("Failed to set player ready status")
		return &pb.SetPlayerReadyResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.SetPlayerReadyResponse{
		Success: true,
		Message: "Player ready status updated successfully",
	}, nil
}

// GetPlayerStatus retrieves a player's current status
func (c *GameController) GetPlayerStatus(ctx context.Context, req *pb.GetPlayerStatusRequest) (*pb.GetPlayerStatusResponse, error) {
	c.logger.WithField("user_id", req.UserId).Info("Getting player status")

	playerStatus, err := c.playerService.GetPlayerStatus(ctx, req.UserId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get player status")
		return &pb.GetPlayerStatusResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	var currentRoomID string
	var currentSessionID string
	if playerStatus.CurrentRoomID != nil {
		currentRoomID = *playerStatus.CurrentRoomID
	}
	if playerStatus.CurrentSessionID != nil {
		currentSessionID = *playerStatus.CurrentSessionID
	}

	return &pb.GetPlayerStatusResponse{
		CurrentRoomId:    currentRoomID,
		CurrentSessionId: currentSessionID,
		Status:           convertPlayerStatusToProto(playerStatus.Status),
		Success:          true,
		Message:          "Player status retrieved successfully",
	}, nil
}

// GetGameConfiguration retrieves game configuration
func (c *GameController) GetGameConfiguration(ctx context.Context, req *pb.GetGameConfigurationRequest) (*pb.GetGameConfigurationResponse, error) {
	c.logger.WithField("game_type", req.GameType).Info("Getting game configuration")

	// This would typically call a config service
	// For now, return a basic configuration
	config := &pb.GameConfiguration{
		Id:         "default",
		GameType:   req.GameType,
		Version:    1,
		Rules:      make(map[string]string),
		Payouts:    []*pb.PayoutRule{},
		HouseEdge:  0.05,
		MinPlayers: 2,
		MaxPlayers: 8,
		BetLimits: &pb.BetLimits{
			MinBet:   100,
			MaxBet:   10000,
			Currency: "USD",
		},
		Timeouts: &pb.Timeouts{
			WaitingTimeoutSeconds: 300,
			PlayingTimeoutSeconds: 600,
			IdleTimeoutSeconds:    1800,
		},
		IsActive: true,
	}

	return &pb.GetGameConfigurationResponse{
		Configuration: config,
		Success:       true,
		Message:       "Game configuration retrieved successfully",
	}, nil
}

// UpdateGameConfiguration updates game configuration
func (c *GameController) UpdateGameConfiguration(ctx context.Context, req *pb.UpdateGameConfigurationRequest) (*pb.UpdateGameConfigurationResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"game_type":     req.GameType,
		"admin_user_id": req.AdminUserId,
	}).Info("Updating game configuration")

	// This would typically call a config service
	// For now, just return success
	return &pb.UpdateGameConfigurationResponse{
		Success: true,
		Message: "Game configuration updated successfully",
	}, nil
}

// Helper conversion functions

// convertGameType converts protobuf GameType to internal GameType
func convertGameType(pbType pb.GameType) models.GameType {
	switch pbType {
	case pb.GameType_GAME_TYPE_PRIZEWHEEL:
		return models.GameTypePrizeWheel
	case pb.GameType_GAME_TYPE_AMIDAKUJI:
		return models.GameTypeAmidakuji
	default:
		return models.GameTypePrizeWheel
	}
}

// convertGameTypeToProto converts internal GameType to protobuf GameType
func convertGameTypeToProto(gameType models.GameType) pb.GameType {
	switch gameType {
	case models.GameTypePrizeWheel:
		return pb.GameType_GAME_TYPE_PRIZEWHEEL
	case models.GameTypeAmidakuji:
		return pb.GameType_GAME_TYPE_AMIDAKUJI
	default:
		return pb.GameType_GAME_TYPE_PRIZEWHEEL
	}
}

// convertRoomStatusToProto converts internal RoomStatus to protobuf RoomStatus
func convertRoomStatusToProto(status models.RoomStatus) pb.RoomStatus {
	switch status {
	case models.RoomStatusWaiting:
		return pb.RoomStatus_ROOM_STATUS_WAITING
	case models.RoomStatusActive:
		return pb.RoomStatus_ROOM_STATUS_ACTIVE
	case models.RoomStatusFull:
		return pb.RoomStatus_ROOM_STATUS_FULL
	case models.RoomStatusClosed:
		return pb.RoomStatus_ROOM_STATUS_CLOSED
	case models.RoomStatusArchived:
		return pb.RoomStatus_ROOM_STATUS_ARCHIVED
	default:
		return pb.RoomStatus_ROOM_STATUS_WAITING
	}
}

// convertSessionStatusToProto converts internal SessionStatus to protobuf SessionStatus
func convertSessionStatusToProto(status models.SessionStatus) pb.SessionStatus {
	switch status {
	case models.SessionStatusPending:
		return pb.SessionStatus_SESSION_STATUS_PENDING
	case models.SessionStatusWaiting:
		return pb.SessionStatus_SESSION_STATUS_WAITING
	case models.SessionStatusActive:
		return pb.SessionStatus_SESSION_STATUS_ACTIVE
	case models.SessionStatusCompleted:
		return pb.SessionStatus_SESSION_STATUS_COMPLETED
	case models.SessionStatusCancelled:
		return pb.SessionStatus_SESSION_STATUS_CANCELLED
	default:
		return pb.SessionStatus_SESSION_STATUS_PENDING
	}
}

// convertPlayerStatusToProto converts internal PlayerStatus to protobuf PlayerStatus
func convertPlayerStatusToProto(status string) pb.PlayerStatus {
	switch status {
	case "joined":
		return pb.PlayerStatus_PLAYER_STATUS_JOINED
	case "ready":
		return pb.PlayerStatus_PLAYER_STATUS_READY
	case "playing":
		return pb.PlayerStatus_PLAYER_STATUS_PLAYING
	case "finished":
		return pb.PlayerStatus_PLAYER_STATUS_FINISHED
	default:
		return pb.PlayerStatus_PLAYER_STATUS_JOINED
	}
}

// convertRoomToProto converts internal Room to protobuf Room
func convertRoomToProto(room *models.Room) *pb.Room {
	players := make([]*pb.Player, len(room.Players))
	for i, p := range room.Players {
		players[i] = &pb.Player{
			UserId:    p.UserID,
			Username:  p.Username,
			Position:  int32(p.Position),
			IsReady:   p.IsReady,
			BetAmount: p.BetAmount,
			Status:    convertPlayerStatusToProto(string(p.Status)),
		}
	}

	var currentSession string
	if room.CurrentSession != nil {
		currentSession = *room.CurrentSession
	}

	return &pb.Room{
		Id:             room.ID.Hex(),
		Name:           room.Name,
		GameType:       convertGameTypeToProto(room.GameType),
		Status:         convertRoomStatusToProto(room.Status),
		CurrentPlayers: int32(room.CurrentPlayers),
		MaxPlayers:     int32(room.MaxPlayers),
		MinPlayers:     int32(room.MinPlayers),
		Players:        players,
		Configuration: &pb.RoomConfiguration{
			GameType: convertGameTypeToProto(room.Configuration.GameType),
			BetLimits: &pb.BetLimits{
				MinBet:   room.Configuration.BetLimits.MinBet,
				MaxBet:   room.Configuration.BetLimits.MaxBet,
				Currency: room.Configuration.BetLimits.Currency,
			},
			AutoStart:    room.Configuration.AutoStart,
			IsPrivate:    room.Configuration.IsPrivate,
			Password:     room.Configuration.Password,
			GameSpecific: convertInterfaceMapToString(room.Configuration.GameSpecific),
		},
		CurrentSession: currentSession,
		CreatedBy:      room.CreatedBy,
	}
}

// convertSessionToProto converts internal GameSession to protobuf GameSession
func convertSessionToProto(session *models.GameSession) *pb.GameSession {
	players := make([]*pb.Player, len(session.Players))
	for i, p := range session.Players {
		players[i] = &pb.Player{
			UserId:    p.UserID,
			Position:  int32(p.Position),
			BetAmount: p.BetAmount,
			Status:    convertPlayerStatusToProto(string(p.Status)),
		}
	}

	pbSession := &pb.GameSession{
		Id:       session.ID.Hex(),
		RoomId:   session.RoomID,
		GameType: convertGameTypeToProto(session.GameType),
		Status:   convertSessionStatusToProto(session.Status),
		Players:  players,
		Configuration: &pb.SessionConfiguration{
			MinPlayers: int32(session.Configuration.MinPlayers),
			MaxPlayers: int32(session.Configuration.MaxPlayers),
			BetLimits: &pb.BetLimits{
				MinBet:   session.Configuration.BetLimits.MinBet,
				MaxBet:   session.Configuration.BetLimits.MaxBet,
				Currency: session.Configuration.BetLimits.Currency,
			},
			GameSpecific: convertInterfaceMapToString(session.Configuration.GameSpecific),
		},
		Version: session.Version,
	}

	if session.Results != nil {
		payouts := make([]*pb.PlayerPayout, len(session.Results.Payouts))
		for i, p := range session.Results.Payouts {
			payouts[i] = &pb.PlayerPayout{
				UserId:    p.UserID,
				Amount:    p.Amount,
				Reason:    p.Reason,
				Processed: p.Processed,
			}
		}

		gameData := make(map[string]string)
		for k, v := range session.Results.GameData {
			gameData[k] = convertToString(v)
		}

		pbSession.Results = &pb.GameResults{
			WinnerUserId:   session.Results.WinnerUserID,
			WinnerPosition: int32(session.Results.WinnerPosition),
			TotalBetPool:   session.Results.TotalBetPool,
			HouseTake:      session.Results.HouseTake,
			PrizePool:      session.Results.PrizePool,
			Payouts:        payouts,
			GameData:       gameData,
		}
	}

	return pbSession
}

// Helper functions
func convertStringToObjectID(s string) primitive.ObjectID {
	// This is a simplified implementation
	// In production, you'd properly convert string to ObjectID
	return primitive.NewObjectID()
}

func convertToString(v interface{}) string {
	switch val := v.(type) {
	case string:
		return val
	case int:
		return strconv.Itoa(val)
	case int64:
		return strconv.FormatInt(val, 10)
	case float64:
		return strconv.FormatFloat(val, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(val)
	default:
		return ""
	}
}

// convertStringMapToInterface converts map[string]string to map[string]interface{}
func convertStringMapToInterface(m map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range m {
		result[k] = v
	}
	return result
}

// convertInterfaceMapToString converts map[string]interface{} to map[string]string
func convertInterfaceMapToString(m map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range m {
		result[k] = convertToString(v)
	}
	return result
}
