package controllers

import (
	"context"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/services"
	pb "github.com/xzgame/game-service/proto"
)

// RoomController implements the RoomService gRPC interface
type RoomController struct {
	pb.UnimplementedRoomServiceServer
	roomService services.RoomService
	gameService services.GameService
	logger      *logrus.Logger
}

// NewRoomController creates a new room controller instance
func NewRoomController(
	roomService services.RoomService,
	gameService services.GameService,
) *RoomController {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &RoomController{
		roomService: roomService,
		gameService: gameService,
		logger:      logger,
	}
}

// ListRooms lists available rooms with filtering
func (c *RoomController) ListRooms(ctx context.Context, req *pb.ListRoomsRequest) (*pb.ListRoomsResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"game_type": req.GameType,
		"status":    req.Status,
		"has_space": req.HasSpace,
		"page":      req.Page,
		"limit":     req.Limit,
	}).Info("Listing rooms")

	// Convert protobuf request to internal filter
	filter := models.RoomListFilter{
		HasSpace: req.HasSpace,
		Page:     int(req.Page),
		Limit:    int(req.Limit),
	}

	if req.GameType != pb.GameType_GAME_TYPE_UNSPECIFIED {
		gameType := convertGameType(req.GameType)
		filter.GameType = &gameType
	}

	if req.Status != pb.RoomStatus_ROOM_STATUS_UNSPECIFIED {
		status := convertRoomStatus(req.Status)
		filter.Status = &status
	}

	// Set default limit if not provided
	if filter.Limit <= 0 {
		filter.Limit = 20
	}

	response, err := c.roomService.ListRooms(ctx, filter)
	if err != nil {
		c.logger.WithError(err).Error("Failed to list rooms")
		return &pb.ListRoomsResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	// Convert rooms to protobuf
	pbRooms := make([]*pb.Room, len(response.Rooms))
	for i, room := range response.Rooms {
		pbRooms[i] = convertRoomToProto(&room)
	}

	return &pb.ListRoomsResponse{
		Rooms: pbRooms,
		Pagination: &pb.Pagination{
			CurrentPage: int32(response.Pagination.CurrentPage),
			PerPage:     int32(response.Pagination.PerPage),
			TotalCount:  response.Pagination.TotalCount,
			TotalPages:  int32(response.Pagination.TotalPages),
		},
		Success: true,
		Message: "Rooms listed successfully",
	}, nil
}

// GetRoom retrieves a specific room by ID
func (c *RoomController) GetRoom(ctx context.Context, req *pb.GetRoomRequest) (*pb.GetRoomResponse, error) {
	c.logger.WithField("room_id", req.RoomId).Info("Getting room")

	room, err := c.roomService.GetRoom(ctx, req.RoomId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get room")
		return &pb.GetRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	pbRoom := convertRoomToProto(room)

	return &pb.GetRoomResponse{
		Room:    pbRoom,
		Success: true,
		Message: "Room retrieved successfully",
	}, nil
}

// UpdateRoom updates a room's settings
func (c *RoomController) UpdateRoom(ctx context.Context, req *pb.UpdateRoomRequest) (*pb.UpdateRoomResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id":       req.RoomId,
		"admin_user_id": req.AdminUserId,
	}).Info("Updating room")

	// Convert protobuf request to internal model
	updateReq := models.UpdateRoomRequest{
		RoomID: req.RoomId,
	}

	if req.Name != "" {
		updateReq.Name = &req.Name
	}

	if req.MaxPlayers > 0 {
		maxPlayers := int(req.MaxPlayers)
		updateReq.MaxPlayers = &maxPlayers
	}

	if req.BetLimits != nil {
		betLimits := models.BetLimitConfig{
			MinBet:   req.BetLimits.MinBet,
			MaxBet:   req.BetLimits.MaxBet,
			Currency: req.BetLimits.Currency,
		}
		updateReq.BetLimits = &betLimits
	}

	if req.GameSpecific != nil {
		updateReq.GameSpecific = convertStringMapToInterface(req.GameSpecific)
	}

	room, err := c.roomService.UpdateRoom(ctx, req.RoomId, updateReq)
	if err != nil {
		c.logger.WithError(err).Error("Failed to update room")
		return &pb.UpdateRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	pbRoom := convertRoomToProto(room)

	return &pb.UpdateRoomResponse{
		Room:    pbRoom,
		Success: true,
		Message: "Room updated successfully",
	}, nil
}

// DeleteRoom deletes a room
func (c *RoomController) DeleteRoom(ctx context.Context, req *pb.DeleteRoomRequest) (*pb.DeleteRoomResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"room_id":       req.RoomId,
		"admin_user_id": req.AdminUserId,
	}).Info("Deleting room")

	err := c.roomService.DeleteRoom(ctx, req.RoomId, req.AdminUserId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to delete room")
		return &pb.DeleteRoomResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.DeleteRoomResponse{
		Success: true,
		Message: "Room deleted successfully",
	}, nil
}

// GetRoomStats retrieves statistics for a room
func (c *RoomController) GetRoomStats(ctx context.Context, req *pb.GetRoomStatsRequest) (*pb.GetRoomStatsResponse, error) {
	c.logger.WithField("room_id", req.RoomId).Info("Getting room stats")

	stats, err := c.roomService.GetRoomStats(ctx, req.RoomId)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get room stats")
		return &pb.GetRoomStatsResponse{
			Success: false,
			Message: err.Error(),
		}, status.Error(codes.Internal, err.Error())
	}

	pbStats := &pb.RoomStats{
		RoomId:                  stats.RoomID,
		TotalGames:              int32(stats.TotalGames),
		TotalPlayers:            int32(stats.TotalPlayers),
		AverageGameTimeSeconds:  int64(stats.AverageGameTime.Seconds()),
		TotalBetVolume:          stats.TotalBetVolume,
	}

	if stats.LastGameAt != nil {
		// Convert time to protobuf timestamp
		// pbStats.LastGameAt = timestamppb.New(*stats.LastGameAt)
	}

	return &pb.GetRoomStatsResponse{
		Stats:   pbStats,
		Success: true,
		Message: "Room stats retrieved successfully",
	}, nil
}

// Helper conversion functions

// convertRoomStatus converts protobuf RoomStatus to internal RoomStatus
func convertRoomStatus(pbStatus pb.RoomStatus) models.RoomStatus {
	switch pbStatus {
	case pb.RoomStatus_ROOM_STATUS_WAITING:
		return models.RoomStatusWaiting
	case pb.RoomStatus_ROOM_STATUS_ACTIVE:
		return models.RoomStatusActive
	case pb.RoomStatus_ROOM_STATUS_FULL:
		return models.RoomStatusFull
	case pb.RoomStatus_ROOM_STATUS_CLOSED:
		return models.RoomStatusClosed
	case pb.RoomStatus_ROOM_STATUS_ARCHIVED:
		return models.RoomStatusArchived
	default:
		return models.RoomStatusWaiting
	}
}