package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GameType represents the type of game
type GameType string

const (
	GameTypePrizeWheel GameType = "prizewheel"
	GameTypeAmidakuji  GameType = "amidakuji"
)

// GamePhase represents the current phase of a game
type GamePhase string

const (
	GamePhasePending   GamePhase = "pending"
	GamePhaseWaiting   GamePhase = "waiting"
	GamePhaseActive    GamePhase = "active"
	GamePhaseCompleted GamePhase = "completed"
	GamePhaseCancelled GamePhase = "cancelled"
)

// SessionStatus represents the status of a game session
type SessionStatus string

const (
	SessionStatusPending   SessionStatus = "pending"
	SessionStatusWaiting   SessionStatus = "waiting"
	SessionStatusActive    SessionStatus = "active"
	SessionStatusCompleted SessionStatus = "completed"
	SessionStatusCancelled SessionStatus = "cancelled"
)

// PlayerStatus represents the status of a player in a session
type PlayerStatus string

const (
	PlayerStatusJoined   PlayerStatus = "joined"
	PlayerStatusReady    PlayerStatus = "ready"
	PlayerStatusPlaying  PlayerStatus = "playing"
	PlayerStatusFinished PlayerStatus = "finished"
)

// GameSession represents a complete game session
type GameSession struct {
	ID            primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	RoomID        string                 `bson:"room_id" json:"room_id"`
	GameType      GameType               `bson:"game_type" json:"game_type"`
	Status        SessionStatus          `bson:"status" json:"status"`
	Players       []SessionPlayer        `bson:"players" json:"players"`
	StartTime     time.Time              `bson:"start_time" json:"start_time"`
	EndTime       *time.Time             `bson:"end_time,omitempty" json:"end_time,omitempty"`
	Results       *GameResults           `bson:"results,omitempty" json:"results,omitempty"`
	Configuration SessionConfiguration   `bson:"configuration" json:"configuration"`
	FairnessProof *FairnessProof         `bson:"fairness_proof,omitempty" json:"fairness_proof,omitempty"`
	Version       int64                  `bson:"version" json:"version"`
	CreatedAt     time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt     time.Time              `bson:"updated_at" json:"updated_at"`
}

// SessionPlayer represents a player in a game session
type SessionPlayer struct {
	UserID    string        `bson:"user_id" json:"user_id"`
	Position  int           `bson:"position" json:"position"`
	BetAmount int64         `bson:"bet_amount" json:"bet_amount"`
	WinAmount int64         `bson:"win_amount" json:"win_amount"`
	JoinedAt  time.Time     `bson:"joined_at" json:"joined_at"`
	Status    PlayerStatus  `bson:"status" json:"status"`
}

// SessionConfiguration holds game-specific configuration
type SessionConfiguration struct {
	MinPlayers    int                    `bson:"min_players" json:"min_players"`
	MaxPlayers    int                    `bson:"max_players" json:"max_players"`
	BetLimits     BetLimitConfig         `bson:"bet_limits" json:"bet_limits"`
	Timeouts      TimeoutConfig          `bson:"timeouts" json:"timeouts"`
	GameSpecific  map[string]interface{} `bson:"game_specific" json:"game_specific"`
}

// BetLimitConfig defines betting limits
type BetLimitConfig struct {
	MinBet   int64  `bson:"min_bet" json:"min_bet"`
	MaxBet   int64  `bson:"max_bet" json:"max_bet"`
	Currency string `bson:"currency" json:"currency"`
}

// TimeoutConfig defines various timeout settings
type TimeoutConfig struct {
	WaitingTimeout time.Duration `bson:"waiting_timeout" json:"waiting_timeout"`
	PlayingTimeout time.Duration `bson:"playing_timeout" json:"playing_timeout"`
	IdleTimeout    time.Duration `bson:"idle_timeout" json:"idle_timeout"`
}

// GameResults holds the results of a completed game
type GameResults struct {
	WinnerUserID    string                 `bson:"winner_user_id" json:"winner_user_id"`
	WinnerPosition  int                    `bson:"winner_position" json:"winner_position"`
	TotalBetPool    int64                  `bson:"total_bet_pool" json:"total_bet_pool"`
	HouseTake       int64                  `bson:"house_take" json:"house_take"`
	PrizePool       int64                  `bson:"prize_pool" json:"prize_pool"`
	Payouts         []PlayerPayout         `bson:"payouts" json:"payouts"`
	GameData        map[string]interface{} `bson:"game_data" json:"game_data"`
	CompletedAt     time.Time              `bson:"completed_at" json:"completed_at"`
}

// PlayerPayout represents a payout to a player
type PlayerPayout struct {
	UserID    string  `bson:"user_id" json:"user_id"`
	Amount    int64   `bson:"amount" json:"amount"`
	Reason    string  `bson:"reason" json:"reason"`
	Processed bool    `bson:"processed" json:"processed"`
}

// FairnessProof provides cryptographic proof of game fairness
type FairnessProof struct {
	Seed         string    `bson:"seed" json:"seed"`
	Algorithm    string    `bson:"algorithm" json:"algorithm"`
	Timestamp    time.Time `bson:"timestamp" json:"timestamp"`
	Hash         string    `bson:"hash" json:"hash"`
	Verification string    `bson:"verification" json:"verification"`
	PublicData   string    `bson:"public_data" json:"public_data"`
}

// GameState represents the current state of an active game
type GameState struct {
	SessionID     string                 `json:"session_id"`
	Phase         GamePhase              `json:"phase"`
	Players       []SessionPlayer        `json:"players"`
	StartTime     time.Time              `json:"start_time"`
	GameData      map[string]interface{} `json:"game_data"`
	Results       *GameResults           `json:"results,omitempty"`
	LastUpdated   time.Time              `json:"last_updated"`
}

// GameHistory represents a completed game for historical records
type GameHistory struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	SessionID     string             `bson:"session_id" json:"session_id"`
	RoomID        string             `bson:"room_id" json:"room_id"`
	GameType      GameType           `bson:"game_type" json:"game_type"`
	Players       []SessionPlayer    `bson:"players" json:"players"`
	Results       GameResults        `bson:"results" json:"results"`
	FairnessProof FairnessProof      `bson:"fairness_proof" json:"fairness_proof"`
	Duration      time.Duration      `bson:"duration" json:"duration"`
	CompletedAt   time.Time          `bson:"completed_at" json:"completed_at"`
	CreatedAt     time.Time          `bson:"created_at" json:"created_at"`
}

// GameConfiguration represents configurable game parameters
type GameConfiguration struct {
	ID           primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	GameType     GameType               `bson:"game_type" json:"game_type"`
	Version      int                    `bson:"version" json:"version"`
	Rules        map[string]interface{} `bson:"rules" json:"rules"`
	Payouts      []PayoutRule           `bson:"payouts" json:"payouts"`
	HouseEdge    float64                `bson:"house_edge" json:"house_edge"`
	MinPlayers   int                    `bson:"min_players" json:"min_players"`
	MaxPlayers   int                    `bson:"max_players" json:"max_players"`
	BetLimits    BetLimitConfig         `bson:"bet_limits" json:"bet_limits"`
	Timeouts     TimeoutConfig          `bson:"timeouts" json:"timeouts"`
	IsActive     bool                   `bson:"is_active" json:"is_active"`
	CreatedAt    time.Time              `bson:"created_at" json:"created_at"`
	ActivatedAt  *time.Time             `bson:"activated_at,omitempty" json:"activated_at,omitempty"`
}

// PayoutRule defines a payout rule for games
type PayoutRule struct {
	Condition   string  `bson:"condition" json:"condition"`
	Multiplier  float64 `bson:"multiplier" json:"multiplier"`
	Probability float64 `bson:"probability" json:"probability"`
	Description string  `bson:"description" json:"description"`
}

// PayoutCalculation represents the result of payout calculations
type PayoutCalculation struct {
	GameType     GameType       `json:"game_type"`
	TotalBetPool int64          `json:"total_bet_pool"`
	HouseEdge    float64        `json:"house_edge"`
	HouseTake    int64          `json:"house_take"`
	PrizePool    int64          `json:"prize_pool"`
	Payouts      []PlayerPayout `json:"payouts"`
	CalculatedAt time.Time      `json:"calculated_at"`
}

// GameHistoryFilter represents filters for game history queries
type GameHistoryFilter struct {
	GameType     *GameType  `json:"game_type,omitempty"`
	PlayerID     *string    `json:"player_id,omitempty"`
	DateFrom     *time.Time `json:"date_from,omitempty"`
	DateTo       *time.Time `json:"date_to,omitempty"`
	MinBetAmount *int64     `json:"min_bet_amount,omitempty"`
	MaxBetAmount *int64     `json:"max_bet_amount,omitempty"`
	Page         int        `json:"page"`
	Limit        int        `json:"limit"`
}

// PlayerSession represents a player's current session state
type PlayerSession struct {
	UserID       string    `bson:"_id" json:"user_id"`
	SessionID    string    `bson:"session_id" json:"session_id"`
	RoomID       string    `bson:"room_id" json:"room_id"`
	Status       string    `bson:"status" json:"status"`
	LastPing     time.Time `bson:"last_ping" json:"last_ping"`
	ConnectionID string    `bson:"connection_id" json:"connection_id"`
	CreatedAt    time.Time `bson:"created_at" json:"created_at"`
	UpdatedAt    time.Time `bson:"updated_at" json:"updated_at"`
}

// PlayerActivity represents a player's activity record
type PlayerActivity struct {
	UserID       string                 `bson:"user_id" json:"user_id"`
	ActivityType string                 `bson:"activity_type" json:"activity_type"`
	RoomID       *string                `bson:"room_id,omitempty" json:"room_id,omitempty"`
	SessionID    *string                `bson:"session_id,omitempty" json:"session_id,omitempty"`
	Data         map[string]interface{} `bson:"data,omitempty" json:"data,omitempty"`
	Timestamp    time.Time              `bson:"timestamp" json:"timestamp"`
}

// PlayerStatusInfo represents a player's current status information
type PlayerStatusInfo struct {
	UserID           string    `json:"user_id"`
	CurrentRoomID    *string   `json:"current_room_id,omitempty"`
	CurrentSessionID *string   `json:"current_session_id,omitempty"`
	Status           string    `json:"status"`
	LastActivity     time.Time `json:"last_activity"`
	IsOnline         bool      `json:"is_online"`
}
