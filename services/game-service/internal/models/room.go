package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// RoomStatus represents the current status of a room
type RoomStatus string

const (
	RoomStatusWaiting  RoomStatus = "waiting"
	RoomStatusActive   RoomStatus = "active"
	RoomStatusFull     RoomStatus = "full"
	RoomStatusClosed   RoomStatus = "closed"
	RoomStatusArchived RoomStatus = "archived"
)

// Room represents a game room where players can join and play
type Room struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Name           string             `bson:"name" json:"name"`
	GameType       GameType           `bson:"game_type" json:"game_type"`
	Status         RoomStatus         `bson:"status" json:"status"`
	CurrentPlayers int                `bson:"current_players" json:"current_players"`
	MaxPlayers     int                `bson:"max_players" json:"max_players"`
	MinPlayers     int                `bson:"min_players" json:"min_players"`
	BetAmount      int64              `bson:"bet_amount" json:"bet_amount"`
	Players        []RoomPlayer       `bson:"players" json:"players"`
	Configuration  RoomConfiguration  `bson:"configuration" json:"configuration"`
	CurrentSession *string            `bson:"current_session,omitempty" json:"current_session,omitempty"`
	LastActivity   time.Time          `bson:"last_activity" json:"last_activity"`
	CreatedBy      string             `bson:"created_by" json:"created_by"`
	CreatedAt      time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt      time.Time          `bson:"updated_at" json:"updated_at"`
}

// RoomPlayer represents a player in a room
type RoomPlayer struct {
	UserID    string       `bson:"user_id" json:"user_id"`
	Username  string       `bson:"username" json:"username"`
	Position  int          `bson:"position" json:"position"`
	IsReady   bool         `bson:"is_ready" json:"is_ready"`
	BetAmount int64        `bson:"bet_amount" json:"bet_amount"`
	JoinedAt  time.Time    `bson:"joined_at" json:"joined_at"`
	Status    PlayerStatus `bson:"status" json:"status"`
}

// RoomConfiguration holds room-specific settings
type RoomConfiguration struct {
	GameType     GameType               `bson:"game_type" json:"game_type"`
	BetLimits    BetLimitConfig         `bson:"bet_limits" json:"bet_limits"`
	Timeouts     TimeoutConfig          `bson:"timeouts" json:"timeouts"`
	AutoStart    bool                   `bson:"auto_start" json:"auto_start"`
	IsPrivate    bool                   `bson:"is_private" json:"is_private"`
	Password     string                 `bson:"password,omitempty" json:"password,omitempty"`
	GameSpecific map[string]interface{} `bson:"game_specific" json:"game_specific"`
}

// RoomEvent represents events that occur in a room
type RoomEvent struct {
	Type      RoomEventType          `json:"type"`
	RoomID    string                 `json:"room_id"`
	UserID    string                 `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// RoomEventType represents the type of room event
type RoomEventType string

const (
	RoomEventPlayerJoined    RoomEventType = "player_joined"
	RoomEventPlayerLeft      RoomEventType = "player_left"
	RoomEventPlayerReady     RoomEventType = "player_ready"
	RoomEventPlayerNotReady  RoomEventType = "player_not_ready"
	RoomEventGameStarting    RoomEventType = "game_starting"
	RoomEventGameStarted     RoomEventType = "game_started"
	RoomEventGameFinished    RoomEventType = "game_finished"
	RoomEventRoomClosed      RoomEventType = "room_closed"
	RoomEventStatusChanged   RoomEventType = "status_changed"
	RoomEventCountdownUpdate RoomEventType = "countdown_update"
)

// CreateRoomRequest represents a request to create a new room
type CreateRoomRequest struct {
	Name         string                 `json:"name" validate:"required,min=3,max=50"`
	GameType     GameType               `json:"game_type" validate:"required"`
	MaxPlayers   int                    `json:"max_players" validate:"required,min=2,max=8"`
	MinPlayers   int                    `json:"min_players" validate:"required,min=2"`
	BetLimits    BetLimitConfig         `json:"bet_limits"`
	AutoStart    bool                   `json:"auto_start"`
	IsPrivate    bool                   `json:"is_private"`
	Password     string                 `json:"password,omitempty"`
	GameSpecific map[string]interface{} `json:"game_specific,omitempty"`
}

// JoinRoomRequest represents a request to join a room
type JoinRoomRequest struct {
	RoomID    string `json:"room_id" validate:"required"`
	UserID    string `json:"user_id" validate:"required"`
	Password  string `json:"password,omitempty"`
	BetAmount int64  `json:"bet_amount" validate:"required,min=1"`
}

// LeaveRoomRequest represents a request to leave a room
type LeaveRoomRequest struct {
	RoomID string `json:"room_id" validate:"required"`
	UserID string `json:"user_id" validate:"required"`
}

// UpdateRoomRequest represents a request to update room settings
type UpdateRoomRequest struct {
	RoomID       string                 `json:"room_id" validate:"required"`
	Name         *string                `json:"name,omitempty"`
	MaxPlayers   *int                   `json:"max_players,omitempty"`
	BetLimits    *BetLimitConfig        `json:"bet_limits,omitempty"`
	AutoStart    *bool                  `json:"auto_start,omitempty"`
	GameSpecific map[string]interface{} `json:"game_specific,omitempty"`
}

// PlayerReadyRequest represents a request to mark a player as ready
type PlayerReadyRequest struct {
	RoomID string `json:"room_id" validate:"required"`
	UserID string `json:"user_id" validate:"required"`
	Ready  bool   `json:"ready"`
}

// RoomListFilter represents filters for listing rooms
type RoomListFilter struct {
	GameType   *GameType   `json:"game_type,omitempty"`
	Status     *RoomStatus `json:"status,omitempty"`
	MinPlayers *int        `json:"min_players,omitempty"`
	MaxPlayers *int        `json:"max_players,omitempty"`
	HasSpace   bool        `json:"has_space"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
}

// RoomListResponse represents the response for listing rooms
type RoomListResponse struct {
	Rooms      []Room     `json:"rooms"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination information
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PerPage     int   `json:"per_page"`
	TotalCount  int64 `json:"total_count"`
	TotalPages  int   `json:"total_pages"`
}

// RoomStats represents statistics for a room
type RoomStats struct {
	RoomID          string        `json:"room_id"`
	TotalGames      int           `json:"total_games"`
	TotalPlayers    int           `json:"total_players"`
	AverageGameTime time.Duration `json:"average_game_time"`
	TotalBetVolume  int64         `json:"total_bet_volume"`
	LastGameAt      *time.Time    `json:"last_game_at,omitempty"`
}

// RoomActivity represents recent activity in a room
type RoomActivity struct {
	RoomID    string       `json:"room_id"`
	Events    []RoomEvent  `json:"events"`
	Players   []RoomPlayer `json:"players"`
	GameState *GameState   `json:"game_state,omitempty"`
	UpdatedAt time.Time    `json:"updated_at"`
}

// Validation methods

// Validate validates the CreateRoomRequest
func (r *CreateRoomRequest) Validate() error {
	if r.MinPlayers > r.MaxPlayers {
		return ErrInvalidPlayerLimits
	}
	if r.BetLimits.MinBet > r.BetLimits.MaxBet {
		return ErrInvalidBetLimits
	}
	return nil
}

// Validate validates the JoinRoomRequest
func (r *JoinRoomRequest) Validate() error {
	if r.BetAmount <= 0 {
		return ErrInvalidBetAmount
	}
	return nil
}

// Helper methods

// CanJoin checks if a room can accept new players
func (r *Room) CanJoin() bool {
	return r.Status == RoomStatusWaiting && r.CurrentPlayers < r.MaxPlayers
}

// IsPlayerInRoom checks if a user is already in the room
func (r *Room) IsPlayerInRoom(userID string) bool {
	for _, player := range r.Players {
		if player.UserID == userID {
			return true
		}
	}
	return false
}

// GetPlayer returns a player by user ID
func (r *Room) GetPlayer(userID string) *RoomPlayer {
	for i, player := range r.Players {
		if player.UserID == userID {
			return &r.Players[i]
		}
	}
	return nil
}

// AllPlayersReady checks if all players in the room are ready
func (r *Room) AllPlayersReady() bool {
	if r.CurrentPlayers < r.MinPlayers {
		return false
	}
	for _, player := range r.Players {
		if !player.IsReady {
			return false
		}
	}
	return true
}

// GetReadyPlayerCount returns the number of ready players
func (r *Room) GetReadyPlayerCount() int {
	count := 0
	for _, player := range r.Players {
		if player.IsReady {
			count++
		}
	}
	return count
}

// GetReadyPlayers returns a slice of ready players
func (r *Room) GetReadyPlayers() []RoomPlayer {
	var readyPlayers []RoomPlayer
	for _, player := range r.Players {
		if player.IsReady {
			readyPlayers = append(readyPlayers, player)
		}
	}
	return readyPlayers
}

// HasMinimumReadyPlayers checks if there are enough ready players to start
func (r *Room) HasMinimumReadyPlayers() bool {
	return r.GetReadyPlayerCount() >= r.MinPlayers
}

// CanStartGame checks if the room can start a game
func (r *Room) CanStartGame() bool {
	return r.Status == RoomStatusWaiting &&
		r.CurrentPlayers >= r.MinPlayers &&
		r.AllPlayersReady() &&
		r.CurrentSession == nil
}
