package models

import (
	"errors"
	"fmt"
)

// Common errors
var (
	// Validation errors
	ErrInvalidInput         = errors.New("invalid input")
	ErrInvalidPlayerLimits  = errors.New("min players cannot be greater than max players")
	ErrInvalidBetLimits     = errors.New("min bet cannot be greater than max bet")
	ErrInvalidBetAmount     = errors.New("invalid bet amount")
	ErrInvalidGameType      = errors.New("invalid game type")
	ErrInvalidRoomStatus    = errors.New("invalid room status")
	ErrInvalidSessionStatus = errors.New("invalid session status")

	// Room errors
	ErrRoomNotFound         = errors.New("room not found")
	ErrRoomFull             = errors.New("room is full")
	ErrRoomClosed           = errors.New("room is closed")
	ErrRoomAlreadyActive    = errors.New("room already has an active game")
	ErrPlayerAlreadyInRoom  = errors.New("player already in room")
	ErrPlayerNotInRoom      = errors.New("player not in room")
	ErrInsufficientPlayers  = errors.New("insufficient players to start game")
	ErrPlayersNotReady      = errors.New("not all players are ready")
	ErrRoomPasswordRequired = errors.New("room password required")
	ErrInvalidRoomPassword  = errors.New("invalid room password")

	// Game session errors
	ErrSessionNotFound           = errors.New("game session not found")
	ErrSessionAlreadyActive      = errors.New("game session already active")
	ErrSessionNotActive          = errors.New("game session not active")
	ErrSessionCompleted          = errors.New("game session already completed")
	ErrSessionCancelled          = errors.New("game session cancelled")
	ErrInvalidStateTransition    = errors.New("invalid state transition")
	ErrConcurrentModification    = errors.New("concurrent modification detected")
	ErrPlayerAlreadyInSession    = errors.New("player already in another session")

	// Game logic errors
	ErrGameNotStarted           = errors.New("game not started")
	ErrGameAlreadyFinished      = errors.New("game already finished")
	ErrInvalidGameConfiguration = errors.New("invalid game configuration")
	ErrInvalidGameData          = errors.New("invalid game data")
	ErrGameTimeout              = errors.New("game timeout")
	ErrInvalidRandomSeed        = errors.New("invalid random seed")
	ErrFairnessVerificationFailed = errors.New("fairness verification failed")

	// Player errors
	ErrPlayerNotFound       = errors.New("player not found")
	ErrInsufficientBalance  = errors.New("insufficient balance")
	ErrPlayerNotAuthorized  = errors.New("player not authorized")
	ErrPlayerAlreadyReady   = errors.New("player already ready")
	ErrPlayerNotReady       = errors.New("player not ready")

	// Authentication errors
	ErrUnauthorized     = errors.New("unauthorized")
	ErrInvalidToken     = errors.New("invalid token")
	ErrTokenExpired     = errors.New("token expired")
	ErrInvalidSignature = errors.New("invalid signature")

	// Database errors
	ErrDatabaseConnection = errors.New("database connection error")
	ErrDatabaseTimeout    = errors.New("database timeout")
	ErrDuplicateKey       = errors.New("duplicate key error")
	ErrDocumentNotFound   = errors.New("document not found")

	// Redis errors
	ErrRedisConnection = errors.New("redis connection error")
	ErrRedisTimeout    = errors.New("redis timeout")
	ErrCacheNotFound   = errors.New("cache entry not found")

	// Configuration errors
	ErrConfigNotFound    = errors.New("configuration not found")
	ErrInvalidConfig     = errors.New("invalid configuration")
	ErrConfigValidation  = errors.New("configuration validation failed")

	// Rate limiting errors
	ErrRateLimitExceeded = errors.New("rate limit exceeded")
	ErrTooManyRequests   = errors.New("too many requests")

	// Service errors
	ErrServiceUnavailable = errors.New("service unavailable")
	ErrInternalError      = errors.New("internal server error")
	ErrTimeout            = errors.New("operation timeout")
)

// GameError represents a game-specific error with additional context
type GameError struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
	Cause   error                  `json:"-"`
}

// Error implements the error interface
func (e *GameError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Cause)
	}
	return e.Message
}

// Unwrap returns the underlying error
func (e *GameError) Unwrap() error {
	return e.Cause
}

// NewGameError creates a new GameError
func NewGameError(code, message string, cause error) *GameError {
	return &GameError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// NewGameErrorWithDetails creates a new GameError with additional details
func NewGameErrorWithDetails(code, message string, details map[string]interface{}, cause error) *GameError {
	return &GameError{
		Code:    code,
		Message: message,
		Details: details,
		Cause:   cause,
	}
}

// Error codes for different types of errors
const (
	// Validation error codes
	ErrorCodeInvalidInput    = "INVALID_INPUT"
	ErrorCodeValidationFailed = "VALIDATION_FAILED"

	// Room error codes
	ErrorCodeRoomNotFound         = "ROOM_NOT_FOUND"
	ErrorCodeRoomFull             = "ROOM_FULL"
	ErrorCodeRoomClosed           = "ROOM_CLOSED"
	ErrorCodeRoomAlreadyActive    = "ROOM_ALREADY_ACTIVE"
	ErrorCodeInvalidRoomPassword  = "INVALID_ROOM_PASSWORD"
	ErrorCodeInvalidRoomStatus    = "INVALID_ROOM_STATUS"
	ErrorCodePlayerInRoom         = "PLAYER_ALREADY_IN_ROOM"
	ErrorCodePlayerNotInRoom      = "PLAYER_NOT_IN_ROOM"

	// Game error codes
	ErrorCodeGameNotFound     = "GAME_NOT_FOUND"
	ErrorCodeGameNotStarted   = "GAME_NOT_STARTED"
	ErrorCodeGameFinished     = "GAME_ALREADY_FINISHED"
	ErrorCodeInvalidGameState = "INVALID_GAME_STATE"
	ErrorCodeGameTimeout      = "GAME_TIMEOUT"

	// Player error codes
	ErrorCodePlayerNotFound      = "PLAYER_NOT_FOUND"
	ErrorCodeInsufficientBalance = "INSUFFICIENT_BALANCE"
	ErrorCodePlayerNotAuthorized = "PLAYER_NOT_AUTHORIZED"

	// Authentication error codes
	ErrorCodeUnauthorized   = "UNAUTHORIZED"
	ErrorCodeInvalidToken   = "INVALID_TOKEN"
	ErrorCodeTokenExpired   = "TOKEN_EXPIRED"

	// System error codes
	ErrorCodeDatabaseError    = "DATABASE_ERROR"
	ErrorCodeRedisError       = "REDIS_ERROR"
	ErrorCodeInternalError    = "INTERNAL_ERROR"
	ErrorCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	ErrorCodeRateLimitExceeded  = "RATE_LIMIT_EXCEEDED"
)

// Common error constructors

// NewValidationError creates a validation error
func NewValidationError(message string, details map[string]interface{}) *GameError {
	return NewGameErrorWithDetails(ErrorCodeValidationFailed, message, details, nil)
}

// NewRoomError creates a room-related error
func NewRoomError(code, message string, roomID string) *GameError {
	return NewGameErrorWithDetails(code, message, map[string]interface{}{
		"room_id": roomID,
	}, nil)
}

// NewGameSessionError creates a game session error
func NewGameSessionError(code, message string, sessionID string) *GameError {
	return NewGameErrorWithDetails(code, message, map[string]interface{}{
		"session_id": sessionID,
	}, nil)
}

// NewPlayerError creates a player-related error
func NewPlayerError(code, message string, userID string) *GameError {
	return NewGameErrorWithDetails(code, message, map[string]interface{}{
		"user_id": userID,
	}, nil)
}

// NewDatabaseError creates a database error
func NewDatabaseError(message string, cause error) *GameError {
	return NewGameError(ErrorCodeDatabaseError, message, cause)
}

// NewRedisError creates a Redis error
func NewRedisError(message string, cause error) *GameError {
	return NewGameError(ErrorCodeRedisError, message, cause)
}

// NewInternalError creates an internal server error
func NewInternalError(message string, cause error) *GameError {
	return NewGameError(ErrorCodeInternalError, message, cause)
}

// IsGameError checks if an error is a GameError
func IsGameError(err error) bool {
	_, ok := err.(*GameError)
	return ok
}

// GetGameError extracts a GameError from an error, or creates one if it's not a GameError
func GetGameError(err error) *GameError {
	if gameErr, ok := err.(*GameError); ok {
		return gameErr
	}
	return NewInternalError("unexpected error", err)
}

// Error response structure for API responses
type ErrorResponse struct {
	Error   string                 `json:"error"`
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// ToErrorResponse converts a GameError to an ErrorResponse
func (e *GameError) ToErrorResponse() *ErrorResponse {
	return &ErrorResponse{
		Error:   "game_error",
		Code:    e.Code,
		Message: e.Message,
		Details: e.Details,
	}
}
