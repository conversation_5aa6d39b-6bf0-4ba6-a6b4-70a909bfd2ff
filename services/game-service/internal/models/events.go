package models

import "time"

// Event request models for notification service

// GameEventRequest represents a game event request
type GameEventRequest struct {
	SessionID string                 `json:"session_id"`
	RoomID    string                 `json:"room_id,omitempty"`
	EventType string                 `json:"event_type"`
	GameType  string                 `json:"game_type,omitempty"`
	UserID    string                 `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// RoomEventRequest represents a room event request
type RoomEventRequest struct {
	RoomID    string                 `json:"room_id"`
	EventType string                 `json:"event_type"`
	EventData map[string]interface{} `json:"event_data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// UserNotificationRequest represents a user notification request
type UserNotificationRequest struct {
	UserIDs          []string               `json:"user_ids"`
	NotificationType string                 `json:"notification_type"`
	Data             map[string]interface{} `json:"data,omitempty"`
	Timestamp        time.Time              `json:"timestamp"`
}

// SystemEventRequest represents a system event request
type SystemEventRequest struct {
	EventType string                 `json:"event_type"`
	Severity  string                 `json:"severity"`
	EventData map[string]interface{} `json:"event_data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// Subscription models

// EnhancedSubscribeRequest represents a subscription request
type EnhancedSubscribeRequest struct {
	UserID      string                 `json:"user_id"`
	ChannelType string                 `json:"channel_type"`
	Filters     map[string]interface{} `json:"filters,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// EnhancedEventSubscription represents an event subscription
type EnhancedEventSubscription struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"user_id"`
	ChannelType string                 `json:"channel_type"`
	Filters     map[string]interface{} `json:"filters,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// UnsubscribeRequest represents an unsubscribe request
type UnsubscribeRequest struct {
	UserID         string    `json:"user_id"`
	SubscriptionID string    `json:"subscription_id"`
	Timestamp      time.Time `json:"timestamp"`
}

// Connection models

// RegisterConnectionRequest represents a connection registration request
type RegisterConnectionRequest struct {
	UserID         string                 `json:"user_id"`
	ConnectionID   string                 `json:"connection_id"`
	ConnectionType string                 `json:"connection_type"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	Timestamp      time.Time              `json:"timestamp"`
}

// UserConnection represents a user connection
type UserConnection struct {
	UserID         string                 `json:"user_id"`
	ConnectionID   string                 `json:"connection_id"`
	ConnectionType string                 `json:"connection_type"`
	Status         string                 `json:"status"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	ConnectedAt    time.Time              `json:"connected_at"`
	LastPing       time.Time              `json:"last_ping"`
}

// Note: FairnessProof and GameResults are defined in game.go to avoid duplication

// Object ID helper for compatibility

// NewObjectID creates a new object ID (mock implementation)
func NewObjectID() interface{} {
	// This would typically return a proper ObjectID from MongoDB driver
	// For now, return a string representation
	return "mock-object-id"
}

// Note: Player status constants are defined in game.go to avoid duplication
