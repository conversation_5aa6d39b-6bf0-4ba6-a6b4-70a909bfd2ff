package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// GameEngineClient handles communication with the game-engine-service
type GameEngineClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewGameEngineClient creates a new game engine client
func NewGameEngineClient(baseURL string, logger *logrus.Logger) *GameEngineClient {
	return &GameEngineClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// ColorSelectionRequest represents a color selection request
type ColorSelectionRequest struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Color     string `json:"color"`
	Timestamp string `json:"timestamp"`
}

// ColorSelectionResponse represents a color selection response
type ColorSelectionResponse struct {
	Success bool                   `json:"success"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// ColorStateResponse represents color state data
type ColorStateResponse struct {
	Success bool                   `json:"success"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// SelectColor sends a color selection request to game-engine-service
func (c *GameEngineClient) SelectColor(ctx context.Context, request *ColorSelectionRequest) (*ColorSelectionResponse, error) {
	c.logger.WithFields(logrus.Fields{
		"roomId": request.RoomID,
		"userId": request.UserID,
		"color":  request.Color,
	}).Debug("Sending color selection to game-engine-service")

	// Prepare request body
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/api/v1/color/select", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var response ColorSelectionResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return &response, fmt.Errorf("game-engine-service error: %s", response.Error)
	}

	c.logger.WithFields(logrus.Fields{
		"roomId": request.RoomID,
		"userId": request.UserID,
		"color":  request.Color,
	}).Info("Color selection processed by game-engine-service")

	return &response, nil
}

// GetColorState retrieves the current color state for a room
func (c *GameEngineClient) GetColorState(ctx context.Context, roomID string) (*ColorStateResponse, error) {
	c.logger.WithField("roomId", roomID).Debug("Getting color state from game-engine-service")

	// Create HTTP request
	url := fmt.Sprintf("%s/api/v1/color/state/%s", c.baseURL, roomID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var response ColorStateResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return &response, fmt.Errorf("game-engine-service error: %s", response.Error)
	}

	return &response, nil
}

// GetAvailableColors retrieves available colors for a room
func (c *GameEngineClient) GetAvailableColors(ctx context.Context, roomID string) (*ColorStateResponse, error) {
	c.logger.WithField("roomId", roomID).Debug("Getting available colors from game-engine-service")

	// Create HTTP request
	url := fmt.Sprintf("%s/api/v1/color/available/%s", c.baseURL, roomID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var response ColorStateResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return &response, fmt.Errorf("game-engine-service error: %s", response.Error)
	}

	return &response, nil
}

// ValidateColorSelection validates if a color can be selected
func (c *GameEngineClient) ValidateColorSelection(ctx context.Context, roomID, userID, color string) error {
	c.logger.WithFields(logrus.Fields{
		"roomId": roomID,
		"userId": userID,
		"color":  color,
	}).Debug("Validating color selection with game-engine-service")

	// Create HTTP request
	url := fmt.Sprintf("%s/api/v1/color/validate", c.baseURL)
	requestBody := map[string]string{
		"roomId": roomID,
		"userId": userID,
		"color":  color,
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp map[string]interface{}
		json.NewDecoder(resp.Body).Decode(&errorResp)
		if errorMsg, ok := errorResp["error"].(string); ok {
			return fmt.Errorf("validation failed: %s", errorMsg)
		}
		return fmt.Errorf("validation failed with status: %d", resp.StatusCode)
	}

	return nil
}

// HealthCheck checks if the game-engine-service is healthy
func (c *GameEngineClient) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("game-engine-service unhealthy: status %d", resp.StatusCode)
	}

	return nil
}
