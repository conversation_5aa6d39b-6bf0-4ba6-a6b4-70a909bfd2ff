package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/redis"
)

// Note: LobbyService interface is now defined in interfaces.go

// lobbyService implements the LobbyService interface
type lobbyService struct {
	redisClient    *redis.RedisClient
	roomRepository RoomRepository
	config         *config.Config
	logger         *logrus.Logger
}

// RoomRepository interface for room data access
type RoomRepository interface {
	ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error)
	GetActiveRoomsOptimized(ctx context.Context, gameType *models.GameType, limit int) ([]*models.Room, error)
	GetRoom(ctx context.Context, roomID string) (*models.Room, error)
}

// NewLobbyService creates a new lobby service instance
func NewLobbyService(
	redisClient *redis.RedisClient,
	roomRepository RoomRepository,
	config *config.Config,
) LobbyService {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &lobbyService{
		redisClient:    redisClient,
		roomRepository: roomRepository,
		config:         config,
		logger:         logger,
	}
}

// LobbyUpdateMessage represents a lobby update message
type LobbyUpdateMessage struct {
	Action string         `json:"action"`
	Room   *LobbyRoomInfo `json:"room"`
}

// Note: LobbyRoomInfo is now defined in interfaces.go

// PublishRoomCreated publishes a room created event to the lobby
func (s *lobbyService) PublishRoomCreated(ctx context.Context, room *models.Room) error {
	s.logger.WithField("room_id", room.ID.Hex()).Info("Publishing room created event to lobby")

	// Create standardized lobby update
	update := LobbyUpdate{
		Type:      "room_created",
		Action:    "room_created",
		Room:      s.convertRoomToLobbyInfo(room),
		Timestamp: time.Now(),
	}

	return s.PublishLobbyUpdate(ctx, update)
}

// PublishRoomUpdated publishes a room updated event to the lobby
func (s *lobbyService) PublishRoomUpdated(ctx context.Context, room *models.Room) error {
	s.logger.WithField("room_id", room.ID.Hex()).Info("Publishing room updated event to lobby")

	// Create standardized lobby update
	update := LobbyUpdate{
		Type:      "room_updated",
		Action:    "room_updated",
		Room:      s.convertRoomToLobbyInfo(room),
		Timestamp: time.Now(),
	}

	return s.PublishLobbyUpdate(ctx, update)
}

// PublishRoomDeleted publishes a room deleted event to the lobby
func (s *lobbyService) PublishRoomDeleted(ctx context.Context, roomID string, room *models.Room) error {
	s.logger.WithField("room_id", roomID).Info("Publishing room deleted event to lobby")

	// Create standardized lobby update
	update := LobbyUpdate{
		Type:      "room_deleted",
		Action:    "room_deleted",
		Room:      s.convertRoomToLobbyInfo(room),
		Timestamp: time.Now(),
	}

	return s.PublishLobbyUpdate(ctx, update)
}

// PublishPlayerCountChanged publishes a player count changed event to the lobby
func (s *lobbyService) PublishPlayerCountChanged(ctx context.Context, room *models.Room) error {
	actualPlayerCount := len(room.Players) // Use actual player count from array
	s.logger.WithFields(logrus.Fields{
		"room_id":      room.ID.Hex(),
		"player_count": actualPlayerCount, // FIXED: Use actual player count instead of room.CurrentPlayers
	}).Info("Publishing player count changed event to lobby")

	// Create standardized lobby update
	update := LobbyUpdate{
		Type:      "player_count_changed",
		Action:    "player_count_changed",
		Room:      s.convertRoomToLobbyInfo(room),
		Timestamp: time.Now(),
	}

	return s.PublishLobbyUpdate(ctx, update)
}

// Helper methods

// convertRoomToLobbyInfo converts a room model to lobby room info
func (s *lobbyService) convertRoomToLobbyInfo(room *models.Room) *LobbyRoomInfo {
	if room == nil {
		return nil
	}

	// Calculate bet amount (use minimum bet from configuration)
	betAmount := int64(0)
	if room.Configuration.BetLimits.MinBet > 0 {
		betAmount = room.Configuration.BetLimits.MinBet
	}

	actualPlayerCount := len(room.Players) // Use actual player count from array
	hasSpace := actualPlayerCount < room.MaxPlayers

	return &LobbyRoomInfo{
		ID:           room.ID.Hex(),
		Name:         room.Name,
		GameType:     string(room.GameType),
		Status:       string(room.Status),
		PlayerCount:  actualPlayerCount, // FIXED: Use actual player count instead of room.CurrentPlayers
		MaxPlayers:   room.MaxPlayers,
		MinPlayers:   room.MinPlayers,
		BetAmount:    betAmount,
		IsPrivate:    room.Configuration.IsPrivate,
		CreatedAt:    room.CreatedAt,
		UpdatedAt:    room.UpdatedAt,
		LastActivity: room.LastActivity,
		HasSpace:     hasSpace,
		IsFeatured:   false, // TODO: Implement featured room logic
	}
}

// publishLobbyUpdate publishes a lobby update message to Redis
func (s *lobbyService) publishLobbyUpdate(ctx context.Context, message LobbyUpdateMessage) error {
	// Create the Redis message envelope
	redisMessage := struct {
		Event struct {
			Type      string             `json:"type"`
			Payload   LobbyUpdateMessage `json:"payload"`
			Timestamp time.Time          `json:"timestamp"`
		} `json:"event"`
		Metadata struct {
			ServiceID     string `json:"serviceId"`
			Version       string `json:"version"`
			CorrelationID string `json:"correlationId"`
			Priority      int    `json:"priority"`
		} `json:"metadata"`
	}{
		Event: struct {
			Type      string             `json:"type"`
			Payload   LobbyUpdateMessage `json:"payload"`
			Timestamp time.Time          `json:"timestamp"`
		}{
			Type:      "room_list_updated",
			Payload:   message,
			Timestamp: time.Now(),
		},
		Metadata: struct {
			ServiceID     string `json:"serviceId"`
			Version       string `json:"version"`
			CorrelationID string `json:"correlationId"`
			Priority      int    `json:"priority"`
		}{
			ServiceID:     "game-service",
			Version:       "1.0.0",
			CorrelationID: fmt.Sprintf("lobby-%d", time.Now().UnixNano()),
			Priority:      1,
		},
	}

	// Publish to the lobby updates channel
	channel := "game:lobby:updates"
	err := s.redisClient.Publish(ctx, channel, redisMessage)
	if err != nil {
		logFields := logrus.Fields{
			"channel": channel,
			"action":  message.Action,
		}
		if message.Room != nil {
			logFields["room_id"] = message.Room.ID
		}
		s.logger.WithError(err).WithFields(logFields).Error("Failed to publish lobby update")
		return err
	}

	logFields := logrus.Fields{
		"channel": channel,
		"action":  message.Action,
	}
	if message.Room != nil {
		logFields["room_id"] = message.Room.ID
	}
	s.logger.WithFields(logFields).Debug("Lobby update published successfully")

	return nil
}

// ===== NEW ENHANCED LOBBY SERVICE METHODS =====

// GetActiveRooms retrieves active rooms with filtering and pagination
func (s *lobbyService) GetActiveRooms(ctx context.Context, filter LobbyRoomFilter) (*LobbyRoomListResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"game_type": filter.GameType,
		"has_space": filter.HasSpace,
		"page":      filter.Page,
		"limit":     filter.Limit,
	}).Info("Getting active rooms for lobby")

	// Convert lobby filter to room filter
	roomFilter := models.RoomListFilter{
		GameType: filter.GameType,
		Status:   filter.Status,
		HasSpace: filter.HasSpace,
		Page:     filter.Page,
		Limit:    filter.Limit,
	}

	// Set default values
	if roomFilter.Limit <= 0 {
		roomFilter.Limit = 20
	}
	if roomFilter.Page < 0 {
		roomFilter.Page = 0
	}

	// Get rooms from repository
	rooms, totalCount, err := s.roomRepository.ListRooms(ctx, roomFilter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get active rooms")
		return nil, err
	}

	// Convert to lobby room info
	lobbyRooms := make([]*LobbyRoomInfo, len(rooms))
	for i, room := range rooms {
		lobbyRooms[i] = s.convertRoomToLobbyInfo(room)
	}

	// Calculate pagination info
	hasMore := int64(filter.Page*filter.Limit+len(lobbyRooms)) < totalCount

	return &LobbyRoomListResponse{
		Rooms:      lobbyRooms,
		TotalCount: totalCount,
		Page:       filter.Page,
		Limit:      filter.Limit,
		HasMore:    hasMore,
	}, nil
}

// GetRoomsByGameType retrieves rooms filtered by game type
func (s *lobbyService) GetRoomsByGameType(ctx context.Context, gameType models.GameType, limit int) ([]*LobbyRoomInfo, error) {
	s.logger.WithFields(logrus.Fields{
		"game_type": gameType,
		"limit":     limit,
	}).Info("Getting rooms by game type")

	if limit <= 0 {
		limit = 10
	}

	rooms, err := s.roomRepository.GetActiveRoomsOptimized(ctx, &gameType, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get rooms by game type")
		return nil, err
	}

	// Convert to lobby room info
	lobbyRooms := make([]*LobbyRoomInfo, len(rooms))
	for i, room := range rooms {
		lobbyRooms[i] = s.convertRoomToLobbyInfo(room)
	}

	return lobbyRooms, nil
}

// GetFeaturedRooms retrieves featured rooms for lobby display
func (s *lobbyService) GetFeaturedRooms(ctx context.Context, limit int) ([]*LobbyRoomInfo, error) {
	s.logger.WithField("limit", limit).Info("Getting featured rooms")

	if limit <= 0 {
		limit = 5
	}

	// For now, get active rooms with space and mark them as featured
	// TODO: Implement proper featured room logic based on popularity, etc.
	rooms, err := s.roomRepository.GetActiveRoomsOptimized(ctx, nil, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get featured rooms")
		return nil, err
	}

	// Convert to lobby room info and mark as featured
	lobbyRooms := make([]*LobbyRoomInfo, len(rooms))
	for i, room := range rooms {
		lobbyInfo := s.convertRoomToLobbyInfo(room)
		lobbyInfo.IsFeatured = true
		lobbyRooms[i] = lobbyInfo
	}

	return lobbyRooms, nil
}

// SearchRooms searches for rooms based on query and filter
func (s *lobbyService) SearchRooms(ctx context.Context, query string, filter LobbyRoomFilter) ([]*LobbyRoomInfo, error) {
	s.logger.WithFields(logrus.Fields{
		"query":  query,
		"filter": filter,
	}).Info("Searching rooms")

	// TODO: Implement proper search functionality
	// For now, just return filtered rooms
	response, err := s.GetActiveRooms(ctx, filter)
	if err != nil {
		return nil, err
	}

	return response.Rooms, nil
}

// HandleLobbySubscription handles lobby subscription requests
func (s *lobbyService) HandleLobbySubscription(ctx context.Context, request LobbySubscriptionRequest) (*LobbySubscriptionResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"user_id":   request.UserID,
		"username":  request.Username,
		"socket_id": request.SocketID,
	}).Info("Handling lobby subscription")

	// Get current active rooms
	filter := LobbyRoomFilter{
		HasSpace:  true,
		Page:      0,
		Limit:     50, // Return up to 50 rooms for initial load
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	roomList, err := s.GetActiveRooms(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get rooms for lobby subscription")
		return &LobbySubscriptionResponse{
			Success:   false,
			Error:     "Failed to get room list",
			Timestamp: time.Now().Format(time.RFC3339),
		}, err
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"room_count": len(roomList.Rooms),
	}).Info("Successfully processed lobby subscription")

	return &LobbySubscriptionResponse{
		Success:   true,
		Rooms:     roomList.Rooms,
		Timestamp: time.Now().Format(time.RFC3339),
	}, nil
}

// HandleLobbyUnsubscription handles lobby unsubscription
func (s *lobbyService) HandleLobbyUnsubscription(ctx context.Context, userID string) error {
	s.logger.WithField("user_id", userID).Info("Handling lobby unsubscription")
	// TODO: Implement unsubscription logic if needed
	return nil
}

// PublishLobbyUpdate publishes a lobby update event
func (s *lobbyService) PublishLobbyUpdate(ctx context.Context, update LobbyUpdate) error {
	s.logger.WithFields(logrus.Fields{
		"type":   update.Type,
		"action": update.Action,
	}).Info("Publishing lobby update")

	// Create standardized message format
	message := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      fmt.Sprintf("lobby:%s", update.Type),
			"payload":   update,
			"timestamp": update.Timestamp.Format(time.RFC3339),
			"version":   "1.0.0",
		},
		"metadata": map[string]interface{}{
			"serviceId":     "game-service",
			"version":       "1.0.0",
			"correlationId": fmt.Sprintf("lobby-update-%d", time.Now().UnixNano()),
			"priority":      2, // High priority for lobby updates
		},
	}

	// Use standardized lobby events channel
	return s.redisClient.Publish(ctx, "lobby:events", message)
}

// GetLobbyStats retrieves lobby statistics
func (s *lobbyService) GetLobbyStats(ctx context.Context) (*LobbyStats, error) {
	s.logger.Info("Getting lobby statistics")

	// TODO: Implement proper statistics calculation
	// For now, return basic stats
	filter := LobbyRoomFilter{
		Page:  0,
		Limit: 1000, // Get all rooms for stats
	}

	roomList, err := s.GetActiveRooms(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get rooms for lobby stats")
		return nil, err
	}

	// Calculate basic statistics
	stats := &LobbyStats{
		TotalRooms:       len(roomList.Rooms),
		ActiveRooms:      0,
		WaitingRooms:     0,
		TotalPlayers:     0,
		RoomsByGameType:  make(map[string]int),
		PopularGameTypes: []string{},
		PeakHours:        []int{},
		LastUpdated:      time.Now(),
	}

	for _, room := range roomList.Rooms {
		stats.TotalPlayers += room.PlayerCount
		stats.RoomsByGameType[room.GameType]++

		if room.Status == "ACTIVE" {
			stats.ActiveRooms++
		} else if room.Status == "WAITING" {
			stats.WaitingRooms++
		}
	}

	return stats, nil
}

// GetRoomPopularityStats retrieves room popularity statistics
func (s *lobbyService) GetRoomPopularityStats(ctx context.Context, gameType *models.GameType) ([]*RoomPopularityInfo, error) {
	s.logger.WithField("game_type", gameType).Info("Getting room popularity stats")

	// TODO: Implement proper popularity tracking
	// For now, return empty stats
	return []*RoomPopularityInfo{}, nil
}

// RefreshRoomListCache refreshes the room list cache
func (s *lobbyService) RefreshRoomListCache(ctx context.Context) error {
	s.logger.Info("Refreshing room list cache")
	// TODO: Implement cache refresh logic
	return nil
}

// InvalidateRoomCache invalidates cache for a specific room
func (s *lobbyService) InvalidateRoomCache(ctx context.Context, roomID string) error {
	s.logger.WithField("room_id", roomID).Info("Invalidating room cache")
	// TODO: Implement cache invalidation logic
	return nil
}
