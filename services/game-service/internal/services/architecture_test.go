package services

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

// TestArchitectureCompliance verifies that the refactored architecture meets our requirements
func TestArchitectureCompliance(t *testing.T) {
	t.Run("RequestHandlerService should be under 1000 lines", func(t *testing.T) {
		// This test verifies that our main request handler file is under the 1000 line limit
		// The actual line count verification would be done by static analysis tools
		// For now, we verify the service can be created and basic functionality works

		// Create a minimal service instance to verify structure
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       false,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
		}

		assert.NotNil(t, service)
		assert.NotNil(t, service.pendingRequests)
		assert.False(t, service.isRunning)
	})

	t.Run("Service should follow single responsibility principle", func(t *testing.T) {
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       false,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
		}

		// Test that the service focuses on request handling responsibilities
		correlationID := "test-123"

		// Request tracking functionality
		assert.False(t, service.isDuplicateRequest(correlationID))
		service.markRequestProcessing(correlationID)
		assert.True(t, service.isDuplicateRequest(correlationID))
		service.markRequestComplete(correlationID)
		assert.False(t, service.isDuplicateRequest(correlationID))

		// Statistics functionality
		stats := service.GetStats()
		assert.NotNil(t, stats)
		assert.Contains(t, stats, "isRunning")
		assert.Contains(t, stats, "processedCount")
		assert.Contains(t, stats, "pendingRequests")

		// Health check functionality
		assert.False(t, service.IsHealthy()) // Not running
	})

	t.Run("Service should handle concurrent requests safely", func(t *testing.T) {
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       true,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
		}

		// Test concurrent access to request tracking
		done := make(chan bool, 10)

		for i := 0; i < 10; i++ {
			go func(id int) {
				correlationID := fmt.Sprintf("test-%d", id)
				service.markRequestProcessing(correlationID)
				time.Sleep(10 * time.Millisecond)
				service.markRequestComplete(correlationID)
				done <- true
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < 10; i++ {
			<-done
		}

		// Verify no requests are still pending
		stats := service.GetStats()
		assert.Equal(t, 0, stats["pendingRequests"].(int))
	})

	t.Run("Service should provide proper lifecycle management", func(t *testing.T) {
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       false,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
			logger:          logrus.New(),
		}

		// Test initial state
		assert.False(t, service.IsHealthy())

		// Simulate starting
		service.activityMutex.Lock()
		service.isRunning = true
		service.lastActivity = time.Now()
		service.activityMutex.Unlock()

		assert.True(t, service.IsHealthy())

		// Test stopping
		service.Stop()
		assert.False(t, service.IsHealthy())
	})

	t.Run("Message handling should be robust", func(t *testing.T) {
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       true,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
			logger:          logrus.New(),
		}

		ctx := context.Background()

		// Test invalid JSON handling
		initialCount := service.processedCount
		service.handleMessage(ctx, "invalid json", "test:channel")

		// Should increment processed count even for invalid messages
		assert.Greater(t, service.processedCount, initialCount)

		// Test valid message structure
		validMessage := `{
			"event": {
				"type": "test_request",
				"payload": {},
				"timestamp": "2023-12-06T10:00:00Z"
			},
			"metadata": {
				"serviceId": "test-service",
				"version": "1.0.0",
				"correlationId": "test-123",
				"priority": 1
			}
		}`

		beforeCount := service.processedCount
		service.handleMessage(ctx, validMessage, "game:requests")
		assert.Greater(t, service.processedCount, beforeCount)
	})
}

// TestModularArchitecture verifies that the modular request handlers are properly structured
func TestModularArchitecture(t *testing.T) {
	t.Run("Request types should be properly defined", func(t *testing.T) {
		// Test that RequestMessage structure is well-defined
		msg := RequestMessage{}

		// Verify structure exists
		assert.NotNil(t, msg.Event)
		assert.NotNil(t, msg.Metadata)

		// Test with sample data
		msg.Event.Type = "test_request"
		msg.Event.Payload = map[string]interface{}{"test": "data"}
		msg.Event.Timestamp = time.Now()

		msg.Metadata.ServiceID = "test-service"
		msg.Metadata.CorrelationID = "test-123"
		msg.Metadata.Version = "1.0.0"

		assert.Equal(t, "test_request", msg.Event.Type)
		assert.Equal(t, "test-service", msg.Metadata.ServiceID)
		assert.Equal(t, "test-123", msg.Metadata.CorrelationID)
	})

	t.Run("Service should delegate to appropriate handlers", func(t *testing.T) {
		service := &RequestHandlerService{
			pendingRequests: make(map[string]bool),
			isRunning:       true,
			startTime:       time.Now(),
			processedCount:  0,
			lastActivity:    time.Now(),
			logger:          logrus.New(),
		}

		ctx := context.Background()

		// Create test messages for different request types
		testMessages := []struct {
			requestType string
			channel     string
		}{
			{"request_room_list", "game:requests"},
			{"subscribe_lobby", "game:requests"},
			{"verify_token", "game:requests"},
			{"unknown_type", "game:requests"},
		}

		for _, tm := range testMessages {
			msg := RequestMessage{}
			msg.Event.Type = tm.requestType
			msg.Event.Timestamp = time.Now()
			msg.Metadata.CorrelationID = fmt.Sprintf("test-%s", tm.requestType)
			msg.Metadata.ServiceID = "test-service"

			// This should not panic and should handle the request appropriately
			service.handleGameRequest(ctx, msg)
		}

		// Verify all requests were processed
		stats := service.GetStats()
		assert.Equal(t, 0, stats["pendingRequests"].(int)) // All should be completed
	})
}
