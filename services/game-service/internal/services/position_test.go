package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/xzgame/game-service/internal/models"
)

func TestAtomicRoomService_CalculateNextPosition(t *testing.T) {
	service := &AtomicRoomService{}

	tests := []struct {
		name     string
		players  []models.RoomPlayer
		expected int
	}{
		{
			name:     "empty room should return position 0",
			players:  []models.RoomPlayer{},
			expected: 0,
		},
		{
			name: "single player at position 0 should return position 1",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
			},
			expected: 1,
		},
		{
			name: "two players at positions 0,1 should return position 2",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 1},
			},
			expected: 2,
		},
		{
			name: "players with gap should fill the gap",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 2},
			},
			expected: 1,
		},
		{
			name: "players with multiple gaps should fill first gap",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 2},
				{UserID: "user3", Position: 4},
			},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.calculateNextPosition(tt.players)
			assert.Equal(t, tt.expected, result, "Position calculation failed for test case: %s", tt.name)
		})
	}
}

func TestRoomService_CalculateNextAvailablePosition(t *testing.T) {
	service := &roomService{}

	tests := []struct {
		name     string
		players  []models.RoomPlayer
		expected int
	}{
		{
			name:     "empty room should return position 0",
			players:  []models.RoomPlayer{},
			expected: 0,
		},
		{
			name: "single player at position 0 should return position 1",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
			},
			expected: 1,
		},
		{
			name: "two players at positions 0,1 should return position 2",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 1},
			},
			expected: 2,
		},
		{
			name: "players with gap should fill the gap",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 2},
			},
			expected: 1,
		},
		{
			name: "players with multiple gaps should fill first gap",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 2},
				{UserID: "user3", Position: 4},
			},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.calculateNextAvailablePosition(tt.players)
			assert.Equal(t, tt.expected, result, "Position calculation failed for test case: %s", tt.name)
		})
	}
}

func TestPositionReassignment(t *testing.T) {
	tests := []struct {
		name     string
		players  []models.RoomPlayer
		expected []int
	}{
		{
			name: "sequential positions should remain unchanged",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 0},
				{UserID: "user2", Position: 1},
				{UserID: "user3", Position: 2},
			},
			expected: []int{0, 1, 2},
		},
		{
			name: "non-sequential positions should be reassigned",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 2},
				{UserID: "user2", Position: 3},
				{UserID: "user3", Position: 5},
			},
			expected: []int{0, 1, 2},
		},
		{
			name: "mixed positions should be reassigned sequentially",
			players: []models.RoomPlayer{
				{UserID: "user1", Position: 1},
				{UserID: "user2", Position: 0},
				{UserID: "user3", Position: 4},
			},
			expected: []int{0, 1, 2},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Sort players by position (simulating the reassignment logic)
			for i := 0; i < len(tt.players)-1; i++ {
				for j := i + 1; j < len(tt.players); j++ {
					if tt.players[i].Position > tt.players[j].Position {
						tt.players[i], tt.players[j] = tt.players[j], tt.players[i]
					}
				}
			}

			// Reassign positions starting from 0
			for i := range tt.players {
				tt.players[i].Position = i
			}

			// Verify positions
			for i, player := range tt.players {
				assert.Equal(t, tt.expected[i], player.Position, "Position reassignment failed for player %d in test case: %s", i, tt.name)
			}
		})
	}
}
