package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/redis"
)

// configService implements the ConfigService interface
type configService struct {
	configRepo  repositories.ConfigRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
}

// NewConfigService creates a new config service instance
func NewConfigService(
	configRepo repositories.ConfigRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
) ConfigService {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &configService{
		configRepo:  configRepo,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
	}
}

// GetGameConfiguration retrieves game configuration for a specific game type
func (s *configService) GetGameConfiguration(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error) {
	s.logger.WithField("game_type", gameType).Info("Getting game configuration")

	// Try to get from cache first
	cacheKey := fmt.Sprintf(redis.KeyGameConfig, string(gameType))
	var cachedConfig models.GameConfiguration
	if err := s.redisClient.Get(ctx, cacheKey, &cachedConfig); err == nil {
		return &cachedConfig, nil
	}

	// Get from database if not in cache
	gameConfig, err := s.configRepo.GetGameConfig(ctx, gameType)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get game configuration")
		return nil, err
	}

	// Cache the configuration
	if err := s.redisClient.Set(ctx, cacheKey, gameConfig, s.config.RedisTTL); err != nil {
		s.logger.WithError(err).Warn("Failed to cache game configuration")
	}

	return gameConfig, nil
}

// UpdateGameConfiguration updates game configuration
func (s *configService) UpdateGameConfiguration(ctx context.Context, gameType models.GameType, gameConfig *models.GameConfiguration) error {
	s.logger.WithField("game_type", gameType).Info("Updating game configuration")

	// Validate configuration
	if err := s.ValidateGameConfiguration(ctx, gameConfig); err != nil {
		return err
	}

	// Update in database
	updates := map[string]interface{}{
		"rules":      gameConfig.Rules,
		"payouts":    gameConfig.Payouts,
		"house_edge": gameConfig.HouseEdge,
		"bet_limits": gameConfig.BetLimits,
		"timeouts":   gameConfig.Timeouts,
		"is_active":  gameConfig.IsActive,
	}

	err := s.configRepo.UpdateGameConfig(ctx, gameType, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update game configuration")
		return err
	}

	// Invalidate cache
	cacheKey := fmt.Sprintf(redis.KeyGameConfig, string(gameType))
	s.redisClient.Delete(ctx, cacheKey)

	s.logger.WithField("game_type", gameType).Info("Game configuration updated successfully")
	return nil
}

// GetActiveGameConfiguration retrieves the active configuration for a game type
func (s *configService) GetActiveGameConfiguration(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error) {
	gameConfig, err := s.configRepo.GetActiveGameConfig(ctx, gameType)
	if err != nil {
		return nil, err
	}

	return gameConfig, nil
}

// ValidateGameConfiguration validates a game configuration
func (s *configService) ValidateGameConfiguration(ctx context.Context, gameConfig *models.GameConfiguration) error {
	s.logger.WithField("game_type", gameConfig.GameType).Info("Validating game configuration")

	// Basic validation
	if gameConfig.HouseEdge < 0 || gameConfig.HouseEdge > 1 {
		return models.NewValidationError("house edge must be between 0 and 1", map[string]interface{}{
			"house_edge": gameConfig.HouseEdge,
		})
	}

	if gameConfig.MinPlayers <= 0 || gameConfig.MaxPlayers <= 0 {
		return models.NewValidationError("player limits must be positive", map[string]interface{}{
			"min_players": gameConfig.MinPlayers,
			"max_players": gameConfig.MaxPlayers,
		})
	}

	if gameConfig.MinPlayers > gameConfig.MaxPlayers {
		return models.NewValidationError("min players cannot exceed max players", map[string]interface{}{
			"min_players": gameConfig.MinPlayers,
			"max_players": gameConfig.MaxPlayers,
		})
	}

	if gameConfig.BetLimits.MinBet <= 0 || gameConfig.BetLimits.MaxBet <= 0 {
		return models.NewValidationError("bet limits must be positive", map[string]interface{}{
			"min_bet": gameConfig.BetLimits.MinBet,
			"max_bet": gameConfig.BetLimits.MaxBet,
		})
	}

	if gameConfig.BetLimits.MinBet > gameConfig.BetLimits.MaxBet {
		return models.NewValidationError("min bet cannot exceed max bet", map[string]interface{}{
			"min_bet": gameConfig.BetLimits.MinBet,
			"max_bet": gameConfig.BetLimits.MaxBet,
		})
	}

	// Validate payouts
	totalProbability := 0.0
	for _, payout := range gameConfig.Payouts {
		if payout.Probability < 0 || payout.Probability > 1 {
			return models.NewValidationError("payout probability must be between 0 and 1", map[string]interface{}{
				"condition":   payout.Condition,
				"probability": payout.Probability,
			})
		}
		totalProbability += payout.Probability
	}

	if totalProbability > 1.0 {
		return models.NewValidationError("total payout probability cannot exceed 1", map[string]interface{}{
			"total_probability": totalProbability,
		})
	}

	return nil
}

// GetRuntimeSettings retrieves runtime settings
func (s *configService) GetRuntimeSettings(ctx context.Context) (*RuntimeSettings, error) {
	s.logger.Info("Getting runtime settings")

	// For now, return default settings
	// In a real implementation, this would be stored in database/cache
	settings := &RuntimeSettings{
		MaxConcurrentGames: s.config.MaxConcurrentGames,
		MaintenanceMode:    false,
		GameTypeEnabled: map[string]bool{
			string(models.GameTypePrizeWheel): true,
			string(models.GameTypeAmidakuji):  true,
		},
		GlobalBetLimits: models.BetLimitConfig{
			MinBet:   100,
			MaxBet:   100000,
			Currency: "USD",
		},
		UpdatedAt: time.Now(),
	}

	return settings, nil
}

// UpdateRuntimeSettings updates runtime settings
func (s *configService) UpdateRuntimeSettings(ctx context.Context, settings *RuntimeSettings) error {
	s.logger.Info("Updating runtime settings")

	// In a real implementation, this would update database/cache
	// For now, just log the operation
	s.logger.WithFields(logrus.Fields{
		"max_concurrent_games": settings.MaxConcurrentGames,
		"maintenance_mode":     settings.MaintenanceMode,
		"game_type_enabled":    settings.GameTypeEnabled,
	}).Info("Runtime settings updated")

	return nil
}

// IsGameTypeEnabled checks if a game type is enabled
func (s *configService) IsGameTypeEnabled(ctx context.Context, gameType models.GameType) (bool, error) {
	settings, err := s.GetRuntimeSettings(ctx)
	if err != nil {
		return false, err
	}

	enabled, exists := settings.GameTypeEnabled[string(gameType)]
	if !exists {
		return false, nil
	}

	return enabled, nil
}

// RefreshConfigurationCache refreshes the configuration cache
func (s *configService) RefreshConfigurationCache(ctx context.Context) error {
	s.logger.Info("Refreshing configuration cache")

	// Get all game types and refresh their cache
	gameTypes := []models.GameType{
		models.GameTypePrizeWheel,
		models.GameTypeAmidakuji,
	}

	for _, gameType := range gameTypes {
		// Invalidate existing cache
		cacheKey := fmt.Sprintf(redis.KeyGameConfig, string(gameType))
		s.redisClient.Delete(ctx, cacheKey)

		// Reload from database
		_, err := s.GetGameConfiguration(ctx, gameType)
		if err != nil {
			s.logger.WithError(err).WithField("game_type", gameType).Warn("Failed to refresh configuration for game type")
		}
	}

	s.logger.Info("Configuration cache refreshed successfully")
	return nil
}

// InvalidateConfigurationCache invalidates cache for a specific game type
func (s *configService) InvalidateConfigurationCache(ctx context.Context, gameType models.GameType) error {
	s.logger.WithField("game_type", gameType).Info("Invalidating configuration cache")

	cacheKey := fmt.Sprintf(redis.KeyGameConfig, string(gameType))
	err := s.redisClient.Delete(ctx, cacheKey)
	if err != nil {
		s.logger.WithError(err).Error("Failed to invalidate configuration cache")
		return err
	}

	s.logger.WithField("game_type", gameType).Info("Configuration cache invalidated successfully")
	return nil
}
