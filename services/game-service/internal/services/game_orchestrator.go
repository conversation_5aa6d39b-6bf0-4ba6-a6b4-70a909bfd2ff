package services

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/clients/auth"
	"github.com/xzgame/game-service/pkg/clients/gameengine"
	"github.com/xzgame/game-service/pkg/clients/notification"
	"github.com/xzgame/game-service/pkg/clients/room"
	"github.com/xzgame/game-service/pkg/redis"
)

// GameOrchestrator orchestrates game operations across multiple services
type GameOrchestrator struct {
	gameRepo    repositories.GameRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger

	// Service clients for microservice communication
	authClient         *auth.AuthClient
	roomClient         *room.RoomClient
	gameEngineClient   *gameengine.GameEngineClient
	notificationClient *notification.NotificationClient

	// Internal coordinators
	sessionCoordinator *SessionCoordinator
	eventCoordinator   *EventCoordinator
}

// NewGameOrchestrator creates a new game orchestrator
func NewGameOrchestrator(
	gameRepo repositories.GameRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
) *GameOrchestrator {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Initialize service clients
	authClient := auth.NewAuthClient(config.AuthServiceURL)
	roomClient := room.NewRoomClient(config.RoomServiceURL)
	gameEngineClient := gameengine.NewGameEngineClient(config.GameEngineServiceURL)
	notificationClient := notification.NewNotificationClient(config.NotificationServiceURL)

	orchestrator := &GameOrchestrator{
		gameRepo:           gameRepo,
		redisClient:        redisClient,
		config:             config,
		logger:             logger,
		authClient:         authClient,
		roomClient:         roomClient,
		gameEngineClient:   gameEngineClient,
		notificationClient: notificationClient,
	}

	// Initialize coordinators
	orchestrator.sessionCoordinator = NewSessionCoordinator(orchestrator)
	orchestrator.eventCoordinator = NewEventCoordinator(orchestrator)

	return orchestrator
}

// CreateGameSession orchestrates game session creation across services
func (o *GameOrchestrator) CreateGameSession(ctx context.Context, roomID string, config models.SessionConfiguration) (*models.GameSession, error) {
	o.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"config":  config,
	}).Info("Orchestrating game session creation")

	// Step 1: Validate room exists and is ready for game
	room, err := o.roomClient.GetRoom(ctx, roomID)
	if err != nil {
		o.logger.WithError(err).Error("Failed to get room from room service")
		return nil, fmt.Errorf("failed to get room: %w", err)
	}

	if room.Status != models.RoomStatusWaiting {
		return nil, models.NewGameSessionError(
			models.ErrorCodeInvalidGameState,
			"room is not in waiting state",
			roomID,
		)
	}

	// Step 2: Create session via session coordinator
	session, err := o.sessionCoordinator.CreateSession(ctx, roomID, config)
	if err != nil {
		o.logger.WithError(err).Error("Failed to create session")
		return nil, err
	}

	// Step 3: Notify room service about session creation
	err = o.roomClient.UpdateRoomStatus(ctx, roomID, models.RoomStatusActive)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to update room status")
	}

	// Step 4: Publish session created event
	err = o.eventCoordinator.PublishSessionCreated(ctx, session)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to publish session created event")
	}

	o.logger.WithField("session_id", session.ID.Hex()).Info("Game session orchestrated successfully")
	return session, nil
}

// StartGame orchestrates game start across services
func (o *GameOrchestrator) StartGame(ctx context.Context, sessionID string) error {
	o.logger.WithField("session_id", sessionID).Info("Orchestrating game start")

	// Step 1: Get session and validate state
	session, err := o.sessionCoordinator.GetSession(ctx, sessionID)
	if err != nil {
		return err
	}

	if session.Status != models.SessionStatusWaiting {
		return models.NewGameSessionError(
			models.ErrorCodeInvalidGameState,
			"game session is not in waiting state",
			sessionID,
		)
	}

	// Step 2: Update session status to active
	err = o.sessionCoordinator.UpdateSessionStatus(ctx, sessionID, models.SessionStatusActive)
	if err != nil {
		o.logger.WithError(err).Error("Failed to update session status")
		return err
	}

	// Step 3: Execute game logic via game engine service
	results, err := o.gameEngineClient.ExecuteGame(ctx, sessionID, session.GameType, session.Players)
	if err != nil {
		o.logger.WithError(err).Error("Failed to execute game logic")
		// Mark session as cancelled on game logic failure
		o.sessionCoordinator.UpdateSessionStatus(ctx, sessionID, models.SessionStatusCancelled)
		return err
	}

	// Step 4: End the game with results
	err = o.EndGame(ctx, sessionID, results)
	if err != nil {
		o.logger.WithError(err).Error("Failed to end game")
		return err
	}

	o.logger.WithField("session_id", sessionID).Info("Game orchestrated successfully")
	return nil
}

// EndGame orchestrates game completion across services
func (o *GameOrchestrator) EndGame(ctx context.Context, sessionID string, results *models.GameResults) error {
	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"winner":     results.WinnerUserID,
	}).Info("Orchestrating game end")

	// Step 1: Update session with results
	err := o.sessionCoordinator.CompleteSession(ctx, sessionID, results)
	if err != nil {
		o.logger.WithError(err).Error("Failed to complete session")
		return err
	}

	// Step 2: Get session for room update
	session, err := o.sessionCoordinator.GetSession(ctx, sessionID)
	if err != nil {
		return err
	}

	// Step 3: Update room status back to waiting
	err = o.roomClient.UpdateRoomStatus(ctx, session.RoomID, models.RoomStatusWaiting)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to update room status")
	}

	// Step 4: Publish game completed event
	err = o.eventCoordinator.PublishGameCompleted(ctx, sessionID, results)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to publish game completed event")
	}

	o.logger.WithField("session_id", sessionID).Info("Game end orchestrated successfully")
	return nil
}

// CancelGame orchestrates game cancellation across services
func (o *GameOrchestrator) CancelGame(ctx context.Context, sessionID string, reason string) error {
	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"reason":     reason,
	}).Info("Orchestrating game cancellation")

	// Step 1: Cancel session
	err := o.sessionCoordinator.CancelSession(ctx, sessionID, reason)
	if err != nil {
		o.logger.WithError(err).Error("Failed to cancel session")
		return err
	}

	// Step 2: Get session for room update
	session, err := o.sessionCoordinator.GetSession(ctx, sessionID)
	if err != nil {
		return err
	}

	// Step 3: Update room status back to waiting
	err = o.roomClient.UpdateRoomStatus(ctx, session.RoomID, models.RoomStatusWaiting)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to update room status")
	}

	// Step 4: Publish game cancelled event
	err = o.eventCoordinator.PublishGameCancelled(ctx, sessionID, reason)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to publish game cancelled event")
	}

	o.logger.WithField("session_id", sessionID).Info("Game cancellation orchestrated successfully")
	return nil
}

// GetGameState retrieves current game state
func (o *GameOrchestrator) GetGameState(ctx context.Context, sessionID string) (*models.GameState, error) {
	return o.sessionCoordinator.GetGameState(ctx, sessionID)
}

// UpdateGameState updates game state
func (o *GameOrchestrator) UpdateGameState(ctx context.Context, sessionID string, gameData map[string]interface{}) error {
	return o.sessionCoordinator.UpdateGameState(ctx, sessionID, gameData)
}

// AddPlayerToGame adds a player to a game session
func (o *GameOrchestrator) AddPlayerToGame(ctx context.Context, sessionID string, player models.SessionPlayer) error {
	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    player.UserID,
	}).Info("Orchestrating player addition to game")

	// Step 1: Add player to session
	err := o.sessionCoordinator.AddPlayer(ctx, sessionID, player)
	if err != nil {
		o.logger.WithError(err).Error("Failed to add player to session")
		return err
	}

	// Step 2: Publish player joined event
	err = o.eventCoordinator.PublishPlayerJoined(ctx, sessionID, player.UserID)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to publish player joined event")
	}

	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    player.UserID,
	}).Info("Player addition orchestrated successfully")

	return nil
}

// RemovePlayerFromGame removes a player from a game session
func (o *GameOrchestrator) RemovePlayerFromGame(ctx context.Context, sessionID string, userID string) error {
	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Orchestrating player removal from game")

	// Step 1: Remove player from session
	err := o.sessionCoordinator.RemovePlayer(ctx, sessionID, userID)
	if err != nil {
		o.logger.WithError(err).Error("Failed to remove player from session")
		return err
	}

	// Step 2: Publish player left event
	err = o.eventCoordinator.PublishPlayerLeft(ctx, sessionID, userID)
	if err != nil {
		o.logger.WithError(err).Warn("Failed to publish player left event")
	}

	o.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Player removal orchestrated successfully")

	return nil
}

// GetGameHistory retrieves game history for a player
func (o *GameOrchestrator) GetGameHistory(ctx context.Context, userID string, gameType *models.GameType, page, limit int) ([]*models.GameHistory, error) {
	return o.gameRepo.GetGameHistoryByPlayer(ctx, userID, gameType, limit, page*limit)
}

// GetPlayerGameStats retrieves game statistics for a player
func (o *GameOrchestrator) GetPlayerGameStats(ctx context.Context, userID string) (*PlayerGameStats, error) {
	return o.sessionCoordinator.GetPlayerStats(ctx, userID)
}

// Health check methods

// HealthCheck performs a comprehensive health check
func (o *GameOrchestrator) HealthCheck(ctx context.Context) error {
	// Check service clients connectivity
	// Database health check would be implemented when repository supports it

	// Check Redis connectivity
	if cmd := o.redisClient.Ping(ctx); cmd.Err() != nil {
		return fmt.Errorf("redis health check failed: %w", cmd.Err())
	}

	// Check service clients
	if err := o.authClient.HealthCheck(ctx); err != nil {
		o.logger.WithError(err).Warn("Auth service health check failed")
	}

	if err := o.roomClient.HealthCheck(ctx); err != nil {
		o.logger.WithError(err).Warn("Room service health check failed")
	}

	if err := o.gameEngineClient.HealthCheck(ctx); err != nil {
		o.logger.WithError(err).Warn("Game engine service health check failed")
	}

	if err := o.notificationClient.HealthCheck(ctx); err != nil {
		o.logger.WithError(err).Warn("Notification service health check failed")
	}

	return nil
}

// GetServiceStats returns orchestrator statistics
func (o *GameOrchestrator) GetServiceStats() *GameOrchestratorStats {
	return &GameOrchestratorStats{
		ServiceClients: []string{"auth", "room", "game-engine", "notification"},
		Coordinators:   []string{"session", "event"},
		Version:        "1.0.0",
	}
}

// GameOrchestratorStats represents orchestrator statistics
type GameOrchestratorStats struct {
	ServiceClients []string `json:"serviceClients"`
	Coordinators   []string `json:"coordinators"`
	Version        string   `json:"version"`
}
