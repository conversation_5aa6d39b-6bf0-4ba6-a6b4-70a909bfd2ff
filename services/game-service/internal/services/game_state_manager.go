package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/redis"
)

// GameStateManager manages the game state loop for rooms
type GameStateManager struct {
	logger      *logrus.Logger
	redisClient *redis.RedisClient
	roomService RoomService
	gameService GameService

	// Active game loops
	activeLoops map[string]*GameLoop
	loopsMutex  sync.RWMutex

	// Configuration
	config *GameStateConfig
}

// GameStateConfig holds configuration for game state management
type GameStateConfig struct {
	StartingDuration time.Duration
	PlayingDuration  time.Duration
	EndDuration      time.Duration
}

// GameLoop represents an active game loop for a room
type GameLoop struct {
	RoomID     string
	State      GameState
	Timer      *time.Timer
	Cancel     context.CancelFunc
	LastUpdate time.Time
	Players    []models.RoomPlayer
	mutex      sync.RWMutex
}

// GameState represents the current state of a game
type GameState string

const (
	StateWaiting  GameState = "waiting"
	StateStarting GameState = "starting"
	StatePlaying  GameState = "playing"
	StateEnd      GameState = "end"
)

// NewGameStateManager creates a new game state manager
func NewGameStateManager(logger *logrus.Logger, redisClient *redis.RedisClient, roomService RoomService, gameService GameService, managerClient interface{}) *GameStateManager {
	gsm := &GameStateManager{
		logger:      logger,
		redisClient: redisClient,
		roomService: roomService,
		gameService: gameService,
		activeLoops: make(map[string]*GameLoop),
		config: &GameStateConfig{
			StartingDuration: 10 * time.Second, // Default values
			PlayingDuration:  30 * time.Second,
			EndDuration:      15 * time.Second,
		},
	}

	// Load configuration from manager service
	if managerClient != nil {
		gsm.loadGameConfiguration(context.Background(), managerClient)
	}

	return gsm
}

// StartGameLoop starts a new game loop for a room when all players are ready
func (gsm *GameStateManager) StartGameLoop(ctx context.Context, roomID string) error {
	gsm.loopsMutex.Lock()
	defer gsm.loopsMutex.Unlock()

	// Check if loop is already active
	if _, exists := gsm.activeLoops[roomID]; exists {
		gsm.logger.WithField("roomId", roomID).Warn("Game loop already active for room")
		return fmt.Errorf("game loop already active for room %s", roomID)
	}

	// Get room information
	room, err := gsm.roomService.GetRoom(ctx, roomID)
	if err != nil {
		return fmt.Errorf("failed to get room: %w", err)
	}

	// Verify all players are ready
	if !room.CanStartGame() {
		return fmt.Errorf("room is not ready to start game")
	}

	// Create new game loop
	loopCtx, cancel := context.WithCancel(ctx)
	gameLoop := &GameLoop{
		RoomID:     roomID,
		State:      StateStarting,
		Cancel:     cancel,
		LastUpdate: time.Now(),
		Players:    room.Players,
	}

	gsm.activeLoops[roomID] = gameLoop

	// Start the game loop in a goroutine
	go gsm.runGameLoop(loopCtx, gameLoop)

	gsm.logger.WithField("roomId", roomID).Info("Started game loop")
	return nil
}

// StopGameLoop stops an active game loop
func (gsm *GameStateManager) StopGameLoop(roomID string) {
	gsm.loopsMutex.Lock()
	defer gsm.loopsMutex.Unlock()

	if gameLoop, exists := gsm.activeLoops[roomID]; exists {
		gameLoop.Cancel()
		if gameLoop.Timer != nil {
			gameLoop.Timer.Stop()
		}
		delete(gsm.activeLoops, roomID)
		gsm.logger.WithField("roomId", roomID).Info("Stopped game loop")
	}
}

// HandlePlayerReadyChange handles when a player's ready status changes
func (gsm *GameStateManager) HandlePlayerReadyChange(ctx context.Context, roomID string, allReady bool) error {
	gsm.loopsMutex.RLock()
	gameLoop, exists := gsm.activeLoops[roomID]
	gsm.loopsMutex.RUnlock()

	if !exists {
		// No active loop - start one if all players are ready
		if allReady {
			return gsm.StartGameLoop(ctx, roomID)
		}
		return nil
	}

	gameLoop.mutex.Lock()
	defer gameLoop.mutex.Unlock()

	// If we're in starting state and not all players are ready, stop the countdown
	if gameLoop.State == StateStarting && !allReady {
		if gameLoop.Timer != nil {
			gameLoop.Timer.Stop()
		}

		// Publish countdown stopped event
		gsm.publishGameStateEvent(ctx, roomID, "countdown_stopped", map[string]interface{}{
			"reason": "player_not_ready",
		})

		gsm.logger.WithField("roomId", roomID).Info("Stopped countdown due to player not ready")
		return nil
	}

	// If all players are ready again during starting state, restart countdown
	if gameLoop.State == StateStarting && allReady {
		return gsm.restartCountdown(ctx, gameLoop)
	}

	return nil
}

// runGameLoop runs the main game loop
func (gsm *GameStateManager) runGameLoop(ctx context.Context, gameLoop *GameLoop) {
	defer func() {
		gsm.loopsMutex.Lock()
		delete(gsm.activeLoops, gameLoop.RoomID)
		gsm.loopsMutex.Unlock()
	}()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			if err := gsm.executeGameState(ctx, gameLoop); err != nil {
				gsm.logger.WithError(err).WithField("roomId", gameLoop.RoomID).Error("Error executing game state")
				return
			}
		}
	}
}

// executeGameState executes the current game state
func (gsm *GameStateManager) executeGameState(ctx context.Context, gameLoop *GameLoop) error {
	gameLoop.mutex.Lock()
	defer gameLoop.mutex.Unlock()

	switch gameLoop.State {
	case StateStarting:
		return gsm.executeStartingState(ctx, gameLoop)
	case StatePlaying:
		return gsm.executePlayingState(ctx, gameLoop)
	case StateEnd:
		return gsm.executeEndState(ctx, gameLoop)
	default:
		return fmt.Errorf("unknown game state: %s", gameLoop.State)
	}
}

// executeStartingState handles the starting/countdown state
func (gsm *GameStateManager) executeStartingState(ctx context.Context, gameLoop *GameLoop) error {
	// Publish game starting event
	gsm.publishGameStateEvent(ctx, gameLoop.RoomID, "game_starting", map[string]interface{}{
		"countdown": int(gsm.config.StartingDuration.Seconds()),
		"state":     string(StateStarting),
	})

	// Publish specification-compliant game state change event
	participantCount := gsm.getParticipantCount(ctx, gameLoop.RoomID)
	gsm.publishGameStateChangeSpec(ctx, gameLoop.RoomID, "waiting", "countdown", participantCount)

	// Start countdown timer
	gameLoop.Timer = time.NewTimer(gsm.config.StartingDuration)

	// Publish countdown updates
	go gsm.publishCountdownUpdates(ctx, gameLoop.RoomID, gsm.config.StartingDuration)

	select {
	case <-gameLoop.Timer.C:
		// Countdown finished, transition to playing
		gameLoop.State = StatePlaying
		gameLoop.LastUpdate = time.Now()
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// executePlayingState handles the playing state
func (gsm *GameStateManager) executePlayingState(ctx context.Context, gameLoop *GameLoop) error {
	// Publish game started event
	gsm.publishGameStateEvent(ctx, gameLoop.RoomID, "game_started", map[string]interface{}{
		"state": string(StatePlaying),
	})

	// Publish specification-compliant game state change event
	participantCount := gsm.getParticipantCount(ctx, gameLoop.RoomID)
	gsm.publishGameStateChangeSpec(ctx, gameLoop.RoomID, "countdown", "spinning", participantCount)

	// Execute game logic (wheel spinning, etc.)
	if err := gsm.executeGameLogic(ctx, gameLoop); err != nil {
		return fmt.Errorf("failed to execute game logic: %w", err)
	}

	// Wait for playing duration
	gameLoop.Timer = time.NewTimer(gsm.config.PlayingDuration)

	select {
	case <-gameLoop.Timer.C:
		// Playing finished, transition to end
		gameLoop.State = StateEnd
		gameLoop.LastUpdate = time.Now()
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// executeEndState handles the end state
func (gsm *GameStateManager) executeEndState(ctx context.Context, gameLoop *GameLoop) error {
	// Publish game finished event
	gsm.publishGameStateEvent(ctx, gameLoop.RoomID, "game_finished", map[string]interface{}{
		"state": string(StateEnd),
	})

	// Publish specification-compliant game state change event
	participantCount := gsm.getParticipantCount(ctx, gameLoop.RoomID)
	gsm.publishGameStateChangeSpec(ctx, gameLoop.RoomID, "spinning", "result", participantCount)

	// Wait for end duration
	gameLoop.Timer = time.NewTimer(gsm.config.EndDuration)

	select {
	case <-gameLoop.Timer.C:
		// End finished, check if we should restart the loop
		// Publish transition back to waiting state
		gsm.publishGameStateChangeSpec(ctx, gameLoop.RoomID, "result", "waiting", 0)
		return gsm.checkForRestart(ctx, gameLoop)
	case <-ctx.Done():
		return ctx.Err()
	}
}

// restartCountdown restarts the countdown timer
func (gsm *GameStateManager) restartCountdown(ctx context.Context, gameLoop *GameLoop) error {
	if gameLoop.Timer != nil {
		gameLoop.Timer.Stop()
	}

	// Publish countdown restarted event
	gsm.publishGameStateEvent(ctx, gameLoop.RoomID, "countdown_restarted", map[string]interface{}{
		"countdown": int(gsm.config.StartingDuration.Seconds()),
	})

	gsm.logger.WithField("roomId", gameLoop.RoomID).Info("Restarted countdown")
	return nil
}

// checkForRestart checks if the game loop should restart
func (gsm *GameStateManager) checkForRestart(ctx context.Context, gameLoop *GameLoop) error {
	// Get updated room state
	room, err := gsm.roomService.GetRoom(ctx, gameLoop.RoomID)
	if err != nil {
		return fmt.Errorf("failed to get room for restart check: %w", err)
	}

	// If all players are still ready, restart the loop
	if room.CanStartGame() {
		gameLoop.State = StateStarting
		gameLoop.LastUpdate = time.Now()
		gameLoop.Players = room.Players

		gsm.logger.WithField("roomId", gameLoop.RoomID).Info("Restarting game loop")
		return nil
	}

	// Otherwise, end the loop
	gsm.logger.WithField("roomId", gameLoop.RoomID).Info("Ending game loop - not all players ready")
	return fmt.Errorf("game loop ended")
}

// executeGameLogic executes the actual game logic (wheel spinning, etc.)
func (gsm *GameStateManager) executeGameLogic(_ context.Context, gameLoop *GameLoop) error {
	// This would call the actual game service to execute game logic
	// For now, just log that we're executing game logic
	gsm.logger.WithField("roomId", gameLoop.RoomID).Info("Executing game logic")

	// TODO: Call gameService.ExecuteGameLogic here
	return nil
}

// publishCountdownUpdates publishes countdown timer updates
func (gsm *GameStateManager) publishCountdownUpdates(ctx context.Context, roomID string, duration time.Duration) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	remaining := int(duration.Seconds())

	for remaining > 0 {
		select {
		case <-ticker.C:
			remaining--
			gsm.publishGameStateEvent(ctx, roomID, "countdown_update", map[string]interface{}{
				"countdown": remaining,
			})
		case <-ctx.Done():
			return
		}
	}
}

// loadGameConfiguration loads game configuration from Manager Service
func (gsm *GameStateManager) loadGameConfiguration(ctx context.Context, managerClient interface{}) {
	// Type assert to get the actual manager client
	if client, ok := managerClient.(interface {
		GetGameConfiguration(ctx context.Context) (*GameConfiguration, error)
	}); ok {
		config, err := client.GetGameConfiguration(ctx)
		if err != nil {
			gsm.logger.WithError(err).Warn("Failed to load game configuration from Manager Service, using defaults")
			return
		}

		// Update configuration with values from Manager Service
		if config.StartingDuration > 0 {
			gsm.config.StartingDuration = time.Duration(config.StartingDuration) * time.Second
		}
		if config.PlayingDuration > 0 {
			gsm.config.PlayingDuration = time.Duration(config.PlayingDuration) * time.Second
		}
		if config.EndDuration > 0 {
			gsm.config.EndDuration = time.Duration(config.EndDuration) * time.Second
		}

		gsm.logger.WithFields(logrus.Fields{
			"startingDuration": gsm.config.StartingDuration,
			"playingDuration":  gsm.config.PlayingDuration,
			"endDuration":      gsm.config.EndDuration,
		}).Info("Loaded game configuration from Manager Service")
	} else {
		gsm.logger.Warn("Manager client does not support game configuration, using defaults")
	}
}

// publishGameStateEvent publishes a game state event via Redis
func (gsm *GameStateManager) publishGameStateEvent(ctx context.Context, roomID string, eventType string, data map[string]interface{}) {
	event := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      eventType,
			"payload":   data,
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		},
		"metadata": map[string]interface{}{
			"serviceId": "game-service",
			"version":   "1.0.0",
		},
	}

	channel := fmt.Sprintf("game:room:%s", roomID)
	if err := gsm.redisClient.Publish(ctx, channel, event); err != nil {
		gsm.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":    roomID,
			"eventType": eventType,
			"channel":   channel,
		}).Warn("Failed to publish game state event")
	}
}

// publishGameStateChangeSpec publishes a specification-compliant game state change event
func (gsm *GameStateManager) publishGameStateChangeSpec(ctx context.Context, roomID, previousState, newState string, participantCount int) {
	// Get current prize pool from room service
	prizePool := gsm.getPrizePool(ctx, roomID)

	event := map[string]interface{}{
		"event": map[string]interface{}{
			"type": "game_state_change",
			"payload": map[string]interface{}{
				"roomId":           roomID,
				"previousState":    previousState,
				"newState":         newState,
				"participantCount": participantCount,
				"prizePool":        prizePool,
				"timestamp":        time.Now().UTC().Format(time.RFC3339),
			},
		},
		"metadata": map[string]interface{}{
			"serviceId": "game-service",
			"version":   "1.0.0",
		},
	}

	channel := fmt.Sprintf("game:room:%s", roomID)
	if err := gsm.redisClient.Publish(ctx, channel, event); err != nil {
		gsm.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        roomID,
			"previousState": previousState,
			"newState":      newState,
			"channel":       channel,
		}).Warn("Failed to publish game state change spec event")
	}

	gsm.logger.WithFields(logrus.Fields{
		"roomId":           roomID,
		"previousState":    previousState,
		"newState":         newState,
		"participantCount": participantCount,
		"prizePool":        prizePool,
	}).Info("Published game state change spec event")
}

// getParticipantCount gets the number of ready players in a room
func (gsm *GameStateManager) getParticipantCount(ctx context.Context, roomID string) int {
	// Get room from room service to count ready players
	if gsm.roomService == nil {
		gsm.logger.WithField("roomId", roomID).Warn("Room service not available for participant count")
		return 0
	}

	room, err := gsm.roomService.GetRoom(ctx, roomID)
	if err != nil {
		gsm.logger.WithError(err).WithField("roomId", roomID).Warn("Failed to get room for participant count")
		return 0
	}

	readyCount := room.GetReadyPlayerCount()
	gsm.logger.WithFields(logrus.Fields{
		"roomId":     roomID,
		"readyCount": readyCount,
		"totalCount": room.CurrentPlayers,
	}).Debug("Retrieved participant count")

	return readyCount
}

// getPrizePool gets the current prize pool for a room
func (gsm *GameStateManager) getPrizePool(ctx context.Context, roomID string) float64 {
	// Try to get prize pool from cache first
	cacheKey := fmt.Sprintf("room:%s:prize_pool", roomID)
	var cachedPrizePool float64
	if err := gsm.redisClient.Get(ctx, cacheKey, &cachedPrizePool); err == nil {
		return cachedPrizePool
	}

	// Get room from room service to calculate prize pool
	if gsm.roomService == nil {
		gsm.logger.WithField("roomId", roomID).Warn("Room service not available for prize pool calculation")
		return 0.0
	}

	room, err := gsm.roomService.GetRoom(ctx, roomID)
	if err != nil {
		gsm.logger.WithError(err).WithField("roomId", roomID).Warn("Failed to get room for prize pool calculation")
		return 0.0
	}

	// Calculate prize pool based on ready players and bet amount
	readyCount := room.GetReadyPlayerCount()
	prizePool := float64(readyCount) * (float64(room.BetAmount) / 100.0) // Convert cents to dollars

	// Cache the prize pool for 30 seconds
	if err := gsm.redisClient.Set(ctx, cacheKey, prizePool, 30*time.Second); err != nil {
		gsm.logger.WithError(err).Warn("Failed to cache prize pool")
	}

	gsm.logger.WithFields(logrus.Fields{
		"roomId":     roomID,
		"readyCount": readyCount,
		"betAmount":  room.BetAmount,
		"prizePool":  prizePool,
	}).Debug("Calculated prize pool")

	return prizePool
}
