package services

import (
	"context"
	"fmt"
	"time"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/redis"
)

// gameService implements the GameService interface using orchestration
type gameService struct {
	orchestrator  *GameOrchestrator
	healthService *HealthService
}

// NewGameService creates a new game service instance
func NewGameService(
	gameRepo repositories.GameRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
) GameService {
	// Create orchestrator and health service
	orchestrator := NewGameOrchestrator(gameRepo, redisClient, config)
	healthService := NewHealthService(gameRepo, redisClient, config)

	return &gameService{
		orchestrator:  orchestrator,
		healthService: healthService,
	}
}

// Game session management methods

// CreateGameSession creates a new game session
func (s *gameService) CreateGameSession(ctx context.Context, roomID string, config models.SessionConfiguration) (*models.GameSession, error) {
	return s.orchestrator.CreateGameSession(ctx, roomID, config)
}

// StartGame starts a game session
func (s *gameService) StartGame(ctx context.Context, sessionID string) error {
	return s.orchestrator.StartGame(ctx, sessionID)
}

// EndGame ends a game session with results
func (s *gameService) EndGame(ctx context.Context, sessionID string, results *models.GameResults) error {
	return s.orchestrator.EndGame(ctx, sessionID, results)
}

// CancelGame cancels a game session
func (s *gameService) CancelGame(ctx context.Context, sessionID string, reason string) error {
	return s.orchestrator.CancelGame(ctx, sessionID, reason)
}

// GetGameSession retrieves a game session by ID
func (s *gameService) GetGameSession(ctx context.Context, sessionID string) (*models.GameSession, error) {
	return s.orchestrator.sessionCoordinator.GetSession(ctx, sessionID)
}

// GetGameState retrieves the current game state
func (s *gameService) GetGameState(ctx context.Context, sessionID string) (*models.GameState, error) {
	return s.orchestrator.GetGameState(ctx, sessionID)
}

// UpdateGameState updates the game state
func (s *gameService) UpdateGameState(ctx context.Context, sessionID string, gameData map[string]interface{}) error {
	return s.orchestrator.UpdateGameState(ctx, sessionID, gameData)
}

// Player management methods

// AddPlayerToGame adds a player to a game session
func (s *gameService) AddPlayerToGame(ctx context.Context, sessionID string, player models.SessionPlayer) error {
	return s.orchestrator.AddPlayerToGame(ctx, sessionID, player)
}

// RemovePlayerFromGame removes a player from a game session
func (s *gameService) RemovePlayerFromGame(ctx context.Context, sessionID string, userID string) error {
	return s.orchestrator.RemovePlayerFromGame(ctx, sessionID, userID)
}

// UpdatePlayerStatus updates a player's status in a game session
func (s *gameService) UpdatePlayerStatus(ctx context.Context, sessionID string, userID string, status models.PlayerStatus) error {
	// Player status updates are now handled internally by the orchestrator
	// This method is deprecated in favor of game state management
	return models.NewGameError(
		models.ErrorCodeInvalidGameState,
		"player status updates should be handled through game state management",
		nil,
	)
}

// Game logic methods (delegated to game engine service)

// ExecuteGameLogic executes the game logic based on game type
func (s *gameService) ExecuteGameLogic(ctx context.Context, sessionID string, gameType models.GameType) (*models.GameResults, error) {
	// Get session to pass players to game engine
	session, err := s.orchestrator.sessionCoordinator.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Delegate to game engine service
	return s.orchestrator.gameEngineClient.ExecuteGame(ctx, sessionID, gameType, session.Players)
}

// GenerateGameResults generates game results for a session
func (s *gameService) GenerateGameResults(ctx context.Context, sessionID string) (*models.GameResults, error) {
	session, err := s.orchestrator.sessionCoordinator.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	return s.ExecuteGameLogic(ctx, sessionID, session.GameType)
}

// CalculatePayouts calculates payouts for game results
func (s *gameService) CalculatePayouts(ctx context.Context, sessionID string, results *models.GameResults) (*models.PayoutCalculation, error) {
	// Delegate to game engine service
	return s.orchestrator.gameEngineClient.CalculatePayouts(ctx, sessionID, results)
}

// ProcessPayouts processes payouts for game results
func (s *gameService) ProcessPayouts(ctx context.Context, sessionID string, payouts *models.PayoutCalculation) error {
	// This would typically integrate with a payment service
	// For now, we'll just return success as this is handled by external payment service
	return nil
}

// History and statistics methods

// GetGameHistory retrieves game history for a player
func (s *gameService) GetGameHistory(ctx context.Context, userID string, gameType *models.GameType, page, limit int) ([]*models.GameHistory, error) {
	return s.orchestrator.GetGameHistory(ctx, userID, gameType, page, limit)
}

// GetPlayerGameStats retrieves game statistics for a player
func (s *gameService) GetPlayerGameStats(ctx context.Context, userID string) (*PlayerGameStats, error) {
	return s.orchestrator.GetPlayerGameStats(ctx, userID)
}

// Maintenance methods

// CleanupExpiredSessions cleans up expired game sessions
func (s *gameService) CleanupExpiredSessions(ctx context.Context) error {
	// This would be implemented as a scheduled job that calls the orchestrator
	// For now, return success as this is handled by external scheduler
	return nil
}

// ArchiveCompletedSessions archives old completed sessions
func (s *gameService) ArchiveCompletedSessions(ctx context.Context, olderThan time.Time) error {
	// This would be implemented as a scheduled job
	// For now, return success as this is handled by external archival service
	return nil
}

// Health and monitoring methods

// HealthCheck performs a comprehensive health check
func (s *gameService) HealthCheck(ctx context.Context) error {
	result := s.healthService.HealthCheck(ctx)
	if result.Status != "healthy" {
		return fmt.Errorf("health check failed: %s", result.Status)
	}
	return nil
}

// GetServiceStats returns service statistics
func (s *gameService) GetServiceStats(ctx context.Context) (*ServiceStats, error) {
	return s.healthService.GetServiceStats(ctx), nil
}

// GetOrchestratorStats returns orchestrator statistics
func (s *gameService) GetOrchestratorStats() *GameOrchestratorStats {
	return s.orchestrator.GetServiceStats()
}

// Note: Type definitions moved to interfaces.go to avoid duplication
