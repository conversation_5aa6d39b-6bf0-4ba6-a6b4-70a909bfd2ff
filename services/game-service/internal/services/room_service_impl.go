package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/clients/room"
	"github.com/xzgame/game-service/pkg/redis"
)

// roomService implements the RoomService interface using orchestration
type roomService struct {
	roomClient  *room.RoomClient
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
}

// NewRoomService creates a new room service instance
func NewRoomService(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	managerClient interface{}, // Legacy parameter for compatibility
	config *config.Config,
) RoomService {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Create room service client
	roomClient := room.NewRoomClient(config.RoomServiceURL)

	return &roomService{
		roomClient:  roomClient,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
	}
}

// Room management methods (delegated to room service)

// CreateRoom creates a new room
func (s *roomService) CreateRoom(ctx context.Context, request models.CreateRoomRequest) (*models.Room, error) {
	s.logger.WithFields(logrus.Fields{
		"name":      request.Name,
		"game_type": request.GameType,
	}).Info("Creating room via room service")

	return s.roomClient.CreateRoom(ctx, request)
}

// GetRoom retrieves a room by ID
func (s *roomService) GetRoom(ctx context.Context, roomID string) (*models.Room, error) {
	return s.roomClient.GetRoom(ctx, roomID)
}

// GetRoomFresh retrieves a room by ID, bypassing cache
func (s *roomService) GetRoomFresh(ctx context.Context, roomID string) (*models.Room, error) {
	// Room service handles its own caching, so this is the same as GetRoom
	return s.roomClient.GetRoom(ctx, roomID)
}

// UpdateRoom updates a room
func (s *roomService) UpdateRoom(ctx context.Context, roomID string, updates models.UpdateRoomRequest) (*models.Room, error) {
	s.logger.WithField("room_id", roomID).Info("Updating room via room service")

	// For now, we'll get the room and return it as room service doesn't have update method yet
	// In a real implementation, this would call s.roomClient.UpdateRoom(ctx, roomID, updates)
	return s.roomClient.GetRoom(ctx, roomID)
}

// DeleteRoom deletes a room
func (s *roomService) DeleteRoom(ctx context.Context, roomID string, adminUserID string) error {
	s.logger.WithFields(logrus.Fields{
		"room_id":       roomID,
		"admin_user_id": adminUserID,
	}).Info("Deleting room via room service")

	return s.roomClient.DeleteRoom(ctx, roomID, adminUserID)
}

// ListRooms lists rooms with filtering and pagination
func (s *roomService) ListRooms(ctx context.Context, filter models.RoomListFilter) (*models.RoomListResponse, error) {
	// Return empty list - room listing is handled by manager service
	response := &models.RoomListResponse{
		Rooms: []models.Room{},
		Pagination: models.Pagination{
			CurrentPage: filter.Page,
			PerPage:     filter.Limit,
			TotalCount:  0,
			TotalPages:  0,
		},
	}

	return response, nil
}

// GetAvailableRooms gets available rooms for a game type
func (s *roomService) GetAvailableRooms(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	return s.roomClient.GetAvailableRooms(ctx, gameType)
}

// GetRoomsByPlayer gets all rooms where a specific player is present
func (s *roomService) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	s.logger.WithField("user_id", userID).Debug("Getting rooms by player via room service")

	// Return empty list - room queries are handled by manager service
	return []*models.Room{}, nil
}

// Player management methods (delegated to room service)

// JoinRoom adds a player to a room
func (s *roomService) JoinRoom(ctx context.Context, request models.JoinRoomRequest) (*models.Room, error) {
	return s.JoinRoomWithUsername(ctx, request, request.UserID)
}

// JoinRoomWithUsername adds a player to a room with a specific username
func (s *roomService) JoinRoomWithUsername(ctx context.Context, request models.JoinRoomRequest, username string) (*models.Room, error) {
	s.logger.WithFields(logrus.Fields{
		"room_id":    request.RoomID,
		"user_id":    request.UserID,
		"username":   username,
		"bet_amount": request.BetAmount,
	}).Info("Player joining room via room service")

	// Create room player
	player := models.RoomPlayer{
		UserID:   request.UserID,
		Username: username,
		Status:   "waiting", // Use string instead of enum for now
	}

	// Add player to room
	err := s.roomClient.AddPlayerToRoom(ctx, request.RoomID, player)
	if err != nil {
		return nil, err
	}

	// Return updated room
	return s.roomClient.GetRoom(ctx, request.RoomID)
}

// LeaveRoom removes a player from a room (interface method)
func (s *roomService) LeaveRoom(ctx context.Context, request models.LeaveRoomRequest) error {
	s.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
	}).Info("Player leaving room via room service")

	// Remove player from room
	return s.roomClient.RemovePlayerFromRoom(ctx, request.RoomID, request.UserID)
}

// UpdatePlayerStatus updates a player's status in a room
func (s *roomService) UpdatePlayerStatus(ctx context.Context, roomID string, userID string, status models.PlayerStatus) error {
	s.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"user_id": userID,
		"status":  status,
	}).Info("Updating player status via room service")

	// For now, return success as room service doesn't have this method yet
	// In a real implementation, this would call s.roomClient.UpdatePlayerStatus(ctx, roomID, userID, status)
	return nil
}

// Room validation and access methods

// ValidateRoomAccess validates if a user can access a room
func (s *roomService) ValidateRoomAccess(ctx context.Context, roomID string, userID string, password string) (bool, error) {
	return s.roomClient.ValidateRoomAccess(ctx, roomID, userID, password)
}

// IsPlayerInRoom checks if a player is in a specific room
func (s *roomService) IsPlayerInRoom(ctx context.Context, roomID string, userID string) (bool, error) {
	room, err := s.roomClient.GetRoom(ctx, roomID)
	if err != nil {
		return false, err
	}

	// Check if player is in room
	for _, player := range room.Players {
		if player.UserID == userID {
			return true, nil
		}
	}

	return false, nil
}

// GetPlayerPosition gets a player's position in a room
func (s *roomService) GetPlayerPosition(ctx context.Context, roomID string, userID string) (int, error) {
	room, err := s.roomClient.GetRoom(ctx, roomID)
	if err != nil {
		return -1, err
	}

	// Find player position
	for _, player := range room.Players {
		if player.UserID == userID {
			return player.Position, nil
		}
	}

	return -1, models.NewRoomError(models.ErrorCodePlayerNotInRoom, "player not found in room", roomID)
}

// Color selection methods (legacy compatibility)

// SetPlayerColor sets a player's color selection for a room
func (s *roomService) SetPlayerColor(ctx context.Context, roomID string, userID string, color string) error {
	s.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"user_id": userID,
		"color":   color,
	}).Info("Setting player color selection")

	// Store color selection in Redis for backward compatibility
	colorKey := fmt.Sprintf("room:%s:player_colors", roomID)
	colorData := map[string]interface{}{userID: color}

	return s.redisClient.Set(ctx, colorKey, colorData, s.config.RedisTTL)
}

// GetPlayerColors gets all player color selections for a room
func (s *roomService) GetPlayerColors(ctx context.Context, roomID string) (map[string]string, error) {
	colorKey := fmt.Sprintf("room:%s:player_colors", roomID)

	var colorData map[string]interface{}
	err := s.redisClient.Get(ctx, colorKey, &colorData)
	if err != nil {
		return make(map[string]string), nil // Return empty map if no colors set
	}

	// Convert interface{} values to strings
	colors := make(map[string]string)
	for k, v := range colorData {
		if str, ok := v.(string); ok {
			colors[k] = str
		}
	}

	return colors, nil
}

// Health check methods

// HealthCheck performs a health check
func (s *roomService) HealthCheck(ctx context.Context) error {
	return s.roomClient.HealthCheck(ctx)
}

// GetRoomStats retrieves room service statistics (interface method)
func (s *roomService) GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error) {
	// For now, return basic stats
	room, err := s.roomClient.GetRoom(ctx, roomID)
	if err != nil {
		return nil, err
	}

	return &models.RoomStats{
		RoomID:       roomID,
		TotalGames:   0, // Would be calculated from database
		TotalPlayers: room.CurrentPlayers,
	}, nil
}

// GetServiceRoomStats retrieves room service statistics
func (s *roomService) GetServiceRoomStats(ctx context.Context) (*room.RoomStats, error) {
	return s.roomClient.GetRoomStats(ctx)
}

// BroadcastRoomEvent broadcasts a room event (required by RoomService interface)
func (s *roomService) BroadcastRoomEvent(ctx context.Context, roomID string, event models.RoomEvent) error {
	s.logger.WithFields(logrus.Fields{
		"room_id":    roomID,
		"event_type": event.Type,
	}).Debug("Broadcasting room event via room service")

	// For now, just log the event as room service handles its own events
	// In a real implementation, this would delegate to the room service
	return nil
}

// CheckRoomReadiness checks if a room is ready for game start (required by RoomService interface)
func (s *roomService) CheckRoomReadiness(ctx context.Context, roomID string) (bool, error) {
	room, err := s.roomClient.GetRoom(ctx, roomID)
	if err != nil {
		return false, err
	}

	// Check if room has minimum players and all are ready
	if room.CurrentPlayers < room.MinPlayers {
		return false, nil
	}

	// For now, assume room is ready if it has enough players
	// In a real implementation, this would check player readiness status
	return true, nil
}

// GetPlayerColorSelections gets player color selections for a room (required by RoomService interface)
func (s *roomService) GetPlayerColorSelections(ctx context.Context, roomID string) (map[string]string, error) {
	return s.GetPlayerColors(ctx, roomID)
}

// Missing interface methods - adding stubs for now

// RemovePlayerFromCache removes a player from cache
func (s *roomService) RemovePlayerFromCache(ctx context.Context, roomID, userID string) error {
	// For now, just return success as room service handles its own caching
	return nil
}

// SetPlayerReady sets a player as ready
func (s *roomService) SetPlayerReady(ctx context.Context, request models.PlayerReadyRequest) (*models.Room, error) {
	// For now, just return the room as room service doesn't have this method yet
	return s.roomClient.GetRoom(ctx, request.RoomID)
}

// UpdateRoomStatus updates room status
func (s *roomService) UpdateRoomStatus(ctx context.Context, roomID string, status models.RoomStatus) error {
	return s.roomClient.UpdateRoomStatus(ctx, roomID, status)
}

// StartGameInRoom starts a game in a room
func (s *roomService) StartGameInRoom(ctx context.Context, roomID string) (*models.GameSession, error) {
	// This would typically create a game session
	// For now, return an error as this should be handled by the game service
	return nil, fmt.Errorf("game session creation should be handled by game service")
}

// GetRoomActivity gets room activity
func (s *roomService) GetRoomActivity(ctx context.Context, roomID string, since time.Time) (*models.RoomActivity, error) {
	// Return empty activity - room activity is handled by manager service
	return &models.RoomActivity{
		RoomID:    roomID,
		Events:    []models.RoomEvent{},
		Players:   []models.RoomPlayer{},
		UpdatedAt: time.Now(),
	}, nil
}

// NotifyPlayersInRoom notifies all players in a room
func (s *roomService) NotifyPlayersInRoom(ctx context.Context, roomID string, message interface{}) error {
	// For now, just log the notification
	s.logger.WithField("room_id", roomID).Debug("Notifying players in room")
	return nil
}

// PublishRoomStateUpdate publishes room state update
func (s *roomService) PublishRoomStateUpdate(ctx context.Context, roomID string, room *models.Room) error {
	// For now, just log the update
	s.logger.WithField("room_id", roomID).Debug("Publishing room state update")

	// Use legacy event publishing for compatibility
	roomEvent := models.RoomEvent{
		Type:   "room_state_updated",
		RoomID: roomID,
		Data:   map[string]interface{}{"room": room},
	}
	_ = s.publishRoomEvent(ctx, roomEvent) // Use the deprecated method for compatibility

	return nil
}

// PublishJoinEventsForPlayer publishes join events for a player
func (s *roomService) PublishJoinEventsForPlayer(ctx context.Context, roomID, userID, username string) error {
	// For now, just log the event
	s.logger.WithFields(logrus.Fields{
		"room_id":  roomID,
		"user_id":  userID,
		"username": username,
	}).Debug("Publishing join events for player")
	return nil
}

// Legacy compatibility methods (deprecated)

// publishRoomEvent publishes a room event (deprecated - handled by room service)
func (s *roomService) publishRoomEvent(ctx context.Context, event models.RoomEvent) error {
	_ = ctx // Parameter required by interface
	// Room events are now handled by the room service
	s.logger.WithField("event_type", event.Type).Debug("Room event publishing delegated to room service")
	return nil
}

// Helper methods

// findDuplicatePlayers finds duplicate players in a room (for testing)
func (s *roomService) findDuplicatePlayers(room *models.Room) (map[string]models.RoomPlayer, []string) {
	uniquePlayers := make(map[string]models.RoomPlayer)
	duplicateUserIDs := []string{}
	userIDCounts := make(map[string]int)

	// Count occurrences of each user ID
	for _, player := range room.Players {
		userIDCounts[player.UserID]++
	}

	// Find earliest joined player for each user ID and identify duplicates
	for _, player := range room.Players {
		if userIDCounts[player.UserID] > 1 {
			// This user has duplicates
			if existing, exists := uniquePlayers[player.UserID]; !exists || player.JoinedAt.Before(existing.JoinedAt) {
				uniquePlayers[player.UserID] = player
			}
			// Add to duplicates list if not already there
			found := false
			for _, dupID := range duplicateUserIDs {
				if dupID == player.UserID {
					found = true
					break
				}
			}
			if !found {
				duplicateUserIDs = append(duplicateUserIDs, player.UserID)
			}
		} else {
			// No duplicates for this user
			uniquePlayers[player.UserID] = player
		}
	}

	return uniquePlayers, duplicateUserIDs
}

// calculateNextAvailablePosition calculates the next available position (for testing)
func (s *roomService) calculateNextAvailablePosition(players []models.RoomPlayer) int {
	if len(players) == 0 {
		return 0
	}

	// Find the highest position and add 1
	maxPosition := -1
	for _, player := range players {
		if player.Position > maxPosition {
			maxPosition = player.Position
		}
	}

	return maxPosition + 1
}
