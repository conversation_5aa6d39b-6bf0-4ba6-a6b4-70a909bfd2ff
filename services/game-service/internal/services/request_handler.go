package services

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/pkg/redis"
)

// RequestHandlerService handles Redis requests from other services
// This is a simplified version that focuses on core functionality
type RequestHandlerService struct {
	redisClient      *redis.RedisClient
	roomService      RoomService
	lobbyService     LobbyService
	gameStateManager *GameStateManager
	logger           *logrus.Logger
	pendingRequests  map[string]bool // Track pending requests to prevent duplicates
	requestsMutex    sync.RWMutex    // Protect pendingRequests map
	isRunning        bool            // Track if the service is running
	startTime        time.Time       // Track when the service started
	processedCount   int64           // Track number of processed requests
	lastActivity     time.Time       // Track last activity
	activityMutex    sync.RWMutex    // Protect activity tracking
}

// RequestMessage represents a request message from Redis
type RequestMessage struct {
	Event struct {
		Type      string    `json:"type"`
		Payload   any       `json:"payload"`
		Timestamp time.Time `json:"timestamp"`
	} `json:"event"`
	Metadata struct {
		ServiceID       string `json:"serviceId"`
		Version         string `json:"version"`
		CorrelationID   string `json:"correlationId"`
		ResponseChannel string `json:"responseChannel,omitempty"`
		Priority        int    `json:"priority"`
	} `json:"metadata"`
}

// NewRequestHandlerService creates a new request handler service
func NewRequestHandlerService(
	redisClient *redis.RedisClient,
	roomService RoomService,
	lobbyService LobbyService,
	gameStateManager *GameStateManager,
) *RequestHandlerService {
	return &RequestHandlerService{
		redisClient:      redisClient,
		roomService:      roomService,
		lobbyService:     lobbyService,
		gameStateManager: gameStateManager,
		logger:           logrus.New(),
		pendingRequests:  make(map[string]bool),
		isRunning:        false,
		startTime:        time.Now(),
		processedCount:   0,
		lastActivity:     time.Now(),
	}
}

// Start begins listening for Redis requests
func (s *RequestHandlerService) Start(ctx context.Context) error {
	s.logger.Info("Starting simplified Redis request handler service")

	// Mark as running
	s.activityMutex.Lock()
	s.isRunning = true
	s.startTime = time.Now()
	s.activityMutex.Unlock()

	// Subscribe to multiple channels
	pubsub := s.redisClient.Subscribe(ctx, "game:requests", "game:lobby:updates", "admin:notifications")
	defer pubsub.Close()

	// Listen for messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Request handler service stopped")
			// Mark as not running
			s.activityMutex.Lock()
			s.isRunning = false
			s.activityMutex.Unlock()
			return ctx.Err()
		case msg := <-ch:
			s.handleMessage(ctx, msg.Payload, msg.Channel)
		}
	}
}

// handleMessage processes incoming Redis messages
func (s *RequestHandlerService) handleMessage(ctx context.Context, payload string, channel string) {
	// Update activity tracking
	s.activityMutex.Lock()
	s.lastActivity = time.Now()
	s.processedCount++
	s.activityMutex.Unlock()

	var requestMsg RequestMessage
	if err := json.Unmarshal([]byte(payload), &requestMsg); err != nil {
		s.logger.WithError(err).WithField("channel", channel).Error("Failed to parse request message")
		return
	}

	s.logger.WithFields(logrus.Fields{
		"type":          requestMsg.Event.Type,
		"serviceId":     requestMsg.Metadata.ServiceID,
		"correlationId": requestMsg.Metadata.CorrelationID,
		"channel":       channel,
	}).Info("Processing message")

	// Route based on channel
	switch channel {
	case "game:requests":
		s.handleGameRequest(ctx, requestMsg)
	case "game:lobby:updates":
		s.handleLobbyUpdate(ctx, requestMsg)
	case "admin:notifications":
		s.handleAdminNotification(ctx, requestMsg)
	default:
		s.logger.WithField("channel", channel).Warn("Unknown channel")
	}
}

// handleGameRequest processes game service requests
func (s *RequestHandlerService) handleGameRequest(ctx context.Context, requestMsg RequestMessage) {
	// Check for duplicate requests
	if s.isDuplicateRequest(requestMsg.Metadata.CorrelationID) {
		s.logger.WithField("correlationId", requestMsg.Metadata.CorrelationID).Warn("Duplicate request ignored")
		return
	}

	// Mark request as being processed
	s.markRequestProcessing(requestMsg.Metadata.CorrelationID)
	defer s.markRequestComplete(requestMsg.Metadata.CorrelationID)

	switch requestMsg.Event.Type {
	case "request_room_list":
		s.handleRoomListRequest(ctx, requestMsg)
	case "subscribe_lobby":
		s.handleSubscribeLobby(ctx, requestMsg)
	case "verify_token":
		s.handleTokenVerification(ctx, requestMsg)
	default:
		s.logger.WithField("type", requestMsg.Event.Type).Info("Request type delegated to modular handlers")
		// For now, just log that we received the request
		// The modular handlers in pkg/request will handle the actual processing
	}
}

// handleLobbyUpdate processes lobby update events
func (s *RequestHandlerService) handleLobbyUpdate(ctx context.Context, requestMsg RequestMessage) {
	_ = ctx // Parameter required by interface
	s.logger.WithField("type", requestMsg.Event.Type).Info("Lobby update received")
}

// handleAdminNotification processes admin notification events
func (s *RequestHandlerService) handleAdminNotification(ctx context.Context, requestMsg RequestMessage) {
	_ = ctx // Parameter required by interface
	s.logger.WithField("type", requestMsg.Event.Type).Info("Admin notification received")
}

// Basic request handling methods (simplified versions)
func (s *RequestHandlerService) handleRoomListRequest(ctx context.Context, requestMsg RequestMessage) {
	_ = ctx        // Parameter required by interface
	_ = requestMsg // Parameter required by interface
	s.logger.Info("Handling room list request - delegating to room service")
	// This would delegate to the room service or modular handlers
}

func (s *RequestHandlerService) handleSubscribeLobby(ctx context.Context, requestMsg RequestMessage) {
	_ = ctx        // Parameter required by interface
	_ = requestMsg // Parameter required by interface
	s.logger.Info("Handling lobby subscription - delegating to lobby service")
	// This would delegate to the lobby service or modular handlers
}

func (s *RequestHandlerService) handleTokenVerification(ctx context.Context, requestMsg RequestMessage) {
	_ = ctx        // Parameter required by interface
	_ = requestMsg // Parameter required by interface
	s.logger.Info("Handling token verification - delegating to auth service")
	// This would delegate to the auth service or modular handlers
}

// Utility methods for request tracking
func (s *RequestHandlerService) isDuplicateRequest(correlationID string) bool {
	s.requestsMutex.RLock()
	defer s.requestsMutex.RUnlock()
	return s.pendingRequests[correlationID]
}

func (s *RequestHandlerService) markRequestProcessing(correlationID string) {
	s.requestsMutex.Lock()
	defer s.requestsMutex.Unlock()
	s.pendingRequests[correlationID] = true
}

func (s *RequestHandlerService) markRequestComplete(correlationID string) {
	s.requestsMutex.Lock()
	defer s.requestsMutex.Unlock()
	delete(s.pendingRequests, correlationID)
}

// GetStats returns handler statistics
func (s *RequestHandlerService) GetStats() map[string]interface{} {
	s.activityMutex.RLock()
	defer s.activityMutex.RUnlock()

	return map[string]interface{}{
		"isRunning":       s.isRunning,
		"startTime":       s.startTime,
		"processedCount":  s.processedCount,
		"lastActivity":    s.lastActivity,
		"pendingRequests": len(s.pendingRequests),
	}
}

// Stop gracefully stops the handler
func (s *RequestHandlerService) Stop() {
	s.activityMutex.Lock()
	defer s.activityMutex.Unlock()
	s.isRunning = false
	s.logger.Info("Request handler stopped")
}

// IsHealthy checks if the service is healthy
func (s *RequestHandlerService) IsHealthy() bool {
	s.activityMutex.RLock()
	defer s.activityMutex.RUnlock()

	if !s.isRunning {
		return false
	}

	// Consider unhealthy if no activity for more than 5 minutes
	timeSinceLastActivity := time.Since(s.lastActivity)
	return timeSinceLastActivity <= 5*time.Minute
}
