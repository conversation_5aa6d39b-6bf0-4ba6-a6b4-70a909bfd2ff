package services

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/xzgame/game-service/internal/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestRoomService_FindDuplicatePlayers(t *testing.T) {
	// Create a logger for the service
	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel) // Reduce log noise during tests

	service := &roomService{
		logger: logger,
	}

	tests := []struct {
		name                     string
		players                  []models.RoomPlayer
		expectedUniqueCount      int
		expectedDuplicateCount   int
		expectedDuplicateUserIDs []string
	}{
		{
			name: "no duplicates - should return empty duplicate list",
			players: []models.RoomPlayer{
				{UserID: "user1", Username: "player1", Position: 0, JoinedAt: time.Now()},
				{UserID: "user2", Username: "player2", Position: 1, JoinedAt: time.Now()},
				{UserID: "user3", Username: "player3", Position: 2, JoinedAt: time.Now()},
			},
			expectedUniqueCount:      3,
			expectedDuplicateCount:   0,
			expectedDuplicateUserIDs: []string{},
		},
		{
			name: "one duplicate player - should identify only the duplicate",
			players: []models.RoomPlayer{
				{UserID: "user1", Username: "player1", Position: 0, JoinedAt: time.Now().Add(-2 * time.Minute)},
				{UserID: "user2", Username: "player2", Position: 1, JoinedAt: time.Now().Add(-1 * time.Minute)},
				{UserID: "user1", Username: "player1", Position: 2, JoinedAt: time.Now()}, // Duplicate
			},
			expectedUniqueCount:      2,
			expectedDuplicateCount:   1,
			expectedDuplicateUserIDs: []string{"user1"},
		},
		{
			name: "multiple duplicates - should identify all duplicate users",
			players: []models.RoomPlayer{
				{UserID: "user1", Username: "player1", Position: 0, JoinedAt: time.Now().Add(-3 * time.Minute)},
				{UserID: "user2", Username: "player2", Position: 1, JoinedAt: time.Now().Add(-2 * time.Minute)},
				{UserID: "user1", Username: "player1", Position: 2, JoinedAt: time.Now().Add(-1 * time.Minute)}, // Duplicate
				{UserID: "user3", Username: "player3", Position: 3, JoinedAt: time.Now().Add(-30 * time.Second)},
				{UserID: "user2", Username: "player2", Position: 4, JoinedAt: time.Now()}, // Duplicate
			},
			expectedUniqueCount:      3,
			expectedDuplicateCount:   2,
			expectedDuplicateUserIDs: []string{"user1", "user2"},
		},
		{
			name: "triple duplicate - should identify user only once in duplicate list",
			players: []models.RoomPlayer{
				{UserID: "user1", Username: "player1", Position: 0, JoinedAt: time.Now().Add(-3 * time.Minute)},
				{UserID: "user1", Username: "player1", Position: 1, JoinedAt: time.Now().Add(-2 * time.Minute)}, // Duplicate
				{UserID: "user1", Username: "player1", Position: 2, JoinedAt: time.Now().Add(-1 * time.Minute)}, // Duplicate
				{UserID: "user2", Username: "player2", Position: 3, JoinedAt: time.Now()},
			},
			expectedUniqueCount:      2,
			expectedDuplicateCount:   1,
			expectedDuplicateUserIDs: []string{"user1"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			room := &models.Room{
				ID:      primitive.NewObjectID(),
				Players: tt.players,
			}

			uniquePlayers, duplicateUserIDs := service.findDuplicatePlayers(room)

			// Check unique players count
			assert.Equal(t, tt.expectedUniqueCount, len(uniquePlayers),
				"Expected %d unique players, got %d", tt.expectedUniqueCount, len(uniquePlayers))

			// Check duplicate count
			assert.Equal(t, tt.expectedDuplicateCount, len(duplicateUserIDs),
				"Expected %d duplicate user IDs, got %d", tt.expectedDuplicateCount, len(duplicateUserIDs))

			// Check specific duplicate user IDs
			assert.ElementsMatch(t, tt.expectedDuplicateUserIDs, duplicateUserIDs,
				"Expected duplicate user IDs %v, got %v", tt.expectedDuplicateUserIDs, duplicateUserIDs)

			// Verify that each unique player is the one with the earliest join time
			for userID, player := range uniquePlayers {
				earliestJoinTime := player.JoinedAt
				for _, roomPlayer := range room.Players {
					if roomPlayer.UserID == userID && roomPlayer.JoinedAt.Before(earliestJoinTime) {
						t.Errorf("Player %s in uniquePlayers is not the earliest joined instance", userID)
					}
				}
			}

			// Verify that all non-duplicate players are in uniquePlayers
			playerCounts := make(map[string]int)
			for _, player := range room.Players {
				playerCounts[player.UserID]++
			}

			for userID, count := range playerCounts {
				if count == 1 {
					// Non-duplicate player should be in uniquePlayers
					assert.Contains(t, uniquePlayers, userID,
						"Non-duplicate player %s should be in uniquePlayers", userID)
					// And should NOT be in duplicateUserIDs
					assert.NotContains(t, duplicateUserIDs, userID,
						"Non-duplicate player %s should not be in duplicateUserIDs", userID)
				}
			}
		})
	}
}
