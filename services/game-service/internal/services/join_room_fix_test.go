package services

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/xzgame/game-service/internal/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestJoinRoomPlayerAddition tests that new players are properly added to rooms
// without replacing existing players, even when duplicate cleanup runs
func TestJoinRoomPlayerAddition(t *testing.T) {
	// Create a logger for the service
	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel) // Reduce log noise during tests
	
	service := &roomService{
		logger: logger,
	}

	t.Run("new players should be added without replacing existing players", func(t *testing.T) {
		// Create a room with existing players
		room := &models.Room{
			ID:             primitive.NewObjectID(),
			CurrentPlayers: 2,
			MaxPlayers:     4,
			Players: []models.RoomPlayer{
				{
					UserID:    "existing_user_1",
					Username:  "ExistingPlayer1",
					Position:  0,
					IsReady:   false,
					BetAmount: 100,
					JoinedAt:  time.Now().Add(-5 * time.Minute),
					Status:    models.PlayerStatusJoined,
				},
				{
					UserID:    "existing_user_2", 
					Username:  "ExistingPlayer2",
					Position:  1,
					IsReady:   true,
					BetAmount: 100,
					JoinedAt:  time.Now().Add(-3 * time.Minute),
					Status:    models.PlayerStatusJoined,
				},
			},
		}

		// Run duplicate cleanup (this is what was causing the bug)
		uniquePlayers, duplicateUserIDs := service.findDuplicatePlayers(room)

		// Verify no duplicates are found (since there are none)
		assert.Equal(t, 0, len(duplicateUserIDs), "Should find no duplicates in a clean room")
		assert.Equal(t, 2, len(uniquePlayers), "Should preserve both existing players")

		// Verify both existing players are preserved
		assert.Contains(t, uniquePlayers, "existing_user_1", "First existing player should be preserved")
		assert.Contains(t, uniquePlayers, "existing_user_2", "Second existing player should be preserved")

		// Verify player details are preserved correctly
		player1 := uniquePlayers["existing_user_1"]
		assert.Equal(t, "ExistingPlayer1", player1.Username)
		assert.Equal(t, 0, player1.Position)
		assert.Equal(t, false, player1.IsReady)

		player2 := uniquePlayers["existing_user_2"]
		assert.Equal(t, "ExistingPlayer2", player2.Username)
		assert.Equal(t, 1, player2.Position)
		assert.Equal(t, true, player2.IsReady)
	})

	t.Run("duplicate cleanup should only remove actual duplicates", func(t *testing.T) {
		// Create a room with one legitimate duplicate
		room := &models.Room{
			ID:             primitive.NewObjectID(),
			CurrentPlayers: 3,
			MaxPlayers:     4,
			Players: []models.RoomPlayer{
				{
					UserID:    "user_1",
					Username:  "Player1",
					Position:  0,
					IsReady:   false,
					BetAmount: 100,
					JoinedAt:  time.Now().Add(-5 * time.Minute), // Earlier join time
					Status:    models.PlayerStatusJoined,
				},
				{
					UserID:    "user_2", 
					Username:  "Player2",
					Position:  1,
					IsReady:   true,
					BetAmount: 100,
					JoinedAt:  time.Now().Add(-3 * time.Minute),
					Status:    models.PlayerStatusJoined,
				},
				{
					UserID:    "user_1", // Duplicate of first player
					Username:  "Player1",
					Position:  2,
					IsReady:   false,
					BetAmount: 100,
					JoinedAt:  time.Now().Add(-1 * time.Minute), // Later join time
					Status:    models.PlayerStatusJoined,
				},
			},
		}

		// Run duplicate cleanup
		uniquePlayers, duplicateUserIDs := service.findDuplicatePlayers(room)

		// Verify only the actual duplicate is identified
		assert.Equal(t, 1, len(duplicateUserIDs), "Should find exactly one duplicate user")
		assert.Equal(t, 2, len(uniquePlayers), "Should have 2 unique players")
		assert.Contains(t, duplicateUserIDs, "user_1", "user_1 should be marked as duplicate")

		// Verify the non-duplicate player is preserved
		assert.Contains(t, uniquePlayers, "user_2", "user_2 should be preserved as unique")

		// Verify the earlier instance of the duplicate player is kept
		duplicatePlayer := uniquePlayers["user_1"]
		assert.Equal(t, 0, duplicatePlayer.Position, "Should keep the earlier instance (position 0)")
		assert.True(t, duplicatePlayer.JoinedAt.Before(time.Now().Add(-4*time.Minute)), 
			"Should keep the player with earlier join time")
	})

	t.Run("position calculation should work correctly for new players", func(t *testing.T) {
		// Test various scenarios for position calculation
		testCases := []struct {
			name             string
			existingPlayers  []models.RoomPlayer
			expectedPosition int
		}{
			{
				name:             "empty room should assign position 0",
				existingPlayers:  []models.RoomPlayer{},
				expectedPosition: 0,
			},
			{
				name: "room with sequential positions should assign next position",
				existingPlayers: []models.RoomPlayer{
					{UserID: "user1", Position: 0},
					{UserID: "user2", Position: 1},
				},
				expectedPosition: 2,
			},
			{
				name: "room with gap should fill the gap",
				existingPlayers: []models.RoomPlayer{
					{UserID: "user1", Position: 0},
					{UserID: "user2", Position: 2},
				},
				expectedPosition: 1,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				position := service.calculateNextAvailablePosition(tc.existingPlayers)
				assert.Equal(t, tc.expectedPosition, position, 
					"Position calculation failed for case: %s", tc.name)
			})
		}
	})
}
