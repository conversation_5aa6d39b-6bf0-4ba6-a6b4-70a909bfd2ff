package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/redis"
)

// AtomicRoomService provides thread-safe room operations with duplicate prevention
type AtomicRoomService struct {
	roomRepo    repositories.RoomRepository
	redisClient *redis.RedisClient
	logger      *logrus.Logger

	// Mutex for room-level operations to prevent race conditions
	roomMutexes sync.Map // map[string]*sync.RWMutex
}

// NewAtomicRoomService creates a new atomic room service
func NewAtomicRoomService(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	logger *logrus.Logger,
) *AtomicRoomService {
	return &AtomicRoomService{
		roomRepo:    roomRepo,
		redisClient: redisClient,
		logger:      logger,
		roomMutexes: sync.Map{},
	}
}

// getRoomMutex gets or creates a mutex for a specific room
func (s *AtomicRoomService) getRoomMutex(roomID string) *sync.RWMutex {
	if mutex, exists := s.roomMutexes.Load(roomID); exists {
		return mutex.(*sync.RWMutex)
	}

	newMutex := &sync.RWMutex{}
	actual, _ := s.roomMutexes.LoadOrStore(roomID, newMutex)
	return actual.(*sync.RWMutex)
}

// AtomicJoinRoom performs atomic room join with duplicate prevention
func (s *AtomicRoomService) AtomicJoinRoom(ctx context.Context, request models.JoinRoomRequest, username string) (*models.Room, error) {
	roomMutex := s.getRoomMutex(request.RoomID)
	roomMutex.Lock()
	defer roomMutex.Unlock()

	s.logger.WithFields(logrus.Fields{
		"room_id":  request.RoomID,
		"user_id":  request.UserID,
		"username": username,
	}).Info("Starting atomic room join")

	// Step 1: Validate room ID format
	objectID, err := primitive.ObjectIDFromHex(request.RoomID)
	if err != nil {
		return nil, models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": request.RoomID,
		})
	}

	// Step 2: Get current room state with read lock on database
	room, err := s.roomRepo.GetRoom(ctx, request.RoomID)
	if err != nil {
		return nil, err
	}

	// Step 3: Comprehensive validation
	if err := s.validateJoinRequest(room, request); err != nil {
		return nil, err
	}

	// Step 4: Check for duplicate player (critical check)
	if room.IsPlayerInRoom(request.UserID) {
		s.logger.WithFields(logrus.Fields{
			"room_id": request.RoomID,
			"user_id": request.UserID,
		}).Warn("Player already in room - returning current state")

		// Return current room state for reconnection
		return room, nil
	}

	// Step 5: Calculate next available position
	nextPosition := s.calculateNextPosition(room.Players)

	// Step 6: Create new player with atomic timestamp
	newPlayer := models.RoomPlayer{
		UserID:    request.UserID,
		Username:  username,
		Position:  nextPosition,
		IsReady:   false,
		BetAmount: request.BetAmount,
		JoinedAt:  time.Now(),
		Status:    models.PlayerStatusJoined,
	}

	// Step 7: Perform atomic database update
	err = s.atomicAddPlayer(ctx, objectID, newPlayer)
	if err != nil {
		return nil, err
	}

	// Step 8: Invalidate cache
	s.invalidateRoomCache(ctx, request.RoomID)

	// Step 9: Get updated room state
	updatedRoom, err := s.roomRepo.GetRoom(ctx, request.RoomID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated room after join")
		return nil, err
	}

	s.logger.WithFields(logrus.Fields{
		"room_id":      request.RoomID,
		"user_id":      request.UserID,
		"position":     nextPosition,
		"player_count": updatedRoom.CurrentPlayers,
	}).Info("Player successfully joined room")

	return updatedRoom, nil
}

// validateJoinRequest performs comprehensive join validation
func (s *AtomicRoomService) validateJoinRequest(room *models.Room, request models.JoinRoomRequest) error {
	// Check room status
	if room.Status != models.RoomStatusWaiting {
		return models.NewRoomError(
			models.ErrorCodeInvalidRoomStatus,
			"room is not accepting new players",
			request.RoomID,
		)
	}

	// Check room capacity
	if room.CurrentPlayers >= room.MaxPlayers {
		return models.NewRoomError(
			models.ErrorCodeRoomFull,
			"room is full",
			request.RoomID,
		)
	}

	// Check password for private rooms
	if room.Configuration.IsPrivate && room.Configuration.Password != request.Password {
		return models.NewRoomError(
			models.ErrorCodeInvalidRoomPassword,
			"invalid room password",
			request.RoomID,
		)
	}

	// Validate bet amount
	if request.BetAmount < room.Configuration.BetLimits.MinBet ||
		request.BetAmount > room.Configuration.BetLimits.MaxBet {
		return models.NewValidationError("bet amount outside allowed limits", map[string]interface{}{
			"bet_amount": request.BetAmount,
			"min_bet":    room.Configuration.BetLimits.MinBet,
			"max_bet":    room.Configuration.BetLimits.MaxBet,
		})
	}

	return nil
}

// calculateNextPosition finds the next available position
func (s *AtomicRoomService) calculateNextPosition(players []models.RoomPlayer) int {
	if len(players) == 0 {
		return 0
	}

	// Get all used positions
	usedPositions := make(map[int]bool)
	for _, player := range players {
		usedPositions[player.Position] = true
	}

	// Find first available position starting from 0
	position := 0
	for usedPositions[position] {
		position++
	}

	return position
}

// atomicAddPlayer performs atomic player addition to database
func (s *AtomicRoomService) atomicAddPlayer(ctx context.Context, roomID primitive.ObjectID, player models.RoomPlayer) error {
	// Use repository's transaction support
	return s.roomRepo.WithTransaction(ctx, func(txCtx context.Context) error {
		// Get current room state within transaction
		room, err := s.roomRepo.GetRoom(txCtx, roomID.Hex())
		if err != nil {
			return models.NewDatabaseError("failed to get room in transaction", err)
		}

		// Final duplicate check within transaction
		if room.IsPlayerInRoom(player.UserID) {
			return models.NewRoomError(
				models.ErrorCodePlayerInRoom,
				"player already in room",
				roomID.Hex(),
			)
		}

		// Final capacity check within transaction
		if room.CurrentPlayers >= room.MaxPlayers {
			return models.NewRoomError(
				models.ErrorCodeRoomFull,
				"room is full",
				roomID.Hex(),
			)
		}

		// Add player to room using repository method
		err = s.roomRepo.AddPlayerToRoom(txCtx, roomID.Hex(), player)
		if err != nil {
			return models.NewDatabaseError("failed to add player to room", err)
		}

		return nil
	})
}

// AtomicRemovePlayer removes a player and reassigns positions
func (s *AtomicRoomService) AtomicRemovePlayer(ctx context.Context, roomID, userID string) (*models.Room, error) {
	roomMutex := s.getRoomMutex(roomID)
	roomMutex.Lock()
	defer roomMutex.Unlock()

	s.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"user_id": userID,
	}).Info("Starting atomic player removal")

	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return nil, models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	// Get current room state
	room, err := s.roomRepo.GetRoom(ctx, roomID)
	if err != nil {
		return nil, err
	}

	// Check if player exists
	if !room.IsPlayerInRoom(userID) {
		return nil, models.NewRoomError(
			models.ErrorCodePlayerNotInRoom,
			"player not in room",
			roomID,
		)
	}

	// Perform atomic removal and position reassignment
	err = s.atomicRemoveAndReassign(ctx, objectID, userID)
	if err != nil {
		return nil, err
	}

	// Invalidate cache
	s.invalidateRoomCache(ctx, roomID)

	// Get updated room state
	updatedRoom, err := s.roomRepo.GetRoom(ctx, roomID)
	if err != nil {
		return nil, err
	}

	s.logger.WithFields(logrus.Fields{
		"room_id":      roomID,
		"user_id":      userID,
		"player_count": updatedRoom.CurrentPlayers,
	}).Info("Player successfully removed and positions reassigned")

	return updatedRoom, nil
}

// atomicRemoveAndReassign removes player and reassigns positions atomically
func (s *AtomicRoomService) atomicRemoveAndReassign(ctx context.Context, roomID primitive.ObjectID, userID string) error {
	return s.roomRepo.WithTransaction(ctx, func(txCtx context.Context) error {
		// Remove player from room
		err := s.roomRepo.RemovePlayerFromRoom(txCtx, roomID.Hex(), userID)
		if err != nil {
			return models.NewDatabaseError("failed to remove player from room", err)
		}

		// Get updated room state to reassign positions
		updatedRoom, err := s.roomRepo.GetRoom(txCtx, roomID.Hex())
		if err != nil {
			return models.NewDatabaseError("failed to get updated room", err)
		}

		// Reassign positions sequentially starting from 0
		for i, player := range updatedRoom.Players {
			newPosition := i
			if player.Position != newPosition {
				err = s.roomRepo.UpdatePlayerInRoom(txCtx, roomID.Hex(), player.UserID, map[string]interface{}{
					"position": newPosition,
				})
				if err != nil {
					return models.NewDatabaseError("failed to update player position", err)
				}
			}
		}

		return nil
	})
}

// invalidateRoomCache invalidates all cache entries for a room
func (s *AtomicRoomService) invalidateRoomCache(ctx context.Context, roomID string) {
	cacheKeys := []string{
		fmt.Sprintf("room:state:%s", roomID),
		fmt.Sprintf("room:players:%s", roomID),
		fmt.Sprintf("room:config:%s", roomID),
	}

	for _, key := range cacheKeys {
		if err := s.redisClient.Delete(ctx, key); err != nil {
			s.logger.WithError(err).WithField("cache_key", key).Warn("Failed to invalidate cache")
		}
	}
}
