package services

import (
	"context"
	"time"

	"github.com/xzgame/game-service/internal/models"
)

// GameService defines the interface for game-related business logic
type GameService interface {
	// Game session management
	CreateGameSession(ctx context.Context, roomID string, config models.SessionConfiguration) (*models.GameSession, error)
	StartGame(ctx context.Context, sessionID string) error
	EndGame(ctx context.Context, sessionID string, results *models.GameResults) error
	CancelGame(ctx context.Context, sessionID string, reason string) error

	// Game state management
	GetGameState(ctx context.Context, sessionID string) (*models.GameState, error)
	UpdateGameState(ctx context.Context, sessionID string, gameData map[string]interface{}) error

	// Player management in games
	AddPlayerToGame(ctx context.Context, sessionID string, player models.SessionPlayer) error
	RemovePlayerFromGame(ctx context.Context, sessionID string, userID string) error
	UpdatePlayerStatus(ctx context.Context, sessionID string, userID string, status models.PlayerStatus) error

	// Game execution
	ExecuteGameLogic(ctx context.Context, sessionID string, gameType models.GameType) (*models.GameResults, error)
	GenerateGameResults(ctx context.Context, sessionID string) (*models.GameResults, error)
	CalculatePayouts(ctx context.Context, sessionID string, results *models.GameResults) (*models.PayoutCalculation, error)

	// Game history and statistics
	GetGameHistory(ctx context.Context, userID string, gameType *models.GameType, page, limit int) ([]*models.GameHistory, error)
	GetPlayerGameStats(ctx context.Context, userID string) (*PlayerGameStats, error)

	// Cleanup and maintenance
	CleanupExpiredSessions(ctx context.Context) error
	ArchiveCompletedSessions(ctx context.Context, olderThan time.Time) error

	// Additional methods for orchestration
	GetGameSession(ctx context.Context, sessionID string) (*models.GameSession, error)
	ProcessPayouts(ctx context.Context, sessionID string, payouts *models.PayoutCalculation) error

	// Health check and monitoring
	HealthCheck(ctx context.Context) error
	GetServiceStats(ctx context.Context) (*ServiceStats, error)
	GetOrchestratorStats() *GameOrchestratorStats
}

// RoomService defines the interface for room-related business logic
type RoomService interface {
	// Room management
	CreateRoom(ctx context.Context, request models.CreateRoomRequest) (*models.Room, error)
	GetRoom(ctx context.Context, roomID string) (*models.Room, error)
	GetRoomFresh(ctx context.Context, roomID string) (*models.Room, error) // Bypasses cache
	UpdateRoom(ctx context.Context, roomID string, updates models.UpdateRoomRequest) (*models.Room, error)
	DeleteRoom(ctx context.Context, roomID string, adminUserID string) error

	// Room listing and filtering
	ListRooms(ctx context.Context, filter models.RoomListFilter) (*models.RoomListResponse, error)
	GetAvailableRooms(ctx context.Context, gameType models.GameType) ([]*models.Room, error)
	GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error)

	// Player management in rooms
	JoinRoom(ctx context.Context, request models.JoinRoomRequest) (*models.Room, error)
	JoinRoomWithUsername(ctx context.Context, request models.JoinRoomRequest, username string) (*models.Room, error)
	LeaveRoom(ctx context.Context, request models.LeaveRoomRequest) error
	RemovePlayerFromCache(ctx context.Context, roomID, userID string) error
	SetPlayerReady(ctx context.Context, request models.PlayerReadyRequest) (*models.Room, error)

	// Room state management
	UpdateRoomStatus(ctx context.Context, roomID string, status models.RoomStatus) error
	CheckRoomReadiness(ctx context.Context, roomID string) (bool, error)
	StartGameInRoom(ctx context.Context, roomID string) (*models.GameSession, error)
	GetPlayerColorSelections(ctx context.Context, roomID string) (map[string]string, error)

	// Room statistics and activity
	GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error)
	GetRoomActivity(ctx context.Context, roomID string, since time.Time) (*models.RoomActivity, error)

	// Room events and notifications
	BroadcastRoomEvent(ctx context.Context, roomID string, event models.RoomEvent) error
	NotifyPlayersInRoom(ctx context.Context, roomID string, message interface{}) error
	PublishRoomStateUpdate(ctx context.Context, roomID string, room *models.Room) error
	PublishJoinEventsForPlayer(ctx context.Context, roomID, userID, username string) error
}

// PlayerService defines the interface for player-related business logic
type PlayerService interface {
	// Player session management
	CreatePlayerSession(ctx context.Context, userID string, roomID string) error
	GetPlayerSession(ctx context.Context, userID string) (*PlayerSession, error)
	UpdatePlayerSession(ctx context.Context, userID string, updates map[string]interface{}) error
	DeletePlayerSession(ctx context.Context, userID string) error

	// Player status and activity
	GetPlayerStatus(ctx context.Context, userID string) (*PlayerStatus, error)
	UpdatePlayerActivity(ctx context.Context, userID string, activity PlayerActivity) error
	GetPlayerActivity(ctx context.Context, userID string, since time.Time) ([]*PlayerActivity, error)

	// Player validation and authorization
	ValidatePlayerBalance(ctx context.Context, userID string, amount int64) error
	AuthorizePlayerAction(ctx context.Context, userID string, action string, context map[string]interface{}) error

	// Player cleanup
	CleanupInactivePlayers(ctx context.Context, inactiveSince time.Time) error
}

// ConfigService defines the interface for game configuration management
type ConfigService interface {
	// Game configuration management
	GetGameConfiguration(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error)
	UpdateGameConfiguration(ctx context.Context, gameType models.GameType, config *models.GameConfiguration) error
	GetActiveGameConfiguration(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error)

	// Configuration validation
	ValidateGameConfiguration(ctx context.Context, config *models.GameConfiguration) error

	// Runtime settings
	GetRuntimeSettings(ctx context.Context) (*RuntimeSettings, error)
	UpdateRuntimeSettings(ctx context.Context, settings *RuntimeSettings) error
	IsGameTypeEnabled(ctx context.Context, gameType models.GameType) (bool, error)

	// Configuration caching
	RefreshConfigurationCache(ctx context.Context) error
	InvalidateConfigurationCache(ctx context.Context, gameType models.GameType) error
}

// RandomService defines the interface for random number generation and fairness
type RandomService interface {
	// Random number generation
	GenerateSecureRandom(ctx context.Context, max int) (int, error)
	GenerateRandomSeed(ctx context.Context) (string, error)
	GenerateRandomWithSeed(ctx context.Context, seed string, max int) (int, error)

	// Fairness proof generation
	GenerateFairnessProof(ctx context.Context, seed string, algorithm string) (*models.FairnessProof, error)
	VerifyFairnessProof(ctx context.Context, proof *models.FairnessProof) (bool, error)

	// Game-specific random generation
	GeneratePrizeWheelResult(ctx context.Context, playerCount int, seed string) (*PrizeWheelResult, error)
	GenerateAmidakujiPattern(ctx context.Context, playerCount int, seed string) (*AmidakujiPattern, error)
}

// LobbyService defines the interface for lobby-related operations
type LobbyService interface {
	// Room discovery and listing
	GetActiveRooms(ctx context.Context, filter LobbyRoomFilter) (*LobbyRoomListResponse, error)
	GetRoomsByGameType(ctx context.Context, gameType models.GameType, limit int) ([]*LobbyRoomInfo, error)
	GetFeaturedRooms(ctx context.Context, limit int) ([]*LobbyRoomInfo, error)
	SearchRooms(ctx context.Context, query string, filter LobbyRoomFilter) ([]*LobbyRoomInfo, error)

	// Subscription management
	HandleLobbySubscription(ctx context.Context, request LobbySubscriptionRequest) (*LobbySubscriptionResponse, error)
	HandleLobbyUnsubscription(ctx context.Context, userID string) error

	// Real-time updates
	PublishRoomCreated(ctx context.Context, room *models.Room) error
	PublishRoomUpdated(ctx context.Context, room *models.Room) error
	PublishRoomDeleted(ctx context.Context, roomID string, room *models.Room) error
	PublishPlayerCountChanged(ctx context.Context, room *models.Room) error
	PublishLobbyUpdate(ctx context.Context, update LobbyUpdate) error

	// Statistics and analytics
	GetLobbyStats(ctx context.Context) (*LobbyStats, error)
	GetRoomPopularityStats(ctx context.Context, gameType *models.GameType) ([]*RoomPopularityInfo, error)

	// Cache management
	RefreshRoomListCache(ctx context.Context) error
	InvalidateRoomCache(ctx context.Context, roomID string) error
}

// EventService defines the interface for event publishing and handling
type EventService interface {
	// Event publishing
	PublishGameEvent(ctx context.Context, event GameEvent) error
	PublishRoomEvent(ctx context.Context, event models.RoomEvent) error
	PublishPlayerEvent(ctx context.Context, event PlayerEvent) error

	// Event subscription management
	SubscribeToGameEvents(ctx context.Context, sessionID string, handler GameEventHandler) error
	SubscribeToRoomEvents(ctx context.Context, roomID string, handler RoomEventHandler) error
	UnsubscribeFromEvents(ctx context.Context, subscriptionID string) error

	// Broadcast operations
	BroadcastToRoom(ctx context.Context, roomID string, message interface{}) error
	BroadcastToSession(ctx context.Context, sessionID string, message interface{}) error
	BroadcastGlobal(ctx context.Context, message interface{}) error
}

// Additional types for service operations

// PlayerSession represents a player's current session state
type PlayerSession struct {
	UserID       string    `json:"user_id"`
	SessionID    string    `json:"session_id"`
	RoomID       string    `json:"room_id"`
	Status       string    `json:"status"`
	LastPing     time.Time `json:"last_ping"`
	ConnectionID string    `json:"connection_id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// PlayerStatus represents a player's current status
type PlayerStatus struct {
	UserID           string    `json:"user_id"`
	CurrentRoomID    *string   `json:"current_room_id,omitempty"`
	CurrentSessionID *string   `json:"current_session_id,omitempty"`
	Status           string    `json:"status"`
	LastActivity     time.Time `json:"last_activity"`
	IsOnline         bool      `json:"is_online"`
}

// PlayerActivity represents a player's activity record
type PlayerActivity struct {
	UserID       string                 `json:"user_id"`
	ActivityType string                 `json:"activity_type"`
	RoomID       *string                `json:"room_id,omitempty"`
	SessionID    *string                `json:"session_id,omitempty"`
	Data         map[string]interface{} `json:"data,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
}

// PlayerGameStats represents a player's game statistics
type PlayerGameStats struct {
	UserID           string          `json:"user_id"`
	TotalGames       int             `json:"total_games"`
	GamesWon         int             `json:"games_won"`
	GamesLost        int             `json:"games_lost"`
	TotalBetAmount   int64           `json:"total_bet_amount"`
	TotalWinAmount   int64           `json:"total_win_amount"`
	AverageGameTime  time.Duration   `json:"average_game_time"`
	FavoriteGameType models.GameType `json:"favorite_game_type"`
	LastGameAt       *time.Time      `json:"last_game_at,omitempty"`
}

// RuntimeSettings represents runtime configuration settings
type RuntimeSettings struct {
	MaxConcurrentGames int                   `json:"max_concurrent_games"`
	MaintenanceMode    bool                  `json:"maintenance_mode"`
	GameTypeEnabled    map[string]bool       `json:"game_type_enabled"`
	GlobalBetLimits    models.BetLimitConfig `json:"global_bet_limits"`
	UpdatedAt          time.Time             `json:"updated_at"`
}

// Game-specific result types

// PrizeWheelResult represents the result of a prize wheel game
type PrizeWheelResult struct {
	WinningPosition int                    `json:"winning_position"`
	WinnerUserID    string                 `json:"winner_user_id"`
	SpinData        map[string]interface{} `json:"spin_data"`
	AnimationData   map[string]interface{} `json:"animation_data"`
}

// AmidakujiPattern represents the pattern for an Amidakuji game
type AmidakujiPattern struct {
	PlayerCount   int                    `json:"player_count"`
	Pattern       [][]bool               `json:"pattern"`
	WinnerPath    []int                  `json:"winner_path"`
	WinnerUserID  string                 `json:"winner_user_id"`
	AnimationData map[string]interface{} `json:"animation_data"`
}

// Event types and handlers

// GameEvent represents a game-related event
type GameEvent struct {
	Type      string                 `json:"type"`
	SessionID string                 `json:"session_id"`
	UserID    string                 `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// PlayerEvent represents a player-related event
type PlayerEvent struct {
	Type      string                 `json:"type"`
	UserID    string                 `json:"user_id"`
	RoomID    string                 `json:"room_id,omitempty"`
	SessionID string                 `json:"session_id,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// ManagerClient interface for interacting with Manager Service
type ManagerClient interface {
	ValidatePlayerBalance(ctx context.Context, userID string, betAmount int64) (bool, error)
	GetPlayerBalance(ctx context.Context, userID string) (float64, error)
	NotifyPlayerKicked(ctx context.Context, roomID, userID, reason string) error
	GetGameConfiguration(ctx context.Context) (*GameConfiguration, error)
}

// GameConfigurationClient represents a client that can get game configuration
type GameConfigurationClient interface {
	GetGameConfiguration(ctx context.Context) (*GameConfiguration, error)
}

// GameConfiguration represents game configuration from Manager Service
type GameConfiguration struct {
	StartingDuration int `json:"starting_duration"` // in seconds
	PlayingDuration  int `json:"playing_duration"`  // in seconds
	EndDuration      int `json:"end_duration"`      // in seconds
}

// Lobby-related types

// LobbyRoomFilter represents filtering options for lobby room queries
type LobbyRoomFilter struct {
	GameType   *models.GameType   `json:"game_type,omitempty"`
	Status     *models.RoomStatus `json:"status,omitempty"`
	HasSpace   bool               `json:"has_space"`
	MinPlayers *int               `json:"min_players,omitempty"`
	MaxPlayers *int               `json:"max_players,omitempty"`
	BetRange   *BetRange          `json:"bet_range,omitempty"`
	IsPrivate  *bool              `json:"is_private,omitempty"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	SortBy     string             `json:"sort_by"`    // "created_at", "player_count", "bet_amount"
	SortOrder  string             `json:"sort_order"` // "asc", "desc"
}

// BetRange represents a betting amount range filter
type BetRange struct {
	Min int64 `json:"min"`
	Max int64 `json:"max"`
}

// LobbyRoomInfo represents room information for lobby display
type LobbyRoomInfo struct {
	ID           string    `json:"id"`
	Name         string    `json:"name"`
	GameType     string    `json:"game_type"`
	Status       string    `json:"status"`
	PlayerCount  int       `json:"player_count"`
	MaxPlayers   int       `json:"max_players"`
	MinPlayers   int       `json:"min_players"`
	BetAmount    int64     `json:"bet_amount"`
	IsPrivate    bool      `json:"is_private"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	LastActivity time.Time `json:"last_activity"`
	HasSpace     bool      `json:"has_space"`
	IsFeatured   bool      `json:"is_featured"`
}

// LobbyRoomListResponse represents a paginated list of lobby rooms
type LobbyRoomListResponse struct {
	Rooms      []*LobbyRoomInfo `json:"rooms"`
	TotalCount int64            `json:"total_count"`
	Page       int              `json:"page"`
	Limit      int              `json:"limit"`
	HasMore    bool             `json:"has_more"`
}

// LobbySubscriptionRequest represents a lobby subscription request
type LobbySubscriptionRequest struct {
	UserID    string `json:"user_id"`
	Username  string `json:"username"`
	SocketID  string `json:"socket_id"`
	Timestamp string `json:"timestamp"`
}

// LobbySubscriptionResponse represents a lobby subscription response
type LobbySubscriptionResponse struct {
	Success   bool             `json:"success"`
	Rooms     []*LobbyRoomInfo `json:"rooms,omitempty"`
	Error     string           `json:"error,omitempty"`
	Timestamp string           `json:"timestamp"`
}

// LobbyUpdate represents a lobby update event
type LobbyUpdate struct {
	Type      string           `json:"type"` // "room_created", "room_updated", "room_deleted", "player_count_changed"
	Action    string           `json:"action"`
	Room      *LobbyRoomInfo   `json:"room,omitempty"`
	Rooms     []*LobbyRoomInfo `json:"rooms,omitempty"`
	UserID    string           `json:"user_id,omitempty"`
	SocketID  string           `json:"socket_id,omitempty"`
	Timestamp time.Time        `json:"timestamp"`
}

// LobbyStats represents lobby statistics
type LobbyStats struct {
	TotalRooms       int            `json:"total_rooms"`
	ActiveRooms      int            `json:"active_rooms"`
	WaitingRooms     int            `json:"waiting_rooms"`
	TotalPlayers     int            `json:"total_players"`
	RoomsByGameType  map[string]int `json:"rooms_by_game_type"`
	AverageWaitTime  time.Duration  `json:"average_wait_time"`
	PopularGameTypes []string       `json:"popular_game_types"`
	PeakHours        []int          `json:"peak_hours"`
	LastUpdated      time.Time      `json:"last_updated"`
}

// RoomPopularityInfo represents room popularity statistics
type RoomPopularityInfo struct {
	RoomID       string        `json:"room_id"`
	RoomName     string        `json:"room_name"`
	GameType     string        `json:"game_type"`
	JoinCount    int           `json:"join_count"`
	AverageStay  time.Duration `json:"average_stay"`
	Rating       float64       `json:"rating"`
	LastActivity time.Time     `json:"last_activity"`
}

// Event handler function types
type GameEventHandler func(event GameEvent) error
type RoomEventHandler func(event models.RoomEvent) error
type PlayerEventHandler func(event PlayerEvent) error
