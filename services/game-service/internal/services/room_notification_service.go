package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/redis"
)

// RoomNotificationService handles real-time room notifications
type RoomNotificationService struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewRoomNotificationService creates a new room notification service
func NewRoomNotificationService(
	redisClient *redis.RedisClient,
	logger *logrus.Logger,
) *RoomNotificationService {
	return &RoomNotificationService{
		redisClient: redisClient,
		logger:      logger,
	}
}

// RoomInfoUpdatedEvent represents the comprehensive room update event
type RoomInfoUpdatedEvent struct {
	Room             RoomData               `json:"room"`
	RoomState        RoomStateData          `json:"roomState"`
	Players          []PlayerData           `json:"players"`
	GameConfig       GameConfigData         `json:"gameConfig"`
	GameSpecificData map[string]interface{} `json:"gameSpecificData"`
	Timestamp        string                 `json:"timestamp"`
}

// RoomData represents room information
type RoomData struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	GameType    string `json:"gameType"`
	Status      string `json:"status"`
	PlayerCount int    `json:"playerCount"`
	MaxPlayers  int    `json:"maxPlayers"`
	BetAmount   int64  `json:"betAmount"`
	PrizePool   int64  `json:"prizePool"`
	IsPrivate   bool   `json:"isPrivate"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// RoomStateData represents current room state
type RoomStateData struct {
	PlayerCount    int   `json:"playerCount"`
	ReadyCount     int   `json:"readyCount"`
	CanStartGame   bool  `json:"canStartGame"`
	PrizePool      int64 `json:"prizePool"`
	GameInProgress bool  `json:"gameInProgress"`
	Countdown      *int  `json:"countdown"`
}

// PlayerData represents player information
type PlayerData struct {
	BetAmount int64  `json:"betAmount"`
	IsReady   bool   `json:"isReady"`
	JoinedAt  string `json:"joinedAt"`
	Position  int    `json:"position"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
}

// GameConfigData represents game configuration
type GameConfigData struct {
	BetAmount  int64             `json:"betAmount"`
	GameType   string            `json:"gameType"`
	MaxPlayers int               `json:"maxPlayers"`
	MinPlayers int               `json:"minPlayers"`
	Settings   map[string]string `json:"settings"`
}

// NotifyRoomInfoUpdated - DISABLED - All room events are now disabled to prevent duplicate events
func (s *RoomNotificationService) NotifyRoomInfoUpdated(ctx context.Context, room *models.Room) error {
	// DISABLED: All room_info_updated events are now disabled
	// But we still build the event structure for future use
	_ = s.buildRoomInfoUpdatedEvent(room) // Use the helper function

	s.logger.WithFields(logrus.Fields{
		"room_id":      room.ID,
		"player_count": room.CurrentPlayers,
		"status":       room.Status,
		"message":      "NotifyRoomInfoUpdated disabled - preventing duplicate events",
	}).Debug("Skipped sending room info updated notification")

	return nil
}

// NotifyPlayerJoined - DISABLED - All player events are now disabled to prevent duplicate events
func (s *RoomNotificationService) NotifyPlayerJoined(ctx context.Context, room *models.Room, player *models.RoomPlayer) error {
	// DISABLED: All player_joined events are now disabled
	s.logger.WithFields(logrus.Fields{
		"room_id":  room.ID,
		"user_id":  player.UserID,
		"username": player.Username,
		"position": player.Position,
		"message":  "NotifyPlayerJoined disabled - preventing duplicate events",
	}).Debug("Skipped sending player joined notification")

	return nil
}

// NotifyPlayerLeft sends player left notification
func (s *RoomNotificationService) NotifyPlayerLeft(ctx context.Context, room *models.Room, userID string) error {
	s.logger.WithFields(logrus.Fields{
		"room_id": room.ID,
		"user_id": userID,
	}).Info("Sending player left notification")

	// Send comprehensive room update
	if err := s.NotifyRoomInfoUpdated(ctx, room); err != nil {
		return err
	}

	// Build lobby update event for room changes
	_ = s.buildLobbyUpdateEvent(room, "player_left") // Use the helper function

	// Send specific player left event
	playerLeftEvent := map[string]interface{}{
		"event": "player_left",
		"data": map[string]interface{}{
			"roomId":    room.ID.Hex(),
			"userId":    userID,
			"roomState": s.buildRoomStateData(room),
		},
		"timestamp": time.Now().Format(time.RFC3339),
	}

	eventData, err := json.Marshal(playerLeftEvent)
	if err != nil {
		return err
	}

	roomChannel := fmt.Sprintf("room:%s:events", room.ID.Hex())
	return s.redisClient.Publish(ctx, roomChannel, string(eventData))
}

// buildRoomInfoUpdatedEvent creates comprehensive room update event
func (s *RoomNotificationService) buildRoomInfoUpdatedEvent(room *models.Room) RoomInfoUpdatedEvent {
	// Get game-specific data based on game type
	gameSpecificData := s.buildGameSpecificData(room)

	return RoomInfoUpdatedEvent{
		Room:             s.buildRoomData(room),
		RoomState:        s.buildRoomStateData(room),
		Players:          s.buildPlayersData(room.Players),
		GameConfig:       s.buildGameConfigData(room),
		GameSpecificData: gameSpecificData,
		Timestamp:        time.Now().Format(time.RFC3339),
	}
}

// buildGameSpecificData creates game-specific data based on game type
func (s *RoomNotificationService) buildGameSpecificData(room *models.Room) map[string]interface{} {
	gameSpecificData := make(map[string]interface{})

	// Add game type identifier
	gameSpecificData["gameType"] = string(room.GameType)

	switch room.GameType {
	case models.GameTypePrizeWheel:
		// Get Prize Wheel specific data
		prizeWheelData := s.buildPrizeWheelSpecificData(room)
		for key, value := range prizeWheelData {
			gameSpecificData[key] = value
		}
	default:
		// For unknown game types, return basic structure
		s.logger.WithFields(logrus.Fields{
			"room_id":   room.ID,
			"game_type": room.GameType,
		}).Debug("No specific data handler for game type")
	}

	return gameSpecificData
}

// buildPrizeWheelSpecificData creates Prize Wheel specific data
func (s *RoomNotificationService) buildPrizeWheelSpecificData(room *models.Room) map[string]interface{} {
	// Define available colors for Prize Wheel
	allColors := []string{"red", "blue", "green", "yellow", "purple", "orange", "pink", "teal"}

	// For now, return basic structure - in a real implementation, this would fetch from Redis
	// TODO: Integrate with Redis color selection cache
	colorSelections := make(map[string]string)
	playerColorMappings := make(map[string]interface{})

	// Calculate available colors (all colors for now since we don't have Redis integration here)
	availableColors := allColors

	// Build Prize Wheel specific data structure
	prizeWheelData := map[string]interface{}{
		"colorSelections":          colorSelections,
		"availableColors":          availableColors,
		"playerColorMappings":      playerColorMappings,
		"colorSelectionTimestamps": make(map[string]string), // Placeholder for future implementation
	}

	s.logger.WithFields(logrus.Fields{
		"room_id":                room.ID,
		"available_colors_count": len(availableColors),
	}).Debug("Built Prize Wheel specific data")

	return prizeWheelData
}

// buildRoomData converts room model to room data
func (s *RoomNotificationService) buildRoomData(room *models.Room) RoomData {
	actualPlayerCount := len(room.Players) // Use actual player count from array
	return RoomData{
		ID:          room.ID.Hex(),
		Name:        room.Name,
		GameType:    string(room.GameType),
		Status:      string(room.Status),
		PlayerCount: actualPlayerCount, // FIXED: Use actual player count instead of room.CurrentPlayers
		MaxPlayers:  room.MaxPlayers,
		BetAmount:   room.Configuration.BetLimits.MinBet, // Use min bet as base bet amount
		PrizePool:   s.calculatePrizePool(room),
		IsPrivate:   room.Configuration.IsPrivate,
		CreatedAt:   room.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   room.UpdatedAt.Format(time.RFC3339),
	}
}

// buildRoomStateData creates room state information
func (s *RoomNotificationService) buildRoomStateData(room *models.Room) RoomStateData {
	readyCount := 0
	actualPlayerCount := len(room.Players) // Use actual player count from array
	for _, player := range room.Players {
		if player.IsReady {
			readyCount++
		}
	}

	return RoomStateData{
		PlayerCount:    actualPlayerCount, // FIXED: Use actual player count instead of room.CurrentPlayers
		ReadyCount:     readyCount,
		CanStartGame:   room.CanStartGame(),
		PrizePool:      s.calculatePrizePool(room),
		GameInProgress: room.Status == models.RoomStatusActive,
		Countdown:      nil, // TODO: Implement countdown logic
	}
}

// buildPlayersData converts player models to player data
func (s *RoomNotificationService) buildPlayersData(players []models.RoomPlayer) []PlayerData {
	var playersData []PlayerData
	for _, player := range players {
		playersData = append(playersData, s.convertPlayerData(&player))
	}
	return playersData
}

// convertPlayerData converts a single player model to player data
func (s *RoomNotificationService) convertPlayerData(player *models.RoomPlayer) PlayerData {
	return PlayerData{
		BetAmount: player.BetAmount,
		IsReady:   player.IsReady,
		JoinedAt:  player.JoinedAt.Format(time.RFC3339),
		Position:  player.Position,
		UserID:    player.UserID,
		Username:  player.Username,
	}
}

// buildGameConfigData creates game configuration data
func (s *RoomNotificationService) buildGameConfigData(room *models.Room) GameConfigData {
	// Convert map[string]interface{} to map[string]string
	settings := make(map[string]string)
	for key, value := range room.Configuration.GameSpecific {
		if strValue, ok := value.(string); ok {
			settings[key] = strValue
		} else {
			settings[key] = fmt.Sprintf("%v", value)
		}
	}

	return GameConfigData{
		BetAmount:  room.Configuration.BetLimits.MinBet,
		GameType:   string(room.GameType),
		MaxPlayers: room.MaxPlayers,
		MinPlayers: room.MinPlayers,
		Settings:   settings,
	}
}

// calculatePrizePool calculates current prize pool
func (s *RoomNotificationService) calculatePrizePool(room *models.Room) int64 {
	var prizePool int64
	for _, player := range room.Players {
		if player.IsReady {
			prizePool += player.BetAmount
		}
	}
	return prizePool
}

// buildLobbyUpdateEvent creates lobby update event
func (s *RoomNotificationService) buildLobbyUpdateEvent(room *models.Room, action string) map[string]interface{} {
	return map[string]interface{}{
		"event": map[string]interface{}{
			"type": "room_list_updated",
			"payload": map[string]interface{}{
				"action": action,
				"room":   s.buildRoomData(room),
			},
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}
}
