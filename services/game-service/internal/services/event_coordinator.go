package services

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/models"
)

// EventCoordinator coordinates event publishing across services
type EventCoordinator struct {
	orchestrator *GameOrchestrator
	logger       *logrus.Logger
}

// NewEventCoordinator creates a new event coordinator
func NewEventCoordinator(orchestrator *GameOrchestrator) *EventCoordinator {
	return &EventCoordinator{
		orchestrator: orchestrator,
		logger:       orchestrator.logger,
	}
}

// PublishSessionCreated publishes a session created event
func (ec *EventCoordinator) PublishSessionCreated(ctx context.Context, session *models.GameSession) error {
	ec.logger.WithField("session_id", session.ID.Hex()).Info("Publishing session created event")

	event := &models.GameEventRequest{
		SessionID: session.ID.Hex(),
		RoomID:    session.RoomID,
		EventType: "session_created",
		GameType:  string(session.GameType),
		Data: map[string]interface{}{
			"room_id":    session.RoomID,
			"game_type":  session.GameType,
			"status":     session.Status,
			"created_at": session.CreatedAt,
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish session created event")
		return err
	}

	ec.logger.WithField("session_id", session.ID.Hex()).Info("Session created event published successfully")
	return nil
}

// PublishGameCompleted publishes a game completed event
func (ec *EventCoordinator) PublishGameCompleted(ctx context.Context, sessionID string, results *models.GameResults) error {
	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"winner":     results.WinnerUserID,
	}).Info("Publishing game completed event")

	event := &models.GameEventRequest{
		SessionID: sessionID,
		EventType: "game_completed",
		GameType:  "game", // Generic game type for completed events
		Data: map[string]interface{}{
			"winner_user_id": results.WinnerUserID,
			"total_bet_pool": results.TotalBetPool,
			"prize_pool":     results.PrizePool,
			"completed_at":   time.Now(),
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish game completed event")
		return err
	}

	ec.logger.WithField("session_id", sessionID).Info("Game completed event published successfully")
	return nil
}

// PublishGameCancelled publishes a game cancelled event
func (ec *EventCoordinator) PublishGameCancelled(ctx context.Context, sessionID string, reason string) error {
	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"reason":     reason,
	}).Info("Publishing game cancelled event")

	event := &models.GameEventRequest{
		SessionID: sessionID,
		EventType: "game_cancelled",
		GameType:  "game", // Generic game type for cancelled events
		Data: map[string]interface{}{
			"reason":       reason,
			"cancelled_at": time.Now(),
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish game cancelled event")
		return err
	}

	ec.logger.WithField("session_id", sessionID).Info("Game cancelled event published successfully")
	return nil
}

// PublishPlayerJoined publishes a player joined event
func (ec *EventCoordinator) PublishPlayerJoined(ctx context.Context, sessionID string, userID string) error {
	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Publishing player joined event")

	event := &models.GameEventRequest{
		SessionID: sessionID,
		EventType: "player_joined",
		GameType:  "game", // Generic game type for player events
		Data: map[string]interface{}{
			"user_id":   userID,
			"joined_at": time.Now(),
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish player joined event")
		return err
	}

	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Player joined event published successfully")

	return nil
}

// PublishPlayerLeft publishes a player left event
func (ec *EventCoordinator) PublishPlayerLeft(ctx context.Context, sessionID string, userID string) error {
	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Publishing player left event")

	event := &models.GameEventRequest{
		SessionID: sessionID,
		EventType: "player_left",
		GameType:  "game", // Generic game type for player events
		Data: map[string]interface{}{
			"user_id": userID,
			"left_at": time.Now(),
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish player left event")
		return err
	}

	ec.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Player left event published successfully")

	return nil
}

// PublishGameStateChanged publishes a game state changed event
func (ec *EventCoordinator) PublishGameStateChanged(ctx context.Context, sessionID string, gameState *models.GameState) error {
	ec.logger.WithField("session_id", sessionID).Info("Publishing game state changed event")

	event := &models.GameEventRequest{
		SessionID: sessionID,
		EventType: "game_state_changed",
		GameType:  "game", // Generic game type for state events
		Data: map[string]interface{}{
			"phase":        gameState.Phase,
			"game_data":    gameState.GameData,
			"last_updated": gameState.LastUpdated,
		},
	}

	err := ec.orchestrator.notificationClient.BroadcastGameEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish game state changed event")
		return err
	}

	ec.logger.WithField("session_id", sessionID).Info("Game state changed event published successfully")
	return nil
}

// PublishRoomEvent publishes a room-related event
func (ec *EventCoordinator) PublishRoomEvent(ctx context.Context, roomID string, eventType string, eventData map[string]interface{}) error {
	ec.logger.WithFields(logrus.Fields{
		"room_id":    roomID,
		"event_type": eventType,
	}).Info("Publishing room event")

	event := &models.RoomEventRequest{
		RoomID:    roomID,
		EventType: eventType,
		EventData: eventData,
	}

	err := ec.orchestrator.notificationClient.BroadcastRoomEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish room event")
		return err
	}

	ec.logger.WithFields(logrus.Fields{
		"room_id":    roomID,
		"event_type": eventType,
	}).Info("Room event published successfully")

	return nil
}

// PublishUserNotification publishes a user-specific notification
func (ec *EventCoordinator) PublishUserNotification(ctx context.Context, userIDs []string, notificationType string, data map[string]interface{}) error {
	ec.logger.WithFields(logrus.Fields{
		"user_count":        len(userIDs),
		"notification_type": notificationType,
	}).Info("Publishing user notification")

	event := &models.UserNotificationRequest{
		UserIDs:          userIDs,
		NotificationType: notificationType,
		Data:             data,
		// Priority removed - not in UserNotificationRequest model
	}

	err := ec.orchestrator.notificationClient.SendUserNotification(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish user notification")
		return err
	}

	ec.logger.WithFields(logrus.Fields{
		"user_count":        len(userIDs),
		"notification_type": notificationType,
	}).Info("User notification published successfully")

	return nil
}

// PublishSystemEvent publishes a system-wide event
func (ec *EventCoordinator) PublishSystemEvent(ctx context.Context, eventType string, severity string, eventData map[string]interface{}) error {
	ec.logger.WithFields(logrus.Fields{
		"event_type": eventType,
		"severity":   severity,
	}).Info("Publishing system event")

	event := &models.SystemEventRequest{
		EventType: eventType,
		Severity:  severity,
		EventData: eventData,
	}

	err := ec.orchestrator.notificationClient.BroadcastSystemEvent(ctx, event)
	if err != nil {
		ec.logger.WithError(err).Error("Failed to publish system event")
		return err
	}

	ec.logger.WithFields(logrus.Fields{
		"event_type": eventType,
		"severity":   severity,
	}).Info("System event published successfully")

	return nil
}

// GetEventStats returns event coordinator statistics
func (ec *EventCoordinator) GetEventStats() *EventCoordinatorStats {
	return &EventCoordinatorStats{
		SupportedEvents: []string{
			"session_created",
			"game_completed",
			"game_cancelled",
			"player_joined",
			"player_left",
			"game_state_changed",
		},
		EventChannels: []string{"game", "room", "user", "system"},
		Version:       "1.0.0",
	}
}

// EventCoordinatorStats represents event coordinator statistics
type EventCoordinatorStats struct {
	SupportedEvents []string `json:"supportedEvents"`
	EventChannels   []string `json:"eventChannels"`
	Version         string   `json:"version"`
}
