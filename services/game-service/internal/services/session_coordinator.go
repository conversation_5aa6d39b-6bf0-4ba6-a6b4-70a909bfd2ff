package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/redis"
)

// SessionCoordinator coordinates game session operations
type SessionCoordinator struct {
	orchestrator *GameOrchestrator
	logger       *logrus.Logger
}

// NewSessionCoordinator creates a new session coordinator
func NewSessionCoordinator(orchestrator *GameOrchestrator) *SessionCoordinator {
	return &SessionCoordinator{
		orchestrator: orchestrator,
		logger:       orchestrator.logger,
	}
}

// CreateSession creates a new game session
func (sc *SessionCoordinator) CreateSession(ctx context.Context, roomID string, config models.SessionConfiguration) (*models.GameSession, error) {
	sc.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"config":  config,
	}).Info("Creating game session")

	session := &models.GameSession{
		RoomID:        roomID,
		GameType:      config.GameSpecific["game_type"].(models.GameType),
		Status:        models.SessionStatusPending,
		Players:       make([]models.SessionPlayer, 0),
		Configuration: config,
		StartTime:     time.Now(),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := sc.orchestrator.gameRepo.CreateSession(ctx, session)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to create game session")
		return nil, err
	}

	// Cache the session state
	sessionKey := fmt.Sprintf(redis.KeyGameSession, session.ID.Hex())
	if err := sc.orchestrator.redisClient.Set(ctx, sessionKey, session, sc.orchestrator.config.RedisTTL); err != nil {
		sc.logger.WithError(err).Warn("Failed to cache game session")
	}

	sc.logger.WithField("session_id", session.ID.Hex()).Info("Game session created successfully")
	return session, nil
}

// GetSession retrieves a game session
func (sc *SessionCoordinator) GetSession(ctx context.Context, sessionID string) (*models.GameSession, error) {
	// Try to get from cache first
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	var cachedSession models.GameSession
	if err := sc.orchestrator.redisClient.Get(ctx, sessionKey, &cachedSession); err == nil {
		return &cachedSession, nil
	}

	// Get from database if not in cache
	session, err := sc.orchestrator.gameRepo.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Cache the session for future requests
	if err := sc.orchestrator.redisClient.Set(ctx, sessionKey, session, sc.orchestrator.config.RedisTTL); err != nil {
		sc.logger.WithError(err).Warn("Failed to cache game session")
	}

	return session, nil
}

// UpdateSessionStatus updates the session status
func (sc *SessionCoordinator) UpdateSessionStatus(ctx context.Context, sessionID string, status models.SessionStatus) error {
	err := sc.orchestrator.gameRepo.UpdateSessionStatus(ctx, sessionID, status)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to update session status")
		return err
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	return nil
}

// CompleteSession completes a session with results
func (sc *SessionCoordinator) CompleteSession(ctx context.Context, sessionID string, results *models.GameResults) error {
	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"winner":     results.WinnerUserID,
	}).Info("Completing game session")

	// Update session with results and completed status
	updates := map[string]interface{}{
		"status":   models.SessionStatusCompleted,
		"results":  results,
		"end_time": time.Now(),
	}

	err := sc.orchestrator.gameRepo.UpdateSession(ctx, sessionID, updates)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to update session with results")
		return err
	}

	// Update all players to finished status
	session, err := sc.orchestrator.gameRepo.GetSession(ctx, sessionID)
	if err != nil {
		return err
	}

	for _, player := range session.Players {
		err = sc.orchestrator.gameRepo.UpdatePlayerStatus(ctx, sessionID, player.UserID, models.PlayerStatusFinished)
		if err != nil {
			sc.logger.WithError(err).WithField("user_id", player.UserID).Warn("Failed to update player status")
		}
	}

	// Create game history record
	history := &models.GameHistory{
		SessionID:   sessionID,
		RoomID:      session.RoomID,
		GameType:    session.GameType,
		Players:     session.Players,
		Results:     *results,
		Duration:    time.Since(session.StartTime),
		CompletedAt: time.Now(),
		CreatedAt:   time.Now(),
	}

	if session.FairnessProof != nil {
		history.FairnessProof = *session.FairnessProof
	}

	err = sc.orchestrator.gameRepo.CreateGameHistory(ctx, history)
	if err != nil {
		sc.logger.WithError(err).Warn("Failed to create game history record")
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	sc.logger.WithField("session_id", sessionID).Info("Game session completed successfully")
	return nil
}

// CancelSession cancels a session
func (sc *SessionCoordinator) CancelSession(ctx context.Context, sessionID string, reason string) error {
	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"reason":     reason,
	}).Info("Cancelling game session")

	updates := map[string]interface{}{
		"status":   models.SessionStatusCancelled,
		"end_time": time.Now(),
	}

	err := sc.orchestrator.gameRepo.UpdateSession(ctx, sessionID, updates)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to cancel game session")
		return err
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	sc.logger.WithField("session_id", sessionID).Info("Game session cancelled successfully")
	return nil
}

// GetGameState retrieves the current game state
func (sc *SessionCoordinator) GetGameState(ctx context.Context, sessionID string) (*models.GameState, error) {
	session, err := sc.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	return sc.sessionToGameState(session), nil
}

// UpdateGameState updates the game state
func (sc *SessionCoordinator) UpdateGameState(ctx context.Context, sessionID string, gameData map[string]interface{}) error {
	updates := map[string]interface{}{
		"game_data": gameData,
	}

	err := sc.orchestrator.gameRepo.UpdateSession(ctx, sessionID, updates)
	if err != nil {
		return err
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	return nil
}

// AddPlayer adds a player to a session
func (sc *SessionCoordinator) AddPlayer(ctx context.Context, sessionID string, player models.SessionPlayer) error {
	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    player.UserID,
	}).Info("Adding player to session")

	err := sc.orchestrator.gameRepo.AddPlayerToSession(ctx, sessionID, player)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to add player to session")
		return err
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    player.UserID,
	}).Info("Player added to session successfully")

	return nil
}

// RemovePlayer removes a player from a session
func (sc *SessionCoordinator) RemovePlayer(ctx context.Context, sessionID string, userID string) error {
	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Removing player from session")

	err := sc.orchestrator.gameRepo.RemovePlayerFromSession(ctx, sessionID, userID)
	if err != nil {
		sc.logger.WithError(err).Error("Failed to remove player from session")
		return err
	}

	// Invalidate cache
	sessionKey := fmt.Sprintf(redis.KeyGameSession, sessionID)
	sc.orchestrator.redisClient.Delete(ctx, sessionKey)

	sc.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("Player removed from session successfully")

	return nil
}

// GetPlayerStats retrieves game statistics for a player
func (sc *SessionCoordinator) GetPlayerStats(ctx context.Context, userID string) (*PlayerGameStats, error) {
	// This would typically involve aggregation queries
	// For now, return basic stats
	history, err := sc.orchestrator.gameRepo.GetGameHistoryByPlayer(ctx, userID, nil, 100, 0)
	if err != nil {
		return nil, err
	}

	stats := &PlayerGameStats{
		UserID:           userID,
		TotalGames:       len(history),
		GamesWon:         0,
		GamesLost:        0,
		TotalBetAmount:   0,
		TotalWinAmount:   0,
		AverageGameTime:  0,
		FavoriteGameType: models.GameTypePrizeWheel,
		LastGameAt:       nil,
	}

	for _, game := range history {
		stats.TotalBetAmount += game.Results.TotalBetPool
		if game.Results.WinnerUserID == userID {
			stats.GamesWon++
			stats.TotalWinAmount += game.Results.PrizePool
		} else {
			stats.GamesLost++
		}
	}

	if len(history) > 0 {
		stats.LastGameAt = &history[0].CompletedAt
	}

	return stats, nil
}

// Helper methods

// sessionToGameState converts a GameSession to GameState
func (sc *SessionCoordinator) sessionToGameState(session *models.GameSession) *models.GameState {
	gameData := make(map[string]interface{})
	if session.Results != nil {
		gameData = session.Results.GameData
	}

	return &models.GameState{
		SessionID:   session.ID.Hex(),
		Phase:       models.GamePhase(session.Status),
		Players:     session.Players,
		StartTime:   session.StartTime,
		GameData:    gameData,
		Results:     session.Results,
		LastUpdated: session.UpdatedAt,
	}
}
