package services

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/game-service/internal/config"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/repositories"
	"github.com/xzgame/game-service/pkg/redis"
)

// playerService implements the PlayerService interface
type playerService struct {
	playerRepo  repositories.PlayerRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
}

// NewPlayerService creates a new player service instance
func NewPlayerService(
	playerRepo repositories.PlayerRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
) PlayerService {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &playerService{
		playerRepo:  playerRepo,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
	}
}

// CreatePlayerSession creates a new player session
func (s *playerService) CreatePlayerSession(ctx context.Context, userID string, roomID string) error {
	s.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"room_id": roomID,
	}).Info("Creating player session")

	session := &models.PlayerSession{
		UserID:       userID,
		RoomID:       roomID,
		Status:       "active",
		LastPing:     time.Now(),
		ConnectionID: "", // Would be set from connection context
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err := s.playerRepo.CreatePlayerSession(ctx, session)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create player session")
		return err
	}

	s.logger.WithField("user_id", userID).Info("Player session created successfully")
	return nil
}

// GetPlayerSession retrieves a player's current session
func (s *playerService) GetPlayerSession(ctx context.Context, userID string) (*PlayerSession, error) {
	repoSession, err := s.playerRepo.GetPlayerSession(ctx, userID)
	if err != nil {
		return nil, err
	}

	if repoSession == nil {
		return nil, nil
	}

	session := &PlayerSession{
		UserID:       repoSession.UserID,
		SessionID:    repoSession.SessionID,
		RoomID:       repoSession.RoomID,
		Status:       repoSession.Status,
		LastPing:     repoSession.LastPing,
		ConnectionID: repoSession.ConnectionID,
		CreatedAt:    repoSession.CreatedAt,
		UpdatedAt:    repoSession.UpdatedAt,
	}

	return session, nil
}

// UpdatePlayerSession updates a player's session
func (s *playerService) UpdatePlayerSession(ctx context.Context, userID string, updates map[string]interface{}) error {
	err := s.playerRepo.UpdatePlayerSession(ctx, userID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update player session")
		return err
	}

	return nil
}

// DeletePlayerSession deletes a player's session
func (s *playerService) DeletePlayerSession(ctx context.Context, userID string) error {
	err := s.playerRepo.DeletePlayerSession(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to delete player session")
		return err
	}

	s.logger.WithField("user_id", userID).Info("Player session deleted successfully")
	return nil
}

// GetPlayerStatus retrieves a player's current status
func (s *playerService) GetPlayerStatus(ctx context.Context, userID string) (*PlayerStatus, error) {
	repoStatus, err := s.playerRepo.GetPlayerStatus(ctx, userID)
	if err != nil {
		return nil, err
	}

	if repoStatus == nil {
		// Return default status if no record found
		return &PlayerStatus{
			UserID:       userID,
			Status:       "offline",
			LastActivity: time.Now(),
			IsOnline:     false,
		}, nil
	}

	status := &PlayerStatus{
		UserID:           repoStatus.UserID,
		CurrentRoomID:    repoStatus.CurrentRoomID,
		CurrentSessionID: repoStatus.CurrentSessionID,
		Status:           repoStatus.Status,
		LastActivity:     repoStatus.LastActivity,
		IsOnline:         repoStatus.IsOnline,
	}

	return status, nil
}

// UpdatePlayerActivity updates a player's activity
func (s *playerService) UpdatePlayerActivity(ctx context.Context, userID string, activity PlayerActivity) error {
	repoActivity := models.PlayerActivity{
		UserID:       activity.UserID,
		ActivityType: activity.ActivityType,
		RoomID:       activity.RoomID,
		SessionID:    activity.SessionID,
		Data:         activity.Data,
		Timestamp:    activity.Timestamp,
	}

	err := s.playerRepo.UpdatePlayerActivity(ctx, userID, repoActivity)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update player activity")
		return err
	}

	return nil
}

// GetPlayerActivity retrieves a player's activity history
func (s *playerService) GetPlayerActivity(ctx context.Context, userID string, since time.Time) ([]*PlayerActivity, error) {
	repoActivities, err := s.playerRepo.GetPlayerActivity(ctx, userID, since)
	if err != nil {
		return nil, err
	}

	activities := make([]*PlayerActivity, len(repoActivities))
	for i, repoActivity := range repoActivities {
		activities[i] = &PlayerActivity{
			UserID:       repoActivity.UserID,
			ActivityType: repoActivity.ActivityType,
			RoomID:       repoActivity.RoomID,
			SessionID:    repoActivity.SessionID,
			Data:         repoActivity.Data,
			Timestamp:    repoActivity.Timestamp,
		}
	}

	return activities, nil
}

// ValidatePlayerBalance validates if a player has sufficient balance
func (s *playerService) ValidatePlayerBalance(ctx context.Context, userID string, amount int64) error {
	// This would typically call an external service to check balance
	// For now, just return success
	s.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"amount":  amount,
	}).Info("Validating player balance")

	// Simulate balance check
	if amount <= 0 {
		return models.NewPlayerError(
			models.ErrorCodeInvalidInput,
			"invalid bet amount",
			userID,
		)
	}

	// In a real implementation, this would check against a wallet service
	return nil
}

// AuthorizePlayerAction authorizes a player action
func (s *playerService) AuthorizePlayerAction(ctx context.Context, userID string, action string, actionContext map[string]interface{}) error {
	s.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"action":  action,
		"context": actionContext,
	}).Info("Authorizing player action")

	// This would typically implement authorization logic
	// For now, just return success
	return nil
}

// CleanupInactivePlayers cleans up inactive player sessions
func (s *playerService) CleanupInactivePlayers(ctx context.Context, inactiveSince time.Time) error {
	s.logger.WithField("inactive_since", inactiveSince).Info("Cleaning up inactive players")

	err := s.playerRepo.CleanupExpiredSessions(ctx, inactiveSince)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cleanup inactive players")
		return err
	}

	s.logger.Info("Inactive players cleaned up successfully")
	return nil
}
