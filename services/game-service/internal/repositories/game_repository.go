package repositories

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/database"
)

// gameRepository implements the GameRepository interface
type gameRepository struct {
	db         *database.MongoClient
	collection *mongo.Collection
	historyCollection *mongo.Collection
}

// NewGameRepository creates a new game repository instance
func NewGameRepository(db *database.MongoClient) GameRepository {
	return &gameRepository{
		db:         db,
		collection: db.GetCollection(database.CollectionGameSessions),
		historyCollection: db.GetCollection(database.CollectionGameHistory),
	}
}

// CreateSession creates a new game session
func (r *gameRepository) CreateSession(ctx context.Context, session *models.GameSession) error {
	if session.ID.IsZero() {
		session.ID = primitive.NewObjectID()
	}
	session.CreatedAt = time.Now()
	session.UpdatedAt = time.Now()
	session.Version = 1

	_, err := r.collection.InsertOne(ctx, session)
	if err != nil {
		return models.NewDatabaseError("failed to create game session", err)
	}

	return nil
}

// GetSession retrieves a game session by ID
func (r *gameRepository) GetSession(ctx context.Context, sessionID string) (*models.GameSession, error) {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return nil, models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	var session models.GameSession
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, models.ErrSessionNotFound
		}
		return nil, models.NewDatabaseError("failed to get game session", err)
	}

	return &session, nil
}

// UpdateSession updates a game session with optimistic locking
func (r *gameRepository) UpdateSession(ctx context.Context, sessionID string, updates map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	// Add updated timestamp and increment version
	updates["updated_at"] = time.Now()

	// Use optimistic locking by including current version in filter
	session, err := r.GetSession(ctx, sessionID)
	if err != nil {
		return err
	}

	filter := bson.M{
		"_id":     objectID,
		"version": session.Version,
	}

	updateDoc := bson.M{
		"$set": updates,
		"$inc": bson.M{"version": 1},
	}

	result, err := r.collection.UpdateOne(ctx, filter, updateDoc)
	if err != nil {
		return models.NewDatabaseError("failed to update game session", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrConcurrentModification
	}

	return nil
}

// DeleteSession deletes a game session
func (r *gameRepository) DeleteSession(ctx context.Context, sessionID string) error {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return models.NewDatabaseError("failed to delete game session", err)
	}

	if result.DeletedCount == 0 {
		return models.ErrSessionNotFound
	}

	return nil
}

// UpdateSessionStatus updates the status of a game session
func (r *gameRepository) UpdateSessionStatus(ctx context.Context, sessionID string, status models.SessionStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}

	// Add end time if session is completed or cancelled
	if status == models.SessionStatusCompleted || status == models.SessionStatusCancelled {
		now := time.Now()
		updates["end_time"] = &now
	}

	return r.UpdateSession(ctx, sessionID, updates)
}

// AddPlayerToSession adds a player to a game session
func (r *gameRepository) AddPlayerToSession(ctx context.Context, sessionID string, player models.SessionPlayer) error {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	player.JoinedAt = time.Now()
	player.Status = models.PlayerStatusJoined

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$push": bson.M{"players": player},
		"$set":  bson.M{"updated_at": time.Now()},
		"$inc":  bson.M{"version": 1},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to add player to session", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrSessionNotFound
	}

	return nil
}

// RemovePlayerFromSession removes a player from a game session
func (r *gameRepository) RemovePlayerFromSession(ctx context.Context, sessionID string, userID string) error {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$pull": bson.M{"players": bson.M{"user_id": userID}},
		"$set":  bson.M{"updated_at": time.Now()},
		"$inc":  bson.M{"version": 1},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to remove player from session", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrSessionNotFound
	}

	return nil
}

// UpdatePlayerStatus updates a player's status in a game session
func (r *gameRepository) UpdatePlayerStatus(ctx context.Context, sessionID string, userID string, status models.PlayerStatus) error {
	objectID, err := primitive.ObjectIDFromHex(sessionID)
	if err != nil {
		return models.NewValidationError("invalid session ID format", map[string]interface{}{
			"session_id": sessionID,
		})
	}

	filter := bson.M{
		"_id":              objectID,
		"players.user_id": userID,
	}
	update := bson.M{
		"$set": bson.M{
			"players.$.status":     status,
			"updated_at":          time.Now(),
		},
		"$inc": bson.M{"version": 1},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to update player status", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrPlayerNotInRoom
	}

	return nil
}

// GetSessionsByRoom retrieves all sessions for a specific room
func (r *gameRepository) GetSessionsByRoom(ctx context.Context, roomID string) ([]*models.GameSession, error) {
	filter := bson.M{"room_id": roomID}
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get sessions by room", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.GameSession
	for cursor.Next(ctx) {
		var session models.GameSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// GetSessionsByPlayer retrieves sessions for a specific player with pagination
func (r *gameRepository) GetSessionsByPlayer(ctx context.Context, userID string, limit int, offset int) ([]*models.GameSession, error) {
	filter := bson.M{"players.user_id": userID}
	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get sessions by player", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.GameSession
	for cursor.Next(ctx) {
		var session models.GameSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// GetActiveSessionsByPlayer retrieves active sessions for a specific player
func (r *gameRepository) GetActiveSessionsByPlayer(ctx context.Context, userID string) ([]*models.GameSession, error) {
	filter := bson.M{
		"players.user_id": userID,
		"status": bson.M{
			"$in": []models.SessionStatus{
				models.SessionStatusPending,
				models.SessionStatusWaiting,
				models.SessionStatusActive,
			},
		},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get active sessions by player", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.GameSession
	for cursor.Next(ctx) {
		var session models.GameSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// GetExpiredSessions retrieves sessions that have expired
func (r *gameRepository) GetExpiredSessions(ctx context.Context, expiredBefore time.Time) ([]*models.GameSession, error) {
	filter := bson.M{
		"status": bson.M{
			"$in": []models.SessionStatus{
				models.SessionStatusPending,
				models.SessionStatusWaiting,
				models.SessionStatusActive,
			},
		},
		"created_at": bson.M{"$lt": expiredBefore},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get expired sessions", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.GameSession
	for cursor.Next(ctx) {
		var session models.GameSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// CreateGameHistory creates a game history record
func (r *gameRepository) CreateGameHistory(ctx context.Context, history *models.GameHistory) error {
	if history.ID.IsZero() {
		history.ID = primitive.NewObjectID()
	}
	history.CreatedAt = time.Now()

	_, err := r.historyCollection.InsertOne(ctx, history)
	if err != nil {
		return models.NewDatabaseError("failed to create game history", err)
	}

	return nil
}

// GetGameHistory retrieves game history with filters
func (r *gameRepository) GetGameHistory(ctx context.Context, filter models.GameHistoryFilter) ([]*models.GameHistory, error) {
	mongoFilter := bson.M{}

	if filter.GameType != nil {
		mongoFilter["game_type"] = *filter.GameType
	}

	if filter.PlayerID != nil {
		mongoFilter["players.user_id"] = *filter.PlayerID
	}

	if filter.DateFrom != nil || filter.DateTo != nil {
		dateFilter := bson.M{}
		if filter.DateFrom != nil {
			dateFilter["$gte"] = *filter.DateFrom
		}
		if filter.DateTo != nil {
			dateFilter["$lte"] = *filter.DateTo
		}
		mongoFilter["completed_at"] = dateFilter
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "completed_at", Value: -1}}).
		SetLimit(int64(filter.Limit)).
		SetSkip(int64(filter.Page * filter.Limit))

	cursor, err := r.historyCollection.Find(ctx, mongoFilter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get game history", err)
	}
	defer cursor.Close(ctx)

	var history []*models.GameHistory
	for cursor.Next(ctx) {
		var record models.GameHistory
		if err := cursor.Decode(&record); err != nil {
			return nil, models.NewDatabaseError("failed to decode game history", err)
		}
		history = append(history, &record)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return history, nil
}

// GetGameHistoryByPlayer retrieves game history for a specific player
func (r *gameRepository) GetGameHistoryByPlayer(ctx context.Context, userID string, gameType *models.GameType, limit int, offset int) ([]*models.GameHistory, error) {
	filter := bson.M{"players.user_id": userID}

	if gameType != nil {
		filter["game_type"] = *gameType
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "completed_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.historyCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get game history by player", err)
	}
	defer cursor.Close(ctx)

	var history []*models.GameHistory
	for cursor.Next(ctx) {
		var record models.GameHistory
		if err := cursor.Decode(&record); err != nil {
			return nil, models.NewDatabaseError("failed to decode game history", err)
		}
		history = append(history, &record)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return history, nil
}

// WithTransaction executes a function within a database transaction
func (r *gameRepository) WithTransaction(ctx context.Context, fn func(context.Context) error) error {
	session, err := r.db.StartSession()
	if err != nil {
		return models.NewDatabaseError("failed to start transaction", err)
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sc mongo.SessionContext) (interface{}, error) {
		return nil, fn(sc)
	})

	if err != nil {
		return models.NewDatabaseError("transaction failed", err)
	}

	return nil
}
