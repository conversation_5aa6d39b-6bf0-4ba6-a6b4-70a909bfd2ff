package repositories

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/database"
)

// roomRepository implements the RoomRepository interface
type roomRepository struct {
	db         *database.MongoClient
	collection *mongo.Collection
}

// NewRoomRepository creates a new room repository instance
func NewRoomRepository(db *database.MongoClient) RoomRepository {
	return &roomRepository{
		db:         db,
		collection: db.GetCollection(database.CollectionRooms),
	}
}

// CreateRoom creates a new room
func (r *roomRepository) CreateRoom(ctx context.Context, room *models.Room) error {
	if room.ID.IsZero() {
		room.ID = primitive.NewObjectID()
	}
	room.CreatedAt = time.Now()
	room.UpdatedAt = time.Now()
	room.LastActivity = time.Now()
	room.CurrentPlayers = 0
	room.Players = make([]models.RoomPlayer, 0)

	_, err := r.collection.InsertOne(ctx, room)
	if err != nil {
		return models.NewDatabaseError("failed to create room", err)
	}

	return nil
}

// GetRoom retrieves a room by ID
func (r *roomRepository) GetRoom(ctx context.Context, roomID string) (*models.Room, error) {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return nil, models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	var room models.Room
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&room)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, models.ErrRoomNotFound
		}
		return nil, models.NewDatabaseError("failed to get room", err)
	}

	return &room, nil
}

// UpdateRoom updates a room
func (r *roomRepository) UpdateRoom(ctx context.Context, roomID string, updates map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	updates["updated_at"] = time.Now()
	updates["last_activity"] = time.Now()

	filter := bson.M{"_id": objectID}
	update := bson.M{"$set": updates}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to update room", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrRoomNotFound
	}

	return nil
}

// DeleteRoom deletes a room
func (r *roomRepository) DeleteRoom(ctx context.Context, roomID string) error {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return models.NewDatabaseError("failed to delete room", err)
	}

	if result.DeletedCount == 0 {
		return models.ErrRoomNotFound
	}

	return nil
}

// AddPlayerToRoom adds a player to a room
func (r *roomRepository) AddPlayerToRoom(ctx context.Context, roomID string, player models.RoomPlayer) error {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	player.JoinedAt = time.Now()
	player.Status = models.PlayerStatusJoined

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$push": bson.M{"players": player},
		"$inc":  bson.M{"current_players": 1},
		"$set": bson.M{
			"updated_at":    time.Now(),
			"last_activity": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to add player to room", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrRoomNotFound
	}

	return nil
}

// RemovePlayerFromRoom removes a player from a room with enhanced consistency checks
func (r *roomRepository) RemovePlayerFromRoom(ctx context.Context, roomID string, userID string) error {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	// First, verify the player exists in the room before attempting removal
	preFilter := bson.M{
		"_id":             objectID,
		"players.user_id": userID,
	}

	var existingRoom models.Room
	err = r.collection.FindOne(ctx, preFilter).Decode(&existingRoom)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Player not in room or room doesn't exist
			return models.ErrPlayerNotInRoom
		}
		return models.NewDatabaseError("failed to verify player in room", err)
	}

	// Count players before removal for verification
	playerCountBefore := len(existingRoom.Players)

	// Count how many instances of this player exist (to handle duplicates)
	playerInstances := 0
	for _, player := range existingRoom.Players {
		if player.UserID == userID {
			playerInstances++
		}
	}

	// Execute the removal operation (removes ALL instances of the player)
	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$pull": bson.M{"players": bson.M{"user_id": userID}},
		"$inc":  bson.M{"current_players": -playerInstances}, // Decrement by actual instances
		"$set": bson.M{
			"updated_at":    time.Now(),
			"last_activity": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to remove player from room", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrRoomNotFound
	}

	// Verify the removal was successful by checking the updated document
	var updatedRoom models.Room
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&updatedRoom)
	if err != nil {
		return models.NewDatabaseError("failed to verify player removal", err)
	}

	// Check that player count decreased by the number of instances removed
	playerCountAfter := len(updatedRoom.Players)
	expectedPlayerCount := playerCountBefore - playerInstances
	if playerCountAfter != expectedPlayerCount {
		return models.NewDatabaseError("player count mismatch after removal",
			fmt.Errorf("expected %d players (removed %d instances), got %d", expectedPlayerCount, playerInstances, playerCountAfter))
	}

	// Verify player is actually removed
	for _, player := range updatedRoom.Players {
		if player.UserID == userID {
			return models.NewDatabaseError("player still exists after removal",
				fmt.Errorf("player %s still found in room %s", userID, roomID))
		}
	}

	return nil
}

// UpdatePlayerInRoom updates a player's information in a room
func (r *roomRepository) UpdatePlayerInRoom(ctx context.Context, roomID string, userID string, updates map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(roomID)
	if err != nil {
		return models.NewValidationError("invalid room ID format", map[string]interface{}{
			"room_id": roomID,
		})
	}

	// Build the update document for the specific player
	playerUpdates := bson.M{}
	for key, value := range updates {
		playerUpdates["players.$."+key] = value
	}
	playerUpdates["updated_at"] = time.Now()
	playerUpdates["last_activity"] = time.Now()

	filter := bson.M{
		"_id":             objectID,
		"players.user_id": userID,
	}
	update := bson.M{"$set": playerUpdates}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to update player in room", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrPlayerNotInRoom
	}

	return nil
}

// ListRooms retrieves rooms with filtering and pagination
func (r *roomRepository) ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error) {
	mongoFilter := bson.M{}

	if filter.GameType != nil {
		mongoFilter["game_type"] = *filter.GameType
	}

	if filter.Status != nil {
		mongoFilter["status"] = *filter.Status
	}

	if filter.MinPlayers != nil {
		mongoFilter["min_players"] = bson.M{"$gte": *filter.MinPlayers}
	}

	if filter.MaxPlayers != nil {
		mongoFilter["max_players"] = bson.M{"$lte": *filter.MaxPlayers}
	}

	if filter.HasSpace {
		mongoFilter["$expr"] = bson.M{
			"$lt": []string{"$current_players", "$max_players"},
		}
	}

	// Count total documents
	totalCount, err := r.collection.CountDocuments(ctx, mongoFilter)
	if err != nil {
		return nil, 0, models.NewDatabaseError("failed to count rooms", err)
	}

	// Set default pagination values
	if filter.Limit <= 0 {
		filter.Limit = 20
	}
	if filter.Page < 0 {
		filter.Page = 0
	}

	// Find documents with pagination
	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetLimit(int64(filter.Limit)).
		SetSkip(int64(filter.Page * filter.Limit))

	cursor, err := r.collection.Find(ctx, mongoFilter, opts)
	if err != nil {
		return nil, 0, models.NewDatabaseError("failed to list rooms", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, 0, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, 0, models.NewDatabaseError("cursor error", err)
	}

	return rooms, totalCount, nil
}

// GetRoomsByGameType retrieves rooms by game type
func (r *roomRepository) GetRoomsByGameType(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	filter := bson.M{"game_type": gameType}
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get rooms by game type", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return rooms, nil
}

// GetRoomsByStatus retrieves rooms by status
func (r *roomRepository) GetRoomsByStatus(ctx context.Context, status models.RoomStatus) ([]*models.Room, error) {
	filter := bson.M{"status": status}
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get rooms by status", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return rooms, nil
}

// GetRoomsByPlayer retrieves rooms where a player is present
func (r *roomRepository) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	filter := bson.M{"players.user_id": userID}
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get rooms by player", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return rooms, nil
}

// Batch operations for performance optimization

// BatchUpdateRoomStatus updates multiple rooms' status in a single operation
func (r *roomRepository) BatchUpdateRoomStatus(ctx context.Context, roomIDs []string, status models.RoomStatus) error {
	if len(roomIDs) == 0 {
		return nil
	}

	objectIDs := make([]primitive.ObjectID, len(roomIDs))
	for i, id := range roomIDs {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return models.NewValidationError("invalid room ID format", map[string]interface{}{
				"room_id": id,
			})
		}
		objectIDs[i] = objectID
	}

	filter := bson.M{"_id": bson.M{"$in": objectIDs}}
	update := bson.M{
		"$set": bson.M{
			"status":        status,
			"updated_at":    time.Now(),
			"last_activity": time.Now(),
		},
	}

	result, err := r.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to batch update room status", err)
	}

	if result.ModifiedCount == 0 {
		return models.NewValidationError("no rooms were updated", map[string]interface{}{
			"room_ids": roomIDs,
			"status":   status,
		})
	}

	return nil
}

// BatchGetRooms retrieves multiple rooms by their IDs in a single query
func (r *roomRepository) BatchGetRooms(ctx context.Context, roomIDs []string) ([]*models.Room, error) {
	if len(roomIDs) == 0 {
		return []*models.Room{}, nil
	}

	objectIDs := make([]primitive.ObjectID, len(roomIDs))
	for i, id := range roomIDs {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, models.NewValidationError("invalid room ID format", map[string]interface{}{
				"room_id": id,
			})
		}
		objectIDs[i] = objectID
	}

	filter := bson.M{"_id": bson.M{"$in": objectIDs}}
	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to batch get rooms", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return rooms, nil
}

// GetActiveRoomsOptimized retrieves active rooms with optimized query
func (r *roomRepository) GetActiveRoomsOptimized(ctx context.Context, gameType *models.GameType, limit int) ([]*models.Room, error) {
	filter := bson.M{
		"status": bson.M{"$in": []models.RoomStatus{
			models.RoomStatusWaiting,
			models.RoomStatusActive,
		}},
		"$expr": bson.M{
			"$lt": []string{"$current_players", "$max_players"},
		},
	}

	if gameType != nil {
		filter["game_type"] = *gameType
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetProjection(bson.M{
			"_id":             1,
			"name":            1,
			"game_type":       1,
			"status":          1,
			"current_players": 1,
			"max_players":     1,
			"min_players":     1,
			"bet_amount":      1,
			"created_at":      1,
			"last_activity":   1,
			"configuration":   1,
		})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get active rooms", err)
	}
	defer cursor.Close(ctx)

	var rooms []*models.Room
	for cursor.Next(ctx) {
		var room models.Room
		if err := cursor.Decode(&room); err != nil {
			return nil, models.NewDatabaseError("failed to decode room", err)
		}
		rooms = append(rooms, &room)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return rooms, nil
}

// GetRoomStats retrieves statistics for a room
func (r *roomRepository) GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error) {
	// This would typically involve aggregation queries to calculate statistics
	// For now, return basic stats
	room, err := r.GetRoom(ctx, roomID)
	if err != nil {
		return nil, err
	}

	stats := &models.RoomStats{
		RoomID:          roomID,
		TotalGames:      0, // Would be calculated from game history
		TotalPlayers:    room.CurrentPlayers,
		AverageGameTime: 0,   // Would be calculated from game history
		TotalBetVolume:  0,   // Would be calculated from game history
		LastGameAt:      nil, // Would be from last game session
	}

	return stats, nil
}

// GetRoomActivity retrieves recent activity for a room
func (r *roomRepository) GetRoomActivity(ctx context.Context, roomID string, since time.Time) (*models.RoomActivity, error) {
	room, err := r.GetRoom(ctx, roomID)
	if err != nil {
		return nil, err
	}

	activity := &models.RoomActivity{
		RoomID:    roomID,
		Events:    []models.RoomEvent{}, // Would be populated from event logs
		Players:   room.Players,
		GameState: nil, // Would be populated if there's an active game
		UpdatedAt: room.UpdatedAt,
	}

	return activity, nil
}

// WithTransaction executes a function within a database transaction
func (r *roomRepository) WithTransaction(ctx context.Context, fn func(context.Context) error) error {
	session, err := r.db.StartSession()
	if err != nil {
		return models.NewDatabaseError("failed to start transaction", err)
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sc mongo.SessionContext) (interface{}, error) {
		return nil, fn(sc)
	})

	if err != nil {
		return models.NewDatabaseError("transaction failed", err)
	}

	return nil
}
