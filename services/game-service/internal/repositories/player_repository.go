package repositories

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/database"
)

// playerRepository implements the PlayerRepository interface
type playerRepository struct {
	db         *database.MongoClient
	collection *mongo.Collection
	activityCollection *mongo.Collection
}

// NewPlayerRepository creates a new player repository instance
func NewPlayerRepository(db *database.MongoClient) PlayerRepository {
	return &playerRepository{
		db:         db,
		collection: db.GetCollection(database.CollectionPlayerSessions),
		activityCollection: db.GetCollection(database.CollectionAuditLogs),
	}
}

// CreatePlayerSession creates a new player session
func (r *playerRepository) CreatePlayerSession(ctx context.Context, session *models.PlayerSession) error {
	session.CreatedAt = time.Now()
	session.UpdatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, session)
	if err != nil {
		return models.NewDatabaseError("failed to create player session", err)
	}

	return nil
}

// GetPlayerSession retrieves a player session by user ID
func (r *playerRepository) GetPlayerSession(ctx context.Context, userID string) (*models.PlayerSession, error) {
	var session models.PlayerSession
	err := r.collection.FindOne(ctx, bson.M{"_id": userID}).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, models.NewDatabaseError("failed to get player session", err)
	}

	return &session, nil
}

// UpdatePlayerSession updates a player session
func (r *playerRepository) UpdatePlayerSession(ctx context.Context, userID string, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()

	filter := bson.M{"_id": userID}
	update := bson.M{"$set": updates}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to update player session", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrPlayerNotFound
	}

	return nil
}

// DeletePlayerSession deletes a player session
func (r *playerRepository) DeletePlayerSession(ctx context.Context, userID string) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": userID})
	if err != nil {
		return models.NewDatabaseError("failed to delete player session", err)
	}

	if result.DeletedCount == 0 {
		return models.ErrPlayerNotFound
	}

	return nil
}

// GetPlayerStatus retrieves a player's current status
func (r *playerRepository) GetPlayerStatus(ctx context.Context, userID string) (*models.PlayerStatusInfo, error) {
	session, err := r.GetPlayerSession(ctx, userID)
	if err != nil {
		return nil, err
	}

	if session == nil {
		return nil, nil
	}

	status := &models.PlayerStatusInfo{
		UserID:           session.UserID,
		CurrentRoomID:    &session.RoomID,
		CurrentSessionID: &session.SessionID,
		Status:           session.Status,
		LastActivity:     session.LastPing,
		IsOnline:         time.Since(session.LastPing) < 5*time.Minute,
	}

	return status, nil
}

// GetPlayersInRoom retrieves all players in a specific room
func (r *playerRepository) GetPlayersInRoom(ctx context.Context, roomID string) ([]*models.PlayerSession, error) {
	filter := bson.M{"room_id": roomID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get players in room", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.PlayerSession
	for cursor.Next(ctx) {
		var session models.PlayerSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode player session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// GetPlayersInSession retrieves all players in a specific game session
func (r *playerRepository) GetPlayersInSession(ctx context.Context, sessionID string) ([]*models.PlayerSession, error) {
	filter := bson.M{"session_id": sessionID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get players in session", err)
	}
	defer cursor.Close(ctx)

	var sessions []*models.PlayerSession
	for cursor.Next(ctx) {
		var session models.PlayerSession
		if err := cursor.Decode(&session); err != nil {
			return nil, models.NewDatabaseError("failed to decode player session", err)
		}
		sessions = append(sessions, &session)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return sessions, nil
}

// UpdatePlayerActivity updates a player's activity
func (r *playerRepository) UpdatePlayerActivity(ctx context.Context, userID string, activity models.PlayerActivity) error {
	activity.Timestamp = time.Now()

	_, err := r.activityCollection.InsertOne(ctx, activity)
	if err != nil {
		return models.NewDatabaseError("failed to update player activity", err)
	}

	return nil
}

// GetPlayerActivity retrieves a player's activity history
func (r *playerRepository) GetPlayerActivity(ctx context.Context, userID string, since time.Time) ([]*models.PlayerActivity, error) {
	filter := bson.M{
		"user_id":   userID,
		"timestamp": bson.M{"$gte": since},
	}

	cursor, err := r.activityCollection.Find(ctx, filter)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get player activity", err)
	}
	defer cursor.Close(ctx)

	var activities []*models.PlayerActivity
	for cursor.Next(ctx) {
		var activity models.PlayerActivity
		if err := cursor.Decode(&activity); err != nil {
			return nil, models.NewDatabaseError("failed to decode player activity", err)
		}
		activities = append(activities, &activity)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return activities, nil
}

// CleanupExpiredSessions removes expired player sessions
func (r *playerRepository) CleanupExpiredSessions(ctx context.Context, expiredBefore time.Time) error {
	filter := bson.M{
		"last_ping": bson.M{"$lt": expiredBefore},
	}

	result, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return models.NewDatabaseError("failed to cleanup expired sessions", err)
	}

	// Log the number of cleaned up sessions
	if result.DeletedCount > 0 {
		// Could log this information
	}

	return nil
}
