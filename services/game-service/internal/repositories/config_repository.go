package repositories

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/pkg/database"
)

// configRepository implements the ConfigRepository interface
type configRepository struct {
	db         *database.MongoClient
	collection *mongo.Collection
}

// NewConfigRepository creates a new config repository instance
func NewConfigRepository(db *database.MongoClient) ConfigRepository {
	return &configRepository{
		db:         db,
		collection: db.GetCollection(database.CollectionGameConfigurations),
	}
}

// CreateGameConfig creates a new game configuration
func (r *configRepository) CreateGameConfig(ctx context.Context, config *models.GameConfiguration) error {
	if config.ID.IsZero() {
		config.ID = primitive.NewObjectID()
	}
	config.CreatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, config)
	if err != nil {
		return models.NewDatabaseError("failed to create game configuration", err)
	}

	return nil
}

// GetGameConfig retrieves a game configuration by game type
func (r *configRepository) GetGameConfig(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error) {
	var config models.GameConfiguration
	err := r.collection.FindOne(ctx, bson.M{"game_type": gameType}).Decode(&config)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Return default configuration if none exists
			return r.getDefaultGameConfig(gameType), nil
		}
		return nil, models.NewDatabaseError("failed to get game configuration", err)
	}

	return &config, nil
}

// UpdateGameConfig updates a game configuration
func (r *configRepository) UpdateGameConfig(ctx context.Context, gameType models.GameType, updates map[string]interface{}) error {
	filter := bson.M{"game_type": gameType}
	update := bson.M{"$set": updates}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return models.NewDatabaseError("failed to update game configuration", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrConfigNotFound
	}

	return nil
}

// DeleteGameConfig deletes a game configuration
func (r *configRepository) DeleteGameConfig(ctx context.Context, gameType models.GameType) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"game_type": gameType})
	if err != nil {
		return models.NewDatabaseError("failed to delete game configuration", err)
	}

	if result.DeletedCount == 0 {
		return models.ErrConfigNotFound
	}

	return nil
}

// GetGameConfigVersion retrieves a specific version of game configuration
func (r *configRepository) GetGameConfigVersion(ctx context.Context, gameType models.GameType, version int) (*models.GameConfiguration, error) {
	var config models.GameConfiguration
	filter := bson.M{
		"game_type": gameType,
		"version":   version,
	}

	err := r.collection.FindOne(ctx, filter).Decode(&config)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, models.ErrConfigNotFound
		}
		return nil, models.NewDatabaseError("failed to get game configuration version", err)
	}

	return &config, nil
}

// GetActiveGameConfig retrieves the active game configuration
func (r *configRepository) GetActiveGameConfig(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error) {
	var config models.GameConfiguration
	filter := bson.M{
		"game_type": gameType,
		"is_active": true,
	}

	err := r.collection.FindOne(ctx, filter).Decode(&config)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Return default configuration if no active config exists
			return r.getDefaultGameConfig(gameType), nil
		}
		return nil, models.NewDatabaseError("failed to get active game configuration", err)
	}

	return &config, nil
}

// ActivateGameConfig activates a specific version of game configuration
func (r *configRepository) ActivateGameConfig(ctx context.Context, gameType models.GameType, version int) error {
	// First, deactivate all existing configurations for this game type
	deactivateFilter := bson.M{"game_type": gameType}
	deactivateUpdate := bson.M{"$set": bson.M{"is_active": false}}

	_, err := r.collection.UpdateMany(ctx, deactivateFilter, deactivateUpdate)
	if err != nil {
		return models.NewDatabaseError("failed to deactivate existing configurations", err)
	}

	// Then, activate the specified version
	activateFilter := bson.M{
		"game_type": gameType,
		"version":   version,
	}
	activateUpdate := bson.M{
		"$set": bson.M{
			"is_active":    true,
			"activated_at": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, activateFilter, activateUpdate)
	if err != nil {
		return models.NewDatabaseError("failed to activate game configuration", err)
	}

	if result.ModifiedCount == 0 {
		return models.ErrConfigNotFound
	}

	return nil
}

// ListGameConfigs retrieves all game configurations
func (r *configRepository) ListGameConfigs(ctx context.Context) ([]*models.GameConfiguration, error) {
	opts := options.Find().SetSort(bson.D{{Key: "game_type", Value: 1}, {Key: "version", Value: -1}})

	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to list game configurations", err)
	}
	defer cursor.Close(ctx)

	var configs []*models.GameConfiguration
	for cursor.Next(ctx) {
		var config models.GameConfiguration
		if err := cursor.Decode(&config); err != nil {
			return nil, models.NewDatabaseError("failed to decode game configuration", err)
		}
		configs = append(configs, &config)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return configs, nil
}

// GetConfigHistory retrieves configuration history for a game type
func (r *configRepository) GetConfigHistory(ctx context.Context, gameType models.GameType) ([]*models.GameConfiguration, error) {
	filter := bson.M{"game_type": gameType}
	opts := options.Find().SetSort(bson.D{{Key: "version", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, models.NewDatabaseError("failed to get configuration history", err)
	}
	defer cursor.Close(ctx)

	var configs []*models.GameConfiguration
	for cursor.Next(ctx) {
		var config models.GameConfiguration
		if err := cursor.Decode(&config); err != nil {
			return nil, models.NewDatabaseError("failed to decode game configuration", err)
		}
		configs = append(configs, &config)
	}

	if err := cursor.Err(); err != nil {
		return nil, models.NewDatabaseError("cursor error", err)
	}

	return configs, nil
}

// getDefaultGameConfig returns a default configuration for a game type
func (r *configRepository) getDefaultGameConfig(gameType models.GameType) *models.GameConfiguration {
	now := time.Now()
	
	config := &models.GameConfiguration{
		ID:       primitive.NewObjectID(),
		GameType: gameType,
		Version:  1,
		Rules:    make(map[string]interface{}),
		Payouts: []models.PayoutRule{
			{
				Condition:   "winner",
				Multiplier:  0.95, // 95% of the pool after house edge
				Probability: 1.0,
				Description: "Winner takes the prize pool",
			},
		},
		HouseEdge:  0.05, // 5% house edge
		MinPlayers: 2,
		MaxPlayers: 8,
		BetLimits: models.BetLimitConfig{
			MinBet:   100,
			MaxBet:   10000,
			Currency: "USD",
		},
		Timeouts: models.TimeoutConfig{
			WaitingTimeout: 5 * time.Minute,
			PlayingTimeout: 10 * time.Minute,
			IdleTimeout:    30 * time.Minute,
		},
		IsActive:    true,
		CreatedAt:   now,
		ActivatedAt: &now,
	}

	// Add game-specific rules
	switch gameType {
	case models.GameTypePrizeWheel:
		config.Rules["wheel_segments"] = 8
		config.Rules["animation_duration"] = 3000 // milliseconds
	case models.GameTypeAmidakuji:
		config.Rules["ladder_height"] = 10
		config.Rules["animation_duration"] = 5000 // milliseconds
	}

	return config
}
