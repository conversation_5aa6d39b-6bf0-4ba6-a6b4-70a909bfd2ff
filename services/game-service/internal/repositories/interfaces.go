package repositories

import (
	"context"
	"time"

	"github.com/xzgame/game-service/internal/models"
)

// GameRepository defines the interface for game session data operations
type GameRepository interface {
	// Game session operations
	CreateSession(ctx context.Context, session *models.GameSession) error
	GetSession(ctx context.Context, sessionID string) (*models.GameSession, error)
	UpdateSession(ctx context.Context, sessionID string, updates map[string]interface{}) error
	DeleteSession(ctx context.Context, sessionID string) error

	// Session state management
	UpdateSessionStatus(ctx context.Context, sessionID string, status models.SessionStatus) error
	AddPlayerToSession(ctx context.Context, sessionID string, player models.SessionPlayer) error
	RemovePlayerFromSession(ctx context.Context, sessionID string, userID string) error
	UpdatePlayerStatus(ctx context.Context, sessionID string, userID string, status models.PlayerStatus) error

	// Session queries
	GetSessionsByRoom(ctx context.Context, roomID string) ([]*models.GameSession, error)
	GetSessionsByPlayer(ctx context.Context, userID string, limit int, offset int) ([]*models.GameSession, error)
	GetActiveSessionsByPlayer(ctx context.Context, userID string) ([]*models.GameSession, error)
	GetExpiredSessions(ctx context.Context, expiredBefore time.Time) ([]*models.GameSession, error)

	// Game history operations
	CreateGameHistory(ctx context.Context, history *models.GameHistory) error
	GetGameHistory(ctx context.Context, filter models.GameHistoryFilter) ([]*models.GameHistory, error)
	GetGameHistoryByPlayer(ctx context.Context, userID string, gameType *models.GameType, limit int, offset int) ([]*models.GameHistory, error)

	// Atomic operations
	WithTransaction(ctx context.Context, fn func(context.Context) error) error
}

// RoomRepository defines the interface for room data operations
type RoomRepository interface {
	// Room CRUD operations
	CreateRoom(ctx context.Context, room *models.Room) error
	GetRoom(ctx context.Context, roomID string) (*models.Room, error)
	UpdateRoom(ctx context.Context, roomID string, updates map[string]interface{}) error
	DeleteRoom(ctx context.Context, roomID string) error

	// Room player management
	AddPlayerToRoom(ctx context.Context, roomID string, player models.RoomPlayer) error
	RemovePlayerFromRoom(ctx context.Context, roomID string, userID string) error
	UpdatePlayerInRoom(ctx context.Context, roomID string, userID string, updates map[string]interface{}) error

	// Room queries
	ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error)
	GetRoomsByGameType(ctx context.Context, gameType models.GameType) ([]*models.Room, error)
	GetRoomsByStatus(ctx context.Context, status models.RoomStatus) ([]*models.Room, error)
	GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error)
	GetActiveRoomsOptimized(ctx context.Context, gameType *models.GameType, limit int) ([]*models.Room, error)

	// Room statistics
	GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error)
	GetRoomActivity(ctx context.Context, roomID string, since time.Time) (*models.RoomActivity, error)

	// Atomic operations
	WithTransaction(ctx context.Context, fn func(context.Context) error) error
}

// PlayerRepository defines the interface for player data operations
type PlayerRepository interface {
	// Player session tracking
	CreatePlayerSession(ctx context.Context, session *models.PlayerSession) error
	GetPlayerSession(ctx context.Context, userID string) (*models.PlayerSession, error)
	UpdatePlayerSession(ctx context.Context, userID string, updates map[string]interface{}) error
	DeletePlayerSession(ctx context.Context, userID string) error

	// Player status queries
	GetPlayerStatus(ctx context.Context, userID string) (*models.PlayerStatusInfo, error)
	GetPlayersInRoom(ctx context.Context, roomID string) ([]*models.PlayerSession, error)
	GetPlayersInSession(ctx context.Context, sessionID string) ([]*models.PlayerSession, error)

	// Player activity tracking
	UpdatePlayerActivity(ctx context.Context, userID string, activity models.PlayerActivity) error
	GetPlayerActivity(ctx context.Context, userID string, since time.Time) ([]*models.PlayerActivity, error)

	// Cleanup operations
	CleanupExpiredSessions(ctx context.Context, expiredBefore time.Time) error
}

// ConfigRepository defines the interface for game configuration operations
type ConfigRepository interface {
	// Game configuration operations
	CreateGameConfig(ctx context.Context, config *models.GameConfiguration) error
	GetGameConfig(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error)
	UpdateGameConfig(ctx context.Context, gameType models.GameType, updates map[string]interface{}) error
	DeleteGameConfig(ctx context.Context, gameType models.GameType) error

	// Configuration versioning
	GetGameConfigVersion(ctx context.Context, gameType models.GameType, version int) (*models.GameConfiguration, error)
	GetActiveGameConfig(ctx context.Context, gameType models.GameType) (*models.GameConfiguration, error)
	ActivateGameConfig(ctx context.Context, gameType models.GameType, version int) error

	// Configuration queries
	ListGameConfigs(ctx context.Context) ([]*models.GameConfiguration, error)
	GetConfigHistory(ctx context.Context, gameType models.GameType) ([]*models.GameConfiguration, error)
}

// Repository factory interface
type RepositoryFactory interface {
	GameRepository() GameRepository
	RoomRepository() RoomRepository
	PlayerRepository() PlayerRepository
	ConfigRepository() ConfigRepository
	Close() error
}
