package config

import (
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds all configuration for the game service
type Config struct {
	// Server configuration
	Environment  string
	Port         string
	MetricsPort  string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration

	// Database configuration
	MongoURL     string
	RedisURL     string
	DatabaseName string

	// Security configuration
	JWTSecret        string
	JWTIssuer        string
	JWTAudience      []string
	RandomSeedSecret string
	EncryptionKey    string

	// Game configuration
	MaxConcurrentGames     int
	DefaultTimeout         time.Duration
	MaxPlayersPerRoom      int
	SessionCleanupInterval time.Duration
	ArchiveCompletedAfter  time.Duration

	// Redis configuration
	RedisPoolSize int
	RedisTimeout  time.Duration
	RedisTTL      time.Duration

	// Logging configuration
	LogLevel string

	// Monitoring configuration
	MetricsEnabled bool
	TracingEnabled bool

	// Rate limiting configuration
	RateLimitWindow time.Duration
	RateLimitMax    int

	// External services configuration
	ManagerServiceURL      string
	AuthServiceURL         string
	RoomServiceURL         string
	GameEngineServiceURL   string
	NotificationServiceURL string

	// Redis configuration for health checks
	RedisHost string
}

// Load creates a new Config instance with values from environment variables
func Load() *Config {
	return &Config{
		// Server configuration
		Environment:  getEnv("GO_ENV", "development"),
		Port:         getEnv("PORT", "8080"),
		MetricsPort:  getEnv("METRICS_PORT", "9090"),
		ReadTimeout:  getDurationEnv("READ_TIMEOUT", 30*time.Second),
		WriteTimeout: getDurationEnv("WRITE_TIMEOUT", 30*time.Second),

		// Database configuration
		MongoURL:     getEnv("MONGODB_URL", "mongodb://localhost:27017/xzgame"),
		RedisURL:     getEnv("REDIS_URL", "redis://localhost:6379"),
		DatabaseName: getEnv("DATABASE_NAME", "xzgame"),

		// Security configuration
		JWTSecret:        getEnv("JWT_SECRET", "your_jwt_secret_here"),
		JWTIssuer:        getEnv("JWT_ISSUER", "xzgame-auth-service"),
		JWTAudience:      getStringSliceEnv("JWT_AUDIENCE", []string{"xzgame-api", "xzgame-game-service"}),
		RandomSeedSecret: getEnv("RANDOM_SEED_SECRET", "your_random_seed_secret"),
		EncryptionKey:    getEnv("ENCRYPTION_KEY", "your_encryption_key_here"),

		// Game configuration
		MaxConcurrentGames:     getIntEnv("MAX_CONCURRENT_GAMES", 1000),
		DefaultTimeout:         getDurationEnv("DEFAULT_TIMEOUT", 60*time.Second),
		MaxPlayersPerRoom:      getIntEnv("MAX_PLAYERS_PER_ROOM", 8),
		SessionCleanupInterval: getDurationEnv("SESSION_CLEANUP_INTERVAL", 5*time.Minute),
		ArchiveCompletedAfter:  getDurationEnv("ARCHIVE_COMPLETED_AFTER", 24*time.Hour),

		// Redis configuration
		RedisPoolSize: getIntEnv("REDIS_POOL_SIZE", 50),
		RedisTimeout:  getDurationEnv("REDIS_TIMEOUT", 5*time.Second),
		RedisTTL:      getDurationEnv("REDIS_TTL", 30*time.Minute),

		// Logging configuration
		LogLevel: getEnv("LOG_LEVEL", "info"),

		// Monitoring configuration
		MetricsEnabled: getBoolEnv("METRICS_ENABLED", true),
		TracingEnabled: getBoolEnv("TRACING_ENABLED", false),

		// Rate limiting configuration
		RateLimitWindow: getDurationEnv("RATE_LIMIT_WINDOW", 60*time.Second),
		RateLimitMax:    getIntEnv("RATE_LIMIT_MAX", 100),

		// External services configuration
		ManagerServiceURL:      getEnv("MANAGER_SERVICE_URL", "http://localhost:3002"),
		AuthServiceURL:         getEnv("AUTH_SERVICE_URL", "http://localhost:3001"),
		RoomServiceURL:         getEnv("ROOM_SERVICE_URL", "http://localhost:3003"),
		GameEngineServiceURL:   getEnv("GAME_ENGINE_SERVICE_URL", "http://localhost:3004"),
		NotificationServiceURL: getEnv("NOTIFICATION_SERVICE_URL", "http://localhost:3005"),

		// Redis configuration for health checks
		RedisHost: getEnv("REDIS_HOST", "localhost:6379"),
	}
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Add validation logic here
	return nil
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getStringSliceEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
