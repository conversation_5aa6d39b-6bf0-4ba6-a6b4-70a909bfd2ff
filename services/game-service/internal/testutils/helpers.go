package testutils

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/xzgame/game-service/internal/models"
)

// AssertRoomValid asserts that a room is valid
func AssertRoomValid(t *testing.T, room *models.Room) {
	require.NotNil(t, room)
	assert.NotEmpty(t, room.Name)
	assert.NotEmpty(t, room.GameType)
	assert.NotEmpty(t, room.Status)
	assert.GreaterOrEqual(t, room.CurrentPlayers, 0)
	assert.Greater(t, room.MaxPlayers, 0)
	assert.Greater(t, room.MinPlayers, 0)
	assert.LessOrEqual(t, room.MinPlayers, room.MaxPlayers)
	assert.LessOrEqual(t, room.CurrentPlayers, room.MaxPlayers)
	assert.NotNil(t, room.Configuration)
	assert.NotZero(t, room.CreatedAt)
	assert.NotZero(t, room.UpdatedAt)
}

// AssertGameSessionValid asserts that a game session is valid
func AssertGameSessionValid(t *testing.T, session *models.GameSession) {
	require.NotNil(t, session)
	assert.NotEmpty(t, session.RoomID)
	assert.NotEmpty(t, session.GameType)
	assert.NotEmpty(t, session.Status)
	assert.NotNil(t, session.Players)
	assert.NotZero(t, session.StartTime)
	assert.NotNil(t, session.Configuration)
	assert.Greater(t, session.Configuration.MaxPlayers, 0)
	assert.Greater(t, session.Configuration.MinPlayers, 0)
	assert.LessOrEqual(t, session.Configuration.MinPlayers, session.Configuration.MaxPlayers)
	assert.NotZero(t, session.CreatedAt)
	assert.NotZero(t, session.UpdatedAt)
}

// AssertPlayerValid asserts that a session player is valid
func AssertPlayerValid(t *testing.T, player *models.SessionPlayer) {
	require.NotNil(t, player)
	assert.NotEmpty(t, player.UserID)
	assert.Greater(t, player.Position, 0)
	assert.NotEmpty(t, player.Status)
	assert.Greater(t, player.BetAmount, int64(0))
	assert.NotZero(t, player.JoinedAt)
}

// AssertGameResultsValid asserts that game results are valid
func AssertGameResultsValid(t *testing.T, results *models.GameResults) {
	require.NotNil(t, results)
	assert.NotEmpty(t, results.WinnerUserID)
	assert.Greater(t, results.WinnerPosition, 0)
	assert.GreaterOrEqual(t, results.TotalBetPool, int64(0))
	assert.GreaterOrEqual(t, results.HouseTake, int64(0))
	assert.GreaterOrEqual(t, results.PrizePool, int64(0))
	assert.NotNil(t, results.Payouts)
	assert.NotZero(t, results.CompletedAt)

	// Validate that total bet pool = house take + prize pool
	assert.Equal(t, results.TotalBetPool, results.HouseTake+results.PrizePool)
}

// AssertBetLimitsValid asserts that bet limits are valid
func AssertBetLimitsValid(t *testing.T, limits *models.BetLimitConfig) {
	require.NotNil(t, limits)
	assert.Greater(t, limits.MinBet, int64(0))
	assert.Greater(t, limits.MaxBet, int64(0))
	assert.LessOrEqual(t, limits.MinBet, limits.MaxBet)
	assert.NotEmpty(t, limits.Currency)
}

// AssertTimeoutsValid asserts that timeout configuration is valid
func AssertTimeoutsValid(t *testing.T, timeouts *models.TimeoutConfig) {
	require.NotNil(t, timeouts)
	assert.GreaterOrEqual(t, timeouts.WaitingTimeout, time.Duration(0))
	assert.GreaterOrEqual(t, timeouts.PlayingTimeout, time.Duration(0))
	assert.GreaterOrEqual(t, timeouts.IdleTimeout, time.Duration(0))
}

// AssertErrorContains asserts that an error contains a specific string
func AssertErrorContains(t *testing.T, err error, contains string) {
	require.Error(t, err)
	assert.Contains(t, err.Error(), contains)
}

// AssertNoError asserts that there is no error
func AssertNoError(t *testing.T, err error) {
	assert.NoError(t, err)
}

// CreateTestContext creates a test context with timeout
func CreateTestContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 30*time.Second)
}

// CreateTestContextWithDeadline creates a test context with specific deadline
func CreateTestContextWithDeadline(deadline time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), deadline)
}

// WaitForCondition waits for a condition to be true or timeout
func WaitForCondition(t *testing.T, condition func() bool, timeout time.Duration, message string) {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	timeoutChan := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			if condition() {
				return
			}
		case <-timeoutChan:
			t.Fatalf("Timeout waiting for condition: %s", message)
		}
	}
}

// CompareRooms compares two rooms for equality (ignoring timestamps and IDs)
func CompareRooms(t *testing.T, expected, actual *models.Room) {
	assert.Equal(t, expected.Name, actual.Name)
	assert.Equal(t, expected.GameType, actual.GameType)
	assert.Equal(t, expected.Status, actual.Status)
	assert.Equal(t, expected.CurrentPlayers, actual.CurrentPlayers)
	assert.Equal(t, expected.MaxPlayers, actual.MaxPlayers)
	assert.Equal(t, expected.MinPlayers, actual.MinPlayers)
	assert.Equal(t, len(expected.Players), len(actual.Players))

	// Compare configuration
	assert.Equal(t, expected.Configuration.GameType, actual.Configuration.GameType)
	assert.Equal(t, expected.Configuration.AutoStart, actual.Configuration.AutoStart)
	assert.Equal(t, expected.Configuration.IsPrivate, actual.Configuration.IsPrivate)

	// Compare bet limits
	AssertBetLimitsEqual(t, &expected.Configuration.BetLimits, &actual.Configuration.BetLimits)
}

// CompareGameSessions compares two game sessions for equality (ignoring timestamps and IDs)
func CompareGameSessions(t *testing.T, expected, actual *models.GameSession) {
	assert.Equal(t, expected.RoomID, actual.RoomID)
	assert.Equal(t, expected.GameType, actual.GameType)
	assert.Equal(t, expected.Status, actual.Status)
	assert.Equal(t, len(expected.Players), len(actual.Players))

	// Compare configuration
	assert.Equal(t, expected.Configuration.MinPlayers, actual.Configuration.MinPlayers)
	assert.Equal(t, expected.Configuration.MaxPlayers, actual.Configuration.MaxPlayers)

	// Compare bet limits
	AssertBetLimitsEqual(t, &expected.Configuration.BetLimits, &actual.Configuration.BetLimits)
}

// AssertBetLimitsEqual asserts that two bet limit configurations are equal
func AssertBetLimitsEqual(t *testing.T, expected, actual *models.BetLimitConfig) {
	assert.Equal(t, expected.MinBet, actual.MinBet)
	assert.Equal(t, expected.MaxBet, actual.MaxBet)
	assert.Equal(t, expected.Currency, actual.Currency)
}

// AssertPlayersEqual asserts that two player slices are equal
func AssertPlayersEqual(t *testing.T, expected, actual []models.SessionPlayer) {
	assert.Equal(t, len(expected), len(actual))

	for i, expectedPlayer := range expected {
		if i < len(actual) {
			actualPlayer := actual[i]
			assert.Equal(t, expectedPlayer.UserID, actualPlayer.UserID)
			assert.Equal(t, expectedPlayer.Position, actualPlayer.Position)
			assert.Equal(t, expectedPlayer.Status, actualPlayer.Status)
			assert.Equal(t, expectedPlayer.BetAmount, actualPlayer.BetAmount)
		}
	}
}

// AssertRoomPlayersEqual asserts that two room player slices are equal
func AssertRoomPlayersEqual(t *testing.T, expected, actual []models.RoomPlayer) {
	assert.Equal(t, len(expected), len(actual))

	for i, expectedPlayer := range expected {
		if i < len(actual) {
			actualPlayer := actual[i]
			assert.Equal(t, expectedPlayer.UserID, actualPlayer.UserID)
			assert.Equal(t, expectedPlayer.Position, actualPlayer.Position)
			assert.Equal(t, expectedPlayer.Status, actualPlayer.Status)
		}
	}
}

// GenerateUniqueUserID generates a unique user ID for testing
func GenerateUniqueUserID(prefix string) string {
	return prefix + "_" + time.Now().Format("20060102150405") + "_" + RandomString(6)
}

// GenerateUniqueRoomID generates a unique room ID for testing
func GenerateUniqueRoomID(prefix string) string {
	return prefix + "_" + time.Now().Format("20060102150405") + "_" + RandomString(6)
}

// RandomString generates a random string of specified length
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// IsValidGameType checks if a game type is valid
func IsValidGameType(gameType models.GameType) bool {
	switch gameType {
	case models.GameTypePrizeWheel, models.GameTypeAmidakuji:
		return true
	default:
		return false
	}
}

// IsValidRoomStatus checks if a room status is valid
func IsValidRoomStatus(status models.RoomStatus) bool {
	switch status {
	case models.RoomStatusWaiting, models.RoomStatusActive, models.RoomStatusFull, models.RoomStatusClosed, models.RoomStatusArchived:
		return true
	default:
		return false
	}
}

// IsValidSessionStatus checks if a session status is valid
func IsValidSessionStatus(status models.SessionStatus) bool {
	switch status {
	case models.SessionStatusWaiting, models.SessionStatusActive, models.SessionStatusCompleted, models.SessionStatusCancelled:
		return true
	default:
		return false
	}
}

// IsValidPlayerStatus checks if a player status is valid
func IsValidPlayerStatus(status models.PlayerStatus) bool {
	switch status {
	case models.PlayerStatusJoined, models.PlayerStatusReady, models.PlayerStatusPlaying, models.PlayerStatusFinished:
		return true
	default:
		return false
	}
}

// MockTime returns a fixed time for testing
func MockTime() time.Time {
	return time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
}

// MockTimePtr returns a pointer to a fixed time for testing
func MockTimePtr() *time.Time {
	t := MockTime()
	return &t
}
