package testutils

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/xzgame/game-service/internal/models"
)

// CreateTestRoom creates a test room with default values
func CreateTestRoom() *models.Room {
	return &models.Room{
		ID:             primitive.NewObjectID(),
		Name:           "Test Room",
		GameType:       models.GameTypePrizeWheel,
		Status:         models.RoomStatusWaiting,
		CurrentPlayers: 0,
		MaxPlayers:     8,
		MinPlayers:     2,
		Players:        []models.RoomPlayer{},
		Configuration: models.RoomConfiguration{
			GameType: models.GameTypePrizeWheel,
			BetLimits: models.BetLimitConfig{
				MinBet:   10,
				MaxBet:   1000,
				Currency: "USD",
			},
			Timeouts: models.TimeoutConfig{
				WaitingTimeout: 30 * time.Second,
				PlayingTimeout: 60 * time.Second,
				IdleTimeout:    300 * time.Second,
			},
			AutoStart: true,
			IsPrivate: false,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// CreateTestRoomWithPlayers creates a test room with specified number of players
func CreateTestRoomWithPlayers(playerCount int) *models.Room {
	room := CreateTestRoom()
	room.CurrentPlayers = playerCount

	players := make([]models.RoomPlayer, playerCount)
	for i := 0; i < playerCount; i++ {
		players[i] = models.RoomPlayer{
			UserID:   "user" + string(rune(i+1)),
			Position: i + 1,
			Status:   models.PlayerStatusJoined,
			JoinedAt: time.Now(),
		}
	}
	room.Players = players

	return room
}

// CreateTestGameSession creates a test game session with default values
func CreateTestGameSession() *models.GameSession {
	return &models.GameSession{
		ID:       primitive.NewObjectID(),
		RoomID:   "room123",
		GameType: models.GameTypePrizeWheel,
		Status:   models.SessionStatusWaiting,
		Players:  []models.SessionPlayer{},
		StartTime: time.Now(),
		Configuration: models.SessionConfiguration{
			MinPlayers: 2,
			MaxPlayers: 8,
			BetLimits: models.BetLimitConfig{
				MinBet:   10,
				MaxBet:   1000,
				Currency: "USD",
			},
			Timeouts: models.TimeoutConfig{
				WaitingTimeout: 30 * time.Second,
				PlayingTimeout: 60 * time.Second,
				IdleTimeout:    300 * time.Second,
			},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// CreateTestGameSessionWithPlayers creates a test game session with specified players
func CreateTestGameSessionWithPlayers(playerCount int) *models.GameSession {
	session := CreateTestGameSession()

	players := make([]models.SessionPlayer, playerCount)
	for i := 0; i < playerCount; i++ {
		players[i] = models.SessionPlayer{
			UserID:    "user" + string(rune(i+1)),
			Position:  i + 1,
			Status:    models.PlayerStatusJoined,
			BetAmount: 100,
			JoinedAt:  time.Now(),
		}
	}
	session.Players = players

	return session
}

// CreateTestActiveGameSession creates an active game session
func CreateTestActiveGameSession() *models.GameSession {
	session := CreateTestGameSessionWithPlayers(4)
	session.Status = models.SessionStatusActive
	session.StartTime = time.Now()

	// Set all players to playing status
	for i := range session.Players {
		session.Players[i].Status = models.PlayerStatusPlaying
	}

	return session
}

// CreateTestGameResults creates test game results
func CreateTestGameResults() *models.GameResults {
	return &models.GameResults{
		WinnerUserID:   "user1",
		WinnerPosition: 1,
		TotalBetPool:   400,
		HouseTake:      20,
		PrizePool:      380,
		Payouts: []models.PlayerPayout{
			{
				UserID: "user1",
				Amount: 380,
				Reason: "winner",
			},
		},
		GameData: map[string]interface{}{
			"wheel_position": 45,
			"spin_duration":  3.5,
		},
		CompletedAt: time.Now(),
	}
}

// CreateTestGameHistory creates test game history
func CreateTestGameHistory() *models.GameHistory {
	return &models.GameHistory{
		ID:        primitive.NewObjectID(),
		SessionID: "session123",
		RoomID:    "room123",
		GameType:  models.GameTypePrizeWheel,
		Players: []models.SessionPlayer{
			{
				UserID:    "user1",
				Position:  1,
				Status:    models.PlayerStatusFinished,
				BetAmount: 100,
				JoinedAt:  time.Now().Add(-10 * time.Minute),
			},
			{
				UserID:    "user2",
				Position:  2,
				Status:    models.PlayerStatusFinished,
				BetAmount: 100,
				JoinedAt:  time.Now().Add(-10 * time.Minute),
			},
		},
		Results: *CreateTestGameResults(),
		FairnessProof: models.FairnessProof{
			Seed:         "server_seed_123",
			Algorithm:    "sha256",
			Timestamp:    time.Now(),
			Hash:         "hash_789",
			Verification: "https://verify.example.com/hash_789",
			PublicData:   "client_seed_456",
		},
		Duration:    5 * time.Minute,
		CompletedAt: time.Now(),
	}
}

// CreateTestPlayerSession creates a test player session
func CreateTestPlayerSession() *models.PlayerSession {
	return &models.PlayerSession{
		UserID:       "user123",
		SessionID:    "session456",
		RoomID:       "room789",
		Status:       "active",
		LastPing:     time.Now(),
		ConnectionID: "conn_123",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

// CreateTestPlayerActivity creates test player activity
func CreateTestPlayerActivity() *models.PlayerActivity {
	return &models.PlayerActivity{
		UserID:       "user123",
		ActivityType: "join_room",
		RoomID:       stringPtr("room456"),
		SessionID:    stringPtr("session789"),
		Data: map[string]interface{}{
			"bet_amount": 100,
			"position":   1,
		},
		Timestamp: time.Now(),
	}
}

// CreateTestRoomStats creates test room statistics
func CreateTestRoomStats() *models.RoomStats {
	lastGameAt := time.Now().Add(-1 * time.Hour)
	return &models.RoomStats{
		RoomID:          "room123",
		TotalGames:      25,
		TotalPlayers:    100,
		AverageGameTime: 4 * time.Minute,
		TotalBetVolume:  50000,
		LastGameAt:      &lastGameAt,
	}
}

// CreateTestGameConfiguration creates test game configuration
func CreateTestGameConfiguration() *models.GameConfiguration {
	return &models.GameConfiguration{
		GameType: models.GameTypePrizeWheel,
		Version:  1,
		Rules: map[string]interface{}{
			"wheel_segments": 36,
			"spin_duration":  3.0,
		},
		Payouts: []models.PayoutRule{
			{
				Condition:   "winner",
				Multiplier:  0.95,
				Probability: 1.0,
				Description: "Winner takes the prize pool",
			},
		},
		HouseEdge: 0.05, // 5%
		MinPlayers: 2,
		MaxPlayers: 8,
		BetLimits: models.BetLimitConfig{
			MinBet:   10,
			MaxBet:   1000,
			Currency: "USD",
		},
		Timeouts: models.TimeoutConfig{
			WaitingTimeout: 30 * time.Second,
			PlayingTimeout: 60 * time.Second,
			IdleTimeout:    300 * time.Second,
		},
		IsActive:  true,
		CreatedAt: time.Now(),
	}
}

// CreateTestSystemConfiguration creates test system configuration
func CreateTestSystemConfiguration() map[string]interface{} {
	return map[string]interface{}{
		"maintenance_mode": false,
		"max_concurrent_games": 100,
		"default_currency": "USD",
		"supported_currencies": []string{"USD", "EUR", "GBP"},
		"rate_limits": map[string]int{
			"create_room": 10,
			"join_room":   20,
			"start_game":  5,
		},
		"feature_flags": map[string]bool{
			"new_game_types": true,
			"tournament_mode": false,
		},
	}
}

// CreateTestCreateRoomRequest creates a test create room request
func CreateTestCreateRoomRequest() models.CreateRoomRequest {
	return models.CreateRoomRequest{
		Name:        "Test Room",
		GameType:    models.GameTypePrizeWheel,
		MaxPlayers:  8,
		MinPlayers:  2,

		BetLimits: models.BetLimitConfig{
			MinBet:   10,
			MaxBet:   1000,
			Currency: "USD",
		},
		IsPrivate: false,
		Password:  "",
		GameSpecific: map[string]interface{}{
			"auto_start": true,
		},
	}
}

// CreateTestJoinRoomRequest creates a test join room request
func CreateTestJoinRoomRequest() models.JoinRoomRequest {
	return models.JoinRoomRequest{
		RoomID:    "room123",
		UserID:    "user456",
		BetAmount: 100,
		Password:  "",
	}
}

// CreateTestLeaveRoomRequest creates a test leave room request
func CreateTestLeaveRoomRequest() models.LeaveRoomRequest {
	return models.LeaveRoomRequest{
		RoomID: "room123",
		UserID: "user456",
	}
}

// CreateTestRoomListFilter creates a test room list filter
func CreateTestRoomListFilter() models.RoomListFilter {
	gameType := models.GameTypePrizeWheel
	status := models.RoomStatusWaiting

	return models.RoomListFilter{
		GameType: &gameType,
		Status:   &status,
		HasSpace: true,
		Page:     1,
		Limit:    20,
	}
}

// CreateTestGameHistoryFilter creates a test game history filter
func CreateTestGameHistoryFilter() models.GameHistoryFilter {
	gameType := models.GameTypePrizeWheel
	userID := "user123"
	now := time.Now()
	yesterday := now.Add(-24 * time.Hour)

	return models.GameHistoryFilter{
		GameType:     &gameType,
		PlayerID:     &userID,
		DateFrom:     &yesterday,
		DateTo:       &now,
		MinBetAmount: int64Ptr(10),
		MaxBetAmount: int64Ptr(1000),
		Page:         1,
		Limit:        20,
	}
}

// Helper functions

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// int64Ptr returns a pointer to an int64
func int64Ptr(i int64) *int64 {
	return &i
}

// timePtr returns a pointer to a time.Time
func timePtr(t time.Time) *time.Time {
	return &t
}
