syntax = "proto3";

package gameservice;

option go_package = "github.com/xzgame/game-service/proto";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// GameService handles core game operations
service GameService {
  // Game session management
  rpc CreateRoom(CreateRoomRequest) returns (CreateRoomResponse);
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
  rpc StartGame(StartGameRequest) returns (StartGameResponse);
  rpc GetGameState(GetGameStateRequest) returns (GetGameStateResponse);
  rpc GetGameHistory(GetGameHistoryRequest) returns (GetGameHistoryResponse);

  // Player management
  rpc SetPlayerReady(SetPlayerReadyRequest) returns (SetPlayerReadyResponse);
  rpc GetPlayerStatus(GetPlayerStatusRequest) returns (GetPlayerStatusResponse);
  rpc RemovePlayer(RemovePlayerRequest) returns (RemovePlayerResponse);
  rpc ReassignPlayerPositions(ReassignPlayerPositionsRequest) returns (ReassignPlayerPositionsResponse);

  // Cache management
  rpc InvalidateRoomCache(InvalidateRoomCacheRequest) returns (InvalidateRoomCacheResponse);
  rpc InvalidatePlayerCache(InvalidatePlayerCacheRequest) returns (InvalidatePlayerCacheResponse);

  // Subscription management
  rpc ManageRoomSubscription(ManageRoomSubscriptionRequest) returns (ManageRoomSubscriptionResponse);

  // Game configuration
  rpc GetGameConfiguration(GetGameConfigurationRequest) returns (GetGameConfigurationResponse);
  rpc UpdateGameConfiguration(UpdateGameConfigurationRequest) returns (UpdateGameConfigurationResponse);
}

// RoomService handles room management operations
service RoomService {
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse);
  rpc GetRoom(GetRoomRequest) returns (GetRoomResponse);
  rpc UpdateRoom(UpdateRoomRequest) returns (UpdateRoomResponse);
  rpc DeleteRoom(DeleteRoomRequest) returns (DeleteRoomResponse);
  rpc GetRoomStats(GetRoomStatsRequest) returns (GetRoomStatsResponse);
}

// Enums
enum GameType {
  GAME_TYPE_UNSPECIFIED = 0;
  GAME_TYPE_PRIZEWHEEL = 1;
  GAME_TYPE_AMIDAKUJI = 2;
}

enum RoomStatus {
  ROOM_STATUS_UNSPECIFIED = 0;
  ROOM_STATUS_WAITING = 1;
  ROOM_STATUS_ACTIVE = 2;
  ROOM_STATUS_FULL = 3;
  ROOM_STATUS_CLOSED = 4;
  ROOM_STATUS_ARCHIVED = 5;
}

enum SessionStatus {
  SESSION_STATUS_UNSPECIFIED = 0;
  SESSION_STATUS_PENDING = 1;
  SESSION_STATUS_WAITING = 2;
  SESSION_STATUS_ACTIVE = 3;
  SESSION_STATUS_COMPLETED = 4;
  SESSION_STATUS_CANCELLED = 5;
}

enum PlayerStatus {
  PLAYER_STATUS_UNSPECIFIED = 0;
  PLAYER_STATUS_JOINED = 1;
  PLAYER_STATUS_READY = 2;
  PLAYER_STATUS_PLAYING = 3;
  PLAYER_STATUS_FINISHED = 4;
}

// Common message types
message BetLimits {
  int64 min_bet = 1;
  int64 max_bet = 2;
  string currency = 3;
}

message Timeouts {
  int64 waiting_timeout_seconds = 1;
  int64 playing_timeout_seconds = 2;
  int64 idle_timeout_seconds = 3;
}

message Player {
  string user_id = 1;
  string username = 2;
  int32 position = 3;
  bool is_ready = 4;
  int64 bet_amount = 5;
  PlayerStatus status = 6;
  google.protobuf.Timestamp joined_at = 7;
}

message GameResults {
  string winner_user_id = 1;
  int32 winner_position = 2;
  int64 total_bet_pool = 3;
  int64 house_take = 4;
  int64 prize_pool = 5;
  repeated PlayerPayout payouts = 6;
  map<string, string> game_data = 7;
  google.protobuf.Timestamp completed_at = 8;
}

message PlayerPayout {
  string user_id = 1;
  int64 amount = 2;
  string reason = 3;
  bool processed = 4;
}

message FairnessProof {
  string seed = 1;
  string algorithm = 2;
  google.protobuf.Timestamp timestamp = 3;
  string hash = 4;
  string verification = 5;
  string public_data = 6;
}

message Room {
  string id = 1;
  string name = 2;
  GameType game_type = 3;
  RoomStatus status = 4;
  int32 current_players = 5;
  int32 max_players = 6;
  int32 min_players = 7;
  repeated Player players = 8;
  RoomConfiguration configuration = 9;
  string current_session = 10;
  google.protobuf.Timestamp last_activity = 11;
  string created_by = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
}

message RoomConfiguration {
  GameType game_type = 1;
  BetLimits bet_limits = 2;
  Timeouts timeouts = 3;
  bool auto_start = 4;
  bool is_private = 5;
  string password = 6;
  map<string, string> game_specific = 7;
}

message GameSession {
  string id = 1;
  string room_id = 2;
  GameType game_type = 3;
  SessionStatus status = 4;
  repeated Player players = 5;
  google.protobuf.Timestamp start_time = 6;
  google.protobuf.Timestamp end_time = 7;
  GameResults results = 8;
  SessionConfiguration configuration = 9;
  FairnessProof fairness_proof = 10;
  int64 version = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

message SessionConfiguration {
  int32 min_players = 1;
  int32 max_players = 2;
  BetLimits bet_limits = 3;
  Timeouts timeouts = 4;
  map<string, string> game_specific = 5;
}

// Request/Response messages for GameService

message CreateRoomRequest {
  string name = 1;
  GameType game_type = 2;
  int32 max_players = 3;
  int32 min_players = 4;
  BetLimits bet_limits = 5;
  bool auto_start = 6;
  bool is_private = 7;
  string password = 8;
  map<string, string> game_specific = 9;
  string user_id = 10; // Creator's user ID
}

message CreateRoomResponse {
  Room room = 1;
  bool success = 2;
  string message = 3;
}

message JoinRoomRequest {
  string room_id = 1;
  string user_id = 2;
  string password = 3;
  int64 bet_amount = 4;
}

message JoinRoomResponse {
  Room room = 1;
  bool success = 2;
  string message = 3;
}

message LeaveRoomRequest {
  string room_id = 1;
  string user_id = 2;
}

message LeaveRoomResponse {
  bool success = 1;
  string message = 2;
}

message StartGameRequest {
  string room_id = 1;
  string user_id = 2; // User requesting to start (must be in room)
}

message StartGameResponse {
  GameSession session = 1;
  bool success = 2;
  string message = 3;
}

message GetGameStateRequest {
  string session_id = 1;
  string user_id = 2;
}

message GetGameStateResponse {
  GameSession session = 1;
  map<string, string> game_data = 2;
  bool success = 3;
  string message = 4;
}

message GetGameHistoryRequest {
  string user_id = 1;
  GameType game_type = 2;
  int32 page = 3;
  int32 limit = 4;
}

message GetGameHistoryResponse {
  repeated GameSession sessions = 1;
  Pagination pagination = 2;
  bool success = 3;
  string message = 4;
}

message SetPlayerReadyRequest {
  string room_id = 1;
  string user_id = 2;
  bool ready = 3;
}

message SetPlayerReadyResponse {
  bool success = 1;
  string message = 2;
}

message GetPlayerStatusRequest {
  string user_id = 1;
}

message GetPlayerStatusResponse {
  string current_room_id = 1;
  string current_session_id = 2;
  PlayerStatus status = 3;
  bool success = 4;
  string message = 5;
}

message GetGameConfigurationRequest {
  GameType game_type = 1;
}

message GetGameConfigurationResponse {
  GameConfiguration configuration = 1;
  bool success = 2;
  string message = 3;
}

message UpdateGameConfigurationRequest {
  GameType game_type = 1;
  GameConfiguration configuration = 2;
  string admin_user_id = 3;
}

message UpdateGameConfigurationResponse {
  bool success = 1;
  string message = 2;
}

message GameConfiguration {
  string id = 1;
  GameType game_type = 2;
  int32 version = 3;
  map<string, string> rules = 4;
  repeated PayoutRule payouts = 5;
  double house_edge = 6;
  int32 min_players = 7;
  int32 max_players = 8;
  BetLimits bet_limits = 9;
  Timeouts timeouts = 10;
  bool is_active = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp activated_at = 13;
}

message PayoutRule {
  string condition = 1;
  double multiplier = 2;
  double probability = 3;
  string description = 4;
}

// Request/Response messages for RoomService

message ListRoomsRequest {
  GameType game_type = 1;
  RoomStatus status = 2;
  bool has_space = 3;
  int32 page = 4;
  int32 limit = 5;
}

message ListRoomsResponse {
  repeated Room rooms = 1;
  Pagination pagination = 2;
  bool success = 3;
  string message = 4;
}

message GetRoomRequest {
  string room_id = 1;
}

message GetRoomResponse {
  Room room = 1;
  bool success = 2;
  string message = 3;
}

message UpdateRoomRequest {
  string room_id = 1;
  string name = 2;
  int32 max_players = 3;
  BetLimits bet_limits = 4;
  bool auto_start = 5;
  map<string, string> game_specific = 6;
  string admin_user_id = 7;
}

message UpdateRoomResponse {
  Room room = 1;
  bool success = 2;
  string message = 3;
}

message DeleteRoomRequest {
  string room_id = 1;
  string admin_user_id = 2;
}

message DeleteRoomResponse {
  bool success = 1;
  string message = 2;
}

message GetRoomStatsRequest {
  string room_id = 1;
}

message GetRoomStatsResponse {
  RoomStats stats = 1;
  bool success = 2;
  string message = 3;
}

message RoomStats {
  string room_id = 1;
  int32 total_games = 2;
  int32 total_players = 3;
  int64 average_game_time_seconds = 4;
  int64 total_bet_volume = 5;
  google.protobuf.Timestamp last_game_at = 6;
}

message Pagination {
  int32 current_page = 1;
  int32 per_page = 2;
  int64 total_count = 3;
  int32 total_pages = 4;
}

// New message types for enhanced player management

message RemovePlayerRequest {
  string room_id = 1;
  string user_id = 2;
  string admin_user_id = 3; // User performing the removal
  string reason = 4; // Reason for removal (kicked, left, etc.)
}

message RemovePlayerResponse {
  bool success = 1;
  string message = 2;
  Room updated_room = 3;
}

message ReassignPlayerPositionsRequest {
  string room_id = 1;
  repeated string user_ids = 2; // Order determines new positions
}

message ReassignPlayerPositionsResponse {
  bool success = 1;
  string message = 2;
  Room updated_room = 3;
}

// Cache invalidation messages

message InvalidateRoomCacheRequest {
  string room_id = 1;
  repeated string cache_keys = 2; // Specific cache keys to invalidate
}

message InvalidateRoomCacheResponse {
  bool success = 1;
  string message = 2;
  int32 invalidated_count = 3;
}

message InvalidatePlayerCacheRequest {
  string user_id = 1;
  string room_id = 2;
  repeated string cache_keys = 3;
}

message InvalidatePlayerCacheResponse {
  bool success = 1;
  string message = 2;
  int32 invalidated_count = 3;
}

// Subscription management messages

enum SubscriptionAction {
  SUBSCRIPTION_ACTION_UNSPECIFIED = 0;
  SUBSCRIPTION_ACTION_SUBSCRIBE = 1;
  SUBSCRIPTION_ACTION_UNSUBSCRIBE = 2;
  SUBSCRIPTION_ACTION_SWITCH = 3; // Switch from one channel to another
}

enum ChannelType {
  CHANNEL_TYPE_UNSPECIFIED = 0;
  CHANNEL_TYPE_LOBBY = 1;
  CHANNEL_TYPE_ROOM = 2;
}

message ManageRoomSubscriptionRequest {
  string user_id = 1;
  string socket_id = 2;
  SubscriptionAction action = 3;
  ChannelType from_channel = 4; // For switch operations
  ChannelType to_channel = 5;   // For switch operations
  string room_id = 6;           // For room subscriptions
}

message ManageRoomSubscriptionResponse {
  bool success = 1;
  string message = 2;
  string current_channel = 3;
}
