// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/game_service.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type GameType int32

const (
	GameType_GAME_TYPE_UNSPECIFIED GameType = 0
	GameType_GAME_TYPE_PRIZEWHEEL  GameType = 1
	GameType_GAME_TYPE_AMIDAKUJI   GameType = 2
)

// Enum value maps for GameType.
var (
	GameType_name = map[int32]string{
		0: "GAME_TYPE_UNSPECIFIED",
		1: "GAME_TYPE_PRIZEWHEEL",
		2: "GAME_TYPE_AMIDAKUJI",
	}
	GameType_value = map[string]int32{
		"GAME_TYPE_UNSPECIFIED": 0,
		"GAME_TYPE_PRIZEWHEEL":  1,
		"GAME_TYPE_AMIDAKUJI":   2,
	}
)

func (x GameType) Enum() *GameType {
	p := new(GameType)
	*p = x
	return p
}

func (x GameType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_service_proto_enumTypes[0].Descriptor()
}

func (GameType) Type() protoreflect.EnumType {
	return &file_proto_game_service_proto_enumTypes[0]
}

func (x GameType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameType.Descriptor instead.
func (GameType) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{0}
}

type RoomStatus int32

const (
	RoomStatus_ROOM_STATUS_UNSPECIFIED RoomStatus = 0
	RoomStatus_ROOM_STATUS_WAITING     RoomStatus = 1
	RoomStatus_ROOM_STATUS_ACTIVE      RoomStatus = 2
	RoomStatus_ROOM_STATUS_FULL        RoomStatus = 3
	RoomStatus_ROOM_STATUS_CLOSED      RoomStatus = 4
	RoomStatus_ROOM_STATUS_ARCHIVED    RoomStatus = 5
)

// Enum value maps for RoomStatus.
var (
	RoomStatus_name = map[int32]string{
		0: "ROOM_STATUS_UNSPECIFIED",
		1: "ROOM_STATUS_WAITING",
		2: "ROOM_STATUS_ACTIVE",
		3: "ROOM_STATUS_FULL",
		4: "ROOM_STATUS_CLOSED",
		5: "ROOM_STATUS_ARCHIVED",
	}
	RoomStatus_value = map[string]int32{
		"ROOM_STATUS_UNSPECIFIED": 0,
		"ROOM_STATUS_WAITING":     1,
		"ROOM_STATUS_ACTIVE":      2,
		"ROOM_STATUS_FULL":        3,
		"ROOM_STATUS_CLOSED":      4,
		"ROOM_STATUS_ARCHIVED":    5,
	}
)

func (x RoomStatus) Enum() *RoomStatus {
	p := new(RoomStatus)
	*p = x
	return p
}

func (x RoomStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoomStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_service_proto_enumTypes[1].Descriptor()
}

func (RoomStatus) Type() protoreflect.EnumType {
	return &file_proto_game_service_proto_enumTypes[1]
}

func (x RoomStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoomStatus.Descriptor instead.
func (RoomStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{1}
}

type SessionStatus int32

const (
	SessionStatus_SESSION_STATUS_UNSPECIFIED SessionStatus = 0
	SessionStatus_SESSION_STATUS_PENDING     SessionStatus = 1
	SessionStatus_SESSION_STATUS_WAITING     SessionStatus = 2
	SessionStatus_SESSION_STATUS_ACTIVE      SessionStatus = 3
	SessionStatus_SESSION_STATUS_COMPLETED   SessionStatus = 4
	SessionStatus_SESSION_STATUS_CANCELLED   SessionStatus = 5
)

// Enum value maps for SessionStatus.
var (
	SessionStatus_name = map[int32]string{
		0: "SESSION_STATUS_UNSPECIFIED",
		1: "SESSION_STATUS_PENDING",
		2: "SESSION_STATUS_WAITING",
		3: "SESSION_STATUS_ACTIVE",
		4: "SESSION_STATUS_COMPLETED",
		5: "SESSION_STATUS_CANCELLED",
	}
	SessionStatus_value = map[string]int32{
		"SESSION_STATUS_UNSPECIFIED": 0,
		"SESSION_STATUS_PENDING":     1,
		"SESSION_STATUS_WAITING":     2,
		"SESSION_STATUS_ACTIVE":      3,
		"SESSION_STATUS_COMPLETED":   4,
		"SESSION_STATUS_CANCELLED":   5,
	}
)

func (x SessionStatus) Enum() *SessionStatus {
	p := new(SessionStatus)
	*p = x
	return p
}

func (x SessionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_service_proto_enumTypes[2].Descriptor()
}

func (SessionStatus) Type() protoreflect.EnumType {
	return &file_proto_game_service_proto_enumTypes[2]
}

func (x SessionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionStatus.Descriptor instead.
func (SessionStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{2}
}

type PlayerStatus int32

const (
	PlayerStatus_PLAYER_STATUS_UNSPECIFIED PlayerStatus = 0
	PlayerStatus_PLAYER_STATUS_JOINED      PlayerStatus = 1
	PlayerStatus_PLAYER_STATUS_READY       PlayerStatus = 2
	PlayerStatus_PLAYER_STATUS_PLAYING     PlayerStatus = 3
	PlayerStatus_PLAYER_STATUS_FINISHED    PlayerStatus = 4
)

// Enum value maps for PlayerStatus.
var (
	PlayerStatus_name = map[int32]string{
		0: "PLAYER_STATUS_UNSPECIFIED",
		1: "PLAYER_STATUS_JOINED",
		2: "PLAYER_STATUS_READY",
		3: "PLAYER_STATUS_PLAYING",
		4: "PLAYER_STATUS_FINISHED",
	}
	PlayerStatus_value = map[string]int32{
		"PLAYER_STATUS_UNSPECIFIED": 0,
		"PLAYER_STATUS_JOINED":      1,
		"PLAYER_STATUS_READY":       2,
		"PLAYER_STATUS_PLAYING":     3,
		"PLAYER_STATUS_FINISHED":    4,
	}
)

func (x PlayerStatus) Enum() *PlayerStatus {
	p := new(PlayerStatus)
	*p = x
	return p
}

func (x PlayerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_game_service_proto_enumTypes[3].Descriptor()
}

func (PlayerStatus) Type() protoreflect.EnumType {
	return &file_proto_game_service_proto_enumTypes[3]
}

func (x PlayerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerStatus.Descriptor instead.
func (PlayerStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{3}
}

// Common message types
type BetLimits struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MinBet        int64                  `protobuf:"varint,1,opt,name=min_bet,json=minBet,proto3" json:"min_bet,omitempty"`
	MaxBet        int64                  `protobuf:"varint,2,opt,name=max_bet,json=maxBet,proto3" json:"max_bet,omitempty"`
	Currency      string                 `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BetLimits) Reset() {
	*x = BetLimits{}
	mi := &file_proto_game_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BetLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BetLimits) ProtoMessage() {}

func (x *BetLimits) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BetLimits.ProtoReflect.Descriptor instead.
func (*BetLimits) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{0}
}

func (x *BetLimits) GetMinBet() int64 {
	if x != nil {
		return x.MinBet
	}
	return 0
}

func (x *BetLimits) GetMaxBet() int64 {
	if x != nil {
		return x.MaxBet
	}
	return 0
}

func (x *BetLimits) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type Timeouts struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	WaitingTimeoutSeconds int64                  `protobuf:"varint,1,opt,name=waiting_timeout_seconds,json=waitingTimeoutSeconds,proto3" json:"waiting_timeout_seconds,omitempty"`
	PlayingTimeoutSeconds int64                  `protobuf:"varint,2,opt,name=playing_timeout_seconds,json=playingTimeoutSeconds,proto3" json:"playing_timeout_seconds,omitempty"`
	IdleTimeoutSeconds    int64                  `protobuf:"varint,3,opt,name=idle_timeout_seconds,json=idleTimeoutSeconds,proto3" json:"idle_timeout_seconds,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Timeouts) Reset() {
	*x = Timeouts{}
	mi := &file_proto_game_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Timeouts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timeouts) ProtoMessage() {}

func (x *Timeouts) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timeouts.ProtoReflect.Descriptor instead.
func (*Timeouts) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{1}
}

func (x *Timeouts) GetWaitingTimeoutSeconds() int64 {
	if x != nil {
		return x.WaitingTimeoutSeconds
	}
	return 0
}

func (x *Timeouts) GetPlayingTimeoutSeconds() int64 {
	if x != nil {
		return x.PlayingTimeoutSeconds
	}
	return 0
}

func (x *Timeouts) GetIdleTimeoutSeconds() int64 {
	if x != nil {
		return x.IdleTimeoutSeconds
	}
	return 0
}

type Player struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Position      int32                  `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
	IsReady       bool                   `protobuf:"varint,4,opt,name=is_ready,json=isReady,proto3" json:"is_ready,omitempty"`
	BetAmount     int64                  `protobuf:"varint,5,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Status        PlayerStatus           `protobuf:"varint,6,opt,name=status,proto3,enum=gameservice.PlayerStatus" json:"status,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Player) Reset() {
	*x = Player{}
	mi := &file_proto_game_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Player) ProtoMessage() {}

func (x *Player) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Player.ProtoReflect.Descriptor instead.
func (*Player) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{2}
}

func (x *Player) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Player) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Player) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Player) GetIsReady() bool {
	if x != nil {
		return x.IsReady
	}
	return false
}

func (x *Player) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Player) GetStatus() PlayerStatus {
	if x != nil {
		return x.Status
	}
	return PlayerStatus_PLAYER_STATUS_UNSPECIFIED
}

func (x *Player) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

type GameResults struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	WinnerUserId   string                 `protobuf:"bytes,1,opt,name=winner_user_id,json=winnerUserId,proto3" json:"winner_user_id,omitempty"`
	WinnerPosition int32                  `protobuf:"varint,2,opt,name=winner_position,json=winnerPosition,proto3" json:"winner_position,omitempty"`
	TotalBetPool   int64                  `protobuf:"varint,3,opt,name=total_bet_pool,json=totalBetPool,proto3" json:"total_bet_pool,omitempty"`
	HouseTake      int64                  `protobuf:"varint,4,opt,name=house_take,json=houseTake,proto3" json:"house_take,omitempty"`
	PrizePool      int64                  `protobuf:"varint,5,opt,name=prize_pool,json=prizePool,proto3" json:"prize_pool,omitempty"`
	Payouts        []*PlayerPayout        `protobuf:"bytes,6,rep,name=payouts,proto3" json:"payouts,omitempty"`
	GameData       map[string]string      `protobuf:"bytes,7,rep,name=game_data,json=gameData,proto3" json:"game_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CompletedAt    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GameResults) Reset() {
	*x = GameResults{}
	mi := &file_proto_game_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameResults) ProtoMessage() {}

func (x *GameResults) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameResults.ProtoReflect.Descriptor instead.
func (*GameResults) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{3}
}

func (x *GameResults) GetWinnerUserId() string {
	if x != nil {
		return x.WinnerUserId
	}
	return ""
}

func (x *GameResults) GetWinnerPosition() int32 {
	if x != nil {
		return x.WinnerPosition
	}
	return 0
}

func (x *GameResults) GetTotalBetPool() int64 {
	if x != nil {
		return x.TotalBetPool
	}
	return 0
}

func (x *GameResults) GetHouseTake() int64 {
	if x != nil {
		return x.HouseTake
	}
	return 0
}

func (x *GameResults) GetPrizePool() int64 {
	if x != nil {
		return x.PrizePool
	}
	return 0
}

func (x *GameResults) GetPayouts() []*PlayerPayout {
	if x != nil {
		return x.Payouts
	}
	return nil
}

func (x *GameResults) GetGameData() map[string]string {
	if x != nil {
		return x.GameData
	}
	return nil
}

func (x *GameResults) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

type PlayerPayout struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Processed     bool                   `protobuf:"varint,4,opt,name=processed,proto3" json:"processed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerPayout) Reset() {
	*x = PlayerPayout{}
	mi := &file_proto_game_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerPayout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerPayout) ProtoMessage() {}

func (x *PlayerPayout) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerPayout.ProtoReflect.Descriptor instead.
func (*PlayerPayout) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{4}
}

func (x *PlayerPayout) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PlayerPayout) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PlayerPayout) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *PlayerPayout) GetProcessed() bool {
	if x != nil {
		return x.Processed
	}
	return false
}

type FairnessProof struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Seed          string                 `protobuf:"bytes,1,opt,name=seed,proto3" json:"seed,omitempty"`
	Algorithm     string                 `protobuf:"bytes,2,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Hash          string                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	Verification  string                 `protobuf:"bytes,5,opt,name=verification,proto3" json:"verification,omitempty"`
	PublicData    string                 `protobuf:"bytes,6,opt,name=public_data,json=publicData,proto3" json:"public_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FairnessProof) Reset() {
	*x = FairnessProof{}
	mi := &file_proto_game_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FairnessProof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FairnessProof) ProtoMessage() {}

func (x *FairnessProof) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FairnessProof.ProtoReflect.Descriptor instead.
func (*FairnessProof) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{5}
}

func (x *FairnessProof) GetSeed() string {
	if x != nil {
		return x.Seed
	}
	return ""
}

func (x *FairnessProof) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *FairnessProof) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *FairnessProof) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *FairnessProof) GetVerification() string {
	if x != nil {
		return x.Verification
	}
	return ""
}

func (x *FairnessProof) GetPublicData() string {
	if x != nil {
		return x.PublicData
	}
	return ""
}

type Room struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GameType       GameType               `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Status         RoomStatus             `protobuf:"varint,4,opt,name=status,proto3,enum=gameservice.RoomStatus" json:"status,omitempty"`
	CurrentPlayers int32                  `protobuf:"varint,5,opt,name=current_players,json=currentPlayers,proto3" json:"current_players,omitempty"`
	MaxPlayers     int32                  `protobuf:"varint,6,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	MinPlayers     int32                  `protobuf:"varint,7,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	Players        []*Player              `protobuf:"bytes,8,rep,name=players,proto3" json:"players,omitempty"`
	Configuration  *RoomConfiguration     `protobuf:"bytes,9,opt,name=configuration,proto3" json:"configuration,omitempty"`
	CurrentSession string                 `protobuf:"bytes,10,opt,name=current_session,json=currentSession,proto3" json:"current_session,omitempty"`
	LastActivity   *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=last_activity,json=lastActivity,proto3" json:"last_activity,omitempty"`
	CreatedBy      string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Room) Reset() {
	*x = Room{}
	mi := &file_proto_game_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Room) ProtoMessage() {}

func (x *Room) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Room.ProtoReflect.Descriptor instead.
func (*Room) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{6}
}

func (x *Room) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Room) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Room) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *Room) GetStatus() RoomStatus {
	if x != nil {
		return x.Status
	}
	return RoomStatus_ROOM_STATUS_UNSPECIFIED
}

func (x *Room) GetCurrentPlayers() int32 {
	if x != nil {
		return x.CurrentPlayers
	}
	return 0
}

func (x *Room) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *Room) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *Room) GetPlayers() []*Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *Room) GetConfiguration() *RoomConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *Room) GetCurrentSession() string {
	if x != nil {
		return x.CurrentSession
	}
	return ""
}

func (x *Room) GetLastActivity() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivity
	}
	return nil
}

func (x *Room) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Room) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Room) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type RoomConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	BetLimits     *BetLimits             `protobuf:"bytes,2,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	Timeouts      *Timeouts              `protobuf:"bytes,3,opt,name=timeouts,proto3" json:"timeouts,omitempty"`
	AutoStart     bool                   `protobuf:"varint,4,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	IsPrivate     bool                   `protobuf:"varint,5,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Password      string                 `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,7,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomConfiguration) Reset() {
	*x = RoomConfiguration{}
	mi := &file_proto_game_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomConfiguration) ProtoMessage() {}

func (x *RoomConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomConfiguration.ProtoReflect.Descriptor instead.
func (*RoomConfiguration) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{7}
}

func (x *RoomConfiguration) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *RoomConfiguration) GetBetLimits() *BetLimits {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *RoomConfiguration) GetTimeouts() *Timeouts {
	if x != nil {
		return x.Timeouts
	}
	return nil
}

func (x *RoomConfiguration) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *RoomConfiguration) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *RoomConfiguration) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RoomConfiguration) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

type GameSession struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RoomId        string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	GameType      GameType               `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Status        SessionStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=gameservice.SessionStatus" json:"status,omitempty"`
	Players       []*Player              `protobuf:"bytes,5,rep,name=players,proto3" json:"players,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Results       *GameResults           `protobuf:"bytes,8,opt,name=results,proto3" json:"results,omitempty"`
	Configuration *SessionConfiguration  `protobuf:"bytes,9,opt,name=configuration,proto3" json:"configuration,omitempty"`
	FairnessProof *FairnessProof         `protobuf:"bytes,10,opt,name=fairness_proof,json=fairnessProof,proto3" json:"fairness_proof,omitempty"`
	Version       int64                  `protobuf:"varint,11,opt,name=version,proto3" json:"version,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameSession) Reset() {
	*x = GameSession{}
	mi := &file_proto_game_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameSession) ProtoMessage() {}

func (x *GameSession) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameSession.ProtoReflect.Descriptor instead.
func (*GameSession) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{8}
}

func (x *GameSession) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GameSession) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GameSession) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *GameSession) GetStatus() SessionStatus {
	if x != nil {
		return x.Status
	}
	return SessionStatus_SESSION_STATUS_UNSPECIFIED
}

func (x *GameSession) GetPlayers() []*Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *GameSession) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GameSession) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GameSession) GetResults() *GameResults {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GameSession) GetConfiguration() *SessionConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *GameSession) GetFairnessProof() *FairnessProof {
	if x != nil {
		return x.FairnessProof
	}
	return nil
}

func (x *GameSession) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *GameSession) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GameSession) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SessionConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MinPlayers    int32                  `protobuf:"varint,1,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,2,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	BetLimits     *BetLimits             `protobuf:"bytes,3,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	Timeouts      *Timeouts              `protobuf:"bytes,4,opt,name=timeouts,proto3" json:"timeouts,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,5,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionConfiguration) Reset() {
	*x = SessionConfiguration{}
	mi := &file_proto_game_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionConfiguration) ProtoMessage() {}

func (x *SessionConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionConfiguration.ProtoReflect.Descriptor instead.
func (*SessionConfiguration) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{9}
}

func (x *SessionConfiguration) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *SessionConfiguration) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *SessionConfiguration) GetBetLimits() *BetLimits {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *SessionConfiguration) GetTimeouts() *Timeouts {
	if x != nil {
		return x.Timeouts
	}
	return nil
}

func (x *SessionConfiguration) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

type CreateRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,3,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	MinPlayers    int32                  `protobuf:"varint,4,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	BetLimits     *BetLimits             `protobuf:"bytes,5,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	AutoStart     bool                   `protobuf:"varint,6,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	IsPrivate     bool                   `protobuf:"varint,7,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Password      string                 `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,9,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	UserId        string                 `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Creator's user ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomRequest) Reset() {
	*x = CreateRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomRequest) ProtoMessage() {}

func (x *CreateRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomRequest.ProtoReflect.Descriptor instead.
func (*CreateRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateRoomRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRoomRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *CreateRoomRequest) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *CreateRoomRequest) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *CreateRoomRequest) GetBetLimits() *BetLimits {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *CreateRoomRequest) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *CreateRoomRequest) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *CreateRoomRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateRoomRequest) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

func (x *CreateRoomRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type CreateRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomResponse) Reset() {
	*x = CreateRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomResponse) ProtoMessage() {}

func (x *CreateRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomResponse.ProtoReflect.Descriptor instead.
func (*CreateRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *CreateRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type JoinRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	BetAmount     int64                  `protobuf:"varint,4,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinRoomRequest) Reset() {
	*x = JoinRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRoomRequest) ProtoMessage() {}

func (x *JoinRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRoomRequest.ProtoReflect.Descriptor instead.
func (*JoinRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{12}
}

func (x *JoinRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *JoinRoomRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *JoinRoomRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *JoinRoomRequest) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type JoinRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinRoomResponse) Reset() {
	*x = JoinRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRoomResponse) ProtoMessage() {}

func (x *JoinRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRoomResponse.ProtoReflect.Descriptor instead.
func (*JoinRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{13}
}

func (x *JoinRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *JoinRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *JoinRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type LeaveRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveRoomRequest) Reset() {
	*x = LeaveRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomRequest) ProtoMessage() {}

func (x *LeaveRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomRequest.ProtoReflect.Descriptor instead.
func (*LeaveRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{14}
}

func (x *LeaveRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *LeaveRoomRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type LeaveRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveRoomResponse) Reset() {
	*x = LeaveRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomResponse) ProtoMessage() {}

func (x *LeaveRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomResponse.ProtoReflect.Descriptor instead.
func (*LeaveRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{15}
}

func (x *LeaveRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *LeaveRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StartGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // User requesting to start (must be in room)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameRequest) Reset() {
	*x = StartGameRequest{}
	mi := &file_proto_game_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameRequest) ProtoMessage() {}

func (x *StartGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameRequest.ProtoReflect.Descriptor instead.
func (*StartGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{16}
}

func (x *StartGameRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *StartGameRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type StartGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameResponse) Reset() {
	*x = StartGameResponse{}
	mi := &file_proto_game_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameResponse) ProtoMessage() {}

func (x *StartGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameResponse.ProtoReflect.Descriptor instead.
func (*StartGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{17}
}

func (x *StartGameResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *StartGameResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StartGameResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetGameStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStateRequest) Reset() {
	*x = GetGameStateRequest{}
	mi := &file_proto_game_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStateRequest) ProtoMessage() {}

func (x *GetGameStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStateRequest.ProtoReflect.Descriptor instead.
func (*GetGameStateRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetGameStateRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *GetGameStateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetGameStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *GameSession           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	GameData      map[string]string      `protobuf:"bytes,2,rep,name=game_data,json=gameData,proto3" json:"game_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameStateResponse) Reset() {
	*x = GetGameStateResponse{}
	mi := &file_proto_game_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameStateResponse) ProtoMessage() {}

func (x *GetGameStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameStateResponse.ProtoReflect.Descriptor instead.
func (*GetGameStateResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetGameStateResponse) GetSession() *GameSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *GetGameStateResponse) GetGameData() map[string]string {
	if x != nil {
		return x.GameData
	}
	return nil
}

func (x *GetGameStateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetGameStateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetGameHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameHistoryRequest) Reset() {
	*x = GetGameHistoryRequest{}
	mi := &file_proto_game_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameHistoryRequest) ProtoMessage() {}

func (x *GetGameHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetGameHistoryRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetGameHistoryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetGameHistoryRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *GetGameHistoryRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetGameHistoryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetGameHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sessions      []*GameSession         `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameHistoryResponse) Reset() {
	*x = GetGameHistoryResponse{}
	mi := &file_proto_game_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameHistoryResponse) ProtoMessage() {}

func (x *GetGameHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetGameHistoryResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetGameHistoryResponse) GetSessions() []*GameSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *GetGameHistoryResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetGameHistoryResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetGameHistoryResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SetPlayerReadyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Ready         bool                   `protobuf:"varint,3,opt,name=ready,proto3" json:"ready,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPlayerReadyRequest) Reset() {
	*x = SetPlayerReadyRequest{}
	mi := &file_proto_game_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPlayerReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerReadyRequest) ProtoMessage() {}

func (x *SetPlayerReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerReadyRequest.ProtoReflect.Descriptor instead.
func (*SetPlayerReadyRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{22}
}

func (x *SetPlayerReadyRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SetPlayerReadyRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SetPlayerReadyRequest) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

type SetPlayerReadyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPlayerReadyResponse) Reset() {
	*x = SetPlayerReadyResponse{}
	mi := &file_proto_game_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPlayerReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerReadyResponse) ProtoMessage() {}

func (x *SetPlayerReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerReadyResponse.ProtoReflect.Descriptor instead.
func (*SetPlayerReadyResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{23}
}

func (x *SetPlayerReadyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SetPlayerReadyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetPlayerStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlayerStatusRequest) Reset() {
	*x = GetPlayerStatusRequest{}
	mi := &file_proto_game_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerStatusRequest) ProtoMessage() {}

func (x *GetPlayerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerStatusRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetPlayerStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetPlayerStatusResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CurrentRoomId    string                 `protobuf:"bytes,1,opt,name=current_room_id,json=currentRoomId,proto3" json:"current_room_id,omitempty"`
	CurrentSessionId string                 `protobuf:"bytes,2,opt,name=current_session_id,json=currentSessionId,proto3" json:"current_session_id,omitempty"`
	Status           PlayerStatus           `protobuf:"varint,3,opt,name=status,proto3,enum=gameservice.PlayerStatus" json:"status,omitempty"`
	Success          bool                   `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	Message          string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetPlayerStatusResponse) Reset() {
	*x = GetPlayerStatusResponse{}
	mi := &file_proto_game_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerStatusResponse) ProtoMessage() {}

func (x *GetPlayerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerStatusResponse.ProtoReflect.Descriptor instead.
func (*GetPlayerStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetPlayerStatusResponse) GetCurrentRoomId() string {
	if x != nil {
		return x.CurrentRoomId
	}
	return ""
}

func (x *GetPlayerStatusResponse) GetCurrentSessionId() string {
	if x != nil {
		return x.CurrentSessionId
	}
	return ""
}

func (x *GetPlayerStatusResponse) GetStatus() PlayerStatus {
	if x != nil {
		return x.Status
	}
	return PlayerStatus_PLAYER_STATUS_UNSPECIFIED
}

func (x *GetPlayerStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetPlayerStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetGameConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameConfigurationRequest) Reset() {
	*x = GetGameConfigurationRequest{}
	mi := &file_proto_game_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameConfigurationRequest) ProtoMessage() {}

func (x *GetGameConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameConfigurationRequest.ProtoReflect.Descriptor instead.
func (*GetGameConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetGameConfigurationRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

type GetGameConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configuration *GameConfiguration     `protobuf:"bytes,1,opt,name=configuration,proto3" json:"configuration,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGameConfigurationResponse) Reset() {
	*x = GetGameConfigurationResponse{}
	mi := &file_proto_game_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGameConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameConfigurationResponse) ProtoMessage() {}

func (x *GetGameConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameConfigurationResponse.ProtoReflect.Descriptor instead.
func (*GetGameConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetGameConfigurationResponse) GetConfiguration() *GameConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *GetGameConfigurationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetGameConfigurationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateGameConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Configuration *GameConfiguration     `protobuf:"bytes,2,opt,name=configuration,proto3" json:"configuration,omitempty"`
	AdminUserId   string                 `protobuf:"bytes,3,opt,name=admin_user_id,json=adminUserId,proto3" json:"admin_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGameConfigurationRequest) Reset() {
	*x = UpdateGameConfigurationRequest{}
	mi := &file_proto_game_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGameConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGameConfigurationRequest) ProtoMessage() {}

func (x *UpdateGameConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGameConfigurationRequest.ProtoReflect.Descriptor instead.
func (*UpdateGameConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateGameConfigurationRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *UpdateGameConfigurationRequest) GetConfiguration() *GameConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *UpdateGameConfigurationRequest) GetAdminUserId() string {
	if x != nil {
		return x.AdminUserId
	}
	return ""
}

type UpdateGameConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGameConfigurationResponse) Reset() {
	*x = UpdateGameConfigurationResponse{}
	mi := &file_proto_game_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGameConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGameConfigurationResponse) ProtoMessage() {}

func (x *UpdateGameConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGameConfigurationResponse.ProtoReflect.Descriptor instead.
func (*UpdateGameConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateGameConfigurationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateGameConfigurationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GameConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Version       int32                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	Rules         map[string]string      `protobuf:"bytes,4,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Payouts       []*PayoutRule          `protobuf:"bytes,5,rep,name=payouts,proto3" json:"payouts,omitempty"`
	HouseEdge     float64                `protobuf:"fixed64,6,opt,name=house_edge,json=houseEdge,proto3" json:"house_edge,omitempty"`
	MinPlayers    int32                  `protobuf:"varint,7,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,8,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	BetLimits     *BetLimits             `protobuf:"bytes,9,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	Timeouts      *Timeouts              `protobuf:"bytes,10,opt,name=timeouts,proto3" json:"timeouts,omitempty"`
	IsActive      bool                   `protobuf:"varint,11,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ActivatedAt   *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=activated_at,json=activatedAt,proto3" json:"activated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameConfiguration) Reset() {
	*x = GameConfiguration{}
	mi := &file_proto_game_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameConfiguration) ProtoMessage() {}

func (x *GameConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameConfiguration.ProtoReflect.Descriptor instead.
func (*GameConfiguration) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{30}
}

func (x *GameConfiguration) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GameConfiguration) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *GameConfiguration) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *GameConfiguration) GetRules() map[string]string {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *GameConfiguration) GetPayouts() []*PayoutRule {
	if x != nil {
		return x.Payouts
	}
	return nil
}

func (x *GameConfiguration) GetHouseEdge() float64 {
	if x != nil {
		return x.HouseEdge
	}
	return 0
}

func (x *GameConfiguration) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *GameConfiguration) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *GameConfiguration) GetBetLimits() *BetLimits {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *GameConfiguration) GetTimeouts() *Timeouts {
	if x != nil {
		return x.Timeouts
	}
	return nil
}

func (x *GameConfiguration) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *GameConfiguration) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GameConfiguration) GetActivatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivatedAt
	}
	return nil
}

type PayoutRule struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Condition     string                 `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	Multiplier    float64                `protobuf:"fixed64,2,opt,name=multiplier,proto3" json:"multiplier,omitempty"`
	Probability   float64                `protobuf:"fixed64,3,opt,name=probability,proto3" json:"probability,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayoutRule) Reset() {
	*x = PayoutRule{}
	mi := &file_proto_game_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayoutRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayoutRule) ProtoMessage() {}

func (x *PayoutRule) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayoutRule.ProtoReflect.Descriptor instead.
func (*PayoutRule) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{31}
}

func (x *PayoutRule) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *PayoutRule) GetMultiplier() float64 {
	if x != nil {
		return x.Multiplier
	}
	return 0
}

func (x *PayoutRule) GetProbability() float64 {
	if x != nil {
		return x.Probability
	}
	return 0
}

func (x *PayoutRule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ListRoomsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=gameservice.GameType" json:"game_type,omitempty"`
	Status        RoomStatus             `protobuf:"varint,2,opt,name=status,proto3,enum=gameservice.RoomStatus" json:"status,omitempty"`
	HasSpace      bool                   `protobuf:"varint,3,opt,name=has_space,json=hasSpace,proto3" json:"has_space,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsRequest) Reset() {
	*x = ListRoomsRequest{}
	mi := &file_proto_game_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsRequest) ProtoMessage() {}

func (x *ListRoomsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsRequest.ProtoReflect.Descriptor instead.
func (*ListRoomsRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{32}
}

func (x *ListRoomsRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *ListRoomsRequest) GetStatus() RoomStatus {
	if x != nil {
		return x.Status
	}
	return RoomStatus_ROOM_STATUS_UNSPECIFIED
}

func (x *ListRoomsRequest) GetHasSpace() bool {
	if x != nil {
		return x.HasSpace
	}
	return false
}

func (x *ListRoomsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRoomsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListRoomsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rooms         []*Room                `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsResponse) Reset() {
	*x = ListRoomsResponse{}
	mi := &file_proto_game_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsResponse) ProtoMessage() {}

func (x *ListRoomsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsResponse.ProtoReflect.Descriptor instead.
func (*ListRoomsResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListRoomsResponse) GetRooms() []*Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *ListRoomsResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListRoomsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ListRoomsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomRequest) Reset() {
	*x = GetRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomRequest) ProtoMessage() {}

func (x *GetRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomRequest.ProtoReflect.Descriptor instead.
func (*GetRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomResponse) Reset() {
	*x = GetRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomResponse) ProtoMessage() {}

func (x *GetRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomResponse.ProtoReflect.Descriptor instead.
func (*GetRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *GetRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,3,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	BetLimits     *BetLimits             `protobuf:"bytes,4,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	AutoStart     bool                   `protobuf:"varint,5,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,6,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	AdminUserId   string                 `protobuf:"bytes,7,opt,name=admin_user_id,json=adminUserId,proto3" json:"admin_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoomRequest) Reset() {
	*x = UpdateRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomRequest) ProtoMessage() {}

func (x *UpdateRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *UpdateRoomRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateRoomRequest) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *UpdateRoomRequest) GetBetLimits() *BetLimits {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *UpdateRoomRequest) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *UpdateRoomRequest) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

func (x *UpdateRoomRequest) GetAdminUserId() string {
	if x != nil {
		return x.AdminUserId
	}
	return ""
}

type UpdateRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoomResponse) Reset() {
	*x = UpdateRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomResponse) ProtoMessage() {}

func (x *UpdateRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomResponse.ProtoReflect.Descriptor instead.
func (*UpdateRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *UpdateRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	AdminUserId   string                 `protobuf:"bytes,2,opt,name=admin_user_id,json=adminUserId,proto3" json:"admin_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomRequest) Reset() {
	*x = DeleteRoomRequest{}
	mi := &file_proto_game_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomRequest) ProtoMessage() {}

func (x *DeleteRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *DeleteRoomRequest) GetAdminUserId() string {
	if x != nil {
		return x.AdminUserId
	}
	return ""
}

type DeleteRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomResponse) Reset() {
	*x = DeleteRoomResponse{}
	mi := &file_proto_game_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomResponse) ProtoMessage() {}

func (x *DeleteRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomResponse.ProtoReflect.Descriptor instead.
func (*DeleteRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteRoomResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetRoomStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomStatsRequest) Reset() {
	*x = GetRoomStatsRequest{}
	mi := &file_proto_game_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomStatsRequest) ProtoMessage() {}

func (x *GetRoomStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomStatsRequest.ProtoReflect.Descriptor instead.
func (*GetRoomStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetRoomStatsRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *RoomStats             `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomStatsResponse) Reset() {
	*x = GetRoomStatsResponse{}
	mi := &file_proto_game_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomStatsResponse) ProtoMessage() {}

func (x *GetRoomStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomStatsResponse.ProtoReflect.Descriptor instead.
func (*GetRoomStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetRoomStatsResponse) GetStats() *RoomStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetRoomStatsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetRoomStatsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RoomStats struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	RoomId                 string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	TotalGames             int32                  `protobuf:"varint,2,opt,name=total_games,json=totalGames,proto3" json:"total_games,omitempty"`
	TotalPlayers           int32                  `protobuf:"varint,3,opt,name=total_players,json=totalPlayers,proto3" json:"total_players,omitempty"`
	AverageGameTimeSeconds int64                  `protobuf:"varint,4,opt,name=average_game_time_seconds,json=averageGameTimeSeconds,proto3" json:"average_game_time_seconds,omitempty"`
	TotalBetVolume         int64                  `protobuf:"varint,5,opt,name=total_bet_volume,json=totalBetVolume,proto3" json:"total_bet_volume,omitempty"`
	LastGameAt             *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_game_at,json=lastGameAt,proto3" json:"last_game_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *RoomStats) Reset() {
	*x = RoomStats{}
	mi := &file_proto_game_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomStats) ProtoMessage() {}

func (x *RoomStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomStats.ProtoReflect.Descriptor instead.
func (*RoomStats) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{42}
}

func (x *RoomStats) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomStats) GetTotalGames() int32 {
	if x != nil {
		return x.TotalGames
	}
	return 0
}

func (x *RoomStats) GetTotalPlayers() int32 {
	if x != nil {
		return x.TotalPlayers
	}
	return 0
}

func (x *RoomStats) GetAverageGameTimeSeconds() int64 {
	if x != nil {
		return x.AverageGameTimeSeconds
	}
	return 0
}

func (x *RoomStats) GetTotalBetVolume() int64 {
	if x != nil {
		return x.TotalBetVolume
	}
	return 0
}

func (x *RoomStats) GetLastGameAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastGameAt
	}
	return nil
}

type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   int32                  `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PerPage       int32                  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	TotalCount    int64                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPages    int32                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_proto_game_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_proto_game_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_proto_game_service_proto_rawDescGZIP(), []int{43}
}

func (x *Pagination) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *Pagination) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *Pagination) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

var File_proto_game_service_proto protoreflect.FileDescriptor

const file_proto_game_service_proto_rawDesc = "" +
	"\n" +
	"\x18proto/game_service.proto\x12\vgameservice\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"Y\n" +
	"\tBetLimits\x12\x17\n" +
	"\amin_bet\x18\x01 \x01(\x03R\x06minBet\x12\x17\n" +
	"\amax_bet\x18\x02 \x01(\x03R\x06maxBet\x12\x1a\n" +
	"\bcurrency\x18\x03 \x01(\tR\bcurrency\"\xac\x01\n" +
	"\bTimeouts\x126\n" +
	"\x17waiting_timeout_seconds\x18\x01 \x01(\x03R\x15waitingTimeoutSeconds\x126\n" +
	"\x17playing_timeout_seconds\x18\x02 \x01(\x03R\x15playingTimeoutSeconds\x120\n" +
	"\x14idle_timeout_seconds\x18\x03 \x01(\x03R\x12idleTimeoutSeconds\"\xff\x01\n" +
	"\x06Player\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bposition\x18\x03 \x01(\x05R\bposition\x12\x19\n" +
	"\bis_ready\x18\x04 \x01(\bR\aisReady\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\x05 \x01(\x03R\tbetAmount\x121\n" +
	"\x06status\x18\x06 \x01(\x0e2\x19.gameservice.PlayerStatusR\x06status\x127\n" +
	"\tjoined_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\"\xb6\x03\n" +
	"\vGameResults\x12$\n" +
	"\x0ewinner_user_id\x18\x01 \x01(\tR\fwinnerUserId\x12'\n" +
	"\x0fwinner_position\x18\x02 \x01(\x05R\x0ewinnerPosition\x12$\n" +
	"\x0etotal_bet_pool\x18\x03 \x01(\x03R\ftotalBetPool\x12\x1d\n" +
	"\n" +
	"house_take\x18\x04 \x01(\x03R\thouseTake\x12\x1d\n" +
	"\n" +
	"prize_pool\x18\x05 \x01(\x03R\tprizePool\x123\n" +
	"\apayouts\x18\x06 \x03(\v2\x19.gameservice.PlayerPayoutR\apayouts\x12C\n" +
	"\tgame_data\x18\a \x03(\v2&.gameservice.GameResults.GameDataEntryR\bgameData\x12=\n" +
	"\fcompleted_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x1a;\n" +
	"\rGameDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\fPlayerPayout\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x03R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x1c\n" +
	"\tprocessed\x18\x04 \x01(\bR\tprocessed\"\xd4\x01\n" +
	"\rFairnessProof\x12\x12\n" +
	"\x04seed\x18\x01 \x01(\tR\x04seed\x12\x1c\n" +
	"\talgorithm\x18\x02 \x01(\tR\talgorithm\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x12\n" +
	"\x04hash\x18\x04 \x01(\tR\x04hash\x12\"\n" +
	"\fverification\x18\x05 \x01(\tR\fverification\x12\x1f\n" +
	"\vpublic_data\x18\x06 \x01(\tR\n" +
	"publicData\"\xee\x04\n" +
	"\x04Room\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x122\n" +
	"\tgame_type\x18\x03 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12/\n" +
	"\x06status\x18\x04 \x01(\x0e2\x17.gameservice.RoomStatusR\x06status\x12'\n" +
	"\x0fcurrent_players\x18\x05 \x01(\x05R\x0ecurrentPlayers\x12\x1f\n" +
	"\vmax_players\x18\x06 \x01(\x05R\n" +
	"maxPlayers\x12\x1f\n" +
	"\vmin_players\x18\a \x01(\x05R\n" +
	"minPlayers\x12-\n" +
	"\aplayers\x18\b \x03(\v2\x13.gameservice.PlayerR\aplayers\x12D\n" +
	"\rconfiguration\x18\t \x01(\v2\x1e.gameservice.RoomConfigurationR\rconfiguration\x12'\n" +
	"\x0fcurrent_session\x18\n" +
	" \x01(\tR\x0ecurrentSession\x12?\n" +
	"\rlast_activity\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\flastActivity\x12\x1d\n" +
	"\n" +
	"created_by\x18\f \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xa3\x03\n" +
	"\x11RoomConfiguration\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x125\n" +
	"\n" +
	"bet_limits\x18\x02 \x01(\v2\x16.gameservice.BetLimitsR\tbetLimits\x121\n" +
	"\btimeouts\x18\x03 \x01(\v2\x15.gameservice.TimeoutsR\btimeouts\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x04 \x01(\bR\tautoStart\x12\x1d\n" +
	"\n" +
	"is_private\x18\x05 \x01(\bR\tisPrivate\x12\x1a\n" +
	"\bpassword\x18\x06 \x01(\tR\bpassword\x12U\n" +
	"\rgame_specific\x18\a \x03(\v20.gameservice.RoomConfiguration.GameSpecificEntryR\fgameSpecific\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8f\x05\n" +
	"\vGameSession\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\aroom_id\x18\x02 \x01(\tR\x06roomId\x122\n" +
	"\tgame_type\x18\x03 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x122\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1a.gameservice.SessionStatusR\x06status\x12-\n" +
	"\aplayers\x18\x05 \x03(\v2\x13.gameservice.PlayerR\aplayers\x129\n" +
	"\n" +
	"start_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x122\n" +
	"\aresults\x18\b \x01(\v2\x18.gameservice.GameResultsR\aresults\x12G\n" +
	"\rconfiguration\x18\t \x01(\v2!.gameservice.SessionConfigurationR\rconfiguration\x12A\n" +
	"\x0efairness_proof\x18\n" +
	" \x01(\v2\x1a.gameservice.FairnessProofR\rfairnessProof\x12\x18\n" +
	"\aversion\x18\v \x01(\x03R\aversion\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xdd\x02\n" +
	"\x14SessionConfiguration\x12\x1f\n" +
	"\vmin_players\x18\x01 \x01(\x05R\n" +
	"minPlayers\x12\x1f\n" +
	"\vmax_players\x18\x02 \x01(\x05R\n" +
	"maxPlayers\x125\n" +
	"\n" +
	"bet_limits\x18\x03 \x01(\v2\x16.gameservice.BetLimitsR\tbetLimits\x121\n" +
	"\btimeouts\x18\x04 \x01(\v2\x15.gameservice.TimeoutsR\btimeouts\x12X\n" +
	"\rgame_specific\x18\x05 \x03(\v23.gameservice.SessionConfiguration.GameSpecificEntryR\fgameSpecific\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xdf\x03\n" +
	"\x11CreateRoomRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x122\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12\x1f\n" +
	"\vmax_players\x18\x03 \x01(\x05R\n" +
	"maxPlayers\x12\x1f\n" +
	"\vmin_players\x18\x04 \x01(\x05R\n" +
	"minPlayers\x125\n" +
	"\n" +
	"bet_limits\x18\x05 \x01(\v2\x16.gameservice.BetLimitsR\tbetLimits\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x06 \x01(\bR\tautoStart\x12\x1d\n" +
	"\n" +
	"is_private\x18\a \x01(\bR\tisPrivate\x12\x1a\n" +
	"\bpassword\x18\b \x01(\tR\bpassword\x12U\n" +
	"\rgame_specific\x18\t \x03(\v20.gameservice.CreateRoomRequest.GameSpecificEntryR\fgameSpecific\x12\x17\n" +
	"\auser_id\x18\n" +
	" \x01(\tR\x06userId\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"o\n" +
	"\x12CreateRoomResponse\x12%\n" +
	"\x04room\x18\x01 \x01(\v2\x11.gameservice.RoomR\x04room\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"~\n" +
	"\x0fJoinRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\x04 \x01(\x03R\tbetAmount\"m\n" +
	"\x10JoinRoomResponse\x12%\n" +
	"\x04room\x18\x01 \x01(\v2\x11.gameservice.RoomR\x04room\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"D\n" +
	"\x10LeaveRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"G\n" +
	"\x11LeaveRoomResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"D\n" +
	"\x10StartGameRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"{\n" +
	"\x11StartGameResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.gameservice.GameSessionR\asession\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"M\n" +
	"\x13GetGameStateRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"\x89\x02\n" +
	"\x14GetGameStateResponse\x122\n" +
	"\asession\x18\x01 \x01(\v2\x18.gameservice.GameSessionR\asession\x12L\n" +
	"\tgame_data\x18\x02 \x03(\v2/.gameservice.GetGameStateResponse.GameDataEntryR\bgameData\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x1a;\n" +
	"\rGameDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8e\x01\n" +
	"\x15GetGameHistoryRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x122\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\x05R\x05limit\"\xbb\x01\n" +
	"\x16GetGameHistoryResponse\x124\n" +
	"\bsessions\x18\x01 \x03(\v2\x18.gameservice.GameSessionR\bsessions\x127\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x17.gameservice.PaginationR\n" +
	"pagination\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"_\n" +
	"\x15SetPlayerReadyRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05ready\x18\x03 \x01(\bR\x05ready\"L\n" +
	"\x16SetPlayerReadyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"1\n" +
	"\x16GetPlayerStatusRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xd6\x01\n" +
	"\x17GetPlayerStatusResponse\x12&\n" +
	"\x0fcurrent_room_id\x18\x01 \x01(\tR\rcurrentRoomId\x12,\n" +
	"\x12current_session_id\x18\x02 \x01(\tR\x10currentSessionId\x121\n" +
	"\x06status\x18\x03 \x01(\x0e2\x19.gameservice.PlayerStatusR\x06status\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\"Q\n" +
	"\x1bGetGameConfigurationRequest\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\"\x98\x01\n" +
	"\x1cGetGameConfigurationResponse\x12D\n" +
	"\rconfiguration\x18\x01 \x01(\v2\x1e.gameservice.GameConfigurationR\rconfiguration\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\xbe\x01\n" +
	"\x1eUpdateGameConfigurationRequest\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12D\n" +
	"\rconfiguration\x18\x02 \x01(\v2\x1e.gameservice.GameConfigurationR\rconfiguration\x12\"\n" +
	"\radmin_user_id\x18\x03 \x01(\tR\vadminUserId\"U\n" +
	"\x1fUpdateGameConfigurationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x81\x05\n" +
	"\x11GameConfiguration\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x122\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12\x18\n" +
	"\aversion\x18\x03 \x01(\x05R\aversion\x12?\n" +
	"\x05rules\x18\x04 \x03(\v2).gameservice.GameConfiguration.RulesEntryR\x05rules\x121\n" +
	"\apayouts\x18\x05 \x03(\v2\x17.gameservice.PayoutRuleR\apayouts\x12\x1d\n" +
	"\n" +
	"house_edge\x18\x06 \x01(\x01R\thouseEdge\x12\x1f\n" +
	"\vmin_players\x18\a \x01(\x05R\n" +
	"minPlayers\x12\x1f\n" +
	"\vmax_players\x18\b \x01(\x05R\n" +
	"maxPlayers\x125\n" +
	"\n" +
	"bet_limits\x18\t \x01(\v2\x16.gameservice.BetLimitsR\tbetLimits\x121\n" +
	"\btimeouts\x18\n" +
	" \x01(\v2\x15.gameservice.TimeoutsR\btimeouts\x12\x1b\n" +
	"\tis_active\x18\v \x01(\bR\bisActive\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12=\n" +
	"\factivated_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\vactivatedAt\x1a8\n" +
	"\n" +
	"RulesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8e\x01\n" +
	"\n" +
	"PayoutRule\x12\x1c\n" +
	"\tcondition\x18\x01 \x01(\tR\tcondition\x12\x1e\n" +
	"\n" +
	"multiplier\x18\x02 \x01(\x01R\n" +
	"multiplier\x12 \n" +
	"\vprobability\x18\x03 \x01(\x01R\vprobability\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"\xbe\x01\n" +
	"\x10ListRoomsRequest\x122\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x15.gameservice.GameTypeR\bgameType\x12/\n" +
	"\x06status\x18\x02 \x01(\x0e2\x17.gameservice.RoomStatusR\x06status\x12\x1b\n" +
	"\thas_space\x18\x03 \x01(\bR\bhasSpace\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x05 \x01(\x05R\x05limit\"\xa9\x01\n" +
	"\x11ListRoomsResponse\x12'\n" +
	"\x05rooms\x18\x01 \x03(\v2\x11.gameservice.RoomR\x05rooms\x127\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x17.gameservice.PaginationR\n" +
	"pagination\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\")\n" +
	"\x0eGetRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\"l\n" +
	"\x0fGetRoomResponse\x12%\n" +
	"\x04room\x18\x01 \x01(\v2\x11.gameservice.RoomR\x04room\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\xf3\x02\n" +
	"\x11UpdateRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vmax_players\x18\x03 \x01(\x05R\n" +
	"maxPlayers\x125\n" +
	"\n" +
	"bet_limits\x18\x04 \x01(\v2\x16.gameservice.BetLimitsR\tbetLimits\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x05 \x01(\bR\tautoStart\x12U\n" +
	"\rgame_specific\x18\x06 \x03(\v20.gameservice.UpdateRoomRequest.GameSpecificEntryR\fgameSpecific\x12\"\n" +
	"\radmin_user_id\x18\a \x01(\tR\vadminUserId\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"o\n" +
	"\x12UpdateRoomResponse\x12%\n" +
	"\x04room\x18\x01 \x01(\v2\x11.gameservice.RoomR\x04room\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"P\n" +
	"\x11DeleteRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\"\n" +
	"\radmin_user_id\x18\x02 \x01(\tR\vadminUserId\"H\n" +
	"\x12DeleteRoomResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\".\n" +
	"\x13GetRoomStatsRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\"x\n" +
	"\x14GetRoomStatsResponse\x12,\n" +
	"\x05stats\x18\x01 \x01(\v2\x16.gameservice.RoomStatsR\x05stats\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\x8d\x02\n" +
	"\tRoomStats\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1f\n" +
	"\vtotal_games\x18\x02 \x01(\x05R\n" +
	"totalGames\x12#\n" +
	"\rtotal_players\x18\x03 \x01(\x05R\ftotalPlayers\x129\n" +
	"\x19average_game_time_seconds\x18\x04 \x01(\x03R\x16averageGameTimeSeconds\x12(\n" +
	"\x10total_bet_volume\x18\x05 \x01(\x03R\x0etotalBetVolume\x12<\n" +
	"\flast_game_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastGameAt\"\x8c\x01\n" +
	"\n" +
	"Pagination\x12!\n" +
	"\fcurrent_page\x18\x01 \x01(\x05R\vcurrentPage\x12\x19\n" +
	"\bper_page\x18\x02 \x01(\x05R\aperPage\x12\x1f\n" +
	"\vtotal_count\x18\x03 \x01(\x03R\n" +
	"totalCount\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x05R\n" +
	"totalPages*X\n" +
	"\bGameType\x12\x19\n" +
	"\x15GAME_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14GAME_TYPE_PRIZEWHEEL\x10\x01\x12\x17\n" +
	"\x13GAME_TYPE_AMIDAKUJI\x10\x02*\xa2\x01\n" +
	"\n" +
	"RoomStatus\x12\x1b\n" +
	"\x17ROOM_STATUS_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13ROOM_STATUS_WAITING\x10\x01\x12\x16\n" +
	"\x12ROOM_STATUS_ACTIVE\x10\x02\x12\x14\n" +
	"\x10ROOM_STATUS_FULL\x10\x03\x12\x16\n" +
	"\x12ROOM_STATUS_CLOSED\x10\x04\x12\x18\n" +
	"\x14ROOM_STATUS_ARCHIVED\x10\x05*\xbe\x01\n" +
	"\rSessionStatus\x12\x1e\n" +
	"\x1aSESSION_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16SESSION_STATUS_PENDING\x10\x01\x12\x1a\n" +
	"\x16SESSION_STATUS_WAITING\x10\x02\x12\x19\n" +
	"\x15SESSION_STATUS_ACTIVE\x10\x03\x12\x1c\n" +
	"\x18SESSION_STATUS_COMPLETED\x10\x04\x12\x1c\n" +
	"\x18SESSION_STATUS_CANCELLED\x10\x05*\x97\x01\n" +
	"\fPlayerStatus\x12\x1d\n" +
	"\x19PLAYER_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14PLAYER_STATUS_JOINED\x10\x01\x12\x17\n" +
	"\x13PLAYER_STATUS_READY\x10\x02\x12\x19\n" +
	"\x15PLAYER_STATUS_PLAYING\x10\x03\x12\x1a\n" +
	"\x16PLAYER_STATUS_FINISHED\x10\x042\x89\a\n" +
	"\vGameService\x12M\n" +
	"\n" +
	"CreateRoom\x12\x1e.gameservice.CreateRoomRequest\x1a\x1f.gameservice.CreateRoomResponse\x12G\n" +
	"\bJoinRoom\x12\x1c.gameservice.JoinRoomRequest\x1a\x1d.gameservice.JoinRoomResponse\x12J\n" +
	"\tLeaveRoom\x12\x1d.gameservice.LeaveRoomRequest\x1a\x1e.gameservice.LeaveRoomResponse\x12J\n" +
	"\tStartGame\x12\x1d.gameservice.StartGameRequest\x1a\x1e.gameservice.StartGameResponse\x12S\n" +
	"\fGetGameState\x12 .gameservice.GetGameStateRequest\x1a!.gameservice.GetGameStateResponse\x12Y\n" +
	"\x0eGetGameHistory\x12\".gameservice.GetGameHistoryRequest\x1a#.gameservice.GetGameHistoryResponse\x12Y\n" +
	"\x0eSetPlayerReady\x12\".gameservice.SetPlayerReadyRequest\x1a#.gameservice.SetPlayerReadyResponse\x12\\\n" +
	"\x0fGetPlayerStatus\x12#.gameservice.GetPlayerStatusRequest\x1a$.gameservice.GetPlayerStatusResponse\x12k\n" +
	"\x14GetGameConfiguration\x12(.gameservice.GetGameConfigurationRequest\x1a).gameservice.GetGameConfigurationResponse\x12t\n" +
	"\x17UpdateGameConfiguration\x12+.gameservice.UpdateGameConfigurationRequest\x1a,.gameservice.UpdateGameConfigurationResponse2\x92\x03\n" +
	"\vRoomService\x12J\n" +
	"\tListRooms\x12\x1d.gameservice.ListRoomsRequest\x1a\x1e.gameservice.ListRoomsResponse\x12D\n" +
	"\aGetRoom\x12\x1b.gameservice.GetRoomRequest\x1a\x1c.gameservice.GetRoomResponse\x12M\n" +
	"\n" +
	"UpdateRoom\x12\x1e.gameservice.UpdateRoomRequest\x1a\x1f.gameservice.UpdateRoomResponse\x12M\n" +
	"\n" +
	"DeleteRoom\x12\x1e.gameservice.DeleteRoomRequest\x1a\x1f.gameservice.DeleteRoomResponse\x12S\n" +
	"\fGetRoomStats\x12 .gameservice.GetRoomStatsRequest\x1a!.gameservice.GetRoomStatsResponseB&Z$github.com/xzgame/game-service/protob\x06proto3"

var (
	file_proto_game_service_proto_rawDescOnce sync.Once
	file_proto_game_service_proto_rawDescData []byte
)

func file_proto_game_service_proto_rawDescGZIP() []byte {
	file_proto_game_service_proto_rawDescOnce.Do(func() {
		file_proto_game_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_game_service_proto_rawDesc), len(file_proto_game_service_proto_rawDesc)))
	})
	return file_proto_game_service_proto_rawDescData
}

var file_proto_game_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_proto_game_service_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_proto_game_service_proto_goTypes = []any{
	(GameType)(0),                           // 0: gameservice.GameType
	(RoomStatus)(0),                         // 1: gameservice.RoomStatus
	(SessionStatus)(0),                      // 2: gameservice.SessionStatus
	(PlayerStatus)(0),                       // 3: gameservice.PlayerStatus
	(*BetLimits)(nil),                       // 4: gameservice.BetLimits
	(*Timeouts)(nil),                        // 5: gameservice.Timeouts
	(*Player)(nil),                          // 6: gameservice.Player
	(*GameResults)(nil),                     // 7: gameservice.GameResults
	(*PlayerPayout)(nil),                    // 8: gameservice.PlayerPayout
	(*FairnessProof)(nil),                   // 9: gameservice.FairnessProof
	(*Room)(nil),                            // 10: gameservice.Room
	(*RoomConfiguration)(nil),               // 11: gameservice.RoomConfiguration
	(*GameSession)(nil),                     // 12: gameservice.GameSession
	(*SessionConfiguration)(nil),            // 13: gameservice.SessionConfiguration
	(*CreateRoomRequest)(nil),               // 14: gameservice.CreateRoomRequest
	(*CreateRoomResponse)(nil),              // 15: gameservice.CreateRoomResponse
	(*JoinRoomRequest)(nil),                 // 16: gameservice.JoinRoomRequest
	(*JoinRoomResponse)(nil),                // 17: gameservice.JoinRoomResponse
	(*LeaveRoomRequest)(nil),                // 18: gameservice.LeaveRoomRequest
	(*LeaveRoomResponse)(nil),               // 19: gameservice.LeaveRoomResponse
	(*StartGameRequest)(nil),                // 20: gameservice.StartGameRequest
	(*StartGameResponse)(nil),               // 21: gameservice.StartGameResponse
	(*GetGameStateRequest)(nil),             // 22: gameservice.GetGameStateRequest
	(*GetGameStateResponse)(nil),            // 23: gameservice.GetGameStateResponse
	(*GetGameHistoryRequest)(nil),           // 24: gameservice.GetGameHistoryRequest
	(*GetGameHistoryResponse)(nil),          // 25: gameservice.GetGameHistoryResponse
	(*SetPlayerReadyRequest)(nil),           // 26: gameservice.SetPlayerReadyRequest
	(*SetPlayerReadyResponse)(nil),          // 27: gameservice.SetPlayerReadyResponse
	(*GetPlayerStatusRequest)(nil),          // 28: gameservice.GetPlayerStatusRequest
	(*GetPlayerStatusResponse)(nil),         // 29: gameservice.GetPlayerStatusResponse
	(*GetGameConfigurationRequest)(nil),     // 30: gameservice.GetGameConfigurationRequest
	(*GetGameConfigurationResponse)(nil),    // 31: gameservice.GetGameConfigurationResponse
	(*UpdateGameConfigurationRequest)(nil),  // 32: gameservice.UpdateGameConfigurationRequest
	(*UpdateGameConfigurationResponse)(nil), // 33: gameservice.UpdateGameConfigurationResponse
	(*GameConfiguration)(nil),               // 34: gameservice.GameConfiguration
	(*PayoutRule)(nil),                      // 35: gameservice.PayoutRule
	(*ListRoomsRequest)(nil),                // 36: gameservice.ListRoomsRequest
	(*ListRoomsResponse)(nil),               // 37: gameservice.ListRoomsResponse
	(*GetRoomRequest)(nil),                  // 38: gameservice.GetRoomRequest
	(*GetRoomResponse)(nil),                 // 39: gameservice.GetRoomResponse
	(*UpdateRoomRequest)(nil),               // 40: gameservice.UpdateRoomRequest
	(*UpdateRoomResponse)(nil),              // 41: gameservice.UpdateRoomResponse
	(*DeleteRoomRequest)(nil),               // 42: gameservice.DeleteRoomRequest
	(*DeleteRoomResponse)(nil),              // 43: gameservice.DeleteRoomResponse
	(*GetRoomStatsRequest)(nil),             // 44: gameservice.GetRoomStatsRequest
	(*GetRoomStatsResponse)(nil),            // 45: gameservice.GetRoomStatsResponse
	(*RoomStats)(nil),                       // 46: gameservice.RoomStats
	(*Pagination)(nil),                      // 47: gameservice.Pagination
	nil,                                     // 48: gameservice.GameResults.GameDataEntry
	nil,                                     // 49: gameservice.RoomConfiguration.GameSpecificEntry
	nil,                                     // 50: gameservice.SessionConfiguration.GameSpecificEntry
	nil,                                     // 51: gameservice.CreateRoomRequest.GameSpecificEntry
	nil,                                     // 52: gameservice.GetGameStateResponse.GameDataEntry
	nil,                                     // 53: gameservice.GameConfiguration.RulesEntry
	nil,                                     // 54: gameservice.UpdateRoomRequest.GameSpecificEntry
	(*timestamppb.Timestamp)(nil),           // 55: google.protobuf.Timestamp
}
var file_proto_game_service_proto_depIdxs = []int32{
	3,  // 0: gameservice.Player.status:type_name -> gameservice.PlayerStatus
	55, // 1: gameservice.Player.joined_at:type_name -> google.protobuf.Timestamp
	8,  // 2: gameservice.GameResults.payouts:type_name -> gameservice.PlayerPayout
	48, // 3: gameservice.GameResults.game_data:type_name -> gameservice.GameResults.GameDataEntry
	55, // 4: gameservice.GameResults.completed_at:type_name -> google.protobuf.Timestamp
	55, // 5: gameservice.FairnessProof.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 6: gameservice.Room.game_type:type_name -> gameservice.GameType
	1,  // 7: gameservice.Room.status:type_name -> gameservice.RoomStatus
	6,  // 8: gameservice.Room.players:type_name -> gameservice.Player
	11, // 9: gameservice.Room.configuration:type_name -> gameservice.RoomConfiguration
	55, // 10: gameservice.Room.last_activity:type_name -> google.protobuf.Timestamp
	55, // 11: gameservice.Room.created_at:type_name -> google.protobuf.Timestamp
	55, // 12: gameservice.Room.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 13: gameservice.RoomConfiguration.game_type:type_name -> gameservice.GameType
	4,  // 14: gameservice.RoomConfiguration.bet_limits:type_name -> gameservice.BetLimits
	5,  // 15: gameservice.RoomConfiguration.timeouts:type_name -> gameservice.Timeouts
	49, // 16: gameservice.RoomConfiguration.game_specific:type_name -> gameservice.RoomConfiguration.GameSpecificEntry
	0,  // 17: gameservice.GameSession.game_type:type_name -> gameservice.GameType
	2,  // 18: gameservice.GameSession.status:type_name -> gameservice.SessionStatus
	6,  // 19: gameservice.GameSession.players:type_name -> gameservice.Player
	55, // 20: gameservice.GameSession.start_time:type_name -> google.protobuf.Timestamp
	55, // 21: gameservice.GameSession.end_time:type_name -> google.protobuf.Timestamp
	7,  // 22: gameservice.GameSession.results:type_name -> gameservice.GameResults
	13, // 23: gameservice.GameSession.configuration:type_name -> gameservice.SessionConfiguration
	9,  // 24: gameservice.GameSession.fairness_proof:type_name -> gameservice.FairnessProof
	55, // 25: gameservice.GameSession.created_at:type_name -> google.protobuf.Timestamp
	55, // 26: gameservice.GameSession.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 27: gameservice.SessionConfiguration.bet_limits:type_name -> gameservice.BetLimits
	5,  // 28: gameservice.SessionConfiguration.timeouts:type_name -> gameservice.Timeouts
	50, // 29: gameservice.SessionConfiguration.game_specific:type_name -> gameservice.SessionConfiguration.GameSpecificEntry
	0,  // 30: gameservice.CreateRoomRequest.game_type:type_name -> gameservice.GameType
	4,  // 31: gameservice.CreateRoomRequest.bet_limits:type_name -> gameservice.BetLimits
	51, // 32: gameservice.CreateRoomRequest.game_specific:type_name -> gameservice.CreateRoomRequest.GameSpecificEntry
	10, // 33: gameservice.CreateRoomResponse.room:type_name -> gameservice.Room
	10, // 34: gameservice.JoinRoomResponse.room:type_name -> gameservice.Room
	12, // 35: gameservice.StartGameResponse.session:type_name -> gameservice.GameSession
	12, // 36: gameservice.GetGameStateResponse.session:type_name -> gameservice.GameSession
	52, // 37: gameservice.GetGameStateResponse.game_data:type_name -> gameservice.GetGameStateResponse.GameDataEntry
	0,  // 38: gameservice.GetGameHistoryRequest.game_type:type_name -> gameservice.GameType
	12, // 39: gameservice.GetGameHistoryResponse.sessions:type_name -> gameservice.GameSession
	47, // 40: gameservice.GetGameHistoryResponse.pagination:type_name -> gameservice.Pagination
	3,  // 41: gameservice.GetPlayerStatusResponse.status:type_name -> gameservice.PlayerStatus
	0,  // 42: gameservice.GetGameConfigurationRequest.game_type:type_name -> gameservice.GameType
	34, // 43: gameservice.GetGameConfigurationResponse.configuration:type_name -> gameservice.GameConfiguration
	0,  // 44: gameservice.UpdateGameConfigurationRequest.game_type:type_name -> gameservice.GameType
	34, // 45: gameservice.UpdateGameConfigurationRequest.configuration:type_name -> gameservice.GameConfiguration
	0,  // 46: gameservice.GameConfiguration.game_type:type_name -> gameservice.GameType
	53, // 47: gameservice.GameConfiguration.rules:type_name -> gameservice.GameConfiguration.RulesEntry
	35, // 48: gameservice.GameConfiguration.payouts:type_name -> gameservice.PayoutRule
	4,  // 49: gameservice.GameConfiguration.bet_limits:type_name -> gameservice.BetLimits
	5,  // 50: gameservice.GameConfiguration.timeouts:type_name -> gameservice.Timeouts
	55, // 51: gameservice.GameConfiguration.created_at:type_name -> google.protobuf.Timestamp
	55, // 52: gameservice.GameConfiguration.activated_at:type_name -> google.protobuf.Timestamp
	0,  // 53: gameservice.ListRoomsRequest.game_type:type_name -> gameservice.GameType
	1,  // 54: gameservice.ListRoomsRequest.status:type_name -> gameservice.RoomStatus
	10, // 55: gameservice.ListRoomsResponse.rooms:type_name -> gameservice.Room
	47, // 56: gameservice.ListRoomsResponse.pagination:type_name -> gameservice.Pagination
	10, // 57: gameservice.GetRoomResponse.room:type_name -> gameservice.Room
	4,  // 58: gameservice.UpdateRoomRequest.bet_limits:type_name -> gameservice.BetLimits
	54, // 59: gameservice.UpdateRoomRequest.game_specific:type_name -> gameservice.UpdateRoomRequest.GameSpecificEntry
	10, // 60: gameservice.UpdateRoomResponse.room:type_name -> gameservice.Room
	46, // 61: gameservice.GetRoomStatsResponse.stats:type_name -> gameservice.RoomStats
	55, // 62: gameservice.RoomStats.last_game_at:type_name -> google.protobuf.Timestamp
	14, // 63: gameservice.GameService.CreateRoom:input_type -> gameservice.CreateRoomRequest
	16, // 64: gameservice.GameService.JoinRoom:input_type -> gameservice.JoinRoomRequest
	18, // 65: gameservice.GameService.LeaveRoom:input_type -> gameservice.LeaveRoomRequest
	20, // 66: gameservice.GameService.StartGame:input_type -> gameservice.StartGameRequest
	22, // 67: gameservice.GameService.GetGameState:input_type -> gameservice.GetGameStateRequest
	24, // 68: gameservice.GameService.GetGameHistory:input_type -> gameservice.GetGameHistoryRequest
	26, // 69: gameservice.GameService.SetPlayerReady:input_type -> gameservice.SetPlayerReadyRequest
	28, // 70: gameservice.GameService.GetPlayerStatus:input_type -> gameservice.GetPlayerStatusRequest
	30, // 71: gameservice.GameService.GetGameConfiguration:input_type -> gameservice.GetGameConfigurationRequest
	32, // 72: gameservice.GameService.UpdateGameConfiguration:input_type -> gameservice.UpdateGameConfigurationRequest
	36, // 73: gameservice.RoomService.ListRooms:input_type -> gameservice.ListRoomsRequest
	38, // 74: gameservice.RoomService.GetRoom:input_type -> gameservice.GetRoomRequest
	40, // 75: gameservice.RoomService.UpdateRoom:input_type -> gameservice.UpdateRoomRequest
	42, // 76: gameservice.RoomService.DeleteRoom:input_type -> gameservice.DeleteRoomRequest
	44, // 77: gameservice.RoomService.GetRoomStats:input_type -> gameservice.GetRoomStatsRequest
	15, // 78: gameservice.GameService.CreateRoom:output_type -> gameservice.CreateRoomResponse
	17, // 79: gameservice.GameService.JoinRoom:output_type -> gameservice.JoinRoomResponse
	19, // 80: gameservice.GameService.LeaveRoom:output_type -> gameservice.LeaveRoomResponse
	21, // 81: gameservice.GameService.StartGame:output_type -> gameservice.StartGameResponse
	23, // 82: gameservice.GameService.GetGameState:output_type -> gameservice.GetGameStateResponse
	25, // 83: gameservice.GameService.GetGameHistory:output_type -> gameservice.GetGameHistoryResponse
	27, // 84: gameservice.GameService.SetPlayerReady:output_type -> gameservice.SetPlayerReadyResponse
	29, // 85: gameservice.GameService.GetPlayerStatus:output_type -> gameservice.GetPlayerStatusResponse
	31, // 86: gameservice.GameService.GetGameConfiguration:output_type -> gameservice.GetGameConfigurationResponse
	33, // 87: gameservice.GameService.UpdateGameConfiguration:output_type -> gameservice.UpdateGameConfigurationResponse
	37, // 88: gameservice.RoomService.ListRooms:output_type -> gameservice.ListRoomsResponse
	39, // 89: gameservice.RoomService.GetRoom:output_type -> gameservice.GetRoomResponse
	41, // 90: gameservice.RoomService.UpdateRoom:output_type -> gameservice.UpdateRoomResponse
	43, // 91: gameservice.RoomService.DeleteRoom:output_type -> gameservice.DeleteRoomResponse
	45, // 92: gameservice.RoomService.GetRoomStats:output_type -> gameservice.GetRoomStatsResponse
	78, // [78:93] is the sub-list for method output_type
	63, // [63:78] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_proto_game_service_proto_init() }
func file_proto_game_service_proto_init() {
	if File_proto_game_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_game_service_proto_rawDesc), len(file_proto_game_service_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_proto_game_service_proto_goTypes,
		DependencyIndexes: file_proto_game_service_proto_depIdxs,
		EnumInfos:         file_proto_game_service_proto_enumTypes,
		MessageInfos:      file_proto_game_service_proto_msgTypes,
	}.Build()
	File_proto_game_service_proto = out.File
	file_proto_game_service_proto_goTypes = nil
	file_proto_game_service_proto_depIdxs = nil
}
