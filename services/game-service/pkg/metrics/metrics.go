package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// GameServiceMetrics holds all Prometheus metrics for the game service
type GameServiceMetrics struct {
	// Database metrics
	DatabaseOperations *prometheus.CounterVec
	DatabaseDuration   *prometheus.HistogramVec
	DatabaseErrors     *prometheus.CounterVec

	// Redis metrics
	RedisOperations *prometheus.CounterVec
	RedisDuration   *prometheus.HistogramVec
	RedisErrors     *prometheus.CounterVec
	RedisPoolSize   *prometheus.GaugeVec

	// Room metrics
	RoomsActive    *prometheus.GaugeVec
	RoomOperations *prometheus.CounterVec
	RoomDuration   *prometheus.HistogramVec
	PlayersInRooms *prometheus.GaugeVec

	// Game metrics
	GamesCreated   prometheus.Counter
	GamesCompleted *prometheus.CounterVec
	GameDuration   *prometheus.HistogramVec
	PlayersActive  *prometheus.GaugeVec

	// Request handler metrics
	RequestsProcessed *prometheus.CounterVec
	RequestDuration   *prometheus.HistogramVec
	RequestErrors     *prometheus.CounterVec
	PendingRequests   prometheus.Gauge

	// Memory and performance metrics
	MemoryUsage    *prometheus.GaugeVec
	GoroutineCount prometheus.Gauge
	GCDuration     prometheus.Histogram
	CPUUsage       prometheus.Gauge

	// External service metrics
	ExternalCalls    *prometheus.CounterVec
	ExternalDuration *prometheus.HistogramVec
	ExternalErrors   *prometheus.CounterVec
}

// NewGameServiceMetrics creates a new metrics instance
func NewGameServiceMetrics() *GameServiceMetrics {
	return &GameServiceMetrics{
		// Database metrics
		DatabaseOperations: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_database_operations_total",
			Help: "Total number of database operations",
		}, []string{"operation", "collection", "status"}),

		DatabaseDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_database_duration_seconds",
			Help:    "Duration of database operations",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5},
		}, []string{"operation", "collection"}),

		DatabaseErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_database_errors_total",
			Help: "Total number of database errors",
		}, []string{"operation", "collection", "error_type"}),

		// Redis metrics
		RedisOperations: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_redis_operations_total",
			Help: "Total number of Redis operations",
		}, []string{"operation", "status"}),

		RedisDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_redis_duration_seconds",
			Help:    "Duration of Redis operations",
			Buckets: []float64{0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1},
		}, []string{"operation"}),

		RedisErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_redis_errors_total",
			Help: "Total number of Redis errors",
		}, []string{"operation", "error_type"}),

		RedisPoolSize: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "game_service_redis_pool_connections",
			Help: "Number of Redis pool connections",
		}, []string{"status"}),

		// Room metrics
		RoomsActive: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "game_service_rooms_active",
			Help: "Number of active rooms",
		}, []string{"game_type", "status"}),

		RoomOperations: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_room_operations_total",
			Help: "Total number of room operations",
		}, []string{"operation", "status"}),

		RoomDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_room_operation_duration_seconds",
			Help:    "Duration of room operations",
			Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10},
		}, []string{"operation"}),

		PlayersInRooms: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "game_service_players_in_rooms",
			Help: "Number of players currently in rooms",
		}, []string{"game_type"}),

		// Game metrics
		GamesCreated: promauto.NewCounter(prometheus.CounterOpts{
			Name: "game_service_games_created_total",
			Help: "Total number of games created",
		}),

		GamesCompleted: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_games_completed_total",
			Help: "Total number of games completed",
		}, []string{"game_type", "status"}),

		GameDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_game_duration_seconds",
			Help:    "Duration of games from start to finish",
			Buckets: []float64{5, 10, 30, 60, 120, 300, 600},
		}, []string{"game_type"}),

		PlayersActive: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "game_service_players_active",
			Help: "Number of active players",
		}, []string{"game_type"}),

		// Request handler metrics
		RequestsProcessed: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_requests_processed_total",
			Help: "Total number of requests processed",
		}, []string{"event_type", "status"}),

		RequestDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_request_duration_seconds",
			Help:    "Duration of request processing",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5},
		}, []string{"event_type"}),

		RequestErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_request_errors_total",
			Help: "Total number of request processing errors",
		}, []string{"event_type", "error_type"}),

		PendingRequests: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "game_service_pending_requests",
			Help: "Number of pending requests",
		}),

		// Memory and performance metrics
		MemoryUsage: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "game_service_memory_usage_bytes",
			Help: "Memory usage in bytes",
		}, []string{"type"}),

		GoroutineCount: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "game_service_goroutines",
			Help: "Number of goroutines",
		}),

		GCDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "game_service_gc_duration_seconds",
			Help:    "Duration of garbage collection",
			Buckets: []float64{0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05, 0.1},
		}),

		CPUUsage: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "game_service_cpu_usage_percent",
			Help: "CPU usage percentage",
		}),

		// External service metrics
		ExternalCalls: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_external_calls_total",
			Help: "Total number of external service calls",
		}, []string{"service", "method", "status"}),

		ExternalDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "game_service_external_call_duration_seconds",
			Help:    "Duration of external service calls",
			Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30},
		}, []string{"service", "method"}),

		ExternalErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "game_service_external_errors_total",
			Help: "Total number of external service errors",
		}, []string{"service", "method", "error_type"}),
	}
}

// RecordDatabaseOperation records a database operation metric
func (m *GameServiceMetrics) RecordDatabaseOperation(operation, collection, status string, duration time.Duration) {
	m.DatabaseOperations.WithLabelValues(operation, collection, status).Inc()
	m.DatabaseDuration.WithLabelValues(operation, collection).Observe(duration.Seconds())
}

// RecordDatabaseError records a database error metric
func (m *GameServiceMetrics) RecordDatabaseError(operation, collection, errorType string) {
	m.DatabaseErrors.WithLabelValues(operation, collection, errorType).Inc()
}

// RecordRedisOperation records a Redis operation metric
func (m *GameServiceMetrics) RecordRedisOperation(operation, status string, duration time.Duration) {
	m.RedisOperations.WithLabelValues(operation, status).Inc()
	m.RedisDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

// RecordRedisError records a Redis error metric
func (m *GameServiceMetrics) RecordRedisError(operation, errorType string) {
	m.RedisErrors.WithLabelValues(operation, errorType).Inc()
}

// RecordRoomOperation records a room operation metric
func (m *GameServiceMetrics) RecordRoomOperation(operation, status string, duration time.Duration) {
	m.RoomOperations.WithLabelValues(operation, status).Inc()
	m.RoomDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

// RecordRequestProcessed records a processed request metric
func (m *GameServiceMetrics) RecordRequestProcessed(eventType, status string, duration time.Duration) {
	m.RequestsProcessed.WithLabelValues(eventType, status).Inc()
	m.RequestDuration.WithLabelValues(eventType).Observe(duration.Seconds())
}

// RecordExternalCall records an external service call metric
func (m *GameServiceMetrics) RecordExternalCall(service, method, status string, duration time.Duration) {
	m.ExternalCalls.WithLabelValues(service, method, status).Inc()
	m.ExternalDuration.WithLabelValues(service, method).Observe(duration.Seconds())
}

// Global metrics instance
var Metrics *GameServiceMetrics

// Initialize initializes the global metrics instance
func Initialize() {
	Metrics = NewGameServiceMetrics()
}
