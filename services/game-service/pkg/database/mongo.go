package database

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

// MongoClient wraps the MongoDB client with additional functionality
type MongoClient struct {
	client   *mongo.Client
	database *mongo.Database
}

// NewMongoClient creates a new MongoDB client connection with optimized settings
func NewMongoClient(mongoURL string) (*MongoClient, error) {
	// Set client options with performance optimizations
	clientOptions := options.Client().ApplyURI(mongoURL)

	// Optimized connection pool settings for high throughput
	clientOptions.SetMaxPoolSize(200)                  // Increased from 100
	clientOptions.SetMinPoolSize(20)                   // Increased from 10
	clientOptions.SetMaxConnIdleTime(15 * time.Minute) // Reduced from 30 minutes
	clientOptions.SetConnectTimeout(10 * time.Second)
	clientOptions.SetServerSelectionTimeout(5 * time.Second)
	clientOptions.SetSocketTimeout(30 * time.Second)     // Add socket timeout
	clientOptions.SetHeartbeatInterval(10 * time.Second) // Add heartbeat interval

	// Enable compression for better network performance
	clientOptions.SetCompressors([]string{"snappy", "zlib"})

	// Set read preference for better load distribution
	clientOptions.SetReadPreference(readpref.SecondaryPreferred())

	// Enable retryable writes for better reliability
	clientOptions.SetRetryWrites(true)
	clientOptions.SetRetryReads(true)

	// Create client
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, err
	}

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		return nil, err
	}

	// Extract database name from URL or use default
	database := client.Database("xzgame")

	// Create MongoDB client wrapper
	mongoClient := &MongoClient{
		client:   client,
		database: database,
	}

	// Initialize database indexes for performance
	if err := mongoClient.ensureIndexes(ctx); err != nil {
		return nil, fmt.Errorf("failed to create database indexes: %w", err)
	}

	return mongoClient, nil
}

// GetDatabase returns the database instance
func (mc *MongoClient) GetDatabase() *mongo.Database {
	return mc.database
}

// GetCollection returns a collection from the database
func (mc *MongoClient) GetCollection(name string) *mongo.Collection {
	return mc.database.Collection(name)
}

// Ping tests the database connection
func (mc *MongoClient) Ping(ctx context.Context) error {
	return mc.client.Ping(ctx, readpref.Primary())
}

// Disconnect closes the database connection
func (mc *MongoClient) Disconnect(ctx context.Context) error {
	return mc.client.Disconnect(ctx)
}

// StartSession starts a new session for transactions
func (mc *MongoClient) StartSession() (mongo.Session, error) {
	return mc.client.StartSession()
}

// WithTransaction executes a function within a transaction
func (mc *MongoClient) WithTransaction(ctx context.Context, fn func(mongo.SessionContext) (interface{}, error)) (interface{}, error) {
	session, err := mc.StartSession()
	if err != nil {
		return nil, err
	}
	defer session.EndSession(ctx)

	return session.WithTransaction(ctx, fn)
}

// Collection names constants
const (
	CollectionUsers              = "users"
	CollectionTransactions       = "transactions"
	CollectionRooms              = "rooms"
	CollectionGameConfigurations = "game_configurations"
	CollectionGameHistory        = "game_history"
	CollectionGameSessions       = "game_sessions"
	CollectionPlayerSessions     = "player_sessions"
	CollectionAuditLogs          = "audit_logs"
)

// ensureIndexes creates necessary indexes for optimal performance (internal method)
func (mc *MongoClient) ensureIndexes(ctx context.Context) error {
	return mc.CreateIndexes(ctx)
}

// CreateIndexes creates necessary indexes for optimal performance
func (mc *MongoClient) CreateIndexes(ctx context.Context) error {
	// Users collection indexes
	usersCollection := mc.GetCollection(CollectionUsers)
	_, err := usersCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "username", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "created_at", Value: 1}},
		},
	})
	if err != nil {
		return err
	}

	// Transactions collection indexes
	transactionsCollection := mc.GetCollection(CollectionTransactions)
	_, err = transactionsCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "user_id", Value: 1}, {Key: "created_at", Value: -1}},
		},
		{
			Keys: bson.D{{Key: "type", Value: 1}, {Key: "created_at", Value: -1}},
		},
		{
			Keys: bson.D{{Key: "status", Value: 1}},
		},
	})
	if err != nil {
		return err
	}

	// Rooms collection indexes
	roomsCollection := mc.GetCollection(CollectionRooms)
	_, err = roomsCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "status", Value: 1}, {Key: "created_at", Value: -1}},
		},
		{
			Keys: bson.D{{Key: "game_type", Value: 1}, {Key: "status", Value: 1}},
		},
		{
			Keys:    bson.D{{Key: "created_at", Value: 1}},
			Options: options.Index().SetExpireAfterSeconds(86400), // 24 hours TTL
		},
	})
	if err != nil {
		return err
	}

	// Game sessions collection indexes
	gameSessionsCollection := mc.GetCollection(CollectionGameSessions)
	_, err = gameSessionsCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "room_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "status", Value: 1}, {Key: "created_at", Value: -1}},
		},
		{
			Keys: bson.D{{Key: "players.user_id", Value: 1}},
		},
		{
			Keys:    bson.D{{Key: "created_at", Value: 1}},
			Options: options.Index().SetExpireAfterSeconds(604800), // 7 days TTL
		},
	})
	if err != nil {
		return err
	}

	// Game history collection indexes
	gameHistoryCollection := mc.GetCollection(CollectionGameHistory)
	_, err = gameHistoryCollection.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "session_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "game_type", Value: 1}, {Key: "completed_at", Value: -1}},
		},
		{
			Keys: bson.D{{Key: "players.user_id", Value: 1}, {Key: "completed_at", Value: -1}},
		},
	})
	if err != nil {
		return err
	}

	return nil
}
