package redis

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v8"
)

// RedisClient wraps the Redis client with additional functionality
type RedisClient struct {
	client *redis.Client
}

// NewRedisClient creates a new Redis client connection with optimized settings
func NewRedisClient(redisURL string) (*RedisClient, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, err
	}

	// Optimized connection pool settings for high throughput
	opt.PoolSize = 100                        // Increased from 50
	opt.MinIdleConns = 20                     // Increased from 10
	opt.MaxConnAge = 15 * time.Minute         // Reduced from 30 minutes
	opt.PoolTimeout = 3 * time.Second         // Reduced from 5 seconds
	opt.IdleTimeout = 3 * time.Minute         // Reduced from 5 minutes
	opt.IdleCheckFrequency = 30 * time.Second // Reduced from 1 minute

	// Add connection retry settings
	opt.MaxRetries = 3
	opt.MinRetryBackoff = 8 * time.Millisecond
	opt.MaxRetryBackoff = 512 * time.Millisecond

	// Add read/write timeouts
	opt.ReadTimeout = 3 * time.Second
	opt.WriteTimeout = 3 * time.Second

	// Enable TCP keepalive
	opt.DialTimeout = 5 * time.Second

	client := redis.NewClient(opt)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	return &RedisClient{
		client: client,
	}, nil
}

// GetClient returns the underlying Redis client
func (rc *RedisClient) GetClient() *redis.Client {
	return rc.client
}

// Ping tests the Redis connection
func (rc *RedisClient) Ping(ctx context.Context) *redis.StatusCmd {
	return rc.client.Ping(ctx)
}

// Close closes the Redis connection
func (rc *RedisClient) Close() error {
	return rc.client.Close()
}

// Cache operations

// Set stores a value in Redis with TTL
func (rc *RedisClient) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return rc.client.Set(ctx, key, data, ttl).Err()
}

// Get retrieves a value from Redis
func (rc *RedisClient) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := rc.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(data), dest)
}

// Delete removes a key from Redis
func (rc *RedisClient) Delete(ctx context.Context, keys ...string) error {
	return rc.client.Del(ctx, keys...).Err()
}

// Exists checks if a key exists in Redis
func (rc *RedisClient) Exists(ctx context.Context, key string) (bool, error) {
	result, err := rc.client.Exists(ctx, key).Result()
	return result > 0, err
}

// SetNX sets a key only if it doesn't exist (atomic operation)
func (rc *RedisClient) SetNX(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return false, err
	}
	return rc.client.SetNX(ctx, key, data, ttl).Result()
}

// Increment atomically increments a counter
func (rc *RedisClient) Increment(ctx context.Context, key string) (int64, error) {
	return rc.client.Incr(ctx, key).Result()
}

// Pub/Sub operations

// Publish publishes a message to a channel
func (rc *RedisClient) Publish(ctx context.Context, channel string, message interface{}) error {
	data, err := json.Marshal(message)
	if err != nil {
		return err
	}
	return rc.client.Publish(ctx, channel, data).Err()
}

// Subscribe subscribes to one or more channels
func (rc *RedisClient) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return rc.client.Subscribe(ctx, channels...)
}

// PSubscribe subscribes to channels matching patterns
func (rc *RedisClient) PSubscribe(ctx context.Context, patterns ...string) *redis.PubSub {
	return rc.client.PSubscribe(ctx, patterns...)
}

// List operations

// ListPush pushes values to the left of a list
func (rc *RedisClient) ListPush(ctx context.Context, key string, values ...interface{}) error {
	return rc.client.LPush(ctx, key, values...).Err()
}

// ListPop pops a value from the right of a list
func (rc *RedisClient) ListPop(ctx context.Context, key string, dest interface{}) error {
	data, err := rc.client.RPop(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(data), dest)
}

// ListLength returns the length of a list
func (rc *RedisClient) ListLength(ctx context.Context, key string) (int64, error) {
	return rc.client.LLen(ctx, key).Result()
}

// Set operations

// SetAdd adds members to a set
func (rc *RedisClient) SetAdd(ctx context.Context, key string, members ...interface{}) error {
	return rc.client.SAdd(ctx, key, members...).Err()
}

// SetRemove removes members from a set
func (rc *RedisClient) SetRemove(ctx context.Context, key string, members ...interface{}) error {
	return rc.client.SRem(ctx, key, members...).Err()
}

// SetMembers returns all members of a set
func (rc *RedisClient) SetMembers(ctx context.Context, key string) ([]string, error) {
	return rc.client.SMembers(ctx, key).Result()
}

// SetIsMember checks if a value is a member of a set
func (rc *RedisClient) SetIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	return rc.client.SIsMember(ctx, key, member).Result()
}

// Hash operations

// HashSet sets fields in a hash
func (rc *RedisClient) HashSet(ctx context.Context, key string, fields map[string]interface{}) error {
	return rc.client.HMSet(ctx, key, fields).Err()
}

// HashGet gets a field from a hash
func (rc *RedisClient) HashGet(ctx context.Context, key, field string) (string, error) {
	return rc.client.HGet(ctx, key, field).Result()
}

// HashGetAll gets all fields from a hash
func (rc *RedisClient) HashGetAll(ctx context.Context, key string) (map[string]string, error) {
	return rc.client.HGetAll(ctx, key).Result()
}

// HashDelete deletes fields from a hash
func (rc *RedisClient) HashDelete(ctx context.Context, key string, fields ...string) error {
	return rc.client.HDel(ctx, key, fields...).Err()
}

// Batch operations for performance optimization

// BatchSet performs multiple SET operations in a pipeline
func (rc *RedisClient) BatchSet(ctx context.Context, keyValues map[string]interface{}, ttl time.Duration) error {
	pipe := rc.client.Pipeline()

	for key, value := range keyValues {
		data, err := json.Marshal(value)
		if err != nil {
			return err
		}
		pipe.Set(ctx, key, data, ttl)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// BatchGet performs multiple GET operations in a pipeline
func (rc *RedisClient) BatchGet(ctx context.Context, keys []string) (map[string]string, error) {
	pipe := rc.client.Pipeline()

	// Create commands for each key
	cmds := make(map[string]*redis.StringCmd)
	for _, key := range keys {
		cmds[key] = pipe.Get(ctx, key)
	}

	// Execute pipeline
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	// Collect results
	results := make(map[string]string)
	for key, cmd := range cmds {
		val, err := cmd.Result()
		if err == nil {
			results[key] = val
		}
		// Ignore redis.Nil errors for individual keys
	}

	return results, nil
}

// BatchDelete performs multiple DELETE operations in a pipeline
func (rc *RedisClient) BatchDelete(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	pipe := rc.client.Pipeline()
	for _, key := range keys {
		pipe.Del(ctx, key)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// Cache invalidation patterns

// InvalidatePattern deletes all keys matching a pattern
func (rc *RedisClient) InvalidatePattern(ctx context.Context, pattern string) error {
	keys, err := rc.client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return rc.BatchDelete(ctx, keys)
}

// GetOrSet implements cache-aside pattern
func (rc *RedisClient) GetOrSet(ctx context.Context, key string, ttl time.Duration, fetchFunc func() (interface{}, error), dest interface{}) error {
	// Try to get from cache first
	err := rc.Get(ctx, key, dest)
	if err == nil {
		return nil // Cache hit
	}

	if err != redis.Nil {
		return err // Real error
	}

	// Cache miss - fetch data
	value, err := fetchFunc()
	if err != nil {
		return err
	}

	// Store in cache
	if err := rc.Set(ctx, key, value, ttl); err != nil {
		// Log error but don't fail the request
		// In production, you might want to use a logger here
	}

	// Marshal the value to dest
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, dest)
}

// Redis key patterns for the game service
const (
	// Cache keys
	KeyGameConfig    = "game:config:%s"    // game type
	KeyRoomState     = "room:state:%s"     // room ID
	KeyPlayerSession = "player:session:%s" // user ID
	KeyGameSession   = "game:session:%s"   // session ID

	// Pub/Sub channels
	ChannelGameRoom   = "game:room:%s" // room ID
	ChannelGameGlobal = "game:global"
	ChannelAdmin      = "admin:notifications"

	// Rate limiting keys
	KeyRateLimit = "rate_limit:%s:%s" // endpoint:user_id

	// Lock keys
	KeyLockRoom = "lock:room:%s" // room ID
	KeyLockGame = "lock:game:%s" // game ID
)
