package request

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/services"
	"github.com/xzgame/game-service/pkg/redis"
)

// GameRequestHandler handles game-related requests
type GameRequestHandler struct {
	redisClient      *redis.RedisClient
	gameStateManager *services.GameStateManager
	roomService      services.RoomService
	utils            *RequestUtils
	logger           *logrus.Logger
}

// NewGameRequestHandler creates a new game request handler
func NewGameRequestHandler(
	redisClient *redis.RedisClient,
	gameStateManager *services.GameStateManager,
	roomService services.RoomService,
	utils *RequestUtils,
	logger *logrus.Logger,
) *GameRequestHandler {
	return &GameRequestHandler{
		redisClient:      redisClient,
		gameStateManager: gameStateManager,
		roomService:      roomService,
		utils:            utils,
		logger:           logger,
	}
}

// HandleRequest processes game-related requests
func (h *GameRequestHandler) HandleRequest(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "select_wheel_color":
		h.handleSelectWheelColor(ctx, requestMsg)
	case "color_selection":
		h.handleColorSelection(ctx, requestMsg)
	case "select_amidakuji_position":
		h.handleSelectAmidakujiPosition(ctx, requestMsg)
	case "player_ready":
		h.handlePlayerReady(ctx, requestMsg)
	case "get_player_status":
		h.handleGetPlayerStatus(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown game request type")
	}
}

// handleSelectWheelColor processes wheel color selection requests
func (h *GameRequestHandler) handleSelectWheelColor(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload WheelColorPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"colorId":       payload.ColorID,
		"correlationId": correlationID,
	}).Info("Processing wheel color selection")

	// Get current color selections from cache
	cacheKey := "room:" + payload.RoomID + ":color_selections"
	var colorState ColorStateData

	// Initialize if not exists
	if err := h.redisClient.Get(ctx, cacheKey, &colorState); err != nil {
		// Create default color state
		colorState = ColorStateData{
			AvailableColors: []map[string]any{
				{"id": "red", "name": "Red", "hex": "#FF0000"},
				{"id": "blue", "name": "Blue", "hex": "#0000FF"},
				{"id": "green", "name": "Green", "hex": "#00FF00"},
				{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
				{"id": "purple", "name": "Purple", "hex": "#800080"},
				{"id": "orange", "name": "Orange", "hex": "#FFA500"},
			},
			PlayerColors: make(map[string]any),
		}
	}

	// Check if color is available
	colorAvailable := false
	for _, color := range colorState.AvailableColors {
		if colorID, ok := color["id"].(string); ok && colorID == payload.ColorID {
			colorAvailable = true
			break
		}
	}

	if !colorAvailable {
		errorResponse := h.utils.CreateErrorResponse(correlationID, "COLOR_NOT_AVAILABLE", "Selected color is not available")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	// Check if color is already taken
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok && colorID == payload.ColorID {
				errorResponse := h.utils.CreateErrorResponse(correlationID, "COLOR_ALREADY_TAKEN", "Color is already selected by another player")
				responseChannel := h.utils.CreateResponseChannel(correlationID)
				h.utils.PublishResponse(ctx, responseChannel, errorResponse)
				return
			}
		}
	}

	// Remove user's previous color selection if any
	delete(colorState.PlayerColors, payload.UserID)

	// Add new color selection
	colorState.PlayerColors[payload.UserID] = map[string]any{
		"userId":   payload.UserID,
		"username": payload.Username,
		"colorId":  payload.ColorID,
	}

	// Save updated color state
	if err := h.redisClient.Set(ctx, cacheKey, colorState, 0); err != nil {
		h.logger.WithError(err).Error("Failed to save color state")
		errorResponse := h.utils.CreateErrorResponse(correlationID, "SAVE_FAILED", "Failed to save color selection")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"colorId":       payload.ColorID,
		"correlationId": correlationID,
	}).Info("Successfully selected wheel color")

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"colorId":    payload.ColorID,
		"colorState": colorState,
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)

	// Broadcast color state update to room
	broadcastData := map[string]interface{}{
		"event": "color_state_updated",
		"data": map[string]interface{}{
			"roomId":     payload.RoomID,
			"colorState": colorState,
		},
		"timestamp": requestMsg.Event.Timestamp,
	}
	roomChannel := "room:" + payload.RoomID + ":events"
	h.utils.PublishResponse(ctx, roomChannel, broadcastData)
}

// handleColorSelection processes color selection requests (spec format)
func (h *GameRequestHandler) handleColorSelection(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload ColorSelectionPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"color":         payload.Color,
		"correlationId": correlationID,
	}).Info("Processing color selection (spec format)")

	// Get current color selections from cache
	cacheKey := "room:" + payload.RoomID + ":color_selections"
	var colorState ColorStateData

	// Initialize if not exists
	if err := h.redisClient.Get(ctx, cacheKey, &colorState); err != nil {
		// Create default color state
		colorState = ColorStateData{
			AvailableColors: []map[string]any{
				{"id": "red", "name": "Red", "hex": "#FF0000"},
				{"id": "blue", "name": "Blue", "hex": "#0000FF"},
				{"id": "green", "name": "Green", "hex": "#00FF00"},
				{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
				{"id": "purple", "name": "Purple", "hex": "#800080"},
				{"id": "orange", "name": "Orange", "hex": "#FFA500"},
				{"id": "pink", "name": "Pink", "hex": "#FFC0CB"},
				{"id": "teal", "name": "Teal", "hex": "#008080"},
			},
			PlayerColors: make(map[string]any),
		}
	}

	// Check if color is available
	colorAvailable := false
	for _, color := range colorState.AvailableColors {
		if colorID, ok := color["id"].(string); ok && colorID == payload.Color {
			colorAvailable = true
			break
		}
	}

	if !colorAvailable {
		errorResponse := h.utils.CreateErrorResponse(correlationID, "COLOR_NOT_AVAILABLE", "Selected color is not available")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	// Check if color is already taken
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok && colorID == payload.Color {
				errorResponse := h.utils.CreateErrorResponse(correlationID, "COLOR_ALREADY_TAKEN", "Color is already selected by another player")
				responseChannel := h.utils.CreateResponseChannel(correlationID)
				h.utils.PublishResponse(ctx, responseChannel, errorResponse)
				return
			}
		}
	}

	// Remove user's previous color selection if any
	delete(colorState.PlayerColors, payload.UserID)

	// Add new color selection
	colorState.PlayerColors[payload.UserID] = map[string]any{
		"userId":   payload.UserID,
		"username": payload.Username,
		"colorId":  payload.Color,
	}

	// Save updated color state
	if err := h.redisClient.Set(ctx, cacheKey, colorState, 0); err != nil {
		h.logger.WithError(err).Error("Failed to save color state")
		errorResponse := h.utils.CreateErrorResponse(correlationID, "SAVE_FAILED", "Failed to save color selection")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"color":         payload.Color,
		"correlationId": correlationID,
	}).Info("Successfully selected color (spec format)")

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"color":      payload.Color,
		"colorState": colorState,
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)

	// Broadcast multiple real-time color events to room
	h.broadcastColorEvents(ctx, payload.RoomID, payload.UserID, payload.Username, payload.Color, colorState, requestMsg.Event.Timestamp.Format(time.RFC3339))

	// Broadcast color availability update event
	h.broadcastColorAvailabilityUpdate(ctx, payload.RoomID, colorState, requestMsg.Event.Timestamp.Format(time.RFC3339))

	// Broadcast unified room_info_updated event with color state
	h.broadcastUnifiedRoomInfoUpdated(ctx, payload.RoomID, "color_selection", colorState, requestMsg.Event.Timestamp.Format(time.RFC3339))
}

// handleSelectAmidakujiPosition processes Amidakuji position selection requests
func (h *GameRequestHandler) handleSelectAmidakujiPosition(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload AmidakujiPositionPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"position":      payload.Position,
		"correlationId": correlationID,
	}).Info("Processing Amidakuji position selection")

	// Get current position selections from cache
	cacheKey := "room:" + payload.RoomID + ":position_selections"
	var positionState map[string]interface{}

	// Initialize if not exists
	if err := h.redisClient.Get(ctx, cacheKey, &positionState); err != nil {
		positionState = make(map[string]interface{})
	}

	// Check if position is already taken
	for _, playerData := range positionState {
		if playerMap, ok := playerData.(map[string]interface{}); ok {
			if pos, ok := playerMap["position"].(float64); ok && int(pos) == payload.Position {
				errorResponse := h.utils.CreateErrorResponse(correlationID, "POSITION_ALREADY_TAKEN", "Position is already selected by another player")
				responseChannel := h.utils.CreateResponseChannel(correlationID)
				h.utils.PublishResponse(ctx, responseChannel, errorResponse)
				return
			}
		}
	}

	// Remove user's previous position selection if any
	delete(positionState, payload.UserID)

	// Add new position selection
	positionState[payload.UserID] = map[string]interface{}{
		"userId":   payload.UserID,
		"username": payload.Username,
		"position": payload.Position,
	}

	// Save updated position state
	if err := h.redisClient.Set(ctx, cacheKey, positionState, 0); err != nil {
		h.logger.WithError(err).Error("Failed to save position state")
		errorResponse := h.utils.CreateErrorResponse(correlationID, "SAVE_FAILED", "Failed to save position selection")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"position":      payload.Position,
		"correlationId": correlationID,
	}).Info("Successfully selected Amidakuji position")

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"position":      payload.Position,
		"positionState": positionState,
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)

	// Broadcast position state update to room
	broadcastData := map[string]interface{}{
		"event": "position_state_updated",
		"data": map[string]interface{}{
			"roomId":        payload.RoomID,
			"positionState": positionState,
		},
		"timestamp": requestMsg.Event.Timestamp,
	}
	roomChannel := "room:" + payload.RoomID + ":events"
	h.utils.PublishResponse(ctx, roomChannel, broadcastData)
}

// handlePlayerReady processes player ready requests
func (h *GameRequestHandler) handlePlayerReady(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload PlayerReadyPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"isReady":       payload.IsReady,
		"correlationId": correlationID,
	}).Info("Processing player ready request")

	// This would typically interact with the room service to update player ready status
	// For now, we'll create a simple response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"userId":  payload.UserID,
		"isReady": payload.IsReady,
	})

	responseChannel := payload.ResponseChannel
	if responseChannel == "" {
		responseChannel = h.utils.CreateResponseChannel(correlationID)
	}
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// handleGetPlayerStatus processes get player status requests
func (h *GameRequestHandler) handleGetPlayerStatus(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload GetPlayerStatusPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Processing get player status request")

	// This would typically get player status from the room service
	// For now, we'll create a simple response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"userId": payload.UserID,
		"status": "active", // This should come from actual player status
	})

	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// broadcastColorEvents broadcasts multiple real-time color events to room
func (h *GameRequestHandler) broadcastColorEvents(ctx context.Context, roomID, userID, username, color string, colorState ColorStateData, timestamp string) {
	roomChannel := "room:" + roomID + ":events"

	// 1. Broadcast color selection update event
	colorSelectionEvent := map[string]interface{}{
		"event": "color_selection_update",
		"data": map[string]interface{}{
			"player": map[string]interface{}{
				"userId":   userID,
				"username": username,
				"selectedColor": map[string]interface{}{
					"id":   color,
					"name": h.getColorName(color),
					"hex":  h.getColorHex(color),
				},
			},
			"action":          "selected",
			"availableColors": h.getAvailableColors(colorState),
			"takenColors":     h.getTakenColors(colorState),
		},
		"timestamp": timestamp,
	}
	h.utils.PublishResponse(ctx, roomChannel, colorSelectionEvent)

	// 2. Broadcast available colors event
	availableColorsEvent := map[string]interface{}{
		"event": "available_colors",
		"data": map[string]interface{}{
			"availableColors": h.getAvailableColors(colorState),
			"takenColors":     h.getTakenColors(colorState),
			"totalColors":     8, // Total number of colors
			"availableCount":  len(h.getAvailableColors(colorState)),
			"takenCount":      len(h.getTakenColors(colorState)),
		},
		"timestamp": timestamp,
	}
	h.utils.PublishResponse(ctx, roomChannel, availableColorsEvent)

	// 3. Broadcast comprehensive color state sync event
	colorStateSyncEvent := map[string]interface{}{
		"event": "color_state_sync",
		"data": map[string]interface{}{
			"roomId":              roomID,
			"playerColorMappings": h.getPlayerColorMappings(colorState),
			"playerList":          h.getPlayerList(colorState),
			"availableColors":     h.getAvailableColors(colorState),
			"takenColors":         h.getTakenColors(colorState),
			"allColors":           h.getAllColors(),
			"statistics": map[string]interface{}{
				"totalPlayers":   len(colorState.PlayerColors),
				"totalColors":    8,
				"availableCount": len(h.getAvailableColors(colorState)),
				"takenCount":     len(h.getTakenColors(colorState)),
				"selectionRate":  h.calculateSelectionRate(colorState),
			},
		},
		"timestamp": timestamp,
	}
	h.utils.PublishResponse(ctx, roomChannel, colorStateSyncEvent)

	h.logger.WithFields(logrus.Fields{
		"roomId": roomID,
		"userId": userID,
		"color":  color,
		"events": []string{"color_selection_update", "available_colors", "color_state_sync"},
	}).Debug("Broadcasted real-time color events")
}

// broadcastUnifiedRoomInfoUpdated broadcasts unified room_info_updated event with comprehensive room state including color data
func (h *GameRequestHandler) broadcastUnifiedRoomInfoUpdated(ctx context.Context, roomID, reason string, colorState ColorStateData, timestamp string) {
	roomChannel := "room:" + roomID + ":events"

	// Get room details from manager service or cache
	roomDetails := h.getRoomDetails(ctx, roomID)

	// Build unified room info structure
	unifiedRoomInfo := h.buildUnifiedRoomInfo(ctx, roomID, reason, roomDetails, colorState, timestamp)

	// Create event wrapper for Redis publishing
	roomInfoEvent := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "room_info_updated",
			"timestamp": timestamp,
			"payload":   unifiedRoomInfo,
		},
		"metadata": map[string]interface{}{
			"serviceId":     "game-service",
			"version":       "1.0.0",
			"correlationId": "room-info-" + roomID + "-" + timestamp,
		},
	}

	h.utils.PublishResponse(ctx, roomChannel, roomInfoEvent)

	h.logger.WithFields(logrus.Fields{
		"roomId":       roomID,
		"reason":       reason,
		"totalPlayers": len(colorState.PlayerColors),
		"canStart":     h.canGameStart(colorState),
	}).Info("Broadcasted unified room_info_updated event")
}

// buildUnifiedRoomInfo creates a unified room info structure with all necessary data
func (h *GameRequestHandler) buildUnifiedRoomInfo(ctx context.Context, roomID, reason string, roomDetails map[string]interface{}, colorState ColorStateData, timestamp string) map[string]interface{} {
	// Base unified structure
	unifiedInfo := map[string]interface{}{
		"reason":    reason,
		"roomId":    roomID,
		"timestamp": timestamp,
		"roomInfo": map[string]interface{}{
			"room": roomDetails,
			"players": map[string]interface{}{
				"list":          h.getPlayerList(colorState),
				"total":         len(colorState.PlayerColors),
				"withColors":    len(colorState.PlayerColors),
				"colorMappings": h.getPlayerColorMappings(colorState),
			},
			"game": map[string]interface{}{
				"type":              h.getGameTypeFromRoomDetails(roomDetails),
				"canStart":          h.canGameStart(colorState),
				"allColorsSelected": len(h.getAvailableColors(colorState)) == 0,
			},
		},
	}

	// Add color state for prize wheel games
	if h.isPrizeWheelGame(roomDetails) {
		unifiedInfo["roomInfo"].(map[string]interface{})["colors"] = map[string]interface{}{
			"available":   h.getAvailableColors(colorState),
			"taken":       h.getTakenColors(colorState),
			"selected":    h.getSelectedColorsMapping(colorState),
			"assignments": h.getPlayerColorMappings(colorState),
			"statistics": map[string]interface{}{
				"totalColors":    8,
				"availableCount": len(h.getAvailableColors(colorState)),
				"takenCount":     len(h.getTakenColors(colorState)),
				"selectionRate":  h.calculateSelectionRate(colorState),
			},
		}
	}

	return unifiedInfo
}

// Helper methods for unified room info
func (h *GameRequestHandler) getGameTypeFromRoomDetails(roomDetails map[string]interface{}) string {
	if gameType, ok := roomDetails["game_type"].(string); ok {
		return gameType
	}
	return "prizewheel"
}

func (h *GameRequestHandler) isPrizeWheelGame(roomDetails map[string]interface{}) bool {
	gameType := h.getGameTypeFromRoomDetails(roomDetails)
	return gameType == "prizewheel" || gameType == "PRIZEWHEEL"
}

// Helper methods for color event generation
func (h *GameRequestHandler) getColorName(colorID string) string {
	colorNames := map[string]string{
		"red":    "Red",
		"blue":   "Blue",
		"green":  "Green",
		"yellow": "Yellow",
		"purple": "Purple",
		"orange": "Orange",
		"pink":   "Pink",
		"teal":   "Teal",
	}
	if name, exists := colorNames[colorID]; exists {
		return name
	}
	return colorID
}

// getSelectedColorsMapping returns a mapping of player IDs to their selected colors
func (h *GameRequestHandler) getSelectedColorsMapping(colorState ColorStateData) map[string]string {
	selectedColors := make(map[string]string)
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				selectedColors[userID] = colorID
			}
		}
	}
	return selectedColors
}

// getAvailableColorIds returns an array of available color IDs
func (h *GameRequestHandler) getAvailableColorIds(colorState ColorStateData) []string {
	allColorIds := []string{"red", "blue", "green", "yellow", "purple", "orange", "pink", "teal"}
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter available colors
	var availableColorIds []string
	for _, colorID := range allColorIds {
		if !takenColorIds[colorID] {
			availableColorIds = append(availableColorIds, colorID)
		}
	}

	return availableColorIds
}

// canGameStart determines if the game can start based on color selections
func (h *GameRequestHandler) canGameStart(colorState ColorStateData) bool {
	// For prize wheel, game can start when at least 2 players have selected colors
	// This is a basic rule - can be customized based on game requirements
	return len(colorState.PlayerColors) >= 2
}

func (h *GameRequestHandler) getColorHex(colorID string) string {
	colorHex := map[string]string{
		"red":    "#FF0000",
		"blue":   "#0000FF",
		"green":  "#00FF00",
		"yellow": "#FFFF00",
		"purple": "#800080",
		"orange": "#FFA500",
		"pink":   "#FFC0CB",
		"teal":   "#008080",
	}
	if hex, exists := colorHex[colorID]; exists {
		return hex
	}
	return "#000000"
}

func (h *GameRequestHandler) getAllColors() []map[string]interface{} {
	return []map[string]interface{}{
		{"id": "red", "name": "Red", "hex": "#FF0000"},
		{"id": "blue", "name": "Blue", "hex": "#0000FF"},
		{"id": "green", "name": "Green", "hex": "#00FF00"},
		{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
		{"id": "purple", "name": "Purple", "hex": "#800080"},
		{"id": "orange", "name": "Orange", "hex": "#FFA500"},
		{"id": "pink", "name": "Pink", "hex": "#FFC0CB"},
		{"id": "teal", "name": "Teal", "hex": "#008080"},
	}
}

// getPlayerColorMappings returns a mapping of player IDs to their color information
func (h *GameRequestHandler) getPlayerColorMappings(colorState ColorStateData) map[string]interface{} {
	playerMappings := make(map[string]interface{})
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				playerMappings[userID] = map[string]interface{}{
					"userId":   userID,
					"username": colorMap["username"],
					"color": map[string]interface{}{
						"id":   colorID,
						"name": h.getColorName(colorID),
						"hex":  h.getColorHex(colorID),
					},
					"selectedAt": time.Now().Format(time.RFC3339),
				}
			}
		}
	}
	return playerMappings
}

// getAvailableColors returns an array of available color objects
func (h *GameRequestHandler) getAvailableColors(colorState ColorStateData) []map[string]interface{} {
	allColors := h.getAllColors()
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter available colors
	var availableColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && !takenColorIds[colorID] {
			availableColors = append(availableColors, color)
		}
	}

	return availableColors
}

// getTakenColors returns an array of taken color objects
func (h *GameRequestHandler) getTakenColors(colorState ColorStateData) []map[string]interface{} {
	allColors := h.getAllColors()
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter taken colors
	var takenColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && takenColorIds[colorID] {
			takenColors = append(takenColors, color)
		}
	}

	return takenColors
}

// getPlayerList returns a list of players with their color information
func (h *GameRequestHandler) getPlayerList(colorState ColorStateData) []map[string]interface{} {
	var playerList []map[string]interface{}
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				playerList = append(playerList, map[string]interface{}{
					"userId":    userID,
					"username":  colorMap["username"],
					"colorId":   colorID,
					"colorName": h.getColorName(colorID),
					"colorHex":  h.getColorHex(colorID),
				})
			}
		}
	}
	return playerList
}

// calculateSelectionRate calculates the percentage of colors that have been selected
func (h *GameRequestHandler) calculateSelectionRate(colorState ColorStateData) float64 {
	totalColors := 8.0
	takenColors := float64(len(colorState.PlayerColors))
	if totalColors == 0 {
		return 0.0
	}
	return (takenColors / totalColors) * 100.0
}

// getRoomDetails fetches room details from room service or cache
func (h *GameRequestHandler) getRoomDetails(ctx context.Context, roomID string) map[string]interface{} {
	// Try to get room details from cache first
	cacheKey := "room:" + roomID + ":details"
	var roomDetails map[string]interface{}

	if err := h.redisClient.Get(ctx, cacheKey, &roomDetails); err != nil {
		// If not in cache, fetch from room service
		h.logger.WithFields(logrus.Fields{
			"roomId": roomID,
			"error":  err.Error(),
		}).Debug("Room details not found in cache, fetching from room service")

		// Get real room data from room service
		room, err := h.roomService.GetRoom(ctx, roomID)
		if err != nil {
			h.logger.WithFields(logrus.Fields{
				"roomId": roomID,
				"error":  err.Error(),
			}).Warn("Failed to get room from room service, using defaults")

			// Return basic structure as fallback
			return map[string]interface{}{
				"id":              roomID,
				"name":            "Unknown Room",
				"game_type":       "prizewheel",
				"status":          "waiting",
				"current_players": 0,
				"max_players":     4,
				"min_players":     2,
				"bet_amount":      10,
				"currency":        "USD",
				"prize_pool":      0,
				"is_private":      false,
				"has_password":    false,
				"has_space":       true,
				"is_featured":     false,
			}
		}

		// Convert room service data to the expected format
		roomDetails = map[string]interface{}{
			"id":              roomID,
			"name":            room.Name,
			"game_type":       h.convertGameTypeToString(room.GameType),
			"status":          h.convertRoomStatusToString(room.Status),
			"current_players": room.CurrentPlayers,
			"max_players":     room.MaxPlayers,
			"min_players":     room.MinPlayers,
			"bet_amount":      room.BetAmount,
			"currency":        "USD",
			"prize_pool":      0, // Would be calculated from game state
			"is_private":      room.Configuration.IsPrivate,
			"has_password":    room.Configuration.Password != "",
			"has_space":       room.CurrentPlayers < room.MaxPlayers,
			"is_featured":     false, // Would come from room configuration
		}

		// Cache the room details for future use
		if cacheErr := h.redisClient.Set(ctx, cacheKey, roomDetails, 300); cacheErr != nil { // 5 minute cache
			h.logger.WithError(cacheErr).Debug("Failed to cache room details")
		}
	}

	return roomDetails
}

// Helper functions to convert enum types to strings
func (h *GameRequestHandler) convertGameTypeToString(gameType models.GameType) string {
	switch gameType {
	case models.GameTypePrizeWheel:
		return "prizewheel"
	case models.GameTypeAmidakuji:
		return "amidakuji"
	default:
		return "prizewheel"
	}
}

func (h *GameRequestHandler) convertRoomStatusToString(status models.RoomStatus) string {
	switch status {
	case models.RoomStatusWaiting:
		return "waiting"
	case models.RoomStatusActive:
		return "active"
	case models.RoomStatusFull:
		return "full"
	case models.RoomStatusClosed:
		return "closed"
	case models.RoomStatusArchived:
		return "archived"
	default:
		return "waiting"
	}
}

// getSelectedColors returns an array of selected color objects with player info
func (h *GameRequestHandler) getSelectedColors(colorState ColorStateData) []map[string]interface{} {
	var selectedColors []map[string]interface{}
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				selectedColors = append(selectedColors, map[string]interface{}{
					"id":         colorID,
					"name":       h.getColorName(colorID),
					"hex":        h.getColorHex(colorID),
					"userId":     userID,
					"username":   colorMap["username"],
					"selectedAt": time.Now().Format(time.RFC3339),
				})
			}
		}
	}
	return selectedColors
}

// broadcastColorAvailabilityUpdate broadcasts color availability update to all room subscribers
func (h *GameRequestHandler) broadcastColorAvailabilityUpdate(ctx context.Context, roomID string, colorState ColorStateData, timestamp string) {
	// Build available colors list
	availableColors := h.getAvailableColors(colorState)
	selectedColors := h.getSelectedColors(colorState)

	// Build color availability event
	colorAvailabilityEvent := map[string]interface{}{
		"type": "color_availability_updated",
		"data": map[string]interface{}{
			"roomId":    roomID,
			"timestamp": timestamp,
			"colors": map[string]interface{}{
				"available": availableColors,
				"selected":  selectedColors,
				"statistics": map[string]interface{}{
					"totalColors":    8,
					"availableCount": len(availableColors),
					"selectedCount":  len(selectedColors),
					"selectionRate":  h.calculateSelectionRate(colorState),
				},
			},
			"message": "Color availability updated after selection",
		},
	}

	// Create Redis message for socket gateway
	redisMessage := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "color_availability_updated",
			"payload":   colorAvailabilityEvent["data"],
			"timestamp": timestamp,
		},
		"metadata": map[string]interface{}{
			"serviceId": "game-service",
			"version":   "1.0.0",
			"priority":  1,
		},
	}

	// Publish to room-specific channel
	channel := fmt.Sprintf("room:%s:events", roomID)
	messageBytes, err := json.Marshal(redisMessage)
	if err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to marshal color availability update")
		return
	}

	if err := h.redisClient.Publish(ctx, channel, messageBytes); err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to publish color availability update")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":         roomID,
		"availableCount": len(availableColors),
		"selectedCount":  len(selectedColors),
		"timestamp":      timestamp,
	}).Info("Broadcasted color availability update")
}
