package request

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/pkg/clients/auth"
	"github.com/xzgame/game-service/pkg/redis"
)

// AuthRequestHandler handles authentication-related requests
type AuthRequestHandler struct {
	redisClient *redis.RedisClient
	authClient  *auth.AuthClient
	utils       *RequestUtils
	logger      *logrus.Logger
}

// NewAuthRequestHandler creates a new auth request handler
func NewAuthRequestHandler(
	redisClient *redis.RedisClient,
	authClient *auth.AuthClient,
	utils *RequestUtils,
	logger *logrus.Logger,
) *AuthRequestHandler {
	return &AuthRequestHandler{
		redisClient: redisClient,
		authClient:  authClient,
		utils:       utils,
		logger:      logger,
	}
}

// HandleRequest processes authentication-related requests
func (h *AuthRequestHandler) HandleRequest(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "verify_token":
		h.handleTokenVerification(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown auth request type")
	}
}

// handleTokenVerification processes token verification requests
func (h *AuthRequestHandler) handleTokenVerification(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload TokenVerificationPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        payload.UserID,
		"username":      payload.Username,
		"sessionId":     payload.SessionID,
		"correlationId": correlationID,
	}).Info("Processing token verification request")

	// Verify token with auth service
	tokenClaims, err := h.authClient.VerifyToken(ctx, payload.Token)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"userId":        payload.UserID,
			"correlationId": correlationID,
		}).Error("Failed to verify token")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "TOKEN_VERIFICATION_FAILED", err.Error())

		// Use the response channel from the request metadata if available
		responseChannel := requestMsg.Metadata.ResponseChannel
		if responseChannel == "" {
			responseChannel = h.utils.CreateResponseChannel(correlationID)
		}

		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	if tokenClaims == nil {
		h.logger.WithFields(logrus.Fields{
			"userId":        payload.UserID,
			"correlationId": correlationID,
		}).Warn("Token verification failed - invalid token")

		// Send error response for invalid token
		errorResponse := h.utils.CreateErrorResponse(correlationID, "INVALID_TOKEN", "Token is invalid or expired")

		// Use the response channel from the request metadata if available
		responseChannel := requestMsg.Metadata.ResponseChannel
		if responseChannel == "" {
			responseChannel = h.utils.CreateResponseChannel(correlationID)
		}

		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        payload.UserID,
		"username":      payload.Username,
		"correlationId": correlationID,
	}).Info("Token verification successful")

	// Build response data
	responseData := map[string]interface{}{
		"valid":     true,
		"userId":    tokenClaims.UserID,
		"username":  tokenClaims.Username,
		"sessionId": tokenClaims.SessionID,
	}

	// Include additional fields if available
	if tokenClaims.Role != "" {
		responseData["role"] = tokenClaims.Role
	}
	if tokenClaims.DeviceID != "" {
		responseData["deviceId"] = tokenClaims.DeviceID
	}

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, responseData)

	// Use the response channel from the request metadata if available
	responseChannel := requestMsg.Metadata.ResponseChannel
	if responseChannel == "" {
		responseChannel = h.utils.CreateResponseChannel(correlationID)
	}

	h.logger.WithFields(logrus.Fields{
		"correlationId":   correlationID,
		"responseChannel": responseChannel,
	}).Info("Sending token verification response")

	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// TokenInfo represents token information returned by auth service
type TokenInfo struct {
	UserID      string   `json:"userId"`
	Username    string   `json:"username"`
	SessionID   string   `json:"sessionId"`
	Role        string   `json:"role,omitempty"`
	Permissions []string `json:"permissions,omitempty"`
	DeviceID    string   `json:"deviceId,omitempty"`
	ExpiresAt   int64    `json:"expiresAt,omitempty"`
}

// ValidateTokenRequest validates a token verification request
func (h *AuthRequestHandler) ValidateTokenRequest(payload TokenVerificationPayload) error {
	if payload.Token == "" {
		return fmt.Errorf("token is required")
	}
	if payload.UserID == "" {
		return fmt.Errorf("user ID is required")
	}
	return nil
}

// GetTokenInfo extracts token information from a verified token
func (h *AuthRequestHandler) GetTokenInfo(ctx context.Context, token string) (*TokenInfo, error) {
	tokenClaims, err := h.authClient.VerifyToken(ctx, token)
	if err != nil {
		return nil, err
	}
	if tokenClaims == nil {
		return nil, fmt.Errorf("invalid token")
	}

	return &TokenInfo{
		UserID:    tokenClaims.UserID,
		Username:  tokenClaims.Username,
		SessionID: tokenClaims.SessionID,
		Role:      tokenClaims.Role,
		DeviceID:  tokenClaims.DeviceID,
	}, nil
}

// RefreshToken refreshes an authentication token
func (h *AuthRequestHandler) RefreshToken(ctx context.Context, refreshToken string) (*TokenInfo, error) {
	// This would typically call the auth service to refresh the token
	// For now, we'll return an error indicating this feature is not implemented
	return nil, fmt.Errorf("token refresh not implemented")
}

// InvalidateToken invalidates an authentication token
func (h *AuthRequestHandler) InvalidateToken(ctx context.Context, token string) error {
	// This would typically call the auth service to invalidate the token
	// For now, we'll return an error indicating this feature is not implemented
	return fmt.Errorf("token invalidation not implemented")
}

// GetUserSessions gets all active sessions for a user
func (h *AuthRequestHandler) GetUserSessions(ctx context.Context, userID string) ([]map[string]interface{}, error) {
	// This would typically call the auth service to get user sessions
	// For now, we'll return an empty list
	return []map[string]interface{}{}, nil
}

// ValidatePermission checks if a user has a specific permission
func (h *AuthRequestHandler) ValidatePermission(ctx context.Context, userID, permission string) (bool, error) {
	// This would typically call the auth service to validate permissions
	// For now, we'll return true (allowing all permissions)
	return true, nil
}

// GetUserRole gets the role of a user
func (h *AuthRequestHandler) GetUserRole(ctx context.Context, userID string) (string, error) {
	// This would typically call the auth service to get user role
	// For now, we'll return a default role
	return "user", nil
}

// LogAuthEvent logs an authentication event
func (h *AuthRequestHandler) LogAuthEvent(ctx context.Context, eventType, userID, details string) {
	h.logger.WithFields(logrus.Fields{
		"eventType": eventType,
		"userId":    userID,
		"details":   details,
	}).Info("Authentication event")

	// This could also publish to an audit log channel
	auditEvent := map[string]interface{}{
		"event":     "auth_event",
		"eventType": eventType,
		"userId":    userID,
		"details":   details,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Publish to audit channel (optional)
	h.utils.PublishResponse(ctx, "audit:auth:events", auditEvent)
}
