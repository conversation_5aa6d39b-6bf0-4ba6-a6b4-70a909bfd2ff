package request

import (
	"context"
	"encoding/json"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/pkg/redis"
)

// AdminRequestHandler handles admin-related requests
type AdminRequestHandler struct {
	redisClient *redis.RedisClient
	utils       *RequestUtils
	logger      *logrus.Logger
}

// NewAdminRequestHandler creates a new admin request handler
func NewAdminRequestHandler(
	redisClient *redis.RedisClient,
	utils *RequestUtils,
	logger *logrus.Logger,
) *AdminRequestHandler {
	return &AdminRequestHandler{
		redisClient: redisClient,
		utils:       utils,
		logger:      logger,
	}
}

// HandleNotification processes admin notification events
func (h *AdminRequestHandler) HandleNotification(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "player_kicked":
		h.handlePlayerKicked(ctx, requestMsg)
	case "room_closed":
		h.handleRoomClosed(ctx, requestMsg)
	case "system_maintenance":
		h.handleSystemMaintenance(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown admin notification type")
	}
}

// handlePlayerKicked processes player kicked notifications
func (h *AdminRequestHandler) handlePlayerKicked(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload PlayerKickedPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"reason":        payload.Reason,
		"adminId":       payload.AdminID,
		"correlationId": correlationID,
	}).Info("Processing player kicked notification")

	// Broadcast player kicked event to room
	kickedEvent := map[string]interface{}{
		"event": "player_kicked",
		"data": map[string]interface{}{
			"roomId":   payload.RoomID,
			"userId":   payload.UserID,
			"username": payload.Username,
			"reason":   payload.Reason,
			"adminId":  payload.AdminID,
		},
		"timestamp": requestMsg.Event.Timestamp,
	}

	// Publish to room-specific channel
	roomChannel := "room:" + payload.RoomID + ":events"
	if err := h.utils.PublishResponse(ctx, roomChannel, kickedEvent); err != nil {
		h.logger.WithError(err).WithField("roomId", payload.RoomID).Error("Failed to broadcast player kicked event")
	}

	// Also publish to general admin notifications channel
	h.utils.PublishResponse(ctx, "admin:notifications", kickedEvent)

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Successfully processed player kicked notification")
}

// handleRoomClosed processes room closed notifications
func (h *AdminRequestHandler) handleRoomClosed(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	roomID, ok := payload["roomId"].(string)
	if !ok {
		h.logger.WithField("correlationId", correlationID).Error("Missing or invalid roomId in room closed notification")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        roomID,
		"correlationId": correlationID,
	}).Info("Processing room closed notification")

	// Broadcast room closed event
	closedEvent := map[string]interface{}{
		"event": "room_closed",
		"data": map[string]interface{}{
			"roomId": roomID,
			"reason": payload["reason"],
		},
		"timestamp": requestMsg.Event.Timestamp,
	}

	// Publish to room-specific channel
	roomChannel := "room:" + roomID + ":events"
	if err := h.utils.PublishResponse(ctx, roomChannel, closedEvent); err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to broadcast room closed event")
	}

	// Also publish to lobby updates to refresh room list
	lobbyUpdate := map[string]interface{}{
		"event": "room_list_updated",
		"data": map[string]interface{}{
			"updateType": "room_closed",
			"roomId":     roomID,
		},
		"timestamp": requestMsg.Event.Timestamp,
	}
	h.utils.PublishResponse(ctx, "game:lobby:updates", lobbyUpdate)

	h.logger.WithFields(logrus.Fields{
		"roomId":        roomID,
		"correlationId": correlationID,
	}).Info("Successfully processed room closed notification")
}

// handleSystemMaintenance processes system maintenance notifications
func (h *AdminRequestHandler) handleSystemMaintenance(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"maintenanceType": payload["type"],
		"message":         payload["message"],
		"correlationId":   correlationID,
	}).Info("Processing system maintenance notification")

	// Broadcast system maintenance event to all channels
	maintenanceEvent := map[string]interface{}{
		"event":     "system_maintenance",
		"data":      payload,
		"timestamp": requestMsg.Event.Timestamp,
	}

	// Publish to general notifications
	h.utils.PublishResponse(ctx, "system:notifications", maintenanceEvent)

	// Also publish to lobby updates
	h.utils.PublishResponse(ctx, "game:lobby:updates", maintenanceEvent)

	h.logger.WithFields(logrus.Fields{
		"correlationId": correlationID,
	}).Info("Successfully processed system maintenance notification")
}

// BroadcastAdminMessage broadcasts an admin message to all users
func (h *AdminRequestHandler) BroadcastAdminMessage(ctx context.Context, messageType string, message string, data map[string]interface{}) error {
	adminMessage := map[string]interface{}{
		"event": "admin_message",
		"data": map[string]interface{}{
			"type":    messageType,
			"message": message,
			"data":    data,
		},
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Publish to multiple channels for broad reach
	channels := []string{
		"admin:notifications",
		"system:notifications",
		"game:lobby:updates",
	}

	for _, channel := range channels {
		if err := h.utils.PublishResponse(ctx, channel, adminMessage); err != nil {
			h.logger.WithError(err).WithField("channel", channel).Error("Failed to broadcast admin message")
		}
	}

	return nil
}

// NotifyRoomUpdate notifies about room updates
func (h *AdminRequestHandler) NotifyRoomUpdate(ctx context.Context, roomID string, updateType string, data map[string]interface{}) error {
	updateEvent := map[string]interface{}{
		"event": "room_updated",
		"data": map[string]interface{}{
			"roomId":     roomID,
			"updateType": updateType,
			"data":       data,
		},
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Publish to room-specific channel
	roomChannel := "room:" + roomID + ":events"
	if err := h.utils.PublishResponse(ctx, roomChannel, updateEvent); err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to notify room update")
		return err
	}

	// Also publish to lobby updates if it affects room list
	if updateType == "status_changed" || updateType == "player_count_changed" {
		lobbyUpdate := map[string]interface{}{
			"event": "room_list_updated",
			"data": map[string]interface{}{
				"updateType": updateType,
				"roomId":     roomID,
			},
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		}
		h.utils.PublishResponse(ctx, "game:lobby:updates", lobbyUpdate)
	}

	return nil
}

// GetAdminStats returns admin statistics
func (h *AdminRequestHandler) GetAdminStats(ctx context.Context) (map[string]interface{}, error) {
	// This would typically gather statistics from various services
	// For now, return basic stats
	return map[string]interface{}{
		"activeRooms":   0, // Would get from manager service
		"activePlayers": 0, // Would get from player service
		"systemStatus":  "operational",
		"lastUpdated":   time.Now().UTC().Format(time.RFC3339),
	}, nil
}
