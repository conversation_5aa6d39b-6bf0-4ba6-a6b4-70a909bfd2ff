package request

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/internal/models"
	"github.com/xzgame/game-service/internal/services"
	"github.com/xzgame/game-service/pkg/redis"
)

// RoomRequestHandler handles room-related requests
type RoomRequestHandler struct {
	redisClient *redis.RedisClient
	roomService services.RoomService
	utils       *RequestUtils
	logger      *logrus.Logger
}

// NewRoomRequestHandler creates a new room request handler
func NewRoomRequestHandler(
	redisClient *redis.RedisClient,
	roomService services.RoomService,
	utils *RequestUtils,
	logger *logrus.Logger,
) *RoomRequestHandler {
	return &RoomRequestHandler{
		redisClient: redisClient,
		roomService: roomService,
		utils:       utils,
		logger:      logger,
	}
}

// HandleRequest processes room-related requests
func (h *RoomRequestHandler) HandleRequest(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "request_room_list":
		h.handleRoomListRequest(ctx, requestMsg)
	case "join_room":
		h.handleJoinRoom(ctx, requestMsg)
	case "leave_room":
		h.handleLeaveRoom(ctx, requestMsg)
	case "get_room_state":
		h.handleGetRoomState(ctx, requestMsg)
	case "get_unified_room_info":
		h.handleGetUnifiedRoomInfo(ctx, requestMsg)
	case "room_info_request_with_color_state":
		h.handleRoomInfoRequestWithColorState(ctx, requestMsg)
	case "room_info_broadcast_with_color_state":
		h.handleRoomInfoBroadcastWithColorState(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown room request type")
	}
}

// handleRoomListRequest processes room list requests
func (h *RoomRequestHandler) handleRoomListRequest(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.logger.WithError(err).Error("Failed to marshal request payload")
		h.publishRoomListResponse(ctx, []map[string]interface{}{}, correlationID)
		return
	}

	var payload RoomListRequestPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.logger.WithError(err).Error("Failed to parse room list request payload")
		h.publishRoomListResponse(ctx, []map[string]interface{}{}, correlationID)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"requestedBy":   payload.RequestedBy,
		"filters":       payload.Filters,
		"correlationId": correlationID,
	}).Info("Processing room list request")

	// Build filter for manager service
	filters := map[string]any{
		"per_page": payload.Filters.Limit,
		"page":     1,
	}

	// Only apply status filter if specifically requested
	if payload.Filters.Status != "" {
		filters["status"] = payload.Filters.Status
	}

	// Only apply has_space filter if specifically requested
	if payload.Filters.HasSpace {
		filters["has_space"] = true
	}

	// Set default limit if not provided
	if filters["per_page"] == 0 {
		filters["per_page"] = 50
	}

	h.logger.WithFields(logrus.Fields{
		"filters":       filters,
		"correlationId": correlationID,
	}).Info("Fetching rooms from manager service with filters")

	// Get rooms from room service instead of manager service
	filter := models.RoomListFilter{
		Page:  1,
		Limit: 50,
		// Add other filters as needed
	}

	roomListResponse, err := h.roomService.ListRooms(ctx, filter)
	if err != nil {
		h.logger.WithError(err).WithField("correlationId", correlationID).Error("Failed to get rooms from room service")
		h.publishRoomListResponse(ctx, []map[string]interface{}{}, correlationID)
		return
	}

	// Convert rooms to response format
	roomList := make([]map[string]interface{}, len(roomListResponse.Rooms))
	for i, room := range roomListResponse.Rooms {
		roomList[i] = map[string]interface{}{
			"id":             room.ID.Hex(),
			"name":           room.Name,
			"gameType":       string(room.GameType),
			"status":         string(room.Status),
			"currentPlayers": room.CurrentPlayers,
			"maxPlayers":     room.MaxPlayers,
			"betAmount":      room.Configuration.BetLimits.MinBet,
			"isPrivate":      false, // Default to false for now
			"createdAt":      room.CreatedAt,
		}
	}

	h.logger.WithFields(logrus.Fields{
		"roomCount":     len(roomList),
		"correlationId": correlationID,
	}).Info("Retrieved rooms from room service")

	// Publish room list as lobby update
	h.publishRoomListResponse(ctx, roomList, correlationID)
}

// handleJoinRoom processes join room requests
func (h *RoomRequestHandler) handleJoinRoom(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload JoinRoomPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"username":      payload.Username,
		"betAmount":     payload.BetAmount,
		"correlationId": correlationID,
	}).Info("Processing join room request")

	// Create join room request
	joinRequest := models.JoinRoomRequest{
		RoomID:    payload.RoomID,
		UserID:    payload.UserID,
		BetAmount: payload.BetAmount,
		Password:  payload.Password,
	}

	// Execute join room
	room, err := h.roomService.JoinRoomWithUsername(ctx, joinRequest, payload.Username)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"userId":        payload.UserID,
			"correlationId": correlationID,
		}).Error("Failed to join room")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "JOIN_ROOM_FAILED", err.Error())
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Successfully joined room")

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"room":   room,
		"action": "joined",
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// handleLeaveRoom processes leave room requests
func (h *RoomRequestHandler) handleLeaveRoom(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload LeaveRoomPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"reason":        payload.Reason,
		"correlationId": correlationID,
	}).Info("Processing leave room request")

	// Create leave room request
	leaveRequest := models.LeaveRoomRequest{
		RoomID: payload.RoomID,
		UserID: payload.UserID,
	}

	// Execute leave room
	err = h.roomService.LeaveRoom(ctx, leaveRequest)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"userId":        payload.UserID,
			"correlationId": correlationID,
		}).Error("Failed to leave room")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "LEAVE_ROOM_FAILED", err.Error())
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Successfully left room")

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"action": "left",
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// handleGetRoomState processes get room state requests
func (h *RoomRequestHandler) handleGetRoomState(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload GetRoomStatePayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Processing get room state request")

	// Get room state
	room, err := h.roomService.GetRoom(ctx, payload.RoomID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"correlationId": correlationID,
		}).Error("Failed to get room state")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "GET_ROOM_STATE_FAILED", err.Error())
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"room": room,
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// handleGetUnifiedRoomInfo processes unified room info requests for socket gateway
func (h *RoomRequestHandler) handleGetUnifiedRoomInfo(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload UnifiedRoomInfoPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"reason":        payload.Reason,
		"includeColors": payload.IncludeColors,
		"correlationId": correlationID,
	}).Info("Processing unified room info request")

	// Get room state
	room, err := h.roomService.GetRoom(ctx, payload.RoomID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"correlationId": correlationID,
		}).Error("Failed to get room for unified info request")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "UNIFIED_ROOM_INFO_FAILED", err.Error())
		responseChannel := requestMsg.Metadata.ResponseChannel
		if responseChannel == "" {
			responseChannel = h.utils.CreateResponseChannel(correlationID)
		}
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	// Build unified room info structure
	unifiedRoomInfo := h.buildUnifiedRoomInfoResponse(ctx, payload.RoomID, payload.Reason, room)

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, unifiedRoomInfo)
	responseChannel := requestMsg.Metadata.ResponseChannel
	if responseChannel == "" {
		responseChannel = h.utils.CreateResponseChannel(correlationID)
	}
	h.utils.PublishResponse(ctx, responseChannel, successResponse)

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"correlationId": correlationID,
		"hasColors":     unifiedRoomInfo["colors"] != nil,
	}).Info("Successfully sent unified room info response")
}

// handleRoomInfoRequestWithColorState processes room info requests with color state
func (h *RoomRequestHandler) handleRoomInfoRequestWithColorState(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload RoomInfoRequestPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"includeColors": payload.IncludeColors,
		"correlationId": correlationID,
	}).Info("Processing room info request with color state")

	// Get room state
	room, err := h.roomService.GetRoom(ctx, payload.RoomID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"correlationId": correlationID,
		}).Error("Failed to get room for info request")

		// Send error response
		errorResponse := h.utils.CreateErrorResponse(correlationID, "ROOM_INFO_FAILED", err.Error())
		if payload.ResponseChannel != "" {
			h.utils.PublishResponse(ctx, payload.ResponseChannel, errorResponse)
		}
		return
	}

	// Build response data
	responseData := map[string]interface{}{
		"room": room,
	}

	// Include color state if requested
	if payload.IncludeColors && room.GameType == models.GameTypePrizeWheel {
		// Get color state from cache or generate default
		colorState := h.getOrCreateColorState(ctx, payload.RoomID, room)
		responseData["colorState"] = colorState
	}

	// Send success response
	successResponse := h.utils.CreateSuccessResponse(correlationID, responseData)
	responseChannel := payload.ResponseChannel
	if responseChannel == "" {
		responseChannel = h.utils.CreateResponseChannel(correlationID)
	}
	h.utils.PublishResponse(ctx, responseChannel, successResponse)
}

// handleRoomInfoBroadcastWithColorState processes room info broadcast requests with color state
func (h *RoomRequestHandler) handleRoomInfoBroadcastWithColorState(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload RoomInfoRequestPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        payload.RoomID,
		"userId":        payload.UserID,
		"correlationId": correlationID,
	}).Info("Processing room info broadcast with color state")

	// Get room state
	room, err := h.roomService.GetRoom(ctx, payload.RoomID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":        payload.RoomID,
			"correlationId": correlationID,
		}).Error("Failed to get room for broadcast")
		return
	}

	// Build broadcast data
	broadcastData := map[string]interface{}{
		"event": "room_info_updated",
		"data": map[string]interface{}{
			"room": room,
		},
		"timestamp": requestMsg.Event.Timestamp,
	}

	// Include color state if it's a prize wheel game
	if room.GameType == models.GameTypePrizeWheel {
		colorState := h.getOrCreateColorState(ctx, payload.RoomID, room)
		broadcastData["data"].(map[string]interface{})["colorState"] = colorState
	}

	// Broadcast to room channel
	roomChannel := "room:" + payload.RoomID + ":events"
	h.utils.PublishResponse(ctx, roomChannel, broadcastData)
}

// publishRoomListResponse publishes room list response
func (h *RoomRequestHandler) publishRoomListResponse(ctx context.Context, roomList []map[string]interface{}, correlationID string) {
	// Create response
	response := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"rooms": roomList,
		"count": len(roomList),
	})

	// Publish to lobby updates channel
	h.utils.PublishResponse(ctx, "game:lobby:updates", response)
}

// getOrCreateColorState gets or creates color state for a room
func (h *RoomRequestHandler) getOrCreateColorState(ctx context.Context, roomID string, room *models.Room) ColorStateData {
	// Try to get from cache first
	cacheKey := "room:" + roomID + ":color_selections"
	var colorState ColorStateData

	if err := h.redisClient.Get(ctx, cacheKey, &colorState); err == nil {
		return colorState
	}

	// Create default color state
	availableColors := []map[string]any{
		{"id": "red", "name": "Red", "hex": "#FF0000"},
		{"id": "blue", "name": "Blue", "hex": "#0000FF"},
		{"id": "green", "name": "Green", "hex": "#00FF00"},
		{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
		{"id": "purple", "name": "Purple", "hex": "#800080"},
		{"id": "orange", "name": "Orange", "hex": "#FFA500"},
	}

	colorState = ColorStateData{
		AvailableColors: availableColors,
		PlayerColors:    make(map[string]any),
	}

	// Cache the default state
	h.redisClient.Set(ctx, cacheKey, colorState, 0) // No expiration for color state

	return colorState
}

// buildUnifiedRoomInfoResponse builds a unified room info response structure for socket gateway
func (h *RoomRequestHandler) buildUnifiedRoomInfoResponse(ctx context.Context, roomID, reason string, room *models.Room) map[string]interface{} {
	// Get color state for prize wheel games
	var colorState ColorStateData
	if room.GameType == models.GameTypePrizeWheel {
		colorState = h.getOrCreateColorState(ctx, roomID, room)
	}

	// Build room details in the expected format
	roomDetails := map[string]interface{}{
		"id":              roomID,
		"name":            room.Name,
		"game_type":       h.normalizeGameType(string(room.GameType)),
		"status":          h.normalizeRoomStatus(string(room.Status)),
		"current_players": room.CurrentPlayers,
		"max_players":     room.MaxPlayers,
		"min_players":     room.MinPlayers,
		"bet_amount":      room.Configuration.BetLimits.MinBet,
		"currency":        "USD",
		"prize_pool":      0,
		"has_password":    false, // Default to false for now
		"has_space":       room.CurrentPlayers < room.MaxPlayers,
		"is_private":      false, // Default to false for now
		"is_featured":     false,
	}

	// Build players data
	playersData := map[string]interface{}{
		"list":          h.transformPlayersListForUnified(room.Players, colorState),
		"total":         room.CurrentPlayers,
		"withColors":    len(colorState.PlayerColors),
		"colorMappings": h.getPlayerColorMappings(colorState),
	}

	// Build game data
	gameData := map[string]interface{}{
		"type":              h.normalizeGameType(string(room.GameType)),
		"canStart":          room.CanStartGame(),
		"allColorsSelected": h.allColorsSelected(colorState),
	}

	// Base unified structure
	unifiedInfo := map[string]interface{}{
		"room":    roomDetails,
		"players": playersData,
		"game":    gameData,
	}

	// Add color state for prize wheel games
	if room.GameType == models.GameTypePrizeWheel {
		unifiedInfo["colors"] = map[string]interface{}{
			"available":   h.getAvailableColors(colorState),
			"taken":       h.getTakenColors(colorState),
			"selected":    h.getSelectedColorsMapping(colorState),
			"assignments": h.getPlayerColorMappings(colorState),
			"statistics": map[string]interface{}{
				"totalColors":    8,
				"availableCount": len(h.getAvailableColors(colorState)),
				"takenCount":     len(h.getTakenColors(colorState)),
				"selectionRate":  h.calculateSelectionRate(colorState),
			},
		}
	}

	return unifiedInfo
}

// Helper methods for unified room info response

// normalizeGameType converts game type enum to string
func (h *RoomRequestHandler) normalizeGameType(gameType string) string {
	switch gameType {
	case "GAME_TYPE_PRIZE_WHEEL":
		return "prizewheel"
	case "GAME_TYPE_AMIDAKUJI":
		return "amidakuji"
	default:
		// Convert to lowercase and remove prefixes
		normalized := strings.ToLower(gameType)
		normalized = strings.ReplaceAll(normalized, "game_type_", "")
		normalized = strings.ReplaceAll(normalized, "_", "")
		if normalized == "prizewheel" || normalized == "prize_wheel" {
			return "prizewheel"
		}
		return normalized
	}
}

// normalizeRoomStatus converts room status enum to string
func (h *RoomRequestHandler) normalizeRoomStatus(status string) string {
	switch status {
	case "ROOM_STATUS_WAITING":
		return "waiting"
	case "ROOM_STATUS_ACTIVE":
		return "playing"
	case "ROOM_STATUS_FINISHED":
		return "finished"
	default:
		// Convert to lowercase and remove prefixes
		normalized := strings.ToLower(status)
		normalized = strings.ReplaceAll(normalized, "room_status_", "")
		normalized = strings.ReplaceAll(normalized, "_", "")
		return normalized
	}
}

// transformPlayersListForUnified transforms room players to unified format
func (h *RoomRequestHandler) transformPlayersListForUnified(players []models.RoomPlayer, colorState ColorStateData) []map[string]interface{} {
	var playersList []map[string]interface{}

	for _, player := range players {
		playerData := map[string]interface{}{
			"userId":    player.UserID,
			"username":  player.Username,
			"position":  player.Position,
			"isReady":   player.IsReady,
			"betAmount": player.BetAmount,
			"joinedAt":  player.JoinedAt,
			"status":    "PLAYER_STATUS_ACTIVE",
		}

		// Add color information if available
		if playerColor, exists := colorState.PlayerColors[player.UserID]; exists {
			if colorMap, ok := playerColor.(map[string]any); ok {
				if colorID, ok := colorMap["colorId"].(string); ok {
					playerData["colorId"] = colorID
					playerData["colorName"] = h.getColorName(colorID)
					playerData["colorHex"] = h.getColorHex(colorID)
				}
			}
		}

		playersList = append(playersList, playerData)
	}

	return playersList
}

// getPlayerColorMappings returns a mapping of player IDs to their color information
func (h *RoomRequestHandler) getPlayerColorMappings(colorState ColorStateData) map[string]interface{} {
	playerMappings := make(map[string]interface{})
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				playerMappings[userID] = map[string]interface{}{
					"userId":   userID,
					"username": colorMap["username"],
					"color": map[string]interface{}{
						"id":   colorID,
						"name": h.getColorName(colorID),
						"hex":  h.getColorHex(colorID),
					},
					"selectedAt": time.Now().Format(time.RFC3339),
				}
			}
		}
	}
	return playerMappings
}

// allColorsSelected checks if all colors have been selected
func (h *RoomRequestHandler) allColorsSelected(colorState ColorStateData) bool {
	return len(colorState.PlayerColors) >= 8 // Total number of available colors
}

// getAvailableColors returns an array of available color objects
func (h *RoomRequestHandler) getAvailableColors(colorState ColorStateData) []map[string]interface{} {
	allColors := h.getAllColors()
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter available colors
	var availableColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && !takenColorIds[colorID] {
			availableColors = append(availableColors, color)
		}
	}

	return availableColors
}

// getTakenColors returns an array of taken color objects
func (h *RoomRequestHandler) getTakenColors(colorState ColorStateData) []map[string]interface{} {
	allColors := h.getAllColors()
	takenColorIds := make(map[string]bool)

	// Mark taken colors
	for _, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				takenColorIds[colorID] = true
			}
		}
	}

	// Filter taken colors
	var takenColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && takenColorIds[colorID] {
			takenColors = append(takenColors, color)
		}
	}

	return takenColors
}

// getSelectedColorsMapping returns a mapping of user IDs to their selected color IDs
func (h *RoomRequestHandler) getSelectedColorsMapping(colorState ColorStateData) map[string]interface{} {
	selectedMapping := make(map[string]interface{})
	for userID, playerColor := range colorState.PlayerColors {
		if colorMap, ok := playerColor.(map[string]any); ok {
			if colorID, ok := colorMap["colorId"].(string); ok {
				selectedMapping[userID] = colorID
			}
		}
	}
	return selectedMapping
}

// calculateSelectionRate calculates the percentage of colors that have been selected
func (h *RoomRequestHandler) calculateSelectionRate(colorState ColorStateData) float64 {
	totalColors := 8.0
	takenColors := float64(len(colorState.PlayerColors))
	if totalColors == 0 {
		return 0.0
	}
	return (takenColors / totalColors) * 100.0
}

// getAllColors returns all available colors
func (h *RoomRequestHandler) getAllColors() []map[string]interface{} {
	return []map[string]interface{}{
		{"id": "red", "name": "Red", "hex": "#FF0000"},
		{"id": "blue", "name": "Blue", "hex": "#0000FF"},
		{"id": "green", "name": "Green", "hex": "#00FF00"},
		{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
		{"id": "purple", "name": "Purple", "hex": "#800080"},
		{"id": "orange", "name": "Orange", "hex": "#FFA500"},
		{"id": "pink", "name": "Pink", "hex": "#FFC0CB"},
		{"id": "teal", "name": "Teal", "hex": "#008080"},
	}
}

// getColorName returns the display name for a color ID
func (h *RoomRequestHandler) getColorName(colorID string) string {
	colorNames := map[string]string{
		"red":    "Red",
		"blue":   "Blue",
		"green":  "Green",
		"yellow": "Yellow",
		"purple": "Purple",
		"orange": "Orange",
		"pink":   "Pink",
		"teal":   "Teal",
	}
	if name, exists := colorNames[colorID]; exists {
		return name
	}
	return colorID
}

// getColorHex returns the hex color code for a color ID
func (h *RoomRequestHandler) getColorHex(colorID string) string {
	colorHex := map[string]string{
		"red":    "#FF0000",
		"blue":   "#0000FF",
		"green":  "#00FF00",
		"yellow": "#FFFF00",
		"purple": "#800080",
		"orange": "#FFA500",
		"pink":   "#FFC0CB",
		"teal":   "#008080",
	}
	if hex, exists := colorHex[colorID]; exists {
		return hex
	}
	return "#000000"
}
