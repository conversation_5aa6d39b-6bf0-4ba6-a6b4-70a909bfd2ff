package request

import "time"

// RequestMessage represents a request message from Redis
type RequestMessage struct {
	Event struct {
		Type      string    `json:"type"`
		Payload   any       `json:"payload"`
		Timestamp time.Time `json:"timestamp"`
	} `json:"event"`
	Metadata struct {
		ServiceID       string `json:"serviceId"`
		Version         string `json:"version"`
		CorrelationID   string `json:"correlationId"`
		ResponseChannel string `json:"responseChannel,omitempty"`
		Priority        int    `json:"priority"`
	} `json:"metadata"`
}

// RoomListRequestPayload represents the payload for room list requests
type RoomListRequestPayload struct {
	RequestedBy string `json:"requestedBy"`
	Timestamp   string `json:"timestamp"`
	Filters     struct {
		Status   string `json:"status"`
		HasSpace bool   `json:"hasSpace"`
		Limit    int    `json:"limit"`
	} `json:"filters"`
}

// SubscribeLobbyPayload represents the payload for lobby subscription requests
type SubscribeLobbyPayload struct {
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	SocketID  string `json:"socketId"`
	Timestamp string `json:"timestamp"`
}

// TokenVerificationPayload represents the payload for token verification requests
type TokenVerificationPayload struct {
	Token       string   `json:"token"`
	UserID      string   `json:"userId"`
	Username    string   `json:"username"`
	SessionID   string   `json:"sessionId"`
	Roles       []string `json:"roles,omitempty"`       // API Gateway format
	Permissions []string `json:"permissions,omitempty"` // API Gateway format
	JTI         string   `json:"jti,omitempty"`         // API Gateway format
	DeviceID    string   `json:"device_id,omitempty"`   // API Gateway format
	Timestamp   string   `json:"timestamp"`
}

// JoinRoomPayload represents the payload for join room requests
type JoinRoomPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Password  string `json:"password,omitempty"`
	BetAmount int64  `json:"betAmount"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// PlayerReadyPayload represents the payload for player ready requests
type PlayerReadyPayload struct {
	RoomID          string `json:"roomId"`
	UserID          string `json:"userId"`
	Username        string `json:"username"`
	IsReady         bool   `json:"isReady"`
	Token           string `json:"token"`
	ResponseChannel string `json:"responseChannel"`
	Timestamp       string `json:"timestamp"`
}

// LeaveRoomPayload represents the payload for leave room requests
type LeaveRoomPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Reason    string `json:"reason"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// WheelColorPayload represents the payload for wheel color selection requests
type WheelColorPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	ColorID   string `json:"colorId"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// ColorSelectionPayload represents the payload for color selection requests (spec format)
type ColorSelectionPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Color     string `json:"color"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// AmidakujiPositionPayload represents the payload for Amidakuji position selection requests
type AmidakujiPositionPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Position  int    `json:"position"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// WheelColorInfo represents information about a wheel color
type WheelColorInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Hex  string `json:"hex"`
}

// ColorStateData represents the complete color state for a room
type ColorStateData struct {
	AvailableColors []map[string]any `json:"availableColors"`
	PlayerColors    map[string]any   `json:"playerColors"`
}

// AmidakujiPattern represents the Amidakuji game pattern
type AmidakujiPattern struct {
	PlayerCount  int       `json:"playerCount"`
	Rows         int       `json:"rows"`
	Lines        [][]bool  `json:"lines"`
	PlayerPaths  [][]int   `json:"playerPaths"`
	WinnerUserID string    `json:"winnerUserId"`
	WinnerIndex  int       `json:"winnerIndex"`
	Timestamp    time.Time `json:"timestamp"`
}

// GetPlayerStatusPayload represents the payload for get player status requests
type GetPlayerStatusPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// GetRoomStatePayload represents the payload for get room state requests
type GetRoomStatePayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Token     string `json:"token"`
	Timestamp string `json:"timestamp"`
}

// RoomInfoRequestPayload represents the payload for room info requests with color state
type RoomInfoRequestPayload struct {
	RoomID          string `json:"roomId"`
	UserID          string `json:"userId"`
	Username        string `json:"username"`
	RequestType     string `json:"requestType"`
	IncludeColors   bool   `json:"includeColors"`
	ResponseChannel string `json:"responseChannel"`
	Timestamp       string `json:"timestamp"`
}

// UnifiedRoomInfoPayload represents the payload for unified room info requests from socket gateway
type UnifiedRoomInfoPayload struct {
	RoomID        string `json:"roomId"`
	Reason        string `json:"reason"`
	IncludeColors bool   `json:"includeColors"`
	RequestType   string `json:"requestType"`
	Timestamp     string `json:"timestamp"`
}

// PlayerKickedPayload represents the payload for player kicked notifications
type PlayerKickedPayload struct {
	RoomID    string `json:"roomId"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	Reason    string `json:"reason"`
	AdminID   string `json:"adminId"`
	Timestamp string `json:"timestamp"`
}

// RoomListUpdatedPayload represents the payload for room list updated events
type RoomListUpdatedPayload struct {
	UpdateType string `json:"updateType"`
	RoomID     string `json:"roomId"`
	Timestamp  string `json:"timestamp"`
}
