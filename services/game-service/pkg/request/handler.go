package request

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/internal/services"
	"github.com/xzgame/game-service/pkg/clients/auth"
	"github.com/xzgame/game-service/pkg/redis"
)

// <PERSON><PERSON> handles Redis requests from other services
type Handler struct {
	redisClient      *redis.RedisClient
	roomService      services.RoomService
	lobbyService     services.LobbyService
	gameStateManager *services.GameStateManager
	logger           *logrus.Logger
	pendingRequests  map[string]bool // Track pending requests to prevent duplicates
	requestsMutex    sync.RWMutex    // Protect pendingRequests map
	isRunning        bool            // Track if the service is running
	startTime        time.Time       // Track when the service started
	processedCount   int64           // Track number of processed requests
	lastActivity     time.Time       // Track last activity
	activityMutex    sync.RWMutex    // Protect activity tracking
	utils            *RequestUtils

	// Sub-handlers for different request types
	roomHandler  *RoomRequestHandler
	gameHandler  *GameRequestHandler
	lobbyHandler *LobbyRequestHandler
	authHandler  *AuthRequestHandler
	adminHandler *AdminRequestHandler
}

// NewHandler creates a new request handler service
func NewHandler(
	redisClient *redis.RedisClient,
	roomService services.RoomService,
	lobbyService services.LobbyService,
	gameStateManager *services.GameStateManager,
) *Handler {
	logger := logrus.New()
	utils := NewRequestUtils(redisClient, logger)

	handler := &Handler{
		redisClient:      redisClient,
		roomService:      roomService,
		lobbyService:     lobbyService,
		gameStateManager: gameStateManager,
		logger:           logger,
		pendingRequests:  make(map[string]bool),
		isRunning:        false,
		startTime:        time.Now(),
		processedCount:   0,
		lastActivity:     time.Now(),
		utils:            utils,
	}

	// Initialize auth client
	authServiceURL := "http://localhost:8081" // Default auth service URL
	authClient := auth.NewAuthClient(authServiceURL)

	// Initialize sub-handlers (removing manager client dependencies)
	handler.roomHandler = NewRoomRequestHandler(redisClient, roomService, utils, logger)
	handler.gameHandler = NewGameRequestHandler(redisClient, gameStateManager, roomService, utils, logger)
	handler.lobbyHandler = NewLobbyRequestHandler(redisClient, lobbyService, utils, logger)
	handler.authHandler = NewAuthRequestHandler(redisClient, authClient, utils, logger)
	handler.adminHandler = NewAdminRequestHandler(redisClient, utils, logger)

	return handler
}

// Start begins listening for Redis requests
func (h *Handler) Start(ctx context.Context) error {
	h.logger.Info("Starting Redis request handler service")

	// Mark as running
	h.activityMutex.Lock()
	h.isRunning = true
	h.startTime = time.Now()
	h.activityMutex.Unlock()

	// Subscribe to multiple channels
	pubsub := h.redisClient.Subscribe(ctx, "game:requests", "game:lobby:updates", "admin:notifications")
	defer pubsub.Close()

	// Listen for messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			h.logger.Info("Request handler service stopped")
			// Mark as not running
			h.activityMutex.Lock()
			h.isRunning = false
			h.activityMutex.Unlock()
			return ctx.Err()
		case msg := <-ch:
			h.handleMessage(ctx, msg.Payload, msg.Channel)
		}
	}
}

// handleMessage processes incoming Redis messages
func (h *Handler) handleMessage(ctx context.Context, payload string, channel string) {
	// Update activity tracking
	h.activityMutex.Lock()
	h.lastActivity = time.Now()
	h.processedCount++
	h.activityMutex.Unlock()

	var requestMsg RequestMessage
	if err := json.Unmarshal([]byte(payload), &requestMsg); err != nil {
		h.logger.WithError(err).WithField("channel", channel).Error("Failed to parse request message")
		return
	}

	// Validate request message
	if err := h.utils.ValidateRequestMessage(&requestMsg); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "message validation")
		return
	}

	h.utils.LogRequestProcessing(&requestMsg, channel)

	// Route based on channel
	switch channel {
	case "game:requests":
		h.handleGameRequest(ctx, requestMsg)
	case "game:lobby:updates":
		h.handleLobbyUpdate(ctx, requestMsg)
	case "admin:notifications":
		h.handleAdminNotification(ctx, requestMsg)
	default:
		h.logger.WithField("channel", channel).Warn("Unknown channel")
	}
}

// handleGameRequest processes game service requests
func (h *Handler) handleGameRequest(ctx context.Context, requestMsg RequestMessage) {
	// Check for duplicate requests
	if h.isDuplicateRequest(requestMsg.Metadata.CorrelationID) {
		h.logger.WithField("correlationId", requestMsg.Metadata.CorrelationID).Warn("Duplicate request ignored")
		return
	}

	// Mark request as being processed
	h.markRequestProcessing(requestMsg.Metadata.CorrelationID)
	defer h.markRequestComplete(requestMsg.Metadata.CorrelationID)

	switch requestMsg.Event.Type {
	case "request_room_list", "join_room", "leave_room", "get_room_state", "get_unified_room_info":
		h.roomHandler.HandleRequest(ctx, requestMsg)
	case "select_wheel_color", "color_selection", "select_amidakuji_position", "player_ready", "get_player_status":
		h.gameHandler.HandleRequest(ctx, requestMsg)
	case "subscribe_lobby":
		h.lobbyHandler.HandleRequest(ctx, requestMsg)
	case "verify_token":
		h.authHandler.HandleRequest(ctx, requestMsg)
	case "room_info_request_with_color_state", "room_info_broadcast_with_color_state":
		h.roomHandler.HandleRequest(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown request type")
	}
}

// handleLobbyUpdate processes lobby update events
func (h *Handler) handleLobbyUpdate(ctx context.Context, requestMsg RequestMessage) {
	h.lobbyHandler.HandleUpdate(ctx, requestMsg)
}

// handleAdminNotification processes admin notification events
func (h *Handler) handleAdminNotification(ctx context.Context, requestMsg RequestMessage) {
	h.adminHandler.HandleNotification(ctx, requestMsg)
}

// isDuplicateRequest checks if a request is already being processed
func (h *Handler) isDuplicateRequest(correlationID string) bool {
	h.requestsMutex.RLock()
	defer h.requestsMutex.RUnlock()
	return h.pendingRequests[correlationID]
}

// markRequestProcessing marks a request as being processed
func (h *Handler) markRequestProcessing(correlationID string) {
	h.requestsMutex.Lock()
	defer h.requestsMutex.Unlock()
	h.pendingRequests[correlationID] = true
}

// markRequestComplete marks a request as complete
func (h *Handler) markRequestComplete(correlationID string) {
	h.requestsMutex.Lock()
	defer h.requestsMutex.Unlock()
	delete(h.pendingRequests, correlationID)
}

// GetStats returns handler statistics
func (h *Handler) GetStats() map[string]interface{} {
	h.activityMutex.RLock()
	defer h.activityMutex.RUnlock()

	return map[string]interface{}{
		"isRunning":       h.isRunning,
		"startTime":       h.startTime,
		"processedCount":  h.processedCount,
		"lastActivity":    h.lastActivity,
		"pendingRequests": len(h.pendingRequests),
	}
}

// Stop gracefully stops the handler
func (h *Handler) Stop() {
	h.activityMutex.Lock()
	defer h.activityMutex.Unlock()
	h.isRunning = false
	h.logger.Info("Request handler stopped")
}

// IsHealthy checks if the service is healthy
func (h *Handler) IsHealthy() bool {
	h.activityMutex.RLock()
	defer h.activityMutex.RUnlock()

	if !h.isRunning {
		return false
	}

	// Consider unhealthy if no activity for more than 5 minutes
	timeSinceLastActivity := time.Since(h.lastActivity)
	return timeSinceLastActivity <= 5*time.Minute
}
