package request

import (
	"context"
	"encoding/json"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/internal/services"
	"github.com/xzgame/game-service/pkg/redis"
)

// LobbyRequestHandler handles lobby-related requests
type LobbyRequestHandler struct {
	redisClient  *redis.RedisClient
	lobbyService services.LobbyService
	utils        *RequestUtils
	logger       *logrus.Logger
}

// NewLobbyRequestHandler creates a new lobby request handler
func NewLobbyRequestHandler(
	redisClient *redis.RedisClient,
	lobbyService services.LobbyService,
	utils *RequestUtils,
	logger *logrus.Logger,
) *LobbyRequestHandler {
	return &LobbyRequestHandler{
		redisClient:  redisClient,
		lobbyService: lobbyService,
		utils:        utils,
		logger:       logger,
	}
}

// HandleRequest processes lobby-related requests
func (h *LobbyRequestHandler) HandleRequest(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "subscribe_lobby":
		h.handleSubscribeLobby(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown lobby request type")
	}
}

// HandleUpdate processes lobby update events
func (h *LobbyRequestHandler) HandleUpdate(ctx context.Context, requestMsg RequestMessage) {
	switch requestMsg.Event.Type {
	case "room_list_updated":
		h.handleRoomListUpdated(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown lobby update type")
	}
}

// handleSubscribeLobby processes lobby subscription requests
func (h *LobbyRequestHandler) handleSubscribeLobby(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload SubscribeLobbyPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        payload.UserID,
		"username":      payload.Username,
		"socketId":      payload.SocketID,
		"correlationId": correlationID,
	}).Info("Processing lobby subscription request")

	// Use the enhanced lobby service to handle subscription
	subscriptionRequest := services.LobbySubscriptionRequest{
		UserID:    payload.UserID,
		Username:  payload.Username,
		SocketID:  payload.SocketID,
		Timestamp: requestMsg.Event.Timestamp.Format(time.RFC3339),
	}

	subscriptionResponse, err := h.lobbyService.HandleLobbySubscription(ctx, subscriptionRequest)
	if err != nil {
		h.logger.WithError(err).Error("Failed to handle lobby subscription")
		errorResponse := h.utils.CreateErrorResponse(correlationID, "LOBBY_SUBSCRIPTION_FAILED", "Failed to subscribe to lobby")
		responseChannel := h.utils.CreateResponseChannel(correlationID)
		h.utils.PublishResponse(ctx, responseChannel, errorResponse)
		return
	}

	// Convert LobbyRoomInfo to map[string]interface{} for compatibility
	roomList := make([]map[string]interface{}, len(subscriptionResponse.Rooms))
	for i, room := range subscriptionResponse.Rooms {
		roomList[i] = map[string]interface{}{
			"id":              room.ID,
			"name":            room.Name,
			"game_type":       room.GameType,
			"status":          room.Status,
			"current_players": room.PlayerCount,
			"max_players":     room.MaxPlayers,
			"min_players":     room.MinPlayers,
			"bet_amount":      room.BetAmount,
			"is_private":      room.IsPrivate,
			"created_at":      room.CreatedAt.Format(time.RFC3339),
			"updated_at":      room.UpdatedAt.Format(time.RFC3339),
			"has_space":       room.HasSpace,
			"is_featured":     room.IsFeatured,
		}
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        payload.UserID,
		"roomCount":     len(roomList),
		"correlationId": correlationID,
	}).Info("Successfully subscribed to lobby")

	// Send success response with initial room list
	successResponse := h.utils.CreateSuccessResponse(correlationID, map[string]interface{}{
		"subscribed": true,
		"rooms":      roomList,
		"count":      len(roomList),
	})
	responseChannel := h.utils.CreateResponseChannel(correlationID)
	h.utils.PublishResponse(ctx, responseChannel, successResponse)

	// Also publish to lobby updates channel for real-time updates using standardized format
	lobbyUpdate := map[string]interface{}{
		"event": map[string]interface{}{
			"type": "room_list_updated",
			"payload": map[string]interface{}{
				"action":   "subscribe",
				"userId":   payload.UserID,
				"username": payload.Username,
				"socketId": payload.SocketID,
				"rooms":    roomList,
				"count":    len(roomList),
			},
			"timestamp": requestMsg.Event.Timestamp,
		},
		"metadata": map[string]interface{}{
			"serviceId":     "game-service",
			"version":       "1.0.0",
			"correlationId": correlationID,
		},
	}
	h.utils.PublishResponse(ctx, "game:lobby:updates", lobbyUpdate)
}

// handleRoomListUpdated processes room list updated events
func (h *LobbyRequestHandler) handleRoomListUpdated(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload marshaling")
		return
	}

	var payload RoomListUpdatedPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.utils.LogRequestError(&requestMsg, err, "payload parsing")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"updateType":    payload.UpdateType,
		"roomId":        payload.RoomID,
		"correlationId": correlationID,
	}).Info("Processing room list updated event")

	// For now, return empty room list as lobby service only has publish methods
	// In a real implementation, this would get updated rooms from a room service
	rooms := []map[string]interface{}{}

	// Use the empty room list directly
	roomList := rooms

	h.logger.WithFields(logrus.Fields{
		"updateType":    payload.UpdateType,
		"roomId":        payload.RoomID,
		"roomCount":     len(rooms),
		"correlationId": correlationID,
	}).Info("Broadcasting updated room list")

	// Broadcast updated room list to all lobby subscribers
	lobbyUpdate := map[string]interface{}{
		"event": "room_list_updated",
		"data": map[string]interface{}{
			"updateType": payload.UpdateType,
			"roomId":     payload.RoomID,
			"rooms":      roomList,
			"count":      len(rooms),
		},
		"timestamp": requestMsg.Event.Timestamp,
	}
	h.utils.PublishResponse(ctx, "game:lobby:updates", lobbyUpdate)

	// Note: Lobby service doesn't maintain internal state, it just publishes events
}

// GetLobbyStats returns lobby statistics
func (h *LobbyRequestHandler) GetLobbyStats(ctx context.Context) (map[string]interface{}, error) {
	// For now, return basic stats as lobby service only has publish methods
	// In a real implementation, this would get stats from a room service

	// Note: Subscriber count is managed by Socket Gateway service
	subscriberCount := 0

	return map[string]interface{}{
		"totalRooms":      0, // Would get from room service
		"subscriberCount": subscriberCount,
		"lastUpdated":     time.Now().UTC().Format(time.RFC3339),
	}, nil
}

// BroadcastLobbyMessage broadcasts a message to all lobby subscribers
func (h *LobbyRequestHandler) BroadcastLobbyMessage(ctx context.Context, messageType string, data interface{}) error {
	lobbyMessage := map[string]interface{}{
		"event":     messageType,
		"data":      data,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	return h.utils.PublishResponse(ctx, "game:lobby:updates", lobbyMessage)
}
