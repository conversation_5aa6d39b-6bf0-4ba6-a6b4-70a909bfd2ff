package request

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/game-service/pkg/redis"
)

// RequestUtils provides utility functions for request handling
type RequestUtils struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewRequestUtils creates a new request utils instance
func NewRequestUtils(redisClient *redis.RedisClient, logger *logrus.Logger) *RequestUtils {
	return &RequestUtils{
		redisClient: redisClient,
		logger:      logger,
	}
}

// randomInt generates a random integer between min (inclusive) and max (exclusive)
func (u *RequestUtils) RandomInt(min, max int) int {
	if max <= min {
		return min
	}
	return min + int(time.Now().UnixNano()%int64(max-min))
}

// generateAmidakujiLines generates a 2D boolean pattern for Amidakuji horizontal lines
func (u *RequestUtils) GenerateAmidakujiLines(playerCount, rows int) [][]bool {
	// Create a 2D boolean array representing horizontal lines
	// pattern[row][column] = true means there's a horizontal line from column to column+1 at this row
	pattern := make([][]bool, rows)
	for i := range pattern {
		pattern[i] = make([]bool, playerCount-1) // playerCount-1 possible horizontal lines per row
	}

	// Generate random horizontal lines ensuring no adjacent lines in the same row
	for row := 0; row < rows; row++ {
		// Randomly decide how many lines to place in this row (0 to playerCount/2)
		maxLines := (playerCount - 1) / 2
		if maxLines == 0 {
			maxLines = 1
		}

		lineCount := u.RandomInt(0, maxLines+1)

		// Place lines ensuring they don't overlap
		placedLines := make([]bool, playerCount-1)
		for i := 0; i < lineCount; i++ {
			attempts := 0
			for attempts < 10 { // Prevent infinite loop
				pos := u.RandomInt(0, playerCount-1)
				// Check if this position and adjacent positions are free
				if !placedLines[pos] && (pos == 0 || !placedLines[pos-1]) && (pos == playerCount-2 || !placedLines[pos+1]) {
					pattern[row][pos] = true
					placedLines[pos] = true
					break
				}
				attempts++
			}
		}
	}

	return pattern
}

// selectRandomWinner selects a random winner index
func (u *RequestUtils) SelectRandomWinner(playerCount int) int {
	return u.RandomInt(0, playerCount)
}

// traceAmidakujiPath traces the path from a starting position through the Amidakuji pattern
func (u *RequestUtils) TraceAmidakujiPath(startColumn int, pattern [][]bool, playerCount, rows int) []int {
	path := make([]int, 0, rows+1)
	currentColumn := startColumn
	path = append(path, currentColumn)

	for row := 0; row < rows; row++ {
		// Check if there's a line to the right
		if currentColumn < playerCount-1 && pattern[row][currentColumn] {
			currentColumn++
		} else if currentColumn > 0 && pattern[row][currentColumn-1] {
			// Check if there's a line to the left
			currentColumn--
		}
		path = append(path, currentColumn)
	}

	return path
}

// storeAmidakujiPattern stores the Amidakuji pattern in Redis cache
func (u *RequestUtils) StoreAmidakujiPattern(ctx context.Context, roomID string, pattern *AmidakujiPattern) error {
	cacheKey := fmt.Sprintf("room:%s:amidakuji_pattern", roomID)
	ttl := 24 * time.Hour // Cache for 24 hours

	return u.redisClient.Set(ctx, cacheKey, pattern, ttl)
}

// broadcastAmidakujiPattern broadcasts the Amidakuji pattern to all players in the room
func (u *RequestUtils) BroadcastAmidakujiPattern(ctx context.Context, roomID string, pattern *AmidakujiPattern) {
	// Create pattern broadcast message
	broadcastMessage := map[string]any{
		"roomId":    roomID,
		"pattern":   pattern,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Publish to room-specific channel for real-time updates
	roomChannel := fmt.Sprintf("room:%s:amidakuji_pattern", roomID)
	if err := u.redisClient.Publish(ctx, roomChannel, broadcastMessage); err != nil {
		u.logger.WithError(err).WithFields(logrus.Fields{
			"roomId":  roomID,
			"channel": roomChannel,
		}).Error("Failed to broadcast Amidakuji pattern")
	} else {
		u.logger.WithFields(logrus.Fields{
			"roomId":       roomID,
			"channel":      roomChannel,
			"playerCount":  pattern.PlayerCount,
			"winnerUserID": pattern.WinnerUserID,
		}).Info("Successfully broadcasted Amidakuji pattern")
	}
}

// ValidateRequestMessage validates the basic structure of a request message
func (u *RequestUtils) ValidateRequestMessage(msg *RequestMessage) error {
	if msg.Event.Type == "" {
		return fmt.Errorf("event type is required")
	}
	if msg.Metadata.CorrelationID == "" {
		return fmt.Errorf("correlation ID is required")
	}
	if msg.Metadata.ServiceID == "" {
		return fmt.Errorf("service ID is required")
	}
	return nil
}

// LogRequestProcessing logs the processing of a request with standard fields
func (u *RequestUtils) LogRequestProcessing(msg *RequestMessage, channel string) {
	u.logger.WithFields(logrus.Fields{
		"type":          msg.Event.Type,
		"serviceId":     msg.Metadata.ServiceID,
		"correlationId": msg.Metadata.CorrelationID,
		"channel":       channel,
	}).Info("Processing message")
}

// LogRequestError logs an error during request processing
func (u *RequestUtils) LogRequestError(msg *RequestMessage, err error, context string) {
	u.logger.WithError(err).WithFields(logrus.Fields{
		"type":          msg.Event.Type,
		"correlationId": msg.Metadata.CorrelationID,
		"context":       context,
	}).Error("Request processing error")
}

// CreateResponseChannel creates a response channel name for a correlation ID
func (u *RequestUtils) CreateResponseChannel(correlationID string) string {
	return fmt.Sprintf("response:%s", correlationID)
}

// PublishResponse publishes a response to a specific channel
func (u *RequestUtils) PublishResponse(ctx context.Context, channel string, response interface{}) error {
	return u.redisClient.Publish(ctx, channel, response)
}

// CreateErrorResponse creates a standardized error response
func (u *RequestUtils) CreateErrorResponse(correlationID, errorCode, message string) map[string]interface{} {
	return map[string]interface{}{
		"success":       false,
		"error":         errorCode,
		"message":       message,
		"correlationId": correlationID,
		"timestamp":     time.Now().UTC().Format(time.RFC3339),
	}
}

// CreateSuccessResponse creates a standardized success response
func (u *RequestUtils) CreateSuccessResponse(correlationID string, data interface{}) map[string]interface{} {
	return map[string]interface{}{
		"success":       true,
		"data":          data,
		"correlationId": correlationID,
		"timestamp":     time.Now().UTC().Format(time.RFC3339),
	}
}
