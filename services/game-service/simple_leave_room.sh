#!/bin/bash

# Simple script to make a specific user leave a specific room
# Usage: ./simple_leave_room.sh <user_id> <room_id> [username]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}

# Check arguments
if [ $# -lt 2 ]; then
    echo -e "${RED}Usage: $0 <user_id> <room_id> [username]${NC}"
    echo "Example: $0 68334427b8ef34dc195f27bd 68412c9af494b684c1c18ecf res"
    exit 1
fi

USER_ID="$1"
ROOM_ID="$2"
USERNAME="${3:-$USER_ID}"

echo -e "${YELLOW}=== Simple Leave Room ===${NC}"
echo "User ID: $USER_ID"
echo "Room ID: $ROOM_ID"
echo "Username: $USERNAME"
echo

# Function to check if Redis is available
check_redis() {
    echo -e "${YELLOW}Checking Redis connection...${NC}"
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping >/dev/null 2>&1; then
            echo -e "${GREEN}✓ Redis is available${NC}"
            return 0
        else
            echo -e "${RED}✗ Redis is not responding${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ redis-cli not found${NC}"
        return 1
    fi
}

# Function to publish a simple leave room request
publish_leave_request() {
    local correlation_id="leave-room-${USER_ID}-$(date +%s%3N)-simple"
    local response_channel="room:leave:response:${correlation_id}"
    
    echo -e "${YELLOW}Publishing leave room request...${NC}"
    echo "Correlation ID: $correlation_id"
    
    # Create the simple leave request message
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "simple-leave-client",
    "version": "1.0",
    "priority": "normal",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "leave_room",
    "payload": {
      "roomId": "$ROOM_ID",
      "userId": "$USER_ID",
      "username": "$USERNAME",
      "reason": "user_request",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo -e "${YELLOW}Request Message:${NC}"
    echo "$request_message" | jq '.' 2>/dev/null || echo "$request_message"
    echo
    
    # Publish the request
    echo -e "${YELLOW}Publishing to Redis channel 'room:leave'...${NC}"
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "room:leave" 2>/dev/null || {
        echo -e "${RED}✗ Failed to publish (Redis auth issue)${NC}"
        return 1
    }
    
    echo -e "${GREEN}✓ Leave request published${NC}"
    echo "Response channel: $response_channel"
    return 0
}

# Function to listen for response (optional)
listen_for_response() {
    local response_channel="$1"
    local timeout=${2:-5}
    
    echo -e "${YELLOW}Listening for response on channel '$response_channel' (timeout: ${timeout}s)...${NC}"
    
    # Use timeout to limit waiting time
    local response=$(timeout $timeout redis-cli -h $REDIS_HOST -p $REDIS_PORT SUBSCRIBE "$response_channel" 2>/dev/null | head -n 3 | tail -n 1 || true)
    
    if [ -n "$response" ]; then
        echo -e "${GREEN}✓ Response received:${NC}"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        echo
        
        # Check if it was successful
        if echo "$response" | jq -e '.success == true' >/dev/null 2>&1; then
            echo -e "${GREEN}✓ User successfully left the room${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠ Leave operation may have failed${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠ No response received within timeout (operation may still be processing)${NC}"
        return 1
    fi
}

# Function to clear specific user cache
clear_user_cache() {
    echo -e "${YELLOW}Clearing user-specific cache...${NC}"
    
    # Clear user balance cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "player:${USER_ID}:balance" >/dev/null 2>&1 || true
    
    # Clear room state cache to force refresh
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:state:${ROOM_ID}" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✓ Cache cleared${NC}"
}

# Main function
main() {
    echo -e "${GREEN}Starting simple leave room process${NC}"
    echo "================================================"
    echo
    
    # Check prerequisites
    if ! check_redis; then
        echo -e "${RED}Redis is required for this operation${NC}"
        exit 1
    fi
    
    # Check if jq is available for JSON parsing
    if ! command -v jq >/dev/null 2>&1; then
        echo -e "${YELLOW}Warning: jq not found, JSON output may not be formatted${NC}"
    fi
    
    echo
    
    # Publish leave request
    if publish_leave_request; then
        local correlation_id="leave-room-${USER_ID}-$(date +%s%3N)-simple"
        local response_channel="room:leave:response:${correlation_id}"
        
        # Listen for response (optional, with short timeout)
        listen_for_response "$response_channel" 5 || true
    else
        echo -e "${RED}✗ Failed to publish leave request${NC}"
        exit 1
    fi
    
    # Clear cache to ensure fresh data
    clear_user_cache
    
    echo
    echo -e "${GREEN}Leave Room Summary:${NC}"
    echo "- User ID: $USER_ID"
    echo "- Room ID: $ROOM_ID"
    echo "- Username: $USERNAME"
    echo "- Leave request sent: ✓"
    echo "- Cache cleared: ✓"
    echo
    echo -e "${YELLOW}Expected result:${NC}"
    echo "- User $USERNAME should be removed from room $ROOM_ID"
    echo "- Room player count should decrease by 1"
    echo "- Other players remain unaffected"
    echo
    echo -e "${GREEN}✓ Simple leave operation completed${NC}"
}

# Run the leave operation
main "$@"
