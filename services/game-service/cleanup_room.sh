#!/bin/bash

# Script to clean up a specific room with duplicate players and capacity issues
# Usage: ./cleanup_room.sh <room_id>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}

# Check arguments
if [ $# -lt 1 ]; then
    echo -e "${RED}Usage: $0 <room_id>${NC}"
    echo "Example: $0 68412c9af494b684c1c18ecf"
    exit 1
fi

ROOM_ID="$1"

echo -e "${YELLOW}=== Cleaning Up Room ===${NC}"
echo "Room ID: $ROOM_ID"
echo

# Function to check if Redis is available
check_redis() {
    echo -e "${YELLOW}Checking Redis connection...${NC}"
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping >/dev/null 2>&1; then
            echo -e "${GREEN}✓ Redis is available${NC}"
            return 0
        else
            echo -e "${RED}✗ Redis is not responding${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ redis-cli not found${NC}"
        return 1
    fi
}

# Function to clear room cache
clear_room_cache() {
    echo -e "${YELLOW}Clearing room cache...${NC}"
    
    # Clear room state cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:state:${ROOM_ID}" >/dev/null 2>&1 || true
    
    # Clear room players cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:players:${ROOM_ID}" >/dev/null 2>&1 || true
    
    # Clear room config cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:config:${ROOM_ID}" >/dev/null 2>&1 || true
    
    # Clear color selections cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:${ROOM_ID}:color_selections" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✓ Room cache cleared${NC}"
}

# Function to publish room cleanup request
publish_cleanup_request() {
    local correlation_id="cleanup-room-${ROOM_ID}-$(date +%s%3N)"
    local response_channel="room:cleanup:response:${correlation_id}"
    
    echo -e "${YELLOW}Publishing room cleanup request...${NC}"
    echo "Correlation ID: $correlation_id"
    
    # Create the cleanup message
    local cleanup_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "cleanup-script",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "cleanup_room",
    "payload": {
      "roomId": "$ROOM_ID",
      "action": "remove_duplicates_and_fix_capacity",
      "maxPlayers": 2,
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo -e "${YELLOW}Cleanup Message:${NC}"
    echo "$cleanup_message" | jq '.' 2>/dev/null || echo "$cleanup_message"
    echo
    
    # Publish the cleanup request
    echo -e "${YELLOW}Publishing to Redis channel 'admin:cleanup'...${NC}"
    echo "$cleanup_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "admin:cleanup" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✓ Cleanup request published${NC}"
}

# Function to remove specific duplicate user
remove_duplicate_user() {
    local user_id="$1"
    local correlation_id="remove-duplicate-${user_id}-$(date +%s%3N)"
    
    echo -e "${YELLOW}Removing duplicate user: $user_id${NC}"
    
    # Create leave request for the duplicate
    local leave_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "serviceId": "cleanup-script",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "leave_room",
    "payload": {
      "roomId": "$ROOM_ID",
      "userId": "$user_id",
      "reason": "duplicate_cleanup",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    # Publish multiple leave requests to ensure removal
    for i in {1..3}; do
        echo "$leave_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "room:leave" >/dev/null 2>&1 || true
        sleep 1
    done
    
    echo -e "${GREEN}✓ Duplicate removal requests sent for user: $user_id${NC}"
}

# Function to fix room capacity
fix_room_capacity() {
    echo -e "${YELLOW}Publishing room capacity fix request...${NC}"
    
    local correlation_id="fix-capacity-${ROOM_ID}-$(date +%s%3N)"
    
    local fix_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "serviceId": "cleanup-script",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "fix_room_capacity",
    "payload": {
      "roomId": "$ROOM_ID",
      "maxPlayers": 2,
      "action": "enforce_capacity_limit",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    echo "$fix_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "admin:room_fix" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✓ Capacity fix request published${NC}"
}

# Main cleanup function
main() {
    echo -e "${GREEN}Starting room cleanup process${NC}"
    echo "================================================"
    echo
    
    # Check prerequisites
    if ! check_redis; then
        echo -e "${RED}Redis is required for this cleanup${NC}"
        exit 1
    fi
    
    echo
    
    # Clear room cache first
    clear_room_cache
    echo
    
    # Remove known duplicate user (from the logs)
    echo -e "${YELLOW}Removing known duplicate user...${NC}"
    remove_duplicate_user "68334427b8ef34dc195f27bd"
    echo
    
    # Publish general cleanup request
    publish_cleanup_request
    echo
    
    # Fix room capacity
    fix_room_capacity
    echo
    
    # Clear cache again after cleanup
    clear_room_cache
    echo
    
    echo -e "${GREEN}Cleanup Summary:${NC}"
    echo "- Room ID: $ROOM_ID"
    echo "- Cache cleared: ✓"
    echo "- Duplicate user removal: ✓"
    echo "- Cleanup requests sent: ✓"
    echo "- Capacity fix requested: ✓"
    echo
    echo -e "${YELLOW}Note: Check the game service logs to verify the cleanup was successful${NC}"
    echo -e "${YELLOW}The room should now have proper player limits and no duplicates${NC}"
    echo
    echo -e "${GREEN}Expected result:${NC}"
    echo "- Max players: 2"
    echo "- Current players: ≤ 2"
    echo "- No duplicate users"
    echo "- Proper position numbering (1, 2)"
}

# Run the cleanup
main "$@"
