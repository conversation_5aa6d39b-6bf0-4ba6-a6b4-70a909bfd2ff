#!/bin/bash

# Script to remove a user from all rooms
# Usage: ./remove_user_from_rooms.sh <user_id> [username]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}

# Check arguments
if [ $# -lt 1 ]; then
    echo -e "${RED}Usage: $0 <user_id> [username]${NC}"
    echo "Example: $0 683b07882d7dbd11e92bf29d res2"
    exit 1
fi

USER_ID="$1"
USERNAME="${2:-$USER_ID}"

echo -e "${YELLOW}=== Removing User from All Rooms ===${NC}"
echo "User ID: $USER_ID"
echo "Username: $USERNAME"
echo

# Function to check if Redis is available
check_redis() {
    echo -e "${YELLOW}Checking Redis connection...${NC}"
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping >/dev/null 2>&1; then
            echo -e "${GREEN}✓ Redis is available${NC}"
            return 0
        else
            echo -e "${RED}✗ Redis is not responding${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ redis-cli not found${NC}"
        return 1
    fi
}

# Function to publish a leave room request
publish_leave_request() {
    local room_id="$1"
    local correlation_id="leave-room-${USER_ID}-$(date +%s%3N)-cleanup"
    local response_channel="room:leave:response:${correlation_id}"
    
    echo -e "${YELLOW}Publishing leave room request for room: $room_id${NC}"
    
    # Create the request message
    local request_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "responseChannel": "$response_channel",
    "serviceId": "cleanup-script",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "leave_room",
    "payload": {
      "roomId": "$room_id",
      "userId": "$USER_ID",
      "username": "$USERNAME",
      "reason": "cleanup",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
    
    # Publish the request
    echo "$request_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "room:leave"
    
    echo -e "${GREEN}✓ Leave request published for room: $room_id${NC}"
    return 0
}

# Function to clear user from cache
clear_user_cache() {
    echo -e "${YELLOW}Clearing user cache entries...${NC}"
    
    # Clear balance cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "player:${USER_ID}:balance" >/dev/null 2>&1 || true
    
    # Clear any room-specific user caches
    local cache_keys=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT KEYS "*${USER_ID}*" 2>/dev/null || true)
    if [ -n "$cache_keys" ]; then
        echo "Found cache keys: $cache_keys"
        echo "$cache_keys" | xargs -r redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL >/dev/null 2>&1 || true
    fi
    
    echo -e "${GREEN}✓ User cache cleared${NC}"
}

# Function to force remove user from specific room via direct database operation
force_remove_from_room() {
    local room_id="$1"
    
    echo -e "${YELLOW}Force removing user from room: $room_id${NC}"
    
    # This would require direct database access
    # For now, we'll just publish multiple leave requests
    for i in {1..3}; do
        publish_leave_request "$room_id"
        sleep 1
    done
    
    # Clear room cache
    redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "room:state:${room_id}" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✓ Force removal attempted for room: $room_id${NC}"
}

# Function to get rooms where user might be present (from logs or known rooms)
get_known_rooms() {
    # You can add known room IDs here if you have them
    local known_rooms=(
        "fresh-room-1749102937359-hudpo1aflw7"
        # Add more room IDs if known
    )
    
    echo "${known_rooms[@]}"
}

# Main cleanup function
main() {
    echo -e "${GREEN}Starting user cleanup process${NC}"
    echo "================================================"
    echo
    
    # Check prerequisites
    if ! check_redis; then
        echo -e "${RED}Redis is required for this cleanup${NC}"
        exit 1
    fi
    
    echo
    
    # Clear user cache first
    clear_user_cache
    echo
    
    # Get known rooms and attempt to remove user
    local known_rooms=($(get_known_rooms))
    
    if [ ${#known_rooms[@]} -gt 0 ]; then
        echo -e "${YELLOW}Attempting to remove user from known rooms...${NC}"
        for room_id in "${known_rooms[@]}"; do
            if [ -n "$room_id" ]; then
                force_remove_from_room "$room_id"
                echo
            fi
        done
    else
        echo -e "${YELLOW}No known rooms found. Publishing general cleanup request...${NC}"
        
        # Publish a general cleanup request
        local correlation_id="cleanup-${USER_ID}-$(date +%s%3N)"
        local cleanup_message=$(cat <<EOF
{
  "metadata": {
    "correlationId": "$correlation_id",
    "serviceId": "cleanup-script",
    "version": "1.0",
    "priority": "high",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
  },
  "event": {
    "type": "cleanup_user",
    "payload": {
      "userId": "$USER_ID",
      "username": "$USERNAME",
      "action": "remove_from_all_rooms",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
    }
  }
}
EOF
)
        
        echo "$cleanup_message" | redis-cli -h $REDIS_HOST -p $REDIS_PORT -x PUBLISH "admin:cleanup"
        echo -e "${GREEN}✓ General cleanup request published${NC}"
    fi
    
    echo
    echo -e "${GREEN}Cleanup Summary:${NC}"
    echo "- User ID: $USER_ID"
    echo "- Username: $USERNAME"
    echo "- Cache cleared: ✓"
    echo "- Leave requests sent: ✓"
    echo "- Rooms processed: ${#known_rooms[@]}"
    echo
    echo -e "${YELLOW}Note: Check the game service logs to verify the cleanup was successful${NC}"
    echo -e "${YELLOW}You may need to wait a few seconds for the operations to complete${NC}"
}

# Run the cleanup
main "$@"
