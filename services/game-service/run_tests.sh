#!/bin/bash

# Game Service Test Runner
# This script runs all the working tests in the game-service

set -e

echo "🎮 Game Service Test Runner"
echo "=========================="
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run tests with proper error handling
run_test_suite() {
    local test_name="$1"
    local test_path="$2"
    local description="$3"
    
    print_status "Running $test_name..."
    echo "Description: $description"
    echo "Path: $test_path"
    echo ""
    
    if go test $test_path -v; then
        print_success "$test_name completed successfully!"
        echo ""
        return 0
    else
        print_error "$test_name failed!"
        echo ""
        return 1
    fi
}

# Main test execution
main() {
    print_status "Starting Game Service Test Suite"
    echo ""
    
    # Track test results
    total_tests=0
    passed_tests=0
    failed_tests=0
    
    # Test 1: Models Package Tests
    total_tests=$((total_tests + 1))
    if run_test_suite "Models Package Tests" "./internal/models/..." "Core model validation, error handling, and business logic"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 2: Basic Tests
    total_tests=$((total_tests + 1))
    if run_test_suite "Basic Tests" "./tests/basic_test.go" "Basic model functionality and table-driven testing patterns"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test Summary
    echo "=========================================="
    echo "🎯 TEST EXECUTION SUMMARY"
    echo "=========================================="
    echo "Total Test Suites: $total_tests"
    echo "Passed: $passed_tests"
    echo "Failed: $failed_tests"
    echo ""
    
    if [ $failed_tests -eq 0 ]; then
        print_success "All test suites passed! 🎉"
        echo ""
        echo "✅ Models Package: Comprehensive validation and error handling"
        echo "✅ Basic Tests: Table-driven testing and model verification"
        echo ""
        echo "🚀 Test Coverage Highlights:"
        echo "   • 25+ error codes tested"
        echo "   • 5 game types and statuses validated"
        echo "   • Model validation logic verified"
        echo "   • Business logic validation (bet limits, game results)"
        echo "   • Error formatting and handling"
        echo ""
        return 0
    else
        print_error "Some test suites failed!"
        return 1
    fi
}

# Additional test commands
show_help() {
    echo "Game Service Test Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  help, -h, --help    Show this help message"
    echo "  models              Run only models package tests"
    echo "  basic               Run only basic tests"
    echo "  coverage            Run tests with coverage"
    echo "  verbose             Run tests with extra verbose output"
    echo "  watch               Watch for changes and re-run tests"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all working tests"
    echo "  $0 models           # Run only models tests"
    echo "  $0 coverage         # Run tests with coverage report"
    echo ""
}

# Handle command line arguments
case "${1:-}" in
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    "models")
        print_status "Running Models Package Tests only..."
        go test ./internal/models/... -v
        ;;
    "basic")
        print_status "Running Basic Tests only..."
        go test ./tests/basic_test.go -v
        ;;
    "coverage")
        print_status "Running tests with coverage..."
        echo ""
        print_status "Models Package Coverage:"
        go test ./internal/models/... -cover -v
        echo ""
        print_status "Basic Tests Coverage:"
        go test ./tests/basic_test.go -cover -v
        ;;
    "verbose")
        print_status "Running tests with extra verbose output..."
        go test ./internal/models/... -v -count=1
        go test ./tests/basic_test.go -v -count=1
        ;;
    "watch")
        print_status "Watching for changes and re-running tests..."
        print_warning "This requires 'fswatch' to be installed"
        print_warning "Install with: brew install fswatch (macOS) or apt-get install inotify-tools (Linux)"
        echo ""
        if command -v fswatch >/dev/null 2>&1; then
            fswatch -o . | xargs -n1 -I{} bash -c 'clear && echo "🔄 Files changed, re-running tests..." && ./run_tests.sh'
        else
            print_error "fswatch not found. Please install it to use watch mode."
            exit 1
        fi
        ;;
    "")
        # Default: run all tests
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
