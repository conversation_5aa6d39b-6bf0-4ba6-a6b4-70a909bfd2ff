#!/usr/bin/env ruby

require 'bundler/setup'
require_relative 'config/environment'

puts "Debugging user validation issues..."

begin
  user = User.first
  if user.nil?
    puts "No users found"
    exit 1
  end

  puts "User found: #{user.username}"
  puts "User ID: #{user.id}"
  puts "User valid?: #{user.valid?}"

  unless user.valid?
    puts "User validation errors:"
    user.errors.full_messages.each do |error|
      puts "  - #{error}"
    end
  end

  puts "User balance: #{user.balance}"
  puts "User status: #{user.status}"
  puts "User role: #{user.role}"

  # Try to create a simple transaction manually
  puts "\nTrying to create a transaction manually..."

  transaction = user.transactions.build(
    amount: -10.0,
    transaction_type: 'withdrawal',
    balance_before: user.balance,
    balance_after: user.balance - 10.0,
    description: 'Test transaction',
    status: 'completed'
  )

  puts "Transaction valid?: #{transaction.valid?}"

  unless transaction.valid?
    puts "Transaction validation errors:"
    transaction.errors.full_messages.each do |error|
      puts "  - #{error}"
    end
  end

  puts "Transaction user valid?: #{transaction.user.valid?}" if transaction.user

  # Try to save the transaction
  puts "\nTrying to save transaction..."
  begin
    transaction.save!
    puts "Transaction saved successfully!"
    puts "Transaction ID: #{transaction.transaction_id}"
  rescue => e
    puts "Failed to save transaction: #{e.message}"
    puts "Transaction errors: #{transaction.errors.full_messages}"
    puts "User errors: #{transaction.user.errors.full_messages}" if transaction.user
  end

  # Now test the update_balance! method
  puts "\nTesting update_balance! method..."
  begin
    result = user.update_balance!(-5.0, 'withdrawal', 'Test withdrawal')
    puts "update_balance! succeeded!"
    puts "Transaction ID: #{result.transaction_id}"
    puts "New user balance: #{user.reload.balance}"
  rescue => e
    puts "update_balance! failed: #{e.message}"
    puts "User errors: #{user.errors.full_messages}"
    puts "User valid?: #{user.valid?}"

    # Check what happens if we try to save the user
    user.balance -= 5.0
    puts "User valid after balance change?: #{user.valid?}"
    unless user.valid?
      puts "User validation errors after balance change:"
      user.errors.full_messages.each do |error|
        puts "  - #{error}"
      end
    end
  end

rescue => e
  puts "Error: #{e.message}"
  puts e.backtrace.first(5)
end
