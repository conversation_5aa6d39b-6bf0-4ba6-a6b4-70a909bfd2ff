#!/usr/bin/env ruby

# Simple test to verify the kick player fix
require_relative 'config/environment'

puts "🧪 Testing Kick Player Fix"
puts "=" * 50

begin
  # Create test data
  puts "\n📋 Setting up test data..."

  # Create a test creator user first
  creator_user = User.create!(
    username: "creator_#{Time.current.to_i}",
    email: "creator_#{Time.current.to_i}@example.com",
    password: 'password123',
    balance: 1000.0,
    status: 'active',
    role: 'admin'
  )

  # Create a test room
  test_room = Room.create!(
    name: "Test Kick Room #{Time.current.to_i}",
    game_type: 'prizewheel',
    max_players: 4,
    bet_amount: 10.0,
    status: 'waiting',
    creator: creator_user,
    current_players: 0,
    prize_pool: 0.0
  )

  puts "✅ Created test room: #{test_room.name} (#{test_room.id})"

  # Create test user
  test_user1 = User.create!(
    username: "testuser1_#{Time.current.to_i}",
    email: "testuser1_#{Time.current.to_i}@example.com",
    password: 'password123',
    balance: 100.0,
    status: 'active',
    role: 'player'
  )

  puts "✅ Created test user: #{test_user1.username}"

  # Test adding user to room
  puts "\n📋 Testing add_player!..."
  puts "Debug: Room bet_amount = #{test_room.bet_amount} (#{test_room.bet_amount.class})"
  
  test_room.add_player!(test_user1)
  puts "✅ Successfully added #{test_user1.username} to room"

  test_room.reload
  puts "✅ Room now has #{test_room.current_players} players"

  # Test removing user from room
  puts "\n🧪 Testing remove_player!..."
  result = test_room.remove_player!(test_user1)
  puts "✅ Successfully removed #{test_user1.username} from room: #{result}"

  test_room.reload
  puts "✅ Room now has #{test_room.current_players} players"

  # Cleanup
  puts "\n🧹 Cleaning up test data..."
  test_room.game_sessions.destroy_all
  test_room.destroy
  test_user1.destroy
  creator_user.destroy
  puts "✅ Cleanup completed"

  puts "\n🎉 All tests passed! The kick player fix is working correctly."
  puts "✅ Logger calls are now using string interpolation instead of multiple arguments"
  puts "✅ remove_player! method works without errors"
  puts "✅ BSON::Decimal128 compatibility issue resolved"

rescue => e
  puts "❌ Error: #{e.message}"
  puts "❌ Error class: #{e.class.name}"
  puts "❌ Backtrace: #{e.backtrace.first(5).join("\n")}"
  exit 1
end
