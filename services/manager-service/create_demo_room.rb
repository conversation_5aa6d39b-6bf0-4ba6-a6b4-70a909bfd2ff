#!/usr/bin/env ruby

# Create a demo room with players for testing dashboard kick functionality
require_relative 'config/environment'

puts "=== Creating Demo Room for Dashboard Testing ==="

admin_user = User.where(role: 'admin').first
test_users = User.limit(3).to_a

# Create a demo room
demo_room = Room.create!(
  name: "Demo Room - Full for Kick Test",
  game_type: "prizewheel",
  max_players: 3,
  bet_amount: 25.0,
  currency: "USD",
  creator: admin_user,
  status: "waiting"
)

puts "✅ Created demo room: #{demo_room.id}"
puts "   Name: #{demo_room.name}"
puts "   Max players: #{demo_room.max_players}"
puts "   Bet amount: #{demo_room.bet_amount} #{demo_room.currency}"

# Add players to make it full
test_users.each_with_index do |user, index|
  break if index >= demo_room.max_players
  
  session = demo_room.add_player!(user)
  puts "✅ Added player #{index + 1}: #{user.username} (#{user.id})"
end

demo_room.reload
puts "\n📊 Demo Room State:"
puts "   Status: #{demo_room.status}"
puts "   Players: #{demo_room.current_players}/#{demo_room.max_players}"
puts "   Room ID: #{demo_room.id}"

# Show active sessions
active_sessions = demo_room.game_sessions.in(status: ['pending', 'active'])
puts "\n👥 Active Players (#{active_sessions.count}):"
active_sessions.each_with_index do |session, index|
  user = session.user
  puts "   #{index + 1}. #{user.username} (#{user.id})"
  puts "      Status: #{session.status}"
  puts "      Session ID: #{session.id}"
  puts "      Joined: #{session.created_at}"
end

# Verify data consistency
consistency = demo_room.check_data_consistency
puts "\n🔍 Data Consistency: #{consistency[:consistent] ? '✅ Consistent' : '❌ Inconsistent'}"

if !consistency[:consistent]
  puts "   Details: #{consistency}"
end

puts "\n🎯 READY FOR DASHBOARD TESTING!"
puts "   1. Open dashboard and navigate to rooms"
puts "   2. Find room: '#{demo_room.name}' (ID: #{demo_room.id})"
puts "   3. Click 'Details' to see players"
puts "   4. Try kicking any of the #{active_sessions.count} players"
puts "   5. Verify the room updates from #{demo_room.current_players}/#{demo_room.max_players} to #{demo_room.current_players - 1}/#{demo_room.max_players}"

puts "\n📋 Room Details for Dashboard:"
puts "   URL: /admin/rooms/#{demo_room.id}/details"
puts "   Players endpoint: /admin/rooms/#{demo_room.id}/players"
puts "   Kick endpoint: POST /admin/rooms/#{demo_room.id}/kick-player"

puts "\n=== Demo room created successfully ==="
