#!/usr/bin/env ruby

# Fix room ID mapping for existing rooms
# Run this script in the Manager Service directory

require_relative 'config/environment'

puts "🔧 Starting room ID mapping fix..."

# Find the specific room that's causing issues
room_id = "68412c9af494b684c1c18ecf"
room = Room.find_by(external_room_id: room_id)

if room
  puts "✅ Found room with Manager Service ID: #{room_id}"
  puts "   Current external_room_id: #{room.external_room_id}"
  puts "   Current game_service_room_id: #{room.game_service_room_id}"
  puts "   Current metadata: #{room.metadata}"
else
  # Try to find room by Game Service ID pattern
  game_service_pattern = /^room_[a-f0-9]+_\d+$/
  room = Room.where(:external_room_id => game_service_pattern).first
  
  if room
    puts "🔍 Found room with Game Service ID pattern: #{room.external_room_id}"
    puts "   This room needs to be fixed!"
    
    # Store the Game Service ID in metadata
    game_service_id = room.external_room_id
    
    # Generate a new Manager Service external room ID
    new_external_id = "room_#{SecureRandom.hex(8)}_#{Time.current.to_i}"
    
    begin
      room.update!(
        external_room_id: new_external_id,
        metadata: (room.metadata || {}).merge({
          'game_service_room_id' => game_service_id,
          'room_id_fixed_at' => Time.current.iso8601,
          'original_external_id' => game_service_id
        })
      )
      
      puts "✅ Fixed room ID mapping:"
      puts "   Room ID: #{room.id}"
      puts "   Old external_room_id: #{game_service_id}"
      puts "   New external_room_id: #{new_external_id}"
      puts "   Game Service ID stored in metadata: #{room.game_service_room_id}"
      
    rescue => e
      puts "❌ Failed to fix room: #{e.message}"
      exit 1
    end
  else
    puts "❌ No room found with either ID pattern"
    exit 1
  end
end

puts "🎉 Room ID mapping fix completed successfully!"
puts ""
puts "Next steps:"
puts "1. Restart Manager Service to pick up the new room creation logic"
puts "2. Restart Game Service to pick up the enhanced room ID resolution"
puts "3. Test player kick functionality"
