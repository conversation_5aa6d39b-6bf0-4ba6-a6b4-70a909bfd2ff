#!/usr/bin/env ruby

ENV['RAILS_ENV'] ||= 'development'

require 'bundler/setup'
require_relative 'config/environment'

# Force reload models
Object.send(:remove_const, :User) if defined?(User)
Object.send(:remove_const, :Transaction) if defined?(Transaction)
load Rails.root.join('app/models/user.rb')
load Rails.root.join('app/models/transaction.rb')

puts "=== FINAL COMPREHENSIVE TEST ==="

begin
  user = User.first
  puts "User: #{user.username}, Initial Balance: #{user.balance}"
  
  puts "\n1. Testing User.update_balance! method directly..."
  
  # Test deposit
  deposit_tx = user.update_balance!(25.0, 'admin_deposit', description: 'Final test deposit')
  puts "✅ Deposit: #{deposit_tx.transaction_id}, Amount: #{deposit_tx.amount}"
  
  user.reload
  puts "   Balance after deposit: #{user.balance}"
  
  # Test withdrawal
  withdrawal_tx = user.update_balance!(-10.0, 'withdrawal', description: 'Final test withdrawal')
  puts "✅ Withdrawal: #{withdrawal_tx.transaction_id}, Amount: #{withdrawal_tx.amount}"
  
  user.reload
  puts "   Balance after withdrawal: #{user.balance}"
  
  puts "\n2. Testing TransactionService..."
  
  service = TransactionService.new
  
  # Test service deposit
  service_deposit = service.process_deposit(user, 15.0, 'Service deposit test')
  puts "✅ Service Deposit: #{service_deposit.transaction_id}, Amount: #{service_deposit.amount}"
  
  user.reload
  puts "   Balance after service deposit: #{user.balance}"
  
  # Test service withdrawal
  service_withdrawal = service.process_withdrawal(user, 8.0, 'Service withdrawal test')
  puts "✅ Service Withdrawal: #{service_withdrawal.transaction_id}, Amount: #{service_withdrawal.amount}"
  
  user.reload
  puts "   Balance after service withdrawal: #{user.balance}"
  
  puts "\n3. Testing error conditions..."
  
  # Test insufficient balance
  begin
    user.update_balance!(-1000.0, 'withdrawal', description: 'Should fail')
    puts "❌ ERROR: Should have failed!"
  rescue ArgumentError => e
    puts "✅ Correctly caught insufficient balance: #{e.message}"
  end
  
  # Test zero amount
  begin
    user.update_balance!(0.0, 'adjustment', description: 'Should fail')
    puts "❌ ERROR: Should have failed!"
  rescue ArgumentError => e
    puts "✅ Correctly caught zero amount: #{e.message}"
  end
  
  # Test invalid transaction type
  begin
    user.update_balance!(10.0, 'invalid_type', description: 'Should fail')
    puts "❌ ERROR: Should have failed!"
  rescue ArgumentError => e
    puts "✅ Correctly caught invalid transaction type: #{e.message}"
  end
  
  puts "\n4. Verifying transaction records..."
  
  recent_transactions = user.transactions.recent.limit(5)
  puts "Recent transactions:"
  recent_transactions.each do |tx|
    puts "   #{tx.transaction_id}: #{tx.transaction_type} #{tx.amount} (#{tx.status})"
  end
  
  puts "\n🎉 ALL TESTS PASSED! Deposit and withdrawal functionality is working correctly."
  puts "Final user balance: #{user.reload.balance}"

rescue => e
  puts "❌ ERROR: #{e.message}"
  puts e.backtrace.first(5)
  exit 1
end
