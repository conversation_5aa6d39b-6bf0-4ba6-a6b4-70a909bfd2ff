#!/usr/bin/env ruby

require 'bundler/setup'
require_relative 'config/environment'

puts "Testing deposit functionality..."

begin
  # Find a user to test with
  user = User.first
  if user.nil?
    puts "No users found in database"
    exit 1
  end

  puts "Found user: #{user.username} (ID: #{user.id})"
  puts "Current balance: #{user.balance}"

  # Test the update_balance! method for deposit
  initial_balance = user.balance
  test_amount = 25.0

  puts "\nTesting update_balance! method with amount: #{test_amount}"

  transaction_record = user.update_balance!(
    test_amount,
    'admin_deposit',
    'Test deposit',
    nil,
    nil,
    { test: true }
  )

  puts "Transaction created successfully!"
  puts "Transaction ID: #{transaction_record.transaction_id}"
  puts "Transaction type: #{transaction_record.transaction_type}"
  puts "Amount: #{transaction_record.amount}"
  puts "Balance before: #{transaction_record.balance_before}"
  puts "Balance after: #{transaction_record.balance_after}"
  puts "Status: #{transaction_record.status}"

  # Verify user balance was updated
  user.reload
  puts "\nUser balance after transaction: #{user.balance}"
  puts "Expected balance: #{initial_balance + test_amount}"

  if user.balance == initial_balance + test_amount
    puts "✅ Balance update successful!"
  else
    puts "❌ Balance update failed!"
  end

  puts "\n✅ Deposit functionality test completed successfully!"

rescue => e
  puts "❌ Error during test: #{e.message}"
  puts e.backtrace.first(5)
  exit 1
end
