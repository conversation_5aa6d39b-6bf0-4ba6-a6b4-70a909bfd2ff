#!/usr/bin/env ruby

# Comprehensive test runner for Manager Service
# This script runs all tests with proper setup and reporting

require 'fileutils'
require 'optparse'

class TestRunner
  def initialize
    @options = {
      coverage: true,
      parallel: false,
      verbose: false,
      format: 'progress',
      pattern: nil,
      tag: nil,
      exclude_tag: nil
    }
    
    parse_options
    setup_environment
  end

  def run
    puts "🚀 Starting Manager Service Test Suite"
    puts "=" * 60
    
    print_test_info
    setup_test_database
    
    if @options[:coverage]
      setup_coverage
    end
    
    run_tests
    
    if @options[:coverage]
      generate_coverage_report
    end
    
    print_summary
  end

  private

  def parse_options
    OptionParser.new do |opts|
      opts.banner = "Usage: bin/test-all [options]"
      
      opts.on("-c", "--[no-]coverage", "Generate coverage report (default: true)") do |v|
        @options[:coverage] = v
      end
      
      opts.on("-p", "--[no-]parallel", "Run tests in parallel") do |v|
        @options[:parallel] = v
      end
      
      opts.on("-v", "--[no-]verbose", "Verbose output") do |v|
        @options[:verbose] = v
      end
      
      opts.on("-f", "--format FORMAT", "RSpec format (progress, documentation, json)") do |format|
        @options[:format] = format
      end
      
      opts.on("--pattern PATTERN", "Run tests matching pattern") do |pattern|
        @options[:pattern] = pattern
      end
      
      opts.on("--tag TAG", "Run tests with specific tag") do |tag|
        @options[:tag] = tag
      end
      
      opts.on("--exclude-tag TAG", "Exclude tests with specific tag") do |tag|
        @options[:exclude_tag] = tag
      end
      
      opts.on("-h", "--help", "Show this help") do
        puts opts
        exit
      end
    end.parse!
  end

  def setup_environment
    ENV['RAILS_ENV'] = 'test'
    ENV['COVERAGE'] = @options[:coverage].to_s
    
    # Ensure we're in the right directory
    Dir.chdir(File.dirname(__FILE__) + '/..')
  end

  def print_test_info
    puts "📋 Test Configuration:"
    puts "  Environment: #{ENV['RAILS_ENV']}"
    puts "  Coverage: #{@options[:coverage] ? '✅' : '❌'}"
    puts "  Parallel: #{@options[:parallel] ? '✅' : '❌'}"
    puts "  Format: #{@options[:format]}"
    puts "  Pattern: #{@options[:pattern] || 'All tests'}"
    puts "  Tag: #{@options[:tag] || 'None'}"
    puts "  Exclude Tag: #{@options[:exclude_tag] || 'None'}"
    puts
  end

  def setup_test_database
    puts "🗄️  Setting up test database..."
    
    # Drop and recreate test database
    system("bundle exec rails db:drop RAILS_ENV=test") if database_exists?
    system("bundle exec rails db:create RAILS_ENV=test")
    system("bundle exec rails db:migrate RAILS_ENV=test")
    
    puts "✅ Test database ready"
    puts
  end

  def database_exists?
    system("bundle exec rails runner 'ActiveRecord::Base.connection' RAILS_ENV=test 2>/dev/null")
  end

  def setup_coverage
    puts "📊 Setting up coverage reporting..."
    
    # Create coverage directory
    FileUtils.mkdir_p('coverage')
    
    # Clean previous coverage data
    FileUtils.rm_rf('coverage/.resultset.json') if File.exist?('coverage/.resultset.json')
    
    puts "✅ Coverage setup complete"
    puts
  end

  def run_tests
    puts "🧪 Running tests..."
    puts
    
    rspec_command = build_rspec_command
    
    puts "Command: #{rspec_command}" if @options[:verbose]
    puts
    
    success = system(rspec_command)
    
    @test_success = success
    puts
  end

  def build_rspec_command
    cmd = ["bundle exec rspec"]
    
    # Add format
    cmd << "--format #{@options[:format]}"
    
    # Add pattern if specified
    if @options[:pattern]
      cmd << @options[:pattern]
    end
    
    # Add tags
    if @options[:tag]
      cmd << "--tag #{@options[:tag]}"
    end
    
    if @options[:exclude_tag]
      cmd << "--tag ~#{@options[:exclude_tag]}"
    end
    
    # Add parallel option
    if @options[:parallel]
      cmd << "--parallel"
    end
    
    # Add verbose option
    if @options[:verbose]
      cmd << "--verbose"
    end
    
    # Add coverage requirement
    if @options[:coverage]
      cmd << "--require spec_helper"
    end
    
    # Add order randomization
    cmd << "--order random"
    
    # Add failure exit code
    cmd << "--fail-fast" if ENV['CI']
    
    cmd.join(" ")
  end

  def generate_coverage_report
    puts "📊 Generating coverage report..."
    
    if File.exist?('coverage/index.html')
      puts "✅ Coverage report generated at: coverage/index.html"
      
      # Try to open coverage report if not in CI
      if !ENV['CI'] && system('which open > /dev/null 2>&1')
        puts "🌐 Opening coverage report in browser..."
        system('open coverage/index.html')
      end
    else
      puts "⚠️  Coverage report not found"
    end
    
    puts
  end

  def print_summary
    puts "=" * 60
    puts "📋 Test Summary"
    puts "=" * 60
    
    if @test_success
      puts "✅ All tests passed!"
      puts
      print_test_stats
    else
      puts "❌ Some tests failed!"
      puts
      print_failure_info
    end
    
    print_coverage_summary if @options[:coverage]
    print_next_steps
  end

  def print_test_stats
    # Count test files
    model_tests = Dir.glob('spec/models/**/*_spec.rb').length
    controller_tests = Dir.glob('spec/controllers/**/*_spec.rb').length
    service_tests = Dir.glob('spec/services/**/*_spec.rb').length
    job_tests = Dir.glob('spec/jobs/**/*_spec.rb').length
    
    puts "📊 Test Files:"
    puts "  Models: #{model_tests}"
    puts "  Controllers: #{controller_tests}"
    puts "  Services: #{service_tests}"
    puts "  Jobs: #{job_tests}"
    puts "  Total: #{model_tests + controller_tests + service_tests + job_tests}"
    puts
  end

  def print_failure_info
    puts "🔍 To run only failed tests:"
    puts "  bundle exec rspec --only-failures"
    puts
    puts "🔍 To run tests with more detail:"
    puts "  bundle exec rspec --format documentation"
    puts
  end

  def print_coverage_summary
    if File.exist?('coverage/.last_run.json')
      require 'json'
      last_run = JSON.parse(File.read('coverage/.last_run.json'))
      
      puts "📊 Coverage Summary:"
      puts "  Line Coverage: #{last_run['result']['line']}%"
      puts "  Branch Coverage: #{last_run['result']['branch']}%" if last_run['result']['branch']
      puts
    end
  end

  def print_next_steps
    puts "🚀 Next Steps:"
    puts "  • Review any failing tests and fix issues"
    puts "  • Check coverage report for untested code"
    puts "  • Run specific test suites: bin/test-all --pattern spec/models"
    puts "  • Run with tags: bin/test-all --tag focus"
    puts
    puts "📚 Available test commands:"
    puts "  • Models only: bundle exec rspec spec/models"
    puts "  • Controllers only: bundle exec rspec spec/controllers"
    puts "  • Services only: bundle exec rspec spec/services"
    puts "  • Jobs only: bundle exec rspec spec/jobs"
    puts
  end
end

# Run the test suite
if __FILE__ == $0
  runner = TestRunner.new
  runner.run
  
  # Exit with appropriate code
  exit($?.exitstatus || 0)
end
