#!/usr/bin/env ruby

require_relative '../config/environment'

# Parse command line arguments
username = ARGV[0] || ENV['ADMIN_USERNAME'] || 'admin'
email = ARGV[1] || ENV['ADMIN_EMAIL'] || '<EMAIL>'
password = ARGV[2] || ENV['ADMIN_PASSWORD'] || 'admin123456'

puts "🔐 XZ Game Manager Service - Admin User Setup"
puts "=" * 50

# Validate inputs
if password.length < 6
  puts "❌ Password must be at least 6 characters long"
  exit 1
end

unless email.match?(URI::MailTo::EMAIL_REGEXP)
  puts "❌ Invalid email format"
  exit 1
end

puts "Creating admin user with:"
puts "  Username: #{username}"
puts "  Email: #{email}"
puts "  Password: #{'*' * password.length}"
puts

begin
  admin_user = User.find_or_initialize_by(username: username)
  
  if admin_user.persisted?
    puts "⚠️  Admin user '#{username}' already exists"
    puts "   Current email: #{admin_user.email}"
    puts "   Current role: #{admin_user.role}"
    puts "   Current status: #{admin_user.status}"
    puts
    
    print "Do you want to update this user? (y/N): "
    response = STDIN.gets.chomp.downcase
    
    if response == 'y' || response == 'yes'
      admin_user.assign_attributes(
        email: email,
        password: password,
        password_confirmation: password,
        role: 'admin',
        status: 'active'
      )
      
      if admin_user.save
        puts "✅ Admin user '#{username}' updated successfully"
      else
        puts "❌ Failed to update admin user:"
        admin_user.errors.full_messages.each { |error| puts "   - #{error}" }
        exit 1
      end
    else
      puts "ℹ️  Skipping admin user update"
      exit 0
    end
  else
    admin_user.assign_attributes(
      email: email,
      password: password,
      password_confirmation: password,
      role: 'admin',
      status: 'active',
      balance: 10000.0,
      metadata: {
        created_by: 'seed_script',
        created_at: Time.current,
        description: 'Admin user created via seed script'
      }
    )
    
    if admin_user.save
      puts "✅ Admin user '#{username}' created successfully"
      puts "   Email: #{admin_user.email}"
      puts "   Role: #{admin_user.role}"
      puts "   Status: #{admin_user.status}"
      puts "   Balance: #{admin_user.balance}"
    else
      puts "❌ Failed to create admin user:"
      admin_user.errors.full_messages.each { |error| puts "   - #{error}" }
      exit 1
    end
  end

  puts
  puts "🎉 Admin user setup completed!"
  puts
  puts "You can now login with:"
  puts "  Username: #{username}"
  puts "  Password: #{password}"
  puts
  puts "⚠️  Please change the password after first login!"

rescue => e
  puts "❌ Error: #{e.message}"
  puts "   #{e.backtrace.first}"
  exit 1
end
