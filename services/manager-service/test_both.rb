#!/usr/bin/env ruby

require 'bundler/setup'
require_relative 'config/environment'

puts "Testing both deposit and withdrawal functionality..."

begin
  # Find a user to test with
  user = User.first
  if user.nil?
    puts "No users found in database"
    exit 1
  end

  puts "Found user: #{user.username} (ID: #{user.id})"
  puts "Current balance: #{user.balance}"

  # Test deposit with correct number of arguments
  puts "\n=== Testing Deposit ==="
  initial_balance = user.balance
  deposit_amount = 25.0

  puts "Testing deposit with amount: #{deposit_amount}"

  # Use the method with the correct signature
  deposit_transaction = user.update_balance!(
    deposit_amount,
    'admin_deposit',
    'Test deposit'
  )

  puts "Deposit transaction created successfully!"
  puts "Transaction ID: #{deposit_transaction.transaction_id}"
  puts "Amount: #{deposit_transaction.amount}"
  puts "Balance after: #{deposit_transaction.balance_after}"

  user.reload
  puts "User balance after deposit: #{user.balance}"

  # Test withdrawal
  puts "\n=== Testing Withdrawal ==="
  withdrawal_amount = -10.0

  puts "Testing withdrawal with amount: #{withdrawal_amount}"

  withdrawal_transaction = user.update_balance!(
    withdrawal_amount,
    'withdrawal',
    'Test withdrawal'
  )

  puts "Withdrawal transaction created successfully!"
  puts "Transaction ID: #{withdrawal_transaction.transaction_id}"
  puts "Amount: #{withdrawal_transaction.amount}"
  puts "Balance after: #{withdrawal_transaction.balance_after}"

  user.reload
  puts "User balance after withdrawal: #{user.balance}"

  puts "\n✅ Both deposit and withdrawal functionality tests completed successfully!"

rescue => e
  puts "❌ Error during test: #{e.message}"
  puts e.backtrace.first(5)
  exit 1
end
