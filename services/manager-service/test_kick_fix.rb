#!/usr/bin/env ruby

# Test script to verify kick functionality fix
require_relative 'config/environment'

puts "=== Testing Kick Functionality Fix ==="

# Create a test room with real players
admin_user = User.where(role: 'admin').first
if admin_user.nil?
  puts "No admin user found. Please ensure admin user exists."
  exit 1
end

# Find existing users or use admin as test players
test_users = User.where(role: 'player').limit(2).to_a
if test_users.count < 2
  # Use admin user as test players if no regular players exist
  test_user1 = admin_user
  test_user2 = admin_user
  puts "Using admin user for testing (no regular players found)"
else
  test_user1 = test_users[0]
  test_user2 = test_users[1]
end

# Ensure users have sufficient balance
[test_user1, test_user2].each do |user|
  if user.balance < 100
    user.update!(balance: 1000.0)
  end
end

puts "Created/found test users: #{test_user1.username}, #{test_user2.username}"

# Create a test room
room = Room.create!(
  name: "Kick Test Room",
  game_type: "prizewheel",
  max_players: 2,
  bet_amount: 10.0,
  currency: "USD",
  creator: admin_user,
  status: "waiting"
)

puts "Created test room: #{room.id} (#{room.name})"

# Add players to the room
puts "\n=== Adding players to room ==="
begin
  session1 = room.add_player!(test_user1)
  puts "✓ Added #{test_user1.username} to room"
  
  session2 = room.add_player!(test_user2)
  puts "✓ Added #{test_user2.username} to room"
  
  room.reload
  puts "Room state: #{room.current_players}/#{room.max_players} players"
  
rescue => e
  puts "✗ Failed to add players: #{e.message}"
  exit 1
end

# Verify room is full
puts "\n=== Verifying room state ==="
puts "- Current players: #{room.current_players}/#{room.max_players}"
puts "- Is full: #{room.current_players >= room.max_players}"

active_sessions = room.game_sessions.in(status: ['pending', 'active'])
puts "- Active sessions: #{active_sessions.count}"

consistency_check = room.check_data_consistency
puts "- Data consistent: #{consistency_check[:consistent]}"

if !consistency_check[:consistent]
  puts "- Inconsistency details: #{consistency_check}"
end

# Test kick functionality
puts "\n=== Testing kick functionality ==="

# Test 1: Direct Room#kick_player! method
puts "Test 1: Room#kick_player! method"
begin
  result = room.kick_player!(test_user1.id.to_s, admin_user.id.to_s, "Test kick")
  puts "✓ Room#kick_player! succeeded: #{result}"
  
  room.reload
  puts "  Room state after kick: #{room.current_players}/#{room.max_players}"
  
rescue => e
  puts "✗ Room#kick_player! failed: #{e.message}"
end

# Test 2: Controller-style kick with consistency check
puts "\nTest 2: Controller-style kick with consistency check"
begin
  # Simulate what the controller does
  consistency_check = room.check_data_consistency
  if !consistency_check[:consistent]
    puts "  Fixing data inconsistency..."
    fix_result = room.fix_data_consistency!
    puts "  Fix result: #{fix_result}"
    room.reload
  end
  
  # Find remaining player
  remaining_sessions = room.game_sessions.in(status: ['pending', 'active'])
  if remaining_sessions.any?
    player_to_kick = remaining_sessions.first.user
    
    # Verify player is in room
    active_session = room.game_sessions.where(user: player_to_kick).in(status: ['pending', 'active']).first
    if active_session
      puts "  Player #{player_to_kick.username} verified in room"
      
      # Use enhanced seat management service
      seat_management_service = EnhancedSeatManagementService.new
      result = seat_management_service.kick_player_with_seat_management(room, player_to_kick, admin_user, "Controller-style kick")
      
      if result[:success]
        puts "✓ Enhanced kick succeeded"
        room.reload
        puts "  Room state after enhanced kick: #{room.current_players}/#{room.max_players}"
      else
        puts "✗ Enhanced kick failed: #{result[:error]}"
      end
    else
      puts "✗ Player not found in room"
    end
  else
    puts "  No remaining players to kick"
  end
  
rescue => e
  puts "✗ Controller-style kick failed: #{e.message}"
end

# Test 3: Verify room can accept new players after kicks
puts "\nTest 3: Room can accept new players"
room.reload
puts "Final room state: #{room.current_players}/#{room.max_players}"

if room.current_players < room.max_players
  puts "✓ Room can accept new players"
  
  # Try to add a player back
  begin
    if room.can_join?(test_user1)
      session = room.add_player!(test_user1)
      puts "✓ Successfully re-added #{test_user1.username}"
      room.reload
      puts "  Room state: #{room.current_players}/#{room.max_players}"
    else
      puts "✗ Room cannot accept player (can_join? returned false)"
    end
  rescue => e
    puts "✗ Failed to re-add player: #{e.message}"
  end
else
  puts "Room is still full"
end

# Cleanup
puts "\n=== Cleanup ==="
begin
  room.destroy
  puts "✓ Cleaned up test room"
rescue => e
  puts "✗ Failed to cleanup room: #{e.message}"
end

puts "\n=== Test completed ==="
