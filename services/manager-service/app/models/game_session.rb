class GameSession < ApplicationDocument
  include Mongoid::Document
  include Mongoid::Timestamps

  # Fields
  field :session_id, type: String
  field :game_type, type: String
  field :status, type: String, default: 'pending'
  field :bet_amount, type: BigDecimal
  field :win_amount, type: BigDecimal, default: -> { BigDecimal('0.0') }
  field :result, type: Hash, default: {}
  field :started_at, type: Time
  field :ended_at, type: Time
  field :metadata, type: Hash, default: {}

  # Indexes
  index({ session_id: 1 }, { unique: true })
  index({ user_id: 1, created_at: -1 })
  index({ game_type: 1, status: 1 })
  index({ status: 1 })
  index({ started_at: 1 })

  # Validations
  validates :session_id, uniqueness: true, allow_blank: true
  validates :game_type, presence: true, inclusion: { in: %w[prizewheel amidakuji] }
  validates :status, inclusion: { in: %w[pending active completed cancelled] }
  validates :bet_amount, presence: true

  # Custom validation for bet_amount to handle BSON::Decimal128
  validate :validate_bet_amount_positive
  validate :validate_win_amount_non_negative

  # Associations
  belongs_to :user
  belongs_to :room, optional: true

  # Callbacks
  before_validation :generate_session_id, on: :create
  before_save :set_timestamps

  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :pending, -> { where(status: 'pending') }
  scope :completed, -> { where(status: 'completed') }
  scope :cancelled, -> { where(status: 'cancelled') }
  scope :by_game_type, ->(type) { where(game_type: type) }
  scope :recent, -> { order(created_at: :desc) }

  # Instance methods
  def start!
    update!(
      status: 'active',
      started_at: Time.current
    )
  end

  def complete!(result_data = {})
    win_amount_value = result_data[:win_amount] || result_data['win_amount'] || 0.0

    update!(
      status: 'completed',
      ended_at: Time.current,
      win_amount: win_amount_value,
      result: result_data
    )
  end

  def cancel!(reason = nil)
    update!(
      status: 'cancelled',
      ended_at: Time.current,
      metadata: metadata.merge(cancellation_reason: reason)
    )
  end

  def duration
    return nil unless started_at && ended_at
    ended_at - started_at
  end

  def profit_loss
    win_amount - bet_amount
  end

  def won?
    win_amount > bet_amount
  end

  def lost?
    win_amount < bet_amount
  end

  def active?
    status == 'active'
  end

  def completed?
    status == 'completed'
  end

  def pending?
    status == 'pending'
  end

  def cancelled?
    status == 'cancelled'
  end

  # Class methods
  def self.create_for_user(user, game_type, bet_amount, metadata = {})
    create!(
      user: user,
      game_type: game_type,
      bet_amount: bet_amount,
      metadata: metadata
    )
  end

  def self.stats_for_user(user_id)
    sessions = where(user_id: user_id, status: 'completed')
    {
      total_sessions: sessions.count,
      total_bet: sessions.sum(:bet_amount),
      total_won: sessions.sum(:win_amount),
      win_rate: sessions.where(:win_amount.gt => :bet_amount).count.to_f / sessions.count,
      favorite_game: sessions.group(:game_type).count.max_by { |_, count| count }&.first
    }
  end

  private

  def generate_session_id
    return if session_id.present?
    self.session_id = "#{game_type}_#{user_id}_#{Time.current.to_i}_#{SecureRandom.hex(4)}"
  end

  def set_timestamps
    if status_changed? && status == 'active' && started_at.blank?
      self.started_at = Time.current
    elsif status_changed? && %w[completed cancelled].include?(status) && ended_at.blank?
      self.ended_at = Time.current
    end
  end

  # Custom validation for bet_amount to handle BSON::Decimal128 and BigDecimal
  def validate_bet_amount_positive
    return unless bet_amount.present?

    begin
      # Convert to float for validation to handle BSON::Decimal128 and BigDecimal
      amount_value = bet_amount.to_f

      if amount_value <= 0
        errors.add(:bet_amount, 'must be greater than 0')
      end
    rescue StandardError => e
      errors.add(:bet_amount, 'must be a valid number')
    end
  end

  # Custom validation for win_amount to handle BSON::Decimal128 and BigDecimal
  def validate_win_amount_non_negative
    return unless win_amount.present?

    begin
      # Convert to float for validation to handle BSON::Decimal128 and BigDecimal
      amount_value = win_amount.to_f

      if amount_value < 0
        errors.add(:win_amount, 'must be greater than or equal to 0')
      end
    rescue StandardError => e
      errors.add(:win_amount, 'must be a valid number')
    end
  end
end
