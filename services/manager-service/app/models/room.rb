class Room < ApplicationDocument
  include Mongoid::Document
  include Mongoid::Timestamps

  # Fields
  field :external_room_id, type: String
  field :name, type: String
  field :game_type, type: String
  field :status, type: String, default: 'waiting'
  field :max_players, type: Integer
  field :current_players, type: Integer, default: 0
  field :bet_amount, type: BigDecimal
  field :currency, type: String, default: 'USD'
  field :prize_pool, type: BigDecimal, default: 0.0
  field :is_private, type: Boolean, default: false
  field :password_hash, type: String
  field :configuration, type: Hash, default: {}
  field :game_specific_config, type: Hash, default: {}
  field :sync_required, type: <PERSON><PERSON><PERSON>, default: false
  field :last_synced_at, type: Time
  field :metadata, type: Hash, default: {}

  # Indexes
  index({ external_room_id: 1 }, { unique: true })
  index({ creator_id: 1, created_at: -1 })
  index({ game_type: 1, status: 1 })
  index({ status: 1 })
  index({ is_private: 1 })
  index({ sync_required: 1 })

  # Validations
  validates :external_room_id, uniqueness: true, allow_blank: true
  validates :name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :game_type, presence: true, inclusion: { in: %w[prizewheel amidakuji] }
  validates :status, inclusion: { in: %w[starting playing end waiting finished cancelled] }
  validates :max_players, presence: true, numericality: {
    greater_than: 1, less_than_or_equal_to: 8
  }
  validates :current_players, numericality: {
    greater_than_or_equal_to: 0, less_than_or_equal_to: :max_players
  }
  validates :bet_amount, presence: true
  validate :bet_amount_must_be_positive
  validates :currency, presence: true, inclusion: { in: %w[USD EUR GBP] }

  # Associations
  belongs_to :creator, class_name: 'User', foreign_key: 'creator_id'
  has_many :game_sessions, dependent: :destroy

  # Callbacks
  before_create :generate_external_room_id, if: -> { external_room_id.blank? }
  before_save :encrypt_password, if: :password_changed?
  after_update :sync_with_game_service, if: :should_sync?

  # Scopes
  scope :active, -> { where(status: ['waiting', 'playing']) }
  scope :waiting, -> { where(status: 'waiting') }
  scope :playing, -> { where(status: 'playing') }
  scope :finished, -> { where(status: 'finished') }
  scope :cancelled, -> { where(status: 'cancelled') }
  scope :by_game_type, ->(type) { where(game_type: type) }
  scope :public_rooms, -> { where(is_private: false) }
  scope :private_rooms, -> { where(is_private: true) }
  scope :needs_sync, -> { where(sync_required: true) }

  # Virtual attributes
  attr_accessor :password

  # Instance methods
  def can_join?(user, password = nil)
    return false unless waiting?
    return false if current_players >= max_players
    return false if user.balance < bet_amount
    return false if is_private && !valid_password?(password)
    true
  end

  def add_player!(user)
    raise ArgumentError, "Room is full" if current_players >= max_players
    raise ArgumentError, "User cannot join room" unless can_join?(user)

    # Check if player is already in this room
    existing_session = game_sessions.where(user: user).in(status: ['pending', 'active']).first
    if existing_session
      # Player is reconnecting - return existing session without creating new one
      Rails.logger.info "Player reconnecting to room - Room: #{id} - User: #{user.id} - Session: #{existing_session.id}"
      return existing_session
    end

    # Use atomic operations to ensure consistency (Mongoid doesn't have transactions like ActiveRecord)
    begin
      # Double-check room capacity
      reload # Reload to get latest data
      raise ArgumentError, "Room is full" if current_players >= max_players

      # Create game session for the user FIRST (before updating counters)
      session = game_sessions.create!(
        user: user,
        room: self,
        game_type: game_type,
        bet_amount: bet_amount,
        status: 'pending'
      )

      # Update room state atomically AFTER successful session creation
      self.current_players += 1
      save!

      Rails.logger.info "Player added to room successfully - Room: #{id} - User: #{user.id} - Session: #{session.id} - Current players: #{current_players}/#{max_players}"
      session
    rescue StandardError => e
      Rails.logger.error "Failed to add player to room - Room: #{id} - User: #{user.id} - Error: #{e.message}"

      # Rollback: Remove session if it was created but room update failed
      if session&.persisted?
        session.destroy rescue nil
      end

      # Rollback: Decrement counter if it was incremented
      if self.current_players > 0 && self.changed?
        self.current_players -= 1
        save! rescue nil
      end

      raise e
    end
  end

  def remove_player!(user)
    Rails.logger.debug "Room#remove_player! called - room_id: #{id}, user_id: #{user.id}, current_players_before: #{current_players}, prize_pool_before: #{prize_pool}"

    session = game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      Rails.logger.warn "No active session found in Room#remove_player! - room_id: #{id}, user_id: #{user.id}, all_sessions: #{game_sessions.where(user: user).pluck(:status)}"
      return false
    end

    Rails.logger.debug "Found session in Room#remove_player! - room_id: #{id}, user_id: #{user.id}, session_id: #{session.session_id}, session_status: #{session.status}, is_ready: #{session.metadata['is_ready']}"

    # Update room state atomically with safety checks
    if current_players > 0
      self.current_players -= 1
    else
      Rails.logger.warn "Current players already at 0, cannot decrement - room_id: #{id}, user_id: #{user.id}, current_players: #{current_players}"
      # Ensure current_players doesn't go negative
      self.current_players = 0
    end

    # Additional safety check to ensure current_players is within valid range
    if self.current_players < 0
      Rails.logger.warn "Current players went negative, resetting to 0 - room_id: #{id}, user_id: #{user.id}, current_players: #{self.current_players}"
      self.current_players = 0
    elsif self.current_players > max_players
      Rails.logger.warn "Current players exceeds max_players, capping to max - room_id: #{id}, user_id: #{user.id}, current_players: #{self.current_players}, max_players: #{max_players}"
      self.current_players = max_players
    end

    # Remove from prize pool if player was ready
    if session.metadata['is_ready']
      if prize_pool >= bet_amount
        self.prize_pool -= bet_amount
        Rails.logger.debug "Removed from prize pool - room_id: #{id}, user_id: #{user.id}, bet_amount: #{bet_amount}, new_prize_pool: #{prize_pool}"
      else
        Rails.logger.warn "Prize pool insufficient to remove bet amount - room_id: #{id}, user_id: #{user.id}, current_prize_pool: #{prize_pool}, bet_amount: #{bet_amount}"
        self.prize_pool = 0
      end
    end

    # Additional safety check to ensure prize_pool doesn't go negative
    if self.prize_pool < 0
      Rails.logger.warn "Prize pool went negative, resetting to 0 - room_id: #{id}, user_id: #{user.id}, prize_pool: #{self.prize_pool}"
      self.prize_pool = 0
    end

    Rails.logger.debug "Saving room after player removal - Room: #{id} - User: #{user.id} - Players: #{current_players} - Prize Pool: #{prize_pool}"

    begin
      save!
      Rails.logger.debug "Room saved successfully after player removal"
    rescue Mongoid::Errors::Validations => e
      Rails.logger.error "Validation error saving room after player removal - Room: #{id} - User: #{user.id} - Players: #{current_players}/#{max_players} - Prize Pool: #{prize_pool} - Errors: #{errors.full_messages.join(', ')} - Validation Error: #{e.message}"
      raise e
    rescue StandardError => e
      Rails.logger.error "Error saving room after player removal - Room: #{id} - User: #{user.id} - Error: #{e.message} - Class: #{e.class.name}"
      raise e
    end

    Rails.logger.debug "Cancelling session - Room: #{id} - User: #{user.id} - Session: #{session.session_id}"

    session.cancel!('Player left room')

    Rails.logger.debug "Room#remove_player! completed successfully - Room: #{id} - User: #{user.id}"

    true
  end

  def set_player_ready!(user, is_ready)
    session = game_sessions.where(user: user).in(status: ['pending', 'active']).first
    raise ArgumentError, "Player not found in room" unless session

    was_ready = session.metadata['is_ready'] || false

    # Update session ready status
    session.metadata['is_ready'] = is_ready
    session.save!

    # Update prize pool based on ready status change
    if is_ready && !was_ready
      # Player became ready - add to prize pool
      self.prize_pool += bet_amount
      save!
    elsif !is_ready && was_ready
      # Player became not ready - remove from prize pool
      self.prize_pool -= bet_amount
      save!
    end

    session
  end

  def player_ready?(user)
    session = game_sessions.where(user: user).in(status: ['pending', 'active']).first
    return false unless session
    session.metadata['is_ready'] || false
  end

  def ready_players_count
    game_sessions.in(status: ['pending', 'active']).select { |s| s.metadata['is_ready'] }.count
  end

  def current_players_list
    sessions = game_sessions.in(status: ['pending', 'active']).includes(:user).order_by(created_at: :asc)
    sessions.map.with_index do |session, index|
      {
        user_id: session.user.id.to_s,
        username: session.user.username,
        position: index + 1, # Simple sequential position based on join order
        is_ready: session.metadata['is_ready'] || false,
        bet_amount: session.bet_amount,
        joined_at: session.created_at,
        status: session.status,
        session_id: session.session_id,
        balance: session.user.balance
      }
    end
  end

  def find_player_session(user_id)
    game_sessions.where(user_id: user_id).in(status: ['pending', 'active']).first
  end

  # Data consistency methods
  def check_data_consistency
    actual_sessions = game_sessions.in(status: ['pending', 'active']).count
    stored_count = current_players

    inconsistent = actual_sessions != stored_count

    # Get session statuses manually to avoid Mongoid group issues
    session_statuses = {}
    game_sessions.each do |session|
      status = session.status
      session_statuses[status] = (session_statuses[status] || 0) + 1
    end

    {
      consistent: !inconsistent,
      stored_count: stored_count,
      actual_sessions: actual_sessions,
      difference: stored_count - actual_sessions,
      all_sessions: game_sessions.count,
      session_statuses: session_statuses
    }
  end

  def fix_data_consistency!
    consistency_check = check_data_consistency

    unless consistency_check[:consistent]
      Rails.logger.warn "Data inconsistency detected in room #{id}: #{consistency_check}"

      # Fix the current_players count to match actual active sessions
      actual_count = game_sessions.in(status: ['pending', 'active']).count
      old_count = current_players

      update!(current_players: actual_count)

      Rails.logger.info "Fixed room #{id} player count: #{old_count} -> #{actual_count}"

      return {
        fixed: true,
        old_count: old_count,
        new_count: actual_count,
        difference: old_count - actual_count
      }
    end

    { fixed: false, message: "No inconsistency detected" }
  end

  def kick_player!(user_id, kicked_by_user_id, reason = 'Kicked by admin')
    user = User.find(user_id)
    kicked_by = User.find(kicked_by_user_id)

    session = find_player_session(user_id)
    raise ArgumentError, "Player not found in room" unless session

    # Log the kick action
    Rails.logger.info "Kicking player from room - Room: #{id} - Player: #{user_id} - Kicked by: #{kicked_by_user_id} - Reason: #{reason}"

    # Remove player from room
    remove_player!(user)

    # Add kick metadata to session
    session.metadata.merge!({
      'kicked' => true,
      'kicked_by' => kicked_by_user_id,
      'kicked_by_username' => kicked_by.username,
      'kick_reason' => reason,
      'kicked_at' => Time.current.iso8601
    })
    session.cancel!("Kicked: #{reason}")

    Rails.logger.info "Player kicked successfully - Room: #{id} - Player: #{user_id} - New count: #{current_players}"

    true
  end

  private

  def calculate_player_position(session)
    # Calculate position based on join order
    all_sessions = game_sessions.in(status: ['pending', 'active']).order_by(created_at: :asc)
    all_sessions.index(session) + 1
  end

  def start_game!
    raise ArgumentError, "Not enough players" if current_players < 2
    raise ArgumentError, "Room not in waiting state" unless waiting?

    # Update room and sessions atomically
    update!(status: 'playing')
    game_sessions.pending.update_all(status: 'active', started_at: Time.current)
  end

  def finish_game!(results)
    raise ArgumentError, "Room not in playing state" unless playing?

    # Update room status
    update!(status: 'finished', metadata: metadata.merge(results: results))

    # Update game sessions with results
    results[:players]&.each do |player_result|
      session = game_sessions.find_by(user_id: player_result[:user_id])
      session&.complete!(player_result)
    end
  end

  def cancel_game!(reason = nil)
    # Update room status
    update!(
      status: 'cancelled',
      metadata: metadata.merge(cancellation_reason: reason)
    )

    # Cancel all active sessions
    game_sessions.in(status: ['pending', 'active']).each do |session|
      session.cancel!(reason)
    end

    # Reset room state
    update!(current_players: 0, prize_pool: 0.0)
  end

  def waiting?
    status == 'waiting'
  end

  def playing?
    status == 'playing'
  end

  def finished?
    status == 'finished'
  end

  def cancelled?
    status == 'cancelled'
  end

  def full?
    current_players >= max_players
  end

  def empty?
    current_players == 0
  end

  def has_password?
    password_hash.present?
  end

  def is_private?
    read_attribute(:is_private) || false
  end

  def valid_password?(provided_password)
    return true unless has_password?
    BCrypt::Password.new(password_hash) == provided_password
  end

  # Get Game Service room ID from metadata
  def game_service_room_id
    metadata&.dig('game_service_room_id')
  end

  # Set Game Service room ID in metadata
  def game_service_room_id=(room_id)
    self.metadata = (metadata || {}).merge('game_service_room_id' => room_id)
  end

  # Class methods
  def self.create_with_game_service(creator, room_params)
    room_service = RoomManagementService.new
    room_service.create_room(creator, room_params)
  end

  def self.sync_all_pending
    needs_sync.find_each do |room|
      RoomSettingsSyncJob.perform_async(room.id)
    end
  end

  def self.cleanup_finished_rooms
    finished.where(:updated_at.lt => 24.hours.ago).destroy_all
  end

  # Fix existing rooms with incorrect external_room_id mapping
  def self.fix_room_id_mapping
    fixed_count = 0
    error_count = 0

    # Find rooms where external_room_id looks like a Game Service internal ID
    Room.where(:external_room_id => /^room_[a-f0-9]+_\d+$/).each do |room|
      begin
        # Store the Game Service ID in metadata
        game_service_id = room.external_room_id

        # Generate a new Manager Service external room ID
        new_external_id = "room_#{SecureRandom.hex(8)}_#{Time.current.to_i}"

        room.update!(
          external_room_id: new_external_id,
          metadata: (room.metadata || {}).merge({
            'game_service_room_id' => game_service_id,
            'room_id_fixed_at' => Time.current.iso8601
          })
        )

        Rails.logger.info "Fixed room ID mapping - Room: #{room.id} - Old: #{game_service_id} - New: #{new_external_id}"
        fixed_count += 1
      rescue => e
        Rails.logger.error "Failed to fix room ID mapping - Room: #{room.id} - Error: #{e.message}"
        error_count += 1
      end
    end

    Rails.logger.info "Room ID mapping fix completed - Fixed: #{fixed_count} - Errors: #{error_count}"
    { fixed: fixed_count, errors: error_count }
  end

  private

  def generate_external_room_id
    self.external_room_id = "room_#{SecureRandom.hex(8)}_#{Time.current.to_i}"
  end

  def encrypt_password
    if password.present?
      self.password_hash = BCrypt::Password.create(password)
    end
  end

  def password_changed?
    password.present?
  end

  def should_sync?
    sync_required? ||
    (saved_change_to_configuration? || saved_change_to_game_specific_config?)
  end

  def sync_with_game_service
    RoomSettingsSyncJob.perform_async(id.to_s)
  end

  def bet_amount_must_be_positive
    return unless bet_amount

    amount_value = bet_amount.is_a?(BigDecimal) ? bet_amount.to_f : bet_amount.to_f
    if amount_value <= 0
      errors.add(:bet_amount, "must be greater than 0")
    end
  rescue
    errors.add(:bet_amount, "must be a valid number")
  end
end
