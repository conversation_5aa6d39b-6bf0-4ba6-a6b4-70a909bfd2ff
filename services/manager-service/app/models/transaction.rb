class Transaction < ApplicationDocument
  include Mongoid::Document
  include Mongoid::Timestamps

  # Fields
  field :transaction_id, type: String
  field :transaction_type, type: String
  field :amount, type: BigDecimal
  field :balance_before, type: BigDecimal
  field :balance_after, type: BigDecimal
  field :status, type: String, default: 'pending'
  field :description, type: String
  field :reference_id, type: String
  field :reference_type, type: String
  field :processed_at, type: Time
  field :metadata, type: Hash, default: {}

  # Indexes
  index({ transaction_id: 1 }, { unique: true })
  index({ user_id: 1, created_at: -1 })
  index({ transaction_type: 1, status: 1 })
  index({ status: 1 })
  index({ reference_id: 1, reference_type: 1 })
  index({ processed_at: 1 })

  # Validations
  validates :transaction_id, presence: true, uniqueness: true
  validates :transaction_type, presence: true, inclusion: {
    in: %w[deposit admin_deposit withdrawal bet_placed bet_won bet_lost
           adjustment refund bonus reconciliation bet_reserved]
  }
  validate :amount_must_be_numeric
  validate :balance_before_must_be_non_negative
  validate :balance_after_must_be_non_negative
  validates :status, inclusion: {
    in: %w[pending completed failed cancelled]
  }

  # Associations
  belongs_to :user

  # Callbacks
  before_validation :generate_transaction_id, on: :create
  before_save :set_processed_at

  # Scopes
  scope :completed, -> { where(status: 'completed') }
  scope :pending, -> { where(status: 'pending') }
  scope :failed, -> { where(status: 'failed') }
  scope :by_type, ->(type) { where(transaction_type: type) }
  scope :recent, -> { order(created_at: :desc) }
  scope :deposits, -> { where(transaction_type: ['deposit', 'admin_deposit']) }
  scope :withdrawals, -> { where(transaction_type: 'withdrawal') }
  scope :bets, -> { where(transaction_type: /^bet_/) }
  scope :admin_deposits, -> { where(transaction_type: 'admin_deposit') }

  # Instance methods
  def complete!
    update!(
      status: 'completed',
      processed_at: Time.current
    )
  end

  def fail!(reason = nil)
    update!(
      status: 'failed',
      processed_at: Time.current,
      metadata: metadata.merge(failure_reason: reason)
    )
  end

  def cancel!(reason = nil)
    update!(
      status: 'cancelled',
      processed_at: Time.current,
      metadata: metadata.merge(cancellation_reason: reason)
    )
  end

  def debit?
    %w[withdrawal bet_placed bet_reserved].include?(transaction_type)
  end

  def credit?
    %w[deposit admin_deposit bet_won bonus adjustment refund].include?(transaction_type) && amount > 0
  end

  def completed?
    status == 'completed'
  end

  def pending?
    status == 'pending'
  end

  def failed?
    status == 'failed'
  end

  def cancelled?
    status == 'cancelled'
  end

  # Class methods
  def self.create_deposit(user, amount, description = nil, metadata = {})
    create_transaction(
      user: user,
      transaction_type: 'deposit',
      amount: amount,
      description: description || "Deposit of #{amount}",
      metadata: metadata
    )
  end

  def self.create_withdrawal(user, amount, description = nil, metadata = {})
    create_transaction(
      user: user,
      transaction_type: 'withdrawal',
      amount: -amount.abs,
      description: description || "Withdrawal of #{amount}",
      metadata: metadata
    )
  end

  def self.create_admin_deposit(user, amount, description = nil, metadata = {})
    create_transaction(
      user: user,
      transaction_type: 'admin_deposit',
      amount: amount,
      description: description || "Admin deposit of #{amount}",
      metadata: metadata
    )
  end

  def self.create_bet_placed(user, amount, game_session, metadata = {})
    create_transaction(
      user: user,
      transaction_type: 'bet_placed',
      amount: -amount.abs,
      description: "Bet placed for #{game_session.game_type}",
      reference_id: game_session.id.to_s,
      reference_type: 'GameSession',
      metadata: metadata
    )
  end

  def self.create_bet_won(user, amount, game_session, metadata = {})
    create_transaction(
      user: user,
      transaction_type: 'bet_won',
      amount: amount,
      description: "Winnings from #{game_session.game_type}",
      reference_id: game_session.id.to_s,
      reference_type: 'GameSession',
      metadata: metadata
    )
  end

  def self.stats_for_user(user_id, start_date = nil, end_date = nil)
    scope = where(user_id: user_id, status: 'completed')
    scope = scope.where(:created_at.gte => start_date) if start_date
    scope = scope.where(:created_at.lte => end_date) if end_date

    {
      total_transactions: scope.count,
      total_deposits: scope.deposits.sum(:amount),
      total_withdrawals: scope.withdrawals.sum(:amount).abs,
      total_bets: scope.where(transaction_type: 'bet_placed').sum(:amount).abs,
      total_winnings: scope.where(transaction_type: 'bet_won').sum(:amount),
      net_amount: scope.sum(:amount)
    }
  end

  def self.daily_stats(date = Date.current)
    start_time = date.beginning_of_day
    end_time = date.end_of_day
    transactions = where(:created_at.gte => start_time, :created_at.lte => end_time, status: 'completed')

    {
      total_volume: transactions.sum(:amount).abs,
      total_transactions: transactions.count,
      daily_volume: transactions.sum(:amount).abs,
      daily_transactions: transactions.count,
      by_type: {
        deposits: transactions.deposits.sum(:amount),
        withdrawals: transactions.withdrawals.sum(:amount).abs,
        bets: transactions.where(transaction_type: 'bet_placed').sum(:amount).abs,
        winnings: transactions.where(transaction_type: 'bet_won').sum(:amount)
      }
    }
  end

  private

  def generate_transaction_id
    return if transaction_id.present?
    self.transaction_id = "txn_#{user_id}_#{Time.current.to_i}_#{SecureRandom.hex(6)}"
  end

  def amount_must_be_numeric
    return unless amount

    begin
      amount_value = amount.is_a?(BigDecimal) ? amount.to_f : amount.to_f
      errors.add(:amount, "must be a number") unless amount_value.is_a?(Numeric)
    rescue
      errors.add(:amount, "must be a valid number")
    end
  end

  def balance_before_must_be_non_negative
    return unless balance_before

    begin
      balance_value = balance_before.is_a?(BigDecimal) ? balance_before.to_f : balance_before.to_f
      if balance_value < 0
        errors.add(:balance_before, "must be greater than or equal to 0")
      end
    rescue
      errors.add(:balance_before, "must be a valid number")
    end
  end

  def balance_after_must_be_non_negative
    return unless balance_after

    begin
      balance_value = balance_after.is_a?(BigDecimal) ? balance_after.to_f : balance_after.to_f
      if balance_value < 0
        errors.add(:balance_after, "must be greater than or equal to 0")
      end
    rescue
      errors.add(:balance_after, "must be a valid number")
    end
  end

  def set_processed_at
    if status_changed? && %w[completed failed cancelled].include?(status) && processed_at.blank?
      self.processed_at = Time.current
    end
  end

  def self.create_transaction(user:, transaction_type:, amount:, description: nil, reference_id: nil, reference_type: nil, metadata: {})
    current_balance = user.balance
    new_balance = current_balance + amount

    create!(
      user: user,
      transaction_type: transaction_type,
      amount: amount,
      balance_before: current_balance,
      balance_after: new_balance,
      description: description,
      reference_id: reference_id,
      reference_type: reference_type,
      metadata: metadata
    )
  end
end
