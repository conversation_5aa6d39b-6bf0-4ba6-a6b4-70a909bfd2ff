class GameSetting < ApplicationDocument
  include Mongoid::Document
  include Mongoid::Timestamps

  # Fields
  field :game_type, type: String
  field :setting_key, type: String
  field :setting_value, type: Object
  field :data_type, type: String
  field :description, type: String
  field :is_active, type: Boolean, default: true
  field :version, type: Integer, default: 1
  field :last_synced_at, type: Time
  field :sync_required, type: Boolean, default: false
  field :metadata, type: Hash, default: {}

  # Indexes
  index({ game_type: 1, setting_key: 1 }, { unique: true })
  index({ game_type: 1, is_active: 1 })
  index({ sync_required: 1 })
  index({ last_synced_at: 1 })

  # Validations
  validates :game_type, presence: true, inclusion: {
    in: %w[prizewheel amidakuji global]
  }
  validates :setting_key, presence: true
  validates :setting_value, presence: true
  validates :data_type, inclusion: {
    in: %w[string integer float boolean hash array]
  }

  # Callbacks
  before_save :cast_setting_value
  after_save :mark_for_sync
  after_update :sync_with_game_service, if: :should_sync?

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :by_game_type, ->(type) { where(game_type: type) }
  scope :needs_sync, -> { where(sync_required: true) }
  scope :global_settings, -> { where(game_type: 'global') }

  # Instance methods
  def typed_value
    case data_type
    when 'integer'
      setting_value.to_i
    when 'float'
      setting_value.to_f
    when 'boolean'
      ActiveModel::Type::Boolean.new.cast(setting_value)
    when 'hash', 'array'
      setting_value.is_a?(String) ? JSON.parse(setting_value) : setting_value
    else
      setting_value.to_s
    end
  end

  def update_value!(new_value, sync_immediately: true)
    increment(:version, 1)
    update!(
      setting_value: new_value,
      sync_required: true,
      updated_at: Time.current
    )

    if sync_immediately
      GameSettingsSyncJob.perform_later(id.to_s)
    end
  end

  def mark_synced!
    update!(
      sync_required: false,
      last_synced_at: Time.current
    )
  end

  # Class methods
  def self.get_setting(game_type, key, default_value = nil)
    setting = find_by(game_type: game_type, setting_key: key, is_active: true)
    setting&.typed_value || default_value
  end

  def self.set_setting(game_type, key, value, data_type: nil, description: nil)
    data_type ||= infer_data_type(value)

    setting = find_or_initialize_by(game_type: game_type, setting_key: key)
    setting.assign_attributes(
      setting_value: value,
      data_type: data_type,
      description: description,
      is_active: true,
      sync_required: true
    )

    setting.save!
    setting
  end

  def self.get_game_config(game_type)
    settings = by_game_type(game_type).active.to_a
    config = {}

    settings.each do |setting|
      config[setting.setting_key] = setting.typed_value
    end

    config
  end

  # Get game state durations for a specific game type
  def self.get_state_durations(game_type)
    {
      starting: get_setting(game_type, 'starting_duration', 10),
      playing: get_setting(game_type, 'playing_duration', 30),
      end: get_setting(game_type, 'end_duration', 15)
    }
  end

  # Update game state durations for a specific game type
  def self.update_state_durations(game_type, durations)
    durations.each do |state, duration|
      duration_key = "#{state}_duration"
      set_setting(game_type, duration_key, duration.to_i,
                  description: "Duration for #{state} state in seconds")
    end
  end

  # Validate state duration values
  def self.validate_state_durations(durations)
    errors = []

    durations.each do |state, duration|
      duration_value = duration.to_i

      # Basic validation - just ensure positive values
      if duration_value <= 0
        errors << "#{state.to_s.humanize} duration must be greater than 0 seconds"
      end
    end

    errors
  end

  def self.update_game_config(game_type, config_hash)
    config_hash.each do |key, value|
      set_setting(game_type, key.to_s, value)
    end

    # Sync all settings for this game type
    GameSettingsSyncJob.perform_later(game_type)
  end

  def self.sync_all_pending
    needs_sync.find_each do |setting|
      GameSettingsSyncJob.perform_later(setting.id.to_s)
    end
  end

  def self.infer_data_type(value)
    case value
    when Integer
      'integer'
    when Float
      'float'
    when TrueClass, FalseClass
      'boolean'
    when Hash
      'hash'
    when Array
      'array'
    else
      'string'
    end
  end

  # Default game settings
  def self.seed_default_settings
    # Prize Wheel defaults
    prizewheel_defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'default_sections' => 8,
      'spin_duration_min' => 3000,
      'spin_duration_max' => 8000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 60,
      'idle_timeout' => 1800,
      # Game state durations (in seconds)
      'starting_duration' => 10,     # 10 seconds - countdown before game starts
      'playing_duration' => 30,      # 30 seconds - actual game play
      'end_duration' => 15           # 15 seconds - showing results before cleanup
    }

    # Amidakuji defaults
    amidakuji_defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'rows_per_player' => 3,
      'line_probability' => 0.5,
      'animation_duration' => 5000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 90,
      'idle_timeout' => 1800,
      # Game state durations (in seconds)
      'starting_duration' => 10,     # 10 seconds - countdown before game starts
      'playing_duration' => 60,      # 60 seconds - actual game play (longer for Amidakuji)
      'end_duration' => 15           # 15 seconds - showing results before cleanup
    }

    # Global defaults
    global_defaults = {
      'max_concurrent_games' => 1000,
      'maintenance_mode' => false,
      'prizewheel_enabled' => true,
      'amidakuji_enabled' => true
    }

    prizewheel_defaults.each { |k, v| set_setting('prizewheel', k, v) }
    amidakuji_defaults.each { |k, v| set_setting('amidakuji', k, v) }
    global_defaults.each { |k, v| set_setting('global', k, v) }
  end

  private

  def cast_setting_value
    self.setting_value = case data_type
    when 'integer'
      setting_value.to_i
    when 'float'
      setting_value.to_f
    when 'boolean'
      ActiveModel::Type::Boolean.new.cast(setting_value)
    when 'hash', 'array'
      setting_value.is_a?(String) ? setting_value : setting_value.to_json
    else
      setting_value.to_s
    end
  end

  def mark_for_sync
    self.sync_required = true if changed?
  end

  def should_sync?
    sync_required? && saved_change_to_setting_value?
  end

  def sync_with_game_service
    GameSettingsSyncJob.perform_later(id.to_s)
  end
end
