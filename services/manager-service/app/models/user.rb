class User < ApplicationDocument
  include Mongoid::Document
  include Mongoid::Timestamps
  include BCrypt

  # Fields
  field :username, type: String
  field :email, type: String
  field :password_digest, type: String
  field :balance, type: BigDecimal, default: 0.0
  field :status, type: String, default: 'active'
  field :role, type: String, default: 'player'
  field :last_login_at, type: Time
  field :metadata, type: Hash, default: {}

  # Indexes
  index({ username: 1 }, { unique: true })
  index({ email: 1 }, { unique: true })
  index({ status: 1 })
  index({ role: 1 })
  index({ last_login_at: -1 })

  # Validations
  validates :username, presence: true, uniqueness: true, length: { minimum: 3, maximum: 50 }
  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?
  validate :balance_must_be_non_negative
  validates :status, inclusion: { in: %w[active inactive suspended banned] }
  validates :role, inclusion: { in: %w[player admin moderator] }

  # Associations
  has_many :game_sessions, dependent: :destroy
  has_many :transactions, dependent: :destroy

  # Callbacks
  before_save :encrypt_password, if: :password_changed?
  before_save :normalize_email
  before_validation :set_default_balance

  # Virtual attributes
  attr_accessor :password, :password_confirmation

  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :players, -> { where(role: 'player') }
  scope :admins, -> { where(role: 'admin') }
  scope :moderators, -> { where(role: 'moderator') }
  scope :recent_login, -> { where(:last_login_at.gte => 30.days.ago) }
  scope :suspended, -> { where(status: 'suspended') }
  scope :banned, -> { where(status: 'banned') }
  scope :inactive, -> { where(status: 'inactive') }

  # Instance methods
  def authenticate(password)
    BCrypt::Password.new(password_digest) == password
  end

  def admin?
    role == 'admin'
  end

  def player?
    role == 'player'
  end

  def active?
    status == 'active'
  end

  def can_play?
    active? && balance >= 0
  end

  def update_balance!(amount, transaction_type = 'adjustment', description: nil, reference_id: nil, reference_type: nil, metadata: {})
    # Validate inputs
    raise ArgumentError, "Amount cannot be zero" if amount == 0
    raise ArgumentError, "Invalid transaction type" unless valid_transaction_type?(transaction_type)

    # Check for sufficient balance on debits
    if amount < 0 && (self.balance + amount) < 0
      raise ArgumentError, "Insufficient balance. Required: #{amount.abs}, Available: #{self.balance}"
    end

    balance_before = self.balance
    new_balance = balance_before + amount

    # Update user balance
    self.balance = new_balance
    save!

    # Create transaction record (transaction_id will be generated by callback)
    transaction_record = transactions.create!(
      amount: amount,
      transaction_type: transaction_type,
      balance_before: balance_before,
      balance_after: new_balance,
      description: description || "Balance #{transaction_type}: #{amount}",
      reference_id: reference_id,
      reference_type: reference_type,
      status: 'completed',
      metadata: metadata.merge(updated_at: Time.current)
    )

    transaction_record
  end

  def recent_login?
    last_login_at && last_login_at > 30.days.ago
  end

  def moderator?
    role == 'moderator'
  end

  def suspended?
    status == 'suspended'
  end

  def banned?
    status == 'banned'
  end

  def inactive?
    status == 'inactive'
  end

  def statistics
    {
      total_games: game_sessions.completed.count,
      games_won: game_sessions.completed.where(:win_amount.gt => :bet_amount).count,
      total_bet: game_sessions.completed.sum(:bet_amount),
      total_won: game_sessions.completed.sum(:win_amount),
      win_rate: calculate_win_rate
    }
  end

  def update_last_login!
    update!(last_login_at: Time.current)
  end

  private

  def valid_transaction_type?(type)
    %w[deposit admin_deposit withdrawal bet_placed bet_won bet_lost
       adjustment refund bonus reconciliation bet_reserved].include?(type)
  end

  def balance_must_be_non_negative
    return unless balance

    balance_value = balance.is_a?(BigDecimal) ? balance.to_f : balance.to_f
    if balance_value < 0
      errors.add(:balance, "must be greater than or equal to 0")
    end
  rescue
    errors.add(:balance, "must be a valid number")
  end

  def calculate_win_rate
    total_games = game_sessions.completed.count
    return 0.0 if total_games.zero?

    games_won = game_sessions.completed.where(:win_amount.gt => :bet_amount).count
    games_won.to_f / total_games
  end

  def password_required?
    password_digest.blank? || password.present?
  end

  def password_changed?
    password.present?
  end

  def encrypt_password
    self.password_digest = BCrypt::Password.create(password)
  end

  def normalize_email
    self.email = email.downcase.strip if email.present?
  end

  def set_default_balance
    self.balance = 0.0 if balance.nil?
  end
end
