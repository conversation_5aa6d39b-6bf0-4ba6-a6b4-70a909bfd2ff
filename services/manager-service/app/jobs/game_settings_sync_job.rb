class GameSettingsSyncJob < ApplicationJob
  queue_as :critical

  def perform(identifier)
    log_job_start(identifier: identifier)

    with_error_handling(identifier: identifier) do
      if identifier.is_a?(String) && %w[prizewheel amidakuji global].include?(identifier)
        # Sync all settings for a game type
        sync_game_type_settings(identifier)
      else
        # Sync individual setting by ID
        sync_individual_setting(identifier)
      end
    end

    log_job_completion(identifier: identifier)
  end

  private

  def sync_game_type_settings(game_type)
    logger.info "Syncing all settings for game type", game_type: game_type

    settings = GameSetting.by_game_type(game_type).active.needs_sync
    
    if settings.empty?
      logger.info "No settings to sync for game type", game_type: game_type
      return
    end

    # Collect all settings for this game type
    settings_hash = {}
    settings.each do |setting|
      settings_hash[setting.setting_key] = setting.typed_value
    end

    # Sync with Game Service
    begin
      GameServiceClient.instance.sync_game_settings(game_type, settings_hash)
      
      # Mark all settings as synced
      settings.update_all(
        sync_required: false,
        last_synced_at: Time.current
      )

      # Publish notification
      NotificationService.new.publish_game_settings_update(game_type, settings_hash)

      logger.info "Successfully synced game type settings",
        game_type: game_type,
        settings_count: settings.count
        
    rescue StandardError => e
      logger.error "Failed to sync game type settings",
        game_type: game_type,
        error: e.message
      
      # Don't mark as synced if it failed
      raise e
    end
  end

  def sync_individual_setting(setting_id)
    # Convert string ID back to ObjectId if needed
    object_id = setting_id.is_a?(String) ? BSON::ObjectId.from_string(setting_id) : setting_id
    setting = GameSetting.find(object_id)
    
    logger.info "Syncing individual setting",
      setting_id: setting_id,
      game_type: setting.game_type,
      setting_key: setting.setting_key

    unless setting.sync_required?
      logger.info "Setting does not require sync", setting_id: setting_id
      return
    end

    # Get all settings for this game type to send complete config
    all_settings = GameSetting.by_game_type(setting.game_type).active
    settings_hash = {}
    all_settings.each do |s|
      settings_hash[s.setting_key] = s.typed_value
    end

    # Sync with Game Service
    begin
      GameServiceClient.instance.sync_game_settings(setting.game_type, settings_hash)
      
      # Mark this setting as synced
      setting.mark_synced!

      # Publish notification
      NotificationService.new.publish_game_settings_update(setting.game_type, settings_hash)

      logger.info "Successfully synced individual setting",
        setting_id: setting_id,
        game_type: setting.game_type,
        setting_key: setting.setting_key
        
    rescue StandardError => e
      logger.error "Failed to sync individual setting",
        setting_id: setting_id,
        game_type: setting.game_type,
        setting_key: setting.setting_key,
        error: e.message
      
      raise e
    end
  rescue Mongoid::Errors::DocumentNotFound
    logger.warn "Game setting not found", setting_id: setting_id
  end
end
