class RoomConsistencyCheckJob
  include Sidekiq::Job

  def perform
    Rails.logger.info "Starting room consistency check job"
    
    fixed_count = 0
    error_count = 0
    
    # Check all active rooms (waiting, playing, starting)
    Room.in(status: ['waiting', 'playing', 'starting']).each do |room|
      begin
        consistency_check = room.check_data_consistency
        
        unless consistency_check[:consistent]
          Rails.logger.warn "Data inconsistency detected in room #{room.id}: #{consistency_check}"
          
          fix_result = room.fix_data_consistency!
          
          if fix_result[:fixed]
            Rails.logger.info "Fixed room #{room.id} data inconsistency: #{fix_result}"
            fixed_count += 1
            
            # Notify other services about the room update
            begin
              notification_service = NotificationService.new
              notification_service.publish_room_update(room, 'consistency_fixed', {
                old_count: fix_result[:old_count],
                new_count: fix_result[:new_count],
                difference: fix_result[:difference]
              })
            rescue => notification_error
              Rails.logger.error "Failed to notify about room consistency fix: #{notification_error.message}"
            end
          end
        end
        
      rescue => e
        Rails.logger.error "Error checking consistency for room #{room.id}: #{e.message}"
        error_count += 1
      end
    end
    
    Rails.logger.info "Room consistency check completed - Fixed: #{fixed_count}, Errors: #{error_count}"
    
    # Return summary for monitoring
    {
      fixed_count: fixed_count,
      error_count: error_count,
      checked_at: Time.current
    }
  end
end
