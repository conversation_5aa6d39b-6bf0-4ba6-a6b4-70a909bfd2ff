class BalanceReconciliationJob < ApplicationJob
  queue_as :low

  def perform(user_id = nil)
    if user_id
      log_job_start(user_id: user_id)
      reconcile_user_balance(user_id)
      log_job_completion(user_id: user_id)
    else
      log_job_start(scope: 'all_users')
      reconcile_all_user_balances
      log_job_completion(scope: 'all_users')
    end
  end

  private

  def reconcile_user_balance(user_id)
    with_error_handling(user_id: user_id) do
      user = User.find(user_id)
      
      logger.info "Starting balance reconciliation for user",
        user_id: user_id,
        username: user.username,
        current_balance: user.balance

      result = TransactionService.new.reconcile_user_balance(user)
      
      if result[:reconciled]
        logger.warn "Balance discrepancy found and corrected",
          user_id: user_id,
          username: user.username,
          difference: result[:difference],
          transaction_id: result[:transaction].transaction_id

        # Notify user of balance correction
        NotificationJob.perform_async(
          'balance_reconciliation',
          [user_id],
          {
            difference: result[:difference],
            transaction_id: result[:transaction].id.to_s,
            reconciled_at: Time.current.iso8601
          }
        )

        # Notify admins of discrepancy
        admin_user_ids = User.admins.pluck(:id).map(&:to_s)
        NotificationJob.perform_async(
          'balance_discrepancy_detected',
          admin_user_ids,
          {
            user_id: user_id,
            username: user.username,
            difference: result[:difference],
            transaction_id: result[:transaction].id.to_s
          }
        )
      else
        logger.info "Balance reconciliation completed - no discrepancy found",
          user_id: user_id,
          username: user.username
      end
    end
  rescue Mongoid::Errors::DocumentNotFound
    logger.warn "User not found for balance reconciliation", user_id: user_id
  end

  def reconcile_all_user_balances
    with_error_handling(scope: 'all_users') do
      total_users = 0
      reconciled_users = 0
      total_discrepancy = 0.0
      
      logger.info "Starting balance reconciliation for all users"

      User.active.find_each(batch_size: 100) do |user|
        total_users += 1
        
        begin
          result = TransactionService.new.reconcile_user_balance(user)
          
          if result[:reconciled]
            reconciled_users += 1
            total_discrepancy += result[:difference].abs
            
            logger.warn "Balance discrepancy found",
              user_id: user.id.to_s,
              username: user.username,
              difference: result[:difference]

            # Notify user of balance correction
            NotificationJob.perform_async(
              'balance_reconciliation',
              [user.id.to_s],
              {
                difference: result[:difference],
                transaction_id: result[:transaction].id.to_s,
                reconciled_at: Time.current.iso8601
              }
            )
          end
        rescue StandardError => e
          logger.error "Failed to reconcile balance for user",
            user_id: user.id.to_s,
            username: user.username,
            error: e.message
        end

        # Add small delay to avoid overwhelming the database
        sleep(0.01) if total_users % 100 == 0
      end

      logger.info "Balance reconciliation completed for all users",
        total_users: total_users,
        reconciled_users: reconciled_users,
        total_discrepancy: total_discrepancy

      # Notify admins of reconciliation summary
      if reconciled_users > 0
        admin_user_ids = User.admins.pluck(:id).map(&:to_s)
        NotificationJob.perform_async(
          'balance_reconciliation_summary',
          admin_user_ids,
          {
            total_users: total_users,
            reconciled_users: reconciled_users,
            total_discrepancy: total_discrepancy,
            completed_at: Time.current.iso8601
          }
        )
      end
    end
  end
end
