class TransactionProcessorJob < ApplicationJob
  queue_as :critical

  def perform(transaction_id, action = 'process')
    log_job_start(transaction_id: transaction_id, action: action)

    with_error_handling(transaction_id: transaction_id, action: action) do
      transaction = Transaction.find(transaction_id)
      
      case action
      when 'process'
        process_transaction(transaction)
      when 'complete'
        complete_transaction(transaction)
      when 'fail'
        fail_transaction(transaction)
      when 'cancel'
        cancel_transaction(transaction)
      else
        raise ArgumentError, "Unknown action: #{action}"
      end
    end

    log_job_completion(transaction_id: transaction_id, action: action)
  rescue Mongoid::Errors::DocumentNotFound
    logger.warn "Transaction not found", transaction_id: transaction_id
  end

  private

  def process_transaction(transaction)
    logger.info "Processing transaction",
      transaction_id: transaction.transaction_id,
      type: transaction.transaction_type,
      amount: transaction.amount,
      user_id: transaction.user_id.to_s

    unless transaction.pending?
      logger.warn "Transaction is not in pending state",
        transaction_id: transaction.transaction_id,
        current_status: transaction.status
      return
    end

    begin
      case transaction.transaction_type
      when 'deposit'
        process_deposit(transaction)
      when 'withdrawal'
        process_withdrawal(transaction)
      when 'bet_placed'
        process_bet_placed(transaction)
      when 'bet_won'
        process_bet_won(transaction)
      when 'refund'
        process_refund(transaction)
      when 'adjustment'
        process_adjustment(transaction)
      else
        logger.warn "Unknown transaction type",
          transaction_id: transaction.transaction_id,
          type: transaction.transaction_type
        return
      end

      # Complete the transaction
      transaction.complete!
      
      # Publish notification
      NotificationJob.perform_async(
        'transaction_update',
        [transaction.user_id.to_s],
        {
          transaction_id: transaction.id.to_s,
          event_type: 'completed'
        }
      )

      logger.info "Transaction processed successfully",
        transaction_id: transaction.transaction_id

    rescue StandardError => e
      logger.error "Transaction processing failed",
        transaction_id: transaction.transaction_id,
        error: e.message

      transaction.fail!(e.message)
      
      # Publish failure notification
      NotificationJob.perform_async(
        'transaction_update',
        [transaction.user_id.to_s],
        {
          transaction_id: transaction.id.to_s,
          event_type: 'failed',
          error: e.message
        }
      )

      raise e
    end
  end

  def complete_transaction(transaction)
    logger.info "Completing transaction", transaction_id: transaction.transaction_id

    transaction.complete!
    
    # Publish notification
    NotificationJob.perform_async(
      'transaction_update',
      [transaction.user_id.to_s],
      {
        transaction_id: transaction.id.to_s,
        event_type: 'completed'
      }
    )
  end

  def fail_transaction(transaction)
    reason = "Transaction processing failed"
    
    logger.info "Failing transaction",
      transaction_id: transaction.transaction_id,
      reason: reason

    transaction.fail!(reason)
    
    # Publish notification
    NotificationJob.perform_async(
      'transaction_update',
      [transaction.user_id.to_s],
      {
        transaction_id: transaction.id.to_s,
        event_type: 'failed',
        reason: reason
      }
    )
  end

  def cancel_transaction(transaction)
    reason = "Transaction cancelled"
    
    logger.info "Cancelling transaction",
      transaction_id: transaction.transaction_id,
      reason: reason

    transaction.cancel!(reason)
    
    # Publish notification
    NotificationJob.perform_async(
      'transaction_update',
      [transaction.user_id.to_s],
      {
        transaction_id: transaction.id.to_s,
        event_type: 'cancelled',
        reason: reason
      }
    )
  end

  def process_deposit(transaction)
    # Validate deposit amount
    if transaction.amount <= 0
      raise ArgumentError, "Deposit amount must be positive"
    end

    # In a real implementation, this would integrate with payment processors
    # For now, we'll just mark it as processed
    logger.info "Deposit validated", transaction_id: transaction.transaction_id
  end

  def process_withdrawal(transaction)
    # Validate withdrawal amount
    if transaction.amount >= 0
      raise ArgumentError, "Withdrawal amount must be negative"
    end

    # Check if user has sufficient balance
    user = transaction.user
    if user.balance < transaction.amount.abs
      raise ArgumentError, "Insufficient balance for withdrawal"
    end

    # In a real implementation, this would integrate with payment processors
    # For now, we'll just mark it as processed
    logger.info "Withdrawal validated", transaction_id: transaction.transaction_id
  end

  def process_bet_placed(transaction)
    # Validate bet amount
    if transaction.amount >= 0
      raise ArgumentError, "Bet amount must be negative"
    end

    # Check if user has sufficient balance
    user = transaction.user
    if user.balance < transaction.amount.abs
      raise ArgumentError, "Insufficient balance for bet"
    end

    # Validate game session exists
    if transaction.reference_type == 'GameSession'
      game_session = GameSession.find(transaction.reference_id)
      unless game_session.user_id == user.id
        raise ArgumentError, "Game session does not belong to user"
      end
    end

    logger.info "Bet placement validated", transaction_id: transaction.transaction_id
  end

  def process_bet_won(transaction)
    # Validate win amount
    if transaction.amount <= 0
      raise ArgumentError, "Win amount must be positive"
    end

    # Validate game session exists and is completed
    if transaction.reference_type == 'GameSession'
      game_session = GameSession.find(transaction.reference_id)
      unless game_session.user_id == transaction.user_id
        raise ArgumentError, "Game session does not belong to user"
      end
      unless game_session.completed?
        raise ArgumentError, "Game session is not completed"
      end
    end

    logger.info "Bet winnings validated", transaction_id: transaction.transaction_id
  end

  def process_refund(transaction)
    # Validate refund amount
    if transaction.amount <= 0
      raise ArgumentError, "Refund amount must be positive"
    end

    # Validate original transaction exists
    if transaction.reference_type == 'Transaction'
      original_transaction = Transaction.find(transaction.reference_id)
      unless original_transaction.user_id == transaction.user_id
        raise ArgumentError, "Original transaction does not belong to user"
      end
      unless original_transaction.debit?
        raise ArgumentError, "Can only refund debit transactions"
      end
    end

    logger.info "Refund validated", transaction_id: transaction.transaction_id
  end

  def process_adjustment(transaction)
    # Validate adjustment has admin metadata
    unless transaction.metadata['admin_user_id'].present?
      raise ArgumentError, "Adjustment must have admin user information"
    end

    admin_user = User.find(transaction.metadata['admin_user_id'])
    unless admin_user.admin?
      raise ArgumentError, "Only admins can create balance adjustments"
    end

    logger.info "Balance adjustment validated",
      transaction_id: transaction.transaction_id,
      admin_user_id: admin_user.id.to_s
  end
end
