class NotificationJob < ApplicationJob
  queue_as :notifications

  def perform(notification_type, user_ids, data = {})
    log_job_start(
      notification_type: notification_type,
      user_count: Array(user_ids).count
    )

    with_error_handling(notification_type: notification_type, user_ids: user_ids) do
      notification_service = NotificationService.new
      
      case notification_type
      when 'balance_update'
        handle_balance_update(notification_service, user_ids, data)
      when 'game_session_update'
        handle_game_session_update(notification_service, user_ids, data)
      when 'room_update'
        handle_room_update(notification_service, user_ids, data)
      when 'room_deleted'
        handle_room_deleted(notification_service, user_ids, data)
      when 'room_settings_updated'
        handle_room_settings_updated(notification_service, user_ids, data)
      when 'kicked_from_room'
        handle_kicked_from_room(notification_service, user_ids, data)
      when 'game_started'
        handle_game_started(notification_service, user_ids, data)
      when 'game_completed'
        handle_game_completed(notification_service, user_ids, data)
      when 'system_announcement'
        handle_system_announcement(notification_service, user_ids, data)
      when 'transaction_update'
        handle_transaction_update(notification_service, user_ids, data)
      else
        # Generic user notification
        notification_service.publish_user_notification(user_ids, notification_type, data)
      end
    end

    log_job_completion(
      notification_type: notification_type,
      user_count: Array(user_ids).count
    )
  end

  private

  def handle_balance_update(service, user_ids, data)
    user_ids = Array(user_ids)
    
    user_ids.each do |user_id|
      user = User.find(user_id)
      transaction = Transaction.find(data[:transaction_id]) if data[:transaction_id]
      
      if user && transaction
        service.publish_balance_update(user, transaction)
      end
    end
  rescue Mongoid::Errors::DocumentNotFound => e
    logger.warn "User or transaction not found for balance update", 
      user_ids: user_ids,
      transaction_id: data[:transaction_id],
      error: e.message
  end

  def handle_game_session_update(service, user_ids, data)
    game_session = GameSession.find(data[:game_session_id])
    event_type = data[:event_type] || 'updated'
    
    service.publish_game_session_update(game_session, event_type)
  rescue Mongoid::Errors::DocumentNotFound => e
    logger.warn "Game session not found for update", 
      game_session_id: data[:game_session_id],
      error: e.message
  end

  def handle_room_update(service, user_ids, data)
    room = Room.find(data[:room_id])
    event_type = data[:event_type] || 'updated'
    additional_data = data[:additional_data] || {}
    
    service.publish_room_update(room, event_type, additional_data)
  rescue Mongoid::Errors::DocumentNotFound => e
    logger.warn "Room not found for update", 
      room_id: data[:room_id],
      error: e.message
  end

  def handle_room_deleted(service, user_ids, data)
    service.publish_user_notification(
      user_ids,
      'room_deleted',
      {
        room_name: data[:room_name],
        reason: data[:reason],
        refunded: data[:refunded]
      }
    )
  end

  def handle_room_settings_updated(service, user_ids, data)
    service.publish_user_notification(
      user_ids,
      'room_settings_updated',
      {
        room_name: data[:room_name],
        room_id: data[:room_id],
        settings_updated: data[:settings_updated],
        apply_immediately: data[:apply_immediately]
      }
    )
  end

  def handle_kicked_from_room(service, user_ids, data)
    service.publish_user_notification(
      user_ids,
      'kicked_from_room',
      {
        room_name: data[:room_name],
        reason: data[:reason],
        refunded: data[:refunded]
      }
    )
  end

  def handle_game_started(service, user_ids, data)
    service.publish_user_notification(
      user_ids,
      'game_started',
      {
        room_name: data[:room_name],
        room_id: data[:room_id],
        game_type: data[:game_type],
        participants_count: data[:participants_count]
      }
    )
  end

  def handle_game_completed(service, user_ids, data)
    user_ids = Array(user_ids)
    
    user_ids.each do |user_id|
      user_data = data[:results]&.find { |r| r[:user_id] == user_id }
      next unless user_data

      service.publish_user_notification(
        user_id,
        'game_completed',
        {
          room_name: data[:room_name],
          game_type: data[:game_type],
          bet_amount: user_data[:bet_amount],
          win_amount: user_data[:win_amount],
          profit_loss: user_data[:profit_loss],
          result: user_data[:result]
        }
      )
    end
  end

  def handle_system_announcement(service, user_ids, data)
    message = data[:message]
    target_users = user_ids == 'all' ? nil : user_ids
    
    service.publish_system_announcement(message, target_users)
  end

  def handle_transaction_update(service, user_ids, data)
    transaction = Transaction.find(data[:transaction_id])
    event_type = data[:event_type] || 'updated'
    
    service.publish_transaction_update(transaction, event_type)
  rescue Mongoid::Errors::DocumentNotFound => e
    logger.warn "Transaction not found for update", 
      transaction_id: data[:transaction_id],
      error: e.message
  end
end
