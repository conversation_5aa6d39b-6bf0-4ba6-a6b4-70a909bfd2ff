class ApplicationJob < ActiveJob::Base

  # Automatically retry jobs that encountered a deadlock or connection issues
  retry_on Mongoid::Errors::DocumentNotFound, wait: 5.seconds, attempts: 3
  retry_on Redis::CannotConnectError, wait: 10.seconds, attempts: 5
  retry_on StandardError, wait: :exponentially_longer, attempts: 5

  # Most jobs are safe to ignore if the underlying records are no longer available
  discard_on ActiveJob::DeserializationError
  discard_on Mongoid::Errors::InvalidFind

  # Set default queue and priority
  queue_as :default

  protected

  def logger
    Rails.logger
  end

  def with_error_handling(context = {})
    yield
  rescue StandardError => e
    logger.error "Job failed: #{self.class.name}",
      context.merge(
        error: e.message,
        backtrace: e.backtrace.first(5)
      )
    raise e
  end

  def log_job_start(context = {})
    logger.info "Job started: #{self.class.name}", context
  end

  def log_job_completion(context = {})
    logger.info "Job completed: #{self.class.name}", context
  end
end
