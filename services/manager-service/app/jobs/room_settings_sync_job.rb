class RoomSettingsSyncJob < ApplicationJob
  queue_as :critical

  def perform(room_id)
    log_job_start(room_id: room_id)

    with_error_handling(room_id: room_id) do
      room = Room.find(room_id)
      
      unless room.sync_required?
        logger.info "Room does not require sync", room_id: room_id
        return
      end

      sync_room_with_game_service(room)
    end

    log_job_completion(room_id: room_id)
  rescue Mongoid::Errors::DocumentNotFound
    logger.warn "Room not found for sync", room_id: room_id
  end

  private

  def sync_room_with_game_service(room)
    logger.info "Syncing room with Game Service",
      room_id: room.id.to_s,
      external_room_id: room.external_room_id,
      game_type: room.game_type

    settings = {
      max_players: room.max_players,
      bet_amount: room.bet_amount,
      currency: room.currency,
      game_duration: room.configuration['game_duration'] || 30,
      game_specific_config: room.game_specific_config
    }

    begin
      # Sync with Game Service
      GameServiceClient.instance.update_room_settings(room.external_room_id, settings)
      
      # Mark room as synced
      room.update!(
        sync_required: false,
        last_synced_at: Time.current
      )

      # Publish room update notification
      NotificationService.new.publish_room_update(room, 'settings_synced', {
        synced_settings: settings.keys,
        synced_at: Time.current.iso8601
      })

      logger.info "Successfully synced room settings",
        room_id: room.id.to_s,
        external_room_id: room.external_room_id,
        settings: settings.keys
        
    rescue StandardError => e
      logger.error "Failed to sync room settings",
        room_id: room.id.to_s,
        external_room_id: room.external_room_id,
        error: e.message
      
      # Don't mark as synced if it failed
      raise e
    end
  end
end
