class GameSessionsController < ApplicationController
  before_action :set_game_session, only: [:show, :update, :start, :complete, :cancel]
  before_action :check_session_access, only: [:show, :update, :start, :complete, :cancel]

  # GET /game_sessions
  def index
    @game_sessions = current_user.admin? ? GameSession.all : current_user.game_sessions

    # Apply filters
    @game_sessions = @game_sessions.by_game_type(params[:game_type]) if params[:game_type].present?
    @game_sessions = @game_sessions.where(status: params[:status]) if params[:status].present?
    @game_sessions = @game_sessions.where(user_id: params[:user_id]) if params[:user_id].present? && current_user.admin?

    # Pagination
    @game_sessions = @game_sessions.recent.page(params[:page]).per(params[:per_page] || 25)

    success_response({
      game_sessions: @game_sessions.map { |session| game_session_data(session) },
      pagination: pagination_data(@game_sessions)
    })
  end

  # GET /game_sessions/:id
  def show
    success_response({
      game_session: game_session_data(@game_session)
    })
  end

  # POST /game_sessions
  def create
    @game_session = GameSession.new(game_session_params.merge(user: current_user))

    # Validate user can afford the bet
    unless current_user.balance >= @game_session.bet_amount
      return error_response('Insufficient balance for this bet', :unprocessable_entity)
    end

    # Validate user can play
    unless current_user.can_play?
      return error_response('User account is not eligible to play', :forbidden)
    end

    if @game_session.save
      # Create bet transaction
      Transaction.create_bet_placed(
        current_user,
        @game_session.bet_amount,
        @game_session,
        { game_type: @game_session.game_type }
      )

      # Update user balance
      current_user.update_balance!(
        -@game_session.bet_amount,
        'bet_placed',
        "Bet placed for #{@game_session.game_type}",
        @game_session.id.to_s,
        'GameSession'
      )

      success_response({
        game_session: game_session_data(@game_session)
      }, 'Game session created successfully', :created)
    else
      validation_error(@game_session.errors)
    end
  rescue StandardError => e
    Rails.logger.error "Game session creation failed: #{e.message}"
    error_response('Failed to create game session', :unprocessable_entity)
  end

  # PUT /game_sessions/:id
  def update
    if @game_session.update(game_session_update_params)
      success_response({
        game_session: game_session_data(@game_session)
      }, 'Game session updated successfully')
    else
      validation_error(@game_session.errors)
    end
  end

  # PUT /game_sessions/:id/start
  def start
    unless @game_session.status == 'pending'
      return error_response('Game session cannot be started', :unprocessable_entity)
    end

    @game_session.start!

    success_response({
      game_session: game_session_data(@game_session)
    }, 'Game session started successfully')
  rescue StandardError => e
    Rails.logger.error "Failed to start game session: #{e.message}"
    error_response('Failed to start game session', :unprocessable_entity)
  end

  # PUT /game_sessions/:id/complete
  def complete
    unless @game_session.status == 'active'
      return error_response('Game session cannot be completed', :unprocessable_entity)
    end

    result_data = complete_params[:result] || {}
    win_amount = complete_params[:win_amount] || 0

    @game_session.update!(
      win_amount: win_amount,
      result: result_data
    )
    @game_session.complete!(result_data)

    # Create winning transaction if user won
    if win_amount > 0
      Transaction.create_bet_won(
        @game_session.user,
        win_amount,
        @game_session,
        { game_type: @game_session.game_type, result: result_data }
      )

      # Update user balance
      @game_session.user.update_balance!(
        win_amount,
        'bet_won',
        "Winnings from #{@game_session.game_type}",
        @game_session.id.to_s,
        'GameSession'
      )
    end

    success_response({
      game_session: game_session_data(@game_session)
    }, 'Game session completed successfully')
  rescue StandardError => e
    Rails.logger.error "Failed to complete game session: #{e.message}"
    error_response('Failed to complete game session', :unprocessable_entity)
  end

  # PUT /game_sessions/:id/cancel
  def cancel
    unless ['pending', 'active'].include?(@game_session.status)
      return error_response('Game session cannot be cancelled', :unprocessable_entity)
    end

    reason = cancel_params[:reason] || 'Session cancelled'
    @game_session.cancel!(reason)

    # Refund the bet amount
    @game_session.user.update_balance!(
      @game_session.bet_amount,
      'refund',
      "Refund for cancelled #{@game_session.game_type} session",
      @game_session.id.to_s,
      'GameSession'
    )

    success_response({
      game_session: game_session_data(@game_session)
    }, 'Game session cancelled successfully')
  rescue StandardError => e
    Rails.logger.error "Failed to cancel game session: #{e.message}"
    error_response('Failed to cancel game session', :unprocessable_entity)
  end

  private

  def set_game_session
    @game_session = GameSession.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    error_response('Game session not found', :not_found)
  end

  def check_session_access
    unless current_user.admin? || @game_session.user_id == current_user.id
      unauthorized('You can only access your own game sessions')
    end
  end

  def game_session_params
    params.require(:game_session).permit(:game_type, :bet_amount, metadata: {})
  end

  def game_session_update_params
    params.require(:game_session).permit(:win_amount, result: {}, metadata: {})
  end

  def complete_params
    params.permit(:win_amount, result: {})
  end

  def cancel_params
    params.permit(:reason)
  end

  def game_session_data(session)
    {
      id: session.id.to_s,
      session_id: session.session_id,
      game_type: session.game_type,
      status: session.status,
      bet_amount: session.bet_amount.to_f,
      win_amount: session.win_amount.to_f,
      profit_loss: session.profit_loss.to_f,
      result: session.result,
      user: {
        id: session.user.id.to_s,
        username: session.user.username
      },
      started_at: session.started_at&.iso8601,
      ended_at: session.ended_at&.iso8601,
      duration: session.duration,
      created_at: session.created_at.iso8601,
      metadata: session.metadata
    }
  end

  def pagination_data(collection)
    {
      current_page: collection.current_page,
      total_pages: collection.total_pages,
      total_count: collection.total_count,
      per_page: collection.limit_value,
      has_next: collection.next_page.present?,
      has_prev: collection.prev_page.present?
    }
  end
end
