class ApplicationController < ActionController::API
  include ActionController::HttpAuthentication::Token::ControllerMethods

  before_action :authenticate_request
  before_action :set_current_user

  rescue_from Mongoid::Errors::DocumentNotFound, with: :not_found
  rescue_from Mongoid::Errors::Validations, with: :validation_error
  rescue_from JWT::DecodeError, with: :unauthorized
  rescue_from StandardError, with: :internal_server_error

  protected

  def authenticate_request
    token = request.headers['Authorization']&.split(' ')&.last
    return unauthorized unless token

    begin
      decoded_token = JWT.decode(token, jwt_secret, true, algorithm: 'HS256')
      @current_user_id = decoded_token[0]['user_id']
    rescue JWT::DecodeError
      unauthorized
    end
  end

  def set_current_user
    @current_user = User.find(@current_user_id) if @current_user_id
  rescue Mongoid::Errors::DocumentNotFound
    unauthorized
  end

  def current_user
    @current_user
  end

  def require_admin
    unauthorized unless current_user&.admin?
  end

  def require_admin_or_moderator
    unauthorized unless current_user&.admin? || current_user&.role == 'moderator'
  end

  def jwt_secret
    ENV['JWT_SECRET'] || ENV['JWT_SECRET_KEY'] || ENV['SECRET_KEY_BASE'] || 'fallback_secret_for_development'
  end

  def generate_jwt_token(user)
    jwt_config = Rails.application.config.jwt
    payload = {
      sub: user.id.to_s,           # Standard JWT subject claim
      user_id: user.id.to_s,       # Keep for backward compatibility
      username: user.username,
      role: user.role,
      sessionId: SecureRandom.uuid, # Add session ID for socket gateway
      iat: Time.current.to_i,      # Issued at
      exp: 24.hours.from_now.to_i, # Expires at
      iss: jwt_config[:issuer],    # Use configured issuer
      aud: jwt_config[:audience].split(',').map(&:strip) # Use configured audience
    }
    JWT.encode(payload, jwt_secret, 'HS256')
  end

  # Error handlers
  def not_found(exception = nil)
    error_response(
      exception&.message || 'The requested resource could not be found',
      :not_found,
      nil,
      'NOT_FOUND'
    )
  end

  def unauthorized(message = 'Unauthorized access')
    error_response(message, :unauthorized, nil, 'UNAUTHORIZED')
  end

  def validation_error(exception)
    error_response(
      exception.message,
      :unprocessable_entity,
      exception.document&.errors&.full_messages,
      'VALIDATION_ERROR'
    )
  end

  def internal_server_error(exception)
    Rails.logger.error "Internal Server Error: #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")

    error_response(
      'An unexpected error occurred',
      :internal_server_error,
      nil,
      'INTERNAL_SERVER_ERROR'
    )
  end

  # Response helpers
  def success_response(data = {}, message = 'Success', status = :ok)
    render json: {
      success: true,
      message: message,
      data: data
    }, status: status
  end

  def error_response(message, status = :bad_request, details = nil, code = nil)
    # Format error as structured object for game service compatibility
    error_object = {
      message: message
    }

    # Add error code if provided, otherwise derive from status
    if code
      error_object[:code] = code
    else
      error_object[:code] = case status
                           when :unauthorized then 'UNAUTHORIZED'
                           when :not_found then 'NOT_FOUND'
                           when :unprocessable_entity then 'VALIDATION_ERROR'
                           when :forbidden then 'FORBIDDEN'
                           else 'GENERAL_ERROR'
                           end
    end

    response = {
      success: false,
      error: error_object
    }
    response[:details] = details if details

    render json: response, status: status
  end
end
