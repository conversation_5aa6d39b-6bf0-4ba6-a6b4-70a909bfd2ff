class GameService::UsersController < ApplicationController
  # Skip authentication for Game Service operations
  skip_before_action :authenticate_request
  skip_before_action :set_current_user

  before_action :set_user

  # POST /game_service/users/:id/validate_balance
  def validate_balance
    begin
      bet_amount = params[:bet_amount]&.to_i || 0

      Rails.logger.info "Game Service request: Validate balance - User: #{@user.id} - Username: #{@user.username} - Bet Amount: #{bet_amount} - Current Balance: #{@user.balance}"

      # Validate bet amount
      if bet_amount <= 0
        Rails.logger.warn "Invalid bet amount for balance validation - User: #{@user.id} - Bet Amount: #{bet_amount}"
        
        error_response(
          'Invalid bet amount',
          :bad_request,
          nil,
          'INVALID_BET_AMOUNT'
        )
        return
      end

      # Check if user has sufficient balance
      has_sufficient_balance = @user.balance >= bet_amount

      Rails.logger.info "Balance validation result - User: #{@user.id} - Username: #{@user.username} - Balance: #{@user.balance} - Bet Amount: #{bet_amount} - Sufficient: #{has_sufficient_balance}"

      success_response({
        user_id: @user.id.to_s,
        username: @user.username,
        current_balance: @user.balance,
        bet_amount: bet_amount,
        has_sufficient_balance: has_sufficient_balance
      }, 'Balance validation completed')

    rescue StandardError => e
      Rails.logger.error "Error validating balance for Game Service - User: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to validate balance',
        :internal_server_error,
        nil,
        'BALANCE_VALIDATION_ERROR'
      )
    end
  end

  # GET /game_service/users/:id/info
  def info
    begin
      Rails.logger.info "Game Service request: Get user info - User: #{@user.id} - Username: #{@user.username}"

      success_response({
        user_id: @user.id.to_s,
        username: @user.username,
        email: @user.email,
        balance: @user.balance,
        status: @user.status,
        role: @user.role,
        is_active: @user.status == 'active',
        created_at: @user.created_at,
        updated_at: @user.updated_at
      }, 'User info retrieved successfully')

    rescue StandardError => e
      Rails.logger.error "Error getting user info for Game Service - User: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to get user info',
        :internal_server_error,
        nil,
        'USER_INFO_ERROR'
      )
    end
  end

  # GET /game_service/users/:id/sessions
  def sessions
    begin
      Rails.logger.info "Game Service request: Get user sessions - User: #{@user.id} - Username: #{@user.username}"

      # For now, return empty sessions since we don't have a sessions model
      # In a real implementation, this would query the user's active game sessions
      success_response({
        user_id: @user.id.to_s,
        game_sessions: []
      }, 'User sessions retrieved successfully')

    rescue StandardError => e
      Rails.logger.error "Error getting user sessions for Game Service - User: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to get user sessions',
        :internal_server_error,
        nil,
        'USER_SESSIONS_ERROR'
      )
    end
  end

  # POST /game_service/users/:id/deduct_bet
  def deduct_bet
    begin
      bet_amount = params[:bet_amount]&.to_i || 0
      room_id = params[:room_id]

      Rails.logger.info "Game Service request: Deduct bet - User: #{@user.id} - Username: #{@user.username} - Bet Amount: #{bet_amount} - Room: #{room_id} - Current Balance: #{@user.balance}"

      # Validate bet amount
      if bet_amount <= 0
        Rails.logger.warn "Invalid bet amount for deduction - User: #{@user.id} - Bet Amount: #{bet_amount}"

        error_response(
          'Invalid bet amount',
          :bad_request,
          nil,
          'INVALID_BET_AMOUNT'
        )
        return
      end

      # Check if user has sufficient balance
      if @user.balance < bet_amount
        Rails.logger.warn "Insufficient balance for bet deduction - User: #{@user.id} - Balance: #{@user.balance} - Bet Amount: #{bet_amount}"

        error_response(
          'Insufficient balance for bet amount',
          :unprocessable_entity,
          nil,
          'INSUFFICIENT_BALANCE'
        )
        return
      end

      # Create bet transaction and deduct balance
      transaction_service = TransactionService.new

      # Create a mock game session for the transaction
      game_session = OpenStruct.new(
        id: room_id,
        game_type: 'prize_wheel',
        bet_amount: bet_amount
      )

      transaction = transaction_service.process_bet_placed(@user, game_session, bet_amount)

      Rails.logger.info "Bet deduction successful - User: #{@user.id} - Username: #{@user.username} - Bet Amount: #{bet_amount} - New Balance: #{@user.reload.balance} - Transaction: #{transaction.transaction_id}"

      success_response({
        user_id: @user.id.to_s,
        username: @user.username,
        bet_amount: bet_amount,
        previous_balance: @user.balance + bet_amount,
        new_balance: @user.balance,
        transaction_id: transaction.transaction_id
      }, 'Bet amount deducted successfully')

    rescue StandardError => e
      Rails.logger.error "Error deducting bet for Game Service - User: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to deduct bet amount',
        :internal_server_error,
        nil,
        'BET_DEDUCTION_ERROR'
      )
    end
  end

  # POST /game_service/users/:id/update_balance
  def update_balance
    begin
      amount = params[:amount]&.to_f || 0
      operation = params[:operation] || 'adjustment'
      description = params[:description] || "Balance #{operation}"

      Rails.logger.info "Game Service request: Update balance - User: #{@user.id} - Username: #{@user.username} - Amount: #{amount} - Operation: #{operation} - Current Balance: #{@user.balance}"

      # Validate amount
      if amount == 0
        Rails.logger.warn "Invalid amount for balance update - User: #{@user.id} - Amount: #{amount}"

        error_response(
          'Amount cannot be zero',
          :bad_request,
          nil,
          'INVALID_AMOUNT'
        )
        return
      end

      # Check for sufficient balance on debits
      if amount < 0 && (@user.balance + amount) < 0
        Rails.logger.warn "Insufficient balance for deduction - User: #{@user.id} - Balance: #{@user.balance} - Amount: #{amount}"

        error_response(
          'Insufficient balance',
          :unprocessable_entity,
          nil,
          'INSUFFICIENT_BALANCE'
        )
        return
      end

      # Update balance
      previous_balance = @user.balance
      @user.balance += amount
      @user.save!

      Rails.logger.info "Balance update successful - User: #{@user.id} - Username: #{@user.username} - Amount: #{amount} - Previous Balance: #{previous_balance} - New Balance: #{@user.balance}"

      success_response({
        user_id: @user.id.to_s,
        username: @user.username,
        amount: amount,
        operation: operation,
        previous_balance: previous_balance,
        new_balance: @user.balance,
        updated_at: Time.current
      }, 'Balance updated successfully')

    rescue StandardError => e
      Rails.logger.error "Error updating balance for Game Service - User: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to update balance',
        :internal_server_error,
        nil,
        'BALANCE_UPDATE_ERROR'
      )
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    Rails.logger.warn "User not found for Game Service operation - User ID: #{params[:id]}"

    error_response(
      'User not found',
      :not_found,
      nil,
      'USER_NOT_FOUND'
    )
  end
end
