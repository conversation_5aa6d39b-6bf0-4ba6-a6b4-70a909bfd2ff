class GameService::RoomsController < ApplicationController
  # Skip authentication for Game Service notifications
  skip_before_action :authenticate_request
  skip_before_action :set_current_user

  before_action :set_room

  # GET /game_service/rooms/:id
  def show
    begin
      Rails.logger.info "Game Service request: Get room info - Room: #{@room.id}"

      success_response({
        room: room_data(@room)
      }, 'Room info retrieved successfully')

    rescue StandardError => e
      Rails.logger.error "Error getting room info for Game Service - Room: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to get room info',
        :internal_server_error,
        nil,
        'ROOM_INFO_ERROR'
      )
    end
  end

  # POST /game_service/rooms/:id/join
  def join
    begin
      user_id = params[:user_id]
      username = params[:username]
      bet_amount = params[:bet_amount]&.to_f || 0

      Rails.logger.info "Game Service request: Join room - Room: #{@room.id} - User: #{user_id} - Username: #{username} - Bet: #{bet_amount}"

      # Basic validation
      if user_id.blank? || username.blank?
        error_response(
          'User ID and username are required',
          :bad_request,
          nil,
          'INVALID_PARAMETERS'
        )
        return
      end

      if bet_amount != @room.bet_amount.to_f
        error_response(
          'Bet amount does not match room requirement',
          :bad_request,
          nil,
          'INVALID_BET_AMOUNT'
        )
        return
      end

      # Check room capacity
      if @room.current_players >= @room.max_players
        error_response(
          'Room is full',
          :unprocessable_entity,
          nil,
          'ROOM_FULL'
        )
        return
      end

      # Check room status
      if @room.status == 'active'
        error_response(
          'Game already in progress',
          :unprocessable_entity,
          nil,
          'GAME_IN_PROGRESS'
        )
        return
      end

      # Find the user
      user = User.find(user_id)

      # Use the proper add_player! method to create game session and update counters
      session = @room.add_player!(user)

      # Calculate position based on current players
      position = @room.game_sessions.in(status: ['pending', 'active']).count

      success_response({
        room_id: @room.id.to_s,
        user_id: user_id,
        username: username,
        position: position,
        bet_amount: bet_amount,
        joined_at: session.created_at,
        session_id: session.session_id
      }, 'Successfully joined room')

    rescue StandardError => e
      Rails.logger.error "Error joining room for Game Service - Room: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to join room',
        :internal_server_error,
        nil,
        'ROOM_JOIN_ERROR'
      )
    end
  end

  # POST /game_service/rooms/:id/leave
  def leave
    begin
      user_id = params[:user_id]

      Rails.logger.info "Game Service request: Leave room - Room: #{@room.id} - User: #{user_id}"

      if user_id.blank?
        error_response(
          'User ID is required',
          :bad_request,
          nil,
          'INVALID_PARAMETERS'
        )
        return
      end

      # Find the user
      user = User.find(user_id)

      # Use the proper remove_player! method to remove game session and update counters
      removed = @room.remove_player!(user)

      if removed
        success_response({
          room_id: @room.id.to_s,
          user_id: user_id,
          left_at: Time.current
        }, 'Successfully left room')
      else
        error_response(
          'Player was not in room',
          :unprocessable_entity,
          nil,
          'PLAYER_NOT_IN_ROOM'
        )
      end

    rescue StandardError => e
      Rails.logger.error "Error leaving room for Game Service - Room: #{params[:id]} - Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      error_response(
        'Failed to leave room',
        :internal_server_error,
        nil,
        'ROOM_LEAVE_ERROR'
      )
    end
  end

  # POST /game_service/rooms/:id/player_joined
  def player_joined
    begin
      user_id = params[:user_id]
      username = params[:username]

      Rails.logger.info "Game Service notification: Player joined room - Room: #{@room.id} - User: #{user_id} - Username: #{username}"

      # Find the user
      user = User.find(user_id)
      
      # Check if player is already in room (reconnection case)
      existing_session = @room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
      
      if existing_session
        Rails.logger.info "Player already in room (reconnection) - Room: #{@room.id} - User: #{user_id} - Session: #{existing_session.id}"
        
        success_response({
          message: 'Player reconnected to room',
          room: room_data(@room),
          is_reconnection: true
        })
        return
      end

      # Add player to room
      @room.current_players += 1
      @room.save!

      # Create game session for the user
      @room.game_sessions.create!(
        user: user,
        game_type: @room.game_type,
        bet_amount: @room.bet_amount,
        status: 'pending'
      )

      Rails.logger.info "Player added to room via Game Service notification - Room: #{@room.id} - User: #{user_id} - Current players: #{@room.current_players}"

      # TODO: Publish notification when NotificationService is available
      # notification_service = NotificationService.instance
      # notification_service.publish_room_update(@room, 'player_joined', {
      #   user_id: user_id,
      #   username: username,
      #   current_players: @room.current_players
      # })

      success_response({
        message: 'Player added to room successfully',
        room: room_data(@room),
        is_reconnection: false
      })

    rescue Mongoid::Errors::DocumentNotFound => e
      Rails.logger.error "User not found for Game Service notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message}"
      error_response('User not found', :not_found)
    rescue StandardError => e
      Rails.logger.error "Failed to process Game Service player joined notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message} - Backtrace: #{e.backtrace.first(5).join(', ')}"
      error_response('Failed to add player to room', :internal_server_error)
    end
  end

  # POST /game_service/rooms/:id/player_left
  def player_left
    begin
      user_id = params[:user_id]
      reason = params[:reason] || 'Player left room'

      Rails.logger.info "Game Service notification: Player left room - Room: #{@room.id} - User: #{user_id} - Reason: #{reason}"

      # Find the user
      user = User.find(user_id)

      # Find the game session
      session = @room.game_sessions.where(user: user).in(status: ['pending', 'active']).first

      unless session
        Rails.logger.warn "Player not found in room for Game Service notification - Room: #{@room.id} - User: #{user_id}"

        success_response({
          message: 'Player was not in room',
          room: room_data(@room)
        })
        return
      end

      # Remove player from room
      @room.current_players -= 1
      @room.save!

      session.cancel!(reason)

      Rails.logger.info "Player removed from room via Game Service notification - Room: #{@room.id} - User: #{user_id} - Current players: #{@room.current_players} - Reason: #{reason}"

      # TODO: Publish notification when NotificationService is available
      # notification_service = NotificationService.instance
      # notification_service.publish_room_update(@room, 'player_left', {
      #   user_id: user_id,
      #   username: user.username,
      #   current_players: @room.current_players,
      #   reason: reason
      # })

      success_response({
        message: 'Player removed from room successfully',
        room: room_data(@room)
      })

    rescue Mongoid::Errors::DocumentNotFound => e
      Rails.logger.error "User not found for Game Service notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message}"
      error_response('User not found', :not_found)
    rescue StandardError => e
      Rails.logger.error "Failed to process Game Service player left notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message} - Backtrace: #{e.backtrace.first(5).join(', ')}"
      error_response('Failed to remove player from room', :internal_server_error)
    end
  end

  # POST /game_service/rooms/:id/player_kicked
  def player_kicked
    begin
      user_id = params[:user_id]
      reason = params[:reason] || 'Player kicked from room'

      Rails.logger.info "Game Service notification: Player kicked from room - Room: #{@room.id} - User: #{user_id} - Reason: #{reason}"

      # Find the user
      user = User.find(user_id)

      # Find the game session
      session = @room.game_sessions.where(user: user).in(status: ['pending', 'active']).first

      unless session
        Rails.logger.warn "Player not found in room for Game Service kick notification - Room: #{@room.id} - User: #{user_id}"

        success_response({
          message: 'Player was not in room',
          room: room_data(@room)
        })
        return
      end

      # Remove player from room
      @room.current_players -= 1
      @room.save!

      # Cancel session with kick reason
      session.cancel!("Kicked: #{reason}")

      Rails.logger.info "Player kicked from room via Game Service notification - Room: #{@room.id} - User: #{user_id} - Current players: #{@room.current_players} - Reason: #{reason}"

      # TODO: Publish notification when NotificationService is available
      # notification_service = NotificationService.instance
      # notification_service.publish_room_update(@room, 'player_kicked', {
      #   user_id: user_id,
      #   username: user.username,
      #   current_players: @room.current_players,
      #   reason: reason
      # })

      success_response({
        message: 'Player kicked from room successfully',
        room: room_data(@room),
        kick_reason: reason
      })

    rescue Mongoid::Errors::DocumentNotFound => e
      Rails.logger.error "User not found for Game Service kick notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message}"
      error_response('User not found', :not_found)
    rescue StandardError => e
      Rails.logger.error "Failed to process Game Service player kicked notification - Room: #{@room.id} - User: #{params[:user_id]} - Error: #{e.message} - Backtrace: #{e.backtrace.first(5).join(', ')}"
      error_response('Failed to kick player from room', :internal_server_error)
    end
  end

  private

  def set_room
    @room = Room.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    error_response('Room not found', :not_found)
  end

  def room_data(room)
    {
      id: room.id.to_s,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: room.current_players,
      max_players: room.max_players,
      bet_amount: room.bet_amount.to_s,
      currency: room.currency,
      is_private: room.is_private,
      created_at: room.created_at.iso8601,
      updated_at: room.updated_at.iso8601
    }
  end
end
