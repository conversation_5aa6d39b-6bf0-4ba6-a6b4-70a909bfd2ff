class RoomsController < ApplicationController
  # Skip authentication for index and show actions to allow socket gateway to fetch rooms
  skip_before_action :authenticate_request, only: [:index, :show]
  skip_before_action :set_current_user, only: [:index, :show]

  before_action :set_room, only: [:show, :join, :leave, :kick_player, :players]

  # GET /rooms
  def index
    begin
      # Build and execute room query
      rooms_result = build_and_execute_room_query

      # Return successful response
      success_response(rooms_result)
    rescue StandardError => e
      # Handle errors gracefully
      handle_rooms_index_error(e)
    end
  end

  private

  # Builds and executes room query with filters and pagination
  def build_and_execute_room_query
    # Log initial state
    log_rooms_index_request

    # Build query with filters
    query = build_filtered_room_query

    # Apply pagination and fetch results
    paginated_rooms = apply_pagination_to_query(query)

    # Transform and return results
    transform_rooms_response(paginated_rooms, query)
  end

  # Logs initial room index request information
  def log_rooms_index_request
    total_rooms_before_filter = Room.count
    Rails.logger.info "Rooms index request - Total rooms in database: #{total_rooms_before_filter}"
    Rails.logger.info "Rooms index request - Filters: #{params.to_unsafe_h.slice(:id, :game_type, :status, :has_space, :page, :per_page)}"
  end

  # Builds room query with applied filters
  def build_filtered_room_query
    query = Room.all

    # Apply individual filters
    query = apply_room_id_filter(query)
    query = apply_game_type_filter(query)
    query = apply_status_filter(query)
    query = apply_has_space_filter(query)

    # Log filtered count
    total_count = query.count
    Rails.logger.info "Rooms index request - Rooms after filtering: #{total_count}"

    query
  end

  # Applies room ID filter if provided
  def apply_room_id_filter(query)
    if params[:id].present?
      Rails.logger.info "Filtering by room ID: #{params[:id]}"
      query.where(id: params[:id])
    else
      query
    end
  end

  # Applies game type filter if provided
  def apply_game_type_filter(query)
    if params[:game_type].present?
      query.where(game_type: params[:game_type].upcase)
    else
      query
    end
  end

  # Applies status filter if provided
  def apply_status_filter(query)
    if params[:status].present?
      query.where(status: params[:status])
    else
      query
    end
  end

  # Applies has_space filter if provided
  def apply_has_space_filter(query)
    if params[:has_space] == 'true' || params[:has_space] == true
      # Use MongoDB aggregation to compare current_players < max_players
      query.where('$expr' => { '$lt' => ['$current_players', '$max_players'] })
    else
      query
    end
  end

  # Applies pagination to query and returns paginated results
  def apply_pagination_to_query(query)
    page = (params[:page] || 1).to_i
    per_page = (params[:per_page] || 25).to_i

    # Fetch rooms with pagination
    rooms = query.desc(:created_at)
                 .skip((page - 1) * per_page)
                 .limit(per_page)

    {
      rooms: rooms,
      page: page,
      per_page: per_page,
      total_count: query.count
    }
  end

  # Transforms rooms response to expected format
  def transform_rooms_response(paginated_rooms, query)
    # Fix data inconsistencies and transform rooms to API format
    rooms_data = paginated_rooms[:rooms].map do |room|
      # Fix any data inconsistency before serving room data
      consistency_check = room.check_data_consistency
      if !consistency_check[:consistent]
        Rails.logger.warn "Data inconsistency detected in room #{room.id} in list: #{consistency_check}"
        fix_result = room.fix_data_consistency!
        Rails.logger.info "Fixed room #{room.id} data inconsistency in list: #{fix_result}"
        room.reload
      end

      room_data(room)
    end

    Rails.logger.info "Rooms index request - Returning #{rooms_data.length} rooms"

    {
      rooms: rooms_data,
      pagination: {
        current_page: paginated_rooms[:page],
        per_page: paginated_rooms[:per_page],
        total_count: paginated_rooms[:total_count],
        total_pages: (paginated_rooms[:total_count].to_f / paginated_rooms[:per_page]).ceil
      }
    }
  end

  # Handles errors in rooms index gracefully
  def handle_rooms_index_error(error)
    Rails.logger.error "Failed to fetch rooms from Game Service: #{error.message}"
    Rails.logger.error "Error details: #{error.class.name} - #{error.message}"
    Rails.logger.error "Backtrace: #{error.backtrace.first(5).join("\n")}"

    # Return empty list if Game Service is unavailable
    success_response({
      rooms: [],
      pagination: {
        current_page: 1,
        per_page: 25,
        total_count: 0,
        total_pages: 0
      }
    })
  end

  public

  # GET /rooms/:id
  def show
    begin
      # Fix any data inconsistency before serving room data
      consistency_check = @room.check_data_consistency
      if !consistency_check[:consistent]
        Rails.logger.warn "Data inconsistency detected in room #{@room.id} before serving data: #{consistency_check}"
        fix_result = @room.fix_data_consistency!
        Rails.logger.info "Fixed room #{@room.id} data inconsistency: #{fix_result}"
        @room.reload
      end

      # Get base room data from local database (includes players)
      base_room_data = room_data(@room)

      # Try to get additional status from Game Service for real-time updates
      begin
        game_client = GameServiceClient.instance
        grpc_response = game_client.get_room_status(@room.external_room_id)

        # Merge Game Service status with local data, keeping local players data
        if grpc_response && grpc_response['room']
          game_room = grpc_response['room']
          # Update status and other real-time fields from Game Service
          base_room_data[:status] = game_room['status'] if game_room['status']
          # Don't override current_players - use local data which is more accurate
          # base_room_data[:current_players] = game_room['current_players'] if game_room['current_players']
          base_room_data[:prize_pool] = game_room['prize_pool'] if game_room['prize_pool']
        end
      rescue StandardError => game_service_error
        Rails.logger.warn "Game Service unavailable, using local data: #{game_service_error.message}"
        # Continue with local data only
      end

      success_response({
        room: base_room_data
      })
    rescue StandardError => e
      Rails.logger.error "Failed to fetch room details: #{e.message}"
      error_response('Room not found or unavailable', :not_found)
    end
  end

  # POST /rooms/:id/join
  def join
    begin
      # Validate user can join
      unless @current_user.balance >= @room.bet_amount
        return error_response('Insufficient balance', :unprocessable_entity, nil, 'INSUFFICIENT_BALANCE')
      end

      # Check if room has space
      if @room.current_players >= @room.max_players
        return error_response('Room is full', :unprocessable_entity, nil, 'ROOM_FULL')
      end

      # Check password for private rooms
      if @room.is_private && !@room.valid_password?(params[:password])
        return error_response('Invalid room password', :unauthorized, nil, 'INVALID_PASSWORD')
      end

      # Add player to room (handles reconnection automatically)
      result = @room.add_player!(@current_user)
      is_reconnection = result.is_a?(GameSession) # If it returns a session, it's a reconnection

      success_response({
        room: room_data(@room),
        player: {
          id: @current_user.id.to_s,
          username: @current_user.username,
          balance: @current_user.balance,
          is_ready: is_reconnection ? (result.metadata['is_ready'] || false) : false
        },
        is_reconnection: is_reconnection
      }, is_reconnection ? 'Successfully reconnected to room' : 'Successfully joined room')
    rescue ArgumentError => e
      # Map specific ArgumentError messages to appropriate error codes
      error_code = case e.message
                   when /insufficient balance/i then 'INSUFFICIENT_BALANCE'
                   when /room is full/i then 'ROOM_FULL'
                   when /invalid.*password/i then 'INVALID_PASSWORD'
                   when /not accepting players/i then 'ROOM_NOT_ACCEPTING_PLAYERS'
                   when /user.*not.*active/i then 'USER_NOT_ACTIVE'
                   else 'VALIDATION_ERROR'
                   end
      error_response(e.message, :unprocessable_entity, nil, error_code)
    rescue StandardError => e
      Rails.logger.error "Room join failed: #{e.message}"
      error_response('Failed to join room', :unprocessable_entity, nil, 'JOIN_ROOM_FAILED')
    end
  end

  # POST /rooms/:id/leave
  def leave
    begin
      Rails.logger.info "Enhanced leave room request received - Room: #{@room.id} - User: #{@current_user.id} - Username: #{@current_user.username} - Status: #{@room.status} - Players: #{@room.current_players}"

      # Check if player is in ready state
      session = @room.game_sessions.where(user: @current_user).in(status: ['pending', 'active']).first
      if session && session.metadata['is_ready']
        Rails.logger.warn "Player attempted to leave while ready - Room: #{@room.id} - User: #{@current_user.id}"
        error_response('Cannot leave room while in ready state. Please unready first.', :unprocessable_entity, nil, 'CANNOT_LEAVE_WHILE_READY')
        return
      end

      # Use enhanced room management service to remove player
      room_service = RoomManagementService.instance
      result = room_service.enhanced_remove_player_from_room(@room, @current_user, reason: 'voluntary')

      unless result
        Rails.logger.warn "Player not found in room - Room: #{@room.id} - User: #{@current_user.id}"
        error_response('Player not found in room', :unprocessable_entity, nil, 'PLAYER_NOT_IN_ROOM')
        return
      end

      # Reload room to get updated data
      @room.reload

      Rails.logger.info "Enhanced leave room successful - Room: #{@room.id} - User: #{@current_user.id} - New count: #{@room.current_players}"

      success_response({
        room: room_data(@room),
        player: {
          id: @current_user.id.to_s,
          username: @current_user.username,
          balance: @current_user.balance
        },
        enhanced: true
      }, 'Successfully left room with enhanced cleanup')
    rescue ArgumentError => e
      Rails.logger.error "ArgumentError in enhanced leave room: #{e.message} - Room: #{@room.id} - User: #{@current_user.id} - Backtrace: #{e.backtrace.first(3).join(', ')}"

      # Map specific ArgumentError messages to appropriate error codes
      error_code = case e.message
                   when /player not found/i then 'PLAYER_NOT_IN_ROOM'
                   when /room not found/i then 'ROOM_NOT_FOUND'
                   else 'VALIDATION_ERROR'
                   end
      error_response(e.message, :unprocessable_entity, nil, error_code)
    rescue StandardError => e
      Rails.logger.error "StandardError in leave room: #{e.message} - Room: #{@room.id} - User: #{@current_user.id} - Class: #{e.class.name} - Backtrace: #{e.backtrace.first(10).join(', ')}"
      error_response('Failed to leave room', :unprocessable_entity, nil, 'LEAVE_ROOM_FAILED')
    end
  end

  # PUT /rooms/:id/players/:user_id/ready
  def set_player_ready
    begin
      user_id = params[:user_id]
      is_ready = params[:is_ready]

      # Find the user
      user = User.find(user_id)

      # Use room management service to set player ready status
      room_service = RoomManagementService.instance
      session = room_service.set_player_ready(@room, user, is_ready)

      success_response({
        room: room_data(@room),
        player: {
          id: user.id.to_s,
          username: user.username,
          is_ready: is_ready
        },
        room_state: {
          ready_count: @room.ready_players_count,
          total_players: @room.current_players,
          prize_pool: @room.prize_pool,
          can_start_game: @room.ready_players_count == @room.current_players && @room.current_players >= 2
        }
      }, 'Player ready status updated successfully')
    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found, nil, 'USER_NOT_FOUND')
    rescue ArgumentError => e
      # Map specific ArgumentError messages to appropriate error codes
      error_code = case e.message
                   when /player not found/i then 'PLAYER_NOT_IN_ROOM'
                   when /room not found/i then 'ROOM_NOT_FOUND'
                   else 'VALIDATION_ERROR'
                   end
      error_response(e.message, :unprocessable_entity, nil, error_code)
    rescue StandardError => e
      Rails.logger.error "Set player ready failed: #{e.message}"
      error_response('Failed to set player ready status', :unprocessable_entity, nil, 'SET_PLAYER_READY_FAILED')
    end
  end

  # DELETE /rooms/:id/players/:user_id (Kick player)
  def kick_player
    begin
      user_id = params[:user_id]
      reason = params[:reason] || 'Kicked by admin'

      # Validate that the current user has permission to kick (room creator or admin)
      unless can_kick_player?(@room, @current_user)
        return error_response('Insufficient permissions to kick player', :forbidden, nil, 'INSUFFICIENT_PERMISSIONS')
      end

      # Fix any data inconsistency before attempting kick
      consistency_check = @room.check_data_consistency
      if !consistency_check[:consistent]
        Rails.logger.warn "Data inconsistency detected in room #{@room.id} before kick: #{consistency_check}"
        fix_result = @room.fix_data_consistency!
        Rails.logger.info "Fixed room #{@room.id} data inconsistency: #{fix_result}"
        @room.reload
      end

      # Find the user to kick
      user_to_kick = User.find(user_id)

      # Prevent self-kick
      if user_to_kick.id == @current_user.id
        return error_response('Cannot kick yourself', :unprocessable_entity, nil, 'CANNOT_KICK_SELF')
      end

      # Verify user is actually in the room
      active_session = @room.game_sessions.where(user: user_to_kick).in(status: ['pending', 'active']).first
      unless active_session
        return error_response('Player is not currently in this room', :unprocessable_entity, nil, 'PLAYER_NOT_IN_ROOM')
      end

      # Kick the player
      @room.kick_player!(user_id, @current_user.id.to_s, reason)

      # Use room management service for proper cleanup
      room_service = RoomManagementService.instance
      room_service.enhanced_remove_player_from_room(@room, user_to_kick, reason: "kicked: #{reason}")

      # Reload room to get updated data
      @room.reload

      Rails.logger.info "Player kicked successfully - Room: #{@room.id} - Kicked: #{user_id} - By: #{@current_user.id} - Reason: #{reason}"

      success_response({
        room: room_data(@room),
        kicked_player: {
          id: user_to_kick.id.to_s,
          username: user_to_kick.username
        },
        kicked_by: {
          id: @current_user.id.to_s,
          username: @current_user.username
        },
        reason: reason
      }, 'Player kicked successfully')
    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found, nil, 'USER_NOT_FOUND')
    rescue ArgumentError => e
      # Map specific ArgumentError messages to appropriate error codes
      error_code = case e.message
                   when /player not found/i then 'PLAYER_NOT_IN_ROOM'
                   when /room not found/i then 'ROOM_NOT_FOUND'
                   when /insufficient permissions/i then 'INSUFFICIENT_PERMISSIONS'
                   else 'VALIDATION_ERROR'
                   end
      error_response(e.message, :unprocessable_entity, nil, error_code)
    rescue StandardError => e
      Rails.logger.error "Kick player failed: #{e.message}"
      error_response('Failed to kick player', :unprocessable_entity, nil, 'KICK_PLAYER_FAILED')
    end
  end

  # GET /rooms/:id/players
  def players
    begin
      players_list = @room.current_players_list

      success_response({
        room_id: @room.id.to_s,
        room_name: @room.name,
        current_players: @room.current_players,
        max_players: @room.max_players,
        players: players_list,
        ready_players_count: @room.ready_players_count
      })
    rescue StandardError => e
      Rails.logger.error "Failed to fetch room players: #{e.message}"
      error_response('Failed to fetch room players', :unprocessable_entity, nil, 'FETCH_PLAYERS_FAILED')
    end
  end

  private

  def set_room
    @room = Room.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    error_response('Room not found', :not_found)
  end

  def can_kick_player?(room, user)
    # Room creator can kick players
    return true if room.creator_id == user.id

    # Admin users can kick players (assuming there's an admin role)
    return true if user.respond_to?(:admin?) && user.admin?

    # Add other permission logic as needed
    false
  end

  def transform_room_document(room_doc)
    # Transform MongoDB document to expected API format
    {
      id: room_doc['_id'].to_s,
      external_room_id: room_doc['_id'].to_s,
      name: room_doc['name'],
      game_type: room_doc['game_type'],
      status: room_doc['status'],
      max_players: room_doc['max_players'],
      current_players: room_doc['current_players'] || 0,
      bet_amount: room_doc.dig('configuration', 'bet_limits', 'min_bet') || 100,
      currency: room_doc.dig('configuration', 'bet_limits', 'currency') || 'USD',
      prize_pool: 0.0, # Default prize pool
      is_private: room_doc.dig('configuration', 'is_private') || false,
      has_password: room_doc.dig('configuration', 'password').present?,
      creator_id: room_doc['created_by'],
      created_at: room_doc['created_at']&.iso8601,
      updated_at: room_doc['updated_at']&.iso8601
    }
  end

  def transform_room_data(grpc_room)
    {
      id: grpc_room['id'],
      external_room_id: grpc_room['id'],
      name: grpc_room['name'] || grpc_room['room_name'],
      game_type: grpc_room['game_type'],
      status: grpc_room['status'],
      max_players: grpc_room['max_players'] || grpc_room.dig('config', 'max_players'),
      current_players: grpc_room['current_players'] || grpc_room['player_count'],
      bet_amount: grpc_room['bet_amount'] || grpc_room.dig('config', 'bet_amount'),
      currency: grpc_room['currency'] || grpc_room.dig('config', 'currency') || 'USD',
      prize_pool: grpc_room['prize_pool'] || 0.0,
      is_private: grpc_room['is_private'] || false,
      has_password: grpc_room['has_password'] || false,
      creator_id: grpc_room['creator_id'],
      created_at: grpc_room['created_at'],
      updated_at: grpc_room['updated_at']
    }
  end

  def room_data(room)
    # Get players list with consistency check
    players_list = room.current_players_list

    # Check for data consistency - if current_players > 0 but players list is empty,
    # there might be a race condition. Try to reload and fetch again.
    if room.current_players > 0 && players_list.empty?
      Rails.logger.warn "Data inconsistency detected: current_players=#{room.current_players} but players list is empty. Reloading room."
      room.reload
      players_list = room.current_players_list

      if players_list.empty?
        Rails.logger.error "Players list still empty after reload for room #{room.id}. Current players: #{room.current_players}"
      else
        Rails.logger.info "Players list recovered after reload for room #{room.id}. Found #{players_list.length} players."
      end
    end

    {
      id: room.id.to_s,
      external_room_id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      max_players: room.max_players,
      current_players: room.current_players,
      bet_amount: room.bet_amount.to_f, # Return as float for consistency
      currency: room.currency,
      prize_pool: room.prize_pool,
      is_private: room.is_private,
      has_password: room.password_hash.present?,
      creator_id: room.creator_id.to_s,
      creator_username: room.creator&.username,
      created_at: room.created_at,
      updated_at: room.updated_at,
      players: players_list,
      ready_players_count: room.ready_players_count,
      # Additional fields for compatibility
      creator: {
        id: room.creator_id.to_s,
        username: room.creator&.username
      },
      configuration: {
        game_duration: 300,
        created_by_service: 'manager-service',
        created_at: room.created_at
      },
      game_sessions: room.game_sessions.in(status: ['pending', 'active']).map do |session|
        {
          id: session.id.to_s,
          user_id: session.user_id.to_s,
          username: session.user&.username,
          status: session.status,
          bet_amount: session.bet_amount.to_f,
          created_at: session.created_at
        }
      end,
      statistics: {
        total_games_played: room.game_sessions.where(status: 'completed').count,
        total_prize_pool: room.prize_pool,
        average_players: room.current_players,
        last_game_at: room.game_sessions.where(status: 'completed').maximum(:updated_at)
      },
      sync_status: {
        sync_required: false,
        last_synced_at: room.created_at
      }
    }
  end
end
