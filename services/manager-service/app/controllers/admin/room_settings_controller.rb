class Admin::RoomSettingsController < ApplicationController
  before_action :require_admin
  before_action :set_room

  # GET /admin/room-settings/:room_id
  def show
    current_players = @room.game_sessions.where(status: ['pending', 'active']).includes(:user).map do |session|
      {
        user_id: session.user_id.to_s,
        username: session.user.username,
        position: session.metadata['position'] || 0,
        bet_amount: session.bet_amount.to_f,
        joined_at: session.created_at.iso8601
      }
    end

    game_session_info = nil
    if @room.playing?
      active_session = @room.game_sessions.where(status: 'active').first
      if active_session
        game_session_info = {
          session_id: active_session.session_id,
          started_at: active_session.started_at&.iso8601,
          estimated_end_at: estimate_end_time(active_session)
        }
      end
    end

    statistics = calculate_room_statistics(@room)

    success_response({
      room: {
        id: @room.id.to_s,
        external_room_id: @room.external_room_id,
        name: @room.name,
        game_type: @room.game_type,
        status: @room.status,
        current_players: current_players,
        configuration: {
          max_players: @room.max_players,
          bet_amount: @room.bet_amount.to_f,
          currency: @room.currency,
          game_duration: @room.configuration['game_duration'] || 30,
          game_specific_config: @room.game_specific_config
        },
        game_session: game_session_info,
        statistics: statistics
      }
    })
  end

  # PUT /admin/room-settings/:room_id
  def update
    update_params = room_settings_params
    apply_immediately = params[:apply_immediately] == true
    notify_players = params[:notify_players] != false

    # Validate that room can be updated
    if @room.playing? && apply_immediately
      return error_response('Cannot apply changes immediately to a room in progress', :unprocessable_entity)
    end

    settings_updated = []
    players_notified = 0

    begin
      # Update room configuration
      if update_params[:max_players].present?
        @room.max_players = update_params[:max_players]
        settings_updated << 'max_players'
      end

      if update_params[:bet_amount].present?
        @room.bet_amount = update_params[:bet_amount]
        settings_updated << 'bet_amount'
      end

      if update_params[:game_duration].present?
        @room.configuration['game_duration'] = update_params[:game_duration]
        settings_updated << 'game_duration'
      end

      if update_params[:game_specific_config].present?
        @room.game_specific_config.merge!(update_params[:game_specific_config])
        settings_updated << 'game_specific_config'
      end

      @room.sync_required = true
      @room.save!

      # Notify players if requested
      if notify_players && @room.current_players > 0
        player_ids = @room.game_sessions.where(status: ['pending', 'active']).pluck(:user_id)
        NotificationJob.perform_async(
          'room_settings_updated',
          player_ids,
          {
            room_name: @room.name,
            room_id: @room.id.to_s,
            settings_updated: settings_updated,
            apply_immediately: apply_immediately
          }
        )
        players_notified = player_ids.count
      end

      # Sync with Game Service if applying immediately
      if apply_immediately
        RoomSettingsSyncJob.perform_async(@room.id)
      end
    rescue StandardError => e
      Rails.logger.error "Error updating room settings: #{e.message}"
      raise e
    end

    success_response({
      room_id: @room.id.to_s,
      settings_updated: settings_updated,
      applied_immediately: apply_immediately,
      next_game_affected: !apply_immediately,
      players_notified: players_notified
    }, 'Room settings updated successfully')
  rescue Mongoid::Errors::Validations => e
    validation_error(e)
  rescue StandardError => e
    Rails.logger.error "Room settings update failed: #{e.message}"
    error_response('Failed to update room settings', :unprocessable_entity)
  end

  # POST /admin/room-settings/:room_id/force-start
  def force_start
    unless @room.waiting?
      return error_response('Room is not in waiting state', :unprocessable_entity)
    end

    if @room.current_players < 2
      return error_response('Need at least 2 players to start game', :unprocessable_entity)
    end

    begin
      @room.start_game!

      success_response({
        room_id: @room.id.to_s,
        status: @room.status,
        players_count: @room.current_players
      }, 'Game started successfully')
    rescue StandardError => e
      Rails.logger.error "Failed to force start game: #{e.message}"
      error_response('Failed to start game', :unprocessable_entity)
    end
  end

  # POST /admin/room-settings/:room_id/force-end
  def force_end
    unless @room.playing?
      return error_response('Room is not in playing state', :unprocessable_entity)
    end

    reason = params[:reason] || 'Administrative intervention'
    refund_players = params[:refund_players] != false

    begin
      if refund_players
        @room.cancel_game!(reason)
        message = 'Game ended and players refunded'
      else
        # Force complete with no winners
        @room.finish_game!({
          forced_end: true,
          reason: reason,
          players: @room.game_sessions.where(status: 'active').map do |session|
            {
              user_id: session.user_id.to_s,
              win_amount: 0,
              position: 0
            }
          end
        })
        message = 'Game ended without refunds'
      end

      success_response({
        room_id: @room.id.to_s,
        status: @room.status,
        refunded: refund_players,
        reason: reason
      }, message)
    rescue StandardError => e
      Rails.logger.error "Failed to force end game: #{e.message}"
      error_response('Failed to end game', :unprocessable_entity)
    end
  end

  # POST /admin/room-settings/:room_id/kick-player
  def kick_player
    unless @room
      return error_response('Room not found', :not_found)
    end

    user_id = params[:user_id]
    reason = params[:reason] || 'Kicked by administrator'
    notify_player = params[:notify_player] != false

    unless user_id.present?
      return error_response('User ID is required', :bad_request)
    end

    begin
      user = User.find(user_id)

      # Log the kick action for audit purposes
      Rails.logger.info "Admin kick action: User #{current_user.username} (#{current_user.id}) kicking user #{user.username} (#{user_id}) from room #{@room.name} (#{@room.id}). Reason: #{reason}"

      # Use the dedicated room management service for kicking
      room_service = RoomManagementService.instance
      kick_result = room_service.kick_player_from_room(@room, user, reason: reason, kicked_by_user: current_user)

      success_response({
        room_id: kick_result[:room][:id],
        kicked_user_id: kick_result[:kicked_user][:id],
        kicked_username: kick_result[:kicked_user][:username],
        reason: kick_result[:reason],
        current_players: kick_result[:room][:current_players],
        refund_amount: kick_result[:refund_amount],
        kicked_by: kick_result[:kicked_by],
        notification_sent: notify_player
      }, 'Player kicked successfully')
    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found)
    rescue StandardError => e
      Rails.logger.error "Failed to kick player: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      error_response('Failed to kick player', :unprocessable_entity)
    end
  end

  # GET /admin/room-settings/:room_id/sync-status
  def sync_status
    success_response({
      room_id: @room.id.to_s,
      sync_required: @room.sync_required,
      last_synced_at: @room.last_synced_at&.iso8601,
      external_room_id: @room.external_room_id
    })
  end

  # POST /admin/room-settings/:room_id/sync
  def sync_now
    begin
      RoomSettingsSyncJob.perform_async(@room.id)

      success_response({
        room_id: @room.id.to_s,
        sync_queued: true
      }, 'Room sync queued successfully')
    rescue StandardError => e
      Rails.logger.error "Failed to queue room sync: #{e.message}"
      error_response('Failed to queue room sync', :unprocessable_entity)
    end
  end

  private

  def set_room
    room_id = params[:id] || params[:room_id]
    @room = Room.find(room_id) if room_id.present?
  rescue Mongoid::Errors::DocumentNotFound
    @room = nil
    Rails.logger.warn "Room not found: #{room_id}"
  end

  def room_settings_params
    params.permit(
      :max_players, :bet_amount, :game_duration,
      game_specific_config: {}
    )
  end

  def estimate_end_time(session)
    return nil unless session.started_at

    game_duration = @room.configuration['game_duration'] || 30
    (session.started_at + game_duration.seconds).iso8601
  end

  def calculate_room_statistics(room)
    finished_sessions = room.game_sessions.completed

    {
      total_games_played: finished_sessions.count,
      total_prize_pool: finished_sessions.sum(:bet_amount).to_f,
      average_players: finished_sessions.count > 0 ?
        (finished_sessions.count.to_f / room.game_sessions.distinct(:session_id).count) : 0,
      last_game_at: finished_sessions.maximum(:ended_at)&.iso8601
    }
  end
end
