class Admin::GameSettingsController < ApplicationController
  before_action :require_admin

  # GET /admin/game-settings
  def index
    game_type = params[:game_type]
    
    if game_type.present?
      # Get settings for specific game type
      settings = GameSetting.get_game_config(game_type)
      success_response({
        game_settings: { game_type => settings }
      })
    else
      # Get all game settings
      prizewheel_settings = GameSetting.get_game_config('prizewheel')
      amidakuji_settings = GameSetting.get_game_config('amidakuji')
      global_settings = GameSetting.get_game_config('global')
      
      success_response({
        game_settings: {
          prizewheel: prizewheel_settings,
          amidakuji: amidakuji_settings
        },
        global_settings: global_settings
      })
    end
  end

  # POST /admin/game-settings
  def create
    game_type = settings_params[:game_type]
    settings_hash = settings_params[:settings]
    sync_to_game_service = settings_params[:sync_to_game_service] != false

    unless %w[prizewheel amidakuji global].include?(game_type)
      return error_response('Invalid game type', :bad_request)
    end

    updated_settings = []
    
    GameSetting.transaction do
      settings_hash.each do |key, value|
        setting = GameSetting.set_setting(game_type, key.to_s, value)
        updated_settings << key.to_s
      end
    end

    # Sync with Game Service if requested
    sync_status = 'skipped'
    if sync_to_game_service
      begin
        GameSettingsSyncJob.perform_async(game_type)
        sync_status = 'queued'
      rescue StandardError => e
        Rails.logger.error "Failed to queue game settings sync: #{e.message}"
        sync_status = 'failed'
      end
    end

    success_response({
      game_type: game_type,
      settings_updated: updated_settings,
      sync_status: sync_status
    }, 'Game settings updated successfully')
  rescue StandardError => e
    Rails.logger.error "Game settings update failed: #{e.message}"
    error_response('Failed to update game settings', :unprocessable_entity)
  end

  # PUT /admin/game-settings/global
  def update_global
    global_params = params.permit(
      :max_concurrent_games, :maintenance_mode,
      game_types_enabled: {}
    )

    updated_settings = []
    
    GameSetting.transaction do
      global_params.each do |key, value|
        next if key == 'game_types_enabled'
        
        GameSetting.set_setting('global', key.to_s, value)
        updated_settings << key.to_s
      end

      # Handle game_types_enabled separately
      if global_params[:game_types_enabled].present?
        global_params[:game_types_enabled].each do |game_type, enabled|
          GameSetting.set_setting('global', "#{game_type}_enabled", enabled)
          updated_settings << "#{game_type}_enabled"
        end
      end
    end

    # Sync with Game Service
    begin
      GameSettingsSyncJob.perform_async('global')
      sync_status = 'queued'
    rescue StandardError => e
      Rails.logger.error "Failed to queue global settings sync: #{e.message}"
      sync_status = 'failed'
    end

    success_response({
      settings_updated: updated_settings,
      sync_status: sync_status
    }, 'Global settings updated successfully')
  rescue StandardError => e
    Rails.logger.error "Global settings update failed: #{e.message}"
    error_response('Failed to update global settings', :unprocessable_entity)
  end

  # GET /admin/game-settings/sync-status
  def sync_status
    pending_syncs = GameSetting.needs_sync.group(:game_type).count
    last_syncs = GameSetting.group(:game_type).maximum(:last_synced_at)

    success_response({
      pending_syncs: pending_syncs,
      last_sync_times: last_syncs.transform_values { |time| time&.iso8601 },
      total_pending: GameSetting.needs_sync.count
    })
  end

  # POST /admin/game-settings/sync
  def sync_all
    game_type = params[:game_type]
    
    if game_type.present?
      # Sync specific game type
      unless %w[prizewheel amidakuji global].include?(game_type)
        return error_response('Invalid game type', :bad_request)
      end
      
      GameSettingsSyncJob.perform_async(game_type)
      message = "Sync queued for #{game_type} settings"
    else
      # Sync all pending settings
      GameSetting.sync_all_pending
      message = 'Sync queued for all pending settings'
    end

    success_response({
      sync_queued: true,
      game_type: game_type
    }, message)
  rescue StandardError => e
    Rails.logger.error "Failed to queue settings sync: #{e.message}"
    error_response('Failed to queue settings sync', :unprocessable_entity)
  end

  # GET /admin/game-settings/defaults
  def defaults
    defaults = {
      prizewheel: {
        min_players: 2,
        max_players: 8,
        min_bet: 10.0,
        max_bet: 1000.0,
        default_sections: 8,
        spin_duration_range: [3000, 8000],
        house_edge: 0.05,
        payout_structure: {
          "1" => 7.0,
          "2" => 0.0,
          "3" => 0.0,
          "4" => 0.0,
          "5" => 0.0,
          "6" => 0.0,
          "7" => 0.0,
          "8" => 0.0
        },
        timeouts: {
          waiting_timeout: 300,
          playing_timeout: 60,
          idle_timeout: 1800
        }
      },
      amidakuji: {
        min_players: 2,
        max_players: 8,
        min_bet: 10.0,
        max_bet: 1000.0,
        rows_per_player: 3,
        line_probability: 0.5,
        animation_duration: 5000,
        house_edge: 0.05,
        timeouts: {
          waiting_timeout: 300,
          playing_timeout: 90,
          idle_timeout: 1800
        }
      },
      global: {
        max_concurrent_games: 1000,
        maintenance_mode: false,
        game_types_enabled: {
          prizewheel: true,
          amidakuji: true
        }
      }
    }

    success_response({
      default_settings: defaults
    })
  end

  # POST /admin/game-settings/reset
  def reset_to_defaults
    game_type = params[:game_type]
    
    unless %w[prizewheel amidakuji global].include?(game_type)
      return error_response('Invalid game type', :bad_request)
    end

    # Deactivate current settings
    GameSetting.by_game_type(game_type).update_all(is_active: false)
    
    # Seed default settings for the game type
    case game_type
    when 'prizewheel'
      seed_prizewheel_defaults
    when 'amidakuji'
      seed_amidakuji_defaults
    when 'global'
      seed_global_defaults
    end

    # Sync with Game Service
    GameSettingsSyncJob.perform_async(game_type)

    success_response({
      game_type: game_type,
      reset_completed: true,
      sync_queued: true
    }, "#{game_type.capitalize} settings reset to defaults")
  rescue StandardError => e
    Rails.logger.error "Failed to reset settings: #{e.message}"
    error_response('Failed to reset settings', :unprocessable_entity)
  end

  private

  def settings_params
    params.permit(:game_type, :sync_to_game_service, settings: {})
  end

  def seed_prizewheel_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'default_sections' => 8,
      'spin_duration_min' => 3000,
      'spin_duration_max' => 8000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 60,
      'idle_timeout' => 1800
    }
    defaults.each { |k, v| GameSetting.set_setting('prizewheel', k, v) }
  end

  def seed_amidakuji_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'rows_per_player' => 3,
      'line_probability' => 0.5,
      'animation_duration' => 5000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 90,
      'idle_timeout' => 1800
    }
    defaults.each { |k, v| GameSetting.set_setting('amidakuji', k, v) }
  end

  def seed_global_defaults
    defaults = {
      'max_concurrent_games' => 1000,
      'maintenance_mode' => false,
      'prizewheel_enabled' => true,
      'amidakuji_enabled' => true
    }
    defaults.each { |k, v| GameSetting.set_setting('global', k, v) }
  end
end
