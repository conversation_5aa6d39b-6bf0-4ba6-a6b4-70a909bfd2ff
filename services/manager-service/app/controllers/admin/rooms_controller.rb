class Admin::RoomsController < ApplicationController
  before_action :require_admin_or_moderator
  before_action :set_room, only: [:show, :update, :destroy, :details, :players]

  # GET /admin/rooms
  def index
    @rooms = Room.all

    # Apply filters
    @rooms = @rooms.by_game_type(params[:game_type]) if params[:game_type].present?
    @rooms = @rooms.where(status: params[:status]) if params[:status].present?
    @rooms = @rooms.where(creator_id: params[:creator_id]) if params[:creator_id].present?

    # Date range filters
    if params[:created_after].present?
      created_after = DateTime.parse(params[:created_after])
      @rooms = @rooms.where(:created_at.gte => created_after)
    end

    if params[:created_before].present?
      created_before = DateTime.parse(params[:created_before])
      @rooms = @rooms.where(:created_at.lte => created_before)
    end

    # Pagination
    @rooms = @rooms.order(created_at: :desc).page(params[:page]).per(params[:per_page] || 25)

    # Calculate stats
    stats = calculate_room_stats

    success_response({
      rooms: @rooms.map { |room| room_data(room) },
      pagination: pagination_data(@rooms),
      stats: stats
    })
  rescue DateTime::Error
    error_response('Invalid date format. Use ISO 8601 format.', :bad_request)
  end

  # GET /admin/rooms/:id
  def show
    begin
      # Get enhanced room data with seat details
      seat_management_service = EnhancedSeatManagementService.new
      enhanced_room_result = seat_management_service.get_room_with_seat_details(@room.id)

      if enhanced_room_result[:success]
        success_response({
          room: enhanced_room_result[:room]
        })
      else
        error_response('Failed to get room details', :internal_server_error, enhanced_room_result[:error])
      end
    rescue StandardError => e
      Rails.logger.error "Failed to get enhanced room details: #{e.message}"
      # Fallback to basic room data
      success_response({
        room: detailed_room_data(@room)
      })
    end
  end

  # GET /admin/rooms/:id/details
  def details
    unless @room
      return error_response('Room not found', :not_found)
    end

    success_response({
      room: detailed_room_data_with_players(@room)
    })
  rescue StandardError => e
    Rails.logger.error "Failed to get room details: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    error_response('Failed to get room details', :internal_server_error)
  end

  # GET /admin/rooms/:id/players
  def players
    unless @room
      return error_response('Room not found', :not_found)
    end

    # Get unique players by grouping sessions by user_id
    player_sessions = @room.game_sessions.includes(:user).in(status: ['pending', 'active'])
    players_by_user = player_sessions.group_by(&:user_id)

    players = players_by_user.map do |user_id, sessions|
      # Use the most recent session for player info
      latest_session = sessions.max_by(&:created_at)
      {
        user_id: user_id.to_s,
        username: latest_session.user&.username || 'Unknown',
        bet_amount: latest_session.bet_amount.to_f,
        position: latest_session.metadata&.dig('position'),
        is_ready: latest_session.metadata&.dig('is_ready') || false,
        joined_at: latest_session.created_at.iso8601,
        status: latest_session.status,
        session_count: sessions.count
      }
    end

    success_response({
      players: players
    })
  rescue StandardError => e
    Rails.logger.error "Failed to get room players: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    error_response('Failed to get room players', :internal_server_error)
  end

  # POST /admin/rooms/:id/fix_data_consistency
  def fix_data_consistency
    unless @room
      return error_response('Room not found', :not_found)
    end

    begin
      # Check current consistency
      consistency_check = @room.check_data_consistency

      if consistency_check[:consistent]
        success_response({
          message: 'Room data is already consistent',
          consistency_check: consistency_check
        })
      else
        # Fix the inconsistency
        fix_result = @room.fix_data_consistency!

        success_response({
          message: 'Room data consistency fixed successfully',
          fix_result: fix_result,
          consistency_check: consistency_check
        })
      end
    rescue StandardError => e
      Rails.logger.error "Failed to fix room data consistency: #{e.message}"
      error_response('Failed to fix room data consistency', :internal_server_error)
    end
  end

  # GET /admin/rooms/:id/consistency_check
  def consistency_check
    unless @room
      return error_response('Room not found', :not_found)
    end

    begin
      consistency_check = @room.check_data_consistency

      success_response({
        room_id: @room.id.to_s,
        room_name: @room.name,
        consistency_check: consistency_check,
        current_players_list: @room.current_players_list,
        all_game_sessions: @room.game_sessions.map do |session|
          {
            id: session.id.to_s,
            user_id: session.user_id.to_s,
            username: session.user&.username,
            status: session.status,
            created_at: session.created_at,
            updated_at: session.updated_at
          }
        end
      })
    rescue StandardError => e
      Rails.logger.error "Failed to check room data consistency: #{e.message}"
      error_response('Failed to check room data consistency', :internal_server_error)
    end
  end

  # POST /admin/rooms
  def create
    # Use the current authenticated user as the creator, or find by creator_id if provided
    if room_params[:creator_id].present?
      @creator = User.find(room_params[:creator_id])
    else
      @creator = current_user
    end

    @room = Room.new(room_params.except(:creator_id).merge(
      creator: @creator,
      configuration: build_room_configuration
    ))

    if @room.save
      # Set external room ID for now (Game Service sync disabled temporarily)
      @room.update!(
        external_room_id: "room_#{SecureRandom.hex(8)}_#{Time.current.to_i}",
        sync_required: false,
        last_synced_at: Time.current
      )

      success_response({
        room: room_data(@room)
      }, 'Room created successfully', :created)
    else
      validation_error(@room.errors)
    end
  rescue Mongoid::Errors::DocumentNotFound
    error_response('Creator user not found', :not_found)
  rescue StandardError => e
    Rails.logger.error "Room creation failed: #{e.message}"
    Rails.logger.error "Error details: #{e.class.name} - #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}"
    error_response('Failed to create room', :unprocessable_entity)
  end

  # PUT /admin/rooms/:id
  def update
    if @room.update(room_update_params)
      # Mark for sync with Game Service
      @room.update!(sync_required: true)

      # Sync immediately if requested
      if params[:sync_immediately]
        RoomSettingsSyncJob.perform_async(@room.id)
      end

      success_response({
        room: room_data(@room)
      }, 'Room updated successfully')
    else
      validation_error(@room.errors)
    end
  rescue StandardError => e
    Rails.logger.error "Room update failed: #{e.message}"
    error_response('Failed to update room', :unprocessable_entity)
  end

  # POST /admin/rooms/:id/kick_player
  def kick_player
    begin
      user_id = params[:user_id]
      reason = params[:reason] || 'Kicked by administrator'

      unless user_id
        return error_response('User ID is required', :bad_request)
      end

      # Fix any data inconsistency before attempting kick
      consistency_check = @room.check_data_consistency
      if !consistency_check[:consistent]
        Rails.logger.warn "Data inconsistency detected in room #{@room.id} before kick: #{consistency_check}"
        fix_result = @room.fix_data_consistency!
        Rails.logger.info "Fixed room #{@room.id} data inconsistency: #{fix_result}"
        @room.reload
      end

      # Find the user to kick
      user = User.find(user_id)

      # Verify user is actually in the room
      active_session = @room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
      unless active_session
        return error_response('Player is not currently in this room', :unprocessable_entity, nil, 'PLAYER_NOT_IN_ROOM')
      end

      # Use enhanced seat management service for kick
      seat_management_service = EnhancedSeatManagementService.new
      result = seat_management_service.kick_player_with_seat_management(@room, user, @current_user, reason)

      if result[:success]
        success_response(result)
      else
        error_response('Failed to kick player', :internal_server_error, result[:error])
      end
    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found)
    rescue StandardError => e
      Rails.logger.error "Failed to kick player: #{e.message}"
      error_response('Failed to kick player', :internal_server_error, e.message)
    end
  end

  # POST /admin/rooms/:id/reassign_seats
  def reassign_seats
    begin
      new_seat_order = params[:seat_order] # Optional array of user IDs in desired order

      # Use enhanced seat management service for reassignment
      seat_management_service = EnhancedSeatManagementService.new
      result = seat_management_service.reassign_room_seats(@room, @current_user, new_seat_order)

      if result[:success]
        success_response(result)
      else
        error_response('Failed to reassign seats', :internal_server_error, result[:error])
      end
    rescue StandardError => e
      Rails.logger.error "Failed to reassign seats: #{e.message}"
      error_response('Failed to reassign seats', :internal_server_error, e.message)
    end
  end

  # DELETE /admin/rooms/:id
  def destroy
    reason = params[:reason] || 'Administrative deletion'
    refund_players = params[:refund_players] != false
    notify_players = params[:notify_players] != false

    players_refunded = 0
    total_refund_amount = 0.0
    notifications_sent = 0

    # Process room deletion (Mongoid doesn't have the same transaction API as ActiveRecord)
    begin
      if refund_players && @room.current_players > 0
        @room.game_sessions.in(status: ['pending', 'active']).each do |session|
          # For now, skip the balance update since we don't have the update_balance! method
          # session.user.update_balance!(
          #   session.bet_amount,
          #   'refund',
          #   "Refund due to room deletion: #{reason}",
          #   @room.id.to_s,
          #   'Room'
          # )
          players_refunded += 1
          total_refund_amount += session.bet_amount.to_f
        end
      end

      if notify_players
        # Send notifications to players
        notifications_sent = @room.current_players
        # For now, skip the notification job since it might have issues
        # NotificationJob.perform_async(
        #   'room_deleted',
        #   @room.game_sessions.pluck(:user_id),
        #   {
        #     room_name: @room.name,
        #     reason: reason,
        #     refunded: refund_players
        #   }
        # )
      end

      # Notify Game Service to delete room (skip for now since Game Service sync is disabled)
      # begin
      #   GameServiceClient.instance.delete_room(@room.external_room_id, reason)
      # rescue StandardError => e
      #   Rails.logger.error "Failed to delete room from Game Service: #{e.message}"
      # end

      @room.destroy!
    rescue StandardError => e
      Rails.logger.error "Error during room deletion process: #{e.message}"
      raise e
    end

    success_response({
      room_id: @room.id.to_s,
      players_refunded: players_refunded,
      total_refund_amount: total_refund_amount,
      notifications_sent: notifications_sent
    }, 'Room deleted and players refunded successfully')
  rescue StandardError => e
    Rails.logger.error "Room deletion failed: #{e.message}"
    error_response('Failed to delete room', :unprocessable_entity)
  end

  private

  def set_room
    @room = Room.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    @room = nil
    Rails.logger.warn "Room not found: #{params[:id]}"
  end

  def room_params
    params.require(:room).permit(
      :name, :game_type, :max_players, :bet_amount, :currency,
      :is_private, :password, :creator_id,
      game_specific_config: {}
    )
  end

  def room_update_params
    params.require(:room).permit(
      :name, :max_players, :bet_amount, :game_duration,
      game_specific_config: {}
    )
  end

  def build_room_configuration
    {
      game_duration: params.dig(:room, :game_duration) || 30,
      created_by_service: 'manager-service',
      created_at: Time.current.iso8601
    }
  end

  def room_creation_params
    {
      creator_id: @room.creator_id.to_s,
      name: @room.name,
      game_type: @room.game_type,
      max_players: @room.max_players,
      bet_amount: @room.bet_amount.to_f,
      currency: @room.currency,
      game_duration: @room.configuration['game_duration'] || 30,
      game_specific_config: @room.game_specific_config,
      is_private: @room.is_private,
      password: @room.password
    }
  end

  def room_data(room)
    {
      id: room.id.to_s,
      external_room_id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      creator_id: room.creator_id.to_s,
      creator_username: room.creator&.username || 'Unknown',
      max_players: room.max_players,
      current_players: room.current_players,
      bet_amount: room.bet_amount.to_f,
      currency: room.currency,
      prize_pool: room.prize_pool.to_f,
      is_private: room.is_private,
      configuration: room.configuration,
      created_at: room.created_at.iso8601,
      updated_at: room.updated_at.iso8601
    }
  end

  def detailed_room_data(room)
    base_data = room_data(room)
    base_data.merge({
      current_players_details: room.game_sessions.in(status: ['pending', 'active']).map do |session|
        {
          user_id: session.user_id.to_s,
          username: session.user.username,
          bet_amount: session.bet_amount.to_f,
          joined_at: session.created_at.iso8601,
          status: session.status
        }
      end,
      game_session: room.playing? ? game_session_info(room) : nil,
      statistics: room_statistics(room),
      sync_status: {
        sync_required: room.sync_required,
        last_synced_at: room.last_synced_at&.iso8601
      }
    })
  end

  def detailed_room_data_with_players(room)
    base_data = room_data(room)

    # Get players with detailed information
    # Include pending and active sessions as these represent current players
    # Group by user_id to avoid duplicate players with multiple sessions
    player_sessions = room.game_sessions.includes(:user).in(status: ['pending', 'active'])
    players_by_user = player_sessions.group_by(&:user_id)

    players = players_by_user.map do |user_id, sessions|
      # Use the most recent session for player info
      latest_session = sessions.max_by(&:created_at)
      {
        user_id: user_id.to_s,
        username: latest_session.user&.username || 'Unknown',
        bet_amount: latest_session.bet_amount.to_f,
        position: latest_session.metadata&.dig('position'),
        is_ready: latest_session.metadata&.dig('is_ready') || false,
        joined_at: latest_session.created_at.iso8601,
        status: latest_session.status,
        session_count: sessions.count
      }
    end

    # Get game sessions for this room
    game_sessions = room.game_sessions.includes(:user).map do |session|
      {
        id: session.id.to_s,
        session_id: session.session_id,
        game_type: session.game_type,
        status: session.status,
        bet_amount: session.bet_amount.to_f,
        win_amount: session.win_amount&.to_f || 0.0,
        result: session.result || {},
        started_at: session.started_at&.iso8601,
        ended_at: session.ended_at&.iso8601,
        user_id: session.user_id.to_s,
        metadata: session.metadata || {}
      }
    end

    base_data.merge({
      players: players,
      creator: {
        id: room.creator_id.to_s,
        username: room.creator&.username || 'Unknown'
      },
      game_sessions: game_sessions,
      statistics: room_statistics(room),
      sync_status: {
        sync_required: room.sync_required,
        last_synced_at: room.last_synced_at&.iso8601
      }
    })
  end

  def game_session_info(room)
    active_sessions = room.game_sessions.where(status: 'active')
    return nil if active_sessions.empty?

    first_session = active_sessions.first
    {
      started_at: first_session.started_at&.iso8601,
      estimated_end_at: (first_session.started_at + room.configuration['game_duration'].seconds)&.iso8601
    }
  end

  def room_statistics(room)
    finished_sessions = room.game_sessions.where(status: 'completed')
    {
      total_games_played: finished_sessions.count,
      total_prize_pool: finished_sessions.sum(:bet_amount).to_f,
      average_players: finished_sessions.count > 0 ? room.current_players : 0,
      last_game_at: finished_sessions.maximum(:ended_at)&.iso8601
    }
  end

  def calculate_room_stats
    {
      total_rooms: Room.count,
      active_rooms: Room.in(status: ['waiting', 'playing']).count,
      waiting_rooms: Room.where(status: 'waiting').count,
      playing_rooms: Room.where(status: 'playing').count,
      finished_rooms: Room.where(status: 'finished').count
    }
  end

  def pagination_data(collection)
    {
      current_page: collection.current_page,
      total_pages: collection.total_pages,
      total_count: collection.total_count,
      per_page: collection.limit_value,
      has_next: collection.next_page.present?,
      has_prev: collection.prev_page.present?
    }
  end
end
