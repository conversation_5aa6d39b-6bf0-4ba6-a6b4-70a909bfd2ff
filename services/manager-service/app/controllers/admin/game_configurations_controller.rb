class Admin::GameConfigurationsController < ApplicationController
  before_action :require_admin
  before_action :set_game_type, only: [:show, :update, :state_durations, :update_state_durations]
  before_action :validate_game_type, only: [:show, :update, :state_durations, :update_state_durations]

  # GET /admin/game_configurations
  def index
    begin
      configurations = {}
      
      %w[prizewheel amidakuji global].each do |game_type|
        config_data = {
          general_config: GameSetting.get_game_config(game_type)
        }

        # Only add state durations for game types that have them (not global)
        if %w[prizewheel amidakuji].include?(game_type)
          config_data[:state_durations] = GameSetting.get_state_durations(game_type)
        end

        configurations[game_type] = config_data
      end

      success_response({
        configurations: configurations
      })
    rescue StandardError => e
      Rails.logger.error "Failed to fetch game configurations: #{e.message}"
      error_response('Failed to fetch game configurations', :internal_server_error)
    end
  end

  # GET /admin/game_configurations/:game_type
  def show
    begin
      configuration = {
        game_type: @game_type,
        general_config: GameSetting.get_game_config(@game_type),
        last_updated: GameSetting.by_game_type(@game_type).maximum(:updated_at)
      }

      # Only add state durations for game types that have them (not global)
      if %w[prizewheel amidakuji].include?(@game_type)
        configuration[:state_durations] = GameSetting.get_state_durations(@game_type)
      end

      success_response({
        configuration: configuration
      })
    rescue StandardError => e
      Rails.logger.error "Failed to fetch game configuration for #{@game_type}: #{e.message}"
      error_response('Failed to fetch game configuration', :internal_server_error)
    end
  end

  # PUT /admin/game_configurations/:game_type
  def update
    begin
      # Update general configuration if provided
      if params[:general_config].present?
        general_config = params[:general_config].permit!.to_h
        GameSetting.update_game_config(@game_type, general_config)
      end

      # Update state durations if provided
      if params[:state_durations].present?
        state_durations = params[:state_durations].permit!.to_h

        # Validate state durations
        validation_errors = GameSetting.validate_state_durations(state_durations)
        if validation_errors.any?
          return error_response(validation_errors.join(', '), :unprocessable_entity)
        end

        GameSetting.update_state_durations(@game_type, state_durations)
      end

      # Log the configuration update
      Rails.logger.info "Game configuration updated - Game Type: #{@game_type} - Admin: #{current_user.id}"

      # Return updated configuration
      updated_configuration = {
        game_type: @game_type,
        general_config: GameSetting.get_game_config(@game_type),
        last_updated: Time.current
      }

      # Only add state durations for game types that have them (not global)
      if %w[prizewheel amidakuji].include?(@game_type)
        updated_configuration[:state_durations] = GameSetting.get_state_durations(@game_type)
      end

      success_response({
        configuration: updated_configuration,
        message: 'Game configuration updated successfully'
      })
    rescue StandardError => e
      Rails.logger.error "Failed to update game configuration for #{@game_type}: #{e.message}"
      error_response('Failed to update game configuration', :internal_server_error)
    end
  end

  # GET /admin/game_configurations/:game_type/state_durations
  def state_durations
    begin
      durations = GameSetting.get_state_durations(@game_type)
      
      success_response({
        game_type: @game_type,
        state_durations: durations
      })
    rescue StandardError => e
      Rails.logger.error "Failed to fetch state durations for #{@game_type}: #{e.message}"
      error_response('Failed to fetch state durations', :internal_server_error)
    end
  end

  # PUT /admin/game_configurations/:game_type/state_durations
  def update_state_durations
    begin
      state_durations = params.require(:state_durations).permit(
        :idle, :starting, :playing, :wait_to_end, :end
      ).to_h

      # Validate state durations
      validation_errors = GameSetting.validate_state_durations(state_durations)
      if validation_errors.any?
        return error_response(validation_errors.join(', '), :unprocessable_entity)
      end

      GameSetting.update_state_durations(@game_type, state_durations)

      # Log the update
      Rails.logger.info "State durations updated - Game Type: #{@game_type} - Admin: #{current_user.id} - Durations: #{state_durations}"

      success_response({
        game_type: @game_type,
        state_durations: GameSetting.get_state_durations(@game_type),
        message: 'State durations updated successfully'
      })
    rescue ActionController::ParameterMissing => e
      error_response('State durations are required', :bad_request)
    rescue StandardError => e
      Rails.logger.error "Failed to update state durations for #{@game_type}: #{e.message}"
      error_response('Failed to update state durations', :internal_server_error)
    end
  end

  # POST /admin/game_configurations/reset_defaults
  def reset_defaults
    begin
      game_type = params[:game_type]
      
      if game_type.present?
        validate_game_type_param(game_type)
        reset_game_type_defaults(game_type)
        message = "Default configuration reset for #{game_type}"
      else
        # Reset all game types
        %w[prizewheel amidakuji global].each do |type|
          reset_game_type_defaults(type)
        end
        message = "Default configurations reset for all game types"
      end

      Rails.logger.info "Configuration defaults reset - Game Type: #{game_type || 'all'} - Admin: #{current_user.id}"

      success_response({
        message: message
      })
    rescue StandardError => e
      Rails.logger.error "Failed to reset default configurations: #{e.message}"
      error_response('Failed to reset default configurations', :internal_server_error)
    end
  end

  private

  def set_game_type
    @game_type = params[:game_type]
  end

  def validate_game_type
    validate_game_type_param(@game_type)
  end

  def validate_game_type_param(game_type)
    unless %w[prizewheel amidakuji global].include?(game_type)
      raise ArgumentError, "Invalid game type: #{game_type}"
    end
  end

  def reset_game_type_defaults(game_type)
    # Remove existing settings for the game type
    GameSetting.where(game_type: game_type).destroy_all
    
    # Recreate defaults based on game type
    case game_type
    when 'prizewheel'
      create_prizewheel_defaults
    when 'amidakuji'
      create_amidakuji_defaults
    when 'global'
      create_global_defaults
    end
  end

  def create_prizewheel_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'default_sections' => 8,
      'spin_duration_min' => 3000,
      'spin_duration_max' => 8000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 60,
      'idle_timeout' => 1800,
      'starting_duration' => 10,
      'playing_duration' => 30,
      'end_duration' => 15
    }

    defaults.each do |key, value|
      GameSetting.set_setting('prizewheel', key, value, description: 'Default setting')
    end
  end

  def create_amidakuji_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'rows_per_player' => 3,
      'line_probability' => 0.5,
      'animation_duration' => 5000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 90,
      'idle_timeout' => 1800,
      'starting_duration' => 10,
      'playing_duration' => 60,
      'end_duration' => 15
    }

    defaults.each do |key, value|
      GameSetting.set_setting('amidakuji', key, value, description: 'Default setting')
    end
  end

  def create_global_defaults
    defaults = {
      'max_concurrent_games' => 1000,
      'maintenance_mode' => false,
      'prizewheel_enabled' => true,
      'amidakuji_enabled' => true
    }

    defaults.each do |key, value|
      GameSetting.set_setting('global', key, value, description: 'Default global setting')
    end
  end
end
