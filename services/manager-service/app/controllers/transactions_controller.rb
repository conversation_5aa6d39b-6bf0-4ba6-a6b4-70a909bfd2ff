class TransactionsController < ApplicationController
  before_action :set_transaction, only: [:show]
  before_action :require_admin, only: [:index]

  # GET /transactions
  def index
    @transactions = Transaction.includes(:user)

    # Apply filters
    @transactions = @transactions.by_type(params[:type]) if params[:type].present?
    @transactions = @transactions.where(status: params[:status]) if params[:status].present?
    @transactions = @transactions.where(user_id: params[:user_id]) if params[:user_id].present?

    # Username search filter
    if params[:username].present?
      user_ids = User.where(username: /#{Regexp.escape(params[:username])}/i).pluck(:id)
      @transactions = @transactions.where(:user_id.in => user_ids)
    end

    # General search filter (searches transaction_id, description, and username)
    if params[:search].present?
      search_term = Regexp.escape(params[:search])
      user_ids = User.where(username: /#{search_term}/i).pluck(:id)
      @transactions = @transactions.any_of(
        { transaction_id: /#{search_term}/i },
        { description: /#{search_term}/i },
        { :user_id.in => user_ids }
      )
    end

    # Date range filters
    if params[:start_date].present?
      start_date = Date.parse(params[:start_date])
      @transactions = @transactions.where(:created_at.gte => start_date.beginning_of_day)
    end

    if params[:end_date].present?
      end_date = Date.parse(params[:end_date])
      @transactions = @transactions.where(:created_at.lte => end_date.end_of_day)
    end

    # Pagination
    @transactions = @transactions.recent.page(params[:page]).per(params[:per_page] || 25)

    success_response({
      transactions: @transactions.map { |txn| transaction_data(txn) },
      pagination: pagination_data(@transactions)
    })
  rescue Date::Error
    error_response('Invalid date format. Use YYYY-MM-DD format.', :bad_request)
  end

  # GET /transactions/:id
  def show
    # Users can only view their own transactions unless they're admin
    unless current_user.admin? || @transaction.user_id == current_user.id
      return unauthorized('You can only view your own transactions')
    end

    success_response({
      transaction: transaction_data(@transaction)
    })
  end

  # POST /transactions
  def create
    # Only admins can create transactions directly
    require_admin

    @user = User.find(transaction_params[:user_id])

    case transaction_params[:transaction_type]
    when 'deposit'
      @transaction = Transaction.create_deposit(
        @user,
        transaction_params[:amount],
        transaction_params[:description],
        transaction_params[:metadata] || {}
      )
    when 'withdrawal'
      @transaction = Transaction.create_withdrawal(
        @user,
        transaction_params[:amount],
        transaction_params[:description],
        transaction_params[:metadata] || {}
      )
    when 'adjustment'
      @transaction = create_adjustment_transaction
    else
      return error_response('Invalid transaction type for manual creation', :bad_request)
    end

    # Update user balance
    @user.update_balance!(
      @transaction.amount,
      @transaction.transaction_type,
      @transaction.description,
      @transaction.id.to_s,
      'Transaction',
      @transaction.metadata
    )

    @transaction.complete!

    success_response({
      transaction: transaction_data(@transaction)
    }, 'Transaction created successfully', :created)

  rescue Mongoid::Errors::DocumentNotFound
    error_response('User not found', :not_found)
  rescue Mongoid::Errors::Validations => e
    validation_error(e)
  rescue StandardError => e
    Rails.logger.error "Transaction creation failed: #{e.message}"
    error_response('Transaction creation failed', :unprocessable_entity)
  end

  # POST /transactions/deposit
  def deposit
    require_admin

    @user = User.find(deposit_params[:user_id])
    amount = deposit_params[:amount].to_f
    description = deposit_params[:description] || "Deposit by admin #{current_user.username}"

    if amount <= 0
      return error_response('Amount must be positive', :bad_request)
    end

    begin
      # Update user balance and create transaction record in one operation
      @transaction = @user.update_balance!(
        amount,
        'admin_deposit',
        description: description,
        reference_id: nil,
        reference_type: nil,
        metadata: {
          admin_user_id: current_user.id.to_s,
          admin_username: current_user.username,
          payment_method: deposit_params[:payment_method],
          notes: deposit_params[:notes]
        }
      )

      success_response({
        transaction: transaction_data(@transaction),
        user: {
          id: @user.id.to_s,
          username: @user.username,
          balance: @user.balance.to_f
        }
      }, 'Deposit processed successfully', :created)

    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found)
    rescue ArgumentError => e
      error_response(e.message, :bad_request)
    rescue StandardError => e
      Rails.logger.error "Deposit processing failed: #{e.message}"
      error_response('Deposit processing failed', :unprocessable_entity)
    end
  end

  # POST /transactions/withdraw
  def withdraw
    require_admin

    @user = User.find(withdraw_params[:user_id])
    amount = withdraw_params[:amount].to_f
    description = withdraw_params[:description] || "Withdrawal by admin #{current_user.username}"

    if amount <= 0
      return error_response('Amount must be positive', :bad_request)
    end

    begin
      # Update user balance and create transaction record in one operation
      @transaction = @user.update_balance!(
        -amount,
        'withdrawal',
        description: description,
        reference_id: nil,
        reference_type: nil,
        metadata: {
          admin_user_id: current_user.id.to_s,
          admin_username: current_user.username,
          payment_method: withdraw_params[:payment_method],
          notes: withdraw_params[:notes]
        }
      )

      success_response({
        transaction: transaction_data(@transaction),
        user: {
          id: @user.id.to_s,
          username: @user.username,
          balance: @user.balance.to_f
        }
      }, 'Withdrawal processed successfully', :created)

    rescue Mongoid::Errors::DocumentNotFound
      error_response('User not found', :not_found)
    rescue ArgumentError => e
      error_response(e.message, :bad_request)
    rescue StandardError => e
      Rails.logger.error "Withdrawal processing failed: #{e.message}"
      error_response('Withdrawal processing failed', :unprocessable_entity)
    end
  end

  # GET /transactions/stats
  def stats
    if current_user.admin?
      # Global stats for admins
      stats_data = {
        total_volume: Transaction.completed.sum(:amount).abs,
        total_transactions: Transaction.completed.count,
        daily_volume: Transaction.daily_stats[:daily_volume],
        daily_transactions: Transaction.daily_stats[:daily_transactions],
        by_type: Transaction.daily_stats[:by_type]
      }
    else
      # User's own stats
      stats_data = Transaction.stats_for_user(current_user.id)
    end

    success_response(stats_data)
  end

  private

  def set_transaction
    @transaction = Transaction.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    error_response('Transaction not found', :not_found)
  end

  def transaction_params
    params.require(:transaction).permit(
      :user_id, :transaction_type, :amount, :description,
      metadata: {}
    )
  end

  def deposit_params
    params.require(:deposit).permit(:user_id, :amount, :description, :payment_method, :notes)
  end

  def withdraw_params
    params.require(:withdraw).permit(:user_id, :amount, :description, :payment_method, :notes)
  end

  def create_adjustment_transaction
    Transaction.create_transaction(
      user: @user,
      transaction_type: 'adjustment',
      amount: transaction_params[:amount],
      description: transaction_params[:description] || 'Admin balance adjustment',
      metadata: transaction_params[:metadata] || {}
    )
  end

  def transaction_data(transaction)
    {
      id: transaction.id.to_s,
      transaction_id: transaction.transaction_id,
      type: transaction.transaction_type,
      amount: transaction.amount.to_f,
      balance_before: transaction.balance_before.to_f,
      balance_after: transaction.balance_after.to_f,
      status: transaction.status,
      description: transaction.description,
      reference_id: transaction.reference_id,
      reference_type: transaction.reference_type,
      user: {
        id: transaction.user.id.to_s,
        username: transaction.user.username
      },
      created_at: transaction.created_at.iso8601,
      processed_at: transaction.processed_at&.iso8601,
      metadata: transaction.metadata
    }
  end

  def pagination_data(collection)
    {
      current_page: collection.current_page,
      total_pages: collection.total_pages,
      total_count: collection.total_count,
      per_page: collection.limit_value,
      has_next: collection.next_page.present?,
      has_prev: collection.prev_page.present?
    }
  end
end
