class AuthController < ApplicationController
  skip_before_action :authenticate_request, only: [:login, :register]
  skip_before_action :set_current_user, only: [:login, :register]

  # POST /auth/login
  def login
    user = User.find_by(username: login_params[:username])
    
    if user&.authenticate(login_params[:password])
      if user.active?
        user.update_last_login!
        token = generate_jwt_token(user)
        
        success_response({
          user: user_data(user),
          token: token,
          expires_at: 24.hours.from_now.iso8601
        }, 'Login successful')
      else
        error_response('Account is not active', :forbidden)
      end
    else
      error_response('Invalid username or password', :unauthorized)
    end
  rescue StandardError => e
    Rails.logger.error "Login error: #{e.message}"
    error_response('Login failed', :internal_server_error)
  end

  # POST /auth/register
  def register
    user = User.new(register_params)
    
    if user.save
      token = generate_jwt_token(user)
      
      success_response({
        user: user_data(user),
        token: token,
        expires_at: 24.hours.from_now.iso8601
      }, 'Registration successful', :created)
    else
      validation_error_response(user)
    end
  rescue StandardError => e
    Rails.logger.error "Registration error: #{e.message}"
    error_response('Registration failed', :internal_server_error)
  end

  # POST /auth/logout
  def logout
    # In a stateless JWT system, logout is handled client-side
    # Here we could implement token blacklisting if needed
    success_response({}, 'Logout successful')
  end

  # GET /auth/me
  def me
    success_response({
      user: user_data(current_user)
    }, 'User profile retrieved')
  end

  # POST /auth/refresh
  def refresh
    token = generate_jwt_token(current_user)
    
    success_response({
      token: token,
      expires_at: 24.hours.from_now.iso8601
    }, 'Token refreshed')
  end

  # PUT /auth/change_password
  def change_password
    if current_user.authenticate(change_password_params[:current_password])
      current_user.password = change_password_params[:new_password]
      current_user.password_confirmation = change_password_params[:new_password_confirmation]
      
      if current_user.save
        success_response({}, 'Password changed successfully')
      else
        validation_error_response(current_user)
      end
    else
      error_response('Current password is incorrect', :unauthorized)
    end
  rescue StandardError => e
    Rails.logger.error "Change password error: #{e.message}"
    error_response('Password change failed', :internal_server_error)
  end

  private

  def login_params
    params.require(:auth).permit(:username, :password)
  end

  def register_params
    params.require(:auth).permit(:username, :email, :password, :password_confirmation)
  end

  def change_password_params
    params.require(:auth).permit(:current_password, :new_password, :new_password_confirmation)
  end

  def user_data(user)
    {
      id: user.id.to_s,
      username: user.username,
      email: user.email,
      balance: user.balance.to_f,
      status: user.status,
      role: user.role,
      last_login_at: user.last_login_at&.iso8601,
      created_at: user.created_at.iso8601,
      updated_at: user.updated_at.iso8601
    }
  end

  def validation_error_response(model)
    error_response(
      'Validation failed',
      :unprocessable_entity,
      model.errors.full_messages
    )
  end
end
