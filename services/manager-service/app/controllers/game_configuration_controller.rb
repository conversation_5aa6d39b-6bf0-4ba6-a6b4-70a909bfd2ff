class GameConfigurationController < ApplicationController
  # GET /game_configuration
  def show
    begin
      # Get game configuration from settings or use defaults
      config = {
        starting_duration: get_setting('game.starting_duration', 10), # seconds
        playing_duration: get_setting('game.playing_duration', 30),   # seconds
        end_duration: get_setting('game.end_duration', 15)            # seconds
      }

      Rails.logger.info "Game configuration requested - Starting: #{config[:starting_duration]}s, Playing: #{config[:playing_duration]}s, End: #{config[:end_duration]}s"

      success_response(config, 'Game configuration retrieved successfully')
    rescue StandardError => e
      Rails.logger.error "Failed to get game configuration: #{e.message}"
      error_response('Failed to retrieve game configuration', :internal_server_error)
    end
  end

  # PUT /game_configuration
  def update
    begin
      config_params = game_configuration_params

      # Update settings
      if config_params[:starting_duration].present?
        set_setting('game.starting_duration', config_params[:starting_duration].to_i)
      end

      if config_params[:playing_duration].present?
        set_setting('game.playing_duration', config_params[:playing_duration].to_i)
      end

      if config_params[:end_duration].present?
        set_setting('game.end_duration', config_params[:end_duration].to_i)
      end

      # Return updated configuration
      updated_config = {
        starting_duration: get_setting('game.starting_duration', 10),
        playing_duration: get_setting('game.playing_duration', 30),
        end_duration: get_setting('game.end_duration', 15)
      }

      Rails.logger.info "Game configuration updated - Starting: #{updated_config[:starting_duration]}s, Playing: #{updated_config[:playing_duration]}s, End: #{updated_config[:end_duration]}s"

      success_response(updated_config, 'Game configuration updated successfully')
    rescue StandardError => e
      Rails.logger.error "Failed to update game configuration: #{e.message}"
      error_response('Failed to update game configuration', :internal_server_error)
    end
  end

  private

  def game_configuration_params
    params.permit(:starting_duration, :playing_duration, :end_duration)
  end

  # Get a setting value with a default fallback
  def get_setting(key, default_value)
    # For now, use environment variables or defaults
    # In a real implementation, this would use a settings table or configuration service
    case key
    when 'game.starting_duration'
      ENV['GAME_STARTING_DURATION']&.to_i || default_value
    when 'game.playing_duration'
      ENV['GAME_PLAYING_DURATION']&.to_i || default_value
    when 'game.end_duration'
      ENV['GAME_END_DURATION']&.to_i || default_value
    else
      default_value
    end
  end

  # Set a setting value
  def set_setting(key, value)
    # For now, this is a no-op since we're using environment variables
    # In a real implementation, this would update a settings table or configuration service
    Rails.logger.info "Setting #{key} = #{value} (note: using environment variables, setting not persisted)"
  end
end
