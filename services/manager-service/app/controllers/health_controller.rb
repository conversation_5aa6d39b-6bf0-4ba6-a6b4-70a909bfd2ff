class HealthController < ApplicationController
  skip_before_action :authenticate_request, only: [:index, :detailed]
  skip_before_action :set_current_user, only: [:index, :detailed]

  def index
    render json: {
      status: 'healthy',
      service: 'manager-service',
      version: Rails.application.config.service[:version],
      timestamp: Time.current.iso8601
    }
  end

  def detailed
    checks = perform_health_checks
    overall_status = checks.values.all? { |check| check[:status] == 'healthy' } ? 'healthy' : 'unhealthy'

    response_data = {
      status: overall_status,
      service: 'manager-service',
      version: Rails.application.config.service[:version],
      timestamp: Time.current.iso8601,
      checks: checks,
      uptime: uptime_seconds,
      memory_usage: memory_usage_mb
    }

    status_code = overall_status == 'healthy' ? 200 : 503
    render json: response_data, status: status_code
  end

  private

  def perform_health_checks
    checks = {}

    # Database check
    checks[:database] = check_database

    # Redis check
    checks[:redis] = check_redis

    # Game Service check
    checks[:game_service] = check_game_service

    # Background jobs check
    checks[:background_jobs] = check_background_jobs

    # Redis subscriber check
    checks[:redis_subscriber] = check_redis_subscriber

    checks
  end

  def check_database
    start_time = Time.current

    begin
      # Simple query to check database connectivity
      User.count

      {
        status: 'healthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: 'Database connection successful'
      }
    rescue StandardError => e
      {
        status: 'unhealthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: "Database connection failed: #{e.message}"
      }
    end
  end

  def check_redis
    start_time = Time.current

    begin
      redis = Redis.new(url: Rails.application.config.redis_config[:url])
      redis.ping
      redis.close

      {
        status: 'healthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: 'Redis connection successful'
      }
    rescue StandardError => e
      {
        status: 'unhealthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: "Redis connection failed: #{e.message}"
      }
    end
  end

  def check_game_service
    start_time = Time.current

    begin
      game_client = GameServiceClient.instance
      # Attempt a simple health check call to Game Service
      game_client.health_check

      {
        status: 'healthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: 'Game Service connection successful'
      }
    rescue StandardError => e
      {
        status: 'unhealthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: "Game Service connection failed: #{e.message}"
      }
    end
  end

  def check_background_jobs
    start_time = Time.current

    begin
      # Check Sidekiq stats
      stats = Sidekiq::Stats.new

      # Consider unhealthy if too many failed jobs
      failed_threshold = 100
      retry_threshold = 50

      status = 'healthy'
      messages = []

      if stats.failed > failed_threshold
        status = 'unhealthy'
        messages << "Too many failed jobs: #{stats.failed}"
      end

      if stats.retry_size > retry_threshold
        status = 'degraded'
        messages << "High retry queue size: #{stats.retry_size}"
      end

      message = messages.empty? ? 'Background jobs healthy' : messages.join(', ')

      {
        status: status,
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: message,
        details: {
          processed: stats.processed,
          failed: stats.failed,
          retry_size: stats.retry_size,
          dead_size: stats.dead_size,
          enqueued: stats.enqueued
        }
      }
    rescue StandardError => e
      {
        status: 'unhealthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: "Background jobs check failed: #{e.message}"
      }
    end
  end

  def check_redis_subscriber
    start_time = Time.current

    begin
      subscriber = Rails.application.config.redis_subscriber

      if subscriber.nil?
        return {
          status: 'unhealthy',
          response_time_ms: ((Time.current - start_time) * 1000).round(2),
          message: 'Redis subscriber not initialized',
          details: {
            running: false,
            initialized: false
          }
        }
      end

      running = subscriber.running?
      status = running ? 'healthy' : 'unhealthy'
      message = running ? 'Redis subscriber is running and listening for cross-service events' : 'Redis subscriber is not running'

      {
        status: status,
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: message,
        details: {
          running: running,
          initialized: true,
          channels: ['game:sync:notifications', 'game:global:events', 'manager:sync:events']
        }
      }
    rescue StandardError => e
      {
        status: 'unhealthy',
        response_time_ms: ((Time.current - start_time) * 1000).round(2),
        message: "Redis subscriber check failed: #{e.message}",
        details: {
          running: false,
          error: e.message
        }
      }
    end
  end

  def uptime_seconds
    return 0 unless defined?(@@start_time)
    (Time.current - @@start_time).to_i
  end

  def memory_usage_mb
    # Get memory usage in MB
    if RUBY_PLATFORM.include?('linux')
      `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
    else
      # Fallback for other platforms
      0
    end
  rescue
    0
  end

  # Initialize start time when the class is loaded
  @@start_time = Time.current
end
