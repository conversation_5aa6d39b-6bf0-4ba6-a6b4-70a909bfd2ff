class UsersController < ApplicationController
  before_action :set_user, only: [:show, :update, :destroy, :update_balance, :transactions, :game_sessions, :validate_balance]
  before_action :require_admin, only: [:index, :create, :destroy, :update_balance]
  before_action :check_user_access, only: [:show, :update, :transactions, :game_sessions]

  # GET /users
  def index
    users = User.all
    users = users.where(status: params[:status]) if params[:status].present?
    users = users.where(role: params[:role]) if params[:role].present?

    # Add search functionality
    if params[:search].present?
      search_term = Regexp.escape(params[:search])
      users = users.any_of(
        { username: /#{search_term}/i },
        { email: /#{search_term}/i }
      )
    end

    users = users.page(params[:page]).per(params[:per_page] || 25)

    success_response({
      users: users.map { |user| user_data(user) },
      pagination: pagination_data(users)
    })
  end

  # GET /users/:id
  def show
    success_response({
      user: detailed_user_data(@user)
    })
  end

  # POST /users
  def create
    user_params = user_create_params
    initial_balance = user_params[:balance].to_f

    # Create user with zero balance initially
    user = User.new(user_params.except(:balance))

    if user.save
      # If initial balance is provided and > 0, create admin deposit transaction
      if initial_balance > 0
        admin_user = current_user

        # Update user balance and create transaction record in one operation
        transaction = user.update_balance!(
          initial_balance,
          'admin_deposit',
          description: "Initial balance set by admin #{admin_user.username}",
          reference_id: nil,
          reference_type: nil,
          metadata: {
            admin_user_id: admin_user.id.to_s,
            admin_username: admin_user.username,
            reason: 'initial_balance'
          }
        )

        Rails.logger.info "User created with initial balance - user_id: #{user.id}, username: #{user.username}, initial_balance: #{initial_balance}, admin_user: #{admin_user.username}, transaction_id: #{transaction.transaction_id}"
      end

      success_response({
        user: user_data(user.reload)
      }, 'User created successfully', :created)
    else
      validation_error_response(user)
    end
  rescue StandardError => e
    Rails.logger.error "User creation error: #{e.message}"
    error_response('User creation failed', :internal_server_error)
  end

  # PUT /users/:id
  def update
    if @user.update(user_update_params)
      success_response({
        user: user_data(@user)
      }, 'User updated successfully')
    else
      validation_error_response(@user)
    end
  rescue StandardError => e
    Rails.logger.error "User update error: #{e.message}"
    error_response('User update failed', :internal_server_error)
  end

  # DELETE /users/:id
  def destroy
    if @user.destroy
      success_response({}, 'User deleted successfully')
    else
      error_response('Failed to delete user', :internal_server_error)
    end
  rescue StandardError => e
    Rails.logger.error "User deletion error: #{e.message}"
    error_response('User deletion failed', :internal_server_error)
  end

  # PUT /users/:id/balance
  def update_balance
    amount = balance_params[:amount].to_f
    transaction_type = balance_params[:transaction_type] || 'adjustment'
    description = balance_params[:description]

    if amount == 0
      return error_response('Amount cannot be zero', :bad_request)
    end

    begin
      @user.update_balance!(amount, transaction_type)

      success_response({
        user: user_data(@user),
        transaction: {
          amount: amount,
          type: transaction_type,
          new_balance: @user.balance.to_f
        }
      }, 'Balance updated successfully')
    rescue StandardError => e
      Rails.logger.error "Balance update error: #{e.message}"
      error_response('Balance update failed', :internal_server_error)
    end
  end

  # GET /users/:id/transactions
  def transactions
    transactions = @user.transactions.recent
    transactions = transactions.by_type(params[:type]) if params[:type].present?
    transactions = transactions.where(status: params[:status]) if params[:status].present?
    transactions = transactions.page(params[:page]).per(params[:per_page] || 25)

    success_response({
      transactions: transactions.map { |txn| transaction_data(txn) },
      pagination: pagination_data(transactions),
      stats: Transaction.stats_for_user(@user.id)
    })
  end

  # GET /users/:id/game_sessions
  def game_sessions
    sessions = @user.game_sessions.recent
    sessions = sessions.by_game_type(params[:game_type]) if params[:game_type].present?
    sessions = sessions.where(status: params[:status]) if params[:status].present?
    sessions = sessions.page(params[:page]).per(params[:per_page] || 25)

    success_response({
      game_sessions: sessions.map { |session| game_session_data(session) },
      pagination: pagination_data(sessions),
      stats: GameSession.stats_for_user(@user.id)
    })
  end

  # GET /users/search
  def search
    query = params[:q]
    return error_response('Search query is required', :bad_request) if query.blank?

    users = User.any_of(
      { username: /#{Regexp.escape(query)}/i },
      { email: /#{Regexp.escape(query)}/i }
    ).limit(20)

    success_response({
      users: users.map { |user| user_data(user) }
    })
  end

  # GET /users/profile
  def profile
    success_response({
      user: detailed_user_data(current_user)
    })
  end

  # POST /users/:id/validate_balance
  def validate_balance
    bet_amount = validate_balance_params[:bet_amount].to_f

    if bet_amount <= 0
      return error_response('Bet amount must be greater than 0', :bad_request)
    end

    has_sufficient_balance = @user.balance >= bet_amount

    Rails.logger.info "Balance validation - User: #{@user.id} - Balance: #{@user.balance} - Bet Amount: #{bet_amount} - Sufficient: #{has_sufficient_balance}"

    success_response({
      has_sufficient_balance: has_sufficient_balance,
      current_balance: @user.balance.to_f,
      bet_amount: bet_amount,
      user_id: @user.id.to_s
    }, 'Balance validation completed')
  rescue StandardError => e
    Rails.logger.error "Balance validation error: #{e.message}"
    error_response('Balance validation failed', :internal_server_error)
  end

  private

  def set_user
    @user = User.find(params[:id])
  rescue Mongoid::Errors::DocumentNotFound
    not_found
  end

  def check_user_access
    unless current_user.admin? || current_user.id == @user.id
      unauthorized('You can only access your own profile')
    end
  end

  def user_create_params
    params.require(:user).permit(:username, :email, :password, :password_confirmation, :role, :status, :balance)
  end

  def user_update_params
    allowed_params = [:email]
    allowed_params += [:username, :status, :role] if current_user.admin?
    params.require(:user).permit(allowed_params)
  end

  def balance_params
    params.require(:balance).permit(:amount, :transaction_type, :description)
  end

  def validate_balance_params
    params.permit(:bet_amount)
  end

  def user_data(user)
    {
      id: user.id.to_s,
      username: user.username,
      email: user.email,
      balance: user.balance.to_f,
      status: user.status,
      role: user.role,
      last_login_at: user.last_login_at&.iso8601,
      created_at: user.created_at.iso8601,
      updated_at: user.updated_at.iso8601
    }
  end

  def detailed_user_data(user)
    user_data(user).merge({
      total_sessions: user.game_sessions.count,
      total_transactions: user.transactions.count,
      metadata: user.metadata
    })
  end

  def transaction_data(transaction)
    {
      id: transaction.id.to_s,
      transaction_id: transaction.transaction_id,
      type: transaction.transaction_type,
      amount: transaction.amount.to_f,
      balance_before: transaction.balance_before.to_f,
      balance_after: transaction.balance_after.to_f,
      status: transaction.status,
      description: transaction.description,
      created_at: transaction.created_at.iso8601,
      processed_at: transaction.processed_at&.iso8601
    }
  end

  def game_session_data(session)
    {
      id: session.id.to_s,
      session_id: session.session_id,
      game_type: session.game_type,
      status: session.status,
      bet_amount: session.bet_amount.to_f,
      win_amount: session.win_amount.to_f,
      profit_loss: session.profit_loss.to_f,
      started_at: session.started_at&.iso8601,
      ended_at: session.ended_at&.iso8601,
      created_at: session.created_at.iso8601
    }
  end

  def pagination_data(collection)
    {
      current_page: collection.current_page,
      total_pages: collection.total_pages,
      total_count: collection.total_count,
      per_page: collection.limit_value,
      has_next: collection.current_page < collection.total_pages,
      has_prev: collection.current_page > 1
    }
  end

  def validation_error_response(model)
    error_response(
      'Validation failed',
      :unprocessable_entity,
      model.errors.full_messages
    )
  end
end
