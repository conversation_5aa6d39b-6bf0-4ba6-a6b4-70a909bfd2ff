class RequestLoggingMiddleware
  def initialize(app)
    @app = app
    @logger = Rails.logger
  end

  def call(env)
    request = ActionDispatch::Request.new(env)
    start_time = Time.current
    request_id = SecureRandom.uuid
    
    # Add request ID to headers for tracing
    env['HTTP_X_REQUEST_ID'] = request_id
    
    # Skip logging for health checks and assets
    skip_logging = skip_request_logging?(request)
    
    unless skip_logging
      log_request_start(request, request_id)
    end

    status, headers, response = @app.call(env)
    
    unless skip_logging
      duration = ((Time.current - start_time) * 1000).round(2)
      log_request_end(request, request_id, status, duration)
      
      # Log slow requests
      if duration > slow_request_threshold
        log_slow_request(request, request_id, status, duration)
      end
    end

    # Add request ID to response headers
    headers['X-Request-ID'] = request_id
    
    [status, headers, response]
  rescue StandardError => e
    duration = ((Time.current - start_time) * 1000).round(2)
    log_request_error(request, request_id, e, duration) unless skip_logging
    raise e
  end

  private

  def skip_request_logging?(request)
    # Skip logging for certain paths
    skip_paths = [
      '/health',
      '/up',
      '/assets/',
      '/favicon.ico'
    ]
    
    skip_paths.any? { |path| request.path.start_with?(path) }
  end

  def log_request_start(request, request_id)
    @logger.info "Request started - request_id: #{request_id}, method: #{request.method}, path: #{request.path}, query_string: #{request.query_string.presence}, remote_ip: #{request.remote_ip}, user_agent: #{request.user_agent&.truncate(100)}, content_type: #{request.content_type}, content_length: #{request.content_length}, referer: #{request.referer&.truncate(100)}"
  end

  def log_request_end(request, request_id, status, duration)
    log_level = determine_log_level(status)

    @logger.send(log_level, "Request completed - request_id: #{request_id}, method: #{request.method}, path: #{request.path}, status: #{status}, duration_ms: #{duration}, remote_ip: #{request.remote_ip}")
  end

  def log_slow_request(request, request_id, status, duration)
    @logger.warn "Slow request detected - request_id: #{request_id}, method: #{request.method}, path: #{request.path}, status: #{status}, duration_ms: #{duration}, threshold_ms: #{slow_request_threshold}, remote_ip: #{request.remote_ip}, user_agent: #{request.user_agent&.truncate(100)}"
  end

  def log_request_error(request, request_id, error, duration)
    @logger.error "Request failed with exception - request_id: #{request_id}, method: #{request.method}, path: #{request.path}, duration_ms: #{duration}, error_class: #{error.class.name}, error_message: #{error.message}, remote_ip: #{request.remote_ip}, backtrace: #{error.backtrace&.first(5)&.join(', ')}"
  end

  def determine_log_level(status)
    case status
    when 200..299
      :info
    when 300..399
      :info
    when 400..499
      :warn
    when 500..599
      :error
    else
      :info
    end
  end

  def slow_request_threshold
    @slow_request_threshold ||= ENV['SLOW_REQUEST_THRESHOLD_MS']&.to_f || 1000.0
  end
end
