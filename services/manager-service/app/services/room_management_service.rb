class RoomManagementService
  include <PERSON><PERSON>

  def initialize
    @logger = Rails.logger
    @game_client = GameServiceClient.instance
    @balance_service = BalanceService.instance
    @notification_service = NotificationService.new
    @redis = $redis
  end

  # Create a new room with Game Service integration
  def create_room(creator, room_params)
    validate_creator_permissions(creator)
    validate_room_params(room_params)

    room = nil

    # Create room in Manager Service
    room = Room.new(
      name: room_params[:name],
      game_type: room_params[:game_type],
      creator: creator,
      max_players: room_params[:max_players],
      bet_amount: room_params[:bet_amount],
      currency: room_params[:currency] || 'USD',
      is_private: room_params[:is_private] || false,
      configuration: build_room_configuration(room_params),
      game_specific_config: room_params[:game_specific_config] || {}
    )

    # Set password if provided
    room.password = room_params[:password] if room_params[:password].present?

    room.save!

    # Create room in Game Service
    begin
      grpc_response = @game_client.create_room(build_grpc_room_params(room))
      # FIXED: Store Game Service ID in metadata, keep external_room_id as Manager Service ID
      room.update!(
        metadata: (room.metadata || {}).merge({
          'game_service_room_id' => grpc_response.room.id
        }),
        sync_required: false,
        last_synced_at: Time.current
      )

      @logger.info "Room created in Game Service - Manager ID: #{room.external_room_id} - Game Service ID: #{grpc_response.room.id}"
    rescue StandardError => e
      @logger.error "Failed to create room in Game Service - Room: #{room.id} - Error: #{e.message}"

      # Mark for later sync
      room.update!(sync_required: true)

      # Schedule retry
      RoomSettingsSyncJob.perform_in(30.seconds, room.id)
    end

    @logger.info "Room created successfully - Room: #{room.id} - External: #{room.external_room_id} - Creator: #{creator.id} - Game Type: #{room.game_type}"

    room
  end

  # Add a player to a room
  def add_player_to_room(room, user, password: nil)
    validate_room_join(room, user, password)

    # Check for existing session (reconnection)
    existing_session = find_existing_session(room, user)
    is_reconnection = existing_session.present?

    if is_reconnection
      handle_player_reconnection(room, user, existing_session)
    else
      handle_new_player_join(room, user)
    end
  end

  # Kick a player from a room (admin action)
  def kick_player_from_room(room, user, reason: 'Kicked by administrator', kicked_by_user: nil)
    @logger.info "Starting kick player from room - Room: #{room.id} - User: #{user.id} - Reason: #{reason} - Kicked by: #{kicked_by_user&.username || 'System'}"

    # Validate kick eligibility
    session = validate_kick_eligibility(room, user)
    refund_amount = session.bet_amount

    begin
      # Execute the kick
      execute_player_kick(room, user)

      # Handle post-kick notifications and logging
      handle_kick_notifications(room, user, reason, kicked_by_user)

      # Create audit log
      create_kick_audit_log(room, user, reason, kicked_by_user, refund_amount)

      # Return success response
      build_kick_success_response(room, user, reason, kicked_by_user, refund_amount)
    rescue StandardError => e
      handle_kick_error(room, user, e)
    end
  end

  private

  # Handles player reconnection to existing room
  def handle_player_reconnection(room, user, existing_session)
    @logger.info "Player reconnecting to room - Room: #{room.id} - User: #{user.id} - Session: #{existing_session.session_id}"

    # Find existing transaction record
    transaction_record = @balance_service.find_reservation(user, room.id.to_s, 'Room')

    # Notify Game Service of reconnection
    notify_game_service_join(room, user, is_reconnection: true)

    @logger.info "Player reconnected to room - Room: #{room.id} - User: #{user.id}"

    { transaction_record: transaction_record, is_reconnection: true }
  end

  # Handles new player joining room
  def handle_new_player_join(room, user)
    # Reserve balance for the bet
    transaction_record = reserve_player_balance(room, user)

    # Add player to room with explicit transaction handling
    session = nil
    Room.transaction do
      session = room.add_player!(user)

      # Ensure transaction is committed before proceeding
      # This prevents race conditions where room info is fetched before player is visible
    end

    begin
      # Notify Game Service after transaction is committed
      notify_game_service_join(room, user, is_reconnection: false)

      # Reload room to ensure we have the latest state
      room.reload

      @logger.info "Player added to room - Room: #{room.id} - User: #{user.id} - Transaction: #{transaction_record&.transaction_id} - Current players: #{room.current_players}"

      { transaction_record: transaction_record, is_reconnection: false, session: session }
    rescue StandardError => e
      # Rollback on failure
      rollback_player_join(room, user, transaction_record)
      raise e
    end
  end

  # Finds existing session for player in room
  def find_existing_session(room, user)
    room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
  end

  # Reserves balance for player's bet
  def reserve_player_balance(room, user)
    @balance_service.reserve_balance(
      user,
      room.bet_amount,
      room.id.to_s,
      'Room',
      "Bet for room: #{room.name}"
    )
  end

  # Notifies Game Service of player join
  def notify_game_service_join(room, user, is_reconnection:)
    @game_client.join_room(room.external_room_id, user.id.to_s)
    @logger.info "Successfully notified Game Service of player #{is_reconnection ? 'reconnection' : 'join'} - Room: #{room.id} - User: #{user.id}"
  rescue StandardError => e
    @logger.error "Failed to notify Game Service of player #{is_reconnection ? 'reconnection' : 'join'} - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
    raise e
  end

  # Rollbacks player join on failure
  def rollback_player_join(room, user, transaction_record)
    begin
      # Remove player from room if join failed
      room.remove_player!(user)
      @logger.info "Rolled back player join due to Game Service notification failure - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => rollback_error
      @logger.error "Failed to rollback player join - Room: #{room.id} - User: #{user.id} - Error: #{rollback_error.message}"
    end

    # Release reserved balance if join failed
    if transaction_record
      begin
        @balance_service.release_reserved_balance(transaction_record.transaction_id)
        @logger.info "Released reserved balance due to join failure - Room: #{room.id} - User: #{user.id} - Transaction: #{transaction_record.transaction_id}"
      rescue StandardError => balance_error
        @logger.error "Failed to release reserved balance - Room: #{room.id} - User: #{user.id} - Error: #{balance_error.message}"
      end
    end
  end

  # Validates that user can be kicked from room
  def validate_kick_eligibility(room, user)
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      @logger.warn "No active session found for user to kick - Room: #{room.id} - User: #{user.id}"
      raise ArgumentError, "User is not in this room"
    end
    session
  end

  # Executes the player kick operation
  def execute_player_kick(room, user)
    # Remove player from room (this handles refunds automatically)
    room.remove_player!(user)
    @logger.info "Player kicked successfully - Room: #{room.id} - User: #{user.id} - Remaining players: #{room.current_players}"

    # Check if room is now empty and handle accordingly
    handle_empty_room_after_kick(room) if room.current_players == 0
  end

  # Handles all notifications related to player kick
  def handle_kick_notifications(room, user, reason, kicked_by_user)
    # Notify other services about the kick
    publish_kick_notification_to_services(room, user, reason, kicked_by_user)

    # Notify the kicked player directly
    notify_kicked_player(room, user, reason)
  end

  # Publishes kick notification to other services
  def publish_kick_notification_to_services(room, user, reason, kicked_by_user)
    begin
      @notification_service.publish_player_kicked(room, user, reason, kicked_by_user&.username || 'System')
      @logger.info "Published kick notification to other services - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      @logger.error "Failed to publish kick notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the kick operation if notification fails
    end
  end

  # Notifies the kicked player directly
  def notify_kicked_player(room, user, reason)
    begin
      @notification_service.notify_kicked_from_room(user, room, reason)
      @logger.info "Sent kick notification to user - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      @logger.error "Failed to send user kick notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the kick operation if user notification fails
    end
  end

  # Creates audit log entry for kick action
  def create_kick_audit_log(room, user, reason, kicked_by_user, refund_amount)
    audit_data = {
      action_type: 'kick_player',
      room_id: room.id.to_s,
      room_name: room.name,
      target_user_id: user.id.to_s,
      target_username: user.username,
      reason: reason,
      performed_by: kicked_by_user&.id&.to_s || 'system',
      performed_by_username: kicked_by_user&.username || 'System',
      performed_at: Time.current.iso8601,
      refund_amount: refund_amount.to_f
    }

    @logger.info "Kick player audit log: #{audit_data.to_json}"
  end

  # Builds success response for kick operation
  def build_kick_success_response(room, user, reason, kicked_by_user, refund_amount)
    {
      success: true,
      kicked_user: {
        id: user.id.to_s,
        username: user.username
      },
      room: {
        id: room.id.to_s,
        name: room.name,
        current_players: room.current_players
      },
      refund_amount: refund_amount.to_f,
      reason: reason,
      kicked_by: kicked_by_user&.username || 'System'
    }
  end

  # Handles empty room after kick operation
  def handle_empty_room_after_kick(room)
    @logger.info "Room is now empty after kick - Room: #{room.id} - Name: #{room.name}"

    # For now, we'll keep the room alive but mark it as waiting
    # This prevents the "room not found" issue in Game Service
    # The room can be cleaned up later by scheduled cleanup jobs

    # Ensure room status is waiting (not cancelled)
    if room.status != 'waiting'
      room.update!(status: 'waiting')
      @logger.info "Reset empty room status to waiting - Room: #{room.id}"
    end

    # Publish lobby update to show empty room
    publish_lobby_update('updated', room)

    @logger.info "Empty room after kick handled - Room: #{room.id} - Status: #{room.status} - Players: #{room.current_players}"
  end

  # Handles errors during kick operation
  def handle_kick_error(room, user, error)
    @logger.error "Failed to kick player - Room: #{room.id} - User: #{user.id} - Error: #{error.message}"
    @logger.error error.backtrace.join("\n")
    raise error
  end

  def validate_creator_permissions(creator)
    raise ArgumentError, "Creator must be active" unless creator.active?
    raise ArgumentError, "Creator must have sufficient balance" if creator.balance < 0
  end

  def validate_room_params(params)
    required_fields = [:name, :game_type, :max_players, :bet_amount]
    required_fields.each do |field|
      raise ArgumentError, "#{field} is required" unless params[field].present?
    end

    raise ArgumentError, "Invalid game type" unless %w[prizewheel amidakuji].include?(params[:game_type])
    raise ArgumentError, "Max players must be between 2 and 8" unless (2..8).include?(params[:max_players])
    raise ArgumentError, "Bet amount must be positive" unless params[:bet_amount] > 0
  end

  def validate_room_join(room, user, password)
    raise ArgumentError, "Room is not accepting players" unless room.waiting?
    raise ArgumentError, "Room is full" if room.current_players >= room.max_players
    raise ArgumentError, "User must be active" unless user.active?
    raise ArgumentError, "User has insufficient balance" if user.balance < room.bet_amount

    if room.is_private && !room.valid_password?(password)
      raise ArgumentError, "Invalid room password"
    end
  end

  def build_room_configuration(params)
    {
      game_duration: params[:game_duration] || 30,
      created_by_service: 'manager-service',
      created_at: Time.current.iso8601
    }
  end

  def build_grpc_room_params(room)
    {
      creator_id: room.creator_id.to_s,
      name: room.name,
      game_type: room.game_type,
      max_players: room.max_players,
      bet_amount: room.bet_amount,
      currency: room.currency,
      game_duration: room.configuration['game_duration'],
      game_specific_config: room.game_specific_config,
      is_private: room.is_private,
      password: room.password
    }
  end

  # Publish lobby update event to Redis
  def publish_lobby_update(action, room)
    begin
      message = {
        event: {
          type: 'room_list_updated',
          payload: {
            action: action,
            room: format_room_for_lobby(room)
          },
          timestamp: Time.current.iso8601
        },
        metadata: {
          service_id: 'manager-service',
          version: '1.0.0',
          correlation_id: "lobby-#{Time.current.to_f}",
          priority: 1
        }
      }

      channel = 'game:lobby:updates'
      @redis.publish(channel, message.to_json)

      @logger.debug "Lobby update published - Channel: #{channel} - Action: #{action} - Room: #{room.id}"
    rescue => e
      @logger.error "Failed to publish lobby update - Error: #{e.message} - Action: #{action} - Room: #{room.id}"
    end
  end

  # Format room data for lobby display
  def format_room_for_lobby(room)
    {
      id: room.external_room_id || room.id.to_s,
      name: room.name,
      game_type: room.game_type,
      player_count: room.current_players,
      max_players: room.max_players,
      status: room.status,
      bet_amount: room.bet_amount,
      created_at: room.created_at.iso8601
    }
  end
end
