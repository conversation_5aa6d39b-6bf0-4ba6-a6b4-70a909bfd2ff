class BalanceService
  include <PERSON><PERSON>

  def initialize
    @logger = Rails.logger
    @notification_service = NotificationService.new
  end

  # Process a balance update with atomic operations and audit trail
  def update_balance(user, amount, transaction_type, description: nil, reference_id: nil, reference_type: nil, metadata: {})
    raise ArgumentError, "Amount cannot be zero" if amount == 0
    raise ArgumentError, "User cannot be nil" unless user
    raise ArgumentError, "Invalid transaction type" unless valid_transaction_type?(transaction_type)

    # Check if user has sufficient balance for debits
    if amount < 0 && (user.balance + amount) < 0
      raise InsufficientBalanceError, "Insufficient balance for transaction"
    end

    transaction_record = nil

    User.transaction do
      balance_before = user.balance
      user.balance += amount
      user.save!

      # Create transaction record
      transaction_record = user.transactions.create!(
        amount: amount,
        transaction_type: transaction_type,
        balance_before: balance_before,
        balance_after: user.balance,
        description: description || generate_description(transaction_type, amount),
        reference_id: reference_id,
        reference_type: reference_type,
        status: 'completed',
        metadata: metadata.merge(
          processed_at: Time.current,
          service: 'balance_service'
        )
      )

      @logger.info "Balance updated successfully - User: #{user.id} - Username: #{user.username} - Transaction: #{transaction_record.transaction_id} - Amount: #{amount} - Before: #{balance_before} - After: #{user.balance} - Type: #{transaction_type}"

      # Publish balance update notification
      @notification_service.publish_balance_update(user, transaction_record)
    end

    transaction_record
  rescue StandardError => e
    @logger.error "Balance update failed - User: #{user&.id} - Amount: #{amount} - Type: #{transaction_type} - Error: #{e.message}"

    # Create failed transaction record if user exists
    if user && transaction_record.nil?
      begin
        user.transactions.create!(
          amount: amount,
          transaction_type: transaction_type,
          balance_before: user.balance,
          balance_after: user.balance,
          description: description || generate_description(transaction_type, amount),
          reference_id: reference_id,
          reference_type: reference_type,
          status: 'failed',
          metadata: metadata.merge(
            error_message: e.message,
            failed_at: Time.current,
            service: 'balance_service'
          )
        )
      rescue StandardError => create_error
        @logger.error "Failed to create failed transaction record", error: create_error.message
      end
    end

    raise e
  end

  # Process multiple balance updates atomically
  def batch_update_balances(updates)
    raise ArgumentError, "Updates cannot be empty" if updates.empty?

    transaction_records = []

    # Process each update individually since Mongoid doesn't support multi-document transactions
    # in the same way as ActiveRecord
    updates.each do |update|
      user = update[:user]
      amount = update[:amount]
      transaction_type = update[:transaction_type]
      description = update[:description]
      reference_id = update[:reference_id]
      reference_type = update[:reference_type]
      metadata = update[:metadata] || {}

      transaction_record = update_balance(
        user, amount, transaction_type,
        description: description,
        reference_id: reference_id,
        reference_type: reference_type,
        metadata: metadata
      )

      transaction_records << transaction_record
    end

    @logger.info "Batch balance update completed - Count: #{updates.size} - Transactions: #{transaction_records.map(&:transaction_id).join(', ')}"

    transaction_records
  end

  # Reconcile user balance with external service
  def reconcile_balance(user, external_balance, source: 'game_service')
    current_balance = user.balance
    difference = external_balance - current_balance

    return nil if difference == 0

    @logger.info "Balance reconciliation required - User: #{user.id} - Current: #{current_balance} - External: #{external_balance} - Difference: #{difference} - Source: #{source}"

    transaction_record = update_balance(
      user,
      difference,
      'reconciliation',
      description: "Balance reconciliation with #{source}",
      metadata: {
        source: source,
        external_balance: external_balance,
        local_balance_before: current_balance,
        reconciliation_timestamp: Time.current
      }
    )

    @logger.info "Balance reconciliation completed - User: #{user.id} - Transaction: #{transaction_record.transaction_id} - Difference: #{difference}"

    transaction_record
  end

  # Reserve balance for a pending transaction (e.g., game bet)
  def reserve_balance(user, amount, reference_id, reference_type, description: nil)
    raise ArgumentError, "Amount must be positive" unless amount > 0
    raise ArgumentError, "User has insufficient balance" if user.balance < amount

    # Create pending transaction
    transaction_record = user.transactions.create!(
      amount: -amount,
      transaction_type: 'bet_reserved',
      balance_before: user.balance,
      balance_after: user.balance - amount,
      description: description || "Balance reserved for #{reference_type}",
      reference_id: reference_id,
      reference_type: reference_type,
      status: 'pending',
      metadata: {
        reserved_at: Time.current,
        service: 'balance_service'
      }
    )

    # Update user balance
    user.balance -= amount
    user.save!

    @logger.info "Balance reserved - User: #{user.id} - Amount: #{amount} - Transaction: #{transaction_record.transaction_id} - Reference: #{reference_id}"

    transaction_record
  end

  # Release reserved balance (e.g., when game is cancelled)
  def release_reserved_balance(transaction_id)
    transaction = Transaction.find_by(transaction_id: transaction_id, status: 'pending')
    raise ArgumentError, "Transaction not found or not pending" unless transaction

    user = transaction.user
    amount = -transaction.amount # Reverse the reservation

    user.balance += amount
    user.save!

    transaction.update!(
      status: 'cancelled',
      balance_after: user.balance,
      metadata: transaction.metadata.merge(
        released_at: Time.current,
        service: 'balance_service'
      )
    )

    @logger.info "Reserved balance released - User: #{transaction.user_id} - Transaction: #{transaction_id} - Amount: #{-transaction.amount}"

    transaction
  end

  # Confirm reserved balance (e.g., when game starts)
  def confirm_reserved_balance(transaction_id)
    transaction = Transaction.find_by(transaction_id: transaction_id, status: 'pending')
    raise ArgumentError, "Transaction not found or not pending" unless transaction

    transaction.update!(
      status: 'completed',
      metadata: transaction.metadata.merge(
        confirmed_at: Time.current,
        service: 'balance_service'
      )
    )

    @logger.info "Reserved balance confirmed - User: #{transaction.user_id} - Transaction: #{transaction_id}"

    transaction
  end

  # Find existing reservation for a user and reference
  def find_reservation(user, reference_id, reference_type)
    user.transactions.find_by(
      reference_id: reference_id,
      reference_type: reference_type,
      transaction_type: 'bet_reserved',
      status: 'pending'
    )
  end

  private

  def valid_transaction_type?(type)
    %w[
      deposit withdrawal bet_placed bet_won bet_lost
      adjustment refund bonus reconciliation bet_reserved
    ].include?(type)
  end

  def generate_description(transaction_type, amount)
    case transaction_type
    when 'deposit'
      "Deposit of #{amount}"
    when 'withdrawal'
      "Withdrawal of #{amount.abs}"
    when 'bet_placed'
      "Bet placed: #{amount.abs}"
    when 'bet_won'
      "Bet won: #{amount}"
    when 'bet_lost'
      "Bet lost: #{amount.abs}"
    when 'adjustment'
      "Balance adjustment: #{amount}"
    when 'refund'
      "Refund: #{amount}"
    when 'bonus'
      "Bonus: #{amount}"
    when 'reconciliation'
      "Balance reconciliation: #{amount}"
    when 'bet_reserved'
      "Balance reserved: #{amount.abs}"
    else
      "#{transaction_type.humanize}: #{amount}"
    end
  end

  # Custom exception for insufficient balance
  class InsufficientBalanceError < StandardError; end
end
