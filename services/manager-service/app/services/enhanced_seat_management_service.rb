class EnhancedSeatManagementService
  def initialize
    @room_service_client = RoomServiceClient.new
    @game_service_client = GameServiceClient.instance
    @notification_service = NotificationService.new
    @logger = Rails.logger
  end

  # Kick player with comprehensive seat management
  def kick_player_with_seat_management(room, user, admin_user, reason = 'Kicked by administrator')
    @logger.info "Starting enhanced kick player with seat management - Room: #{room.id} - User: #{user.id} - Admin: #{admin_user.id} - Reason: #{reason}"

    # Step 1: Validate kick eligibility
    session = validate_kick_eligibility(room, user)
    refund_amount = session.bet_amount

    begin
      # Step 2: Remove player from Manager Service database
      execute_manager_service_removal(room, user, session, reason)

      # Step 3: Remove player from Room Service with seat reassignment
      room_service_result = execute_room_service_removal(room, user)

      # Step 4: Remove player from Game Service
      game_service_result = execute_game_service_removal(room, user)

      # Step 5: Broadcast seat updates to all subscribers
      broadcast_seat_updates(room, user, room_service_result, reason, admin_user)

      # Step 6: Handle post-kick notifications and logging
      handle_kick_notifications(room, user, reason, admin_user)

      # Step 7: Create audit log
      create_kick_audit_log(room, user, reason, admin_user, refund_amount)

      # Return success response with seat information
      build_enhanced_kick_success_response(room, user, reason, admin_user, refund_amount, room_service_result)
    rescue StandardError => e
      handle_kick_error(room, user, e)
    end
  end

  # View room with comprehensive seat information
  def get_room_with_seat_details(room_id)
    @logger.info "Getting room with seat details - Room: #{room_id}"

    begin
      # Get room from Manager Service
      room = Room.find(room_id)

      # Get detailed seat information from Room Service
      room_service_response = @room_service_client.get_room_info(room.external_room_id)

      # Get game state from Game Service
      game_service_response = @game_service_client.get_room_status(room.external_room_id)

      # Combine and enhance room data
      enhanced_room_data = build_enhanced_room_data(room, room_service_response, game_service_response)

      {
        success: true,
        room: enhanced_room_data
      }
    rescue StandardError => e
      @logger.error "Failed to get room with seat details - Room: #{room_id} - Error: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end

  # Reassign all seats in a room
  def reassign_room_seats(room, admin_user, new_seat_order = nil)
    @logger.info "Reassigning room seats - Room: #{room.id} - Admin: #{admin_user.id}"

    begin
      # Get current room state from Room Service
      current_room_state = @room_service_client.get_room_info(room.external_room_id)

      # Reassign seats using Room Service
      reassignment_result = @room_service_client.reassign_seats(
        room_id: room.external_room_id,
        new_order: new_seat_order
      )

      # Broadcast seat reassignment to all subscribers
      broadcast_seat_reassignment(room, reassignment_result, admin_user)

      # Create audit log
      create_seat_reassignment_audit_log(room, admin_user, current_room_state, reassignment_result)

      {
        success: true,
        message: 'Seats reassigned successfully',
        room: reassignment_result,
        reassigned_by: admin_user.username
      }
    rescue StandardError => e
      @logger.error "Failed to reassign room seats - Room: #{room.id} - Error: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end

  private

  # Validates that user can be kicked from room
  def validate_kick_eligibility(room, user)
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      @logger.warn "No active session found for user to kick - Room: #{room.id} - User: #{user.id}"
      raise ArgumentError, "User is not in this room"
    end
    session
  end

  # Executes removal from Manager Service database
  def execute_manager_service_removal(room, user, session, reason)
    # Cancel session with kick reason using the proper cancel! method
    session.cancel!("Kicked: #{reason}")

    # Update room player count
    room.reload
    room.current_players -= 1
    room.save!

    @logger.info "Player removed from Manager Service database - Room: #{room.id} - User: #{user.id}"
  end

  # Executes removal from Room Service with seat management
  def execute_room_service_removal(room, user)
    @logger.info "Removing player from Room Service - Room: #{room.external_room_id} - User: #{user.id}"

    response = @room_service_client.remove_player_with_seat_reassignment(
      room_id: room.external_room_id,
      user_id: user.id.to_s,
      reassign_seats: true
    )

    unless response.success
      raise StandardError, "Failed to remove player from Room Service: #{response.message}"
    end

    @logger.info "Player removed from Room Service successfully - Room: #{room.external_room_id} - User: #{user.id}"
    response
  end

  # Executes removal from Game Service
  def execute_game_service_removal(room, user)
    @logger.info "Removing player from Game Service - Room: #{room.external_room_id} - User: #{user.id}"

    response = @game_service_client.leave_room(room.external_room_id, user.id.to_s)

    unless response['success']
      @logger.warn "Failed to remove player from Game Service - Room: #{room.external_room_id} - User: #{user.id} - Response: #{response}"
    end

    @logger.info "Player removal from Game Service completed - Room: #{room.external_room_id} - User: #{user.id}"
    response
  end

  # Broadcasts seat updates to all room subscribers
  def broadcast_seat_updates(room, kicked_user, room_service_result, reason, admin_user)
    @logger.info "Broadcasting seat updates - Room: #{room.id}"

    # Publish to notification service for real-time updates
    @notification_service.publish_room_update(room, 'player_kicked_seat_updated', {
      kicked_user: {
        id: kicked_user.id.to_s,
        username: kicked_user.username
      },
      reason: reason,
      kicked_by: admin_user.username,
      updated_seats: room_service_result.room_data&.players || [],
      seat_reassignment: true,
      timestamp: Time.current.iso8601
    })
  end

  # Broadcasts seat reassignment to all room subscribers
  def broadcast_seat_reassignment(room, reassignment_result, admin_user)
    @logger.info "Broadcasting seat reassignment - Room: #{room.id}"

    @notification_service.publish_room_update(room, 'seats_reassigned', {
      reassigned_by: admin_user.username,
      new_seat_arrangement: reassignment_result.room_data&.players || [],
      timestamp: Time.current.iso8601
    })
  end

  # Handles all notifications related to player kick
  def handle_kick_notifications(room, user, reason, kicked_by_user)
    # Notify other services about the kick
    publish_kick_notification_to_services(room, user, reason, kicked_by_user)

    # Notify the kicked player directly
    notify_kicked_player(room, user, reason)
  end

  # Publishes kick notification to other services
  def publish_kick_notification_to_services(room, user, reason, kicked_by_user)
    @notification_service.publish_player_kicked(room, user, reason, kicked_by_user.username)
  end

  # Notifies the kicked player directly
  def notify_kicked_player(room, user, reason)
    @notification_service.publish_user_notification(user, 'player_kicked', {
      room_id: room.id.to_s,
      room_name: room.name,
      reason: reason,
      timestamp: Time.current.iso8601
    })
  end

  # Builds enhanced room data with seat information
  def build_enhanced_room_data(room, room_service_response, game_service_response)
    {
      id: room.id.to_s,
      external_room_id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: room.current_players,
      max_players: room.max_players,
      bet_amount: room.bet_amount,
      currency: room.currency,
      is_private: room.is_private,
      created_at: room.created_at.iso8601,
      updated_at: room.updated_at.iso8601,
      
      # Enhanced seat information from Room Service
      seat_details: room_service_response&.room_info&.players || [],
      seat_statistics: calculate_seat_statistics(room_service_response),
      
      # Game state information from Game Service
      game_state: game_service_response&.room || {},
      
      # Manager Service specific data
      sessions: room.game_sessions.active.map { |session| session_data(session) }
    }
  end

  # Calculates seat statistics
  def calculate_seat_statistics(room_service_response)
    return {} unless room_service_response&.room_info

    players = room_service_response.room_info.players || []
    max_players = room_service_response.room_info.max_players || 0

    used_positions = players.map(&:position).sort
    gaps = (0...max_players).to_a - used_positions

    {
      total_seats: max_players,
      occupied_seats: players.length,
      available_seats: max_players - players.length,
      used_positions: used_positions,
      gaps: gaps,
      occupancy_rate: max_players > 0 ? (players.length.to_f / max_players * 100).round(2) : 0
    }
  end

  # Builds session data for response
  def session_data(session)
    {
      id: session.id.to_s,
      user_id: session.user.id.to_s,
      username: session.user.username,
      status: session.status,
      bet_amount: session.bet_amount,
      is_ready: session.metadata['is_ready'] || false,
      joined_at: session.created_at.iso8601
    }
  end

  # Creates audit log for kick action
  def create_kick_audit_log(room, user, reason, kicked_by_user, refund_amount)
    # Implementation for audit logging
    @logger.info "Kick audit log created - Room: #{room.id} - User: #{user.id} - Kicked by: #{kicked_by_user.id} - Reason: #{reason} - Refund: #{refund_amount}"
  end

  # Creates audit log for seat reassignment
  def create_seat_reassignment_audit_log(room, admin_user, before_state, after_state)
    # Implementation for audit logging
    @logger.info "Seat reassignment audit log created - Room: #{room.id} - Admin: #{admin_user.id}"
  end

  # Builds enhanced kick success response
  def build_enhanced_kick_success_response(room, user, reason, kicked_by_user, refund_amount, room_service_result)
    {
      success: true,
      message: 'Player kicked and seats reassigned successfully',
      kicked_user: {
        id: user.id.to_s,
        username: user.username
      },
      reason: reason,
      kicked_by: kicked_by_user.username,
      refund_amount: refund_amount,
      room: {
        id: room.id.to_s,
        current_players: room.current_players,
        updated_seats: room_service_result.room_data&.players || []
      },
      timestamp: Time.current.iso8601
    }
  end

  # Handles kick errors
  def handle_kick_error(room, user, error)
    @logger.error "Failed to kick player with seat management - Room: #{room.id} - User: #{user.id} - Error: #{error.message}"
    {
      success: false,
      error: error.message,
      timestamp: Time.current.iso8601
    }
  end
end
