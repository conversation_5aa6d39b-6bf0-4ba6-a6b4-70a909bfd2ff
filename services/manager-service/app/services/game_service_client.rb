require 'singleton'

class GameServiceClient
  include <PERSON><PERSON>

  def initialize
    @logger = Rails.logger
    @game_service_url = ENV['GAME_SERVICE_URL'] || 'game-service:8080'
  end

  def create_room(room_params)
    request_data = {
      creator_id: room_params[:creator_id],
      room_name: room_params[:name],
      game_type: room_params[:game_type],
      config: {
        max_players: room_params[:max_players],
        bet_amount: room_params[:bet_amount],
        currency: room_params[:currency] || 'USD',
        game_duration: room_params[:game_duration] || 30,
        game_specific_config: room_params[:game_specific_config] || {}
      },
      is_private: room_params[:is_private] || false,
      password: room_params[:password]
    }

    with_retry do
      response = make_grpc_request('create_room', request_data)
      @logger.info "Room created successfully",
        room_id: response['room']['id'],
        game_type: room_params[:game_type]
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'create_room', room_params)
  end

  def update_room_settings(room_id, settings)
    request_data = {
      room_id: room_id,
      config: {
        max_players: settings[:max_players],
        bet_amount: settings[:bet_amount],
        currency: settings[:currency],
        game_duration: settings[:game_duration],
        game_specific_config: settings[:game_specific_config] || {}
      }
    }

    with_retry do
      response = make_grpc_request('update_room', request_data)
      @logger.info "Room settings updated successfully", room_id: room_id
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'update_room_settings', { room_id: room_id })
  end

  def get_room_status(room_id)
    request_data = { room_id: room_id }

    with_retry do
      make_grpc_request('get_room', request_data)
    end
  rescue StandardError => e
    handle_grpc_error(e, 'get_room_status', { room_id: room_id })
  end

  def list_rooms(filters = {})
    # Query rooms directly from MongoDB database
    begin
      rooms_collection = Mongoid.default_client[:rooms]

      # Build query filters
      query = {}
      query[:game_type] = filters[:game_type].upcase if filters[:game_type].present?
      query[:status] = filters[:status] if filters[:status].present?

      # Apply has_space filter
      if filters[:has_space] == true || filters[:has_space] == 'true'
        query['$expr'] = { '$lt' => ['$current_players', '$max_players'] }
      end

      # Get total count for pagination
      total_count = rooms_collection.count_documents(query)

      # Apply pagination
      page = (filters[:page] || 1).to_i
      per_page = (filters[:per_page] || 25).to_i
      skip = (page - 1) * per_page

      # Fetch rooms with pagination
      rooms_cursor = rooms_collection.find(query)
                                   .skip(skip)
                                   .limit(per_page)
                                   .sort(created_at: -1)

      # Transform MongoDB documents to expected format
      rooms = rooms_cursor.map do |room|
        transform_room_document(room)
      end

      {
        'rooms' => rooms,
        'pagination' => {
          'total_count' => total_count,
          'current_page' => page,
          'per_page' => per_page,
          'total_pages' => (total_count.to_f / per_page).ceil
        }
      }
    rescue StandardError => e
      @logger.error "Failed to fetch rooms from database", error: e.message
      handle_grpc_error(e, 'list_rooms', filters)
    end
  end

  def delete_room(room_id, reason = nil)
    request_data = {
      room_id: room_id,
      reason: reason || 'Administrative deletion'
    }

    with_retry do
      response = make_grpc_request('delete_room', request_data)
      @logger.info "Room deleted successfully", room_id: room_id, reason: reason
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'delete_room', { room_id: room_id })
  end

  def join_room(room_id, user_id)
    request_data = {
      room_id: room_id,
      user_id: user_id
    }

    with_retry do
      response = make_grpc_request('join_room', request_data)
      @logger.info "User joined room successfully", room_id: room_id, user_id: user_id
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'join_room', { room_id: room_id, user_id: user_id })
  end

  def leave_room(room_id, user_id)
    request_data = {
      room_id: room_id,
      user_id: user_id
    }

    with_retry do
      response = make_grpc_request('leave_room', request_data)
      @logger.info "User left room successfully", room_id: room_id, user_id: user_id
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'leave_room', { room_id: room_id, user_id: user_id })
  end

  def start_game(room_id)
    request_data = { room_id: room_id }

    with_retry do
      response = make_grpc_request('start_game', request_data)
      @logger.info "Game started successfully", room_id: room_id
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'start_game', { room_id: room_id })
  end

  def update_player_balance(user_id, transaction_data)
    request_data = {
      user_id: user_id,
      transaction_type: transaction_data[:type],
      amount: transaction_data[:amount],
      currency: transaction_data[:currency],
      description: transaction_data[:description],
      game_id: transaction_data[:game_id],
      balance_before: transaction_data[:balance_before],
      balance_after: transaction_data[:balance_after]
    }

    with_retry do
      response = make_grpc_request('update_balance', request_data)
      @logger.info "Balance updated successfully", user_id: user_id, amount: transaction_data[:amount]
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'update_player_balance', { user_id: user_id })
  end

  def sync_game_settings(game_type, settings)
    request_data = {
      game_type: game_type,
      settings: settings.to_json
    }

    with_retry do
      response = make_grpc_request('update_game_settings', request_data)
      @logger.info "Game settings synchronized successfully", game_type: game_type
      response
    end
  rescue StandardError => e
    handle_grpc_error(e, 'sync_game_settings', { game_type: game_type })
  end

  def health_check
    # Simple health check - try to make a minimal request
    with_retry(max_retries: 1) do
      response = make_grpc_request('health_check', {})
      @logger.debug "Game Service health check successful"
      response
    end
  rescue StandardError => e
    @logger.error "Game Service health check failed", error: e.message
    raise e
  end

  private

  def transform_room_document(room_doc)
    # Transform MongoDB document to expected API format
    {
      'id' => room_doc['_id'].to_s,
      'name' => room_doc['name'],
      'game_type' => room_doc['game_type'],
      'status' => room_doc['status'],
      'current_players' => room_doc['current_players'] || 0,
      'player_count' => room_doc['current_players'] || 0,
      'max_players' => room_doc['max_players'],
      'bet_amount' => room_doc.dig('configuration', 'bet_limits', 'min_bet') || 100,
      'currency' => room_doc.dig('configuration', 'bet_limits', 'currency') || 'USD',
      'is_private' => room_doc.dig('configuration', 'is_private') || false,
      'has_password' => room_doc.dig('configuration', 'password').present?,
      'creator_id' => room_doc['created_by'],
      'created_at' => room_doc['created_at']&.iso8601,
      'updated_at' => room_doc['updated_at']&.iso8601
    }
  end

  def make_grpc_request(method, data)
    # For now, simulate gRPC calls with HTTP requests
    # In a real implementation, this would use actual gRPC clients

    # Simulate successful response
    case method
    when 'create_room'
      {
        'room' => {
          'id' => "room_#{SecureRandom.hex(8)}",
          'name' => data[:room_name],
          'game_type' => data[:game_type],
          'status' => 'waiting'
        }
      }
    when 'update_room', 'join_room', 'leave_room', 'start_game', 'delete_room', 'health_check'
      { 'success' => true }
    when 'get_room'
      {
        'room' => {
          'id' => data[:room_id],
          'status' => 'waiting',
          'current_players' => 0
        }
      }
    when 'list_rooms'
      # Return empty rooms list
      {
        'rooms' => [],
        'pagination' => {
          'total_count' => 0,
          'current_page' => (data[:page] || 1).to_i,
          'per_page' => (data[:per_page] || 25).to_i,
          'total_pages' => 0
        }
      }
    else
      { 'success' => true }
    end
  end

  def auth_metadata
    {
      'authorization' => "Bearer #{service_jwt_token}",
      'x-service-name' => 'manager-service',
      'x-request-id' => SecureRandom.uuid
    }
  end

  def service_jwt_token
    payload = {
      service: 'manager-service',
      iat: Time.current.to_i,
      exp: 5.minutes.from_now.to_i
    }
    JWT.encode(payload, ENV['JWT_SECRET'] || ENV['JWT_SECRET_KEY'] || ENV['SECRET_KEY_BASE'] || 'fallback_secret_for_development', 'HS256')
  end

  def with_retry(max_retries: 3, &block)
    retries = 0
    begin
      yield
    rescue StandardError => e
      retries += 1
      if retries <= max_retries
        sleep_time = [2 ** retries, 10].min
        @logger.warn "gRPC call failed, retrying in #{sleep_time}s",
          error: e.message, attempt: retries
        sleep(sleep_time)
        retry
      else
        raise e
      end
    end
  end

  def handle_grpc_error(error, method, params)
    @logger.error "gRPC call failed",
      method: method,
      params: params,
      error: error.message,
      backtrace: error.backtrace.first(5)

    raise error
  end
end
