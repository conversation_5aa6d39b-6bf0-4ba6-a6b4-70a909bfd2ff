class TransactionService
  include ActiveModel::Model

  def initialize
    @logger = Rails.logger
  end

  def process_deposit(user, amount, description = nil, metadata = {})
    validate_amount!(amount, positive: true)
    validate_user!(user)

    # Use the user's update_balance! method which creates the transaction atomically
    transaction = user.update_balance!(amount, 'deposit', description: description, metadata: metadata)

    # Notify other services
    notify_balance_update(user, transaction)

    @logger.info "Deposit processed successfully: user_id=#{user.id}, amount=#{amount}, transaction_id=#{transaction.transaction_id}"

    transaction
  rescue StandardError => e
    @logger.error "Deposit processing failed: user_id=#{user.id}, amount=#{amount}, error=#{e.message}"
    raise e
  end

  def process_withdrawal(user, amount, description = nil, metadata = {})
    validate_amount!(amount, positive: true)
    validate_user!(user)

    # Use the user's update_balance! method which creates the transaction atomically
    # The update_balance! method will validate sufficient balance
    transaction = user.update_balance!(-amount, 'withdrawal', description: description, metadata: metadata)

    # Notify other services
    notify_balance_update(user, transaction)

    @logger.info "Withdrawal processed successfully: user_id=#{user.id}, amount=#{amount}, transaction_id=#{transaction.transaction_id}"

    transaction
  rescue StandardError => e
    @logger.error "Withdrawal processing failed: user_id=#{user.id}, amount=#{amount}, error=#{e.message}"
    raise e
  end

  def process_bet_placed(user, game_session, amount = nil)
    amount ||= game_session.bet_amount
    validate_amount!(amount, positive: true)
    validate_user!(user)
    validate_sufficient_balance!(user, amount)

    transaction = Transaction.create_bet_placed(user, amount, game_session)
    user.update_balance!(-amount, 'bet_placed', "Bet placed for #{game_session.game_type}",
                        game_session.id.to_s, 'GameSession')
    transaction.complete!

    # Notify Game Service
    notify_game_service_balance_update(user, transaction, game_session)

    @logger.info "Bet placed successfully",
      user_id: user.id.to_s,
      amount: amount,
      game_session_id: game_session.id.to_s,
      transaction_id: transaction.transaction_id

    transaction
  rescue StandardError => e
    @logger.error "Bet placement failed",
      user_id: user.id.to_s,
      amount: amount,
      game_session_id: game_session&.id&.to_s,
      error: e.message
    raise e
  end

  def process_bet_won(user, game_session, win_amount, result_data = {})
    validate_amount!(win_amount, positive: true)
    validate_user!(user)

    transaction = Transaction.create_bet_won(user, win_amount, game_session, result_data)
    user.update_balance!(win_amount, 'bet_won', "Winnings from #{game_session.game_type}",
                        game_session.id.to_s, 'GameSession', result_data)
    transaction.complete!

    # Update game session
    game_session.update!(win_amount: win_amount, result: result_data)

    # Notify Game Service
    notify_game_service_balance_update(user, transaction, game_session)

    @logger.info "Bet winnings processed successfully",
      user_id: user.id.to_s,
      win_amount: win_amount,
      game_session_id: game_session.id.to_s,
      transaction_id: transaction.transaction_id

    transaction
  rescue StandardError => e
    @logger.error "Bet winnings processing failed",
      user_id: user.id.to_s,
      win_amount: win_amount,
      game_session_id: game_session&.id&.to_s,
      error: e.message
    raise e
  end

  def process_refund(user, original_transaction, reason = nil, metadata = {})
    validate_user!(user)

    unless original_transaction.user_id == user.id
      raise ArgumentError, "Transaction does not belong to user"
    end

    unless original_transaction.debit?
      raise ArgumentError, "Can only refund debit transactions"
    end

    refund_amount = original_transaction.amount.abs
    description = reason || "Refund for transaction #{original_transaction.transaction_id}"

    Transaction.transaction do
      transaction = Transaction.create_transaction(
        user: user,
        transaction_type: 'refund',
        amount: refund_amount,
        description: description,
        reference_id: original_transaction.id.to_s,
        reference_type: 'Transaction',
        metadata: metadata.merge(original_transaction_id: original_transaction.transaction_id)
      )

      user.update_balance!(refund_amount, 'refund', description,
                          transaction.id.to_s, 'Transaction', metadata)
      transaction.complete!

      # Mark original transaction as refunded
      original_transaction.update!(
        metadata: original_transaction.metadata.merge(
          refunded: true,
          refund_transaction_id: transaction.transaction_id,
          refunded_at: Time.current
        )
      )

      @logger.info "Refund processed successfully",
        user_id: user.id.to_s,
        refund_amount: refund_amount,
        original_transaction_id: original_transaction.transaction_id,
        transaction_id: transaction.transaction_id

      transaction
    end
  rescue StandardError => e
    @logger.error "Refund processing failed",
      user_id: user.id.to_s,
      original_transaction_id: original_transaction&.transaction_id,
      error: e.message
    raise e
  end

  def process_adjustment(user, amount, description, admin_user, metadata = {})
    validate_user!(user)
    validate_user!(admin_user)

    unless admin_user.admin?
      raise ArgumentError, "Only admins can create balance adjustments"
    end

    Transaction.transaction do
      transaction = Transaction.create_transaction(
        user: user,
        transaction_type: 'adjustment',
        amount: amount,
        description: description,
        metadata: metadata.merge(
          admin_user_id: admin_user.id.to_s,
          admin_username: admin_user.username
        )
      )

      user.update_balance!(amount, 'adjustment', description,
                          transaction.id.to_s, 'Transaction', metadata)
      transaction.complete!

      @logger.info "Balance adjustment processed successfully",
        user_id: user.id.to_s,
        amount: amount,
        admin_user_id: admin_user.id.to_s,
        transaction_id: transaction.transaction_id

      transaction
    end
  rescue StandardError => e
    @logger.error "Balance adjustment failed",
      user_id: user.id.to_s,
      amount: amount,
      admin_user_id: admin_user&.id&.to_s,
      error: e.message
    raise e
  end

  def get_user_transaction_summary(user, start_date = nil, end_date = nil)
    validate_user!(user)

    Transaction.stats_for_user(user.id, start_date, end_date)
  end

  def reconcile_user_balance(user)
    validate_user!(user)

    # Calculate balance from completed transactions
    calculated_balance = user.transactions.completed.sum(:amount)
    current_balance = user.balance

    if calculated_balance != current_balance
      difference = calculated_balance - current_balance

      @logger.warn "Balance discrepancy detected",
        user_id: user.id.to_s,
        current_balance: current_balance,
        calculated_balance: calculated_balance,
        difference: difference

      # Create reconciliation transaction
      reconciliation_transaction = Transaction.create_transaction(
        user: user,
        transaction_type: 'reconciliation',
        amount: difference,
        description: "Balance reconciliation: #{difference}",
        metadata: {
          previous_balance: current_balance,
          calculated_balance: calculated_balance,
          reconciled_at: Time.current
        }
      )

      user.update!(balance: calculated_balance)
      reconciliation_transaction.complete!

      @logger.info "Balance reconciled successfully",
        user_id: user.id.to_s,
        difference: difference,
        transaction_id: reconciliation_transaction.transaction_id

      return { reconciled: true, difference: difference, transaction: reconciliation_transaction }
    end

    { reconciled: false, difference: 0 }
  end

  private

  def validate_amount!(amount, positive: false)
    raise ArgumentError, "Amount must be a number" unless amount.is_a?(Numeric)
    raise ArgumentError, "Amount must be positive" if positive && amount <= 0
    raise ArgumentError, "Amount cannot be zero" if amount == 0
  end

  def validate_user!(user)
    raise ArgumentError, "User must be provided" unless user
    raise ArgumentError, "User must be active" unless user.active?
  end

  def validate_sufficient_balance!(user, amount)
    if user.balance < amount
      raise ArgumentError, "Insufficient balance. Required: #{amount}, Available: #{user.balance}"
    end
  end

  def notify_balance_update(user, transaction)
    # Publish to Redis for real-time updates
    NotificationService.new.publish_balance_update(user, transaction)
  rescue StandardError => e
    @logger.error "Failed to notify balance update: #{e.message}"
  end

  def notify_game_service_balance_update(user, transaction, game_session)
    # Notify Game Service of balance changes
    GameServiceClient.instance.update_player_balance(
      user.id.to_s,
      {
        type: transaction.transaction_type,
        amount: transaction.amount,
        currency: 'USD',
        description: transaction.description,
        game_id: game_session.id.to_s,
        balance_before: transaction.balance_before,
        balance_after: transaction.balance_after
      }
    )
  rescue StandardError => e
    @logger.error "Failed to notify Game Service of balance update: #{e.message}"
  end
end
