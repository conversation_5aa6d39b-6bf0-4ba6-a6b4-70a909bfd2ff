class RoomServiceClient
  def initialize
    @base_url = ENV['ROOM_SERVICE_URL'] || 'http://room-service:3000'
    @timeout = 30
    @logger = Rails.logger
  end

  # Get comprehensive room information including seat details
  def get_room_info(room_id)
    @logger.info "Getting room info from Room Service - Room: #{room_id}"

    response = make_grpc_request('get_room_info', {
      room_id: room_id
    })

    if response['success']
      @logger.info "Successfully retrieved room info - Room: #{room_id}"
      OpenStruct.new(
        success: true,
        room_info: parse_room_info(response['room_info'])
      )
    else
      @logger.error "Failed to get room info - Room: #{room_id} - Error: #{response['error']}"
      OpenStruct.new(
        success: false,
        error: response['error']
      )
    end
  rescue StandardError => e
    handle_grpc_error(e, 'get_room_info', { room_id: room_id })
  end

  # Remove player with seat reassignment
  def remove_player_with_seat_reassignment(room_id:, user_id:, reassign_seats: true)
    @logger.info "Removing player with seat reassignment - Room: #{room_id} - User: #{user_id} - Reassign: #{reassign_seats}"

    response = make_grpc_request('remove_player_with_seat_management', {
      room_id: room_id,
      user_id: user_id,
      reassign_positions: reassign_seats
    })

    if response['success']
      @logger.info "Successfully removed player with seat reassignment - Room: #{room_id} - User: #{user_id}"
      OpenStruct.new(
        success: true,
        room_data: parse_room_data(response['room']),
        message: response['message']
      )
    else
      @logger.error "Failed to remove player - Room: #{room_id} - User: #{user_id} - Error: #{response['error']}"
      OpenStruct.new(
        success: false,
        error: response['error']
      )
    end
  rescue StandardError => e
    handle_grpc_error(e, 'remove_player_with_seat_management', { room_id: room_id, user_id: user_id })
  end

  # Reassign seats in a room
  def reassign_seats(room_id:, new_order: nil)
    @logger.info "Reassigning seats - Room: #{room_id} - New order: #{new_order}"

    response = make_grpc_request('reassign_room_seats', {
      room_id: room_id,
      new_seat_order: new_order
    })

    if response['success']
      @logger.info "Successfully reassigned seats - Room: #{room_id}"
      OpenStruct.new(
        success: true,
        room_data: parse_room_data(response['room']),
        message: response['message']
      )
    else
      @logger.error "Failed to reassign seats - Room: #{room_id} - Error: #{response['error']}"
      OpenStruct.new(
        success: false,
        error: response['error']
      )
    end
  rescue StandardError => e
    handle_grpc_error(e, 'reassign_room_seats', { room_id: room_id })
  end

  # Get seat statistics for a room
  def get_seat_statistics(room_id)
    @logger.info "Getting seat statistics - Room: #{room_id}"

    response = make_grpc_request('get_seat_statistics', {
      room_id: room_id
    })

    if response['success']
      @logger.info "Successfully retrieved seat statistics - Room: #{room_id}"
      OpenStruct.new(
        success: true,
        statistics: response['statistics']
      )
    else
      @logger.error "Failed to get seat statistics - Room: #{room_id} - Error: #{response['error']}"
      OpenStruct.new(
        success: false,
        error: response['error']
      )
    end
  rescue StandardError => e
    handle_grpc_error(e, 'get_seat_statistics', { room_id: room_id })
  end

  # Validate seat configuration
  def validate_seat_configuration(room_id)
    @logger.info "Validating seat configuration - Room: #{room_id}"

    response = make_grpc_request('validate_seat_configuration', {
      room_id: room_id
    })

    if response['success']
      @logger.info "Seat configuration is valid - Room: #{room_id}"
      OpenStruct.new(
        success: true,
        valid: response['valid'],
        issues: response['issues'] || []
      )
    else
      @logger.error "Failed to validate seat configuration - Room: #{room_id} - Error: #{response['error']}"
      OpenStruct.new(
        success: false,
        error: response['error']
      )
    end
  rescue StandardError => e
    handle_grpc_error(e, 'validate_seat_configuration', { room_id: room_id })
  end

  # Health check for Room Service
  def health_check
    @logger.debug "Performing Room Service health check"

    response = make_grpc_request('health_check', {})
    
    if response['success']
      @logger.debug "Room Service health check successful"
      OpenStruct.new(success: true)
    else
      @logger.error "Room Service health check failed - Error: #{response['error']}"
      OpenStruct.new(success: false, error: response['error'])
    end
  rescue StandardError => e
    @logger.error "Room Service health check failed - Error: #{e.message}"
    OpenStruct.new(success: false, error: e.message)
  end

  private

  # Parse room info response
  def parse_room_info(room_info_data)
    return nil unless room_info_data

    OpenStruct.new(
      room_id: room_info_data['room_id'],
      name: room_info_data['name'],
      game_type: room_info_data['game_type'],
      status: room_info_data['status'],
      current_players: room_info_data['current_players'],
      max_players: room_info_data['max_players'],
      min_players: room_info_data['min_players'],
      bet_amount: room_info_data['bet_amount'],
      players: parse_players(room_info_data['players']),
      is_private: room_info_data['is_private'],
      auto_start: room_info_data['auto_start'],
      created_at: room_info_data['created_at'],
      last_activity: room_info_data['last_activity'],
      can_join: room_info_data['can_join'],
      can_start: room_info_data['can_start']
    )
  end

  # Parse room data response
  def parse_room_data(room_data)
    return nil unless room_data

    OpenStruct.new(
      id: room_data['id'],
      name: room_data['name'],
      status: room_data['status'],
      current_players: room_data['current_players'],
      max_players: room_data['max_players'],
      players: parse_players(room_data['players']),
      updated_at: room_data['updated_at']
    )
  end

  # Parse players array
  def parse_players(players_data)
    return [] unless players_data.is_a?(Array)

    players_data.map do |player|
      OpenStruct.new(
        user_id: player['user_id'],
        username: player['username'],
        position: player['position'],
        is_ready: player['is_ready'],
        bet_amount: player['bet_amount'],
        joined_at: player['joined_at'],
        status: player['status']
      )
    end
  end

  # Make gRPC request (simulated for now)
  def make_grpc_request(method, data)
    # For now, simulate gRPC calls with HTTP requests
    # In a real implementation, this would use actual gRPC clients

    @logger.debug "Making Room Service gRPC call - Method: #{method} - Data: #{data}"

    # Simulate successful response based on method
    case method
    when 'get_room_info'
      simulate_get_room_info_response(data)
    when 'remove_player_with_seat_management'
      simulate_remove_player_response(data)
    when 'reassign_room_seats'
      simulate_reassign_seats_response(data)
    when 'get_seat_statistics'
      simulate_seat_statistics_response(data)
    when 'validate_seat_configuration'
      simulate_validate_configuration_response(data)
    when 'health_check'
      { 'success' => true }
    else
      { 'success' => false, 'error' => "Unknown method: #{method}" }
    end
  end

  # Simulate get room info response
  def simulate_get_room_info_response(data)
    {
      'success' => true,
      'room_info' => {
        'room_id' => data[:room_id],
        'name' => 'Mock Room',
        'game_type' => 'prizewheel',
        'status' => 'waiting',
        'current_players' => 2,
        'max_players' => 6,
        'min_players' => 2,
        'bet_amount' => 100,
        'players' => [
          {
            'user_id' => 'user1',
            'username' => 'Player1',
            'position' => 0,
            'is_ready' => true,
            'bet_amount' => 100,
            'joined_at' => Time.current.iso8601,
            'status' => 'active'
          },
          {
            'user_id' => 'user2',
            'username' => 'Player2',
            'position' => 1,
            'is_ready' => false,
            'bet_amount' => 100,
            'joined_at' => Time.current.iso8601,
            'status' => 'active'
          }
        ],
        'is_private' => false,
        'auto_start' => true,
        'created_at' => Time.current.iso8601,
        'last_activity' => Time.current.iso8601,
        'can_join' => true,
        'can_start' => false
      }
    }
  end

  # Simulate remove player response
  def simulate_remove_player_response(data)
    {
      'success' => true,
      'message' => 'Player removed and seats reassigned successfully',
      'room' => {
        'id' => data[:room_id],
        'current_players' => 1,
        'players' => [
          {
            'user_id' => 'user1',
            'username' => 'Player1',
            'position' => 0,
            'is_ready' => true,
            'bet_amount' => 100,
            'joined_at' => Time.current.iso8601,
            'status' => 'active'
          }
        ],
        'updated_at' => Time.current.iso8601
      }
    }
  end

  # Simulate reassign seats response
  def simulate_reassign_seats_response(data)
    {
      'success' => true,
      'message' => 'Seats reassigned successfully',
      'room' => {
        'id' => data[:room_id],
        'players' => [
          {
            'user_id' => 'user1',
            'username' => 'Player1',
            'position' => 0,
            'is_ready' => true,
            'bet_amount' => 100,
            'joined_at' => Time.current.iso8601,
            'status' => 'active'
          },
          {
            'user_id' => 'user2',
            'username' => 'Player2',
            'position' => 1,
            'is_ready' => false,
            'bet_amount' => 100,
            'joined_at' => Time.current.iso8601,
            'status' => 'active'
          }
        ],
        'updated_at' => Time.current.iso8601
      }
    }
  end

  # Simulate seat statistics response
  def simulate_seat_statistics_response(data)
    {
      'success' => true,
      'statistics' => {
        'total_seats' => 6,
        'occupied_seats' => 2,
        'available_seats' => 4,
        'used_positions' => [0, 1],
        'gaps' => [2, 3, 4, 5],
        'occupancy_rate' => 33.33
      }
    }
  end

  # Simulate validate configuration response
  def simulate_validate_configuration_response(data)
    {
      'success' => true,
      'valid' => true,
      'issues' => []
    }
  end

  # Handle gRPC errors
  def handle_grpc_error(error, method, data)
    @logger.error "Room Service gRPC error - Method: #{method} - Data: #{data} - Error: #{error.message}"
    OpenStruct.new(
      success: false,
      error: error.message
    )
  end
end
