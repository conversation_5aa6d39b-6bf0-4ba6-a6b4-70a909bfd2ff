class EnhancedRoomManagementService
  include Logging

  def initialize
    @game_client = GameServiceClient.new
    @notification_service = NotificationService.new
  end

  # Remove player from room with comprehensive cleanup
  def remove_player_from_room(room, user, admin_user, reason = 'kicked')
    logger.info "Starting player removal process - Room: #{room.id} - User: #{user.id} - Admin: #{admin_user.id} - Reason: #{reason}"

    # Step 1: Validate removal permissions
    validate_removal_permissions(room, user, admin_user)

    # Step 2: Check if player is actually in the room
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      raise ArgumentError, "Player is not in this room"
    end

    # Step 3: Perform atomic database operations
    Room.transaction do
      # Remove player from Manager Service database
      session.update!(status: 'cancelled', ended_at: Time.current)
      room.reload
      room.current_players -= 1
      room.save!

      logger.info "Player removed from Manager Service database - Room: #{room.id} - User: #{user.id}"
    end

    # Step 4: Remove player from Game Service via gRPC
    begin
      grpc_response = @game_client.remove_player(
        room_id: room.external_room_id,
        user_id: user.id.to_s,
        admin_user_id: admin_user.id.to_s,
        reason: reason
      )

      unless grpc_response.success
        logger.error "Failed to remove player from Game Service - Room: #{room.id} - User: #{user.id} - Error: #{grpc_response.message}"
        raise StandardError, "Failed to remove player from Game Service: #{grpc_response.message}"
      end

      logger.info "Player successfully removed from Game Service - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      logger.error "gRPC call failed for player removal - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      
      # Rollback Manager Service changes if Game Service fails
      Room.transaction do
        session.update!(status: 'active')
        room.reload
        room.current_players += 1
        room.save!
      end
      
      raise e
    end

    # Step 5: Invalidate caches via gRPC
    begin
      invalidate_player_caches(room.external_room_id, user.id.to_s)
    rescue StandardError => e
      logger.warn "Failed to invalidate caches after player removal - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the operation if cache invalidation fails
    end

    # Step 6: Send notifications
    begin
      send_player_removal_notifications(room, user, reason)
    rescue StandardError => e
      logger.warn "Failed to send notifications after player removal - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the operation if notifications fail
    end

    logger.info "Player removal process completed successfully - Room: #{room.id} - User: #{user.id}"
    
    # Return updated room state
    room.reload
  end

  # Bulk remove players (for room cleanup)
  def remove_multiple_players(room, user_ids, admin_user, reason = 'room_cleanup')
    logger.info "Starting bulk player removal - Room: #{room.id} - Users: #{user_ids} - Admin: #{admin_user.id}"

    results = []
    user_ids.each do |user_id|
      begin
        user = User.find(user_id)
        remove_player_from_room(room, user, admin_user, reason)
        results << { user_id: user_id, success: true }
      rescue StandardError => e
        logger.error "Failed to remove user #{user_id} from room #{room.id}: #{e.message}"
        results << { user_id: user_id, success: false, error: e.message }
      end
    end

    results
  end

  # Force reassign player positions via Game Service
  def reassign_player_positions(room, new_position_order = nil)
    logger.info "Reassigning player positions - Room: #{room.id}"

    begin
      if new_position_order
        # Use provided order
        grpc_response = @game_client.reassign_player_positions(
          room_id: room.external_room_id,
          user_ids: new_position_order
        )
      else
        # Let Game Service determine optimal order
        grpc_response = @game_client.reassign_player_positions(
          room_id: room.external_room_id,
          user_ids: []
        )
      end

      unless grpc_response.success
        raise StandardError, "Failed to reassign positions: #{grpc_response.message}"
      end

      logger.info "Player positions reassigned successfully - Room: #{room.id}"
      grpc_response.updated_room
    rescue StandardError => e
      logger.error "Failed to reassign player positions - Room: #{room.id} - Error: #{e.message}"
      raise e
    end
  end

  # Comprehensive room cleanup (remove all players)
  def cleanup_room(room, admin_user, reason = 'room_cleanup')
    logger.info "Starting room cleanup - Room: #{room.id} - Admin: #{admin_user.id}"

    # Get all active players
    active_sessions = room.game_sessions.in(status: ['pending', 'active'])
    user_ids = active_sessions.map { |session| session.user.id.to_s }

    if user_ids.empty?
      logger.info "No active players to remove from room #{room.id}"
      return { removed_players: [], success: true }
    end

    # Remove all players
    results = remove_multiple_players(room, user_ids, admin_user, reason)
    
    # Update room status if all players removed
    if results.all? { |result| result[:success] }
      room.update!(status: 'waiting', current_players: 0)
      logger.info "Room cleanup completed successfully - Room: #{room.id}"
    else
      logger.warn "Room cleanup partially failed - Room: #{room.id} - Results: #{results}"
    end

    { removed_players: results, success: results.all? { |result| result[:success] } }
  end

  private

  def validate_removal_permissions(room, user, admin_user)
    # Check if admin has permission to remove players
    unless admin_user.admin? || room.created_by == admin_user.id
      raise ArgumentError, "Insufficient permissions to remove player"
    end

    # Check if trying to remove room creator (special case)
    if room.created_by == user.id && admin_user.id != user.id
      logger.warn "Attempting to remove room creator - Room: #{room.id} - Creator: #{user.id} - Admin: #{admin_user.id}"
      # Allow but log for audit
    end
  end

  def invalidate_player_caches(room_id, user_id)
    # Invalidate room cache
    @game_client.invalidate_room_cache(
      room_id: room_id,
      cache_keys: [
        "room:state:#{room_id}",
        "room:players:#{room_id}",
        "room:config:#{room_id}"
      ]
    )

    # Invalidate player cache
    @game_client.invalidate_player_cache(
      user_id: user_id,
      room_id: room_id,
      cache_keys: [
        "player:#{user_id}:room",
        "player:#{user_id}:status"
      ]
    )

    logger.info "Cache invalidation completed - Room: #{room_id} - User: #{user_id}"
  end

  def send_player_removal_notifications(room, user, reason)
    # Notify via Redis pub/sub for real-time updates
    notification_data = {
      event: {
        type: 'player_removed',
        payload: {
          room_id: room.external_room_id,
          user_id: user.id.to_s,
          username: user.username,
          reason: reason,
          timestamp: Time.current.iso8601
        }
      }
    }

    @notification_service.publish_to_channel(
      "room:#{room.external_room_id}:events",
      notification_data
    )

    # Also notify lobby for room list updates
    lobby_notification = {
      event: {
        type: 'room_list_updated',
        payload: {
          action: 'room_updated',
          room: format_room_for_lobby(room),
          timestamp: Time.current.iso8601
        }
      }
    }

    @notification_service.publish_to_channel(
      'game:lobby:updates',
      lobby_notification
    )

    logger.info "Notifications sent for player removal - Room: #{room.id} - User: #{user.id}"
  end

  def format_room_for_lobby(room)
    {
      id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: room.current_players,
      max_players: room.max_players,
      bet_amount: room.bet_amount,
      is_private: room.is_private,
      created_at: room.created_at.iso8601,
      updated_at: room.updated_at.iso8601
    }
  end
end
