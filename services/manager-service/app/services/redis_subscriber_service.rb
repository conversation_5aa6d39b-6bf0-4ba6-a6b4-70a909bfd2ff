class RedisSubscriberService
  include Logging

  def initialize
    @redis = $redis_pubsub
    @room_management_service = RoomManagementService.instance
    @notification_service = NotificationService.new
    @running = false
    @thread = nil
  end

  def start
    return if @running

    @running = true
    @thread = Thread.new do
      begin
        logger.info "Starting Redis subscriber service for cross-service synchronization"
        
        # Subscribe to channels that Manager Service should listen to
        channels = [
          'game:sync:notifications',
          'game:global:events',
          'manager:sync:events'
        ]

        @redis.subscribe(*channels) do |on|
          on.message do |channel, message|
            handle_message(channel, message)
          end

          on.subscribe do |channel, subscriptions|
            logger.info "Subscribed to Redis channel: #{channel} (#{subscriptions} total subscriptions)"
          end

          on.unsubscribe do |channel, subscriptions|
            logger.info "Unsubscribed from Redis channel: #{channel} (#{subscriptions} remaining subscriptions)"
          end
        end
      rescue StandardError => e
        logger.error "Redis subscriber service error: #{e.message}"
        logger.error e.backtrace.join("\n")
        
        # Restart after a delay if still running
        if @running
          sleep(5)
          retry
        end
      end
    end

    logger.info "Redis subscriber service started successfully"
  end

  def stop
    @running = false
    
    if @thread
      @thread.kill
      @thread.join(5) # Wait up to 5 seconds for graceful shutdown
    end
    
    logger.info "Redis subscriber service stopped"
  end

  def running?
    @running && @thread&.alive?
  end

  private

  def handle_message(channel, message)
    begin
      parsed_message = JSON.parse(message)
      
      logger.debug "Received Redis message", {
        channel: channel,
        message_type: parsed_message['type'] || parsed_message.dig('event', 'type'),
        timestamp: Time.current.iso8601
      }

      case channel
      when 'game:sync:notifications'
        handle_game_sync_notification(parsed_message)
      when 'game:global:events'
        handle_game_global_event(parsed_message)
      when 'manager:sync:events'
        handle_manager_sync_event(parsed_message)
      else
        logger.warn "Unhandled Redis channel: #{channel}"
      end

    rescue JSON::ParserError => e
      logger.error "Failed to parse Redis message", {
        channel: channel,
        message: message,
        error: e.message
      }
    rescue StandardError => e
      logger.error "Error handling Redis message", {
        channel: channel,
        error: e.message,
        backtrace: e.backtrace.first(5)
      }
    end
  end

  def handle_game_sync_notification(message)
    event_type = message.dig('event', 'type') || message['type']

    case event_type
    when 'player_left_room'
      handle_player_left_room_sync(message)
    when 'enhanced_player_left_room'
      handle_enhanced_player_left_room_sync(message)
    when 'player_joined_room'
      handle_player_joined_room_sync(message)
    when 'enhanced_player_joined_room'
      handle_enhanced_player_joined_room_sync(message)
    else
      logger.debug "Unhandled game sync notification type: #{event_type}"
    end
  end

  def handle_game_global_event(message)
    event_type = message['type']

    case event_type
    when 'enhanced_player_left_room'
      handle_enhanced_player_left_room_sync(message)
    when 'player_left_room'
      handle_player_left_room_sync(message)
    when 'enhanced_player_joined_room'
      handle_enhanced_player_joined_room_sync(message)
    when 'player_joined_room'
      handle_player_joined_room_sync(message)
    else
      logger.debug "Unhandled game global event type: #{event_type}"
    end
  end

  def handle_manager_sync_event(message)
    event_type = message['type']

    case event_type
    when 'sync_player_leave'
      handle_sync_player_leave(message)
    when 'sync_player_join'
      handle_sync_player_join(message)
    else
      logger.debug "Unhandled manager sync event type: #{event_type}"
    end
  end

  def handle_player_left_room_sync(message)
    payload = message.dig('event', 'payload') || message
    room_id = payload['room_id'] || payload['external_room_id']
    user_id = payload['user_id']
    reason = payload['reason'] || 'Player left room'

    logger.info "Processing player left room sync", {
      room_id: room_id,
      user_id: user_id,
      reason: reason,
      source: 'redis_sync'
    }

    # Find the room by external_room_id
    room = Room.where(external_room_id: room_id).first
    unless room
      logger.warn "Room not found for player left sync", {
        external_room_id: room_id,
        user_id: user_id
      }
      return
    end

    # Find the user
    user = User.find(user_id) rescue nil
    unless user
      logger.warn "User not found for player left sync", {
        room_id: room.id,
        user_id: user_id
      }
      return
    end

    # Check if player is still in the room (avoid duplicate processing)
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      logger.info "Player already removed from room - sync not needed", {
        room_id: room.id,
        user_id: user_id,
        reason: reason
      }
      return
    end

    # Process the leave operation
    begin
      logger.info "Syncing player leave from Game Service", {
        room_id: room.id,
        user_id: user_id,
        reason: reason
      }

      # Use transaction for atomic operation
      Room.transaction do
        # Update session status
        session.metadata['leave_reason'] = reason
        session.metadata['left_at'] = Time.current.iso8601
        session.metadata['sync_source'] = 'game_service_redis'
        session.cancel!("Synced leave: #{reason}")

        # Update room player count
        room.remove_player!(user)

        logger.info "Successfully synced player leave from Game Service", {
          room_id: room.id,
          user_id: user_id,
          new_player_count: room.current_players,
          reason: reason
        }
      end

      # Publish local notifications (don't notify Game Service to avoid loops)
      begin
        @notification_service.publish_room_update(room, 'player_left_synced', {
          user_id: user_id,
          username: user.username,
          current_players: room.current_players,
          reason: reason,
          sync_source: 'game_service'
        })
      rescue StandardError => e
        logger.error "Failed to publish room update after sync", {
          room_id: room.id,
          user_id: user_id,
          error: e.message
        }
      end

    rescue StandardError => e
      logger.error "Failed to sync player leave from Game Service", {
        room_id: room.id,
        user_id: user_id,
        reason: reason,
        error: e.message,
        backtrace: e.backtrace.first(3)
      }
    end
  end

  def handle_enhanced_player_left_room_sync(message)
    # Enhanced version with more comprehensive data
    room_id = message['external_room_id'] || message['room_id']
    user_id = message['user_id']
    reason = message['reason'] || 'Enhanced leave'

    logger.info "Processing enhanced player left room sync", {
      room_id: room_id,
      user_id: user_id,
      reason: reason,
      enhanced: true,
      source: 'redis_sync'
    }

    # Delegate to the standard handler with enhanced metadata
    enhanced_message = {
      'event' => {
        'type' => 'player_left_room',
        'payload' => {
          'room_id' => room_id,
          'user_id' => user_id,
          'reason' => reason,
          'enhanced' => true
        }
      }
    }

    handle_player_left_room_sync(enhanced_message)
  end

  def handle_player_joined_room_sync(message)
    payload = message.dig('event', 'payload') || message
    room_id = payload['room_id'] || payload['external_room_id']
    user_id = payload['user_id']
    username = payload['username'] || 'Unknown'

    logger.info "Processing player joined room sync", {
      room_id: room_id,
      user_id: user_id,
      username: username,
      source: 'redis_sync'
    }

    # Convert to standard format and process
    join_message = {
      'type' => 'sync_player_join',
      'room_id' => room_id,
      'user_id' => user_id,
      'username' => username
    }

    handle_sync_player_join(join_message)
  end

  def handle_enhanced_player_joined_room_sync(message)
    # Enhanced version with more comprehensive data
    room_id = message['external_room_id'] || message['room_id']
    user_id = message['user_id']
    username = message['username'] || 'Unknown'

    logger.info "Processing enhanced player joined room sync", {
      room_id: room_id,
      user_id: user_id,
      username: username,
      enhanced: true,
      source: 'redis_sync'
    }

    # Delegate to the standard handler with enhanced metadata
    enhanced_message = {
      'type' => 'sync_player_join',
      'room_id' => room_id,
      'user_id' => user_id,
      'username' => username,
      'enhanced' => true
    }

    handle_sync_player_join(enhanced_message)
  end

  def handle_sync_player_leave(message)
    # Handle direct sync requests
    room_id = message['room_id']
    user_id = message['user_id']
    reason = message['reason'] || 'Sync request'

    logger.info "Processing sync player leave request", {
      room_id: room_id,
      user_id: user_id,
      reason: reason,
      source: 'direct_sync'
    }

    # Convert to standard format and process
    sync_message = {
      'event' => {
        'type' => 'player_left_room',
        'payload' => {
          'room_id' => room_id,
          'user_id' => user_id,
          'reason' => reason
        }
      }
    }

    handle_player_left_room_sync(sync_message)
  end

  def handle_sync_player_join(message)
    # Handle direct sync requests for player join
    room_id = message['room_id']
    user_id = message['user_id']
    username = message['username']

    logger.info "Processing sync player join request", {
      room_id: room_id,
      user_id: user_id,
      username: username,
      source: 'direct_sync'
    }

    # Find the room by external_room_id
    room = Room.where(external_room_id: room_id).first
    unless room
      logger.warn "Room not found for player join sync", {
        external_room_id: room_id,
        user_id: user_id,
        username: username
      }
      return
    end

    # Find the user
    user = User.find(user_id) rescue nil
    unless user
      logger.warn "User not found for player join sync", {
        room_id: room.id,
        user_id: user_id,
        username: username
      }
      return
    end

    # Check if player is already in the room (avoid duplicate processing)
    existing_session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    if existing_session
      logger.info "Player already in room - sync not needed", {
        room_id: room.id,
        user_id: user_id,
        username: username,
        session_id: existing_session.session_id
      }
      return
    end

    # Process the join operation
    begin
      logger.info "Syncing player join from Game Service", {
        room_id: room.id,
        user_id: user_id,
        username: username
      }

      # Use transaction for atomic operation
      Room.transaction do
        # Add player to room
        room.add_player!(user)

        logger.info "Successfully synced player join from Game Service", {
          room_id: room.id,
          user_id: user_id,
          username: username,
          new_player_count: room.current_players
        }
      end

      # Publish local notifications (don't notify Game Service to avoid loops)
      begin
        # REMOVED: player_joined_synced notification publishing
        # All player events are now disabled to prevent duplicate events
        logger.debug "Skipped publishing player_joined_synced notification - event publishing disabled"
      rescue StandardError => e
        logger.error "Failed to publish room update after join sync", {
          room_id: room.id,
          user_id: user_id,
          username: username,
          error: e.message
        }
      end

    rescue StandardError => e
      logger.error "Failed to sync player join from Game Service", {
        room_id: room.id,
        user_id: user_id,
        username: username,
        error: e.message,
        backtrace: e.backtrace.first(3)
      }
    end
  end
end
