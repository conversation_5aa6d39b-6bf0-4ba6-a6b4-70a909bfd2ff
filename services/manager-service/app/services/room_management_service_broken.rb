class RoomManagementService
  include <PERSON><PERSON>

  def initialize
    @logger = Rails.logger
    @game_client = GameServiceClient.instance
    @balance_service = BalanceService.instance
    @notification_service = NotificationService.new
    @redis = $redis
  end

  # Create a new room with Game Service integration
  def create_room(creator, room_params)
    validate_creator_permissions(creator)
    validate_room_params(room_params)

    room = nil

    # Create room in Manager Service
    room = Room.new(
      name: room_params[:name],
      game_type: room_params[:game_type],
      creator: creator,
      max_players: room_params[:max_players],
      bet_amount: room_params[:bet_amount],
      currency: room_params[:currency] || 'USD',
      is_private: room_params[:is_private] || false,
      configuration: build_room_configuration(room_params),
      game_specific_config: room_params[:game_specific_config] || {}
    )

    # Set password if provided
    room.password = room_params[:password] if room_params[:password].present?

    room.save!

    # Create room in Game Service
    begin
      grpc_response = @game_client.create_room(build_grpc_room_params(room))
      # FIXED: Store Game Service ID in metadata, keep external_room_id as Manager Service ID
      room.update!(
        metadata: (room.metadata || {}).merge({
          'game_service_room_id' => grpc_response.room.id
        }),
        sync_required: false,
        last_synced_at: Time.current
      )

      @logger.info "Room created in Game Service - Manager ID: #{room.external_room_id} - Game Service ID: #{grpc_response.room.id}"
    rescue StandardError => e
      @logger.error "Failed to create room in Game Service - Room: #{room.id} - Error: #{e.message}"

      # Mark for later sync
      room.update!(sync_required: true)

      # Schedule retry
      RoomSettingsSyncJob.perform_in(30.seconds, room.id)
    end

    @logger.info "Room created successfully - Room: #{room.id} - External: #{room.external_room_id} - Creator: #{creator.id} - Game Type: #{room.game_type}"

    room
  end

  # Add a player to a room
  def add_player_to_room(room, user, password: nil)
    validate_room_join(room, user, password)

    # Check for existing session (reconnection)
    existing_session = find_existing_session(room, user)
    is_reconnection = existing_session.present?

    if is_reconnection
      handle_player_reconnection(room, user, existing_session)
    else
      handle_new_player_join(room, user)
    end
  end

  private

  # Handles player reconnection to existing room
  def handle_player_reconnection(room, user, existing_session)
    @logger.info "Player reconnecting to room - Room: #{room.id} - User: #{user.id} - Session: #{existing_session.session_id}"

    # Find existing transaction record
    transaction_record = @balance_service.find_reservation(user, room.id.to_s, 'Room')

    # Notify Game Service of reconnection
    notify_game_service_join(room, user, is_reconnection: true)

    @logger.info "Player reconnected to room - Room: #{room.id} - User: #{user.id}"

    { transaction_record: transaction_record, is_reconnection: true }
  end

  # Handles new player joining room
  def handle_new_player_join(room, user)
    # Reserve balance for the bet
    transaction_record = reserve_player_balance(room, user)

    # Add player to room
    room.add_player!(user)

    begin
      # Notify Game Service
      notify_game_service_join(room, user, is_reconnection: false)

      # REMOVED: player_joined notification publishing
      # All player events are now disabled to prevent duplicate events
      @logger.debug "Skipped publishing player joined notification - event publishing disabled"

      @logger.info "Player added to room - Room: #{room.id} - User: #{user.id} - Transaction: #{transaction_record&.transaction_id}"

      { transaction_record: transaction_record, is_reconnection: false }
    rescue StandardError => e
      # Rollback on failure
      rollback_player_join(room, user, transaction_record)
      raise e
    end
  end

  # Finds existing session for player in room
  def find_existing_session(room, user)
    room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
  end

  # Reserves balance for player's bet
  def reserve_player_balance(room, user)
    @balance_service.reserve_balance(
      user,
      room.bet_amount,
      room.id.to_s,
      'Room',
      "Bet for room: #{room.name}"
    )
  end

  # Notifies Game Service of player join
  def notify_game_service_join(room, user, is_reconnection:)
    @game_client.join_room(room.external_room_id, user.id.to_s)
    @logger.info "Successfully notified Game Service of player #{is_reconnection ? 'reconnection' : 'join'} - Room: #{room.id} - User: #{user.id}"
  rescue StandardError => e
    @logger.error "Failed to notify Game Service of player #{is_reconnection ? 'reconnection' : 'join'} - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
    raise e
  end

  # REMOVED: publish_player_joined_notification method
  # All player events are now disabled to prevent duplicate events
  def publish_player_joined_notification(room, user)
    @logger.debug "publish_player_joined_notification called but disabled - preventing duplicate events"
  end

  # Rollbacks player join on failure
  def rollback_player_join(room, user, transaction_record)
    begin
      # Remove player from room if join failed
      room.remove_player!(user)
      @logger.info "Rolled back player join due to Game Service notification failure - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => rollback_error
      @logger.error "Failed to rollback player join - Room: #{room.id} - User: #{user.id} - Error: #{rollback_error.message}"
    end

    # Release reserved balance if join failed
    if transaction_record
      begin
        @balance_service.release_reserved_balance(transaction_record.transaction_id)
        @logger.info "Released reserved balance due to join failure - Room: #{room.id} - User: #{user.id} - Transaction: #{transaction_record.transaction_id}"
      rescue StandardError => balance_error
        @logger.error "Failed to release reserved balance - Room: #{room.id} - User: #{user.id} - Error: #{balance_error.message}"
      end
    end
  end

  public

  # Remove a player from a room with enhanced synchronization
  def remove_player_from_room(room, user, reason: 'Player left')
    @logger.info "Starting enhanced remove player from room - Room: #{room.id} - User: #{user.id} - Reason: #{reason} - Status: #{room.status} - Players: #{room.current_players}"

    # Use transaction for atomic operations
    Room.transaction do
      # Reload room to get latest state
      room.reload

      # Find and validate session
      session = find_and_validate_session_for_removal(room, user)
      return session if session.is_a?(Boolean) # Early return for idempotent behavior

      # Execute removal process
      execute_player_removal(room, user, session, reason)
    end
  end

  private

  # Finds and validates session for player removal
  def find_and_validate_session_for_removal(room, user)
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first

    unless session
      @logger.warn "No active session found for user in room - Room: #{room.id} - User: #{user.id} - Available sessions: #{room.game_sessions.where(user: user).pluck(:status).join(', ')}"

      # Check if user was already removed (idempotent behavior)
      cancelled_session = room.game_sessions.where(user: user, status: 'cancelled').first
      if cancelled_session
        @logger.info "User already left room (found cancelled session) - Room: #{room.id} - User: #{user.id} - Session: #{cancelled_session.session_id}"
        return true # Return success for idempotent behavior
      end

      return false
    end

    @logger.debug "Found session for user - Room: #{room.id} - User: #{user.id} - Session: #{session.session_id} - Status: #{session.status} - Ready: #{session.metadata['is_ready']}"
    session
  end

  # Executes the complete player removal process
  def execute_player_removal(room, user, session, reason)
    begin
      # Remove player from room
      remove_player_from_room_model(room, user)

      # Handle balance release
      handle_balance_release_for_removal(user, session, room)

      # Notify external services
      notify_services_of_player_removal(room, user, reason)

      @logger.info "Enhanced remove player completed successfully - Room: #{room.id} - User: #{user.id} - Current players: #{room.current_players} - Reason: #{reason}"
      true
    rescue Mongoid::Errors::Validations => e
      handle_validation_error(room, user, e)
    rescue StandardError => e
      handle_removal_error(room, user, e)
    end
  end

  # Removes player from room model
  def remove_player_from_room_model(room, user)
    @logger.debug "Removing player from room model"
    room.remove_player!(user)
    @logger.debug "Player removed from room model, new count: #{room.current_players}"
  end

  # Handles balance release for player removal
  def handle_balance_release_for_removal(user, session, room)
    if session.status == 'pending'
      @logger.debug "Looking for reserved transaction to release"
      reserved_transaction = find_reserved_transaction(user, room)

      if reserved_transaction
        release_reserved_balance(reserved_transaction)
      else
        @logger.debug "No reserved transaction found to release"
      end
    else
      @logger.debug "Session not pending, skipping balance release - Status: #{session.status}"
    end
  end

  # Finds reserved transaction for user and room
  def find_reserved_transaction(user, room)
    user.transactions.find_by(
      reference_id: room.id.to_s,
      reference_type: 'Room',
      status: 'pending'
    )
  end

  # Releases reserved balance
  def release_reserved_balance(reserved_transaction)
    @logger.debug "Releasing reserved balance - Transaction: #{reserved_transaction.transaction_id} - Amount: #{reserved_transaction.amount}"
    @balance_service.release_reserved_balance(reserved_transaction.transaction_id)
    @logger.debug "Reserved balance released successfully"
  end

  # Notifies external services of player removal
  def notify_services_of_player_removal(room, user, reason)
    # Notify Game Service
    notify_game_service_of_removal(room, user)

    # Publish room update notification
    publish_room_update_for_removal(room, user, reason)

    # Publish lobby update
    publish_lobby_update_for_removal(room)

    # Notify Game Service for cache synchronization
    publish_player_left_notification(room, user, reason)
  end

  # Notifies Game Service of player removal
  def notify_game_service_of_removal(room, user)
    begin
      @logger.debug "Notifying Game Service of player leave"
      @game_client.leave_room(room.external_room_id, user.id.to_s)
      @logger.debug "Game Service notified successfully"
    rescue StandardError => e
      @logger.error "Failed to notify Game Service of player leave - Room: #{room.id} - User: #{user.id} - External Room: #{room.external_room_id} - Error: #{e.message} - Class: #{e.class.name}"
      # Don't re-raise - this is not critical for the operation
    end
  end

  # Publishes room update notification for removal
  def publish_room_update_for_removal(room, user, reason)
    begin
      @logger.debug "Publishing room update notification"
      @notification_service.publish_room_update(room, 'player_left', {
        user_id: user.id.to_s,
        username: user.username,
        current_players: room.current_players,
        reason: reason
      })
      @logger.debug "Room update notification published"
    rescue StandardError => e
      @logger.error "Failed to publish room update notification - Room: #{room.id} - Error: #{e.message}"
    end
  end

  # Publishes lobby update for removal
  def publish_lobby_update_for_removal(room)
    begin
      @logger.debug "Publishing lobby update"
      publish_lobby_update('player_count_changed', room)
      @logger.debug "Lobby update published"
    rescue StandardError => e
      @logger.error "Failed to publish lobby update - Room: #{room.id} - Error: #{e.message}"
    end
  end

  # Publishes player left notification
  def publish_player_left_notification(room, user, reason)
    begin
      @notification_service.publish_player_left_room(room, user, reason)
      @logger.info "Published player left notification to Game Service - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      @logger.error "Failed to publish player left notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the leave operation if notification fails
    end
  end

  # Handles validation errors during removal
  def handle_validation_error(room, user, error)
    @logger.error "Validation error removing player from room - Room: #{room.id} - User: #{user.id} - Validation errors: #{error.document.errors.full_messages.join(', ')} - Error: #{error.message}"
    raise ArgumentError, "Room validation failed: #{error.document.errors.full_messages.join(', ')}"
  end

  # Handles general errors during removal
  def handle_removal_error(room, user, error)
    @logger.error "Failed to remove player from room - Room: #{room.id} - User: #{user.id} - Error: #{error.message} - Class: #{error.class.name} - Backtrace: #{error.backtrace.first(5).join(', ')}"
    raise error
  end

  public

  # Enhanced remove player from room with comprehensive validation and cleanup
  def enhanced_remove_player_from_room(room, user, reason: 'Player left')
    @logger.info "Starting enhanced remove player from room - Room: #{room.id} - User: #{user.id} - Reason: #{reason} - Status: #{room.status} - Players: #{room.current_players}"

    # Use atomic operations (Mongoid doesn't have transactions like ActiveRecord)
    begin
      # Reload room to get latest state
      room.reload

      # Find the session
      session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
      unless session
        @logger.warn "No active session found for enhanced remove - Room: #{room.id} - User: #{user.id}"
        return false
      end

      # Check if player is ready and reason is voluntary (but allow kicks)
      is_ready = session.metadata['is_ready'] || false
      is_voluntary = reason == 'voluntary'
      is_kick = reason.to_s.include?('kicked')

      if is_ready && is_voluntary && !is_kick
        @logger.warn "Enhanced remove blocked - ready player attempting voluntary leave - Room: #{room.id} - User: #{user.id}"
        raise ArgumentError, "Cannot leave room while in ready state. Please unready first."
      end

      @logger.debug "Enhanced remove validation passed - Room: #{room.id} - User: #{user.id} - Ready: #{is_ready} - Voluntary: #{is_voluntary} - Kick: #{is_kick}"

      begin
        # Remove player from room with enhanced tracking
        @logger.debug "Removing player from room model with enhanced tracking"
        room.remove_player!(user)
        @logger.debug "Player removed from room model, new count: #{room.current_players}"

        # Enhanced session cleanup
        session.metadata['leave_reason'] = reason
        session.metadata['left_at'] = Time.current.iso8601
        session.metadata['enhanced_leave'] = true
        session.cancel!("Enhanced leave: #{reason}")
        @logger.debug "Enhanced session cleanup completed"

        # Notify Game Service with enhanced data
        begin
          @logger.debug "Notifying Game Service of enhanced player leave"
          @game_client.leave_room(room.external_room_id, user.id.to_s)
          @logger.debug "Game Service notified successfully for enhanced leave"
        rescue StandardError => e
          @logger.error "Failed to notify Game Service of enhanced player leave - Room: #{room.id} - User: #{user.id} - External Room: #{room.external_room_id} - Error: #{e.message} - Class: #{e.class.name}"
          # Don't re-raise - this is not critical for the operation
        end

        # Enhanced lobby update with additional metadata
        begin
          @logger.debug "Publishing enhanced lobby update"
          publish_enhanced_lobby_update('enhanced_player_left', room, user, reason)
          @logger.debug "Enhanced lobby update published"
        rescue StandardError => e
          @logger.error "Failed to publish enhanced lobby update - Room: #{room.id} - Error: #{e.message}"
        end

        # Enhanced notification to Game Service for cache synchronization
        begin
          @notification_service.publish_enhanced_player_left_room(room, user, reason)
          @logger.info "Published enhanced player left notification to Game Service - Room: #{room.id} - User: #{user.id}"
        rescue StandardError => e
          @logger.error "Failed to publish enhanced player left notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
          # Don't fail the leave operation if notification fails
        end

        @logger.info "Enhanced remove player completed successfully - Room: #{room.id} - User: #{user.id} - Current players: #{room.current_players} - Reason: #{reason}"
        true
      rescue Mongoid::Errors::Validations => e
        @logger.error "Validation error in enhanced remove player - Room: #{room.id} - User: #{user.id} - Validation errors: #{e.document.errors.full_messages.join(', ')} - Error: #{e.message}"
        raise ArgumentError, "Room validation failed: #{e.document.errors.full_messages.join(', ')}"
      rescue StandardError => e
        @logger.error "Failed enhanced remove player from room - Room: #{room.id} - User: #{user.id} - Error: #{e.message} - Class: #{e.class.name} - Backtrace: #{e.backtrace.first(5).join(', ')}"
        raise e
      end
  end

  # Enhanced lobby update with additional metadata
  def publish_enhanced_lobby_update(event_type, room, user = nil, reason = nil)
    lobby_data = {
      event_type: event_type,
      room_id: room.id.to_s,
      external_room_id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: room.current_players,
      max_players: room.max_players,
      bet_amount: room.bet_amount.to_f,
      is_private: room.is_private,
      enhanced: true,
      timestamp: Time.current.iso8601
    }

    if user
      lobby_data[:user] = {
        id: user.id.to_s,
        username: user.username
      }
    end

    if reason
      lobby_data[:reason] = reason
    end

    @notification_service.publish_lobby_update(event_type, lobby_data)
  rescue StandardError => e
    @logger.error "Failed to publish enhanced lobby update - Room: #{room.id} - Event: #{event_type} - Error: #{e.message}"
  end

  # Set player ready status
  def set_player_ready(room, user, is_ready)
    session = room.set_player_ready!(user, is_ready)

    @logger.info "Player ready status updated - Room: #{room.id} - User: #{user.id} - Ready: #{is_ready} - Prize Pool: #{room.prize_pool} - Ready Count: #{room.ready_players_count}"

    # Publish notification
    @notification_service.publish_room_update(room, 'player_ready_status', {
      user_id: user.id.to_s,
      username: user.username,
      is_ready: is_ready,
      ready_count: room.ready_players_count,
      total_players: room.current_players,
      prize_pool: room.prize_pool
    })

    session
  end

  # Start a game in a room
  def start_room_game(room)
    raise ArgumentError, "Room not in waiting state" unless room.waiting?
    raise ArgumentError, "Not enough players" if room.current_players < 2

    # Confirm all reserved balances
    room.game_sessions.pending.each do |session|
      reserved_transaction = session.user.transactions.find_by(
        reference_id: room.id.to_s,
        reference_type: 'Room',
        status: 'pending'
      )

      if reserved_transaction
        @balance_service.confirm_reserved_balance(reserved_transaction.transaction_id)
      end
    end

    # Start the game
    room.start_game!

    # Notify Game Service
    begin
      @game_client.start_game(room.external_room_id)
    rescue StandardError => e
      @logger.error "Failed to start game in Game Service - Room: #{room.id} - Error: #{e.message}"
      raise e
    end

    @logger.info "Game started - Room: #{room.id} - Players: #{room.current_players}"

    # Publish notification
    @notification_service.publish_room_update(room, 'game_started', {
      players_count: room.current_players,
      started_at: Time.current.iso8601
    })

    room
  end

  # Kick a player from a room (admin action)
  def kick_player_from_room(room, user, reason: 'Kicked by administrator', kicked_by_user: nil)
    @logger.info "Starting kick player from room - Room: #{room.id} - User: #{user.id} - Reason: #{reason} - Kicked by: #{kicked_by_user&.username || 'System'}"

    # Validate kick eligibility
    session = validate_kick_eligibility(room, user)
    refund_amount = session.bet_amount

    begin
      # Execute the kick
      execute_player_kick(room, user)

      # Handle post-kick notifications and logging
      handle_kick_notifications(room, user, reason, kicked_by_user)

      # Create audit log
      create_kick_audit_log(room, user, reason, kicked_by_user, refund_amount)

      # Return success response
      build_kick_success_response(room, user, reason, kicked_by_user, refund_amount)
    rescue StandardError => e
      handle_kick_error(room, user, e)
    end
  end

  private

  # Validates that user can be kicked from room
  def validate_kick_eligibility(room, user)
    session = room.game_sessions.where(user: user).in(status: ['pending', 'active']).first
    unless session
      @logger.warn "No active session found for user to kick - Room: #{room.id} - User: #{user.id}"
      raise ArgumentError, "User is not in this room"
    end
    session
  end

  # Executes the player kick operation
  def execute_player_kick(room, user)
    # Remove player from room (this handles refunds automatically)
    room.remove_player!(user)
    @logger.info "Player kicked successfully - Room: #{room.id} - User: #{user.id} - Remaining players: #{room.current_players}"

    # Check if room is now empty and handle accordingly
    handle_empty_room_after_kick(room) if room.current_players == 0
  end

  # Handles all notifications related to player kick
  def handle_kick_notifications(room, user, reason, kicked_by_user)
    # Notify other services about the kick
    publish_kick_notification_to_services(room, user, reason, kicked_by_user)

    # Notify the kicked player directly
    notify_kicked_player(room, user, reason)
  end

  # Publishes kick notification to other services
  def publish_kick_notification_to_services(room, user, reason, kicked_by_user)
    begin
      @notification_service.publish_player_kicked(room, user, reason, kicked_by_user&.username || 'System')
      @logger.info "Published kick notification to other services - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      @logger.error "Failed to publish kick notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the kick operation if notification fails
    end
  end

  # Notifies the kicked player directly
  def notify_kicked_player(room, user, reason)
    begin
      @notification_service.notify_kicked_from_room(user, room, reason)
      @logger.info "Sent kick notification to user - Room: #{room.id} - User: #{user.id}"
    rescue StandardError => e
      @logger.error "Failed to send user kick notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
      # Don't fail the kick operation if user notification fails
    end
  end

  # Creates audit log entry for kick action
  def create_kick_audit_log(room, user, reason, kicked_by_user, refund_amount)
    audit_data = {
      action_type: 'kick_player',
      room_id: room.id.to_s,
      room_name: room.name,
      target_user_id: user.id.to_s,
      target_username: user.username,
      reason: reason,
      performed_by: kicked_by_user&.id&.to_s || 'system',
      performed_by_username: kicked_by_user&.username || 'System',
      performed_at: Time.current.iso8601,
      refund_amount: refund_amount.to_f
    }

    @logger.info "Kick player audit log: #{audit_data.to_json}"
  end

  # Builds success response for kick operation
  def build_kick_success_response(room, user, reason, kicked_by_user, refund_amount)
    {
      success: true,
      kicked_user: {
        id: user.id.to_s,
        username: user.username
      },
      room: {
        id: room.id.to_s,
        name: room.name,
        current_players: room.current_players
      },
      refund_amount: refund_amount.to_f,
      reason: reason,
      kicked_by: kicked_by_user&.username || 'System'
    }
  end

  # Handles empty room after kick operation
  def handle_empty_room_after_kick(room)
    @logger.info "Room is now empty after kick - Room: #{room.id} - Name: #{room.name}"

    # For now, we'll keep the room alive but mark it as waiting
    # This prevents the "room not found" issue in Game Service
    # The room can be cleaned up later by scheduled cleanup jobs

    # Ensure room status is waiting (not cancelled)
    if room.status != 'waiting'
      room.update!(status: 'waiting')
      @logger.info "Reset empty room status to waiting - Room: #{room.id}"
    end

    # Publish lobby update to show empty room
    publish_lobby_update('updated', room)

    @logger.info "Empty room after kick handled - Room: #{room.id} - Status: #{room.status} - Players: #{room.current_players}"
  end

  # Handles errors during kick operation
  def handle_kick_error(room, user, error)
    @logger.error "Failed to kick player - Room: #{room.id} - User: #{user.id} - Error: #{error.message}"
    @logger.error error.backtrace.join("\n")
    raise error
  end

  public

  # Complete a game with results
  def complete_room_game(room, results)
    raise ArgumentError, "Room not in playing state" unless room.playing?
    raise ArgumentError, "Results cannot be empty" if results.blank?

    # Process game results and update balances
    process_game_results(room, results)

    # Finish the game
    room.finish_game!(results)

    @logger.info "Game completed - Room: #{room.id} - Results: #{results}"

    # Publish notification
    @notification_service.publish_room_update(room, 'game_completed', {
      results: results,
      completed_at: Time.current.iso8601
    })

    room
  end

  # Cancel a room and refund players
  def cancel_room(room, reason: 'Room cancelled', refund_players: true)
    players_refunded = 0
    total_refund_amount = 0.0

    if refund_players && room.current_players > 0
      room.game_sessions.where(status: ['pending', 'active']).each do |session|
        # Release reserved balance or refund completed bet
        if session.status == 'pending'
          reserved_transaction = session.user.transactions.find_by(
            reference_id: room.id.to_s,
            reference_type: 'Room',
            status: 'pending'
          )

          if reserved_transaction
            @balance_service.release_reserved_balance(reserved_transaction.transaction_id)
            players_refunded += 1
            total_refund_amount += session.bet_amount
          end
        else
          # Refund active bet
          @balance_service.update_balance(
            session.user,
            session.bet_amount,
            'refund',
            description: "Refund for cancelled room: #{reason}",
            reference_id: room.id.to_s,
            reference_type: 'Room'
          )
          players_refunded += 1
          total_refund_amount += session.bet_amount
        end
      end
    end

    # Cancel the room
    room.cancel_game!(reason)

    # Notify Game Service
    begin
      @game_client.delete_room(room.external_room_id, reason)
    rescue StandardError => e
      @logger.error "Failed to delete room from Game Service - Room: #{room.id} - Error: #{e.message}"
    end

    @logger.info "Room cancelled - Room: #{room.id} - Reason: #{reason} - Players refunded: #{players_refunded} - Total refund: #{total_refund_amount}"

    # Publish notification
    @notification_service.publish_room_update(room, 'room_cancelled', {
      reason: reason,
      players_refunded: players_refunded,
      total_refund_amount: total_refund_amount
    })

    {
      players_refunded: players_refunded,
      total_refund_amount: total_refund_amount
    }
  end

  # Update room settings
  def update_room_settings(room, settings)
    room.update!(settings)
    room.update!(sync_required: true)

    # Schedule sync with Game Service
    RoomSettingsSyncJob.perform_async(room.id)

    @logger.info "Room settings updated - Room: #{room.id} - Settings: #{settings.keys.join(', ')}"

    # Publish lobby update event for admin setting changes
    publish_lobby_update('updated', room)

    room
  end

  private

  def validate_creator_permissions(creator)
    raise ArgumentError, "Creator must be active" unless creator.active?
    raise ArgumentError, "Creator must have sufficient balance" if creator.balance < 0
  end

  def validate_room_params(params)
    required_fields = [:name, :game_type, :max_players, :bet_amount]
    required_fields.each do |field|
      raise ArgumentError, "#{field} is required" unless params[field].present?
    end

    raise ArgumentError, "Invalid game type" unless %w[prizewheel amidakuji].include?(params[:game_type])
    raise ArgumentError, "Max players must be between 2 and 8" unless (2..8).include?(params[:max_players])
    raise ArgumentError, "Bet amount must be positive" unless params[:bet_amount] > 0
  end

  def validate_room_join(room, user, password)
    raise ArgumentError, "Room is not accepting players" unless room.waiting?
    raise ArgumentError, "Room is full" if room.current_players >= room.max_players
    raise ArgumentError, "User must be active" unless user.active?
    raise ArgumentError, "User has insufficient balance" if user.balance < room.bet_amount

    if room.is_private && !room.valid_password?(password)
      raise ArgumentError, "Invalid room password"
    end
  end

  def build_room_configuration(params)
    {
      game_duration: params[:game_duration] || 30,
      created_by_service: 'manager-service',
      created_at: Time.current.iso8601
    }
  end

  def build_grpc_room_params(room)
    {
      creator_id: room.creator_id.to_s,
      name: room.name,
      game_type: room.game_type,
      max_players: room.max_players,
      bet_amount: room.bet_amount,
      currency: room.currency,
      game_duration: room.configuration['game_duration'],
      game_specific_config: room.game_specific_config,
      is_private: room.is_private,
      password: room.password
    }
  end

  def process_game_results(room, results)
    return unless results[:players]

    results[:players].each do |player_result|
      user = User.find(player_result[:user_id])
      win_amount = player_result[:win_amount] || 0

      if win_amount > 0
        @balance_service.update_balance(
          user,
          win_amount,
          'bet_won',
          description: "Won #{win_amount} in room: #{room.name}",
          reference_id: room.id.to_s,
          reference_type: 'Room',
          metadata: {
            game_type: room.game_type,
            room_name: room.name,
            win_amount: win_amount,
            game_results: player_result
          }
        )
      end
    end
  end

  # Publish lobby update event to Redis
  def publish_lobby_update(action, room)
    begin
      message = {
        event: {
        type: 'room_list_updated',
        payload: {
          action: action,
          room: format_room_for_lobby(room)
        },
          timestamp: Time.current.iso8601
        },
        metadata: {
          service_id: 'manager-service',
          version: '1.0.0',
          correlation_id: "lobby-#{Time.current.to_f}",
          priority: 1
        }
      }

      channel = 'game:lobby:updates'
      @redis.publish(channel, message.to_json)

      @logger.debug "Lobby update published - Channel: #{channel} - Action: #{action} - Room: #{room.id}"
    rescue => e
      @logger.error "Failed to publish lobby update - Error: #{e.message} - Action: #{action} - Room: #{room.id}"
    end
  end

  # Format room data for lobby display
  def format_room_for_lobby(room)
    {
      id: room.external_room_id || room.id.to_s,
      name: room.name,
      game_type: room.game_type,
      player_count: room.current_players,
      max_players: room.max_players,
      status: room.status,
      bet_amount: room.bet_amount,
      created_at: room.created_at.iso8601
    }
  end
end
