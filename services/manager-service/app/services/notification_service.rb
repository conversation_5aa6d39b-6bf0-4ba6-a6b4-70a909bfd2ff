class NotificationService
  include ActiveModel::Model

  def initialize
    @redis = Redis.new(url: ENV['REDIS_URL'] || 'redis://localhost:6379')
    @logger = Rails.logger
  end

  def publish_balance_update(user, transaction)
    message = {
      type: 'balance_update',
      user_id: user.id.to_s,
      username: user.username,
      balance: user.balance.to_f,
      transaction: {
        id: transaction.id.to_s,
        transaction_id: transaction.transaction_id,
        type: transaction.transaction_type,
        amount: transaction.amount.to_f,
        description: transaction.description
      },
      timestamp: Time.current.iso8601
    }

    publish_to_channel("user:#{user.id}:balance", message)
    publish_to_channel('global:balance_updates', message)
  rescue StandardError => e
    @logger.error "Failed to publish balance update - User: #{user.id} - Error: #{e.message}"
  end

  def publish_room_update(room, event_type, additional_data = {})
    message = {
      type: 'room_update',
      event_type: event_type,
      room_id: room.id.to_s,
      external_room_id: room.external_room_id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: room.current_players,
      max_players: room.max_players,
      timestamp: Time.current.iso8601
    }.merge(additional_data)

    publish_to_channel("room:#{room.id}", message)
    publish_to_channel("game_type:#{room.game_type}", message)
    publish_to_channel('global:room_updates', message)
  rescue StandardError => e
    @logger.error "Failed to publish room update - Room: #{room.id} - Error: #{e.message}"
  end

  def publish_game_session_update(game_session, event_type)
    message = {
      type: 'game_session_update',
      event_type: event_type,
      session_id: game_session.session_id,
      game_type: game_session.game_type,
      status: game_session.status,
      user_id: game_session.user_id.to_s,
      timestamp: Time.current.iso8601
    }

    publish_to_channel("user:#{game_session.user_id}:sessions", message)
    publish_to_channel("session:#{game_session.session_id}", message)
    publish_to_channel('global:session_updates', message)
  rescue StandardError => e
    @logger.error "Failed to publish game session update - Session: #{game_session.session_id} - Error: #{e.message}"
  end

  def publish_game_settings_update(game_type, settings_hash)
    message = {
      type: 'game_settings_update',
      game_type: game_type,
      settings: settings_hash,
      timestamp: Time.current.iso8601
    }

    publish_to_channel("game_settings:#{game_type}", message)
    publish_to_channel('global:settings_updates', message)
  rescue StandardError => e
    @logger.error "Failed to publish game settings update - Game Type: #{game_type} - Error: #{e.message}"
  end

  def publish_transaction_update(transaction, event_type)
    message = {
      type: 'transaction_update',
      event_type: event_type,
      transaction_id: transaction.transaction_id,
      transaction_type: transaction.transaction_type,
      amount: transaction.amount.to_f,
      status: transaction.status,
      user_id: transaction.user_id.to_s,
      timestamp: Time.current.iso8601
    }

    publish_to_channel("user:#{transaction.user_id}:transactions", message)
    publish_to_channel('global:transaction_updates', message)
  rescue StandardError => e
    @logger.error "Failed to publish transaction update - Transaction: #{transaction.transaction_id} - Error: #{e.message}"
  end

  def publish_user_notification(user_ids, notification_type, data)
    user_ids = Array(user_ids)

    message = {
      type: 'user_notification',
      notification_type: notification_type,
      data: data,
      timestamp: Time.current.iso8601
    }

    user_ids.each do |user_id|
      publish_to_channel("user:#{user_id}:notifications", message)
    end

    publish_to_channel('global:user_notifications', message.merge(user_ids: user_ids))
  rescue StandardError => e
    @logger.error "Failed to publish user notification - Type: #{notification_type} - Users: #{user_ids.join(', ')} - Error: #{e.message}"
  end

  def publish_system_announcement(message, target_users = nil)
    announcement = {
      type: 'system_announcement',
      message: message,
      target_users: target_users,
      timestamp: Time.current.iso8601
    }

    if target_users
      Array(target_users).each do |user_id|
        publish_to_channel("user:#{user_id}:announcements", announcement)
      end
    else
      publish_to_channel('global:announcements', announcement)
    end
  rescue StandardError => e
    @logger.error "Failed to publish system announcement - Message: #{message} - Error: #{e.message}"
  end



  def subscribe_to_channel(channel, &block)
    @redis.subscribe(channel) do |on|
      on.message do |channel, message|
        begin
          parsed_message = JSON.parse(message)
          yield(channel, parsed_message)
        rescue JSON::ParserError => e
          @logger.error "Failed to parse Redis message - Channel: #{channel} - Message: #{message} - Error: #{e.message}"
        end
      end
    end
  rescue StandardError => e
    @logger.error "Failed to subscribe to channel - Channel: #{channel} - Error: #{e.message}"
  end

  def get_channel_subscribers(pattern)
    @redis.pubsub('channels', pattern)
  rescue StandardError => e
    @logger.error "Failed to get channel subscribers - Pattern: #{pattern} - Error: #{e.message}"
    []
  end

  def publish_health_check
    message = {
      type: 'health_check',
      service: 'manager-service',
      status: 'healthy',
      timestamp: Time.current.iso8601
    }

    publish_to_channel('global:health', message)
  rescue StandardError => e
    @logger.error "Failed to publish health check", error: e.message
  end

  # Convenience methods for common notification types
  def notify_room_deleted(room, reason, refunded_players)
    publish_user_notification(
      refunded_players.map(&:id),
      'room_deleted',
      {
        room_name: room.name,
        reason: reason,
        refunded: true
      }
    )
  end

  def notify_room_settings_updated(room, settings_updated, players)
    publish_user_notification(
      players.map(&:id),
      'room_settings_updated',
      {
        room_name: room.name,
        room_id: room.id.to_s,
        settings_updated: settings_updated
      }
    )
  end

  def notify_kicked_from_room(user, room, reason)
    publish_user_notification(
      user.id,
      'kicked_from_room',
      {
        room_name: room.name,
        reason: reason,
        refunded: true
      }
    )
  end

  def notify_game_started(room, participants)
    publish_user_notification(
      participants.map(&:user_id),
      'game_started',
      {
        room_name: room.name,
        room_id: room.id.to_s,
        game_type: room.game_type,
        participants_count: participants.count
      }
    )
  end

  def notify_game_completed(room, results)
    participants = room.game_sessions.where(status: 'completed')

    participants.each do |session|
      publish_user_notification(
        session.user_id,
        'game_completed',
        {
          room_name: room.name,
          game_type: room.game_type,
          bet_amount: session.bet_amount.to_f,
          win_amount: session.win_amount.to_f,
          profit_loss: session.profit_loss.to_f,
          result: session.result
        }
      )
    end
  end

  # Publish player kicked notification to other services
  def publish_player_kicked(room, kicked_user, reason, kicked_by_username)
    # Use Manager Service room ID for Socket Gateway, Game Service room ID for Game Service
    manager_room_id = room.external_room_id
    game_service_room_id = room.game_service_room_id

    message = {
      event: {
        type: 'player_kicked',
        payload: {
          room_id: manager_room_id,
          game_service_room_id: game_service_room_id,
          room_name: room.name,
          user_id: kicked_user.id.to_s,
          username: kicked_user.username,
          reason: reason,
          kicked_by: kicked_by_username,
          current_players: room.current_players,
          timestamp: Time.current.iso8601
        }
      },
      metadata: {
        service_id: 'manager-service',
        version: '1.0.0',
        correlation_id: "kick-player-#{room.id}-#{kicked_user.id}-#{Time.current.to_f}",
        priority: 1
      }
    }

    # Publish to admin notifications channel for Game Service and Socket Gateway
    publish_to_channel('admin:notifications', message)

    # Also publish to room-specific channel using Socket Gateway format (Manager Service room ID)
    publish_to_channel("room:#{manager_room_id}:events", message)

    @logger.info "Published player kicked notification to multiple channels - Manager Room: #{manager_room_id} - Game Service Room: #{game_service_room_id} - User: #{kicked_user.id} - Reason: #{reason} - Current players: #{room.current_players}"
  rescue StandardError => e
    @logger.error "Failed to publish player kicked notification - Room: #{room.id} - User: #{kicked_user.id} - Error: #{e.message}"
  end

  # Publish player left room notification for Game Service synchronization
  def publish_player_left_room(room, user, reason)
    message = {
      event: {
        type: 'player_left_room',
        payload: {
          room_id: room.external_room_id || room.id.to_s,
          room_name: room.name,
          user_id: user.id.to_s,
          username: user.username,
          reason: reason,
          current_players: room.current_players,
          timestamp: Time.current.iso8601
        }
      },
      metadata: {
        service_id: 'manager-service',
        version: '1.0.0',
        correlation_id: "leave-#{user.id}-#{Time.current.to_f}",
        priority: 1
      }
    }

    publish_to_channel('game:sync:notifications', message)
    @logger.info "Published player left room notification - Room: #{room.id} - User: #{user.id} - Reason: #{reason}"
  rescue StandardError => e
    @logger.error "Failed to publish player left room notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
  end

  # Enhanced player left room notification with comprehensive data
  def publish_enhanced_player_left_room(room, user, reason)
    message = {
      type: 'enhanced_player_left_room',
      room_id: room.id.to_s,
      external_room_id: room.external_room_id,
      user_id: user.id.to_s,
      username: user.username,
      reason: reason,
      room_data: {
        name: room.name,
        game_type: room.game_type,
        status: room.status,
        current_players: room.current_players,
        max_players: room.max_players,
        bet_amount: room.bet_amount.to_f,
        is_private: room.is_private
      },
      user_data: {
        id: user.id.to_s,
        username: user.username,
        balance: user.balance.to_f
      },
      enhanced: true,
      timestamp: Time.current.iso8601
    }

    # Publish to multiple channels for comprehensive coverage
    channels = [
      'game:sync:notifications',
      'game:global:events',
      'socket:events',
      "room:#{room.external_room_id}:events"
    ]

    channels.each do |channel|
      publish_to_channel(channel, message)
    end

    @logger.info "Published enhanced player left room notification - Room: #{room.id} - User: #{user.id} - Reason: #{reason} - Channels: #{channels.size}"
  rescue StandardError => e
    @logger.error "Failed to publish enhanced player left room notification - Room: #{room.id} - User: #{user.id} - Error: #{e.message}"
  end

  private

  def publish_to_channel(channel, message)
    json_message = message.to_json
    @redis.publish(channel, json_message)

    @logger.debug "Published to Redis channel - Channel: #{channel} - Type: #{message[:type]}"
  rescue StandardError => e
    @logger.error "Failed to publish to Redis channel - Channel: #{channel} - Error: #{e.message}"
    raise e
  end
end
