class GameSettingsService
  include <PERSON><PERSON>

  def initialize
    @logger = Rails.logger
    @game_client = GameServiceClient.instance
  end

  # Get all game settings for a specific game type
  def get_game_settings(game_type)
    validate_game_type(game_type)
    
    settings = GameSetting.by_game_type(game_type).active.to_a
    config = {}

    settings.each do |setting|
      config[setting.setting_key] = setting.typed_value
    end

    @logger.debug "Retrieved game settings",
      game_type: game_type,
      settings_count: settings.size

    config
  end

  # Get global settings
  def get_global_settings
    get_game_settings('global')
  end

  # Update game settings for a specific game type
  def update_game_settings(game_type, settings_hash, sync_immediately: true)
    validate_game_type(game_type)
    validate_settings_hash(settings_hash)

    updated_settings = []
    
    GameSetting.transaction do
      settings_hash.each do |key, value|
        setting = GameSetting.set_setting(
          game_type,
          key.to_s,
          value,
          description: "Updated via GameSettingsService"
        )
        updated_settings << setting
      end
    end

    @logger.info "Game settings updated",
      game_type: game_type,
      updated_keys: settings_hash.keys,
      sync_immediately: sync_immediately

    # Sync with Game Service if requested
    if sync_immediately
      sync_game_settings_to_service(game_type, updated_settings)
    else
      # Schedule background sync
      GameSettingsSyncJob.perform_async(game_type)
    end

    updated_settings
  end

  # Update global settings
  def update_global_settings(settings_hash, sync_immediately: true)
    update_game_settings('global', settings_hash, sync_immediately: sync_immediately)
  end

  # Get a specific setting value
  def get_setting(game_type, key, default_value = nil)
    validate_game_type(game_type)
    GameSetting.get_setting(game_type, key, default_value)
  end

  # Set a specific setting value
  def set_setting(game_type, key, value, description: nil, sync_immediately: true)
    validate_game_type(game_type)
    
    setting = GameSetting.set_setting(
      game_type,
      key.to_s,
      value,
      description: description || "Updated via GameSettingsService"
    )

    @logger.info "Setting updated",
      game_type: game_type,
      key: key,
      value: value,
      sync_immediately: sync_immediately

    if sync_immediately
      sync_setting_to_service(setting)
    else
      GameSettingsSyncJob.perform_async(setting.id)
    end

    setting
  end

  # Sync all pending settings to Game Service
  def sync_all_pending_settings
    pending_settings = GameSetting.needs_sync.to_a
    
    return 0 if pending_settings.empty?

    synced_count = 0
    failed_count = 0

    pending_settings.group_by(&:game_type).each do |game_type, settings|
      begin
        sync_game_settings_to_service(game_type, settings)
        
        # Mark as synced
        setting_ids = settings.map(&:id)
        GameSetting.where(:id.in => setting_ids).update_all(
          sync_required: false,
          last_synced_at: Time.current
        )
        
        synced_count += settings.size
      rescue StandardError => e
        @logger.error "Failed to sync settings for game type",
          game_type: game_type,
          settings_count: settings.size,
          error: e.message
        
        failed_count += settings.size
      end
    end

    @logger.info "Bulk settings sync completed",
      synced_count: synced_count,
      failed_count: failed_count

    synced_count
  end

  # Reset settings to defaults for a game type
  def reset_to_defaults(game_type)
    validate_game_type(game_type)

    GameSetting.transaction do
      # Deactivate existing settings
      GameSetting.by_game_type(game_type).update_all(is_active: false)
      
      # Create default settings
      case game_type
      when 'prizewheel'
        create_prizewheel_defaults
      when 'amidakuji'
        create_amidakuji_defaults
      when 'global'
        create_global_defaults
      end
    end

    @logger.info "Settings reset to defaults",
      game_type: game_type

    # Sync with Game Service
    GameSettingsSyncJob.perform_async(game_type)

    get_game_settings(game_type)
  end

  # Get settings sync status
  def get_sync_status
    total_settings = GameSetting.active.count
    pending_sync = GameSetting.needs_sync.count
    last_sync_times = GameSetting.active.group(:game_type).maximum(:last_synced_at)

    {
      total_settings: total_settings,
      pending_sync: pending_sync,
      sync_percentage: total_settings > 0 ? ((total_settings - pending_sync).to_f / total_settings * 100).round(2) : 100,
      last_sync_by_game_type: last_sync_times.transform_values { |time| time&.iso8601 },
      needs_sync: pending_sync > 0
    }
  end

  # Validate settings against game rules
  def validate_game_settings(game_type, settings_hash)
    validate_game_type(game_type)
    
    errors = []

    case game_type
    when 'prizewheel'
      errors.concat(validate_prizewheel_settings(settings_hash))
    when 'amidakuji'
      errors.concat(validate_amidakuji_settings(settings_hash))
    when 'global'
      errors.concat(validate_global_settings(settings_hash))
    end

    errors
  end

  private

  def validate_game_type(game_type)
    valid_types = %w[prizewheel amidakuji global]
    unless valid_types.include?(game_type)
      raise ArgumentError, "Invalid game type. Must be one of: #{valid_types.join(', ')}"
    end
  end

  def validate_settings_hash(settings_hash)
    raise ArgumentError, "Settings hash cannot be empty" if settings_hash.blank?
    raise ArgumentError, "Settings must be a hash" unless settings_hash.is_a?(Hash)
  end

  def sync_game_settings_to_service(game_type, settings)
    return if game_type == 'global' # Global settings don't sync to Game Service

    config = {}
    settings.each do |setting|
      config[setting.setting_key] = setting.typed_value
    end

    @game_client.sync_game_settings(game_type, config)
    
    @logger.info "Settings synced to Game Service",
      game_type: game_type,
      settings_count: settings.size
  end

  def sync_setting_to_service(setting)
    return if setting.game_type == 'global'
    
    sync_game_settings_to_service(setting.game_type, [setting])
  end

  def create_prizewheel_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'default_sections' => 8,
      'spin_duration_min' => 3000,
      'spin_duration_max' => 8000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 60,
      'idle_timeout' => 1800
    }

    defaults.each do |key, value|
      GameSetting.set_setting('prizewheel', key, value, description: 'Default setting')
    end
  end

  def create_amidakuji_defaults
    defaults = {
      'min_players' => 2,
      'max_players' => 8,
      'min_bet' => 10.0,
      'max_bet' => 1000.0,
      'rows_per_player' => 3,
      'line_probability' => 0.5,
      'animation_duration' => 5000,
      'house_edge' => 0.05,
      'waiting_timeout' => 300,
      'playing_timeout' => 90,
      'idle_timeout' => 1800
    }

    defaults.each do |key, value|
      GameSetting.set_setting('amidakuji', key, value, description: 'Default setting')
    end
  end

  def create_global_defaults
    defaults = {
      'max_concurrent_games' => 1000,
      'maintenance_mode' => false,
      'prizewheel_enabled' => true,
      'amidakuji_enabled' => true,
      'rate_limit_requests' => 100,
      'rate_limit_window' => 60
    }

    defaults.each do |key, value|
      GameSetting.set_setting('global', key, value, description: 'Default global setting')
    end
  end

  def validate_prizewheel_settings(settings)
    errors = []
    
    if settings['min_players'] && (settings['min_players'] < 2 || settings['min_players'] > 8)
      errors << "min_players must be between 2 and 8"
    end
    
    if settings['max_players'] && (settings['max_players'] < 2 || settings['max_players'] > 8)
      errors << "max_players must be between 2 and 8"
    end
    
    if settings['min_bet'] && settings['min_bet'] <= 0
      errors << "min_bet must be positive"
    end
    
    if settings['max_bet'] && settings['max_bet'] <= 0
      errors << "max_bet must be positive"
    end
    
    if settings['house_edge'] && (settings['house_edge'] < 0 || settings['house_edge'] > 1)
      errors << "house_edge must be between 0 and 1"
    end

    errors
  end

  def validate_amidakuji_settings(settings)
    errors = []
    
    if settings['min_players'] && (settings['min_players'] < 2 || settings['min_players'] > 8)
      errors << "min_players must be between 2 and 8"
    end
    
    if settings['max_players'] && (settings['max_players'] < 2 || settings['max_players'] > 8)
      errors << "max_players must be between 2 and 8"
    end
    
    if settings['line_probability'] && (settings['line_probability'] < 0 || settings['line_probability'] > 1)
      errors << "line_probability must be between 0 and 1"
    end

    errors
  end

  def validate_global_settings(settings)
    errors = []
    
    if settings['max_concurrent_games'] && settings['max_concurrent_games'] <= 0
      errors << "max_concurrent_games must be positive"
    end
    
    if settings['rate_limit_requests'] && settings['rate_limit_requests'] <= 0
      errors << "rate_limit_requests must be positive"
    end

    errors
  end
end
