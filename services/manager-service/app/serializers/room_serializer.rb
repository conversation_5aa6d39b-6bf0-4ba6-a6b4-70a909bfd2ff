class RoomSerializer
  include JSONAPI::Serializer

  attributes :id, :external_room_id, :name, :game_type, :status, :max_players, :current_players,
             :bet_amount, :currency, :prize_pool, :is_private, :created_at, :updated_at

  attribute :id do |room|
    room.id.to_s
  end

  attribute :bet_amount do |room|
    room.bet_amount.to_f
  end

  attribute :prize_pool do |room|
    room.prize_pool.to_f
  end

  attribute :created_at do |room|
    room.created_at.iso8601
  end

  attribute :updated_at do |room|
    room.updated_at.iso8601
  end

  # Creator information
  attribute :creator do |room|
    {
      id: room.creator_id.to_s,
      username: room.creator.username
    }
  end

  # Configuration
  attribute :configuration, if: Proc.new { |room, params|
    params && (params[:include_configuration] || params[:current_user]&.admin?)
  } do |room|
    room.configuration
  end

  attribute :game_specific_config, if: Proc.new { |room, params|
    params && (params[:include_configuration] || params[:current_user]&.admin?)
  } do |room|
    room.game_specific_config
  end

  # Conditional attributes
  attribute :has_password do |room|
    room.has_password?
  end

  attribute :full do |room|
    room.full?
  end

  attribute :empty do |room|
    room.empty?
  end

  # Admin-only attributes
  attribute :sync_required, if: Proc.new { |room, params|
    params && params[:current_user]&.admin?
  } do |room|
    room.sync_required
  end

  attribute :last_synced_at, if: Proc.new { |room, params|
    params && params[:current_user]&.admin?
  } do |room|
    room.last_synced_at&.iso8601
  end

  attribute :metadata, if: Proc.new { |room, params|
    params && params[:current_user]&.admin?
  } do |room|
    room.metadata
  end

  # Current players details
  attribute :current_players_details, if: Proc.new { |room, params|
    params && params[:include_players]
  } do |room|
    room.game_sessions.where(status: ['pending', 'active']).includes(:user).map do |session|
      {
        user_id: session.user_id.to_s,
        username: session.user.username,
        position: session.metadata['position'] || 0,
        bet_amount: session.bet_amount.to_f,
        joined_at: session.created_at.iso8601,
        status: session.status
      }
    end
  end

  # Game session information
  attribute :game_session, if: Proc.new { |room, params|
    params && params[:include_game_session] && room.playing?
  } do |room|
    active_sessions = room.game_sessions.where(status: 'active')
    if active_sessions.any?
      first_session = active_sessions.first
      {
        session_id: first_session.session_id,
        started_at: first_session.started_at&.iso8601,
        estimated_end_at: estimate_end_time(room, first_session)
      }
    end
  end

  # Room statistics
  attribute :statistics, if: Proc.new { |room, params|
    params && params[:include_statistics]
  } do |room|
    finished_sessions = room.game_sessions.completed
    {
      total_games_played: finished_sessions.count,
      total_prize_pool: finished_sessions.sum(:bet_amount).to_f,
      average_players: calculate_average_players(room),
      last_game_at: finished_sessions.maximum(:ended_at)&.iso8601
    }
  end

  # Relationships
  belongs_to :creator, if: Proc.new { |room, params|
    params && params[:include_creator]
  }

  has_many :game_sessions, if: Proc.new { |room, params|
    params && params[:include_game_sessions]
  } do |room|
    room.game_sessions.recent.limit(10)
  end

  # Class methods for different serialization contexts
  def self.serialize_for_list(rooms, current_user = nil)
    options = {
      fields: {
        room: [:id, :external_room_id, :name, :game_type, :status, :max_players,
               :current_players, :bet_amount, :currency, :prize_pool, :is_private,
               :has_password, :full, :empty, :creator, :created_at]
      },
      params: {
        current_user: current_user
      }
    }

    # Include additional fields for admins
    if current_user&.admin?
      options[:fields][:room] += [:sync_required, :last_synced_at, :updated_at]
    end

    new(rooms, options).serializable_hash
  end

  def self.serialize_for_detail(room, current_user = nil)
    options = {
      fields: {
        room: [:id, :external_room_id, :name, :game_type, :status, :max_players,
               :current_players, :bet_amount, :currency, :prize_pool, :is_private,
               :has_password, :full, :empty, :creator, :configuration, :game_specific_config,
               :created_at, :updated_at]
      },
      params: {
        current_user: current_user,
        include_configuration: true,
        include_players: true,
        include_game_session: true,
        include_statistics: true
      }
    }

    # Include admin fields
    if current_user&.admin?
      options[:fields][:room] += [:sync_required, :last_synced_at, :metadata]
    end

    new(room, options).serializable_hash[:data][:attributes]
  end

  def self.serialize_for_admin(rooms, include_relations: false)
    options = {
      fields: {
        room: [:id, :external_room_id, :name, :game_type, :status, :max_players,
               :current_players, :bet_amount, :currency, :prize_pool, :is_private,
               :has_password, :full, :empty, :creator, :configuration, :game_specific_config,
               :sync_required, :last_synced_at, :metadata, :created_at, :updated_at]
      },
      params: {
        current_user: OpenStruct.new(admin?: true),
        include_configuration: true,
        include_players: true,
        include_game_session: true,
        include_statistics: true
      }
    }

    if include_relations
      options[:params][:include_creator] = true
      options[:params][:include_game_sessions] = true
      options[:include] = [:creator, :game_sessions]
    end

    new(rooms, options).serializable_hash
  end

  def self.serialize_for_public(rooms)
    options = {
      fields: {
        room: [:id, :external_room_id, :name, :game_type, :status, :max_players,
               :current_players, :bet_amount, :currency, :is_private, :has_password,
               :full, :empty, :creator, :created_at]
      }
    }

    new(rooms, options).serializable_hash
  end

  private

  def self.estimate_end_time(room, session)
    return nil unless session.started_at
    
    game_duration = room.configuration['game_duration'] || 30
    (session.started_at + game_duration.seconds).iso8601
  end

  def self.calculate_average_players(room)
    finished_sessions = room.game_sessions.completed
    return 0 if finished_sessions.empty?
    
    # Group by session_id to count unique games
    games_count = finished_sessions.distinct(:session_id).count
    return 0 if games_count.zero?
    
    finished_sessions.count.to_f / games_count
  end
end
