class UserSerializer
  include JSONAPI::Serializer

  attributes :id, :username, :email, :balance, :status, :role, :last_login_at, :created_at, :updated_at

  attribute :id do |user|
    user.id.to_s
  end

  attribute :balance do |user|
    user.balance.to_f
  end

  attribute :last_login_at do |user|
    user.last_login_at&.iso8601
  end

  attribute :created_at do |user|
    user.created_at.iso8601
  end

  attribute :updated_at do |user|
    user.updated_at.iso8601
  end

  # Conditional attributes based on context
  attribute :statistics, if: Proc.new { |user, params|
    params && params[:include_statistics]
  } do |user|
    user.statistics
  end

  attribute :recent_login, if: Proc.new { |user, params|
    params && params[:include_flags]
  } do |user|
    user.recent_login?
  end

  attribute :can_play, if: Proc.new { |user, params|
    params && params[:include_flags]
  } do |user|
    user.can_play?
  end

  # Admin-only attributes
  attribute :metadata, if: Proc.new { |user, params|
    params && params[:current_user]&.admin?
  } do |user|
    user.metadata
  end

  # Relationships
  has_many :game_sessions, if: Proc.new { |user, params|
    params && params[:include_game_sessions]
  } do |user|
    user.game_sessions.recent.limit(10)
  end

  has_many :transactions, if: Proc.new { |user, params|
    params && params[:include_transactions]
  } do |user|
    user.transactions.recent.limit(10)
  end

  # Class methods for different serialization contexts
  def self.serialize_for_auth(user)
    new(user, {
      fields: {
        user: [:id, :username, :email, :balance, :status, :role, :last_login_at]
      }
    }).serializable_hash[:data][:attributes]
  end

  def self.serialize_for_list(users, current_user = nil)
    options = {
      fields: {
        user: [:id, :username, :email, :balance, :status, :role, :last_login_at, :created_at]
      }
    }
    
    # Include additional fields for admins
    if current_user&.admin?
      options[:fields][:user] += [:metadata]
      options[:params] = { current_user: current_user }
    end

    new(users, options).serializable_hash
  end

  def self.serialize_for_profile(user, current_user = nil, include_stats: false)
    options = {
      fields: {
        user: [:id, :username, :email, :balance, :status, :role, :last_login_at, :created_at, :updated_at]
      },
      params: {
        current_user: current_user,
        include_statistics: include_stats,
        include_flags: true
      }
    }

    # Include sensitive data only for the user themselves or admins
    if current_user&.admin? || current_user&.id == user.id
      options[:fields][:user] += [:statistics] if include_stats
    end

    new(user, options).serializable_hash[:data][:attributes]
  end

  def self.serialize_for_admin(user, include_relations: false)
    options = {
      fields: {
        user: [:id, :username, :email, :balance, :status, :role, :last_login_at, :created_at, :updated_at, :metadata]
      },
      params: {
        current_user: OpenStruct.new(admin?: true),
        include_statistics: true,
        include_flags: true
      }
    }

    if include_relations
      options[:params][:include_game_sessions] = true
      options[:params][:include_transactions] = true
      options[:include] = [:game_sessions, :transactions]
    end

    new(user, options).serializable_hash
  end
end
