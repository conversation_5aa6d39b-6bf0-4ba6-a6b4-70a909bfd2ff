class GameSessionSerializer
  include JSONAPI::Serializer

  attributes :id, :session_id, :game_type, :status, :bet_amount, :win_amount,
             :started_at, :ended_at, :created_at, :updated_at

  attribute :id do |session|
    session.id.to_s
  end

  attribute :bet_amount do |session|
    session.bet_amount.to_f
  end

  attribute :win_amount do |session|
    session.win_amount.to_f
  end

  attribute :started_at do |session|
    session.started_at&.iso8601
  end

  attribute :ended_at do |session|
    session.ended_at&.iso8601
  end

  attribute :created_at do |session|
    session.created_at.iso8601
  end

  attribute :updated_at do |session|
    session.updated_at.iso8601
  end

  # Computed attributes
  attribute :profit_loss do |session|
    session.profit_loss.to_f
  end

  attribute :duration do |session|
    session.duration
  end

  attribute :won do |session|
    session.won?
  end

  attribute :lost do |session|
    session.lost?
  end

  # Conditional attributes
  attribute :result, if: Proc.new { |session, params|
    params && (params[:include_result] || session.completed?)
  } do |session|
    session.result
  end

  attribute :metadata, if: Proc.new { |session, params|
    params && (params[:current_user]&.admin? || params[:include_metadata])
  } do |session|
    session.metadata
  end

  # Relationships
  belongs_to :user, if: Proc.new { |session, params|
    params && params[:include_user]
  }

  # Related transactions
  attribute :transactions, if: Proc.new { |session, params|
    params && params[:include_transactions]
  } do |session|
    transactions = Transaction.where(
      reference_id: session.id.to_s,
      reference_type: 'GameSession'
    ).recent

    transactions.map do |txn|
      {
        id: txn.id.to_s,
        transaction_id: txn.transaction_id,
        type: txn.transaction_type,
        amount: txn.amount.to_f,
        status: txn.status,
        created_at: txn.created_at.iso8601
      }
    end
  end

  # Room information if available
  attribute :room, if: Proc.new { |session, params|
    params && params[:include_room]
  } do |session|
    # Find room that contains this session
    room = Room.where(game_sessions: session).first
    if room
      {
        id: room.id.to_s,
        external_room_id: room.external_room_id,
        name: room.name,
        status: room.status
      }
    end
  end

  # Class methods for different serialization contexts
  def self.serialize_for_list(sessions, current_user = nil)
    options = {
      fields: {
        game_session: [:id, :session_id, :game_type, :status, :bet_amount, :win_amount,
                      :profit_loss, :started_at, :ended_at, :created_at, :won, :lost]
      },
      params: {
        current_user: current_user
      }
    }

    # Include additional fields for admins
    if current_user&.admin?
      options[:fields][:game_session] += [:metadata, :updated_at]
      options[:params][:include_metadata] = true
      options[:params][:include_user] = true
    end

    new(sessions, options).serializable_hash
  end

  def self.serialize_for_detail(session, current_user = nil)
    options = {
      fields: {
        game_session: [:id, :session_id, :game_type, :status, :bet_amount, :win_amount,
                      :profit_loss, :duration, :started_at, :ended_at, :created_at, :updated_at,
                      :won, :lost, :result]
      },
      params: {
        current_user: current_user,
        include_result: true,
        include_transactions: true,
        include_room: true
      }
    }

    # Include metadata for admins or session owner
    if current_user&.admin? || current_user&.id == session.user_id
      options[:fields][:game_session] += [:metadata]
      options[:params][:include_metadata] = true
    end

    # Include user info for admins
    if current_user&.admin?
      options[:params][:include_user] = true
    end

    new(session, options).serializable_hash[:data][:attributes]
  end

  def self.serialize_for_user(sessions, user)
    options = {
      fields: {
        game_session: [:id, :session_id, :game_type, :status, :bet_amount, :win_amount,
                      :profit_loss, :duration, :started_at, :ended_at, :created_at,
                      :won, :lost, :result]
      },
      params: {
        current_user: user,
        include_result: true,
        include_transactions: true,
        include_room: true
      }
    }

    new(sessions, options).serializable_hash
  end

  def self.serialize_for_admin(sessions, include_relations: false)
    options = {
      fields: {
        game_session: [:id, :session_id, :game_type, :status, :bet_amount, :win_amount,
                      :profit_loss, :duration, :started_at, :ended_at, :created_at, :updated_at,
                      :won, :lost, :result, :metadata]
      },
      params: {
        current_user: OpenStruct.new(admin?: true),
        include_result: true,
        include_metadata: true,
        include_transactions: true,
        include_room: true
      }
    }

    if include_relations
      options[:params][:include_user] = true
      options[:include] = [:user]
    end

    new(sessions, options).serializable_hash
  end

  def self.serialize_stats(stats_data)
    {
      total_sessions: stats_data[:total_sessions],
      total_bet: stats_data[:total_bet]&.to_f,
      total_won: stats_data[:total_won]&.to_f,
      win_rate: stats_data[:win_rate]&.to_f,
      favorite_game: stats_data[:favorite_game]
    }
  end
end
