class TransactionSerializer
  include JSONAPI::Serializer

  attributes :id, :transaction_id, :transaction_type, :amount, :balance_before, :balance_after,
             :status, :description, :reference_id, :reference_type, :processed_at, :created_at, :updated_at

  attribute :id do |transaction|
    transaction.id.to_s
  end

  attribute :amount do |transaction|
    transaction.amount.to_f
  end

  attribute :balance_before do |transaction|
    transaction.balance_before.to_f
  end

  attribute :balance_after do |transaction|
    transaction.balance_after.to_f
  end

  attribute :processed_at do |transaction|
    transaction.processed_at&.iso8601
  end

  attribute :created_at do |transaction|
    transaction.created_at.iso8601
  end

  attribute :updated_at do |transaction|
    transaction.updated_at.iso8601
  end

  # Conditional attributes
  attribute :metadata, if: Proc.new { |transaction, params|
    params && (params[:current_user]&.admin? || params[:include_metadata])
  } do |transaction|
    transaction.metadata
  end

  # Computed attributes
  attribute :is_debit do |transaction|
    transaction.debit?
  end

  attribute :is_credit do |transaction|
    transaction.credit?
  end

  attribute :absolute_amount do |transaction|
    transaction.amount.abs.to_f
  end

  # Relationships
  belongs_to :user, if: Proc.new { |transaction, params|
    params && params[:include_user]
  }

  # Reference relationship (polymorphic)
  attribute :reference, if: Proc.new { |transaction, params|
    params && params[:include_reference] && transaction.reference_id.present?
  } do |transaction|
    case transaction.reference_type
    when 'GameSession'
      begin
        game_session = GameSession.find(transaction.reference_id)
        {
          type: 'GameSession',
          id: game_session.id.to_s,
          session_id: game_session.session_id,
          game_type: game_session.game_type,
          status: game_session.status
        }
      rescue Mongoid::Errors::DocumentNotFound
        { type: 'GameSession', id: transaction.reference_id, status: 'not_found' }
      end
    when 'Transaction'
      begin
        ref_transaction = Transaction.find(transaction.reference_id)
        {
          type: 'Transaction',
          id: ref_transaction.id.to_s,
          transaction_id: ref_transaction.transaction_id,
          transaction_type: ref_transaction.transaction_type
        }
      rescue Mongoid::Errors::DocumentNotFound
        { type: 'Transaction', id: transaction.reference_id, status: 'not_found' }
      end
    when 'Room'
      begin
        room = Room.find(transaction.reference_id)
        {
          type: 'Room',
          id: room.id.to_s,
          external_room_id: room.external_room_id,
          name: room.name,
          game_type: room.game_type
        }
      rescue Mongoid::Errors::DocumentNotFound
        { type: 'Room', id: transaction.reference_id, status: 'not_found' }
      end
    else
      {
        type: transaction.reference_type,
        id: transaction.reference_id
      }
    end
  end

  # Class methods for different serialization contexts
  def self.serialize_for_list(transactions, current_user = nil)
    options = {
      fields: {
        transaction: [:id, :transaction_id, :transaction_type, :amount, :balance_before, 
                     :balance_after, :status, :description, :created_at, :processed_at]
      },
      params: {
        current_user: current_user
      }
    }

    # Include additional fields for admins
    if current_user&.admin?
      options[:fields][:transaction] += [:reference_id, :reference_type, :metadata, :updated_at]
      options[:params][:include_metadata] = true
    end

    new(transactions, options).serializable_hash
  end

  def self.serialize_for_detail(transaction, current_user = nil)
    options = {
      fields: {
        transaction: [:id, :transaction_id, :transaction_type, :amount, :balance_before,
                     :balance_after, :status, :description, :reference_id, :reference_type,
                     :processed_at, :created_at, :updated_at, :is_debit, :is_credit, :absolute_amount]
      },
      params: {
        current_user: current_user,
        include_reference: true,
        include_user: current_user&.admin?
      }
    }

    # Include metadata for admins or transaction owner
    if current_user&.admin? || current_user&.id == transaction.user_id
      options[:fields][:transaction] += [:metadata]
      options[:params][:include_metadata] = true
    end

    new(transaction, options).serializable_hash[:data][:attributes]
  end

  def self.serialize_for_user(transactions, user)
    options = {
      fields: {
        transaction: [:id, :transaction_id, :transaction_type, :amount, :balance_before,
                     :balance_after, :status, :description, :created_at, :processed_at,
                     :is_debit, :is_credit, :absolute_amount]
      },
      params: {
        current_user: user,
        include_reference: true
      }
    }

    new(transactions, options).serializable_hash
  end

  def self.serialize_for_admin(transactions, include_relations: false)
    options = {
      fields: {
        transaction: [:id, :transaction_id, :transaction_type, :amount, :balance_before,
                     :balance_after, :status, :description, :reference_id, :reference_type,
                     :processed_at, :created_at, :updated_at, :metadata, :is_debit, :is_credit]
      },
      params: {
        current_user: OpenStruct.new(admin?: true),
        include_metadata: true,
        include_reference: true
      }
    }

    if include_relations
      options[:params][:include_user] = true
      options[:include] = [:user]
    end

    new(transactions, options).serializable_hash
  end

  def self.serialize_stats(stats_data)
    {
      total_transactions: stats_data[:total_transactions],
      total_deposits: stats_data[:total_deposits]&.to_f,
      total_withdrawals: stats_data[:total_withdrawals]&.to_f,
      total_bets: stats_data[:total_bets]&.to_f,
      total_winnings: stats_data[:total_winnings]&.to_f,
      net_amount: stats_data[:net_amount]&.to_f
    }
  end
end
