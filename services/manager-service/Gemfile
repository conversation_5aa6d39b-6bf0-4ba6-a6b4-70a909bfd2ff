source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.4.4'

# Core Rails gems
gem 'rails', '~> 8.0.2'
gem 'bootsnap', '>= 1.4.4', require: false
gem 'puma', '~> 6.0'

# Database and ORM
gem 'mongoid', '~> 8.0'
gem 'mongoid-rspec', '~> 4.1'

# Authentication and Authorization
gem 'jwt', '~> 2.7'
gem 'bcrypt', '~> 3.1.7'
gem 'cancancan', '~> 3.4'

# API and Serialization
gem 'jsonapi-serializer', '~> 2.2'
gem 'rack-cors', '~> 1.1'
gem 'kaminari', '~> 1.2'
gem 'kaminari-mongoid', '~> 1.0'

# gRPC and Protocol Buffers
gem 'grpc', '~> 1.50'
gem 'grpc-tools', '~> 1.50'
gem 'google-protobuf', '~> 3.21'

# Background Jobs
gem 'sidekiq', '~> 7.0'
gem 'sidekiq-cron', '~> 1.9'
gem 'redis', '~> 5.0'

# Monitoring and Logging
gem 'prometheus-client', '~> 4.0'
gem 'lograge', '~> 0.12'
gem 'amazing_print', '~> 1.4'

# Validation and Security
gem 'dry-validation', '~> 1.10'
gem 'rack-attack', '~> 6.6'
gem 'brakeman', '~> 5.4'

# Configuration and Environment
gem 'dotenv-rails', '~> 2.8'
gem 'config', '~> 4.0'

# HTTP Client
gem 'faraday', '~> 2.7'
gem 'faraday-retry', '~> 2.0'

# Utilities
gem 'chronic', '~> 0.10'
gem 'request_store', '~> 1.5'
gem 'connection_pool', '~> 2.3'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

group :development, :test do
  gem 'rspec-rails', '~> 6.0'
  gem 'factory_bot_rails', '~> 6.2'
  gem 'faker', '~> 3.1'
  gem 'pry-rails', '~> 0.3'
  gem 'pry-byebug', '~> 3.10'
  gem 'database_cleaner-mongoid', '~> 2.0'
  gem 'simplecov', '~> 0.22'
  gem 'rubocop', '~> 1.48'
  gem 'rubocop-rails', '~> 2.18'
  gem 'rubocop-rspec', '~> 2.19'
  gem 'rails_best_practices', '~> 1.23'
  gem "debug", platforms: %i[ mri mingw mswin x64_mingw ], require: "debug/prelude"
end

group :development do
  gem 'listen', '~> 3.3'
  gem 'spring', '~> 4.1'
  gem 'spring-watcher-listen', '~> 2.1'
  # gem 'annotate', '~> 3.2'  # Temporarily disabled due to Rails 8.0 compatibility
  gem 'bullet', '~> 7.0'
end

group :test do
  gem 'shoulda-matchers', '~> 5.3'
  gem 'webmock', '~> 3.18'
  gem 'vcr', '~> 6.1'
  gem 'timecop', '~> 0.9'
  gem 'rspec-sidekiq', '~> 4.0'
end

group :production do
  gem 'newrelic_rpm', '~> 8.16'
  gem 'sentry-ruby', '~> 5.8'
  gem 'sentry-rails', '~> 5.8'
end
