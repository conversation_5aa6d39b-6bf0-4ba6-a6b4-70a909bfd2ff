#!/usr/bin/env ruby

# Check current room and session state
require_relative 'config/environment'

puts "=== Current Room and Session State Check ==="

# Check all rooms
puts "\n📋 ALL ROOMS:"
Room.all.each do |room|
  puts "Room: #{room.id} (#{room.name})"
  puts "  Status: #{room.status}"
  puts "  Players: #{room.current_players}/#{room.max_players}"
  puts "  Created: #{room.created_at}"
  puts "  Updated: #{room.updated_at}"
  
  # Check data consistency
  consistency = room.check_data_consistency
  puts "  Consistent: #{consistency[:consistent]}"
  if !consistency[:consistent]
    puts "  ⚠️  Inconsistency: #{consistency}"
  end
  
  # Check all sessions for this room
  all_sessions = room.game_sessions
  puts "  Total sessions: #{all_sessions.count}"
  
  if all_sessions.any?
    sessions_by_status = all_sessions.group_by(&:status)
    sessions_by_status.each do |status, sessions|
      puts "    #{status}: #{sessions.count}"
      sessions.each do |session|
        user = session.user
        puts "      - #{user.username} (#{user.id}) - Created: #{session.created_at}"
        if session.metadata.present?
          puts "        Metadata: #{session.metadata.inspect}"
        end
      end
    end
  else
    puts "    No sessions found"
  end
  
  puts ""
end

# Check for rooms that appear full but have no active sessions
puts "\n🔍 PROBLEMATIC ROOMS (appear full but no active sessions):"
problematic_rooms = Room.where(:current_players.gt => 0).select do |room|
  active_sessions = room.game_sessions.in(status: ['pending', 'active']).count
  active_sessions == 0
end

if problematic_rooms.any?
  problematic_rooms.each do |room|
    puts "❌ Room #{room.id} (#{room.name}): #{room.current_players}/#{room.max_players} players but 0 active sessions"
    
    # Try to fix it
    puts "   Attempting to fix..."
    begin
      fix_result = room.fix_data_consistency!
      puts "   ✅ Fixed: #{fix_result}"
    rescue => e
      puts "   ❌ Fix failed: #{e.message}"
    end
  end
else
  puts "✅ No problematic rooms found"
end

# Check for active sessions without proper room association
puts "\n🔍 ORPHANED SESSIONS (active sessions with invalid room references):"
active_sessions = GameSession.in(status: ['pending', 'active'])
orphaned_sessions = active_sessions.select do |session|
  begin
    session.room.nil? || !session.room.persisted?
  rescue
    true
  end
end

if orphaned_sessions.any?
  orphaned_sessions.each do |session|
    puts "❌ Orphaned session #{session.id} - User: #{session.user.username} - Status: #{session.status}"
    puts "   Room reference: #{session.room_id}"
    
    # Try to cancel orphaned session
    puts "   Cancelling orphaned session..."
    begin
      session.cancel!("Orphaned session cleanup")
      puts "   ✅ Cancelled"
    rescue => e
      puts "   ❌ Cancel failed: #{e.message}"
    end
  end
else
  puts "✅ No orphaned sessions found"
end

# Check current active sessions across all rooms
puts "\n📊 ACTIVE SESSIONS SUMMARY:"
active_sessions = GameSession.in(status: ['pending', 'active'])
puts "Total active sessions: #{active_sessions.count}"

if active_sessions.any?
  puts "\nActive sessions by room:"
  active_sessions.group_by(&:room).each do |room, sessions|
    if room
      puts "  Room #{room.id} (#{room.name}): #{sessions.count} sessions"
      sessions.each do |session|
        puts "    - #{session.user.username} (#{session.status})"
      end
    else
      puts "  No room: #{sessions.count} sessions"
      sessions.each do |session|
        puts "    - #{session.user.username} (#{session.status}) - Room ID: #{session.room_id}"
      end
    end
  end
end

# Test creating a new room and adding players
puts "\n🧪 TESTING NEW ROOM CREATION AND PLAYER ADDITION:"
begin
  admin_user = User.where(role: 'admin').first
  test_users = User.limit(2).to_a
  
  if test_users.count >= 2
    test_room = Room.create!(
      name: "Session Test Room",
      game_type: "prizewheel",
      max_players: 2,
      bet_amount: 10.0,
      currency: "USD",
      creator: admin_user,
      status: "waiting"
    )
    
    puts "✅ Created test room: #{test_room.id}"
    
    # Add first player
    session1 = test_room.add_player!(test_users[0])
    puts "✅ Added #{test_users[0].username} - Session: #{session1.id}"
    
    # Add second player
    session2 = test_room.add_player!(test_users[1])
    puts "✅ Added #{test_users[1].username} - Session: #{session2.id}"
    
    test_room.reload
    puts "Room state: #{test_room.current_players}/#{test_room.max_players}"
    
    # Check active sessions
    active_sessions = test_room.game_sessions.in(status: ['pending', 'active'])
    puts "Active sessions: #{active_sessions.count}"
    
    # Test kick functionality
    if active_sessions.any?
      player_to_kick = active_sessions.first.user
      puts "\nTesting kick of #{player_to_kick.username}..."
      
      result = test_room.kick_player!(player_to_kick.id.to_s, admin_user.id.to_s, "Test kick")
      puts "✅ Kick result: #{result}"
      
      test_room.reload
      puts "Room after kick: #{test_room.current_players}/#{test_room.max_players}"
      
      remaining_sessions = test_room.game_sessions.in(status: ['pending', 'active'])
      puts "Remaining active sessions: #{remaining_sessions.count}"
    end
    
    # Cleanup
    test_room.destroy
    puts "✅ Cleaned up test room"
    
  else
    puts "❌ Not enough users to test (need at least 2)"
  end
  
rescue => e
  puts "❌ Test failed: #{e.message}"
  puts "Error class: #{e.class.name}"
end

puts "\n=== Check completed ==="
