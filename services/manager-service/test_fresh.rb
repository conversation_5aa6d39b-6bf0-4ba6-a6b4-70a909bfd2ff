#!/usr/bin/env ruby

# Force reload of the environment
ENV['RAILS_ENV'] ||= 'development'

require 'bundler/setup'
require_relative 'config/environment'

# Force reload the User class
Object.send(:remove_const, :User) if defined?(User)
load Rails.root.join('app/models/user.rb')

puts "Testing with fresh environment..."

begin
  user = User.first
  puts "User: #{user.username}, Balance: #{user.balance}"

  # Check method signature
  method = user.method(:update_balance!)
  puts "Method signature: #{method.parameters}"

  # Test deposit
  puts "\n=== Testing Deposit ==="
  initial_balance = user.balance
  deposit_result = user.update_balance!(15.0, 'admin_deposit', description: 'Test deposit')
  puts "Deposit Success! Transaction ID: #{deposit_result.transaction_id}"
  puts "Amount: #{deposit_result.amount}"

  user.reload
  puts "Balance after deposit: #{user.balance}"

  # Test withdrawal
  puts "\n=== Testing Withdrawal ==="
  withdrawal_result = user.update_balance!(-8.0, 'withdrawal', description: 'Test withdrawal')
  puts "Withdrawal Success! Transaction ID: #{withdrawal_result.transaction_id}"
  puts "Amount: #{withdrawal_result.amount}"

  user.reload
  puts "Balance after withdrawal: #{user.balance}"

  # Test insufficient balance
  puts "\n=== Testing Insufficient Balance ==="
  begin
    user.update_balance!(-1000.0, 'withdrawal', description: 'Should fail')
    puts "ERROR: Should have failed!"
  rescue ArgumentError => e
    puts "Correctly caught insufficient balance: #{e.message}"
  end

  puts "\n✅ All tests passed!"

rescue => e
  puts "Error: #{e.message}"
  puts e.backtrace.first(3)
end
