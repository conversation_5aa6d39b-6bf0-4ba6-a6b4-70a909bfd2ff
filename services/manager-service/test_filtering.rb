#!/usr/bin/env ruby

ENV['RAILS_ENV'] ||= 'development'

require 'bundler/setup'
require_relative 'config/environment'

# Force reload models
Object.send(:remove_const, :User) if defined?(User)
Object.send(:remove_const, :Transaction) if defined?(Transaction)
load Rails.root.join('app/models/user.rb')
load Rails.root.join('app/models/transaction.rb')

puts "=== Testing Manager Service Filtering ==="

begin
  # Get some test data
  user = User.first
  puts "Test user: #{user.username} (ID: #{user.id})"
  
  # Test basic transaction query
  puts "\n1. Basic transaction query:"
  transactions = Transaction.includes(:user).recent.limit(5)
  puts "Found #{transactions.count} transactions"
  
  # Test username filtering
  puts "\n2. Testing username filtering:"
  user_ids = User.where(username: /#{Regexp.escape('admin')}/i).pluck(:id)
  puts "User IDs matching 'admin': #{user_ids}"
  
  filtered_transactions = Transaction.includes(:user).where(:user_id.in => user_ids).limit(5)
  puts "Transactions for admin users: #{filtered_transactions.count}"
  
  # Test search functionality
  puts "\n3. Testing search functionality:"
  search_term = 'test'
  search_user_ids = User.where(username: /#{search_term}/i).pluck(:id)
  search_transactions = Transaction.includes(:user).any_of(
    { transaction_id: /#{search_term}/i },
    { description: /#{search_term}/i },
    { :user_id.in => search_user_ids }
  ).limit(5)
  puts "Transactions matching '#{search_term}': #{search_transactions.count}"
  
  # Test type filtering
  puts "\n4. Testing type filtering:"
  withdrawal_transactions = Transaction.includes(:user).by_type('withdrawal').limit(5)
  puts "Withdrawal transactions: #{withdrawal_transactions.count}"
  
  # Test status filtering
  puts "\n5. Testing status filtering:"
  completed_transactions = Transaction.includes(:user).where(status: 'completed').limit(5)
  puts "Completed transactions: #{completed_transactions.count}"
  
  # Test combined filtering
  puts "\n6. Testing combined filtering:"
  combined = Transaction.includes(:user)
    .by_type('withdrawal')
    .where(status: 'completed')
    .where(:user_id.in => user_ids)
    .limit(5)
  puts "Combined filter (withdrawal + completed + admin user): #{combined.count}"
  
  puts "\n✅ All filtering tests completed successfully!"

rescue => e
  puts "❌ Error: #{e.message}"
  puts e.backtrace.first(5)
end
