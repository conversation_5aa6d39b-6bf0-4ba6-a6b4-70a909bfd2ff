#!/usr/bin/env ruby

# Debug script to investigate room data inconsistency
require_relative 'config/environment'

puts "=== Debugging Room Data Inconsistency ==="

# Find the problematic room
room_id = "684d9baba533d3e5fea7dc35"
room = Room.find(room_id)

puts "Room details:"
puts "- ID: #{room.id}"
puts "- Name: #{room.name}"
puts "- Status: #{room.status}"
puts "- Current players: #{room.current_players}/#{room.max_players}"
puts "- Prize pool: #{room.prize_pool}"
puts "- Created at: #{room.created_at}"
puts "- Updated at: #{room.updated_at}"

puts "\n=== Game Sessions Analysis ==="

# Get all sessions for this room
all_sessions = room.game_sessions
puts "Total sessions: #{all_sessions.count}"

# Group by status
sessions_by_status = all_sessions.group_by(&:status)
sessions_by_status.each do |status, sessions|
  puts "- #{status}: #{sessions.count}"
  sessions.each do |session|
    user = session.user
    puts "  * User: #{user.username} (#{user.id}) - Created: #{session.created_at} - Updated: #{session.updated_at}"
    if session.metadata.present?
      puts "    Metadata: #{session.metadata.inspect}"
    end
  end
end

puts "\n=== Active Sessions Check ==="
active_sessions = room.game_sessions.in(status: ['pending', 'active'])
puts "Active sessions count: #{active_sessions.count}"
puts "Expected current_players: #{active_sessions.count}"
puts "Actual current_players: #{room.current_players}"
puts "Inconsistency: #{room.current_players != active_sessions.count ? 'YES' : 'NO'}"

puts "\n=== Data Consistency Check ==="
consistency_result = room.check_data_consistency
puts "Consistency check result:"
consistency_result.each do |key, value|
  puts "- #{key}: #{value}"
end

puts "\n=== Attempting to Fix Data Consistency ==="
begin
  fix_result = room.fix_data_consistency!
  puts "Fix result:"
  fix_result.each do |key, value|
    puts "- #{key}: #{value}"
  end
  
  # Reload and check again
  room.reload
  puts "\nAfter fix:"
  puts "- Current players: #{room.current_players}/#{room.max_players}"
  puts "- Is full: #{room.current_players >= room.max_players}"
  
rescue => e
  puts "Fix failed: #{e.message}"
end

puts "\n=== Testing Room Operations After Fix ==="

# Check if room can accept new players
admin_user = User.where(role: 'admin').first
if admin_user && room.current_players < room.max_players
  puts "Room can now accept players: #{room.can_join?(admin_user)}"
else
  puts "Room is still full or no admin user available"
end

# If there are still active sessions, try to kick one
active_sessions = room.game_sessions.in(status: ['pending', 'active'])
if active_sessions.any?
  puts "\nTrying to kick a player after fix..."
  player_to_kick = active_sessions.first.user
  begin
    result = room.kick_player!(player_to_kick.id.to_s, admin_user.id.to_s, "Test kick after fix")
    puts "✓ Kick succeeded: #{result}"
    
    room.reload
    puts "Room state after kick:"
    puts "- Current players: #{room.current_players}/#{room.max_players}"
  rescue => e
    puts "✗ Kick failed: #{e.message}"
  end
else
  puts "No active players to kick after fix"
end

puts "\n=== Debug completed ==="
