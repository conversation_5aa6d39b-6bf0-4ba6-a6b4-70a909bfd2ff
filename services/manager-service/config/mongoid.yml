development:
  # Configure available database clients. (required)
  clients:
    # Defines the default client. (required)
    default:
      # Use MONGODB_URL environment variable for connection
      uri: <%= ENV['MONGODB_URL'] || 'mongodb://localhost:27017/xzgame_manager_development' %>
      options:
        max_pool_size: <%= ENV['MONGODB_MAX_POOL_SIZE'] || 25 %>
        min_pool_size: 5
        wait_queue_timeout: 15
        connect_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        socket_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        server_selection_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        heartbeat_frequency: 10
        retry_writes: true
        read:
          mode: :primary
        write:
          w: 1
          j: true
          fsync: false
        ssl: <%= ENV['MONGODB_SSL'] == 'true' %>
        ssl_verify: <%= ENV['MONGODB_SSL_VERIFY'] != 'false' %>
        auth_source: <%= ENV['MONGODB_AUTH_SOURCE'] || 'admin' %>

  # Configure Mongoid specific options. (optional)
  options:
    # Application name that is printed to the mongodb logs upon establishing
    # a connection in server versions >= 3.4. Note that the name cannot
    # exceed 128 bytes. It is also used as the database name if the
    # database name is not explicitly defined. (default: nil)
    # app_name: MyApplicationName

    # Mark belongs_to associations as required by default, so that saving a
    # model with a missing belongs_to association will trigger a validation
    # error. (default: true)
    # belongs_to_required_by_default: true

    # Raise an exception when a field is redefined. (default: false)
    # duplicate_fields_exception: false

    # Include the root model name in json serialization. (default: false)
    # include_root_in_json: false

    # Include the _type field in serialization. (default: false)
    # include_type_for_serialization: false

    # Whether to join nested persistence contexts for atomic operations
    # to parent contexts by default. (default: false)
    # join_contexts: false

    # Set the Mongoid and Ruby driver log levels when Mongoid is not using
    # Ruby on Rails logger instance. (default: :info)
    # log_level: :info

    # Preload all models in development, needed when models use
    # inheritance. (default: false)
    # preload_models: false

    # Raise an error when performing a #find and the document is not found.
    # (default: true)
    # raise_not_found_error: true

    # Raise an error when defining a scope with the same name as an
    # existing method. (default: false)
    # scope_overwrite_exception: false

    # Use ActiveSupport's time zone in time operations instead of
    # the Ruby default time zone. See the time zone section below for
    # further information. (default: true)
    # use_activesupport_time_zone: true

    # Return stored times as UTC. See the time zone section below for
    # further information. Most applications should not use this option.
    # (default: false)
    # use_utc: false

    # (Deprecated) In MongoDB 4.0 and earlier, set whether to create
    # indexes in the background by default. (default: false)
    # background_indexing: false

test:
  clients:
    default:
      uri: <%= ENV['MONGODB_URL']&.gsub('/xzgame', '/xzgame_manager_test') || 'mongodb://localhost:27017/xzgame_manager_test' %>
      options:
        read:
          mode: :primary
        max_pool_size: 1
        auth_source: <%= ENV['MONGODB_AUTH_SOURCE'] || 'admin' %>

production:
  clients:
    default:
      uri: <%= ENV['MONGODB_URL'] || 'mongodb://localhost:27017/xzgame_manager_production' %>
      options:
        max_pool_size: <%= ENV['MONGODB_MAX_POOL_SIZE'] || 100 %>
        min_pool_size: 10
        wait_queue_timeout: 15
        connect_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        socket_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        server_selection_timeout: <%= ENV['MONGODB_TIMEOUT'] || 10 %>
        heartbeat_frequency: 10
        retry_writes: true
        read:
          mode: :primary
        write:
          w: 1
          j: true
          fsync: false
        ssl: <%= ENV['MONGODB_SSL'] == 'true' %>
        ssl_verify: <%= ENV['MONGODB_SSL_VERIFY'] != 'false' %>
        auth_source: <%= ENV['MONGODB_AUTH_SOURCE'] || 'admin' %>
  options:
    belongs_to_required_by_default: true
    raise_not_found_error: true
    use_activesupport_time_zone: true
