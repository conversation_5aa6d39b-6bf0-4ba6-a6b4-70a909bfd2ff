Rails.application.routes.draw do
  # Health check endpoints
  get "up" => "rails/health#show", as: :rails_health_check
  get '/health', to: 'health#index'
  get '/health/detailed', to: 'health#detailed'

  # Game configuration endpoints
  get '/game_configuration', to: 'game_configuration#show'
  put '/game_configuration', to: 'game_configuration#update'

  # Authentication routes
  scope :auth do
    post 'login', to: 'auth#login'
    post 'register', to: 'auth#register'
    post 'logout', to: 'auth#logout'
    get 'me', to: 'auth#me'
    post 'refresh', to: 'auth#refresh'
    put 'change_password', to: 'auth#change_password'
  end

  # User management routes
  resources :users do
    member do
      put 'balance', to: 'users#update_balance'
      post 'validate_balance', to: 'users#validate_balance'
      get 'transactions', to: 'users#transactions'
      get 'game_sessions', to: 'users#game_sessions'
    end
    collection do
      get 'search', to: 'users#search'
      get 'profile', to: 'users#profile'
    end
  end

  # Public room routes (for regular players)
  resources :rooms, only: [:index, :show] do
    member do
      post 'join', to: 'rooms#join'
      post 'leave', to: 'rooms#leave'
      # Player management routes
      get 'players', to: 'rooms#players'
      put 'players/:user_id/ready', to: 'rooms#set_player_ready'
      delete 'players/:user_id', to: 'rooms#kick_player'
    end
  end

  # Game session routes
  resources :game_sessions, only: [:index, :show, :create, :update] do
    member do
      put 'start', to: 'game_sessions#start'
      put 'complete', to: 'game_sessions#complete'
      put 'cancel', to: 'game_sessions#cancel'
    end
  end

  # Transaction routes
  resources :transactions, only: [:index, :show, :create] do
    collection do
      get 'stats', to: 'transactions#stats'
      post 'deposit', to: 'transactions#deposit'
      post 'withdraw', to: 'transactions#withdraw'
    end
  end

  # Administrative routes
  namespace :admin do
    resources :rooms do
      member do
        get 'details', to: 'rooms#details'
        get 'players', to: 'rooms#players'
        post 'kick-player', to: 'room_settings#kick_player'
        get 'settings', to: 'room_settings#show'
        put 'settings', to: 'room_settings#update'
        post 'force-start', to: 'room_settings#force_start'
        post 'force-end', to: 'room_settings#force_end'
        get 'sync-status', to: 'room_settings#sync_status'
        post 'sync', to: 'room_settings#sync_now'
        # Data consistency routes
        get 'consistency_check', to: 'rooms#consistency_check'
        post 'fix_data_consistency', to: 'rooms#fix_data_consistency'
      end
    end

    # Game settings routes
    get 'game-settings', to: 'game_settings#index'
    post 'game-settings', to: 'game_settings#create'
    put 'game-settings/global', to: 'game_settings#update_global'
    get 'game-settings/sync-status', to: 'game_settings#sync_status'
    post 'game-settings/sync', to: 'game_settings#sync_all'
    get 'game-settings/defaults', to: 'game_settings#defaults'
    post 'game-settings/reset', to: 'game_settings#reset_to_defaults'

    # Game configuration management routes
    resources :game_configurations, param: :game_type, only: [:index, :show, :update] do
      member do
        get 'state_durations', to: 'game_configurations#state_durations'
        put 'state_durations', to: 'game_configurations#update_state_durations'
      end
      collection do
        post 'reset_defaults', to: 'game_configurations#reset_defaults'
      end
    end

    # Room settings routes (alternative path)
    get 'room-settings/:room_id', to: 'room_settings#show'
    put 'room-settings/:room_id', to: 'room_settings#update'
  end

  # Game Service notification routes (for synchronization)
  namespace :game_service do
    resources :rooms, only: [:show] do
      member do
        post 'join', to: 'rooms#join'
        post 'leave', to: 'rooms#leave'
        post 'player_joined', to: 'rooms#player_joined'
        post 'player_left', to: 'rooms#player_left'
        post 'player_kicked', to: 'rooms#player_kicked'
      end
    end

    # Game Service user operations (no authentication required)
    resources :users, only: [] do
      member do
        get 'info', to: 'users#info'
        get 'sessions', to: 'users#sessions'
        post 'validate_balance', to: 'users#validate_balance'
        post 'deduct_bet', to: 'users#deduct_bet'
        post 'update_balance', to: 'users#update_balance'
      end
    end
  end

  # Root route for API documentation or status
  root to: proc { [200, {}, ['XZ Game Manager Service API']] }
end
