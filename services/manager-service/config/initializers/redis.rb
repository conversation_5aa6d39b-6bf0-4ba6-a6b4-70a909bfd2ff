require 'redis'
require 'connection_pool'

# Redis configuration for pub/sub and caching
REDIS_CONFIG = {
  url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'),
  timeout: 5,
  reconnect_attempts: 3
}.freeze

# Main Redis connection pool
$redis_pool = ConnectionPool.new(size: 10, timeout: 5) do
  Redis.new(REDIS_CONFIG)
end

# Dedicated Redis connection for pub/sub
$redis_pubsub = Redis.new(REDIS_CONFIG)

# Redis client for general use
$redis = Redis.new(REDIS_CONFIG)

# Configure Rails cache store to use Redis
Rails.application.configure do
  config.cache_store = :redis_cache_store, {
    url: REDIS_CONFIG[:url],
    pool_size: 5,
    pool_timeout: 5,
    reconnect_attempts: 3
  }
end

# Redis pub/sub channels configuration
REDIS_CHANNELS = {
  game_events: 'game:events',
  user_events: 'user:events',
  transaction_events: 'transaction:events',
  system_events: 'system:events'
}.freeze

# Helper methods for Redis operations
class RedisService
  class << self
    def publish(channel, message)
      $redis_pool.with do |redis|
        redis.publish(channel, message.to_json)
      end
    rescue Redis::BaseError => e
      Rails.logger.error "Redis publish error: #{e.message}"
      false
    end

    def get(key)
      $redis_pool.with do |redis|
        redis.get(key)
      end
    rescue Redis::BaseError => e
      Rails.logger.error "Redis get error: #{e.message}"
      nil
    end

    def set(key, value, options = {})
      $redis_pool.with do |redis|
        if options[:expires_in]
          redis.setex(key, options[:expires_in], value)
        else
          redis.set(key, value)
        end
      end
    rescue Redis::BaseError => e
      Rails.logger.error "Redis set error: #{e.message}"
      false
    end

    def delete(key)
      $redis_pool.with do |redis|
        redis.del(key)
      end
    rescue Redis::BaseError => e
      Rails.logger.error "Redis delete error: #{e.message}"
      false
    end

    def exists?(key)
      $redis_pool.with do |redis|
        redis.exists?(key)
      end
    rescue Redis::BaseError => e
      Rails.logger.error "Redis exists error: #{e.message}"
      false
    end
  end
end
