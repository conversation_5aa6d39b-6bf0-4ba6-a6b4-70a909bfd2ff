# gRPC Client Configuration
module GrpcClients
  class << self
    def game_service_client
      @game_service_client ||= create_game_service_client
    end

    def api_gateway_client
      @api_gateway_client ||= create_api_gateway_client
    end

    private

    def create_game_service_client
      # Use real GameServiceClient instead of mock
      GameServiceClient.instance
    end

    def create_api_gateway_client
      MockGrpcClient.new(
        service_name: 'api-gateway',
        url: ENV['API_GATEWAY_URL'] || 'api-gateway:3000'
      )
    end
  end

  # Mock gRPC client for development/testing
  class MockGrpcClient
    attr_reader :service_name, :url

    def initialize(service_name:, url:)
      @service_name = service_name
      @url = url
      @logger = Rails.logger
    end

    def method_missing(method_name, *args, **kwargs)
      @logger.info "Mock gRPC call to #{service_name}",
        method: method_name,
        args: args,
        kwargs: kwargs

      # Return mock response
      case method_name.to_s
      when 'create_room'
        OpenStruct.new(
          room: OpenStruct.new(
            id: "room_#{SecureRandom.hex(8)}",
            name: args[0]&.room_name || 'Mock Room',
            game_type: args[0]&.game_type || 'prizewheel',
            status: 'waiting'
          )
        )
      when 'update_room', 'join_room', 'leave_room', 'start_game', 'delete_room'
        OpenStruct.new(success: true)
      when 'get_room'
        OpenStruct.new(
          room: OpenStruct.new(
            id: args[0]&.room_id || 'mock_room_id',
            status: 'waiting',
            current_players: 0
          )
        )
      when 'list_rooms'
        # Return empty rooms list
        OpenStruct.new(
          rooms: [],
          pagination: OpenStruct.new(
            current_page: 1,
            total_pages: 0,
            total_count: 0
          )
        )
      else
        OpenStruct.new(success: true)
      end
    end

    def respond_to_missing?(method_name, include_private = false)
      true
    end
  end
end

# Initialize clients on startup
Rails.application.config.after_initialize do
  begin
    GrpcClients.game_service_client
    GrpcClients.api_gateway_client
    Rails.logger.info "gRPC clients initialized successfully"
  rescue StandardError => e
    Rails.logger.error "Failed to initialize gRPC clients: #{e.message}"
  end
end
