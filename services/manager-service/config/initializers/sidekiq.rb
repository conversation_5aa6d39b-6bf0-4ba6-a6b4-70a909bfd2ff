require 'sidekiq'
require 'sidekiq/web'

Sidekiq.configure_server do |config|
  config.redis = {
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'),
    network_timeout: 5
  }

  # Configure connection pool
  config.concurrency = ENV.fetch('SIDEKIQ_CONCURRENCY', 5).to_i

  # Configure error handling
  config.error_handlers << proc do |ex, ctx_hash|
    Rails.logger.error "Sidekiq error: #{ex.message}"
    Rails.logger.error ex.backtrace.join("\n")
  end

  # Schedule periodic jobs
  config.on(:startup) do
    # Schedule room consistency check every 5 minutes
    Sidekiq::Cron::Job.load_from_hash({
      'room_consistency_check' => {
        'cron' => '*/5 * * * *',
        'class' => 'RoomConsistencyCheckJob',
        'description' => 'Check and fix room data consistency issues'
      }
    })
  end
end

Sidekiq.configure_client do |config|
  config.redis = {
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0'),
    network_timeout: 5
  }
end

# Configure Sidekiq Web UI (for development and admin access)
if Rails.env.development?
  require 'sidekiq/web'
  
  # Mount Sidekiq web UI at /sidekiq
  Rails.application.routes.draw do
    mount Sidekiq::Web => '/sidekiq'
  end
end
