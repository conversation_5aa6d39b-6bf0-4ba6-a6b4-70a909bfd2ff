# Redis Subscriber Service Initializer
# This service handles cross-service synchronization events from Game Service

Rails.application.config.after_initialize do
  # Only start the subscriber in non-test environments
  unless Rails.env.test?
    begin
      # Initialize the Redis subscriber service
      redis_subscriber = RedisSubscriberService.new
      
      # Start the subscriber service
      redis_subscriber.start
      
      # Store reference for graceful shutdown
      Rails.application.config.redis_subscriber = redis_subscriber
      
      Rails.logger.info "Redis subscriber service initialized and started"
      
      # Set up graceful shutdown
      at_exit do
        begin
          if Rails.application.config.redis_subscriber&.running?
            Rails.logger.info "Shutting down Redis subscriber service..."
            Rails.application.config.redis_subscriber.stop
            Rails.logger.info "Redis subscriber service stopped gracefully"
          end
        rescue StandardError => e
          Rails.logger.error "Error during Redis subscriber shutdown: #{e.message}"
        end
      end
      
    rescue StandardError => e
      Rails.logger.error "Failed to initialize Redis subscriber service: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end
end

# Health check method for monitoring
class RedisSubscriberHealth<PERSON>heck
  def self.healthy?
    subscriber = Rails.application.config.redis_subscriber
    subscriber&.running? || false
  end
  
  def self.status
    subscriber = Rails.application.config.redis_subscriber
    
    if subscriber.nil?
      { status: 'not_initialized', running: false }
    elsif subscriber.running?
      { status: 'running', running: true }
    else
      { status: 'stopped', running: false }
    end
  end
end
