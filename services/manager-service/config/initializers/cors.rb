# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin Ajax requests.

# Read more: https://github.com/cyu/rack-cors

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins do |source, env|
      # Allow requests from configured origins
      allowed_origins = ENV['CORS_ALLOWED_ORIGINS']&.split(',') || []

      # In development, allow localhost with any port
      if Rails.env.development?
        allowed_origins << /\Ahttp:\/\/localhost:\d+\z/
        allowed_origins << /\Ahttp:\/\/127\.0\.0\.1:\d+\z/
        allowed_origins << /\Ahttp:\/\/0\.0\.0\.0:\d+\z/
        allowed_origins << /\Ahttps:\/\/localhost:\d+\z/
      end

      # In production, only allow specific configured origins
      if Rails.env.production? && allowed_origins.empty?
        Rails.logger.warn "No CORS origins configured for production"
        return false
      end

      allowed_origins.any? do |origin|
        if origin.is_a?(Regexp)
          origin.match?(source)
        else
          origin == source
        end
      end
    end

    resource '*',
      headers: :any,
      methods: [:get, :post, :put, :patch, :delete, :options, :head],
      credentials: true,
      expose: ['Authorization', 'Content-Type', 'X-Request-ID', 'X-User-ID', 'X-Total-Count']
  end
end

# Additional CORS configuration for WebSocket connections
if defined?(ActionCable)
  allowed_ws_origins = ENV['CORS_ALLOWED_ORIGINS']&.split(',') || []

  if Rails.env.development?
    # In development, allow any localhost origin for WebSocket
    ActionCable.server.config.allowed_request_origins = nil
  else
    ActionCable.server.config.allowed_request_origins = allowed_ws_origins
  end
end
