# Security configuration for the Manager Service

# Rate limiting configuration
Rails.application.config.rate_limiting = {
  enabled: ENV['RATE_LIMITING_ENABLED'] != 'false',
  requests_per_minute: ENV['RATE_LIMIT_REQUESTS']&.to_i || 100,
  burst_limit: ENV['RATE_LIMIT_BURST']&.to_i || 200,
  redis_key_prefix: 'rate_limit:manager_service'
}

# JWT configuration
Rails.application.config.jwt = {
  secret_key: ENV['JWT_SECRET'] || ENV['JWT_SECRET_KEY'] || ENV['SECRET_KEY_BASE'] || 'fallback_jwt_secret_for_development',
  algorithm: 'HS256',
  expiration: ENV['JWT_EXPIRATION']&.to_i || 24.hours.to_i,
  refresh_expiration: ENV['JWT_REFRESH_EXPIRATION']&.to_i || 7.days.to_i,
  issuer: <PERSON>NV['JWT_ISSUER'] || 'xzgame-auth-service',
  audience: ENV['JWT_AUDIENCE'] || 'xzgame-api,xzgame-game-service'
}

# Password requirements
Rails.application.config.password_requirements = {
  min_length: ENV['PASSWORD_MIN_LENGTH']&.to_i || 8,
  max_length: ENV['PASSWORD_MAX_LENGTH']&.to_i || 128,
  require_uppercase: ENV['PASSWORD_REQUIRE_UPPERCASE'] != 'false',
  require_lowercase: ENV['PASSWORD_REQUIRE_LOWERCASE'] != 'false',
  require_numbers: ENV['PASSWORD_REQUIRE_NUMBERS'] != 'false',
  require_special_chars: ENV['PASSWORD_REQUIRE_SPECIAL'] != 'false',
  forbidden_passwords: %w[password 123456 qwerty admin user guest]
}

# Session security
Rails.application.config.session_store :disabled

# Force SSL in production
if Rails.env.production?
  Rails.application.config.force_ssl = ENV['FORCE_SSL'] != 'false'
end

# Security headers
Rails.application.config.force_ssl = false unless Rails.env.production?

# Content Security Policy
Rails.application.configure do
  config.content_security_policy do |policy|
    policy.default_src :self
    policy.font_src    :self, :data
    policy.img_src     :self, :data, :https
    policy.object_src  :none
    policy.script_src  :self
    policy.style_src   :self, :unsafe_inline
    policy.connect_src :self, :https, 'ws:', 'wss:'

    # Allow connections to Game Service and other internal services
    if ENV['GAME_SERVICE_URL']
      game_service_host = URI.parse(ENV['GAME_SERVICE_URL']).host
      policy.connect_src :self, :https, 'ws:', 'wss:', "https://#{game_service_host}", "wss://#{game_service_host}"
    end
  end

  # Generate session nonces for permitted importmap and inline scripts
  config.content_security_policy_nonce_generator = ->(request) { request.session.id.to_s }
  config.content_security_policy_nonce_directives = %w(script-src)

  # Report violations without enforcing the policy
  config.content_security_policy_report_only = Rails.env.development?
end

# Additional security headers
Rails.application.config.middleware.use Rack::Attack if defined?(Rack::Attack)

# Configure Rack::Attack if available
if defined?(Rack::Attack)
  # Throttle requests by IP
  Rack::Attack.throttle('requests by ip', limit: 100, period: 1.minute) do |request|
    request.ip unless request.path.start_with?('/health')
  end

  # Throttle login attempts
  Rack::Attack.throttle('login attempts by ip', limit: 5, period: 1.minute) do |request|
    if request.path == '/auth/login' && request.post?
      request.ip
    end
  end

  # Throttle login attempts by username
  Rack::Attack.throttle('login attempts by username', limit: 5, period: 1.minute) do |request|
    if request.path == '/auth/login' && request.post?
      # Extract username from request body
      begin
        body = request.body.read
        request.body.rewind
        params = JSON.parse(body)
        params['username']&.downcase
      rescue JSON::ParserError
        nil
      end
    end
  end

  # Block requests from known bad IPs
  Rack::Attack.blocklist('block bad ips') do |request|
    bad_ips = ENV['BLOCKED_IPS']&.split(',') || []
    bad_ips.include?(request.ip)
  end

  # Allow requests from whitelisted IPs
  Rack::Attack.safelist('allow from localhost') do |request|
    ['127.0.0.1', '::1'].include?(request.ip) && Rails.env.development?
  end

  # Custom response for throttled requests
  Rack::Attack.throttled_responder = lambda do |request|
    match_data = request.env['rack.attack.match_data']
    now = match_data[:epoch_time]

    headers = {
      'Content-Type' => 'application/json',
      'Retry-After' => match_data[:period].to_s,
      'X-RateLimit-Limit' => match_data[:limit].to_s,
      'X-RateLimit-Remaining' => '0',
      'X-RateLimit-Reset' => (now + match_data[:period]).to_s
    }

    body = {
      error: 'Rate limit exceeded',
      message: 'Too many requests. Please try again later.',
      retry_after: match_data[:period]
    }.to_json

    [429, headers, [body]]
  end

  # Custom response for blocked requests
  Rack::Attack.blocklisted_responder = lambda do |request|
    [403, { 'Content-Type' => 'application/json' }, [{
      error: 'Forbidden',
      message: 'Your IP address has been blocked.'
    }.to_json]]
  end
end

# Logging configuration for security events
Rails.application.configure do
  config.log_tags = [
    :request_id,
    lambda { |request| "IP:#{request.remote_ip}" },
    lambda { |request| "User-Agent:#{request.user_agent&.truncate(50)}" }
  ]
end

# API versioning
Rails.application.config.api_version = ENV['API_VERSION'] || 'v1'

# Request timeout configuration
Rails.application.config.request_timeout = ENV['REQUEST_TIMEOUT']&.to_i || 30

# Maximum request size (in bytes)
Rails.application.config.max_request_size = ENV['MAX_REQUEST_SIZE']&.to_i || 10.megabytes

# Audit logging configuration
Rails.application.config.audit_logging = {
  enabled: ENV['AUDIT_LOGGING_ENABLED'] != 'false',
  log_level: ENV['AUDIT_LOG_LEVEL'] || 'info',
  include_request_body: ENV['AUDIT_LOG_REQUEST_BODY'] == 'true',
  include_response_body: ENV['AUDIT_LOG_RESPONSE_BODY'] == 'true',
  sensitive_fields: %w[password token secret key authorization]
}

# Health check configuration
Rails.application.config.health_check = {
  enabled: true,
  path: '/health',
  detailed_path: '/health/detailed',
  checks: %w[database redis game_service]
}

# Maintenance mode configuration
Rails.application.config.maintenance_mode = {
  enabled: ENV['MAINTENANCE_MODE'] == 'true',
  message: ENV['MAINTENANCE_MESSAGE'] || 'Service is temporarily unavailable for maintenance.',
  allowed_ips: ENV['MAINTENANCE_ALLOWED_IPS']&.split(',') || ['127.0.0.1']
}

# Error handling configuration
Rails.application.config.error_handling = {
  show_detailed_errors: Rails.env.development?,
  log_errors: true,
  notify_errors: ENV['ERROR_NOTIFICATIONS_ENABLED'] == 'true',
  error_notification_webhook: ENV['ERROR_NOTIFICATION_WEBHOOK']
}

# Service discovery configuration
Rails.application.config.service_discovery = {
  enabled: ENV['SERVICE_DISCOVERY_ENABLED'] == 'true',
  consul_url: ENV['CONSUL_URL'],
  service_name: 'manager-service',
  service_port: ENV['PORT'] || 3000,
  health_check_interval: ENV['HEALTH_CHECK_INTERVAL']&.to_i || 30
}
