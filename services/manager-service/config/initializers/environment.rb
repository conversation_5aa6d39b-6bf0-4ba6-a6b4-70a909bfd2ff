# Environment-specific configuration for the Manager Service

# Service configuration
Rails.application.config.service = {
  name: 'manager-service',
  version: ENV['SERVICE_VERSION'] || '1.0.0',
  environment: Rails.env,
  port: ENV['PORT'] || 3000,
  host: ENV['HOST'] || '0.0.0.0',
  base_url: ENV['BASE_URL'] || "http://localhost:#{ENV['PORT'] || 3000}"
}

# External service URLs
Rails.application.config.external_services = {
  game_service: {
    url: ENV['GAME_SERVICE_URL'] || 'http://localhost:50051',
    timeout: ENV['GAME_SERVICE_TIMEOUT']&.to_i || 10,
    retry_attempts: ENV['GAME_SERVICE_RETRY_ATTEMPTS']&.to_i || 3,
    retry_delay: ENV['GAME_SERVICE_RETRY_DELAY']&.to_i || 1
  },
  notification_service: {
    url: ENV['NOTIFICATION_SERVICE_URL'],
    timeout: ENV['NOTIFICATION_SERVICE_TIMEOUT']&.to_i || 5,
    enabled: ENV['NOTIFICATION_SERVICE_ENABLED'] != 'false'
  },
  analytics_service: {
    url: ENV['ANALYTICS_SERVICE_URL'],
    timeout: ENV['ANALYTICS_SERVICE_TIMEOUT']&.to_i || 5,
    enabled: ENV['ANALYTICS_SERVICE_ENABLED'] == 'true'
  }
}

# Database configuration
Rails.application.config.database = {
  pool_size: ENV['DB_POOL_SIZE']&.to_i || 25,
  timeout: ENV['DB_TIMEOUT']&.to_i || 5000,
  retry_attempts: ENV['DB_RETRY_ATTEMPTS']&.to_i || 3,
  retry_delay: ENV['DB_RETRY_DELAY']&.to_i || 1
}

# Redis configuration
Rails.application.config.redis_config = {
  url: ENV['REDIS_URL'] || 'redis://localhost:6379/0',
  pool_size: ENV['REDIS_POOL_SIZE']&.to_i || 25,
  timeout: ENV['REDIS_TIMEOUT']&.to_i || 5,
  reconnect_attempts: ENV['REDIS_RECONNECT_ATTEMPTS']&.to_i || 3,
  reconnect_delay: ENV['REDIS_RECONNECT_DELAY']&.to_i || 1,
  namespace: ENV['REDIS_NAMESPACE'] || 'manager_service'
}

# Sidekiq configuration
Rails.application.config.sidekiq = {
  redis_url: ENV['SIDEKIQ_REDIS_URL'] || ENV['REDIS_URL'] || 'redis://localhost:6379/1',
  concurrency: ENV['SIDEKIQ_CONCURRENCY']&.to_i || 10,
  queues: {
    critical: ENV['SIDEKIQ_CRITICAL_QUEUE_WEIGHT']&.to_i || 10,
    high: ENV['SIDEKIQ_HIGH_QUEUE_WEIGHT']&.to_i || 5,
    default: ENV['SIDEKIQ_DEFAULT_QUEUE_WEIGHT']&.to_i || 3,
    low: ENV['SIDEKIQ_LOW_QUEUE_WEIGHT']&.to_i || 1
  },
  retry_attempts: ENV['SIDEKIQ_RETRY_ATTEMPTS']&.to_i || 5,
  dead_job_retention: ENV['SIDEKIQ_DEAD_JOB_RETENTION']&.to_i || 7.days.to_i
}

# Logging configuration
Rails.application.config.logging = {
  level: ENV['LOG_LEVEL'] || 'info',
  format: ENV['LOG_FORMAT'] || 'json',
  output: ENV['LOG_OUTPUT'] || 'stdout',
  file_path: ENV['LOG_FILE_PATH'],
  max_file_size: ENV['LOG_MAX_FILE_SIZE'] || '100MB',
  max_files: ENV['LOG_MAX_FILES']&.to_i || 5,
  structured_logging: ENV['STRUCTURED_LOGGING'] != 'false'
}

# Monitoring and metrics
Rails.application.config.monitoring = {
  enabled: ENV['MONITORING_ENABLED'] != 'false',
  metrics_port: ENV['METRICS_PORT']&.to_i || 9090,
  health_check_port: ENV['HEALTH_CHECK_PORT']&.to_i || 8080,
  prometheus_enabled: ENV['PROMETHEUS_ENABLED'] == 'true',
  datadog_enabled: ENV['DATADOG_ENABLED'] == 'true',
  newrelic_enabled: ENV['NEWRELIC_ENABLED'] == 'true'
}

# Game configuration
Rails.application.config.game_settings = {
  default_currency: ENV['DEFAULT_CURRENCY'] || 'USD',
  min_bet_amount: ENV['MIN_BET_AMOUNT']&.to_f || 1.0,
  max_bet_amount: ENV['MAX_BET_AMOUNT']&.to_f || 10000.0,
  max_concurrent_games: ENV['MAX_CONCURRENT_GAMES']&.to_i || 1000,
  game_timeout: ENV['GAME_TIMEOUT']&.to_i || 300, # 5 minutes
  room_cleanup_interval: ENV['ROOM_CLEANUP_INTERVAL']&.to_i || 3600, # 1 hour
  session_cleanup_interval: ENV['SESSION_CLEANUP_INTERVAL']&.to_i || 1800 # 30 minutes
}

# Balance and transaction configuration
Rails.application.config.balance_settings = {
  min_balance: ENV['MIN_BALANCE']&.to_f || 0.0,
  max_balance: ENV['MAX_BALANCE']&.to_f || 1000000.0,
  max_transaction_amount: ENV['MAX_TRANSACTION_AMOUNT']&.to_f || 50000.0,
  transaction_fee_percentage: ENV['TRANSACTION_FEE_PERCENTAGE']&.to_f || 0.0,
  daily_withdrawal_limit: ENV['DAILY_WITHDRAWAL_LIMIT']&.to_f || 10000.0,
  monthly_withdrawal_limit: ENV['MONTHLY_WITHDRAWAL_LIMIT']&.to_f || 100000.0
}

# User configuration
Rails.application.config.user_settings = {
  max_concurrent_sessions: ENV['MAX_CONCURRENT_SESSIONS']&.to_i || 3,
  session_timeout: ENV['SESSION_TIMEOUT']&.to_i || 24.hours.to_i,
  max_failed_login_attempts: ENV['MAX_FAILED_LOGIN_ATTEMPTS']&.to_i || 5,
  account_lockout_duration: ENV['ACCOUNT_LOCKOUT_DURATION']&.to_i || 30.minutes.to_i,
  password_reset_token_expiry: ENV['PASSWORD_RESET_TOKEN_EXPIRY']&.to_i || 1.hour.to_i,
  email_verification_required: ENV['EMAIL_VERIFICATION_REQUIRED'] == 'true'
}

# Feature flags
Rails.application.config.feature_flags = {
  prizewheel_enabled: ENV['PRIZEWHEEL_ENABLED'] != 'false',
  amidakuji_enabled: ENV['AMIDAKUJI_ENABLED'] != 'false',
  private_rooms_enabled: ENV['PRIVATE_ROOMS_ENABLED'] != 'false',
  room_passwords_enabled: ENV['ROOM_PASSWORDS_ENABLED'] != 'false',
  spectator_mode_enabled: ENV['SPECTATOR_MODE_ENABLED'] == 'true',
  tournament_mode_enabled: ENV['TOURNAMENT_MODE_ENABLED'] == 'true',
  social_features_enabled: ENV['SOCIAL_FEATURES_ENABLED'] == 'true',
  analytics_enabled: ENV['ANALYTICS_ENABLED'] == 'true',
  admin_panel_enabled: ENV['ADMIN_PANEL_ENABLED'] != 'false'
}

# Cache configuration
Rails.application.config.cache_settings = {
  default_ttl: ENV['CACHE_DEFAULT_TTL']&.to_i || 1.hour.to_i,
  user_cache_ttl: ENV['USER_CACHE_TTL']&.to_i || 30.minutes.to_i,
  room_cache_ttl: ENV['ROOM_CACHE_TTL']&.to_i || 5.minutes.to_i,
  settings_cache_ttl: ENV['SETTINGS_CACHE_TTL']&.to_i || 1.hour.to_i,
  transaction_cache_ttl: ENV['TRANSACTION_CACHE_TTL']&.to_i || 10.minutes.to_i
}

# Background job configuration
Rails.application.config.background_jobs = {
  transaction_processor_enabled: ENV['TRANSACTION_PROCESSOR_ENABLED'] != 'false',
  settings_sync_enabled: ENV['SETTINGS_SYNC_ENABLED'] != 'false',
  room_cleanup_enabled: ENV['ROOM_CLEANUP_ENABLED'] != 'false',
  session_cleanup_enabled: ENV['SESSION_CLEANUP_ENABLED'] != 'false',
  notification_enabled: ENV['NOTIFICATION_JOBS_ENABLED'] != 'false',
  analytics_enabled: ENV['ANALYTICS_JOBS_ENABLED'] == 'true'
}

# Development-specific configuration
if Rails.env.development?
  Rails.application.config.development = {
    auto_reload: true,
    debug_mode: ENV['DEBUG_MODE'] == 'true',
    verbose_logging: ENV['VERBOSE_LOGGING'] == 'true',
    mock_external_services: ENV['MOCK_EXTERNAL_SERVICES'] == 'true',
    seed_data_enabled: ENV['SEED_DATA_ENABLED'] != 'false'
  }
end

# Test-specific configuration
if Rails.env.test?
  Rails.application.config.test = {
    parallel_workers: ENV['PARALLEL_WORKERS']&.to_i || 1,
    database_cleaner_strategy: ENV['DATABASE_CLEANER_STRATEGY'] || 'truncation',
    mock_external_services: true,
    disable_background_jobs: ENV['DISABLE_BACKGROUND_JOBS'] != 'false'
  }
end

# Production-specific configuration
if Rails.env.production?
  Rails.application.config.production = {
    precompile_assets: true,
    serve_static_files: ENV['SERVE_STATIC_FILES'] == 'true',
    log_to_stdout: ENV['LOG_TO_STDOUT'] != 'false',
    force_ssl: ENV['FORCE_SSL'] != 'false',
    asset_host: ENV['ASSET_HOST'],
    cdn_url: ENV['CDN_URL']
  }
end

# Validation of required environment variables
required_env_vars = []

if Rails.env.production?
  required_env_vars += %w[
    JWT_SECRET_KEY
    MONGODB_URL
    REDIS_URL
    GAME_SERVICE_URL
  ]
end

missing_env_vars = required_env_vars.select { |var| ENV[var].blank? }

if missing_env_vars.any?
  Rails.logger.error "Missing required environment variables: #{missing_env_vars.join(', ')}"
  
  if Rails.env.production?
    raise "Missing required environment variables: #{missing_env_vars.join(', ')}"
  end
end

# Log configuration summary
# Rails.logger.info "Manager Service Configuration Loaded",
#   service_name: Rails.application.config.service[:name],
#   version: Rails.application.config.service[:version],
#   environment: Rails.env,
#   port: Rails.application.config.service[:port],
#   features: Rails.application.config.feature_flags.select { |k, v| v }.keys

Rails.logger.info(
  "Manager Service Configuration Loaded - " +
  "service=#{Rails.application.config.service[:name]} " +
  "version=#{Rails.application.config.service[:version]} " +
  "environment=#{Rails.env} " +
  "port=#{Rails.application.config.service[:port]} " +
  "features=#{Rails.application.config.feature_flags.select { |k, v| v }.keys.join(',')}"
)
