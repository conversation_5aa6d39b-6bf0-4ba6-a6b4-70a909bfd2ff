#!/usr/bin/env ruby

# Simple integration test for room reconnection and prize pool logic
# Run this from the manager-service directory: ruby simple_reconnection_test.rb

require_relative 'config/environment'

class SimpleReconnectionTest
  def initialize
    @test_results = []
  end

  def run_all_tests
    puts "🚀 Starting Simple Room Reconnection and Prize Pool Tests"
    puts "=" * 60

    setup_test_data
    
    test_initial_room_join
    test_prize_pool_on_ready_status
    test_player_reconnection
    test_multiple_players_prize_pool
    test_player_leave_with_ready_status
    
    cleanup_test_data
    
    print_results
  end

  private

  def setup_test_data
    puts "\n📋 Setting up test data..."
    
    # Create test users
    @user1 = User.find_or_create_by(username: 'simple_test_user1') do |user|
      user.email = '<EMAIL>'
      user.password = 'password123'
      user.balance = 1000.0
      user.status = 'active'
      user.role = 'player'
    end

    @user2 = User.find_or_create_by(username: 'simple_test_user2') do |user|
      user.email = '<EMAIL>'
      user.password = 'password123'
      user.balance = 1000.0
      user.status = 'active'
      user.role = 'player'
    end

    # Create admin user for room creation
    @admin = User.find_or_create_by(username: 'simple_test_admin') do |user|
      user.email = '<EMAIL>'
      user.password = 'password123'
      user.balance = 10000.0
      user.status = 'active'
      user.role = 'admin'
    end

    # Create test room
    @room = Room.create!(
      name: 'Simple Test Room',
      game_type: 'prizewheel',
      bet_amount: 100,
      max_players: 4,
      status: 'waiting',
      current_players: 0,
      prize_pool: 0,
      is_private: false,
      external_room_id: "simple_test_room_#{Time.now.to_i}",
      creator: @admin
    )

    puts "✅ Test data created:"
    puts "   - User 1: #{@user1.username} (Balance: #{@user1.balance})"
    puts "   - User 2: #{@user2.username} (Balance: #{@user2.balance})"
    puts "   - Room: #{@room.name} (Bet: #{@room.bet_amount})"
  end

  def test_initial_room_join
    test_name = "Initial Room Join (No Prize Pool Update)"
    puts "\n🧪 Testing: #{test_name}"

    begin
      # User 1 joins room
      initial_prize_pool = @room.prize_pool.to_f
      result = @room.add_player!(@user1)
      @room.reload

      # Verify player was added but prize pool unchanged
      assert_equal(@room.current_players, 1, "Player count should be 1")
      assert_equal(@room.prize_pool.to_f, initial_prize_pool, "Prize pool should not change on join")
      assert_instance_of(GameSession, result, "Should return new GameSession")
      assert_equal(result.user, @user1, "Session should belong to user1")

      record_test_result(test_name, true, "Player joined successfully without prize pool update")
    rescue => e
      record_test_result(test_name, false, "Error: #{e.message}")
    end
  end

  def test_prize_pool_on_ready_status
    test_name = "Prize Pool Updates on Ready Status"
    puts "\n🧪 Testing: #{test_name}"

    begin
      initial_prize_pool = @room.prize_pool.to_f

      # User 1 becomes ready - prize pool should increase
      @room.set_player_ready!(@user1, true)
      @room.reload
      
      expected_prize_pool = initial_prize_pool + @room.bet_amount.to_f
      assert_equal(@room.prize_pool.to_f, expected_prize_pool, "Prize pool should increase when player becomes ready")
      assert_equal(@room.ready_players_count, 1, "Ready count should be 1")

      # User 1 becomes not ready - prize pool should decrease
      @room.set_player_ready!(@user1, false)
      @room.reload
      
      assert_equal(@room.prize_pool.to_f, initial_prize_pool, "Prize pool should decrease when player becomes not ready")
      assert_equal(@room.ready_players_count, 0, "Ready count should be 0")

      record_test_result(test_name, true, "Prize pool correctly updated based on ready status")
    rescue => e
      record_test_result(test_name, false, "Error: #{e.message}")
    end
  end

  def test_player_reconnection
    test_name = "Player Reconnection Logic"
    puts "\n🧪 Testing: #{test_name}"

    begin
      # Set user1 as ready first
      @room.set_player_ready!(@user1, true)
      initial_player_count = @room.current_players
      initial_prize_pool = @room.prize_pool.to_f
      initial_session_count = GameSession.count

      # User 1 "reconnects" (tries to join again)
      result = @room.add_player!(@user1)
      @room.reload

      # Verify reconnection behavior
      assert_equal(@room.current_players, initial_player_count, "Player count should not change on reconnection")
      assert_equal(@room.prize_pool.to_f, initial_prize_pool, "Prize pool should not change on reconnection")
      assert_equal(GameSession.count, initial_session_count, "No new session should be created")
      assert_instance_of(GameSession, result, "Should return existing GameSession")
      assert_equal(result.metadata['is_ready'], true, "Ready status should be preserved")

      record_test_result(test_name, true, "Player reconnection preserved state without duplicates")
    rescue => e
      record_test_result(test_name, false, "Error: #{e.message}")
    end
  end

  def test_multiple_players_prize_pool
    test_name = "Multiple Players Prize Pool Management"
    puts "\n🧪 Testing: #{test_name}"

    begin
      # Reset room state
      @room.set_player_ready!(@user1, false)
      
      # Add second player
      @room.add_player!(@user2)
      @room.reload
      
      initial_prize_pool = @room.prize_pool.to_f
      assert_equal(@room.current_players, 2, "Should have 2 players")
      assert_equal(@room.prize_pool.to_f, 0, "Prize pool should be 0 with no ready players")

      # User 1 becomes ready
      @room.set_player_ready!(@user1, true)
      @room.reload
      assert_equal(@room.prize_pool.to_f, @room.bet_amount.to_f, "Prize pool should be 1x bet amount")
      assert_equal(@room.ready_players_count, 1, "Ready count should be 1")

      # User 2 becomes ready
      @room.set_player_ready!(@user2, true)
      @room.reload
      assert_equal(@room.prize_pool.to_f, @room.bet_amount.to_f * 2, "Prize pool should be 2x bet amount")
      assert_equal(@room.ready_players_count, 2, "Ready count should be 2")

      # User 1 becomes not ready
      @room.set_player_ready!(@user1, false)
      @room.reload
      assert_equal(@room.prize_pool.to_f, @room.bet_amount.to_f, "Prize pool should be 1x bet amount")
      assert_equal(@room.ready_players_count, 1, "Ready count should be 1")

      record_test_result(test_name, true, "Multiple players prize pool managed correctly")
    rescue => e
      record_test_result(test_name, false, "Error: #{e.message}")
    end
  end

  def test_player_leave_with_ready_status
    test_name = "Player Leave with Ready Status"
    puts "\n🧪 Testing: #{test_name}"

    begin
      # Ensure user2 is ready
      @room.set_player_ready!(@user2, true)
      initial_prize_pool = @room.prize_pool.to_f
      initial_player_count = @room.current_players

      # User 2 leaves room (and was ready)
      @room.remove_player!(@user2)
      @room.reload

      expected_player_count = initial_player_count - 1
      expected_prize_pool = initial_prize_pool - @room.bet_amount.to_f

      assert_equal(@room.current_players, expected_player_count, "Player count should decrease")
      assert_equal(@room.prize_pool.to_f, expected_prize_pool, "Prize pool should decrease by bet amount")

      record_test_result(test_name, true, "Player leave correctly updated prize pool")
    rescue => e
      record_test_result(test_name, false, "Error: #{e.message}")
    end
  end

  def cleanup_test_data
    puts "\n🧹 Cleaning up test data..."
    
    # Remove game sessions
    GameSession.where(room: @room).destroy_all
    
    # Remove room
    @room.destroy if @room
    
    # Remove test users
    [@user1, @user2, @admin].compact.each(&:destroy)
    
    puts "✅ Test data cleaned up"
  end

  def record_test_result(test_name, passed, message)
    @test_results << {
      name: test_name,
      passed: passed,
      message: message
    }
    
    status = passed ? "✅ PASS" : "❌ FAIL"
    puts "   #{status}: #{message}"
  end

  def print_results
    puts "\n" + "=" * 60
    puts "📊 TEST RESULTS SUMMARY"
    puts "=" * 60

    passed_count = @test_results.count { |r| r[:passed] }
    total_count = @test_results.count

    @test_results.each do |result|
      status = result[:passed] ? "✅" : "❌"
      puts "#{status} #{result[:name]}"
      puts "   #{result[:message]}" unless result[:passed]
    end

    puts "\n📈 Overall Results: #{passed_count}/#{total_count} tests passed"
    
    if passed_count == total_count
      puts "🎉 All tests passed! Room reconnection and prize pool logic working correctly."
    else
      puts "⚠️  Some tests failed. Please review the implementation."
      exit(1)
    end
  end

  def assert_equal(actual, expected, message)
    unless actual == expected
      raise "Assertion failed: #{message}. Expected: #{expected}, Actual: #{actual}"
    end
  end

  def assert_instance_of(object, klass, message)
    unless object.is_a?(klass)
      raise "Assertion failed: #{message}. Expected instance of #{klass}, got #{object.class}"
    end
  end
end

# Run the tests
if __FILE__ == $0
  test_runner = SimpleReconnectionTest.new
  test_runner.run_all_tests
end
