# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Starting database seeding..."

# Create default admin user
admin_username = ENV['ADMIN_USERNAME'] || 'admin'
admin_email = ENV['ADMIN_EMAIL'] || '<EMAIL>'
admin_password = ENV['ADMIN_PASSWORD'] || 'admin123456'

puts "Creating admin user..."

begin
  admin_user = User.find_or_initialize_by(username: admin_username)

  if admin_user.persisted?
    puts "  ✅ Admin user '#{admin_username}' already exists"
  else
    admin_user.assign_attributes(
      email: admin_email,
      password: admin_password,
      password_confirmation: admin_password,
      role: 'admin',
      status: 'active',
      balance: 10000.0, # Give admin user some initial balance
      metadata: {
        created_by: 'seed',
        created_at: Time.current,
        description: 'Default admin user created during database seeding'
      }
    )

    if admin_user.save
      puts "  ✅ Admin user '#{admin_username}' created successfully"
      puts "     Email: #{admin_email}"
      puts "     Role: #{admin_user.role}"
      puts "     Status: #{admin_user.status}"
      puts "     Balance: #{admin_user.balance}"
    else
      puts "  ❌ Failed to create admin user:"
      admin_user.errors.full_messages.each do |error|
        puts "     - #{error}"
      end
    end
  end
rescue => e
  puts "  ❌ Error creating admin user: #{e.message}"
  puts "     #{e.backtrace.first}"
end

# Create default game settings if they don't exist
puts "\nCreating default game settings..."

begin
  # Use the existing seed_default_settings method from GameSetting model
  existing_settings_count = GameSetting.count

  GameSetting.seed_default_settings

  new_settings_count = GameSetting.count
  created_count = new_settings_count - existing_settings_count

  if created_count > 0
    puts "  ✅ Created #{created_count} new game settings"
  else
    puts "  ✅ All default game settings already exist"
  end

  # Display summary of settings
  puts "  📊 Game settings summary:"
  %w[prizewheel amidakuji global].each do |game_type|
    count = GameSetting.by_game_type(game_type).active.count
    puts "     #{game_type.capitalize}: #{count} settings"
  end

rescue => e
  puts "  ❌ Error creating game settings: #{e.message}"
  puts "     #{e.backtrace.first}"
end

puts "\n🎉 Database seeding completed!"
puts "\nAdmin user credentials:"
puts "  Username: #{admin_username}"
puts "  Email: #{admin_email}"
puts "  Password: #{admin_password}"
puts "\n⚠️  Please change the admin password after first login!"
