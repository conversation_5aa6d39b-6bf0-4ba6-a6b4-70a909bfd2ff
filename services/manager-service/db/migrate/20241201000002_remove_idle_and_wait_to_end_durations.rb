class RemoveIdleAndWaitToEndDurations < ActiveRecord::Migration[7.0]
  def up
    puts "Removing idle and wait_to_end duration settings..."
    
    # Remove idle_duration and wait_to_end_duration settings for all game types
    %w[prizewheel amidakuji].each do |game_type|
      %w[idle_duration wait_to_end_duration].each do |duration_key|
        setting = GameSetting.find_by(game_type: game_type, setting_key: duration_key)
        if setting
          setting.destroy
          puts "  Removed #{game_type}.#{duration_key}"
        else
          puts "  Skipped #{game_type}.#{duration_key} (not found)"
        end
      end
    end
    
    puts "Idle and wait_to_end duration settings removal completed!"
  end
  
  def down
    puts "Restoring idle and wait_to_end duration settings..."
    
    # Restore idle_duration and wait_to_end_duration settings
    prizewheel_durations = {
      'idle_duration' => 300,
      'wait_to_end_duration' => 5
    }
    
    amidakuji_durations = {
      'idle_duration' => 300,
      'wait_to_end_duration' => 5
    }
    
    # Add Prize Wheel durations
    prizewheel_durations.each do |key, value|
      existing = GameSetting.find_by(game_type: 'prizewheel', setting_key: key)
      unless existing
        GameSetting.create!(
          game_type: 'prizewheel',
          setting_key: key,
          setting_value: value,
          data_type: 'integer',
          description: "Duration for #{key.gsub('_duration', '')} state in seconds",
          is_active: true,
          version: 1,
          sync_required: true
        )
        puts "  Restored prizewheel.#{key} = #{value}"
      end
    end
    
    # Add Amidakuji durations
    amidakuji_durations.each do |key, value|
      existing = GameSetting.find_by(game_type: 'amidakuji', setting_key: key)
      unless existing
        GameSetting.create!(
          game_type: 'amidakuji',
          setting_key: key,
          setting_value: value,
          data_type: 'integer',
          description: "Duration for #{key.gsub('_duration', '')} state in seconds",
          is_active: true,
          version: 1,
          sync_required: true
        )
        puts "  Restored amidakuji.#{key} = #{value}"
      end
    end
    
    puts "Idle and wait_to_end duration settings restoration completed!"
  end
end
