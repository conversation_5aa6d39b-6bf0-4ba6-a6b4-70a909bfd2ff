class AddGameStateDurations < ActiveRecord::Migration[7.0]
  def up
    # This migration adds game state duration settings to the GameSetting collection
    # Since we're using MongoDB with Mongoid, we'll use Ruby code to seed the data
    
    puts "Adding game state duration settings..."
    
    # Prize Wheel state durations
    prizewheel_durations = {
      'idle_duration' => 300,        # 5 minutes - waiting for players
      'starting_duration' => 10,     # 10 seconds - countdown before game starts
      'playing_duration' => 30,      # 30 seconds - actual game play
      'wait_to_end_duration' => 5,   # 5 seconds - processing results
      'end_duration' => 15           # 15 seconds - showing results before cleanup
    }
    
    # Amidakuji state durations
    amidakuji_durations = {
      'idle_duration' => 300,        # 5 minutes - waiting for players
      'starting_duration' => 10,     # 10 seconds - countdown before game starts
      'playing_duration' => 60,      # 60 seconds - actual game play (longer for Amidakuji)
      'wait_to_end_duration' => 5,   # 5 seconds - processing results
      'end_duration' => 15           # 15 seconds - showing results before cleanup
    }
    
    # Add Prize Wheel durations
    prizewheel_durations.each do |key, value|
      existing = GameSetting.find_by(game_type: 'prizewheel', setting_key: key)
      unless existing
        GameSetting.create!(
          game_type: 'prizewheel',
          setting_key: key,
          setting_value: value,
          data_type: 'integer',
          description: "Duration for #{key.gsub('_duration', '')} state in seconds",
          is_active: true,
          version: 1,
          sync_required: true
        )
        puts "  Added prizewheel.#{key} = #{value}"
      else
        puts "  Skipped prizewheel.#{key} (already exists)"
      end
    end
    
    # Add Amidakuji durations
    amidakuji_durations.each do |key, value|
      existing = GameSetting.find_by(game_type: 'amidakuji', setting_key: key)
      unless existing
        GameSetting.create!(
          game_type: 'amidakuji',
          setting_key: key,
          setting_value: value,
          data_type: 'integer',
          description: "Duration for #{key.gsub('_duration', '')} state in seconds",
          is_active: true,
          version: 1,
          sync_required: true
        )
        puts "  Added amidakuji.#{key} = #{value}"
      else
        puts "  Skipped amidakuji.#{key} (already exists)"
      end
    end
    
    puts "Game state duration settings migration completed!"
  end
  
  def down
    # Remove the game state duration settings
    puts "Removing game state duration settings..."
    
    duration_keys = [
      'idle_duration',
      'starting_duration', 
      'playing_duration',
      'wait_to_end_duration',
      'end_duration'
    ]
    
    %w[prizewheel amidakuji].each do |game_type|
      duration_keys.each do |key|
        setting = GameSetting.find_by(game_type: game_type, setting_key: key)
        if setting
          setting.destroy
          puts "  Removed #{game_type}.#{key}"
        end
      end
    end
    
    puts "Game state duration settings rollback completed!"
  end
end
