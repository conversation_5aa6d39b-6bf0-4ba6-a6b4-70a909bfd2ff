# Database Seeding Guide

This directory contains database seeding functionality for the XZ Game Manager Service.

## Overview

The seeding system provides several ways to populate the database with initial data:

1. **Admin User Creation** - Creates default admin users
2. **Game Settings** - Sets up default game configurations
3. **Sample Data** - Creates test users and data for development

## Files

- `seeds.rb` - Main seeding file (run with `rails db:seed`)
- `README.md` - This documentation file

## Quick Start

### Create Admin User (Recommended)

```bash
# Using the custom script (easiest)
./bin/seed-admin

# With custom credentials
./bin/seed-<NAME_EMAIL> your_secure_password

# Using environment variables
ADMIN_USERNAME=admin ADMIN_EMAIL=<EMAIL> ADMIN_PASSWORD=secure123 ./bin/seed-admin
```

### Full Database Seeding

```bash
# Seed everything (admin user + game settings)
rails db:seed

# Or using make command
make db-seed
```

## Rake Tasks

### Admin User Management

```bash
# Create admin user with custom credentials
rails db:seed:admin[username,email,password]

# Example
rails db:seed:admin[admin,<EMAIL>,secure123]

# Reset admin password
rails db:seed:reset_admin_password[admin,new_password]

# Show all admin users
rails db:seed:show_admins
```

### Sample Data for Development

```bash
# Create sample users (player1, player2, moderator1)
rails db:seed:sample_users

# Seed everything including sample data
rails db:seed:all
```

## Environment Variables

You can customize the default admin user using environment variables:

```bash
export ADMIN_USERNAME=admin
export ADMIN_EMAIL=<EMAIL>
export ADMIN_PASSWORD=your_secure_password

rails db:seed
```

## Default Admin User

If no environment variables are set, the following defaults are used:

- **Username**: `admin`
- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Role**: `admin`
- **Status**: `active`
- **Initial Balance**: `10000.0`

⚠️ **Security Warning**: Always change the default password in production!

## Game Settings

The seeding process automatically creates default game settings for:

### Prize Wheel Game
- Min/Max players: 2-8
- Min/Max bet: 10.0-1000.0
- Default sections: 8
- Spin duration: 3000-8000ms
- House edge: 5%
- Timeouts: waiting (300s), playing (60s), idle (1800s)

### Amidakuji Game
- Min/Max players: 2-8
- Min/Max bet: 10.0-1000.0
- Rows per player: 3
- Line probability: 50%
- Animation duration: 5000ms
- House edge: 5%
- Timeouts: waiting (300s), playing (90s), idle (1800s)

### Global Settings
- Max concurrent games: 1000
- Maintenance mode: false
- Game type enablement flags

## Sample Users (Development Only)

When using `rails db:seed:sample_users`, the following test users are created:

| Username | Email | Role | Password | Balance |
|----------|-------|------|----------|---------|
| player1 | <EMAIL> | player | password123 | 1000.0 |
| player2 | <EMAIL> | player | password123 | 500.0 |
| moderator1 | <EMAIL> | moderator | password123 | 2000.0 |

## Production Deployment

For production deployment:

1. **Set secure environment variables**:
   ```bash
   export ADMIN_USERNAME=your_admin_username
   export ADMIN_EMAIL=<EMAIL>
   export ADMIN_PASSWORD=very_secure_password_here
   ```

2. **Run seeding**:
   ```bash
   RAILS_ENV=production rails db:seed
   ```

3. **Verify admin user creation**:
   ```bash
   RAILS_ENV=production rails db:seed:show_admins
   ```

## Troubleshooting

### Common Issues

1. **"User already exists" error**
   - The seeding is idempotent - existing users won't be overwritten
   - Use the update option in the interactive script

2. **Password validation errors**
   - Ensure password is at least 6 characters
   - Check User model validations

3. **Email format errors**
   - Ensure email follows valid format
   - Check for typos in email address

4. **MongoDB connection errors**
   - Ensure MongoDB is running
   - Check `config/mongoid.yml` configuration
   - Verify `MONGODB_URL` environment variable

### Checking Seeded Data

```bash
# Check admin users
rails db:seed:show_admins

# Check game settings
rails console
> GameSetting.count
> GameSetting.by_game_type('prizewheel').count
> GameSetting.by_game_type('amidakuji').count
```

## Security Best Practices

1. **Change default passwords** immediately after seeding
2. **Use strong passwords** in production (minimum 12 characters)
3. **Set unique admin usernames** (avoid 'admin')
4. **Use environment variables** for sensitive data
5. **Regularly rotate admin passwords**
6. **Monitor admin user activity**

## Integration with Docker

When using Docker Compose, seeding can be automated:

```yaml
# docker-compose.yml
services:
  manager-service:
    # ... other config
    environment:
      - ADMIN_USERNAME=admin
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=secure_password_here
    command: >
      sh -c "rails db:seed && rails server"
```
