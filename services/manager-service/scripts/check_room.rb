#!/usr/bin/env ruby

# Script to check if room ID "68335cec692398dcafbc4f54" exists in Manager Service database
# and what its game type should be

require_relative '../config/environment'

room_id = "68335cec692398dcafbc4f54"

puts "🔍 Checking Manager Service database for room ID: #{room_id}"
puts "=" * 60

begin
  # Try to find room by MongoDB ObjectId
  room = Room.find(room_id)
  
  puts "✅ ROOM FOUND in Manager Service!"
  puts "Room Details:"
  puts "  - ID: #{room.id}"
  puts "  - External Room ID: #{room.external_room_id}"
  puts "  - Name: #{room.name}"
  puts "  - Game Type: #{room.game_type}"
  puts "  - Status: #{room.status}"
  puts "  - Max Players: #{room.max_players}"
  puts "  - Current Players: #{room.current_players}"
  puts "  - Bet Amount: #{room.bet_amount}"
  puts "  - Currency: #{room.currency}"
  puts "  - Is Private: #{room.is_private}"
  puts "  - Created At: #{room.created_at}"
  puts "  - Updated At: #{room.updated_at}"
  puts "  - Creator ID: #{room.creator_id}"
  puts "  - Sync Required: #{room.sync_required}"
  puts "  - Last Synced At: #{room.last_synced_at}"
  
  if room.game_specific_config.present?
    puts "  - Game Specific Config:"
    room.game_specific_config.each do |key, value|
      puts "    - #{key}: #{value}"
    end
  end
  
  if room.configuration.present?
    puts "  - Configuration:"
    room.configuration.each do |key, value|
      puts "    - #{key}: #{value}"
    end
  end
  
  puts "\n🎯 EXPECTED GAME TYPE: #{room.game_type}"
  
rescue Mongoid::Errors::DocumentNotFound
  puts "❌ ROOM NOT FOUND by ObjectId in Manager Service!"
  
  # Try to find by external_room_id
  puts "\n🔍 Searching by external_room_id..."
  room_by_external = Room.where(external_room_id: room_id).first
  
  if room_by_external
    puts "✅ FOUND by external_room_id!"
    puts "Room Details:"
    puts "  - ID: #{room_by_external.id}"
    puts "  - External Room ID: #{room_by_external.external_room_id}"
    puts "  - Name: #{room_by_external.name}"
    puts "  - Game Type: #{room_by_external.game_type}"
    puts "  - Status: #{room_by_external.status}"
    puts "\n🎯 EXPECTED GAME TYPE: #{room_by_external.game_type}"
  else
    puts "❌ ROOM NOT FOUND by external_room_id either!"
    
    # Search for similar room IDs
    puts "\n🔍 Searching for similar room IDs..."
    similar_rooms = Room.where(:external_room_id => /#{room_id[0..10]}/).limit(5)
    
    if similar_rooms.any?
      puts "📋 Found similar rooms:"
      similar_rooms.each do |r|
        puts "  - ID: #{r.id}, External: #{r.external_room_id}, Game Type: #{r.game_type}, Name: #{r.name}"
      end
    else
      puts "❌ No similar rooms found"
    end
    
    # Show recent rooms for context
    puts "\n📋 Recent rooms in database:"
    recent_rooms = Room.desc(:created_at).limit(10)
    recent_rooms.each do |r|
      puts "  - ID: #{r.id}, External: #{r.external_room_id}, Game Type: #{r.game_type}, Name: #{r.name}, Created: #{r.created_at}"
    end
  end
  
rescue StandardError => e
  puts "❌ ERROR: #{e.message}"
  puts "Backtrace: #{e.backtrace.first(5).join("\n")}"
end

puts "\n" + "=" * 60
puts "🔍 Database Statistics:"
puts "  - Total Rooms: #{Room.count}"
puts "  - Waiting Rooms: #{Room.waiting.count}"
puts "  - Playing Rooms: #{Room.playing.count}"
puts "  - Amidakuji Rooms: #{Room.by_game_type('amidakuji').count}"
puts "  - Prize Wheel Rooms: #{Room.by_game_type('prizewheel').count}"

puts "\n📋 All Game Types in database:"
game_types = Room.distinct(:game_type)
game_types.each do |gt|
  count = Room.where(game_type: gt).count
  puts "  - #{gt}: #{count} rooms"
end
