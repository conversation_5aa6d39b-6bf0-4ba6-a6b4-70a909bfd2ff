#!/usr/bin/env ruby

# Fix script for room 684d9baba533d3e5fea7dc35
# This script addresses the specific issue where the room shows current_players: 2 but game_sessions: []

require_relative '../config/environment'

ROOM_ID = '684d9baba533d3e5fea7dc35'

puts "🔧 Fixing Room Data Inconsistency"
puts "=" * 50
puts "Room ID: #{ROOM_ID}"
puts "Timestamp: #{Time.current}"
puts ""

begin
  # Find the room
  room = Room.find(ROOM_ID)
  
  puts "📊 Current Room State:"
  puts "  Name: #{room.name}"
  puts "  Status: #{room.status}"
  puts "  Current Players: #{room.current_players}"
  puts "  Max Players: #{room.max_players}"
  puts "  Created: #{room.created_at}"
  puts "  Updated: #{room.updated_at}"
  puts ""
  
  # Check data consistency
  puts "🔍 Checking Data Consistency..."
  consistency = room.check_data_consistency
  
  puts "  Consistent: #{consistency[:consistent] ? '✅' : '❌'}"
  puts "  Stored Count: #{consistency[:stored_count]}"
  puts "  Actual Sessions: #{consistency[:actual_sessions]}"
  puts "  Difference: #{consistency[:difference]}"
  puts "  All Sessions: #{consistency[:all_sessions]}"
  puts "  Session Statuses: #{consistency[:session_statuses]}"
  puts ""
  
  # Show all game sessions
  puts "🎮 All Game Sessions (#{room.game_sessions.count} total):"
  if room.game_sessions.any?
    room.game_sessions.each_with_index do |session, index|
      puts "  #{index + 1}. ID: #{session.id}"
      puts "     User: #{session.user&.username || 'Unknown'} (#{session.user_id})"
      puts "     Status: #{session.status}"
      puts "     Bet: #{session.bet_amount}"
      puts "     Created: #{session.created_at}"
      puts "     Updated: #{session.updated_at}"
      puts "     Metadata: #{session.metadata}"
      puts ""
    end
  else
    puts "  ❌ No game sessions found!"
  end
  
  # Show current players list
  puts "👥 Current Players List:"
  players = room.current_players_list
  if players.any?
    players.each_with_index do |player, index|
      puts "  #{index + 1}. #{player[:username]} (#{player[:user_id]})"
      puts "     Status: #{player[:status]}"
      puts "     Position: #{player[:position]}"
      puts "     Ready: #{player[:is_ready]}"
      puts "     Joined: #{player[:joined_at]}"
      puts ""
    end
  else
    puts "  ❌ No active players found!"
  end
  
  # Fix inconsistency if needed
  if consistency[:consistent]
    puts "✅ Room data is already consistent!"
  else
    puts "❌ Room data is inconsistent. Attempting to fix..."
    
    # Show what we're about to fix
    puts ""
    puts "🔧 Fix Plan:"
    puts "  Current stored count: #{consistency[:stored_count]}"
    puts "  Actual active sessions: #{consistency[:actual_sessions]}"
    puts "  Will update stored count to match actual sessions"
    puts ""
    
    # Ask for confirmation (in production, you might want to skip this)
    print "Continue with fix? (y/N): "
    response = STDIN.gets.chomp.downcase
    
    if response == 'y' || response == 'yes'
      fix_result = room.fix_data_consistency!
      
      if fix_result[:fixed]
        puts "✅ Successfully fixed room data consistency!"
        puts "  Old count: #{fix_result[:old_count]}"
        puts "  New count: #{fix_result[:new_count]}"
        puts "  Difference: #{fix_result[:difference]}"
      else
        puts "⚠️  #{fix_result[:message]}"
      end
    else
      puts "❌ Fix cancelled by user"
    end
  end
  
  puts ""
  puts "🎯 Recommendations:"
  
  if consistency[:stored_count] > consistency[:actual_sessions]
    puts "  • The room shows more players than actual sessions"
    puts "  • This suggests players joined but sessions weren't created properly"
    puts "  • Or sessions were created but later deleted/cancelled"
    puts "  • Check the application logs for join errors around room creation time"
  elsif consistency[:stored_count] < consistency[:actual_sessions]
    puts "  • The room shows fewer players than actual sessions"
    puts "  • This suggests sessions were created but player count wasn't incremented"
    puts "  • This is less common but indicates a race condition in the join process"
  end
  
  if consistency[:all_sessions] > consistency[:actual_sessions]
    puts "  • There are #{consistency[:all_sessions] - consistency[:actual_sessions]} non-active sessions"
    puts "  • Check session statuses: #{consistency[:session_statuses]}"
    puts "  • These might be cancelled, completed, or failed sessions"
  end
  
  puts ""
  puts "🔍 Next Steps:"
  puts "  1. Check application logs for errors during player join process"
  puts "  2. Verify the room join workflow is working correctly"
  puts "  3. Monitor for similar issues in other rooms"
  puts "  4. Consider implementing better error handling in the join process"
  
  # Final verification
  puts ""
  puts "🔄 Final Verification:"
  room.reload
  final_consistency = room.check_data_consistency
  
  if final_consistency[:consistent]
    puts "✅ Room is now consistent!"
    puts "  Current players: #{room.current_players}"
    puts "  Active sessions: #{final_consistency[:actual_sessions]}"
  else
    puts "❌ Room is still inconsistent!"
    puts "  Stored count: #{final_consistency[:stored_count]}"
    puts "  Actual sessions: #{final_consistency[:actual_sessions]}"
    puts "  Manual intervention may be required"
  end

rescue Mongoid::Errors::DocumentNotFound
  puts "❌ Room not found: #{ROOM_ID}"
  puts "Please verify the room ID is correct"
  exit 1
  
rescue StandardError => e
  puts "❌ Error occurred: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(10).map { |line| "  #{line}" }
  exit 1
end

puts ""
puts "🎉 Fix script completed!"
puts "Timestamp: #{Time.current}"
