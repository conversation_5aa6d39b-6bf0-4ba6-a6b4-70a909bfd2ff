#!/usr/bin/env ruby

# Fix the specific room that has data inconsistency
require_relative 'config/environment'

puts "=== Fixing Specific Room ==="

room_id = "684e4fc7fefba3190c5b1629"

begin
  room = Room.find(room_id)
  
  puts "Room: #{room.name}"
  puts "Before fix:"
  puts "- current_players: #{room.current_players}"
  puts "- active sessions: #{room.game_sessions.in(status: ['pending', 'active']).count}"
  puts "- players list: #{room.current_players_list.length}"
  
  # Check consistency
  consistency = room.check_data_consistency
  puts "- consistent: #{consistency[:consistent]}"
  
  if !consistency[:consistent]
    puts "\nFixing data inconsistency..."
    fix_result = room.fix_data_consistency!
    puts "Fix result: #{fix_result}"
    
    room.reload
    puts "\nAfter fix:"
    puts "- current_players: #{room.current_players}"
    puts "- active sessions: #{room.game_sessions.in(status: ['pending', 'active']).count}"
    puts "- players list: #{room.current_players_list.length}"
    
    # Force update timestamp to trigger cache invalidation
    room.touch
    puts "- updated_at: #{room.updated_at}"
  else
    puts "Room is already consistent"
  end
  
rescue Mongoid::Errors::DocumentNotFound
  puts "❌ Room not found: #{room_id}"
rescue => e
  puts "❌ Error: #{e.message}"
end

puts "\n=== Fix completed ==="
