#!/usr/bin/env ruby

# Simple test to verify basic kick functionality
require_relative 'config/environment'

puts "=== Simple Kick Test ==="

# Find existing room or create one
room = Room.where(status: 'waiting').first
if room.nil?
  admin_user = User.where(role: 'admin').first
  room = Room.create!(
    name: "Simple Test Room",
    game_type: "prizewheel",
    max_players: 2,
    bet_amount: 10.0,
    currency: "USD",
    creator: admin_user,
    status: "waiting"
  )
  puts "Created test room: #{room.id}"
else
  puts "Using existing room: #{room.id}"
end

# Check room state
puts "Room: #{room.current_players}/#{room.max_players} players"

# Check for data inconsistency
consistency_check = room.check_data_consistency
puts "Data consistent: #{consistency_check[:consistent]}"

if !consistency_check[:consistent]
  puts "Fixing inconsistency..."
  fix_result = room.fix_data_consistency!
  puts "Fix result: #{fix_result}"
  room.reload
  puts "After fix: #{room.current_players}/#{room.max_players} players"
end

# Add players if room is empty
if room.current_players == 0
  admin_user = User.where(role: 'admin').first
  test_users = User.where(role: 'player').limit(2).to_a
  
  if test_users.empty?
    test_users = [admin_user, admin_user]
  elsif test_users.count == 1
    test_users << admin_user
  end
  
  puts "Adding players..."
  test_users.each_with_index do |user, index|
    break if room.current_players >= room.max_players
    
    begin
      session = room.add_player!(user)
      puts "✓ Added #{user.username}"
    rescue => e
      puts "✗ Failed to add #{user.username}: #{e.message}"
    end
  end
  
  room.reload
  puts "Room after adding players: #{room.current_players}/#{room.max_players}"
end

# Test kick if there are players
active_sessions = room.game_sessions.in(status: ['pending', 'active'])
if active_sessions.any?
  player_to_kick = active_sessions.first.user
  admin_user = User.where(role: 'admin').first
  
  puts "\nTesting kick of #{player_to_kick.username}..."
  
  begin
    result = room.kick_player!(player_to_kick.id.to_s, admin_user.id.to_s, "Simple test kick")
    puts "✓ Kick succeeded: #{result}"
    
    room.reload
    puts "Room after kick: #{room.current_players}/#{room.max_players}"
    
  rescue => e
    puts "✗ Kick failed: #{e.message}"
    puts "Error class: #{e.class.name}"
  end
else
  puts "No players to kick"
end

puts "\n=== Test completed ==="
