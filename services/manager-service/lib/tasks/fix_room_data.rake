namespace :rooms do
  desc "Fix room data inconsistencies"
  task fix_data_consistency: :environment do
    puts "🔧 Starting room data consistency fix..."
    
    fixed_count = 0
    error_count = 0
    total_rooms = Room.count
    
    puts "📊 Checking #{total_rooms} rooms for data inconsistencies..."
    
    Room.find_each.with_index do |room, index|
      begin
        consistency_check = room.check_data_consistency
        
        unless consistency_check[:consistent]
          puts "\n❌ Room #{room.id} (#{room.name}) has inconsistency:"
          puts "   Stored count: #{consistency_check[:stored_count]}"
          puts "   Actual sessions: #{consistency_check[:actual_sessions]}"
          puts "   Difference: #{consistency_check[:difference]}"
          puts "   Session statuses: #{consistency_check[:session_statuses]}"
          
          fix_result = room.fix_data_consistency!
          
          if fix_result[:fixed]
            puts "   ✅ Fixed: #{fix_result[:old_count]} -> #{fix_result[:new_count]}"
            fixed_count += 1
          else
            puts "   ⚠️  No fix needed: #{fix_result[:message]}"
          end
        end
        
        # Progress indicator
        if (index + 1) % 100 == 0
          puts "📈 Progress: #{index + 1}/#{total_rooms} rooms checked"
        end
        
      rescue StandardError => e
        puts "❌ Error checking room #{room.id}: #{e.message}"
        error_count += 1
      end
    end
    
    puts "\n🎉 Room data consistency fix completed!"
    puts "   Total rooms checked: #{total_rooms}"
    puts "   Rooms fixed: #{fixed_count}"
    puts "   Errors: #{error_count}"
  end

  desc "Show room data consistency report"
  task consistency_report: :environment do
    puts "📊 Room Data Consistency Report"
    puts "=" * 50
    
    total_rooms = Room.count
    inconsistent_rooms = []
    consistent_rooms = 0
    
    Room.find_each do |room|
      consistency_check = room.check_data_consistency
      
      if consistency_check[:consistent]
        consistent_rooms += 1
      else
        inconsistent_rooms << {
          id: room.id.to_s,
          name: room.name,
          stored_count: consistency_check[:stored_count],
          actual_sessions: consistency_check[:actual_sessions],
          difference: consistency_check[:difference],
          session_statuses: consistency_check[:session_statuses]
        }
      end
    end
    
    puts "Total rooms: #{total_rooms}"
    puts "Consistent rooms: #{consistent_rooms}"
    puts "Inconsistent rooms: #{inconsistent_rooms.count}"
    
    if inconsistent_rooms.any?
      puts "\n❌ Inconsistent Rooms:"
      inconsistent_rooms.each do |room|
        puts "  Room: #{room[:id]} (#{room[:name]})"
        puts "    Stored: #{room[:stored_count]}, Actual: #{room[:actual_sessions]}, Diff: #{room[:difference]}"
        puts "    Statuses: #{room[:session_statuses]}"
        puts ""
      end
      
      puts "💡 Run 'rake rooms:fix_data_consistency' to fix these issues"
    else
      puts "\n✅ All rooms are consistent!"
    end
  end

  desc "Fix specific room by ID"
  task :fix_room, [:room_id] => :environment do |t, args|
    room_id = args[:room_id]
    
    unless room_id
      puts "❌ Please provide a room ID: rake rooms:fix_room[ROOM_ID]"
      exit 1
    end
    
    begin
      room = Room.find(room_id)
      
      puts "🔧 Checking room #{room.id} (#{room.name})..."
      
      consistency_check = room.check_data_consistency
      
      puts "📊 Current state:"
      puts "   Stored count: #{consistency_check[:stored_count]}"
      puts "   Actual sessions: #{consistency_check[:actual_sessions]}"
      puts "   All sessions: #{consistency_check[:all_sessions]}"
      puts "   Session statuses: #{consistency_check[:session_statuses]}"
      
      if consistency_check[:consistent]
        puts "✅ Room is already consistent!"
      else
        puts "❌ Room has inconsistency, fixing..."
        
        fix_result = room.fix_data_consistency!
        
        if fix_result[:fixed]
          puts "✅ Fixed: #{fix_result[:old_count]} -> #{fix_result[:new_count]}"
        else
          puts "⚠️  #{fix_result[:message]}"
        end
      end
      
    rescue Mongoid::Errors::DocumentNotFound
      puts "❌ Room not found: #{room_id}"
      exit 1
    rescue StandardError => e
      puts "❌ Error: #{e.message}"
      exit 1
    end
  end

  desc "Show detailed room information"
  task :show_room, [:room_id] => :environment do |t, args|
    room_id = args[:room_id]
    
    unless room_id
      puts "❌ Please provide a room ID: rake rooms:show_room[ROOM_ID]"
      exit 1
    end
    
    begin
      room = Room.find(room_id)
      
      puts "🏠 Room Details: #{room.id}"
      puts "=" * 50
      puts "Name: #{room.name}"
      puts "Status: #{room.status}"
      puts "Game Type: #{room.game_type}"
      puts "Current Players: #{room.current_players}/#{room.max_players}"
      puts "Bet Amount: #{room.bet_amount} #{room.currency}"
      puts "Created: #{room.created_at}"
      puts "Updated: #{room.updated_at}"
      
      puts "\n📊 Data Consistency:"
      consistency = room.check_data_consistency
      puts "Consistent: #{consistency[:consistent] ? '✅' : '❌'}"
      puts "Stored Count: #{consistency[:stored_count]}"
      puts "Actual Sessions: #{consistency[:actual_sessions]}"
      puts "Difference: #{consistency[:difference]}"
      
      puts "\n🎮 Game Sessions (#{room.game_sessions.count} total):"
      if room.game_sessions.any?
        room.game_sessions.each do |session|
          puts "  #{session.id}: #{session.user.username} - #{session.status} - #{session.created_at}"
        end
      else
        puts "  No game sessions found"
      end
      
      puts "\n👥 Current Players List:"
      players = room.current_players_list
      if players.any?
        players.each do |player|
          puts "  #{player[:username]} (#{player[:user_id]}) - #{player[:status]} - Position: #{player[:position]}"
        end
      else
        puts "  No active players found"
      end
      
    rescue Mongoid::Errors::DocumentNotFound
      puts "❌ Room not found: #{room_id}"
      exit 1
    rescue StandardError => e
      puts "❌ Error: #{e.message}"
      puts e.backtrace.first(5)
      exit 1
    end
  end
end
