namespace :db do
  namespace :seed do
    desc "Create admin user with custom credentials"
    task :admin, [:username, :email, :password] => :environment do |task, args|
      username = args[:username] || ENV['ADMIN_USERNAME'] || 'admin'
      email = args[:email] || ENV['ADMIN_EMAIL'] || '<EMAIL>'
      password = args[:password] || ENV['ADMIN_PASSWORD'] || 'admin123456'

      puts "🔐 Creating admin user..."
      puts "   Username: #{username}"
      puts "   Email: #{email}"

      begin
        admin_user = User.find_or_initialize_by(username: username)
        
        if admin_user.persisted?
          puts "   ⚠️  Admin user '#{username}' already exists"
          
          # Ask if user wants to update the existing admin
          print "   Do you want to update the existing admin user? (y/N): "
          response = STDIN.gets.chomp.downcase
          
          if response == 'y' || response == 'yes'
            admin_user.assign_attributes(
              email: email,
              password: password,
              password_confirmation: password,
              role: 'admin',
              status: 'active'
            )
            
            if admin_user.save
              puts "   ✅ Admin user '#{username}' updated successfully"
            else
              puts "   ❌ Failed to update admin user:"
              admin_user.errors.full_messages.each { |error| puts "      - #{error}" }
            end
          else
            puts "   ℹ️  Skipping admin user update"
          end
        else
          admin_user.assign_attributes(
            email: email,
            password: password,
            password_confirmation: password,
            role: 'admin',
            status: 'active',
            balance: 10000.0,
            metadata: {
              created_by: 'rake_task',
              created_at: Time.current,
              description: 'Admin user created via rake task'
            }
          )
          
          if admin_user.save
            puts "   ✅ Admin user '#{username}' created successfully"
            puts "   💰 Initial balance: #{admin_user.balance}"
          else
            puts "   ❌ Failed to create admin user:"
            admin_user.errors.full_messages.each { |error| puts "      - #{error}" }
          end
        end
      rescue => e
        puts "   ❌ Error: #{e.message}"
      end
    end

    desc "Create sample users for testing"
    task :sample_users => :environment do
      puts "👥 Creating sample users..."

      sample_users = [
        {
          username: 'player1',
          email: '<EMAIL>',
          password: 'password123',
          role: 'player',
          balance: 1000.0
        },
        {
          username: 'player2',
          email: '<EMAIL>',
          password: 'password123',
          role: 'player',
          balance: 500.0
        },
        {
          username: 'moderator1',
          email: '<EMAIL>',
          password: 'password123',
          role: 'moderator',
          balance: 2000.0
        }
      ]

      sample_users.each do |user_data|
        begin
          user = User.find_or_initialize_by(username: user_data[:username])
          
          unless user.persisted?
            user.assign_attributes(
              email: user_data[:email],
              password: user_data[:password],
              password_confirmation: user_data[:password],
              role: user_data[:role],
              status: 'active',
              balance: user_data[:balance],
              metadata: {
                created_by: 'seed_sample',
                created_at: Time.current,
                description: 'Sample user for testing'
              }
            )
            
            if user.save
              puts "   ✅ Created #{user_data[:role]}: #{user_data[:username]}"
            else
              puts "   ❌ Failed to create #{user_data[:username]}: #{user.errors.full_messages.join(', ')}"
            end
          else
            puts "   ⚠️  User '#{user_data[:username]}' already exists"
          end
        rescue => e
          puts "   ❌ Error creating #{user_data[:username]}: #{e.message}"
        end
      end
    end

    desc "Reset admin password"
    task :reset_admin_password, [:username, :new_password] => :environment do |task, args|
      username = args[:username] || ENV['ADMIN_USERNAME'] || 'admin'
      new_password = args[:new_password]

      if new_password.blank?
        puts "❌ New password is required"
        puts "Usage: rake db:seed:reset_admin_password[admin,new_password]"
        exit 1
      end

      puts "🔑 Resetting admin password..."

      begin
        admin_user = User.find_by(username: username, role: 'admin')
        
        if admin_user
          admin_user.password = new_password
          admin_user.password_confirmation = new_password
          
          if admin_user.save
            puts "   ✅ Password reset successfully for admin '#{username}'"
          else
            puts "   ❌ Failed to reset password:"
            admin_user.errors.full_messages.each { |error| puts "      - #{error}" }
          end
        else
          puts "   ❌ Admin user '#{username}' not found"
        end
      rescue => e
        puts "   ❌ Error: #{e.message}"
      end
    end

    desc "Show admin users"
    task :show_admins => :environment do
      puts "👑 Admin users:"
      
      admins = User.where(role: 'admin')
      
      if admins.any?
        admins.each do |admin|
          puts "   • #{admin.username} (#{admin.email}) - Status: #{admin.status}"
        end
      else
        puts "   No admin users found"
      end
    end

    desc "Seed all default data (admin + game settings + sample users)"
    task :all => :environment do
      puts "🌱 Seeding all default data..."
      
      # Run the main seeds file
      Rake::Task['db:seed'].invoke
      
      # Create sample users
      Rake::Task['db:seed:sample_users'].invoke
      
      puts "\n🎉 All seeding completed!"
    end
  end
end
