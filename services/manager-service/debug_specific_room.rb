#!/usr/bin/env ruby

# Debug the specific room that's showing empty players
require_relative 'config/environment'

puts "=== Debug Specific Room ==="

room_id = "684e4fc7fefba3190c5b1629"

begin
  room = Room.find(room_id)
  
  puts "Room found: #{room.id} (#{room.name})"
  puts "Status: #{room.status}"
  puts "Current players: #{room.current_players}/#{room.max_players}"
  puts "Created: #{room.created_at}"
  puts "Updated: #{room.updated_at}"
  
  # Check all sessions for this room
  all_sessions = room.game_sessions
  puts "\nAll sessions: #{all_sessions.count}"
  
  if all_sessions.any?
    sessions_by_status = all_sessions.group_by(&:status)
    sessions_by_status.each do |status, sessions|
      puts "  #{status}: #{sessions.count}"
      sessions.each do |session|
        user = session.user
        puts "    - #{user.username} (#{user.id})"
        puts "      Created: #{session.created_at}"
        puts "      Updated: #{session.updated_at}"
        puts "      Metadata: #{session.metadata.inspect}"
      end
    end
  else
    puts "  No sessions found"
  end
  
  # Check active sessions specifically
  active_sessions = room.game_sessions.in(status: ['pending', 'active'])
  puts "\nActive sessions: #{active_sessions.count}"
  
  # Check data consistency
  consistency = room.check_data_consistency
  puts "\nData consistency: #{consistency}"
  
  # Check current_players_list
  players_list = room.current_players_list
  puts "\nCurrent players list: #{players_list.length} items"
  players_list.each_with_index do |player, index|
    puts "  #{index + 1}. #{player[:username]} (#{player[:user_id]})"
  end
  
  # Test what the API would return
  puts "\n=== API Response Test ==="
  room_controller = RoomsController.new
  api_data = room_controller.send(:room_data, room)
  
  puts "API current_players: #{api_data[:current_players]}"
  puts "API players array: #{api_data[:players].length} items"
  api_data[:players].each_with_index do |player, index|
    puts "  #{index + 1}. #{player[:username]} (#{player[:user_id]})"
  end
  
  puts "\n=== DIAGNOSIS ==="
  if room.current_players == 0 && active_sessions.count == 0
    puts "✅ Room is correctly empty - no players currently in room"
    puts "This explains why socket-gateway receives empty players array"
    
    # Check if there were players before
    if all_sessions.any?
      puts "\n📊 Session History:"
      puts "Total sessions ever created: #{all_sessions.count}"
      cancelled_sessions = all_sessions.where(status: 'cancelled')
      puts "Cancelled sessions: #{cancelled_sessions.count}"
      
      if cancelled_sessions.any?
        puts "\nRecent cancelled sessions:"
        cancelled_sessions.desc(:updated_at).limit(3).each do |session|
          user = session.user
          puts "  - #{user.username} cancelled at #{session.updated_at}"
          if session.metadata['kicked']
            puts "    Reason: Kicked by #{session.metadata['kicked_by_username']}"
          elsif session.metadata['cancellation_reason']
            puts "    Reason: #{session.metadata['cancellation_reason']}"
          end
        end
      end
    else
      puts "No players have ever joined this room"
    end
  else
    puts "❌ Data inconsistency detected!"
    puts "Room current_players: #{room.current_players}"
    puts "Active sessions: #{active_sessions.count}"
  end
  
rescue Mongoid::Errors::DocumentNotFound
  puts "❌ Room not found: #{room_id}"
rescue => e
  puts "❌ Error: #{e.message}"
  puts "Backtrace: #{e.backtrace.first(3).join(', ')}"
end

puts "\n=== Debug completed ==="
