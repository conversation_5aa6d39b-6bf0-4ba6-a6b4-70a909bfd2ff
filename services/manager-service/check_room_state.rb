#!/usr/bin/env ruby

# Check current room state and player data
# Run this script in the Manager Service directory

require_relative 'config/environment'

puts "🔍 Checking room state..."

# Check for rooms with Game Service ID pattern
game_service_pattern = /^room_[a-f0-9]+_\d+$/
rooms_with_gs_pattern = Room.where(:external_room_id => game_service_pattern)

puts "Rooms with Game Service ID pattern: #{rooms_with_gs_pattern.count}"
rooms_with_gs_pattern.each do |room|
  puts "  Room ID: #{room.id}"
  puts "  External Room ID: #{room.external_room_id}"
  puts "  Name: #{room.name}"
  puts "  Current Players: #{room.current_players}"
  puts "  Game Sessions: #{room.game_sessions.count}"
  puts "  Active Sessions: #{room.game_sessions.in(status: ['pending', 'active']).count}"
  puts "  Metadata: #{room.metadata}"
  puts "  ---"
end

# Check for the specific room mentioned in logs
target_room_id = "68412c9af494b684c1c18ecf"
target_room = Room.find_by(external_room_id: target_room_id)

if target_room
  puts "✅ Found target room: #{target_room_id}"
  puts "  Room ID: #{target_room.id}"
  puts "  Name: #{target_room.name}"
  puts "  Current Players: #{target_room.current_players}"
  puts "  Max Players: #{target_room.max_players}"
  puts "  Status: #{target_room.status}"
  puts "  Game Service Room ID: #{target_room.game_service_room_id}"
  puts "  Metadata: #{target_room.metadata}"
  
  puts "  Game Sessions:"
  target_room.game_sessions.each do |session|
    puts "    Session ID: #{session.session_id}"
    puts "    User ID: #{session.user_id}"
    puts "    Status: #{session.status}"
    puts "    Bet Amount: #{session.bet_amount}"
    puts "    Created At: #{session.created_at}"
    puts "    ---"
  end
  
  puts "  Active Sessions:"
  active_sessions = target_room.game_sessions.in(status: ['pending', 'active'])
  active_sessions.each do |session|
    user = session.user
    puts "    User: #{user.username} (#{user.id})"
    puts "    Status: #{session.status}"
    puts "    Bet Amount: #{session.bet_amount}"
    puts "    Is Ready: #{session.metadata['is_ready']}"
    puts "    ---"
  end
else
  puts "❌ Target room not found: #{target_room_id}"
end

puts "🔍 Room state check completed."
