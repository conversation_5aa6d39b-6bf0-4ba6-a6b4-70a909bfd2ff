#!/usr/bin/env ruby

# Test the API endpoints
ENV['RAILS_ENV'] ||= 'development'

require 'bundler/setup'
require_relative 'config/environment'
require 'net/http'
require 'json'

# Force reload the models
Object.send(:remove_const, :User) if defined?(User)
Object.send(:remove_const, :Transaction) if defined?(Transaction)
load Rails.root.join('app/models/user.rb')
load Rails.root.join('app/models/transaction.rb')

puts "Testing API endpoints..."

begin
  # Get a user and admin for testing
  user = User.first
  admin = User.where(role: 'admin').first
  
  if admin.nil?
    puts "No admin user found, creating one..."
    admin = User.create!(
      username: 'test_admin',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      balance: 0.0
    )
  end
  
  puts "User: #{user.username}, Balance: #{user.balance}"
  puts "Admin: #{admin.username}, Balance: #{admin.balance}"
  
  # Test the transaction service directly
  puts "\n=== Testing TransactionService ==="
  
  transaction_service = TransactionService.new
  
  # Test deposit
  puts "Testing deposit via service..."
  deposit_tx = transaction_service.process_deposit(user, 20.0, 'Service test deposit')
  puts "Deposit transaction: #{deposit_tx.transaction_id}, Amount: #{deposit_tx.amount}"
  
  user.reload
  puts "User balance after deposit: #{user.balance}"
  
  # Test withdrawal
  puts "Testing withdrawal via service..."
  withdrawal_tx = transaction_service.process_withdrawal(user, 5.0, 'Service test withdrawal')
  puts "Withdrawal transaction: #{withdrawal_tx.transaction_id}, Amount: #{withdrawal_tx.amount}"
  
  user.reload
  puts "User balance after withdrawal: #{user.balance}"
  
  puts "\n✅ TransactionService tests passed!"

rescue => e
  puts "❌ Error: #{e.message}"
  puts e.backtrace.first(5)
end
