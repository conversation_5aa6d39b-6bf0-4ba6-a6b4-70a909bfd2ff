#!/usr/bin/env ruby

# Debug why socket-gateway is still getting old data
require_relative 'config/environment'
require 'net/http'
require 'json'
require 'uri'

puts "=== Socket Gateway Cache Debug ==="

room_id = "684e4fc7fefba3190c5b1629"

# Check current room state in database
puts "1️⃣ CURRENT DATABASE STATE"
begin
  room = Room.find(room_id)
  
  puts "Room: #{room.name}"
  puts "- Current players: #{room.current_players}"
  puts "- Updated at: #{room.updated_at}"
  
  active_sessions = room.game_sessions.in(status: ['pending', 'active'])
  puts "- Active sessions: #{active_sessions.count}"
  
  active_sessions.each do |session|
    user = session.user
    puts "  * #{user.username} (#{user.id}) - Status: #{session.status}"
    puts "    Created: #{session.created_at}"
    puts "    Updated: #{session.updated_at}"
  end
  
  # Check players list
  players_list = room.current_players_list
  puts "- Players list: #{players_list.length} items"
  players_list.each do |player|
    puts "  * #{player[:username]} (#{player[:user_id]})"
  end
  
rescue Mongoid::Errors::DocumentNotFound
  puts "❌ Room not found in database"
  exit 1
end

# Check what the API endpoint returns
puts "\n2️⃣ MANAGER-SERVICE API RESPONSE"
begin
  uri = URI("http://localhost:3001/rooms/#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  http.read_timeout = 5
  
  response = http.get(uri)
  
  if response.code == '200'
    data = JSON.parse(response.body)
    room_data = data['data']['room']
    
    puts "✅ API Response successful"
    puts "- current_players: #{room_data['current_players']}"
    puts "- players array: #{room_data['players'] ? room_data['players'].length : 'nil'} items"
    puts "- updated_at: #{room_data['updated_at']}"
    
    if room_data['players'] && room_data['players'].any?
      puts "- players data:"
      room_data['players'].each_with_index do |player, index|
        puts "  #{index + 1}. #{player['username']} (#{player['user_id']})"
      end
    else
      puts "- ❌ No players in API response!"
    end
  else
    puts "❌ API Response failed: #{response.code} - #{response.body}"
  end
  
rescue => e
  puts "❌ API Request failed: #{e.message}"
end

# Check room list endpoint (fallback that socket-gateway might use)
puts "\n3️⃣ ROOM LIST ENDPOINT (socket-gateway fallback)"
begin
  uri = URI("http://localhost:3001/rooms?id=#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  http.read_timeout = 5
  
  response = http.get(uri)
  
  if response.code == '200'
    data = JSON.parse(response.body)
    rooms = data['data']['rooms']
    
    if rooms && rooms.any?
      room_data = rooms.first
      puts "✅ Room List Response successful"
      puts "- current_players: #{room_data['current_players']}"
      puts "- players array: #{room_data['players'] ? room_data['players'].length : 'nil'} items"
      puts "- updated_at: #{room_data['updated_at']}"
      
      if room_data['players'] && room_data['players'].any?
        puts "- players data:"
        room_data['players'].each_with_index do |player, index|
          puts "  #{index + 1}. #{player['username']} (#{player['user_id']})"
        end
      else
        puts "- ❌ No players in room list response!"
      end
    else
      puts "❌ Room not found in list"
    end
  else
    puts "❌ Room List Response failed: #{response.code} - #{response.body}"
  end
  
rescue => e
  puts "❌ Room List Request failed: #{e.message}"
end

# Check data consistency and fix if needed
puts "\n4️⃣ DATA CONSISTENCY CHECK"
consistency = room.check_data_consistency
puts "Consistency: #{consistency}"

if !consistency[:consistent]
  puts "🔧 Fixing data inconsistency..."
  fix_result = room.fix_data_consistency!
  puts "Fix result: #{fix_result}"
  room.reload
  
  # Test API again after fix
  puts "\n📡 Testing API after fix..."
  begin
    uri = URI("http://localhost:3001/rooms/#{room_id}")
    http = Net::HTTP.new(uri.host, uri.port)
    response = http.get(uri)
    
    if response.code == '200'
      data = JSON.parse(response.body)
      room_data = data['data']['room']
      puts "After fix - current_players: #{room_data['current_players']}"
      puts "After fix - players array: #{room_data['players'] ? room_data['players'].length : 'nil'} items"
    end
  rescue => e
    puts "API test after fix failed: #{e.message}"
  end
end

# Force update room timestamp to trigger cache invalidation
puts "\n5️⃣ FORCING ROOM UPDATE"
room.touch
room.reload
puts "Room updated_at: #{room.updated_at}"

# Test API one more time
puts "\n6️⃣ FINAL API TEST"
begin
  uri = URI("http://localhost:3001/rooms/#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  response = http.get(uri)
  
  if response.code == '200'
    data = JSON.parse(response.body)
    room_data = data['data']['room']
    puts "Final test - current_players: #{room_data['current_players']}"
    puts "Final test - players array: #{room_data['players'] ? room_data['players'].length : 'nil'} items"
    puts "Final test - updated_at: #{room_data['updated_at']}"
    
    if room_data['players'] && room_data['players'].any?
      puts "✅ Players data now available:"
      room_data['players'].each do |player|
        puts "  * #{player['username']} (#{player['user_id']})"
      end
    else
      puts "❌ Still no players in API response"
    end
  end
rescue => e
  puts "Final API test failed: #{e.message}"
end

puts "\n=== Debug completed ==="
puts "\n💡 NEXT STEPS:"
puts "1. Try subscribing to the room again"
puts "2. Check if socket-gateway now receives updated data"
puts "3. If still empty, there might be socket-gateway caching"
