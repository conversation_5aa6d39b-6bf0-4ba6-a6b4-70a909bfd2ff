#!/usr/bin/env ruby

# Test socket-gateway integration with fixed room data
require_relative 'config/environment'

puts "=== Socket Gateway Integration Test ==="

# Clean up any existing test rooms
Room.where(name: /Socket Test/).destroy_all

# Create a test room with players
admin_user = User.where(role: 'admin').first
test_users = User.limit(2).to_a

test_room = Room.create!(
  name: "Socket Test Room",
  game_type: "prizewheel",
  max_players: 2,
  bet_amount: 15.0,
  currency: "USD",
  creator: admin_user,
  status: "waiting"
)

puts "✅ Created test room: #{test_room.id} (#{test_room.name})"

# Add players
test_users.each_with_index do |user, index|
  break if index >= test_room.max_players
  
  session = test_room.add_player!(user)
  puts "✅ Added #{user.username} - Session: #{session.id}"
end

test_room.reload
puts "Room state: #{test_room.current_players}/#{test_room.max_players}"

# Check what the room endpoint returns (what socket-gateway fetches)
puts "\n📡 TESTING ROOM ENDPOINT (what socket-gateway fetches):"

# Simulate the request that socket-gateway makes
require 'net/http'
require 'json'
require 'uri'

begin
  # Test the room details endpoint
  uri = URI("http://localhost:3001/rooms/#{test_room.id}")
  
  http = Net::HTTP.new(uri.host, uri.port)
  http.read_timeout = 5
  
  request = Net::HTTP::Get.new(uri)
  request['Content-Type'] = 'application/json'
  
  response = http.request(request)
  
  if response.code == '200'
    data = JSON.parse(response.body)
    
    puts "✅ HTTP Response successful"
    puts "Response structure:"
    puts "- success: #{data['success']}"
    puts "- data.room keys: #{data['data']['room'].keys if data['data'] && data['data']['room']}"
    
    if data['data'] && data['data']['room']
      room_data = data['data']['room']
      puts "\n📊 Room Data Analysis:"
      puts "- Room ID: #{room_data['id']}"
      puts "- Name: #{room_data['name']}"
      puts "- Status: #{room_data['status']}"
      puts "- Current Players: #{room_data['current_players']}"
      puts "- Max Players: #{room_data['max_players']}"
      puts "- Players Array: #{room_data['players'] ? room_data['players'].length : 'nil'} items"
      
      if room_data['players'] && room_data['players'].any?
        puts "\n👥 Players in response:"
        room_data['players'].each_with_index do |player, index|
          puts "  #{index + 1}. #{player['username']} (#{player['user_id']})"
          puts "     Position: #{player['position']}"
          puts "     Ready: #{player['is_ready']}"
          puts "     Bet: #{player['bet_amount']}"
        end
      else
        puts "\n❌ No players in response!"
      end
      
      # Check consistency
      expected_players = room_data['current_players'] || 0
      actual_players = room_data['players'] ? room_data['players'].length : 0
      
      if expected_players == actual_players
        puts "\n✅ Data is CONSISTENT: #{expected_players} current_players = #{actual_players} players in array"
      else
        puts "\n❌ Data is INCONSISTENT: #{expected_players} current_players ≠ #{actual_players} players in array"
      end
    end
  else
    puts "❌ HTTP Response failed: #{response.code} - #{response.body}"
  end
  
rescue => e
  puts "❌ HTTP Request failed: #{e.message}"
  puts "This is expected if manager-service is not running on port 3001"
end

# Test the room data method directly
puts "\n🔧 TESTING room_data METHOD DIRECTLY:"
room_controller = RoomsController.new
direct_room_data = room_controller.send(:room_data, test_room)

puts "Direct room_data result:"
puts "- Current Players: #{direct_room_data[:current_players]}"
puts "- Players Array: #{direct_room_data[:players] ? direct_room_data[:players].length : 'nil'} items"

if direct_room_data[:players] && direct_room_data[:players].any?
  puts "\n👥 Players in direct result:"
  direct_room_data[:players].each_with_index do |player, index|
    puts "  #{index + 1}. #{player[:username]} (#{player[:user_id]})"
    puts "     Position: #{player[:position]}"
    puts "     Ready: #{player[:is_ready]}"
    puts "     Bet: #{player[:bet_amount]}"
  end
else
  puts "\n❌ No players in direct result!"
end

# Check data consistency
puts "\n🔍 DATA CONSISTENCY CHECK:"
consistency = test_room.check_data_consistency
puts "Consistency result: #{consistency}"

if !consistency[:consistent]
  puts "\n🔧 FIXING DATA INCONSISTENCY:"
  fix_result = test_room.fix_data_consistency!
  puts "Fix result: #{fix_result}"
  
  test_room.reload
  puts "After fix - Current players: #{test_room.current_players}"
  
  # Test room_data again after fix
  fixed_room_data = room_controller.send(:room_data, test_room)
  puts "After fix - Players array: #{fixed_room_data[:players] ? fixed_room_data[:players].length : 'nil'} items"
end

# Show final state
puts "\n📊 FINAL STATE:"
test_room.reload
active_sessions = test_room.game_sessions.in(status: ['pending', 'active'])
puts "- Room current_players: #{test_room.current_players}"
puts "- Active sessions: #{active_sessions.count}"
puts "- Players list length: #{test_room.current_players_list.length}"

puts "\n🎯 SOCKET-GATEWAY INTEGRATION SUMMARY:"
if test_room.current_players == active_sessions.count && 
   test_room.current_players == test_room.current_players_list.length
  puts "✅ ALL DATA IS CONSISTENT - Socket-gateway will receive correct player data"
else
  puts "❌ DATA INCONSISTENCY DETECTED - Socket-gateway may receive empty players"
end

# Cleanup
test_room.destroy
puts "\n✅ Cleaned up test room"

puts "\n=== Test completed ==="
