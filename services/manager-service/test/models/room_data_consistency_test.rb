require 'test_helper'

class RoomDataConsistencyTest < ActiveSupport::TestCase
  def setup
    @user1 = User.create!(
      username: 'testuser1',
      email: '<EMAIL>',
      password: 'password123',
      balance: 1000.0
    )
    
    @user2 = User.create!(
      username: 'testuser2', 
      email: '<EMAIL>',
      password: 'password123',
      balance: 1000.0
    )
    
    @room = Room.create!(
      name: 'Test Room',
      game_type: 'prizewheel',
      creator: @user1,
      max_players: 4,
      bet_amount: 10.0,
      currency: 'USD'
    )
  end

  test "room starts with consistent data" do
    consistency = @room.check_data_consistency
    
    assert consistency[:consistent], "New room should be consistent"
    assert_equal 0, consistency[:stored_count]
    assert_equal 0, consistency[:actual_sessions]
    assert_equal 0, consistency[:difference]
  end

  test "adding player maintains consistency" do
    # Add player normally
    session = @room.add_player!(@user1)
    
    assert session.persisted?, "Session should be created"
    assert_equal 'pending', session.status
    
    # Check consistency
    consistency = @room.check_data_consistency
    
    assert consistency[:consistent], "Room should remain consistent after adding player"
    assert_equal 1, consistency[:stored_count]
    assert_equal 1, consistency[:actual_sessions]
    assert_equal 0, consistency[:difference]
  end

  test "adding multiple players maintains consistency" do
    # Add two players
    @room.add_player!(@user1)
    @room.add_player!(@user2)
    
    # Check consistency
    consistency = @room.check_data_consistency
    
    assert consistency[:consistent], "Room should remain consistent with multiple players"
    assert_equal 2, consistency[:stored_count]
    assert_equal 2, consistency[:actual_sessions]
    assert_equal 0, consistency[:difference]
  end

  test "removing player maintains consistency" do
    # Add and then remove player
    @room.add_player!(@user1)
    @room.remove_player!(@user1)
    
    # Check consistency
    consistency = @room.check_data_consistency
    
    assert consistency[:consistent], "Room should remain consistent after removing player"
    assert_equal 0, consistency[:stored_count]
    assert_equal 0, consistency[:actual_sessions]
    assert_equal 0, consistency[:difference]
  end

  test "fix_data_consistency fixes inconsistent room" do
    # Create inconsistency by manually manipulating data
    @room.add_player!(@user1)
    
    # Manually increment current_players without creating session (simulating the bug)
    @room.update_column(:current_players, 2)
    
    # Verify inconsistency
    consistency = @room.check_data_consistency
    refute consistency[:consistent], "Room should be inconsistent"
    assert_equal 2, consistency[:stored_count]
    assert_equal 1, consistency[:actual_sessions]
    assert_equal 1, consistency[:difference]
    
    # Fix the inconsistency
    fix_result = @room.fix_data_consistency!
    
    assert fix_result[:fixed], "Fix should be applied"
    assert_equal 2, fix_result[:old_count]
    assert_equal 1, fix_result[:new_count]
    assert_equal 1, fix_result[:difference]
    
    # Verify fix
    @room.reload
    final_consistency = @room.check_data_consistency
    assert final_consistency[:consistent], "Room should be consistent after fix"
    assert_equal 1, @room.current_players
  end

  test "fix_data_consistency handles opposite inconsistency" do
    # Create opposite inconsistency (more sessions than stored count)
    session = @room.game_sessions.create!(
      user: @user1,
      room: @room,
      game_type: @room.game_type,
      bet_amount: @room.bet_amount,
      status: 'pending'
    )
    
    # Don't increment current_players (simulating a different bug)
    
    # Verify inconsistency
    consistency = @room.check_data_consistency
    refute consistency[:consistent], "Room should be inconsistent"
    assert_equal 0, consistency[:stored_count]
    assert_equal 1, consistency[:actual_sessions]
    assert_equal(-1, consistency[:difference])
    
    # Fix the inconsistency
    fix_result = @room.fix_data_consistency!
    
    assert fix_result[:fixed], "Fix should be applied"
    assert_equal 0, fix_result[:old_count]
    assert_equal 1, fix_result[:new_count]
    assert_equal(-1, fix_result[:difference])
    
    # Verify fix
    @room.reload
    final_consistency = @room.check_data_consistency
    assert final_consistency[:consistent], "Room should be consistent after fix"
    assert_equal 1, @room.current_players
  end

  test "fix_data_consistency does nothing for consistent room" do
    # Add player normally (should be consistent)
    @room.add_player!(@user1)
    
    # Try to fix (should do nothing)
    fix_result = @room.fix_data_consistency!
    
    refute fix_result[:fixed], "Fix should not be applied to consistent room"
    assert_equal "No inconsistency detected", fix_result[:message]
  end

  test "current_players_list returns correct data" do
    # Add players
    @room.add_player!(@user1)
    @room.add_player!(@user2)
    
    players = @room.current_players_list
    
    assert_equal 2, players.count
    
    # Check first player
    player1 = players.find { |p| p[:user_id] == @user1.id.to_s }
    assert player1, "User1 should be in players list"
    assert_equal @user1.username, player1[:username]
    assert_equal 1, player1[:position]
    assert_equal 'pending', player1[:status]
    
    # Check second player
    player2 = players.find { |p| p[:user_id] == @user2.id.to_s }
    assert player2, "User2 should be in players list"
    assert_equal @user2.username, player2[:username]
    assert_equal 2, player2[:position]
    assert_equal 'pending', player2[:status]
  end

  test "session creation failure rolls back player count" do
    # Mock session creation to fail
    Room.any_instance.stubs(:game_sessions).raises(StandardError, "Session creation failed")
    
    initial_count = @room.current_players
    
    # Attempt to add player (should fail and rollback)
    assert_raises(StandardError) do
      @room.add_player!(@user1)
    end
    
    # Verify rollback
    @room.reload
    assert_equal initial_count, @room.current_players, "Player count should be rolled back"
    
    # Verify consistency is maintained
    consistency = @room.check_data_consistency
    assert consistency[:consistent], "Room should remain consistent after failed join"
  end
end
