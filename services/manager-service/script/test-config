#!/usr/bin/env ruby

# Simple configuration test script
# This script tests the environment configuration without loading the full Rails app

# Load environment variables from .env file
if File.exist?('.env')
  File.readlines('.env').each do |line|
    line = line.strip
    next if line.empty? || line.start_with?('#')

    key, value = line.split('=', 2)
    ENV[key] = value if key && value
  end
end

puts "🔧 XZ Game Manager Service - Configuration Test"
puts "=" * 60

# Test environment variables
puts "\n📋 Environment Variables:"
puts "  RAILS_ENV: #{ENV['RAILS_ENV'] || 'not set'}"
puts "  PORT: #{ENV['PORT'] || 'not set'}"
puts "  HOST: #{ENV['HOST'] || 'not set'}"

puts "\n🗄️  Database Configuration:"
puts "  MONGODB_URL: #{ENV['MONGODB_URL'] || 'not set'}"
puts "  MONGODB_TIMEOUT: #{ENV['MONGODB_TIMEOUT'] || 'not set'}"
puts "  MONGODB_MAX_POOL_SIZE: #{ENV['MONGODB_MAX_POOL_SIZE'] || 'not set'}"

puts "\n🔴 Redis Configuration:"
puts "  REDIS_URL: #{ENV['REDIS_URL'] || 'not set'}"
puts "  SIDEKIQ_REDIS_URL: #{ENV['SIDEKIQ_REDIS_URL'] || 'not set'}"
puts "  REDIS_POOL_SIZE: #{ENV['REDIS_POOL_SIZE'] || 'not set'}"

puts "\n🔐 Security Configuration:"
puts "  JWT_SECRET: #{ENV['JWT_SECRET'] ? '[SET]' : 'not set'}"
puts "  SECRET_KEY_BASE: #{ENV['SECRET_KEY_BASE'] ? '[SET]' : 'not set'}"
puts "  PASSWORD_MIN_LENGTH: #{ENV['PASSWORD_MIN_LENGTH'] || 'not set'}"

puts "\n🎮 Game Service Configuration:"
puts "  GAME_SERVICE_URL: #{ENV['GAME_SERVICE_URL'] || 'not set'}"
puts "  GAME_SERVICE_TIMEOUT: #{ENV['GAME_SERVICE_TIMEOUT'] || 'not set'}"

puts "\n👤 Admin User Configuration:"
puts "  ADMIN_USERNAME: #{ENV['ADMIN_USERNAME'] || 'not set'}"
puts "  ADMIN_EMAIL: #{ENV['ADMIN_EMAIL'] || 'not set'}"
puts "  ADMIN_PASSWORD: #{ENV['ADMIN_PASSWORD'] ? '[SET]' : 'not set'}"

puts "\n🚦 Feature Flags:"
puts "  PRIZEWHEEL_ENABLED: #{ENV['PRIZEWHEEL_ENABLED'] || 'not set'}"
puts "  AMIDAKUJI_ENABLED: #{ENV['AMIDAKUJI_ENABLED'] || 'not set'}"
puts "  ADMIN_PANEL_ENABLED: #{ENV['ADMIN_PANEL_ENABLED'] || 'not set'}"

puts "\n📊 Logging Configuration:"
puts "  LOG_LEVEL: #{ENV['LOG_LEVEL'] || 'not set'}"
puts "  DEBUG_MODE: #{ENV['DEBUG_MODE'] || 'not set'}"

# Test connection strings
puts "\n🔍 Connection String Analysis:"

# MongoDB URL analysis
if ENV['MONGODB_URL']
  mongodb_url = ENV['MONGODB_URL']
  if mongodb_url.include?('admin:password123')
    puts "  ✅ MongoDB: Configured with authentication"
  elsif mongodb_url.include?('localhost:27017')
    puts "  ✅ MongoDB: Configured for localhost"
  else
    puts "  ⚠️  MongoDB: Custom configuration detected"
  end

  if mongodb_url.include?('authSource=admin')
    puts "  ✅ MongoDB: Auth source configured"
  else
    puts "  ⚠️  MongoDB: Auth source not specified"
  end
else
  puts "  ❌ MongoDB: Not configured"
end

# Redis URL analysis
if ENV['REDIS_URL']
  redis_url = ENV['REDIS_URL']
  if redis_url.include?(':password123@')
    puts "  ✅ Redis: Configured with authentication"
  elsif redis_url.include?('localhost:6379')
    puts "  ✅ Redis: Configured for localhost"
  else
    puts "  ⚠️  Redis: Custom configuration detected"
  end
else
  puts "  ❌ Redis: Not configured"
end

# Security analysis
puts "\n🔒 Security Analysis:"
if ENV['JWT_SECRET'] && ENV['JWT_SECRET'].length >= 32
  puts "  ✅ JWT Secret: Properly configured (#{ENV['JWT_SECRET'].length} characters)"
elsif ENV['JWT_SECRET']
  puts "  ⚠️  JWT Secret: Too short (#{ENV['JWT_SECRET'].length} characters, recommend 32+)"
else
  puts "  ❌ JWT Secret: Not configured"
end

if ENV['SECRET_KEY_BASE'] && ENV['SECRET_KEY_BASE'].length >= 64
  puts "  ✅ Secret Key Base: Properly configured (#{ENV['SECRET_KEY_BASE'].length} characters)"
elsif ENV['SECRET_KEY_BASE']
  puts "  ⚠️  Secret Key Base: Too short (#{ENV['SECRET_KEY_BASE'].length} characters, recommend 64+)"
else
  puts "  ❌ Secret Key Base: Not configured"
end

# Admin user analysis
puts "\n👑 Admin User Analysis:"
if ENV['ADMIN_USERNAME'] && ENV['ADMIN_EMAIL'] && ENV['ADMIN_PASSWORD']
  puts "  ✅ Admin credentials: All configured"

  if ENV['ADMIN_PASSWORD'].length >= 8
    puts "  ✅ Admin password: Meets minimum length requirement"
  else
    puts "  ⚠️  Admin password: Too short (minimum 8 characters recommended)"
  end

  if ENV['ADMIN_EMAIL'].include?('@')
    puts "  ✅ Admin email: Valid format"
  else
    puts "  ⚠️  Admin email: Invalid format"
  end
else
  puts "  ⚠️  Admin credentials: Incomplete configuration"
end

# Configuration recommendations
puts "\n💡 Recommendations:"

missing_configs = []
missing_configs << "MONGODB_URL" unless ENV['MONGODB_URL']
missing_configs << "REDIS_URL" unless ENV['REDIS_URL']
missing_configs << "JWT_SECRET" unless ENV['JWT_SECRET']
missing_configs << "SECRET_KEY_BASE" unless ENV['SECRET_KEY_BASE']

if missing_configs.any?
  puts "  ❌ Missing required configurations: #{missing_configs.join(', ')}"
else
  puts "  ✅ All required configurations are present"
end

if ENV['JWT_SECRET'] == 'dev_jwt_secret_key_change_in_production'
  puts "  ⚠️  Using default JWT secret - change in production!"
end

if ENV['ADMIN_PASSWORD'] == 'admin123456'
  puts "  ⚠️  Using default admin password - change immediately!"
end

puts "\n🎯 Next Steps:"
puts "  1. Start MongoDB and Redis: docker-compose -f ../../docker/docker-compose.dev.yml up -d mongodb redis"
puts "  2. Install dependencies: bundle install"
puts "  3. Create database: rails db:create"
puts "  4. Run migrations: rails db:migrate"
puts "  5. Seed admin user: rails db:seed"
puts "  6. Start server: rails server -p 3002"

puts "\n✅ Configuration test completed!"
