#!/usr/bin/env bash

# Environment switcher script for Manager Service
# Usage: ./bin/env-switch [local|docker]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

ENV_MODE="${1:-local}"

case "$ENV_MODE" in
  "local")
    echo "🔄 Switching to LOCAL environment configuration..."
    echo "   - MongoDB: localhost:27017"
    echo "   - Redis: localhost:6379"
    echo "   - Game Service: localhost:8080"

    # The current .env file is already configured for local development
    echo "✅ Local environment is ready!"
    echo ""
    echo "To start local services:"
    echo "  docker-compose -f ../../docker/docker-compose.dev.yml up -d mongodb redis"
    echo ""
    echo "To run the manager service:"
    echo "  rails server -p 3002"
    ;;

  "docker")
    echo "🔄 Switching to DOCKER environment configuration..."
    echo "   - MongoDB: mongodb container"
    echo "   - Redis: redis container"
    echo "   - Game Service: game-service container"

    if [ -f "$PROJECT_DIR/.env.docker" ]; then
      cp "$PROJECT_DIR/.env.docker" "$PROJECT_DIR/.env"
      echo "✅ Docker environment configuration applied!"
      echo ""
      echo "To start all services:"
      echo "  docker-compose -f ../../docker/docker-compose.dev.yml up -d"
      echo ""
      echo "To run manager service in Docker:"
      echo "  # Uncomment manager-service section in docker-compose.dev.yml"
      echo "  docker-compose -f ../../docker/docker-compose.dev.yml up -d manager-service"
    else
      echo "❌ .env.docker file not found!"
      exit 1
    fi
    ;;

  *)
    echo "❌ Invalid environment mode: $ENV_MODE"
    echo ""
    echo "Usage: $0 [local|docker]"
    echo ""
    echo "Modes:"
    echo "  local  - Configure for local development (services on localhost)"
    echo "  docker - Configure for Docker development (services in containers)"
    exit 1
    ;;
esac

echo ""
echo "Current environment variables:"
echo "  MONGODB_URL: $(grep MONGODB_URL "$PROJECT_DIR/.env" | cut -d'=' -f2-)"
echo "  REDIS_URL: $(grep REDIS_URL "$PROJECT_DIR/.env" | head -1 | cut -d'=' -f2-)"
echo "  GAME_SERVICE_URL: $(grep GAME_SERVICE_URL "$PROJECT_DIR/.env" | cut -d'=' -f2-)"
