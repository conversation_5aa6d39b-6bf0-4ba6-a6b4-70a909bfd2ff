# Room Data Consistency Fix

## Problem Description

The issue was identified where rooms showed inconsistent data:
- `current_players: 2` (indicating 2 players joined)
- `game_sessions: []` (empty array, indicating no active player sessions)

This inconsistency occurred because the room's `current_players` counter was incremented when players joined, but the corresponding game sessions were either:
1. Not created properly
2. Created but later deleted/cancelled
3. Created with incorrect status

## Root Cause Analysis

The original `add_player!` method had a race condition where:
1. Room `current_players` was incremented first
2. Game session was created second
3. If session creation failed, the rollback didn't always work correctly

## Solution Implemented

### 1. Fixed Room Model (`app/models/room.rb`)

**Improved `add_player!` method:**
- Create game session FIRST, before incrementing counters
- Better error handling and rollback logic
- More detailed logging for debugging

**Added data consistency methods:**
- `check_data_consistency` - Detects inconsistencies
- `fix_data_consistency!` - Automatically fixes inconsistencies

### 2. Added Administrative Tools

**Rake Tasks (`lib/tasks/fix_room_data.rake`):**
```bash
# Check all rooms for inconsistencies
rake rooms:consistency_report

# Fix all inconsistent rooms
rake rooms:fix_data_consistency

# Fix specific room
rake rooms:fix_room[ROOM_ID]

# Show detailed room information
rake rooms:show_room[ROOM_ID]
```

**API Endpoints:**
```
GET  /admin/rooms/:id/consistency_check
POST /admin/rooms/:id/fix_data_consistency
```

### 3. Fix Script for Specific Room

**Direct fix script (`scripts/fix_room_684d9baba533d3e5fea7dc35.rb`):**
```bash
cd services/manager-service
ruby scripts/fix_room_684d9baba533d3e5fea7dc35.rb
```

## How to Use

### For the Specific Room Issue

1. **Check the room status:**
   ```bash
   rake rooms:show_room[684d9baba533d3e5fea7dc35]
   ```

2. **Fix the specific room:**
   ```bash
   rake rooms:fix_room[684d9baba533d3e5fea7dc35]
   ```

3. **Or use the dedicated script:**
   ```bash
   ruby scripts/fix_room_684d9baba533d3e5fea7dc35.rb
   ```

### For System-Wide Check

1. **Generate consistency report:**
   ```bash
   rake rooms:consistency_report
   ```

2. **Fix all inconsistent rooms:**
   ```bash
   rake rooms:fix_data_consistency
   ```

### Via API (for admin users)

1. **Check room consistency:**
   ```bash
   curl -X GET "http://localhost:3002/admin/rooms/684d9baba533d3e5fea7dc35/consistency_check" \
        -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

2. **Fix room consistency:**
   ```bash
   curl -X POST "http://localhost:3002/admin/rooms/684d9baba533d3e5fea7dc35/fix_data_consistency" \
        -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

## What the Fix Does

1. **Detects Inconsistency:**
   - Compares `room.current_players` with actual active game sessions
   - Identifies the difference and session statuses

2. **Applies Fix:**
   - Updates `current_players` to match actual active sessions count
   - Logs the change for audit purposes
   - Maintains data integrity

3. **Provides Reporting:**
   - Shows before/after state
   - Explains what was fixed
   - Provides recommendations for preventing future issues

## Prevention Measures

The improved `add_player!` method now:
- Creates sessions before incrementing counters
- Has better error handling
- Includes comprehensive rollback logic
- Provides detailed logging

## Testing

Run the test suite to verify the fix:
```bash
cd services/manager-service
rails test test/models/room_data_consistency_test.rb
```

## Expected Outcome

After running the fix:
- `current_players` will match the actual number of active game sessions
- `game_sessions` array will show the correct player sessions
- Room data will be consistent and accurate
- Future player joins will maintain consistency

## Monitoring

To prevent future issues:
1. Monitor application logs for session creation errors
2. Run periodic consistency checks
3. Set up alerts for data inconsistencies
4. Review the player join workflow for edge cases

## Example Fix Output

```
🔧 Fixing Room Data Inconsistency
Room ID: 684d9baba533d3e5fea7dc35

📊 Current Room State:
  Stored Count: 2
  Actual Sessions: 0
  Difference: 2

✅ Successfully fixed room data consistency!
  Old count: 2
  New count: 0
  
🎯 Room is now consistent!
```
