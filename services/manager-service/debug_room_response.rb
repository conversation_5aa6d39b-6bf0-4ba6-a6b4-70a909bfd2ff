#!/usr/bin/env ruby

# Debug what the room endpoint is actually returning
require_relative 'config/environment'

puts "=== Debug Room Response ==="

# Find a room with players
room = Room.where(:current_players.gt => 0).first

if room.nil?
  # Create a test room with players
  admin_user = User.where(role: 'admin').first
  test_users = User.limit(2).to_a
  
  room = Room.create!(
    name: "Debug Response Room",
    game_type: "prizewheel",
    max_players: 2,
    bet_amount: 10.0,
    currency: "USD",
    creator: admin_user,
    status: "waiting"
  )
  
  # Add players
  test_users.each do |user|
    session = room.add_player!(user)
    puts "Added #{user.username}"
  end
  
  room.reload
  puts "Created test room with #{room.current_players} players"
end

puts "\nRoom: #{room.id} (#{room.name})"
puts "Current players: #{room.current_players}/#{room.max_players}"

# Check active sessions
active_sessions = room.game_sessions.in(status: ['pending', 'active'])
puts "Active sessions: #{active_sessions.count}"

active_sessions.each do |session|
  user = session.user
  puts "  - #{user.username} (#{user.id}) - Status: #{session.status}"
end

# Test what the room_data method returns (used by rooms#show)
puts "\n=== room_data method output ==="
room_controller = RoomsController.new
room_data_result = room_controller.send(:room_data, room)
puts "Room data keys: #{room_data_result.keys}"
puts "Has players field: #{room_data_result.key?(:players)}"
puts "Has current_players_details: #{room_data_result.key?(:current_players_details)}"

if room_data_result.key?(:players)
  puts "Players field: #{room_data_result[:players]}"
end

if room_data_result.key?(:current_players_details)
  puts "Current players details: #{room_data_result[:current_players_details]}"
end

# Test what the serializer returns
puts "\n=== RoomSerializer output ==="
serialized_room = RoomSerializer.serialize_for_detail(room, admin_user)
puts "Serialized keys: #{serialized_room.keys}"
puts "Has players field: #{serialized_room.key?(:players)}"
puts "Has current_players_details: #{serialized_room.key?(:current_players_details)}"

if serialized_room.key?(:players)
  puts "Players field: #{serialized_room[:players]}"
end

if serialized_room.key?(:current_players_details)
  puts "Current players details: #{serialized_room[:current_players_details]}"
end

# Test the current_players_list method
puts "\n=== current_players_list method ==="
players_list = room.current_players_list
puts "Players list: #{players_list}"

# Test what the API endpoint would return
puts "\n=== Simulating API response ==="
api_response = {
  success: true,
  data: {
    room: room_data_result
  }
}

puts "API response structure:"
puts "- success: #{api_response[:success]}"
puts "- data.room keys: #{api_response[:data][:room].keys}"
puts "- data.room.players: #{api_response[:data][:room][:players] || 'NOT PRESENT'}"
puts "- data.room.current_players_details: #{api_response[:data][:room][:current_players_details] || 'NOT PRESENT'}"

puts "\n=== Debug completed ==="
