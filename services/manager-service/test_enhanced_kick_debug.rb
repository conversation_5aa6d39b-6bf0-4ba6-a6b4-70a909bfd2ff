#!/usr/bin/env ruby

# Debug test for EnhancedSeatManagementService
require_relative 'config/environment'

puts "=== Enhanced Kick Debug Test ==="

# Create a simple test room with players
admin_user = User.where(role: 'admin').first
test_users = User.where(role: 'player').limit(2).to_a

if test_users.empty?
  test_users = [admin_user, admin_user]
elsif test_users.count == 1
  test_users << admin_user
end

room = Room.create!(
  name: "Enhanced Debug Room",
  game_type: "prizewheel",
  max_players: 2,
  bet_amount: 10.0,
  currency: "USD",
  creator: admin_user,
  status: "waiting"
)

puts "Created room: #{room.id}"

# Add players
test_users.each do |user|
  session = room.add_player!(user)
  puts "Added #{user.username}"
end

room.reload
puts "Room state: #{room.current_players}/#{room.max_players}"

# Test each step of the enhanced kick process
player_to_kick = test_users.first
puts "\nTesting enhanced kick of #{player_to_kick.username}..."

begin
  service = EnhancedSeatManagementService.new
  
  # Step 1: Validate kick eligibility
  puts "Step 1: Validating kick eligibility..."
  session = room.game_sessions.where(user: player_to_kick).in(status: ['pending', 'active']).first
  puts "Found session: #{session.id} - Status: #{session.status}"
  
  # Step 2: Test the problematic method directly
  puts "Step 2: Testing execute_manager_service_removal..."
  begin
    service.send(:execute_manager_service_removal, room, player_to_kick, session, "Debug test")
    puts "✓ execute_manager_service_removal succeeded"
  rescue => e
    puts "✗ execute_manager_service_removal failed: #{e.message}"
    puts "Error class: #{e.class.name}"
    puts "Backtrace:"
    puts e.backtrace.first(3).map { |line| "  #{line}" }
  end
  
  # Check session state
  session.reload
  puts "Session after removal attempt: #{session.status}"
  
rescue => e
  puts "✗ Enhanced kick setup failed: #{e.message}"
  puts "Error class: #{e.class.name}"
end

# Cleanup
begin
  room.destroy
  puts "\n✓ Cleaned up test room"
rescue => e
  puts "\n✗ Failed to cleanup: #{e.message}"
end

puts "\n=== Debug test completed ==="
