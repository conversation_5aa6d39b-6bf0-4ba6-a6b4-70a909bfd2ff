#!/usr/bin/env ruby

require 'bundler/setup'
require_relative 'config/environment'

puts "Simple transaction test..."

begin
  user = User.first
  puts "User: #{user.username}, Balance: #{user.balance}"

  # Test direct transaction creation
  puts "\nCreating transaction directly..."
  
  balance_before = user.balance
  amount = -5.0
  new_balance = balance_before + amount
  
  transaction = user.transactions.create!(
    amount: amount,
    transaction_type: 'withdrawal',
    balance_before: balance_before,
    balance_after: new_balance,
    description: 'Direct test withdrawal',
    status: 'completed'
  )
  
  puts "Transaction created: #{transaction.transaction_id}"
  
  # Update user balance separately
  user.balance = new_balance
  user.save!
  
  puts "User balance updated to: #{user.balance}"
  puts "✅ Test successful!"

rescue => e
  puts "❌ Error: #{e.message}"
  puts e.backtrace.first(3)
end
