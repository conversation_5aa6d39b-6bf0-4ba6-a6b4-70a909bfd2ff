#!/usr/bin/env ruby

# Test script to reproduce kick player issue in full room
require_relative 'config/environment'

puts "=== Testing Kick Player in Full Room ==="

# Find a room that is full
room = Room.where(status: 'waiting').first
if room.nil?
  puts "No waiting rooms found. Creating a test room..."
  
  # Create a test room
  admin_user = User.where(role: 'admin').first
  if admin_user.nil?
    puts "No admin user found. Please ensure admin user exists."
    exit 1
  end
  
  room = Room.create!(
    name: "Test Full Room",
    game_type: "prizewheel",
    max_players: 2,
    bet_amount: 10.0,
    currency: "USD",
    creator: admin_user,
    status: "waiting"
  )
  puts "Created test room: #{room.id}"
end

puts "Room details:"
puts "- ID: #{room.id}"
puts "- Name: #{room.name}"
puts "- Status: #{room.status}"
puts "- Current players: #{room.current_players}/#{room.max_players}"
puts "- Is full: #{room.current_players >= room.max_players}"

# Get active sessions in the room
active_sessions = room.game_sessions.in(status: ['pending', 'active'])
puts "\nActive sessions: #{active_sessions.count}"

active_sessions.each_with_index do |session, index|
  user = session.user
  puts "  #{index + 1}. User: #{user.username} (#{user.id}) - Status: #{session.status}"
end

if active_sessions.empty?
  puts "No active players to kick. Test cannot proceed."
  exit 0
end

# Try to kick the first player
player_to_kick = active_sessions.first.user
puts "\n=== Attempting to kick player: #{player_to_kick.username} ==="

begin
  # Test the kick_player! method directly
  puts "Testing Room#kick_player! method..."
  admin_user = User.where(role: 'admin').first
  result = room.kick_player!(player_to_kick.id.to_s, admin_user.id.to_s, "Test kick from full room")
  puts "✓ Room#kick_player! succeeded: #{result}"
  
  # Reload room to check updated state
  room.reload
  puts "Room state after kick:"
  puts "- Current players: #{room.current_players}/#{room.max_players}"
  puts "- Is full: #{room.current_players >= room.max_players}"
  
rescue => e
  puts "✗ Room#kick_player! failed: #{e.message}"
  puts "Error class: #{e.class.name}"
  puts "Backtrace:"
  puts e.backtrace.first(5).map { |line| "  #{line}" }
end

# Test the RoomManagementService kick method
puts "\n=== Testing RoomManagementService kick method ==="

begin
  # Find another player to kick if available
  remaining_sessions = room.game_sessions.in(status: ['pending', 'active'])
  if remaining_sessions.any?
    player_to_kick = remaining_sessions.first.user
    puts "Testing RoomManagementService#kick_player_from_room..."
    
    service = RoomManagementService.instance
    result = service.kick_player_from_room(room, player_to_kick, reason: "Test kick via service", kicked_by_user: admin_user)
    puts "✓ RoomManagementService kick succeeded"
    puts "Result: #{result.inspect}"
    
    # Reload room to check updated state
    room.reload
    puts "Room state after service kick:"
    puts "- Current players: #{room.current_players}/#{room.max_players}"
    puts "- Is full: #{room.current_players >= room.max_players}"
  else
    puts "No remaining players to test service kick"
  end
  
rescue => e
  puts "✗ RoomManagementService kick failed: #{e.message}"
  puts "Error class: #{e.class.name}"
  puts "Backtrace:"
  puts e.backtrace.first(5).map { |line| "  #{line}" }
end

# Test the EnhancedSeatManagementService kick method
puts "\n=== Testing EnhancedSeatManagementService kick method ==="

begin
  # Find another player to kick if available
  remaining_sessions = room.game_sessions.in(status: ['pending', 'active'])
  if remaining_sessions.any?
    player_to_kick = remaining_sessions.first.user
    puts "Testing EnhancedSeatManagementService#kick_player_with_seat_management..."
    
    service = EnhancedSeatManagementService.new
    result = service.kick_player_with_seat_management(room, player_to_kick, admin_user, "Test enhanced kick")
    puts "✓ EnhancedSeatManagementService kick succeeded"
    puts "Result success: #{result[:success]}"
    
    # Reload room to check updated state
    room.reload
    puts "Room state after enhanced kick:"
    puts "- Current players: #{room.current_players}/#{room.max_players}"
    puts "- Is full: #{room.current_players >= room.max_players}"
  else
    puts "No remaining players to test enhanced kick"
  end
  
rescue => e
  puts "✗ EnhancedSeatManagementService kick failed: #{e.message}"
  puts "Error class: #{e.class.name}"
  puts "Backtrace:"
  puts e.backtrace.first(5).map { |line| "  #{line}" }
end

puts "\n=== Test completed ==="
