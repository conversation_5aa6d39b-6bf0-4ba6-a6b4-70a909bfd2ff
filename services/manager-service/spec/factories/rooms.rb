FactoryBot.define do
  factory :room do
    sequence(:name) { |n| "Test Room #{n}" }
    game_type { 'prizewheel' }
    bet_amount { 100.0 }
    max_players { 8 }
    status { 'waiting' }
    current_players { 0 }
    prize_pool { 0.0 }
    currency { 'USD' }
    is_private { false }
    sequence(:external_room_id) { |n| "external_room_#{n}" }
    association :creator, factory: :user
    configuration { {} }
    game_specific_config { {} }
    sync_required { false }
    last_synced_at { nil }
    metadata { {} }

    trait :amidakuji do
      game_type { 'amidakuji' }
      game_specific_config { { 'ladder_height' => 10, 'positions' => 8 } }
    end

    trait :private_room do
      is_private { true }
      password { 'secret123' }
    end

    trait :full_room do
      current_players { 8 }
      status { 'full' }
      prize_pool { 800.0 }
    end

    trait :active_room do
      status { 'active' }
      current_players { 4 }
      prize_pool { 400.0 }
    end

    trait :playing do
      status { 'playing' }
      current_players { 6 }
      prize_pool { 600.0 }
    end

    trait :finished do
      status { 'finished' }
      current_players { 0 }
      prize_pool { 0.0 }
    end

    trait :cancelled do
      status { 'cancelled' }
      current_players { 0 }
      prize_pool { 0.0 }
    end

    trait :with_prize_pool do
      prize_pool { 500.0 }
    end

    trait :needs_sync do
      sync_required { true }
      last_synced_at { 1.hour.ago }
    end

    trait :recently_synced do
      sync_required { false }
      last_synced_at { 5.minutes.ago }
    end

    trait :high_stakes do
      bet_amount { 1000.0 }
      max_players { 4 }
    end

    trait :low_stakes do
      bet_amount { 10.0 }
      max_players { 16 }
    end

    trait :with_configuration do
      configuration { { 'auto_start' => true, 'countdown_duration' => 30 } }
    end

    trait :prizewheel_config do
      game_type { 'prizewheel' }
      game_specific_config { { 'wheel_segments' => 8, 'multipliers' => [1, 2, 3, 5, 8, 10, 15, 20] } }
    end

    trait :with_metadata do
      metadata { { 'created_via' => 'api', 'region' => 'us-east-1' } }
    end
  end
end
