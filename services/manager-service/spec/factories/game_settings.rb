FactoryBot.define do
  factory :game_setting do
    game_type { 'prizewheel' }
    setting_key { 'max_players' }
    setting_value { 8 }
    is_active { true }
    description { 'Maximum number of players per game' }
    metadata { {} }
    
    trait :amidakuji do
      game_type { 'amidakuji' }
      setting_key { 'ladder_height' }
      setting_value { 10 }
      description { 'Height of the amidakuji ladder' }
    end
    
    trait :global do
      game_type { 'global' }
      setting_key { 'maintenance_mode' }
      setting_value { false }
      description { 'Global maintenance mode flag' }
    end
    
    trait :inactive do
      is_active { false }
    end
    
    trait :prizewheel_max_players do
      game_type { 'prizewheel' }
      setting_key { 'max_players' }
      setting_value { 8 }
      description { 'Maximum players for prize wheel games' }
    end
    
    trait :prizewheel_min_bet do
      game_type { 'prizewheel' }
      setting_key { 'min_bet_amount' }
      setting_value { 10.0 }
      description { 'Minimum bet amount for prize wheel' }
    end
    
    trait :prizewheel_max_bet do
      game_type { 'prizewheel' }
      setting_key { 'max_bet_amount' }
      setting_value { 1000.0 }
      description { 'Maximum bet amount for prize wheel' }
    end
    
    trait :prizewheel_multipliers do
      game_type { 'prizewheel' }
      setting_key { 'multipliers' }
      setting_value { [1, 2, 3, 5, 8, 10, 15, 20] }
      description { 'Prize wheel multiplier values' }
    end
    
    trait :amidakuji_max_players do
      game_type { 'amidakuji' }
      setting_key { 'max_players' }
      setting_value { 16 }
      description { 'Maximum players for amidakuji games' }
    end
    
    trait :amidakuji_ladder_height do
      game_type { 'amidakuji' }
      setting_key { 'ladder_height' }
      setting_value { 10 }
      description { 'Default ladder height for amidakuji' }
    end
    
    trait :global_maintenance do
      game_type { 'global' }
      setting_key { 'maintenance_mode' }
      setting_value { false }
      description { 'System maintenance mode' }
    end
    
    trait :global_max_concurrent do
      game_type { 'global' }
      setting_key { 'max_concurrent_games' }
      setting_value { 1000 }
      description { 'Maximum concurrent games allowed' }
    end
    
    trait :global_game_types do
      game_type { 'global' }
      setting_key { 'game_types_enabled' }
      setting_value { { 'prizewheel' => true, 'amidakuji' => true } }
      description { 'Enabled game types configuration' }
    end
    
    trait :with_metadata do
      metadata { { 'last_updated_by' => 'admin', 'update_reason' => 'Configuration change' } }
    end
    
    trait :needs_sync do
      metadata { { 'sync_required' => true, 'last_sync_attempt' => 1.hour.ago } }
    end
    
    trait :recently_synced do
      metadata { { 'sync_required' => false, 'last_synced_at' => 5.minutes.ago } }
    end
  end
end
