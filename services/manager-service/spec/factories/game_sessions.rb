FactoryBot.define do
  factory :game_session do
    association :user
    sequence(:session_id) { |n| "session_#{SecureRandom.hex(8)}_#{n}" }
    game_type { 'prizewheel' }
    bet_amount { 100.0 }
    win_amount { 0.0 }
    status { 'pending' }
    started_at { nil }
    ended_at { nil }
    result { {} }
    metadata { { 'is_ready' => false } }

    trait :amidakuji do
      game_type { 'amidakuji' }
    end

    trait :ready do
      metadata { { 'is_ready' => true } }
    end

    trait :active do
      status { 'active' }
      started_at { Time.current }
    end

    trait :completed do
      status { 'completed' }
      started_at { 1.hour.ago }
      ended_at { Time.current }
      win_amount { 200.0 }
      result { { 'winner_position' => 1, 'total_participants' => 8, 'prize_pool' => 800.0 } }
    end

    trait :completed_loss do
      status { 'completed' }
      started_at { 1.hour.ago }
      ended_at { Time.current }
      win_amount { 0.0 }
      result { { 'winner_position' => 3, 'total_participants' => 8, 'prize_pool' => 800.0 } }
    end

    trait :cancelled do
      status { 'cancelled' }
      ended_at { Time.current }
      result { { 'cancellation_reason' => 'Insufficient players' } }
    end

    trait :high_stakes do
      bet_amount { 1000.0 }
    end

    trait :low_stakes do
      bet_amount { 10.0 }
    end

    trait :big_win do
      status { 'completed' }
      started_at { 1.hour.ago }
      ended_at { Time.current }
      win_amount { 1500.0 }
      result { { 'winner_position' => 1, 'total_participants' => 8, 'prize_pool' => 1600.0, 'multiplier' => 20 } }
    end

    trait :with_metadata do
      metadata { { 'room_id' => SecureRandom.hex(12), 'player_count' => 8, 'game_duration' => 45, 'is_ready' => false } }
    end

    trait :recent do
      started_at { 10.minutes.ago }
      ended_at { 5.minutes.ago }
    end

    trait :old do
      started_at { 1.week.ago }
      ended_at { 1.week.ago + 30.minutes }
    end
  end
end
