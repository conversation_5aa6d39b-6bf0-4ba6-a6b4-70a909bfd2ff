FactoryBot.define do
  factory :user do
    sequence(:username) { |n| "user#{n}" }
    sequence(:email) { |n| "user#{n}@example.com" }
    password { 'password123' }
    balance { 1000.0 }
    status { 'active' }
    role { 'player' }
    last_login_at { 1.day.ago }
    metadata { {} }

    trait :admin do
      role { 'admin' }
    end

    trait :moderator do
      role { 'moderator' }
    end

    trait :inactive do
      status { 'inactive' }
    end

    trait :suspended do
      status { 'suspended' }
    end

    trait :banned do
      status { 'banned' }
    end

    trait :with_low_balance do
      balance { 50.0 }
    end

    trait :with_high_balance do
      balance { 10000.0 }
    end

    trait :with_zero_balance do
      balance { 0.0 }
    end

    trait :recent_login do
      last_login_at { 1.hour.ago }
    end

    trait :old_login do
      last_login_at { 2.months.ago }
    end

    trait :with_metadata do
      metadata { { 'preferences' => { 'theme' => 'dark' }, 'stats' => { 'games_played' => 10 } } }
    end
  end
end
