FactoryBot.define do
  factory :transaction do
    association :user
    transaction_id { "txn_#{SecureRandom.hex(8)}" }
    transaction_type { 'deposit' }
    amount { 100.0 }
    balance_before { 1000.0 }
    balance_after { 1100.0 }
    status { 'completed' }
    description { 'Test transaction' }
    processed_at { Time.current }
    metadata { {} }
    
    trait :deposit do
      transaction_type { 'deposit' }
      amount { 100.0 }
    end
    
    trait :admin_deposit do
      transaction_type { 'admin_deposit' }
      amount { 500.0 }
      description { 'Admin deposit for new player' }
    end
    
    trait :withdrawal do
      transaction_type { 'withdrawal' }
      amount { -50.0 }
      balance_before { 1000.0 }
      balance_after { 950.0 }
    end
    
    trait :bet_placed do
      transaction_type { 'bet_placed' }
      amount { -25.0 }
      balance_before { 1000.0 }
      balance_after { 975.0 }
      reference_type { 'GameSession' }
    end
    
    trait :bet_won do
      transaction_type { 'bet_won' }
      amount { 75.0 }
      balance_before { 975.0 }
      balance_after { 1050.0 }
      reference_type { 'GameSession' }
    end
    
    trait :bet_lost do
      transaction_type { 'bet_lost' }
      amount { 0.0 }
      reference_type { 'GameSession' }
    end
    
    trait :bet_reserved do
      transaction_type { 'bet_reserved' }
      amount { -25.0 }
      balance_before { 1000.0 }
      balance_after { 975.0 }
    end
    
    trait :adjustment do
      transaction_type { 'adjustment' }
      amount { 10.0 }
      description { 'Balance adjustment' }
    end
    
    trait :refund do
      transaction_type { 'refund' }
      amount { 25.0 }
      description { 'Game cancelled refund' }
    end
    
    trait :bonus do
      transaction_type { 'bonus' }
      amount { 50.0 }
      description { 'Welcome bonus' }
    end
    
    trait :reconciliation do
      transaction_type { 'reconciliation' }
      amount { 5.0 }
      description { 'Balance reconciliation' }
    end
    
    trait :pending do
      status { 'pending' }
      processed_at { nil }
    end
    
    trait :failed do
      status { 'failed' }
      metadata { { 'failure_reason' => 'Insufficient funds' } }
    end
    
    trait :cancelled do
      status { 'cancelled' }
      metadata { { 'cancellation_reason' => 'User request' } }
    end
    
    trait :with_reference do
      reference_id { SecureRandom.hex(12) }
      reference_type { 'GameSession' }
    end
    
    trait :with_metadata do
      metadata { { 'source' => 'api', 'ip_address' => '***********', 'user_agent' => 'Test Agent' } }
    end
    
    trait :large_amount do
      amount { 1000.0 }
      balance_before { 5000.0 }
      balance_after { 6000.0 }
    end
    
    trait :small_amount do
      amount { 5.0 }
      balance_before { 100.0 }
      balance_after { 105.0 }
    end
  end
end
