#!/bin/bash

# Manager Service Test Runner
# Comprehensive test suite for balance validation and game configuration

set -e

echo "💼 Manager Service Test Suite"
echo "============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
COVERAGE_THRESHOLD=80
PARALLEL_JOBS=4

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v ruby &> /dev/null; then
        print_error "Ruby is not installed"
        exit 1
    fi
    
    if ! command -v bundle &> /dev/null; then
        print_error "Bundler is not installed"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Function to install test dependencies
install_dependencies() {
    print_status "Installing test dependencies..."
    
    # Install gems
    bundle install --quiet || {
        print_error "Failed to install gems"
        exit 1
    }
    
    print_success "Dependencies installed"
}

# Function to start test dependencies
start_test_dependencies() {
    print_status "Starting test dependencies..."
    
    # Start PostgreSQL for testing
    docker run -d --name postgres-manager-test \
        -e POSTGRES_DB=manager_service_test \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=password \
        -p 5433:5432 \
        postgres:14-alpine > /dev/null 2>&1 || {
        print_warning "PostgreSQL test container already running or failed to start"
    }
    
    # Start Redis for testing
    docker run -d --name redis-manager-test -p 6382:6379 redis:7-alpine > /dev/null 2>&1 || {
        print_warning "Redis test container already running or failed to start"
    }
    
    # Wait for services to be ready
    sleep 5
    print_success "Test dependencies started"
}

# Function to stop test dependencies
stop_test_dependencies() {
    print_status "Stopping test dependencies..."
    
    docker stop postgres-manager-test redis-manager-test > /dev/null 2>&1 || true
    docker rm postgres-manager-test redis-manager-test > /dev/null 2>&1 || true
    
    print_success "Test dependencies stopped"
}

# Function to setup test database
setup_test_database() {
    print_status "Setting up test database..."
    
    # Set test environment variables
    export RAILS_ENV=test
    export DATABASE_URL=postgresql://postgres:password@localhost:5433/manager_service_test
    export REDIS_URL=redis://localhost:6382
    
    # Create and migrate test database
    bundle exec rails db:create db:migrate RAILS_ENV=test || {
        print_error "Failed to setup test database"
        return 1
    }
    
    print_success "Test database setup completed"
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    # Run RSpec tests
    bundle exec rspec spec/models spec/services spec/jobs \
        --format progress \
        --color \
        --order random || {
        print_error "Unit tests failed"
        return 1
    }
    
    print_success "Unit tests passed"
}

# Function to run controller tests
run_controller_tests() {
    print_status "Running controller tests..."
    
    # Run controller specs
    bundle exec rspec spec/controllers spec/requests \
        --format progress \
        --color \
        --order random || {
        print_error "Controller tests failed"
        return 1
    }
    
    print_success "Controller tests passed"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Run integration specs if they exist
    if [ -d "spec/integration" ]; then
        bundle exec rspec spec/integration \
            --format progress \
            --color \
            --order random || {
            print_error "Integration tests failed"
            return 1
        }
    else
        print_warning "No integration tests found"
        return 0
    fi
    
    print_success "Integration tests passed"
}

# Function to run coverage tests
run_coverage_tests() {
    print_status "Running tests with coverage..."
    
    # Set coverage environment variable
    export COVERAGE=true
    
    # Run all tests with SimpleCov
    bundle exec rspec \
        --format progress \
        --color \
        --order random || {
        print_error "Tests failed during coverage run"
        return 1
    }
    
    # Check coverage threshold
    if [ -f "coverage/.last_run.json" ]; then
        COVERAGE_PERCENT=$(ruby -r json -e "puts JSON.parse(File.read('coverage/.last_run.json'))['result']['covered_percent'].round(2)")
        echo "Coverage: ${COVERAGE_PERCENT}%"
        
        if (( $(echo "$COVERAGE_PERCENT >= $COVERAGE_THRESHOLD" | bc -l) )); then
            print_success "Coverage threshold met: ${COVERAGE_PERCENT}% >= ${COVERAGE_THRESHOLD}%"
        else
            print_warning "Coverage below threshold: ${COVERAGE_PERCENT}% < ${COVERAGE_THRESHOLD}%"
        fi
        
        print_status "Coverage report generated: coverage/index.html"
    else
        print_warning "No coverage data found"
    fi
    
    print_success "Coverage tests completed"
}

# Function to run linting
run_linting() {
    print_status "Running linting..."
    
    # Check if RuboCop is available
    if bundle list | grep -q rubocop; then
        bundle exec rubocop --format progress || {
            print_warning "Linting issues found"
            return 0
        }
    else
        print_warning "RuboCop not installed, skipping linting"
        return 0
    fi
    
    print_success "Linting passed"
}

# Function to run security checks
run_security_checks() {
    print_status "Running security checks..."
    
    # Check if Brakeman is available
    if bundle list | grep -q brakeman; then
        bundle exec brakeman --quiet --no-pager || {
            print_warning "Security issues found"
            return 0
        }
    else
        print_warning "Brakeman not installed, skipping security checks"
        return 0
    fi
    
    print_success "Security checks passed"
}

# Function to run specific test suites
run_specific_tests() {
    local test_type=$1
    
    case $test_type in
        "balance")
            print_status "Running balance validation tests..."
            bundle exec rspec spec/controllers/balance_validation_spec.rb --format documentation
            ;;
        "game-config")
            print_status "Running game configuration tests..."
            bundle exec rspec spec/controllers/game_configuration_controller_spec.rb --format documentation
            ;;
        "users")
            print_status "Running user tests..."
            bundle exec rspec spec/models/user_spec.rb spec/controllers/users_controller_spec.rb --format documentation
            ;;
        "rooms")
            print_status "Running room tests..."
            bundle exec rspec spec/models/room_spec.rb spec/controllers/rooms_controller_spec.rb --format documentation
            ;;
        *)
            print_error "Unknown test type: $test_type"
            print_status "Available test types: balance, game-config, users, rooms"
            exit 1
            ;;
    esac
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    if [ -d "spec/performance" ]; then
        bundle exec rspec spec/performance \
            --format progress \
            --color || {
            print_warning "Performance tests failed or not available"
            return 0
        }
    else
        print_warning "No performance tests found"
        return 0
    fi
    
    print_success "Performance tests completed"
}

# Function to clean up test artifacts
cleanup() {
    print_status "Cleaning up..."
    
    # Remove coverage and log files
    rm -rf coverage log/test.log tmp/cache
    
    # Stop test dependencies
    stop_test_dependencies
    
    print_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [TEST_TYPE]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -c, --coverage          Run tests with coverage"
    echo "  -l, --lint              Run linting"
    echo "  -s, --security          Run security checks"
    echo "  -p, --performance       Run performance tests"
    echo "  -i, --integration       Run integration tests only"
    echo "  -u, --unit              Run unit tests only"
    echo "  --controllers           Run controller tests only"
    echo "  --no-deps               Skip starting test dependencies"
    echo "  --parallel=N            Set number of parallel test jobs (default: 4)"
    echo ""
    echo "Test Types:"
    echo "  balance                 Run balance validation tests only"
    echo "  game-config             Run game configuration tests only"
    echo "  users                   Run user tests only"
    echo "  rooms                   Run room tests only"
    echo ""
    echo "Examples:"
    echo "  $0                      Run all tests"
    echo "  $0 -c                   Run all tests with coverage"
    echo "  $0 balance              Run only balance validation tests"
    echo "  $0 -u -c                Run unit tests with coverage"
}

# Main execution
main() {
    local run_coverage=false
    local run_lint=false
    local run_security=false
    local run_performance=false
    local run_integration=false
    local run_unit=false
    local run_controllers=false
    local skip_deps=false
    local specific_test=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -c|--coverage)
                run_coverage=true
                shift
                ;;
            -l|--lint)
                run_lint=true
                shift
                ;;
            -s|--security)
                run_security=true
                shift
                ;;
            -p|--performance)
                run_performance=true
                shift
                ;;
            -i|--integration)
                run_integration=true
                shift
                ;;
            -u|--unit)
                run_unit=true
                shift
                ;;
            --controllers)
                run_controllers=true
                shift
                ;;
            --no-deps)
                skip_deps=true
                shift
                ;;
            --parallel=*)
                PARALLEL_JOBS="${1#*=}"
                shift
                ;;
            balance|game-config|users|rooms)
                specific_test=$1
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Set up trap for cleanup
    trap cleanup EXIT
    
    # Check dependencies
    check_dependencies
    
    # Install dependencies
    install_dependencies
    
    # Start test dependencies unless skipped
    if [ "$skip_deps" = false ]; then
        start_test_dependencies
        setup_test_database
    fi
    
    # Run specific test if requested
    if [ -n "$specific_test" ]; then
        run_specific_tests "$specific_test"
        exit 0
    fi
    
    # Run linting if requested
    if [ "$run_lint" = true ]; then
        run_linting
    fi
    
    # Run security checks if requested
    if [ "$run_security" = true ]; then
        run_security_checks
    fi
    
    # Run tests based on flags
    if [ "$run_coverage" = true ]; then
        run_coverage_tests
    elif [ "$run_unit" = true ]; then
        run_unit_tests
    elif [ "$run_controllers" = true ]; then
        run_controller_tests
    elif [ "$run_integration" = true ]; then
        run_integration_tests
    else
        # Run all tests by default
        run_unit_tests
        run_controller_tests
        run_integration_tests
    fi
    
    # Run performance tests if requested
    if [ "$run_performance" = true ]; then
        run_performance_tests
    fi
    
    print_success "All tests completed successfully! 🎉"
}

# Run main function with all arguments
main "$@"
