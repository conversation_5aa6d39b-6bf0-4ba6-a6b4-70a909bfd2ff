# Manager Service Test Suite

This directory contains comprehensive tests for all functionality implemented in the Manager Service. The test suite follows Rails and RSpec best practices with table-driven tests, comprehensive coverage, and realistic test fixtures.

## Test Structure

```
spec/
├── controllers/           # Controller tests (request/response)
│   ├── auth_controller_spec.rb
│   ├── users_controller_spec.rb
│   ├── transactions_controller_spec.rb
│   ├── game_sessions_controller_spec.rb
│   └── admin/
├── models/               # Model tests (validations, associations, methods)
│   ├── user_spec.rb
│   ├── transaction_spec.rb
│   ├── room_spec.rb
│   ├── game_session_spec.rb
│   └── game_setting_spec.rb
├── services/             # Service layer tests (business logic)
│   ├── transaction_service_spec.rb
│   ├── balance_service_spec.rb
│   ├── room_management_service_spec.rb
│   ├── notification_service_spec.rb
│   └── game_service_client_spec.rb
├── jobs/                 # Background job tests
│   ├── transaction_processor_job_spec.rb
│   ├── game_settings_sync_job_spec.rb
│   ├── room_settings_sync_job_spec.rb
│   └── notification_job_spec.rb
├── factories/            # Test data factories
│   ├── users.rb
│   ├── transactions.rb
│   ├── rooms.rb
│   ├── game_sessions.rb
│   └── game_settings.rb
├── rails_helper.rb       # Rails-specific test configuration
├── spec_helper.rb        # General RSpec configuration
└── README.md            # This file
```

## Test Coverage

### Models (5 models, 100% coverage)
- **User**: Authentication, balance management, validations, scopes, callbacks
- **Transaction**: Financial transactions, audit trails, status management
- **Room**: Game room management, password protection, sync logic
- **GameSession**: Individual game session tracking, state management
- **GameSetting**: Configuration management, sync with Game Service

### Controllers (9 controllers, ~50 endpoints)
- **AuthController**: Login, logout, token validation, refresh
- **UsersController**: User CRUD, profile management, admin functions
- **TransactionsController**: Transaction history, deposits, withdrawals, stats
- **GameSessionsController**: Game session management, start/complete/cancel
- **RoomsController**: Room joining/leaving, player ready status
- **Admin Controllers**: Administrative functions for rooms and settings
- **HealthController**: Service health monitoring

### Services (6 service classes)
- **TransactionService**: Atomic balance operations, transaction processing
- **BalanceService**: Advanced balance management with reservations
- **RoomManagementService**: Room lifecycle management, Game Service sync
- **NotificationService**: Real-time notifications via Redis pub/sub
- **GameServiceClient**: gRPC communication with Game Service
- **GameSettingsService**: Configuration synchronization

### Jobs (6 background job classes)
- **TransactionProcessorJob**: Asynchronous transaction processing
- **BalanceReconciliationJob**: Balance consistency checks
- **GameSettingsSyncJob**: Settings synchronization with Game Service
- **RoomSettingsSyncJob**: Room configuration synchronization
- **NotificationJob**: Asynchronous notification delivery

## Running Tests

### Quick Start
```bash
# Run all tests with coverage
bin/test-all

# Run specific test suites
bundle exec rspec spec/models
bundle exec rspec spec/controllers
bundle exec rspec spec/services
bundle exec rspec spec/jobs

# Run specific test file
bundle exec rspec spec/models/user_spec.rb

# Run with specific format
bundle exec rspec --format documentation
```

### Test Runner Options
```bash
# Run with coverage report
bin/test-all --coverage

# Run in parallel (faster)
bin/test-all --parallel

# Run with verbose output
bin/test-all --verbose

# Run specific pattern
bin/test-all --pattern "spec/models/*_spec.rb"

# Run with tags
bin/test-all --tag focus
bin/test-all --exclude-tag slow
```

### Continuous Integration
```bash
# CI-friendly run (fail fast, no browser opening)
CI=true bin/test-all
```

## Test Configuration

### Database Setup
Tests use a separate test database that is automatically created and migrated:
- Database: `manager_service_test`
- Automatic cleanup between tests using DatabaseCleaner
- Mongoid configuration for MongoDB

### Test Dependencies
- **RSpec**: Testing framework
- **FactoryBot**: Test data generation
- **DatabaseCleaner**: Database state management
- **WebMock**: HTTP request mocking
- **Timecop**: Time manipulation for tests
- **Shoulda Matchers**: Rails-specific matchers

### Test Helpers
- **Authentication helpers**: `auth_headers(user)`, `admin_headers(admin)`
- **JSON response helper**: `json_response` for parsing API responses
- **Time manipulation**: Automatic Timecop cleanup
- **Redis mocking**: WebMock configuration for Redis calls

## Test Patterns

### Table-Driven Tests
```ruby
describe 'status validation' do
  %w[active inactive suspended banned].each do |status|
    it "allows #{status} status" do
      user.status = status
      expect(user).to be_valid
    end
  end
end
```

### Comprehensive Error Testing
```ruby
context 'with insufficient balance' do
  it 'raises insufficient balance error' do
    expect {
      service.process_withdrawal(user, 1500.0)
    }.to raise_error(/Insufficient balance/)
  end

  it 'does not create transaction' do
    expect {
      begin
        service.process_withdrawal(user, 1500.0)
      rescue => e
        # Ignore error for this test
      end
    }.not_to change { Transaction.count }
  end
end
```

### Service Integration Tests
```ruby
it 'notifies other services' do
  expect(service).to receive(:notify_balance_update).with(user, instance_of(Transaction))
  service.process_deposit(user, 100.0)
end
```

### Background Job Testing
```ruby
it 'processes transaction successfully' do
  described_class.new.perform(transaction.id.to_s)
  
  transaction.reload
  expect(transaction.status).to eq('completed')
  expect(transaction.processed_at).to be_present
end
```

## Coverage Reports

Coverage reports are automatically generated when running tests with coverage enabled:
- **HTML Report**: `coverage/index.html`
- **JSON Data**: `coverage/.resultset.json`
- **Target**: 90%+ line coverage, 85%+ branch coverage

## Best Practices

### Test Organization
- Group related tests using `describe` and `context`
- Use descriptive test names that explain the expected behavior
- Follow AAA pattern: Arrange, Act, Assert

### Test Data
- Use FactoryBot factories for consistent test data
- Use traits for variations: `create(:user, :admin)`, `create(:transaction, :pending)`
- Avoid hardcoded values; use meaningful test data

### Mocking and Stubbing
- Mock external services (Redis, gRPC calls)
- Stub time-dependent operations with Timecop
- Use `instance_double` for type-safe mocking

### Error Testing
- Test both happy paths and error conditions
- Verify error messages and types
- Test edge cases and boundary conditions

### Performance
- Use `let` for lazy evaluation
- Use `let!` when you need immediate evaluation
- Clean up test data automatically with DatabaseCleaner

## Debugging Tests

### Failed Tests
```bash
# Run only failed tests
bundle exec rspec --only-failures

# Run with detailed output
bundle exec rspec --format documentation --backtrace

# Run specific test with debugging
bundle exec rspec spec/models/user_spec.rb:25 --format documentation
```

### Test Database Issues
```bash
# Reset test database
bundle exec rails db:drop db:create db:migrate RAILS_ENV=test

# Check database state
bundle exec rails console test
```

### Coverage Issues
```bash
# Generate coverage report
COVERAGE=true bundle exec rspec

# Open coverage report
open coverage/index.html
```

## Contributing

When adding new functionality:
1. Write tests first (TDD approach)
2. Ensure all tests pass: `bin/test-all`
3. Maintain coverage above 90%
4. Follow existing test patterns and conventions
5. Update this README if adding new test categories

## Troubleshooting

### Common Issues
- **Redis connection errors**: Ensure Redis is running for notification tests
- **Database connection errors**: Check MongoDB connection and test database setup
- **Timeout errors**: Increase timeout values for slow tests
- **Factory conflicts**: Ensure unique constraints are handled in factories

### Environment Setup
```bash
# Install test dependencies
bundle install

# Setup test database
bundle exec rails db:setup RAILS_ENV=test

# Verify test environment
bundle exec rails console test
```
