require 'rails_helper'

RSpec.describe AuthController, type: :controller do
  describe 'POST #login' do
    let(:user) { create(:user, username: 'testuser', password: 'password123') }

    context 'with valid credentials' do
      let(:valid_params) do
        {
          username: 'testuser',
          password: 'password123'
        }
      end

      it 'returns success response with token' do
        post :login, params: valid_params
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['token']).to be_present
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
        expect(json_response['data']['user']['username']).to eq('testuser')
      end

      it 'updates last_login_at' do
        freeze_time = Time.current
        Timecop.freeze(freeze_time) do
          post :login, params: valid_params
          user.reload
          expect(user.last_login_at).to eq(freeze_time)
        end
      end

      it 'does not include sensitive information' do
        post :login, params: valid_params
        
        expect(json_response['data']['user']).not_to have_key('password_digest')
        expect(json_response['data']['user']).not_to have_key('password')
      end
    end

    context 'with invalid username' do
      let(:invalid_username_params) do
        {
          username: 'nonexistent',
          password: 'password123'
        }
      end

      it 'returns unauthorized error' do
        post :login, params: invalid_username_params
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Invalid username or password')
      end
    end

    context 'with invalid password' do
      let(:invalid_password_params) do
        {
          username: 'testuser',
          password: 'wrongpassword'
        }
      end

      it 'returns unauthorized error' do
        post :login, params: invalid_password_params
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Invalid username or password')
      end
    end

    context 'with missing parameters' do
      it 'returns bad request for missing username' do
        post :login, params: { password: 'password123' }
        
        expect(response).to have_http_status(:bad_request)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Username and password are required')
      end

      it 'returns bad request for missing password' do
        post :login, params: { username: 'testuser' }
        
        expect(response).to have_http_status(:bad_request)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Username and password are required')
      end
    end

    context 'with inactive user' do
      let(:inactive_user) { create(:user, :inactive, username: 'inactive_user', password: 'password123') }
      let(:inactive_params) do
        {
          username: 'inactive_user',
          password: 'password123'
        }
      end

      it 'returns forbidden error' do
        post :login, params: inactive_params
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Account is not active')
      end
    end

    context 'with suspended user' do
      let(:suspended_user) { create(:user, :suspended, username: 'suspended_user', password: 'password123') }
      let(:suspended_params) do
        {
          username: 'suspended_user',
          password: 'password123'
        }
      end

      it 'returns forbidden error' do
        post :login, params: suspended_params
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Account is suspended')
      end
    end

    context 'with banned user' do
      let(:banned_user) { create(:user, :banned, username: 'banned_user', password: 'password123') }
      let(:banned_params) do
        {
          username: 'banned_user',
          password: 'password123'
        }
      end

      it 'returns forbidden error' do
        post :login, params: banned_params
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Account is banned')
      end
    end
  end

  describe 'POST #logout' do
    let(:user) { create(:user) }

    context 'with valid token' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns success response' do
        post :logout
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['message']).to eq('Logged out successfully')
      end
    end

    context 'without token' do
      it 'returns unauthorized error' do
        post :logout
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end

    context 'with invalid token' do
      before do
        request.headers['Authorization'] = 'Bearer invalid_token'
      end

      it 'returns unauthorized error' do
        post :logout
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Invalid token')
      end
    end
  end

  describe 'GET #validate_token' do
    let(:user) { create(:user) }

    context 'with valid token' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns success response with user data' do
        get :validate_token
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
        expect(json_response['data']['user']['username']).to eq(user.username)
        expect(json_response['data']['valid']).to be true
      end

      it 'does not include sensitive information' do
        get :validate_token
        
        expect(json_response['data']['user']).not_to have_key('password_digest')
        expect(json_response['data']['user']).not_to have_key('password')
      end
    end

    context 'without token' do
      it 'returns unauthorized error' do
        get :validate_token
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end

    context 'with invalid token' do
      before do
        request.headers['Authorization'] = 'Bearer invalid_token'
      end

      it 'returns unauthorized error' do
        get :validate_token
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Invalid token')
      end
    end

    context 'with expired token' do
      before do
        expired_token = JWT.encode(
          { user_id: user.id.to_s, exp: 1.hour.ago.to_i }, 
          Rails.application.secret_key_base
        )
        request.headers['Authorization'] = "Bearer #{expired_token}"
      end

      it 'returns unauthorized error' do
        get :validate_token
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Token expired')
      end
    end

    context 'with token for non-existent user' do
      before do
        non_existent_id = BSON::ObjectId.new.to_s
        token = JWT.encode(
          { user_id: non_existent_id, exp: 1.hour.from_now.to_i }, 
          Rails.application.secret_key_base
        )
        request.headers['Authorization'] = "Bearer #{token}"
      end

      it 'returns unauthorized error' do
        get :validate_token
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'with inactive user token' do
      let(:inactive_user) { create(:user, :inactive) }

      before do
        request.headers.merge!(auth_headers(inactive_user))
      end

      it 'returns forbidden error' do
        get :validate_token
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Account is not active')
      end
    end
  end

  describe 'POST #refresh_token' do
    let(:user) { create(:user) }

    context 'with valid token' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns new token' do
        post :refresh_token
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['token']).to be_present
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
      end

      it 'returns different token than original' do
        original_token = request.headers['Authorization'].split(' ').last
        post :refresh_token
        
        new_token = json_response['data']['token']
        expect(new_token).not_to eq(original_token)
      end
    end

    context 'without token' do
      it 'returns unauthorized error' do
        post :refresh_token
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end
  end
end
