require 'rails_helper'

RSpec.describe TransactionsController, type: :controller do
  let(:user) { create(:user, balance: 1000.0) }
  let(:admin_user) { create(:user, :admin) }

  describe 'GET #index' do
    let!(:user_transactions) { create_list(:transaction, 3, user: user) }
    let!(:other_transactions) { create_list(:transaction, 2) }

    context 'as authenticated user viewing own transactions' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns paginated list of user transactions' do
        get :index
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['transactions']).to be_an(Array)
        expect(json_response['data']['transactions'].length).to eq(3)
        expect(json_response['data']['pagination']).to be_present
      end

      it 'filters transactions by type' do
        deposit_transaction = create(:transaction, :deposit, user: user)
        get :index, params: { transaction_type: 'deposit' }
        
        expect(response).to have_http_status(:ok)
        transaction_ids = json_response['data']['transactions'].map { |t| t['id'] }
        expect(transaction_ids).to include(deposit_transaction.id.to_s)
      end

      it 'filters transactions by status' do
        pending_transaction = create(:transaction, :pending, user: user)
        get :index, params: { status: 'pending' }
        
        expect(response).to have_http_status(:ok)
        transaction_ids = json_response['data']['transactions'].map { |t| t['id'] }
        expect(transaction_ids).to include(pending_transaction.id.to_s)
      end

      it 'filters transactions by date range' do
        old_transaction = create(:transaction, user: user, created_at: 1.week.ago)
        recent_transaction = create(:transaction, user: user, created_at: 1.day.ago)
        
        get :index, params: { 
          start_date: 2.days.ago.iso8601, 
          end_date: Time.current.iso8601 
        }
        
        expect(response).to have_http_status(:ok)
        transaction_ids = json_response['data']['transactions'].map { |t| t['id'] }
        expect(transaction_ids).to include(recent_transaction.id.to_s)
        expect(transaction_ids).not_to include(old_transaction.id.to_s)
      end

      it 'paginates results' do
        get :index, params: { page: 1, per_page: 2 }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['data']['transactions'].length).to eq(2)
        expect(json_response['data']['pagination']['current_page']).to eq(1)
      end

      it 'does not include other users transactions' do
        get :index
        
        transaction_user_ids = json_response['data']['transactions'].map { |t| t['user_id'] }.uniq
        expect(transaction_user_ids).to contain_exactly(user.id.to_s)
      end
    end

    context 'as admin viewing all transactions' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'returns all transactions when user_id not specified' do
        get :index
        
        expect(response).to have_http_status(:ok)
        expect(json_response['data']['transactions'].length).to eq(5) # 3 + 2
      end

      it 'filters by user_id when specified' do
        get :index, params: { user_id: user.id }
        
        expect(response).to have_http_status(:ok)
        transaction_user_ids = json_response['data']['transactions'].map { |t| t['user_id'] }.uniq
        expect(transaction_user_ids).to contain_exactly(user.id.to_s)
      end
    end

    context 'without authentication' do
      it 'returns unauthorized error' do
        get :index
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end
  end

  describe 'GET #show' do
    let(:transaction) { create(:transaction, user: user) }
    let(:other_transaction) { create(:transaction) }

    context 'as user viewing own transaction' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns transaction details' do
        get :show, params: { id: transaction.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['transaction']['id']).to eq(transaction.id.to_s)
        expect(json_response['data']['transaction']['amount']).to eq(transaction.amount.to_s)
      end

      it 'returns not found for non-existent transaction' do
        get :show, params: { id: BSON::ObjectId.new.to_s }
        
        expect(response).to have_http_status(:not_found)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Transaction not found')
      end

      it 'returns forbidden for other users transaction' do
        get :show, params: { id: other_transaction.id }
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Access denied')
      end
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'can view any transaction' do
        get :show, params: { id: other_transaction.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['transaction']['id']).to eq(other_transaction.id.to_s)
      end
    end
  end

  describe 'POST #deposit' do
    let(:deposit_params) do
      {
        amount: 100.0,
        description: 'Test deposit'
      }
    end

    context 'as authenticated user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'processes deposit successfully' do
        expect {
          post :deposit, params: deposit_params
        }.to change { user.reload.balance }.by(100.0)
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['transaction']['transaction_type']).to eq('deposit')
        expect(json_response['data']['transaction']['amount']).to eq('100.0')
        expect(json_response['data']['new_balance']).to eq('1100.0')
      end

      it 'creates transaction record' do
        expect {
          post :deposit, params: deposit_params
        }.to change { Transaction.count }.by(1)
        
        transaction = Transaction.last
        expect(transaction.user).to eq(user)
        expect(transaction.transaction_type).to eq('deposit')
        expect(transaction.amount).to eq(100.0)
        expect(transaction.status).to eq('completed')
      end

      it 'returns error for invalid amount' do
        post :deposit, params: { amount: -50.0 }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Amount must be positive')
      end

      it 'returns error for zero amount' do
        post :deposit, params: { amount: 0 }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Amount must be positive')
      end

      it 'returns error for missing amount' do
        post :deposit, params: { description: 'Test' }
        
        expect(response).to have_http_status(:bad_request)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Amount is required')
      end
    end

    context 'without authentication' do
      it 'returns unauthorized error' do
        post :deposit, params: deposit_params
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end
  end

  describe 'POST #withdraw' do
    let(:withdraw_params) do
      {
        amount: 100.0,
        description: 'Test withdrawal'
      }
    end

    context 'as authenticated user with sufficient balance' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'processes withdrawal successfully' do
        expect {
          post :withdraw, params: withdraw_params
        }.to change { user.reload.balance }.by(-100.0)
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['transaction']['transaction_type']).to eq('withdrawal')
        expect(json_response['data']['transaction']['amount']).to eq('-100.0')
        expect(json_response['data']['new_balance']).to eq('900.0')
      end

      it 'creates transaction record' do
        expect {
          post :withdraw, params: withdraw_params
        }.to change { Transaction.count }.by(1)
        
        transaction = Transaction.last
        expect(transaction.user).to eq(user)
        expect(transaction.transaction_type).to eq('withdrawal')
        expect(transaction.amount).to eq(-100.0)
        expect(transaction.status).to eq('completed')
      end

      it 'returns error for insufficient balance' do
        post :withdraw, params: { amount: 1500.0 }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Insufficient balance')
      end

      it 'returns error for invalid amount' do
        post :withdraw, params: { amount: -50.0 }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Amount must be positive')
      end
    end

    context 'as user with insufficient balance' do
      let(:poor_user) { create(:user, balance: 50.0) }

      before do
        request.headers.merge!(auth_headers(poor_user))
      end

      it 'returns insufficient balance error' do
        post :withdraw, params: withdraw_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Insufficient balance')
      end
    end
  end

  describe 'GET #stats' do
    before do
      create(:transaction, :deposit, user: user, amount: 200.0)
      create(:transaction, :withdrawal, user: user, amount: -50.0)
      create(:transaction, :bet_won, user: user, amount: 100.0)
      create(:transaction, :bet_placed, user: user, amount: -25.0)
    end

    context 'as authenticated user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns transaction statistics' do
        get :stats
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        stats = json_response['data']['stats']
        expect(stats['total_transactions']).to eq(4)
        expect(stats['total_deposits']).to eq(1)
        expect(stats['total_withdrawals']).to eq(1)
        expect(stats['total_deposit_amount']).to eq('200.0')
        expect(stats['total_withdrawal_amount']).to eq('50.0')
      end

      it 'filters stats by date range' do
        get :stats, params: { 
          start_date: 1.day.ago.iso8601, 
          end_date: Time.current.iso8601 
        }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['data']['stats']).to be_present
      end
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
        create(:transaction, :deposit, amount: 300.0) # Different user
      end

      it 'returns global stats when no user_id specified' do
        get :stats
        
        expect(response).to have_http_status(:ok)
        stats = json_response['data']['stats']
        expect(stats['total_transactions']).to eq(5) # 4 + 1
      end

      it 'returns user-specific stats when user_id specified' do
        get :stats, params: { user_id: user.id }
        
        expect(response).to have_http_status(:ok)
        stats = json_response['data']['stats']
        expect(stats['total_transactions']).to eq(4)
      end
    end
  end

  describe 'POST #create' do
    let(:transaction_params) do
      {
        transaction_type: 'adjustment',
        amount: 50.0,
        description: 'Balance adjustment'
      }
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'creates transaction successfully' do
        expect {
          post :create, params: transaction_params.merge(user_id: user.id)
        }.to change { Transaction.count }.by(1)
        
        expect(response).to have_http_status(:created)
        expect(json_response['success']).to be true
        expect(json_response['data']['transaction']['transaction_type']).to eq('adjustment')
      end

      it 'returns validation errors for invalid data' do
        post :create, params: { user_id: user.id, transaction_type: 'invalid' }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['errors']).to be_present
      end
    end

    context 'as regular user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        post :create, params: transaction_params.merge(user_id: user.id)
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Admin access required')
      end
    end
  end
end
