require 'rails_helper'

RSpec.describe UsersController, type: :controller do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user, :admin) }

  describe 'GET #index' do
    let!(:users) { create_list(:user, 3) }

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'returns paginated list of users' do
        get :index
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['users']).to be_an(Array)
        expect(json_response['data']['users'].length).to eq(4) # 3 created + admin_user
        expect(json_response['data']['pagination']).to be_present
      end

      it 'filters users by status' do
        inactive_user = create(:user, :inactive)
        get :index, params: { status: 'inactive' }
        
        expect(response).to have_http_status(:ok)
        user_ids = json_response['data']['users'].map { |u| u['id'] }
        expect(user_ids).to include(inactive_user.id.to_s)
        expect(user_ids).not_to include(users.first.id.to_s)
      end

      it 'filters users by role' do
        get :index, params: { role: 'admin' }
        
        expect(response).to have_http_status(:ok)
        user_ids = json_response['data']['users'].map { |u| u['id'] }
        expect(user_ids).to include(admin_user.id.to_s)
        expect(user_ids).not_to include(users.first.id.to_s)
      end

      it 'searches users by username' do
        target_user = create(:user, username: 'searchable_user')
        get :index, params: { search: 'searchable' }
        
        expect(response).to have_http_status(:ok)
        user_ids = json_response['data']['users'].map { |u| u['id'] }
        expect(user_ids).to include(target_user.id.to_s)
      end

      it 'paginates results' do
        get :index, params: { page: 1, per_page: 2 }

        expect(response).to have_http_status(:ok)
        expect(json_response['data']['users'].length).to eq(2)
        expect(json_response['data']['pagination']['current_page']).to eq(1)
        expect(json_response['data']['pagination']['total_pages']).to be >= 2
      end

      it 'includes pagination navigation fields' do
        get :index, params: { page: 1, per_page: 2 }

        expect(response).to have_http_status(:ok)
        pagination = json_response['data']['pagination']
        expect(pagination).to have_key('has_next')
        expect(pagination).to have_key('has_prev')
        expect(pagination['has_prev']).to be false
        expect(pagination['has_next']).to be true if pagination['total_pages'] > 1
      end

      it 'searches users by username' do
        target_user = create(:user, username: 'searchable_user')
        get :index, params: { search: 'searchable' }

        expect(response).to have_http_status(:ok)
        user_usernames = json_response['data']['users'].map { |u| u['username'] }
        expect(user_usernames).to include('searchable_user')
      end

      it 'searches users by email' do
        target_user = create(:user, email: '<EMAIL>')
        get :index, params: { search: 'searchable@example' }

        expect(response).to have_http_status(:ok)
        user_emails = json_response['data']['users'].map { |u| u['email'] }
        expect(user_emails).to include('<EMAIL>')
      end

      it 'returns empty results for non-matching search' do
        get :index, params: { search: 'nonexistent_user_xyz' }

        expect(response).to have_http_status(:ok)
        expect(json_response['data']['users']).to be_empty
      end
    end

    context 'as regular user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        get :index
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Admin access required')
      end
    end

    context 'without authentication' do
      it 'returns unauthorized error' do
        get :index
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end
  end

  describe 'GET #show' do
    context 'as admin viewing any user' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'returns user details' do
        get :show, params: { id: user.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
        expect(json_response['data']['user']['username']).to eq(user.username)
      end

      it 'includes user statistics' do
        create(:game_session, :completed, user: user)
        create(:transaction, :deposit, user: user)
        
        get :show, params: { id: user.id }
        
        expect(json_response['data']['stats']).to be_present
        expect(json_response['data']['stats']['total_games']).to eq(1)
        expect(json_response['data']['stats']['total_transactions']).to eq(1)
      end

      it 'returns not found for non-existent user' do
        get :show, params: { id: BSON::ObjectId.new.to_s }
        
        expect(response).to have_http_status(:not_found)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'as user viewing own profile' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns own user details' do
        get :show, params: { id: user.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
      end
    end

    context 'as user viewing other user' do
      let(:other_user) { create(:user) }

      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        get :show, params: { id: other_user.id }
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Access denied')
      end
    end
  end

  describe 'POST #create' do
    let(:valid_params) do
      {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      }
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'creates new user successfully' do
        expect {
          post :create, params: valid_params
        }.to change { User.count }.by(1)
        
        expect(response).to have_http_status(:created)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['username']).to eq('newuser')
      end

      it 'creates admin deposit transaction for new user' do
        expect {
          post :create, params: valid_params.merge(initial_balance: 500)
        }.to change { Transaction.admin_deposits.count }.by(1)
        
        new_user = User.find_by(username: 'newuser')
        expect(new_user.balance).to eq(500)
      end

      it 'returns validation errors for invalid data' do
        post :create, params: valid_params.merge(username: '')
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['errors']).to be_present
      end

      it 'returns error for duplicate username' do
        create(:user, username: 'newuser')
        post :create, params: valid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['errors']['username']).to include('is already taken')
      end
    end

    context 'as regular user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        post :create, params: valid_params
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Admin access required')
      end
    end
  end

  describe 'PUT #update' do
    let(:update_params) do
      {
        email: '<EMAIL>',
        status: 'inactive'
      }
    end

    context 'as admin updating any user' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'updates user successfully' do
        put :update, params: { id: user.id }.merge(update_params)
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        user.reload
        expect(user.email).to eq('<EMAIL>')
        expect(user.status).to eq('inactive')
      end

      it 'returns validation errors for invalid data' do
        put :update, params: { id: user.id, email: 'invalid-email' }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['errors']).to be_present
      end

      it 'returns not found for non-existent user' do
        put :update, params: { id: BSON::ObjectId.new.to_s }.merge(update_params)
        
        expect(response).to have_http_status(:not_found)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'as user updating own profile' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'updates allowed fields successfully' do
        put :update, params: { id: user.id, email: '<EMAIL>' }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        user.reload
        expect(user.email).to eq('<EMAIL>')
      end

      it 'ignores restricted fields' do
        original_role = user.role
        put :update, params: { id: user.id, role: 'admin', status: 'banned' }
        
        expect(response).to have_http_status(:ok)
        
        user.reload
        expect(user.role).to eq(original_role)
        expect(user.status).to eq('active') # Should remain unchanged
      end
    end

    context 'as user updating other user' do
      let(:other_user) { create(:user) }

      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        put :update, params: { id: other_user.id }.merge(update_params)
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Access denied')
      end
    end
  end

  describe 'DELETE #destroy' do
    let(:target_user) { create(:user) }

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'soft deletes user successfully' do
        delete :destroy, params: { id: target_user.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['message']).to eq('User deleted successfully')
        
        target_user.reload
        expect(target_user.status).to eq('inactive')
      end

      it 'returns not found for non-existent user' do
        delete :destroy, params: { id: BSON::ObjectId.new.to_s }
        
        expect(response).to have_http_status(:not_found)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'as regular user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns forbidden error' do
        delete :destroy, params: { id: target_user.id }
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Admin access required')
      end
    end
  end

  describe 'GET #profile' do
    context 'with valid authentication' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns current user profile' do
        get :profile
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['id']).to eq(user.id.to_s)
        expect(json_response['data']['user']['username']).to eq(user.username)
      end

      it 'includes user statistics' do
        create(:game_session, :completed, user: user)
        create(:transaction, :deposit, user: user)
        
        get :profile
        
        expect(json_response['data']['stats']).to be_present
        expect(json_response['data']['stats']['total_games']).to eq(1)
        expect(json_response['data']['stats']['total_transactions']).to eq(1)
      end

      it 'does not include sensitive information' do
        get :profile
        
        expect(json_response['data']['user']).not_to have_key('password_digest')
        expect(json_response['data']['user']).not_to have_key('password')
      end
    end

    context 'without authentication' do
      it 'returns unauthorized error' do
        get :profile
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end
    end
  end
end
