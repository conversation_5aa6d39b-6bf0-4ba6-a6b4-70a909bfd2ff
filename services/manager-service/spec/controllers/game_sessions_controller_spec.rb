require 'rails_helper'

RSpec.describe GameSessionsController, type: :controller do
  let(:user) { create(:user, balance: 1000.0) }
  let(:admin_user) { create(:user, :admin) }

  describe 'GET #index' do
    let!(:user_sessions) { create_list(:game_session, 3, user: user) }
    let!(:other_sessions) { create_list(:game_session, 2) }

    context 'as authenticated user' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns paginated list of user game sessions' do
        get :index
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['game_sessions']).to be_an(Array)
        expect(json_response['data']['game_sessions'].length).to eq(3)
        expect(json_response['data']['pagination']).to be_present
      end

      it 'filters sessions by game_type' do
        amidakuji_session = create(:game_session, :amidakuji, user: user)
        get :index, params: { game_type: 'amidakuji' }
        
        expect(response).to have_http_status(:ok)
        session_ids = json_response['data']['game_sessions'].map { |s| s['id'] }
        expect(session_ids).to include(amidakuji_session.id.to_s)
      end

      it 'filters sessions by status' do
        completed_session = create(:game_session, :completed, user: user)
        get :index, params: { status: 'completed' }
        
        expect(response).to have_http_status(:ok)
        session_ids = json_response['data']['game_sessions'].map { |s| s['id'] }
        expect(session_ids).to include(completed_session.id.to_s)
      end

      it 'does not include other users sessions' do
        get :index
        
        session_user_ids = json_response['data']['game_sessions'].map { |s| s['user_id'] }.uniq
        expect(session_user_ids).to contain_exactly(user.id.to_s)
      end
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'returns all sessions when user_id not specified' do
        get :index
        
        expect(response).to have_http_status(:ok)
        expect(json_response['data']['game_sessions'].length).to eq(5) # 3 + 2
      end

      it 'filters by user_id when specified' do
        get :index, params: { user_id: user.id }
        
        expect(response).to have_http_status(:ok)
        session_user_ids = json_response['data']['game_sessions'].map { |s| s['user_id'] }.uniq
        expect(session_user_ids).to contain_exactly(user.id.to_s)
      end
    end
  end

  describe 'GET #show' do
    let(:game_session) { create(:game_session, user: user) }
    let(:other_session) { create(:game_session) }

    context 'as user viewing own session' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'returns session details' do
        get :show, params: { id: game_session.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['game_session']['id']).to eq(game_session.id.to_s)
      end

      it 'returns forbidden for other users session' do
        get :show, params: { id: other_session.id }
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Access denied')
      end
    end

    context 'as admin' do
      before do
        request.headers.merge!(auth_headers(admin_user))
      end

      it 'can view any session' do
        get :show, params: { id: other_session.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['game_session']['id']).to eq(other_session.id.to_s)
      end
    end
  end

  describe 'POST #create' do
    let(:session_params) do
      {
        game_type: 'prizewheel',
        bet_amount: 100.0
      }
    end

    context 'as authenticated user with sufficient balance' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'creates game session successfully' do
        expect {
          post :create, params: session_params
        }.to change { GameSession.count }.by(1)
        
        expect(response).to have_http_status(:created)
        expect(json_response['success']).to be true
        expect(json_response['data']['game_session']['game_type']).to eq('prizewheel')
        expect(json_response['data']['game_session']['bet_amount']).to eq('100.0')
      end

      it 'reserves balance for the bet' do
        post :create, params: session_params
        
        user.reload
        expect(user.balance).to eq(900.0) # 1000 - 100
      end

      it 'creates bet_reserved transaction' do
        expect {
          post :create, params: session_params
        }.to change { Transaction.where(transaction_type: 'bet_reserved').count }.by(1)
      end

      it 'returns validation errors for invalid data' do
        post :create, params: { game_type: 'invalid', bet_amount: 100.0 }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['errors']).to be_present
      end
    end

    context 'as user with insufficient balance' do
      let(:poor_user) { create(:user, balance: 50.0) }

      before do
        request.headers.merge!(auth_headers(poor_user))
      end

      it 'returns insufficient balance error' do
        post :create, params: session_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Insufficient balance')
      end
    end
  end

  describe 'PUT #start' do
    let(:game_session) { create(:game_session, user: user, status: 'pending') }

    context 'as session owner' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'starts session successfully' do
        put :start, params: { id: game_session.id }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        game_session.reload
        expect(game_session.status).to eq('active')
        expect(game_session.started_at).to be_present
      end

      it 'returns error for already started session' do
        game_session.update!(status: 'active')
        put :start, params: { id: game_session.id }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('already started')
      end
    end

    context 'as different user' do
      let(:other_user) { create(:user) }

      before do
        request.headers.merge!(auth_headers(other_user))
      end

      it 'returns forbidden error' do
        put :start, params: { id: game_session.id }
        
        expect(response).to have_http_status(:forbidden)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Access denied')
      end
    end
  end

  describe 'PUT #complete' do
    let(:game_session) { create(:game_session, :active, user: user, bet_amount: 100.0) }
    let(:complete_params) do
      {
        win_amount: 200.0,
        result: {
          winner_position: 1,
          total_participants: 8,
          prize_pool: 800.0
        }
      }
    end

    context 'as session owner' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'completes session successfully' do
        put :complete, params: { id: game_session.id }.merge(complete_params)
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        game_session.reload
        expect(game_session.status).to eq('completed')
        expect(game_session.win_amount).to eq(200.0)
        expect(game_session.ended_at).to be_present
        expect(game_session.result['winner_position']).to eq(1)
      end

      it 'processes win transaction' do
        expect {
          put :complete, params: { id: game_session.id }.merge(complete_params)
        }.to change { Transaction.where(transaction_type: 'bet_won').count }.by(1)
        
        user.reload
        expect(user.balance).to eq(1100.0) # 900 (after bet) + 200 (win)
      end

      it 'handles loss (zero win amount)' do
        put :complete, params: { id: game_session.id, win_amount: 0.0 }
        
        expect(response).to have_http_status(:ok)
        
        game_session.reload
        expect(game_session.win_amount).to eq(0.0)
        
        user.reload
        expect(user.balance).to eq(900.0) # No additional credit for loss
      end

      it 'returns error for non-active session' do
        game_session.update!(status: 'pending')
        put :complete, params: { id: game_session.id }.merge(complete_params)
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('not active')
      end
    end
  end

  describe 'PUT #cancel' do
    let(:game_session) { create(:game_session, user: user, status: 'pending') }

    context 'as session owner' do
      before do
        request.headers.merge!(auth_headers(user))
      end

      it 'cancels session successfully' do
        put :cancel, params: { id: game_session.id, reason: 'User request' }
        
        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        
        game_session.reload
        expect(game_session.status).to eq('cancelled')
        expect(game_session.ended_at).to be_present
        expect(game_session.result['cancellation_reason']).to eq('User request')
      end

      it 'refunds reserved balance' do
        put :cancel, params: { id: game_session.id }
        
        expect(response).to have_http_status(:ok)
        
        user.reload
        expect(user.balance).to eq(1000.0) # Balance restored
      end

      it 'creates refund transaction' do
        expect {
          put :cancel, params: { id: game_session.id }
        }.to change { Transaction.where(transaction_type: 'refund').count }.by(1)
      end

      it 'returns error for completed session' do
        game_session.update!(status: 'completed')
        put :cancel, params: { id: game_session.id }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('cannot be cancelled')
      end
    end
  end

  describe 'authentication and authorization' do
    let(:game_session) { create(:game_session, user: user) }

    context 'without authentication' do
      it 'returns unauthorized for index' do
        get :index
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Authentication required')
      end

      it 'returns unauthorized for show' do
        get :show, params: { id: game_session.id }
        
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns unauthorized for create' do
        post :create, params: { game_type: 'prizewheel', bet_amount: 100.0 }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['Authorization'] = 'Bearer invalid_token'
      end

      it 'returns unauthorized error' do
        get :index
        
        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Invalid token')
      end
    end
  end
end
