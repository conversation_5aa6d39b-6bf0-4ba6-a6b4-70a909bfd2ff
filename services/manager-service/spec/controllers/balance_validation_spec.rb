require 'rails_helper'

RSpec.describe 'Balance Validation API', type: :request do
  let(:user) { create(:user, balance: 1000) }
  let(:admin_user) { create(:user, :admin) }
  let(:headers) { { 'Content-Type' => 'application/json' } }

  describe 'POST /users/:id/validate_balance' do
    context 'when user has sufficient balance' do
      it 'returns valid true' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 100 }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['data']['valid']).to be true
        expect(json_response['data']['user_balance']).to eq(1000)
        expect(json_response['data']['bet_amount']).to eq(100)
        expect(json_response['data']['remaining_balance']).to eq(900)
      end
    end

    context 'when user has exact balance' do
      it 'returns valid true' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 1000 }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['data']['valid']).to be true
        expect(json_response['data']['remaining_balance']).to eq(0)
      end
    end

    context 'when user has insufficient balance' do
      it 'returns valid false' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 1500 }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['data']['valid']).to be false
        expect(json_response['data']['user_balance']).to eq(1000)
        expect(json_response['data']['bet_amount']).to eq(1500)
        expect(json_response['data']['deficit']).to eq(500)
      end
    end

    context 'when user does not exist' do
      it 'returns user not found error' do
        post "/users/999999/validate_balance", 
             params: { bet_amount: 100 }.to_json,
             headers: headers

        expect(response).to have_http_status(:not_found)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'when bet amount is invalid' do
      it 'returns validation error for negative amount' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: -100 }.to_json,
             headers: headers

        expect(response).to have_http_status(:bad_request)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Bet amount must be positive')
      end

      it 'returns validation error for zero amount' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 0 }.to_json,
             headers: headers

        expect(response).to have_http_status(:bad_request)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Bet amount must be positive')
      end

      it 'returns validation error for missing bet amount' do
        post "/users/#{user.id}/validate_balance", 
             params: {}.to_json,
             headers: headers

        expect(response).to have_http_status(:bad_request)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Bet amount is required')
      end
    end

    context 'when request format is invalid' do
      it 'returns bad request for invalid JSON' do
        post "/users/#{user.id}/validate_balance", 
             params: 'invalid json',
             headers: headers

        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'with edge cases' do
      it 'handles very large bet amounts' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 999999999999 }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['data']['valid']).to be false
        expect(json_response['data']['deficit']).to be > 0
      end

      it 'handles decimal bet amounts' do
        post "/users/#{user.id}/validate_balance", 
             params: { bet_amount: 100.50 }.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['data']['bet_amount']).to eq(100.50)
      end
    end
  end

  describe 'Performance and concurrency' do
    it 'handles multiple concurrent balance validation requests' do
      threads = []
      results = []
      
      10.times do |i|
        threads << Thread.new do
          response = nil
          begin
            response = post "/users/#{user.id}/validate_balance", 
                           params: { bet_amount: 100 }.to_json,
                           headers: headers
            results << response.status
          rescue => e
            results << e.message
          end
        end
      end
      
      threads.each(&:join)
      
      # All requests should succeed
      expect(results.all? { |r| r == 200 }).to be true
    end
  end

  describe 'Logging and monitoring' do
    it 'logs balance validation requests' do
      expect(Rails.logger).to receive(:info).with(/Balance validation requested/)
      
      post "/users/#{user.id}/validate_balance", 
           params: { bet_amount: 100 }.to_json,
           headers: headers
    end

    it 'logs validation results' do
      expect(Rails.logger).to receive(:info).with(/Balance validation result/)
      
      post "/users/#{user.id}/validate_balance", 
           params: { bet_amount: 100 }.to_json,
           headers: headers
    end
  end
end

RSpec.describe 'Player Kicked Notification API', type: :request do
  let(:room) { create(:room) }
  let(:user) { create(:user) }
  let(:admin_user) { create(:user, :admin) }
  let(:headers) { { 'Content-Type' => 'application/json' } }

  describe 'POST /game_service/rooms/:id/player_kicked' do
    let(:valid_params) do
      {
        user_id: user.id,
        username: user.username,
        reason: 'Insufficient balance',
        kicked_by: 'game-service'
      }
    end

    context 'with valid parameters' do
      it 'successfully processes player kicked notification' do
        post "/game_service/rooms/#{room.id}/player_kicked",
             params: valid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['message']).to eq('Player kicked notification processed successfully')
      end

      it 'logs the kick event' do
        expect(Rails.logger).to receive(:info).with(/Player kicked notification received/)
        
        post "/game_service/rooms/#{room.id}/player_kicked",
             params: valid_params.to_json,
             headers: headers
      end
    end

    context 'when room does not exist' do
      it 'returns room not found error' do
        post "/game_service/rooms/999999/player_kicked",
             params: valid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:not_found)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Room not found')
      end
    end

    context 'when user does not exist' do
      it 'returns user not found error' do
        invalid_params = valid_params.merge(user_id: 999999)
        
        post "/game_service/rooms/#{room.id}/player_kicked",
             params: invalid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:not_found)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('User not found')
      end
    end

    context 'with missing required parameters' do
      it 'returns validation error for missing user_id' do
        invalid_params = valid_params.except(:user_id)
        
        post "/game_service/rooms/#{room.id}/player_kicked",
             params: invalid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:bad_request)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('User ID is required')
      end

      it 'returns validation error for missing reason' do
        invalid_params = valid_params.except(:reason)
        
        post "/game_service/rooms/#{room.id}/player_kicked",
             params: invalid_params.to_json,
             headers: headers

        expect(response).to have_http_status(:bad_request)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to include('Reason is required')
      end
    end

    context 'with different kick reasons' do
      %w[
        Insufficient\ balance
        Inappropriate\ behavior
        Connection\ timeout
        Admin\ action
        System\ maintenance
      ].each do |reason|
        it "handles kick reason: #{reason}" do
          params_with_reason = valid_params.merge(reason: reason)
          
          post "/game_service/rooms/#{room.id}/player_kicked",
               params: params_with_reason.to_json,
               headers: headers

          expect(response).to have_http_status(:ok)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
        end
      end
    end
  end
end

RSpec.describe 'Game Configuration API', type: :request do
  let(:admin_user) { create(:user, :admin) }
  let(:regular_user) { create(:user) }
  let(:headers) { { 'Content-Type' => 'application/json' } }

  describe 'GET /game_configuration' do
    context 'with default configuration' do
      it 'returns default game configuration' do
        get '/game_configuration', headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['data']['starting_duration']).to eq(10)
        expect(json_response['data']['playing_duration']).to eq(30)
        expect(json_response['data']['end_duration']).to eq(15)
      end
    end

    context 'with environment variable configuration' do
      before do
        ENV['GAME_STARTING_DURATION'] = '20'
        ENV['GAME_PLAYING_DURATION'] = '45'
        ENV['GAME_END_DURATION'] = '25'
      end

      after do
        ENV.delete('GAME_STARTING_DURATION')
        ENV.delete('GAME_PLAYING_DURATION')
        ENV.delete('GAME_END_DURATION')
      end

      it 'returns configuration from environment variables' do
        get '/game_configuration', headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['data']['starting_duration']).to eq(20)
        expect(json_response['data']['playing_duration']).to eq(45)
        expect(json_response['data']['end_duration']).to eq(25)
      end
    end
  end

  describe 'PUT /game_configuration' do
    let(:valid_config) do
      {
        starting_duration: 15,
        playing_duration: 40,
        end_duration: 20
      }
    end

    context 'with valid configuration' do
      it 'updates game configuration successfully' do
        put '/game_configuration',
            params: valid_config.to_json,
            headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['data']['starting_duration']).to eq(15)
        expect(json_response['data']['playing_duration']).to eq(40)
        expect(json_response['data']['end_duration']).to eq(20)
      end
    end

    context 'with partial configuration update' do
      it 'updates only specified fields' do
        partial_config = { starting_duration: 25 }
        
        put '/game_configuration',
            params: partial_config.to_json,
            headers: headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['data']['starting_duration']).to eq(25)
        # Other fields should remain at defaults
        expect(json_response['data']['playing_duration']).to eq(30)
        expect(json_response['data']['end_duration']).to eq(15)
      end
    end

    context 'with invalid configuration values' do
      it 'handles negative duration values gracefully' do
        invalid_config = valid_config.merge(starting_duration: -5)
        
        put '/game_configuration',
            params: invalid_config.to_json,
            headers: headers

        expect(response).to have_http_status(:ok)
        # Should use default for invalid values
        json_response = JSON.parse(response.body)
        expect(json_response['data']['starting_duration']).to eq(10) # Default
      end
    end
  end
end
