require 'rails_helper'

RSpec.describe GameServiceClient, type: :service do
  let(:client) { described_class.new }

  describe '#create_room' do
    let(:room_params) do
      {
        room_name: 'Test Room',
        game_type: 'prizewheel',
        max_players: 8,
        bet_amount: 100.0,
        is_private: false
      }
    end

    context 'when gRPC call succeeds' do
      let(:mock_response) do
        double('CreateRoomResponse', room: double('Room', id: 'room_123', name: 'Test Room', status: 'waiting'))
      end

      before do
        allow(client).to receive(:make_grpc_request).with('create_room', room_params).and_return(mock_response)
      end

      it 'creates room successfully' do
        response = client.create_room(room_params)
        
        expect(response).to eq(mock_response)
        expect(response.room.id).to eq('room_123')
        expect(response.room.name).to eq('Test Room')
      end

      it 'logs successful creation' do
        expect(Rails.logger).to receive(:info).with(/Room created in Game Service/)
        client.create_room(room_params)
      end
    end

    context 'when gRPC call fails' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('gRPC error'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to create room in Game Service/)
        
        expect {
          client.create_room(room_params)
        }.to raise_error('gRPC error')
      end
    end

    context 'with invalid parameters' do
      it 'raises argument error for missing room_name' do
        room_params.delete(:room_name)
        
        expect {
          client.create_room(room_params)
        }.to raise_error(ArgumentError, /room_name is required/)
      end

      it 'raises argument error for missing game_type' do
        room_params.delete(:game_type)
        
        expect {
          client.create_room(room_params)
        }.to raise_error(ArgumentError, /game_type is required/)
      end
    end
  end

  describe '#update_room' do
    let(:room_id) { 'room_123' }
    let(:update_params) do
      {
        room_name: 'Updated Room',
        max_players: 10,
        bet_amount: 200.0
      }
    end

    context 'when gRPC call succeeds' do
      before do
        allow(client).to receive(:make_grpc_request).with('update_room', hash_including(room_id: room_id)).and_return(true)
      end

      it 'updates room successfully' do
        result = client.update_room(room_id, update_params)
        
        expect(result).to be true
      end

      it 'logs successful update' do
        expect(Rails.logger).to receive(:info).with(/Room updated in Game Service/)
        client.update_room(room_id, update_params)
      end
    end

    context 'when gRPC call fails' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Update failed'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to update room in Game Service/)
        
        expect {
          client.update_room(room_id, update_params)
        }.to raise_error('Update failed')
      end
    end

    context 'with invalid room_id' do
      it 'raises argument error for nil room_id' do
        expect {
          client.update_room(nil, update_params)
        }.to raise_error(ArgumentError, /room_id is required/)
      end

      it 'raises argument error for empty room_id' do
        expect {
          client.update_room('', update_params)
        }.to raise_error(ArgumentError, /room_id is required/)
      end
    end
  end

  describe '#delete_room' do
    let(:room_id) { 'room_123' }

    context 'when gRPC call succeeds' do
      before do
        allow(client).to receive(:make_grpc_request).with('delete_room', { room_id: room_id }).and_return(true)
      end

      it 'deletes room successfully' do
        result = client.delete_room(room_id)
        
        expect(result).to be true
      end

      it 'logs successful deletion' do
        expect(Rails.logger).to receive(:info).with(/Room deleted from Game Service/)
        client.delete_room(room_id)
      end
    end

    context 'when gRPC call fails' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Deletion failed'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to delete room from Game Service/)
        
        expect {
          client.delete_room(room_id)
        }.to raise_error('Deletion failed')
      end
    end
  end

  describe '#get_room_status' do
    let(:room_id) { 'room_123' }

    context 'when gRPC call succeeds' do
      let(:mock_status) do
        {
          'status' => 'playing',
          'current_players' => 6,
          'prize_pool' => 600.0,
          'started_at' => Time.current.iso8601
        }
      end

      before do
        allow(client).to receive(:make_grpc_request).with('get_room_status', { room_id: room_id }).and_return(mock_status)
      end

      it 'returns room status successfully' do
        status = client.get_room_status(room_id)
        
        expect(status['status']).to eq('playing')
        expect(status['current_players']).to eq(6)
        expect(status['prize_pool']).to eq(600.0)
      end

      it 'logs successful status retrieval' do
        expect(Rails.logger).to receive(:info).with(/Room status retrieved from Game Service/)
        client.get_room_status(room_id)
      end
    end

    context 'when gRPC call fails' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Status retrieval failed'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to get room status from Game Service/)
        
        expect {
          client.get_room_status(room_id)
        }.to raise_error('Status retrieval failed')
      end
    end
  end

  describe '#update_game_settings' do
    let(:game_type) { 'prizewheel' }
    let(:settings) do
      {
        'max_players' => 8,
        'min_bet_amount' => 10.0,
        'max_bet_amount' => 1000.0
      }
    end

    context 'when gRPC call succeeds' do
      before do
        allow(client).to receive(:make_grpc_request).with('update_game_settings', hash_including(game_type: game_type)).and_return(true)
      end

      it 'updates game settings successfully' do
        result = client.update_game_settings(game_type, settings)
        
        expect(result).to be true
      end

      it 'logs successful settings update' do
        expect(Rails.logger).to receive(:info).with(/Game settings updated in Game Service/)
        client.update_game_settings(game_type, settings)
      end
    end

    context 'when gRPC call fails' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Settings update failed'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to update game settings in Game Service/)
        
        expect {
          client.update_game_settings(game_type, settings)
        }.to raise_error('Settings update failed')
      end
    end

    context 'with invalid parameters' do
      it 'raises argument error for invalid game_type' do
        expect {
          client.update_game_settings('invalid_game', settings)
        }.to raise_error(ArgumentError, /Invalid game_type/)
      end

      it 'raises argument error for nil settings' do
        expect {
          client.update_game_settings(game_type, nil)
        }.to raise_error(ArgumentError, /settings cannot be nil/)
      end
    end
  end

  describe '#get_room_list' do
    context 'when gRPC call succeeds' do
      let(:mock_rooms) do
        [
          {
            'id' => 'room_1',
            'name' => 'Room 1',
            'game_type' => 'prizewheel',
            'status' => 'waiting',
            'current_players' => 2,
            'max_players' => 8
          },
          {
            'id' => 'room_2',
            'name' => 'Room 2',
            'game_type' => 'amidakuji',
            'status' => 'playing',
            'current_players' => 6,
            'max_players' => 10
          }
        ]
      end

      before do
        allow(client).to receive(:make_grpc_request).with('get_room_list', {}).and_return(mock_rooms)
      end

      it 'returns room list successfully' do
        rooms = client.get_room_list
        
        expect(rooms).to be_an(Array)
        expect(rooms.length).to eq(2)
        expect(rooms.first['name']).to eq('Room 1')
        expect(rooms.last['name']).to eq('Room 2')
      end

      it 'logs successful room list retrieval' do
        expect(Rails.logger).to receive(:info).with(/Room list retrieved from Game Service/)
        client.get_room_list
      end
    end

    context 'with filters' do
      let(:filters) { { game_type: 'prizewheel', status: 'waiting' } }

      before do
        allow(client).to receive(:make_grpc_request).with('get_room_list', filters).and_return([])
      end

      it 'passes filters to gRPC call' do
        expect(client).to receive(:make_grpc_request).with('get_room_list', filters)
        client.get_room_list(filters)
      end
    end
  end

  describe 'connection management' do
    describe '#health_check' do
      context 'when service is healthy' do
        before do
          allow(client).to receive(:make_grpc_request).with('health_check', {}).and_return({ 'status' => 'healthy' })
        end

        it 'returns healthy status' do
          result = client.health_check
          
          expect(result['status']).to eq('healthy')
        end
      end

      context 'when service is unhealthy' do
        before do
          allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Service unavailable'))
        end

        it 'raises error' do
          expect {
            client.health_check
          }.to raise_error('Service unavailable')
        end
      end
    end

    describe '#connection_status' do
      it 'returns connection information' do
        status = client.connection_status
        
        expect(status).to be_a(Hash)
        expect(status).to have_key('connected')
        expect(status).to have_key('last_ping')
      end
    end
  end

  describe 'error handling' do
    context 'when gRPC service is unavailable' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Service unavailable'))
      end

      it 'handles connection errors gracefully' do
        expect(Rails.logger).to receive(:error).with(/Failed to create room in Game Service/)
        
        expect {
          client.create_room({ room_name: 'Test', game_type: 'prizewheel' })
        }.to raise_error('Service unavailable')
      end
    end

    context 'when gRPC timeout occurs' do
      before do
        allow(client).to receive(:make_grpc_request).and_raise(StandardError.new('Timeout'))
      end

      it 'handles timeout errors' do
        expect(Rails.logger).to receive(:error).with(/Failed to get room status from Game Service/)
        
        expect {
          client.get_room_status('room_123')
        }.to raise_error('Timeout')
      end
    end
  end

  describe 'private methods' do
    describe '#validate_room_params' do
      it 'validates required parameters' do
        valid_params = { room_name: 'Test', game_type: 'prizewheel', max_players: 8 }
        
        expect {
          client.send(:validate_room_params, valid_params)
        }.not_to raise_error
      end

      it 'raises error for missing room_name' do
        invalid_params = { game_type: 'prizewheel', max_players: 8 }
        
        expect {
          client.send(:validate_room_params, invalid_params)
        }.to raise_error(ArgumentError, /room_name is required/)
      end
    end

    describe '#validate_game_type' do
      it 'validates supported game types' do
        %w[prizewheel amidakuji].each do |game_type|
          expect {
            client.send(:validate_game_type, game_type)
          }.not_to raise_error
        end
      end

      it 'raises error for unsupported game type' do
        expect {
          client.send(:validate_game_type, 'invalid_game')
        }.to raise_error(ArgumentError, /Invalid game_type/)
      end
    end

    describe '#format_room_data' do
      let(:room_data) do
        {
          'id' => 'room_123',
          'name' => 'Test Room',
          'created_at' => Time.current.iso8601
        }
      end

      it 'formats room data correctly' do
        formatted = client.send(:format_room_data, room_data)
        
        expect(formatted['id']).to eq('room_123')
        expect(formatted['name']).to eq('Test Room')
        expect(formatted['created_at']).to be_present
      end
    end
  end
end
