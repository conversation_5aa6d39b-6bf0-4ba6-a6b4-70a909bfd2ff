require 'rails_helper'

RSpec.describe RoomManagementService, type: :service do
  let(:service) { described_class.new }
  let(:user) { create(:user, balance: 1000) }
  let(:room) { create(:room, bet_amount: 100, max_players: 4) }
  let(:balance_service) { instance_double(BalanceService) }
  let(:game_client) { instance_double(GameServiceClient) }
  let(:notification_service) { instance_double(NotificationService) }

  before do
    allow(BalanceService).to receive(:instance).and_return(balance_service)
    allow(GameServiceClient).to receive(:instance).and_return(game_client)
    allow(NotificationService).to receive(:instance).and_return(notification_service)
    allow(service).to receive(:instance_variable_get).with(:@balance_service).and_return(balance_service)
    allow(service).to receive(:instance_variable_get).with(:@game_client).and_return(game_client)
    allow(service).to receive(:instance_variable_get).with(:@notification_service).and_return(notification_service)
  end

  describe '#add_player_to_room' do
    context 'when player is joining for the first time' do
      let(:transaction_record) { double('Transaction', transaction_id: 'tx123') }

      before do
        allow(balance_service).to receive(:reserve_balance).and_return(transaction_record)
        allow(game_client).to receive(:join_room)
        allow(notification_service).to receive(:publish_room_update)
      end

      it 'reserves balance and adds player to room' do
        expect(balance_service).to receive(:reserve_balance).with(
          user,
          room.bet_amount,
          room.id.to_s,
          'Room',
          "Bet for room: #{room.name}"
        )

        expect(game_client).to receive(:join_room).with(room.external_room_id, user.id.to_s)

        result = service.add_player_to_room(room, user)

        expect(result[:transaction_record]).to eq(transaction_record)
        expect(result[:is_reconnection]).to be false
      end

      it 'publishes room update notification for new joins' do
        expect(notification_service).to receive(:publish_room_update).with(
          room,
          'player_joined',
          hash_including(
            user_id: user.id.to_s,
            username: user.username
          )
        )

        service.add_player_to_room(room, user)
      end
    end

    context 'when player is reconnecting' do
      let!(:existing_session) do
        room.game_sessions.create!(
          user: user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending',
          metadata: { 'is_ready' => true }
        )
      end

      let(:existing_transaction) { double('Transaction', transaction_id: 'tx456') }

      before do
        room.update!(current_players: 1)
        allow(balance_service).to receive(:find_reservation).and_return(existing_transaction)
        allow(game_client).to receive(:join_room)
      end

      it 'finds existing transaction instead of creating new one' do
        expect(balance_service).to receive(:find_reservation).with(
          user,
          room.id.to_s,
          'Room'
        )

        expect(balance_service).not_to receive(:reserve_balance)

        result = service.add_player_to_room(room, user)

        expect(result[:transaction_record]).to eq(existing_transaction)
        expect(result[:is_reconnection]).to be true
      end

      it 'does not publish room update notification for reconnections' do
        expect(notification_service).not_to receive(:publish_room_update)

        service.add_player_to_room(room, user)
      end

      it 'still notifies game service for reconnections' do
        expect(game_client).to receive(:join_room).with(room.external_room_id, user.id.to_s)

        service.add_player_to_room(room, user)
      end
    end

    context 'when game service notification fails' do
      let(:transaction_record) { double('Transaction', transaction_id: 'tx123') }

      before do
        allow(balance_service).to receive(:reserve_balance).and_return(transaction_record)
        allow(game_client).to receive(:join_room).and_raise(StandardError.new('Game service error'))
      end

      it 'raises error for new joins when game service fails' do
        expect {
          service.add_player_to_room(room, user)
        }.to raise_error(StandardError, 'Game service error')
      end

      it 'does not raise error for reconnections when game service fails' do
        # Create existing session to simulate reconnection
        room.game_sessions.create!(
          user: user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending'
        )
        room.update!(current_players: 1)

        allow(balance_service).to receive(:find_reservation).and_return(transaction_record)

        expect {
          result = service.add_player_to_room(room, user)
          expect(result[:is_reconnection]).to be true
        }.not_to raise_error
      end
    end
  end

  describe '#set_player_ready' do
    let!(:session) do
      room.game_sessions.create!(
        user: user,
        room: room,
        game_type: room.game_type,
        bet_amount: room.bet_amount,
        status: 'pending',
        metadata: { 'is_ready' => false }
      )
    end

    before do
      room.update!(current_players: 1)
      allow(notification_service).to receive(:publish_room_update)
    end

    it 'updates player ready status and publishes notification' do
      expect(notification_service).to receive(:publish_room_update).with(
        room,
        'player_ready_status',
        hash_including(
          user_id: user.id.to_s,
          username: user.username,
          is_ready: true,
          ready_count: 1,
          total_players: 1,
          prize_pool: room.bet_amount
        )
      )

      result = service.set_player_ready(room, user, true)

      expect(result).to eq(session)
      session.reload
      expect(session.metadata['is_ready']).to be true
    end

    it 'updates prize pool when player becomes ready' do
      expect {
        service.set_player_ready(room, user, true)
      }.to change { room.reload.prize_pool }.by(room.bet_amount)
    end

    it 'reduces prize pool when player becomes not ready' do
      # First make player ready
      service.set_player_ready(room, user, true)
      
      expect {
        service.set_player_ready(room, user, false)
      }.to change { room.reload.prize_pool }.by(-room.bet_amount)
    end

    context 'when player is not in room' do
      let(:other_user) { create(:user) }

      it 'raises an error' do
        expect {
          service.set_player_ready(room, other_user, true)
        }.to raise_error(ArgumentError, 'Player not found in room')
      end
    end
  end

  describe 'integration with prize pool logic' do
    let(:user1) { create(:user, balance: 1000) }
    let(:user2) { create(:user, balance: 1000) }
    let(:transaction1) { double('Transaction', transaction_id: 'tx1') }
    let(:transaction2) { double('Transaction', transaction_id: 'tx2') }

    before do
      allow(balance_service).to receive(:reserve_balance).and_return(transaction1, transaction2)
      allow(game_client).to receive(:join_room)
      allow(notification_service).to receive(:publish_room_update)
    end

    it 'correctly manages prize pool with multiple players' do
      # Both players join (no prize pool change)
      service.add_player_to_room(room, user1)
      service.add_player_to_room(room, user2)
      
      room.reload
      expect(room.prize_pool).to eq(0)
      expect(room.current_players).to eq(2)

      # First player becomes ready (prize pool increases)
      service.set_player_ready(room, user1, true)
      room.reload
      expect(room.prize_pool).to eq(room.bet_amount)

      # Second player becomes ready (prize pool increases again)
      service.set_player_ready(room, user2, true)
      room.reload
      expect(room.prize_pool).to eq(room.bet_amount * 2)

      # First player becomes not ready (prize pool decreases)
      service.set_player_ready(room, user1, false)
      room.reload
      expect(room.prize_pool).to eq(room.bet_amount)
    end

    it 'handles reconnection without affecting prize pool' do
      # Player joins and becomes ready
      service.add_player_to_room(room, user1)
      service.set_player_ready(room, user1, true)
      
      initial_prize_pool = room.reload.prize_pool
      expect(initial_prize_pool).to eq(room.bet_amount)

      # Player reconnects (should not change prize pool)
      allow(balance_service).to receive(:find_reservation).and_return(transaction1)
      
      result = service.add_player_to_room(room, user1)
      expect(result[:is_reconnection]).to be true
      
      room.reload
      expect(room.prize_pool).to eq(initial_prize_pool)
      expect(room.current_players).to eq(1) # Should not increment
    end
  end
end
