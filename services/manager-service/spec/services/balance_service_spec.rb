require 'rails_helper'

RSpec.describe BalanceService, type: :service do
  let(:user) { create(:user, balance: 1000.0) }
  let(:service) { described_class.new }

  describe '#reserve_balance' do
    context 'with sufficient balance' do
      it 'reserves balance successfully' do
        reservation_id = service.reserve_balance(user, 100.0, 'game_bet')
        
        expect(reservation_id).to be_present
        expect(reservation_id).to start_with('res_')
        
        # Check that reservation is stored in Redis
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        reservation_data = JSON.parse($redis.get(redis_key))
        expect(reservation_data['amount']).to eq(100.0)
        expect(reservation_data['reason']).to eq('game_bet')
      end

      it 'creates bet_reserved transaction' do
        expect {
          service.reserve_balance(user, 100.0, 'game_bet')
        }.to change { Transaction.count }.by(1)
        
        transaction = Transaction.last
        expect(transaction.transaction_type).to eq('bet_reserved')
        expect(transaction.amount).to eq(-100.0)
        expect(transaction.user).to eq(user)
      end

      it 'updates user balance' do
        service.reserve_balance(user, 100.0, 'game_bet')
        
        user.reload
        expect(user.balance).to eq(900.0)
      end

      it 'sets expiration time for reservation' do
        reservation_id = service.reserve_balance(user, 100.0, 'game_bet')
        
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        ttl = $redis.ttl(redis_key)
        expect(ttl).to be > 0
        expect(ttl).to be <= 3600 # 1 hour default expiration
      end

      it 'allows custom expiration time' do
        reservation_id = service.reserve_balance(user, 100.0, 'game_bet', expires_in: 1800)
        
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        ttl = $redis.ttl(redis_key)
        expect(ttl).to be <= 1800
        expect(ttl).to be > 1700 # Allow some margin for execution time
      end
    end

    context 'with insufficient balance' do
      it 'raises insufficient balance error' do
        expect {
          service.reserve_balance(user, 1500.0, 'game_bet')
        }.to raise_error(BalanceService::InsufficientBalanceError, /Insufficient balance/)
      end

      it 'does not create transaction for insufficient balance' do
        expect {
          begin
            service.reserve_balance(user, 1500.0, 'game_bet')
          rescue BalanceService::InsufficientBalanceError
            # Ignore the error for this test
          end
        }.not_to change { Transaction.count }
      end

      it 'does not change user balance for insufficient balance' do
        original_balance = user.balance
        
        begin
          service.reserve_balance(user, 1500.0, 'game_bet')
        rescue BalanceService::InsufficientBalanceError
          # Ignore the error for this test
        end
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end
    end

    context 'with invalid parameters' do
      it 'raises error for negative amount' do
        expect {
          service.reserve_balance(user, -100.0, 'game_bet')
        }.to raise_error(ArgumentError, /Amount must be positive/)
      end

      it 'raises error for zero amount' do
        expect {
          service.reserve_balance(user, 0.0, 'game_bet')
        }.to raise_error(ArgumentError, /Amount must be positive/)
      end

      it 'raises error for nil user' do
        expect {
          service.reserve_balance(nil, 100.0, 'game_bet')
        }.to raise_error(ArgumentError, /User cannot be nil/)
      end
    end
  end

  describe '#confirm_reservation' do
    let(:reservation_id) { service.reserve_balance(user, 100.0, 'game_bet') }

    context 'with valid reservation' do
      it 'confirms reservation successfully' do
        result = service.confirm_reservation(user, reservation_id, 'bet_placed')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('bet_placed')
        expect(result.amount).to eq(-100.0)
        expect(result.status).to eq('completed')
      end

      it 'removes reservation from Redis' do
        service.confirm_reservation(user, reservation_id, 'bet_placed')
        
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        expect($redis.exists(redis_key)).to eq(0)
      end

      it 'does not change user balance (already reserved)' do
        original_balance = user.reload.balance
        service.confirm_reservation(user, reservation_id, 'bet_placed')
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end

      it 'includes reservation metadata in transaction' do
        result = service.confirm_reservation(user, reservation_id, 'bet_placed')
        
        expect(result.metadata['reservation_id']).to eq(reservation_id)
        expect(result.metadata['original_reason']).to eq('game_bet')
      end
    end

    context 'with non-existent reservation' do
      it 'raises reservation not found error' do
        fake_id = 'res_nonexistent'
        expect {
          service.confirm_reservation(user, fake_id, 'bet_placed')
        }.to raise_error(BalanceService::ReservationNotFoundError, /Reservation not found/)
      end
    end

    context 'with expired reservation' do
      it 'raises reservation expired error' do
        # Manually expire the reservation
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        $redis.del(redis_key)
        
        expect {
          service.confirm_reservation(user, reservation_id, 'bet_placed')
        }.to raise_error(BalanceService::ReservationNotFoundError, /Reservation not found/)
      end
    end
  end

  describe '#cancel_reservation' do
    let(:reservation_id) { service.reserve_balance(user, 100.0, 'game_bet') }

    context 'with valid reservation' do
      it 'cancels reservation successfully' do
        result = service.cancel_reservation(user, reservation_id, 'game_cancelled')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('refund')
        expect(result.amount).to eq(100.0)
        expect(result.status).to eq('completed')
        expect(result.description).to eq('game_cancelled')
      end

      it 'removes reservation from Redis' do
        service.cancel_reservation(user, reservation_id, 'game_cancelled')
        
        redis_key = "balance_reservation:#{user.id}:#{reservation_id}"
        expect($redis.exists(redis_key)).to eq(0)
      end

      it 'restores user balance' do
        original_balance = 1000.0
        service.cancel_reservation(user, reservation_id, 'game_cancelled')
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end

      it 'includes cancellation metadata in transaction' do
        result = service.cancel_reservation(user, reservation_id, 'game_cancelled')
        
        expect(result.metadata['reservation_id']).to eq(reservation_id)
        expect(result.metadata['cancellation_reason']).to eq('game_cancelled')
        expect(result.metadata['original_reason']).to eq('game_bet')
      end
    end

    context 'with non-existent reservation' do
      it 'raises reservation not found error' do
        fake_id = 'res_nonexistent'
        expect {
          service.cancel_reservation(user, fake_id, 'game_cancelled')
        }.to raise_error(BalanceService::ReservationNotFoundError, /Reservation not found/)
      end
    end
  end

  describe '#get_reservation' do
    let(:reservation_id) { service.reserve_balance(user, 100.0, 'game_bet') }

    context 'with valid reservation' do
      it 'returns reservation details' do
        reservation = service.get_reservation(user, reservation_id)
        
        expect(reservation).to be_a(Hash)
        expect(reservation['amount']).to eq(100.0)
        expect(reservation['reason']).to eq('game_bet')
        expect(reservation['user_id']).to eq(user.id.to_s)
        expect(reservation['created_at']).to be_present
      end
    end

    context 'with non-existent reservation' do
      it 'returns nil' do
        fake_id = 'res_nonexistent'
        reservation = service.get_reservation(user, fake_id)
        
        expect(reservation).to be_nil
      end
    end
  end

  describe '#list_user_reservations' do
    before do
      service.reserve_balance(user, 100.0, 'game_bet_1')
      service.reserve_balance(user, 50.0, 'game_bet_2')
      
      # Create reservation for different user
      other_user = create(:user)
      service.reserve_balance(other_user, 75.0, 'other_bet')
    end

    it 'returns all reservations for user' do
      reservations = service.list_user_reservations(user)
      
      expect(reservations).to be_an(Array)
      expect(reservations.length).to eq(2)
      
      amounts = reservations.map { |r| r['amount'] }
      expect(amounts).to contain_exactly(100.0, 50.0)
    end

    it 'does not include other users reservations' do
      reservations = service.list_user_reservations(user)
      
      amounts = reservations.map { |r| r['amount'] }
      expect(amounts).not_to include(75.0)
    end

    it 'returns empty array for user with no reservations' do
      new_user = create(:user)
      reservations = service.list_user_reservations(new_user)
      
      expect(reservations).to eq([])
    end
  end

  describe '#cleanup_expired_reservations' do
    before do
      # Create some reservations
      res1 = service.reserve_balance(user, 100.0, 'game_bet_1')
      res2 = service.reserve_balance(user, 50.0, 'game_bet_2')
      
      # Manually expire one reservation
      redis_key = "balance_reservation:#{user.id}:#{res1}"
      $redis.expire(redis_key, 1)
      sleep(2) # Wait for expiration
    end

    it 'cleans up expired reservations' do
      initial_count = service.list_user_reservations(user).length
      service.cleanup_expired_reservations
      final_count = service.list_user_reservations(user).length
      
      expect(final_count).to be < initial_count
    end
  end

  describe '#get_total_reserved_amount' do
    before do
      service.reserve_balance(user, 100.0, 'game_bet_1')
      service.reserve_balance(user, 50.0, 'game_bet_2')
    end

    it 'returns total reserved amount for user' do
      total = service.get_total_reserved_amount(user)
      expect(total).to eq(150.0)
    end

    it 'returns zero for user with no reservations' do
      new_user = create(:user)
      total = service.get_total_reserved_amount(new_user)
      expect(total).to eq(0.0)
    end
  end

  describe '#get_available_balance' do
    before do
      service.reserve_balance(user, 100.0, 'game_bet')
    end

    it 'returns available balance (total - reserved)' do
      available = service.get_available_balance(user)
      # User had 1000, reserved 100, so available should be 900
      expect(available).to eq(900.0)
    end

    it 'matches current user balance when reservations are active' do
      user.reload
      available = service.get_available_balance(user)
      expect(available).to eq(user.balance)
    end
  end

  describe 'error handling' do
    context 'when Redis is unavailable' do
      before do
        allow($redis).to receive(:setex).and_raise(Redis::CannotConnectError)
      end

      it 'raises service error for reserve_balance' do
        expect {
          service.reserve_balance(user, 100.0, 'game_bet')
        }.to raise_error(BalanceService::ServiceError, /Redis connection error/)
      end
    end

    context 'when database transaction fails' do
      before do
        allow(user).to receive(:update_balance!).and_raise(StandardError.new('DB error'))
      end

      it 'raises service error and cleans up Redis' do
        expect {
          service.reserve_balance(user, 100.0, 'game_bet')
        }.to raise_error(BalanceService::ServiceError, /Transaction failed/)
      end
    end
  end
end
