require 'rails_helper'

RSpec.describe EnhancedSeatManagementService, type: :service do
  let(:service) { described_class.new }
  let(:admin_user) { create(:user, role: 'admin') }
  let(:regular_user) { create(:user) }
  let(:room) { create(:room, current_players: 1) }
  
  # Mock services
  let(:room_service_client) { instance_double(RoomServiceClient) }
  let(:game_service_client) { instance_double(GameServiceClient) }
  let(:notification_service) { instance_double(NotificationService) }

  before do
    allow(RoomServiceClient).to receive(:new).and_return(room_service_client)
    allow(GameServiceClient).to receive(:instance).and_return(game_service_client)
    allow(NotificationService).to receive(:new).and_return(notification_service)
  end

  describe '#kick_player_with_seat_management' do
    let!(:game_session) do
      create(:game_session, 
             room: room, 
             user: regular_user, 
             status: 'pending',
             bet_amount: 100)
    end

    let(:room_service_response) do
      OpenStruct.new(
        success: true,
        room_data: OpenStruct.new(
          players: [
            OpenStruct.new(
              user_id: 'other_user',
              username: 'OtherPlayer',
              position: 0,
              is_ready: true,
              bet_amount: 100
            )
          ]
        ),
        message: 'Player removed and seats reassigned successfully'
      )
    end

    let(:game_service_response) { { 'success' => true } }

    before do
      allow(room_service_client).to receive(:remove_player_with_seat_reassignment)
        .and_return(room_service_response)
      allow(game_service_client).to receive(:leave_room)
        .and_return(game_service_response)
      allow(notification_service).to receive(:publish_room_update)
      allow(notification_service).to receive(:publish_player_kicked)
      allow(notification_service).to receive(:publish_user_notification)
    end

    context 'when player is in the room' do
      it 'successfully kicks the player with seat management' do
        result = service.kick_player_with_seat_management(
          room, 
          regular_user, 
          admin_user, 
          'Inappropriate behavior'
        )

        expect(result[:success]).to be true
        expect(result[:kicked_user][:id]).to eq(regular_user.id.to_s)
        expect(result[:kicked_user][:username]).to eq(regular_user.username)
        expect(result[:reason]).to eq('Inappropriate behavior')
        expect(result[:kicked_by]).to eq(admin_user.username)
        expect(result[:refund_amount]).to eq(100.0)
      end

      it 'removes player from manager service database' do
        expect {
          service.kick_player_with_seat_management(room, regular_user, admin_user)
        }.to change { room.reload.current_players }.from(1).to(0)

        expect(game_session.reload.status).to eq('cancelled')
        expect(game_session.cancellation_reason).to include('Kicked')
      end

      it 'calls room service to remove player with seat reassignment' do
        service.kick_player_with_seat_management(room, regular_user, admin_user)

        expect(room_service_client).to have_received(:remove_player_with_seat_reassignment)
          .with(
            room_id: room.external_room_id,
            user_id: regular_user.id.to_s,
            reassign_seats: true
          )
      end

      it 'calls game service to remove player' do
        service.kick_player_with_seat_management(room, regular_user, admin_user)

        expect(game_service_client).to have_received(:leave_room)
          .with(room.external_room_id, regular_user.id.to_s)
      end

      it 'broadcasts seat updates to room subscribers' do
        service.kick_player_with_seat_management(room, regular_user, admin_user)

        expect(notification_service).to have_received(:publish_room_update)
          .with(room, 'player_kicked_seat_updated', hash_including(
            :kicked_user,
            :reason,
            :kicked_by,
            :updated_seats,
            :seat_reassignment,
            :timestamp
          ))
      end

      it 'publishes kick notification to other services' do
        service.kick_player_with_seat_management(room, regular_user, admin_user, 'Test reason')

        expect(notification_service).to have_received(:publish_player_kicked)
          .with(room, regular_user, 'Test reason', admin_user.username)
      end

      it 'notifies the kicked player directly' do
        service.kick_player_with_seat_management(room, regular_user, admin_user, 'Test reason')

        expect(notification_service).to have_received(:publish_user_notification)
          .with(regular_user, 'player_kicked', hash_including(
            :room_id,
            :room_name,
            :reason,
            :timestamp
          ))
      end
    end

    context 'when player is not in the room' do
      before do
        game_session.update!(status: 'cancelled')
      end

      it 'raises an error' do
        expect {
          service.kick_player_with_seat_management(room, regular_user, admin_user)
        }.to raise_error(ArgumentError, 'User is not in this room')
      end
    end

    context 'when room service fails' do
      before do
        allow(room_service_client).to receive(:remove_player_with_seat_reassignment)
          .and_return(OpenStruct.new(success: false, error: 'Room service error'))
      end

      it 'returns error response' do
        result = service.kick_player_with_seat_management(room, regular_user, admin_user)

        expect(result[:success]).to be false
        expect(result[:error]).to include('Room service error')
      end
    end
  end

  describe '#get_room_with_seat_details' do
    let(:room_service_response) do
      OpenStruct.new(
        success: true,
        room_info: OpenStruct.new(
          room_id: room.external_room_id,
          name: room.name,
          game_type: room.game_type,
          status: room.status,
          current_players: room.current_players,
          max_players: room.max_players,
          players: [
            OpenStruct.new(
              user_id: regular_user.id.to_s,
              username: regular_user.username,
              position: 0,
              is_ready: false,
              bet_amount: 100
            )
          ]
        )
      )
    end

    let(:game_service_response) do
      {
        'room' => {
          'id' => room.external_room_id,
          'status' => 'waiting',
          'current_players' => 1
        }
      }
    end

    before do
      allow(Room).to receive(:find).with(room.id).and_return(room)
      allow(room_service_client).to receive(:get_room_info)
        .and_return(room_service_response)
      allow(game_service_client).to receive(:get_room_status)
        .and_return(game_service_response)
    end

    it 'returns enhanced room data with seat details' do
      result = service.get_room_with_seat_details(room.id)

      expect(result[:success]).to be true
      expect(result[:room]).to include(
        :id,
        :external_room_id,
        :name,
        :game_type,
        :status,
        :seat_details,
        :seat_statistics,
        :game_state
      )
    end

    it 'includes seat statistics' do
      result = service.get_room_with_seat_details(room.id)

      seat_stats = result[:room][:seat_statistics]
      expect(seat_stats).to include(
        :total_seats,
        :occupied_seats,
        :available_seats,
        :used_positions,
        :gaps,
        :occupancy_rate
      )
    end

    it 'calls room service to get room info' do
      service.get_room_with_seat_details(room.id)

      expect(room_service_client).to have_received(:get_room_info)
        .with(room.external_room_id)
    end

    it 'calls game service to get room status' do
      service.get_room_with_seat_details(room.id)

      expect(game_service_client).to have_received(:get_room_status)
        .with(room.external_room_id)
    end

    context 'when room service fails' do
      before do
        allow(room_service_client).to receive(:get_room_info)
          .and_raise(StandardError, 'Room service unavailable')
      end

      it 'returns error response' do
        result = service.get_room_with_seat_details(room.id)

        expect(result[:success]).to be false
        expect(result[:error]).to include('Room service unavailable')
      end
    end
  end

  describe '#reassign_room_seats' do
    let(:new_seat_order) { ['user1', 'user2', 'user3'] }
    
    let(:current_room_state) do
      OpenStruct.new(
        room_info: OpenStruct.new(
          players: [
            OpenStruct.new(user_id: 'user1', position: 2),
            OpenStruct.new(user_id: 'user2', position: 0),
            OpenStruct.new(user_id: 'user3', position: 1)
          ]
        )
      )
    end

    let(:reassignment_result) do
      OpenStruct.new(
        success: true,
        room_data: OpenStruct.new(
          players: [
            OpenStruct.new(user_id: 'user1', position: 0),
            OpenStruct.new(user_id: 'user2', position: 1),
            OpenStruct.new(user_id: 'user3', position: 2)
          ]
        ),
        message: 'Seats reassigned successfully'
      )
    end

    before do
      allow(room_service_client).to receive(:get_room_info)
        .and_return(current_room_state)
      allow(room_service_client).to receive(:reassign_seats)
        .and_return(reassignment_result)
      allow(notification_service).to receive(:publish_room_update)
    end

    it 'successfully reassigns room seats' do
      result = service.reassign_room_seats(room, admin_user, new_seat_order)

      expect(result[:success]).to be true
      expect(result[:message]).to eq('Seats reassigned successfully')
      expect(result[:reassigned_by]).to eq(admin_user.username)
    end

    it 'calls room service to get current room state' do
      service.reassign_room_seats(room, admin_user, new_seat_order)

      expect(room_service_client).to have_received(:get_room_info)
        .with(room.external_room_id)
    end

    it 'calls room service to reassign seats' do
      service.reassign_room_seats(room, admin_user, new_seat_order)

      expect(room_service_client).to have_received(:reassign_seats)
        .with(
          room_id: room.external_room_id,
          new_order: new_seat_order
        )
    end

    it 'broadcasts seat reassignment to room subscribers' do
      service.reassign_room_seats(room, admin_user, new_seat_order)

      expect(notification_service).to have_received(:publish_room_update)
        .with(room, 'seats_reassigned', hash_including(
          :reassigned_by,
          :new_seat_arrangement,
          :timestamp
        ))
    end

    context 'when room service fails' do
      before do
        allow(room_service_client).to receive(:reassign_seats)
          .and_return(OpenStruct.new(success: false, error: 'Reassignment failed'))
      end

      it 'returns error response' do
        result = service.reassign_room_seats(room, admin_user, new_seat_order)

        expect(result[:success]).to be false
        expect(result[:error]).to include('Reassignment failed')
      end
    end
  end

  describe 'private methods' do
    describe '#calculate_seat_statistics' do
      let(:room_service_response) do
        OpenStruct.new(
          room_info: OpenStruct.new(
            max_players: 6,
            players: [
              OpenStruct.new(position: 0),
              OpenStruct.new(position: 2),
              OpenStruct.new(position: 4)
            ]
          )
        )
      end

      it 'calculates correct seat statistics' do
        stats = service.send(:calculate_seat_statistics, room_service_response)

        expect(stats[:total_seats]).to eq(6)
        expect(stats[:occupied_seats]).to eq(3)
        expect(stats[:available_seats]).to eq(3)
        expect(stats[:used_positions]).to eq([0, 2, 4])
        expect(stats[:gaps]).to eq([1, 3, 5])
        expect(stats[:occupancy_rate]).to eq(50.0)
      end
    end

    describe '#build_enhanced_room_data' do
      let(:room_service_response) do
        OpenStruct.new(
          room_info: OpenStruct.new(
            players: [],
            max_players: 6
          )
        )
      end

      let(:game_service_response) do
        { 'room' => { 'status' => 'waiting' } }
      end

      it 'builds comprehensive room data structure' do
        result = service.send(:build_enhanced_room_data, room, room_service_response, game_service_response)

        expect(result).to include(
          :id,
          :external_room_id,
          :name,
          :game_type,
          :status,
          :current_players,
          :max_players,
          :seat_details,
          :seat_statistics,
          :game_state,
          :sessions
        )
      end
    end
  end
end
