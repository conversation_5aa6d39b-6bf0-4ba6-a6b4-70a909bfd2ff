require 'rails_helper'

RSpec.describe RoomManagementService, type: :service do
  let(:creator) { create(:user, :admin) }
  let(:service) { described_class.new }
  let(:game_client) { instance_double(GameServiceClient) }

  before do
    allow(GameServiceClient).to receive(:new).and_return(game_client)
  end

  describe '#create_room' do
    let(:room_params) do
      {
        name: 'Test Room',
        game_type: 'prizewheel',
        max_players: 8,
        bet_amount: 100.0,
        is_private: false
      }
    end

    context 'with valid parameters' do
      before do
        allow(game_client).to receive(:create_room).and_return(
          double(room: double(id: 'external_room_123'))
        )
      end

      it 'creates room successfully' do
        expect {
          service.create_room(creator, room_params)
        }.to change { Room.count }.by(1)
        
        room = Room.last
        expect(room.name).to eq('Test Room')
        expect(room.game_type).to eq('prizewheel')
        expect(room.creator).to eq(creator)
        expect(room.max_players).to eq(8)
        expect(room.bet_amount).to eq(100.0)
      end

      it 'creates room in Game Service' do
        expect(game_client).to receive(:create_room).with(hash_including(
          room_name: 'Test Room',
          game_type: 'prizewheel',
          max_players: 8
        ))
        
        service.create_room(creator, room_params)
      end

      it 'sets external_room_id from Game Service response' do
        room = service.create_room(creator, room_params)
        
        expect(room.external_room_id).to eq('external_room_123')
        expect(room.sync_required).to be false
        expect(room.last_synced_at).to be_present
      end

      it 'sets password when provided' do
        room_params[:password] = 'secret123'
        room = service.create_room(creator, room_params)
        
        expect(room.has_password?).to be true
        expect(room.valid_password?('secret123')).to be true
      end

      it 'sets game-specific configuration' do
        room_params[:game_specific_config] = { 'wheel_segments' => 8 }
        room = service.create_room(creator, room_params)
        
        expect(room.game_specific_config['wheel_segments']).to eq(8)
      end

      it 'logs successful creation' do
        expect(Rails.logger).to receive(:info).with(/Room created successfully/)
        service.create_room(creator, room_params)
      end
    end

    context 'when Game Service creation fails' do
      before do
        allow(game_client).to receive(:create_room).and_raise(StandardError.new('Game Service error'))
      end

      it 'still creates room in database' do
        expect {
          service.create_room(creator, room_params)
        }.to change { Room.count }.by(1)
      end

      it 'marks room for sync' do
        room = service.create_room(creator, room_params)
        
        expect(room.sync_required).to be true
        expect(room.external_room_id).to be_blank
        expect(room.last_synced_at).to be_blank
      end

      it 'schedules retry job' do
        expect(RoomSettingsSyncJob).to receive(:perform_in).with(30.seconds, anything)
        service.create_room(creator, room_params)
      end

      it 'logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to create room in Game Service/)
        service.create_room(creator, room_params)
      end
    end

    context 'with invalid parameters' do
      it 'raises validation error for missing name' do
        room_params.delete(:name)
        
        expect {
          service.create_room(creator, room_params)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'raises validation error for invalid game_type' do
        room_params[:game_type] = 'invalid_game'
        
        expect {
          service.create_room(creator, room_params)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'raises validation error for negative bet_amount' do
        room_params[:bet_amount] = -10.0
        
        expect {
          service.create_room(creator, room_params)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    context 'with nil creator' do
      it 'raises argument error' do
        expect {
          service.create_room(nil, room_params)
        }.to raise_error(ArgumentError, /Creator cannot be nil/)
      end
    end
  end

  describe '#update_room' do
    let(:room) { create(:room, creator: creator) }
    let(:update_params) do
      {
        name: 'Updated Room',
        max_players: 10,
        bet_amount: 200.0
      }
    end

    context 'with valid parameters' do
      before do
        allow(game_client).to receive(:update_room).and_return(true)
      end

      it 'updates room successfully' do
        updated_room = service.update_room(room, update_params)
        
        expect(updated_room.name).to eq('Updated Room')
        expect(updated_room.max_players).to eq(10)
        expect(updated_room.bet_amount).to eq(200.0)
      end

      it 'updates room in Game Service' do
        expect(game_client).to receive(:update_room).with(
          room.external_room_id,
          hash_including(
            room_name: 'Updated Room',
            max_players: 10
          )
        )
        
        service.update_room(room, update_params)
      end

      it 'updates sync timestamp' do
        freeze_time = Time.current
        Timecop.freeze(freeze_time) do
          updated_room = service.update_room(room, update_params)
          expect(updated_room.last_synced_at).to eq(freeze_time)
          expect(updated_room.sync_required).to be false
        end
      end
    end

    context 'when Game Service update fails' do
      before do
        allow(game_client).to receive(:update_room).and_raise(StandardError.new('Game Service error'))
      end

      it 'still updates room in database' do
        updated_room = service.update_room(room, update_params)
        expect(updated_room.name).to eq('Updated Room')
      end

      it 'marks room for sync' do
        updated_room = service.update_room(room, update_params)
        expect(updated_room.sync_required).to be true
      end

      it 'schedules retry job' do
        expect(RoomSettingsSyncJob).to receive(:perform_in).with(30.seconds, room.id.to_s)
        service.update_room(room, update_params)
      end
    end
  end

  describe '#delete_room' do
    let(:room) { create(:room, creator: creator, external_room_id: 'external_123') }

    context 'when room can be deleted' do
      before do
        allow(game_client).to receive(:delete_room).and_return(true)
      end

      it 'deletes room from database' do
        expect {
          service.delete_room(room)
        }.to change { Room.count }.by(-1)
      end

      it 'deletes room from Game Service' do
        expect(game_client).to receive(:delete_room).with('external_123')
        service.delete_room(room)
      end

      it 'logs successful deletion' do
        expect(Rails.logger).to receive(:info).with(/Room deleted successfully/)
        service.delete_room(room)
      end
    end

    context 'when Game Service deletion fails' do
      before do
        allow(game_client).to receive(:delete_room).and_raise(StandardError.new('Game Service error'))
      end

      it 'still deletes room from database' do
        expect {
          service.delete_room(room)
        }.to change { Room.count }.by(-1)
      end

      it 'logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to delete room from Game Service/)
        service.delete_room(room)
      end
    end

    context 'when room has active players' do
      before do
        room.update!(current_players: 3, status: 'playing')
      end

      it 'raises error for room with active players' do
        expect {
          service.delete_room(room)
        }.to raise_error(RoomManagementService::RoomNotDeletableError, /Cannot delete room with active players/)
      end
    end
  end

  describe '#sync_room_with_game_service' do
    let(:room) { create(:room, :needs_sync, external_room_id: 'external_123') }

    context 'when sync succeeds' do
      before do
        allow(game_client).to receive(:update_room).and_return(true)
      end

      it 'syncs room successfully' do
        result = service.sync_room_with_game_service(room)
        
        expect(result).to be true
        room.reload
        expect(room.sync_required).to be false
        expect(room.last_synced_at).to be_present
      end

      it 'calls Game Service with correct parameters' do
        expect(game_client).to receive(:update_room).with(
          'external_123',
          hash_including(
            room_name: room.name,
            game_type: room.game_type,
            max_players: room.max_players
          )
        )
        
        service.sync_room_with_game_service(room)
      end
    end

    context 'when sync fails' do
      before do
        allow(game_client).to receive(:update_room).and_raise(StandardError.new('Sync error'))
      end

      it 'returns false and logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to sync room/)
        result = service.sync_room_with_game_service(room)
        
        expect(result).to be false
        room.reload
        expect(room.sync_required).to be true
      end
    end

    context 'when room has no external_room_id' do
      before do
        room.update!(external_room_id: nil)
        allow(game_client).to receive(:create_room).and_return(
          double(room: double(id: 'new_external_123'))
        )
      end

      it 'creates room in Game Service' do
        expect(game_client).to receive(:create_room)
        service.sync_room_with_game_service(room)
        
        room.reload
        expect(room.external_room_id).to eq('new_external_123')
      end
    end
  end

  describe '#get_room_status' do
    let(:room) { create(:room, external_room_id: 'external_123') }

    context 'when Game Service responds' do
      let(:game_service_status) do
        {
          'status' => 'playing',
          'current_players' => 6,
          'prize_pool' => 600.0
        }
      end

      before do
        allow(game_client).to receive(:get_room_status).and_return(game_service_status)
      end

      it 'returns room status from Game Service' do
        status = service.get_room_status(room)
        
        expect(status['status']).to eq('playing')
        expect(status['current_players']).to eq(6)
        expect(status['prize_pool']).to eq(600.0)
      end

      it 'updates local room data' do
        service.get_room_status(room)
        
        room.reload
        expect(room.status).to eq('playing')
        expect(room.current_players).to eq(6)
        expect(room.prize_pool).to eq(600.0)
      end
    end

    context 'when Game Service is unavailable' do
      before do
        allow(game_client).to receive(:get_room_status).and_raise(StandardError.new('Service unavailable'))
      end

      it 'returns local room data' do
        status = service.get_room_status(room)
        
        expect(status['status']).to eq(room.status)
        expect(status['current_players']).to eq(room.current_players)
      end

      it 'logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to get room status from Game Service/)
        service.get_room_status(room)
      end
    end
  end

  describe 'private methods' do
    describe '#build_grpc_room_params' do
      let(:room) { create(:room, name: 'Test Room', game_type: 'prizewheel') }

      it 'builds correct gRPC parameters' do
        params = service.send(:build_grpc_room_params, room)
        
        expect(params[:room_name]).to eq('Test Room')
        expect(params[:game_type]).to eq('prizewheel')
        expect(params[:max_players]).to eq(room.max_players)
        expect(params[:bet_amount]).to eq(room.bet_amount.to_f)
        expect(params[:is_private]).to eq(room.is_private)
      end

      it 'includes password hash for private rooms' do
        private_room = create(:room, :private_room)
        params = service.send(:build_grpc_room_params, private_room)
        
        expect(params[:password_hash]).to be_present
      end

      it 'includes game-specific configuration' do
        room.game_specific_config = { 'wheel_segments' => 8 }
        params = service.send(:build_grpc_room_params, room)
        
        expect(params[:game_config]['wheel_segments']).to eq(8)
      end
    end

    describe '#validate_creator!' do
      it 'passes for valid admin user' do
        expect {
          service.send(:validate_creator!, creator)
        }.not_to raise_error
      end

      it 'raises error for nil creator' do
        expect {
          service.send(:validate_creator!, nil)
        }.to raise_error(ArgumentError, /Creator cannot be nil/)
      end

      it 'raises error for non-admin user' do
        regular_user = create(:user)
        expect {
          service.send(:validate_creator!, regular_user)
        }.to raise_error(ArgumentError, /Creator must be an admin/)
      end
    end

    describe '#validate_room_params!' do
      let(:valid_params) do
        {
          name: 'Test Room',
          game_type: 'prizewheel',
          max_players: 8,
          bet_amount: 100.0
        }
      end

      it 'passes for valid parameters' do
        expect {
          service.send(:validate_room_params!, valid_params)
        }.not_to raise_error
      end

      it 'raises error for missing required fields' do
        valid_params.delete(:name)
        expect {
          service.send(:validate_room_params!, valid_params)
        }.to raise_error(ArgumentError, /Missing required parameter: name/)
      end

      it 'raises error for invalid game_type' do
        valid_params[:game_type] = 'invalid'
        expect {
          service.send(:validate_room_params!, valid_params)
        }.to raise_error(ArgumentError, /Invalid game_type/)
      end
    end
  end
end
