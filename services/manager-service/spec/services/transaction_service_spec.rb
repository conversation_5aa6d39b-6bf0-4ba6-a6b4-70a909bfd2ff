require 'rails_helper'

RSpec.describe TransactionService, type: :service do
  let(:user) { create(:user, balance: 1000.0) }
  let(:service) { described_class.new }

  describe '#process_deposit' do
    context 'with valid parameters' do
      it 'processes deposit successfully' do
        result = service.process_deposit(user, 100.0, 'Test deposit')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('deposit')
        expect(result.amount).to eq(100.0)
        expect(result.status).to eq('completed')
        expect(result.description).to eq('Test deposit')
        
        user.reload
        expect(user.balance).to eq(1100.0)
      end

      it 'creates transaction with correct balance tracking' do
        result = service.process_deposit(user, 100.0)
        
        expect(result.balance_before).to eq(1000.0)
        expect(result.balance_after).to eq(1100.0)
      end

      it 'works with metadata' do
        metadata = { 'source' => 'api', 'ip' => '***********' }
        result = service.process_deposit(user, 100.0, 'Test deposit', metadata)
        
        expect(result.metadata).to include(metadata)
      end

      it 'notifies other services' do
        expect(service).to receive(:notify_balance_update).with(user, instance_of(Transaction))
        service.process_deposit(user, 100.0)
      end

      it 'logs successful deposit' do
        expect(Rails.logger).to receive(:info).with(/Deposit processed successfully/)
        service.process_deposit(user, 100.0)
      end
    end

    context 'with invalid parameters' do
      it 'raises error for negative amount' do
        expect {
          service.process_deposit(user, -100.0)
        }.to raise_error(/Amount must be positive/)
      end

      it 'raises error for zero amount' do
        expect {
          service.process_deposit(user, 0.0)
        }.to raise_error(/Amount must be positive/)
      end

      it 'raises error for nil user' do
        expect {
          service.process_deposit(nil, 100.0)
        }.to raise_error(/User cannot be nil/)
      end

      it 'raises error for invalid user' do
        invalid_user = User.new
        expect {
          service.process_deposit(invalid_user, 100.0)
        }.to raise_error(/User must be persisted/)
      end
    end

    context 'when transaction fails' do
      before do
        allow(user).to receive(:update_balance!).and_raise(StandardError.new('Database error'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Deposit processing failed/)
        
        expect {
          service.process_deposit(user, 100.0)
        }.to raise_error('Database error')
      end
    end
  end

  describe '#process_withdrawal' do
    context 'with valid parameters and sufficient balance' do
      it 'processes withdrawal successfully' do
        result = service.process_withdrawal(user, 100.0, 'Test withdrawal')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('withdrawal')
        expect(result.amount).to eq(-100.0)
        expect(result.status).to eq('completed')
        expect(result.description).to eq('Test withdrawal')
        
        user.reload
        expect(user.balance).to eq(900.0)
      end

      it 'creates transaction with correct balance tracking' do
        result = service.process_withdrawal(user, 100.0)
        
        expect(result.balance_before).to eq(1000.0)
        expect(result.balance_after).to eq(900.0)
      end

      it 'works with metadata' do
        metadata = { 'method' => 'bank_transfer', 'account' => '****1234' }
        result = service.process_withdrawal(user, 100.0, 'Test withdrawal', metadata)
        
        expect(result.metadata).to include(metadata)
      end

      it 'notifies other services' do
        expect(service).to receive(:notify_balance_update).with(user, instance_of(Transaction))
        service.process_withdrawal(user, 100.0)
      end
    end

    context 'with insufficient balance' do
      it 'raises insufficient balance error' do
        expect {
          service.process_withdrawal(user, 1500.0)
        }.to raise_error(/Insufficient balance/)
      end

      it 'does not create transaction for insufficient balance' do
        expect {
          begin
            service.process_withdrawal(user, 1500.0)
          rescue => e
            # Ignore the error for this test
          end
        }.not_to change { Transaction.count }
      end

      it 'does not change user balance for insufficient balance' do
        original_balance = user.balance
        
        begin
          service.process_withdrawal(user, 1500.0)
        rescue => e
          # Ignore the error for this test
        end
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end
    end

    context 'with invalid parameters' do
      it 'raises error for negative amount' do
        expect {
          service.process_withdrawal(user, -100.0)
        }.to raise_error(/Amount must be positive/)
      end

      it 'raises error for zero amount' do
        expect {
          service.process_withdrawal(user, 0.0)
        }.to raise_error(/Amount must be positive/)
      end
    end
  end

  describe '#process_bet_placement' do
    let(:game_session) { create(:game_session, user: user, bet_amount: 50.0) }

    context 'with valid parameters and sufficient balance' do
      it 'processes bet placement successfully' do
        result = service.process_bet_placement(user, game_session, 50.0)
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('bet_placed')
        expect(result.amount).to eq(-50.0)
        expect(result.status).to eq('completed')
        expect(result.reference_id).to eq(game_session.id.to_s)
        expect(result.reference_type).to eq('GameSession')
        
        user.reload
        expect(user.balance).to eq(950.0)
      end

      it 'includes game session information in metadata' do
        result = service.process_bet_placement(user, game_session, 50.0)
        
        expect(result.metadata['game_type']).to eq(game_session.game_type)
        expect(result.metadata['session_id']).to eq(game_session.session_id)
      end
    end

    context 'with insufficient balance' do
      it 'raises insufficient balance error' do
        expect {
          service.process_bet_placement(user, game_session, 1500.0)
        }.to raise_error(/Insufficient balance/)
      end
    end
  end

  describe '#process_bet_win' do
    let(:game_session) { create(:game_session, user: user, bet_amount: 50.0, win_amount: 150.0) }

    it 'processes bet win successfully' do
      result = service.process_bet_win(user, game_session, 150.0)
      
      expect(result).to be_a(Transaction)
      expect(result.transaction_type).to eq('bet_won')
      expect(result.amount).to eq(150.0)
      expect(result.status).to eq('completed')
      expect(result.reference_id).to eq(game_session.id.to_s)
      expect(result.reference_type).to eq('GameSession')
      
      user.reload
      expect(user.balance).to eq(1150.0)
    end

    it 'includes win information in metadata' do
      result = service.process_bet_win(user, game_session, 150.0)
      
      expect(result.metadata['game_type']).to eq(game_session.game_type)
      expect(result.metadata['session_id']).to eq(game_session.session_id)
      expect(result.metadata['bet_amount']).to eq(game_session.bet_amount)
    end
  end

  describe '#process_refund' do
    let(:original_transaction) { create(:transaction, :bet_placed, user: user, amount: -50.0) }

    it 'processes refund successfully' do
      result = service.process_refund(user, 50.0, 'Game cancelled', original_transaction.id.to_s)
      
      expect(result).to be_a(Transaction)
      expect(result.transaction_type).to eq('refund')
      expect(result.amount).to eq(50.0)
      expect(result.status).to eq('completed')
      expect(result.description).to eq('Game cancelled')
      expect(result.reference_id).to eq(original_transaction.id.to_s)
      
      user.reload
      expect(user.balance).to eq(1050.0)
    end

    it 'includes refund reason in metadata' do
      result = service.process_refund(user, 50.0, 'Game cancelled', original_transaction.id.to_s)
      
      expect(result.metadata['refund_reason']).to eq('Game cancelled')
      expect(result.metadata['original_transaction_id']).to eq(original_transaction.id.to_s)
    end
  end

  describe '#process_adjustment' do
    context 'with positive adjustment' do
      it 'processes positive adjustment successfully' do
        result = service.process_adjustment(user, 25.0, 'Balance correction')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('adjustment')
        expect(result.amount).to eq(25.0)
        expect(result.status).to eq('completed')
        expect(result.description).to eq('Balance correction')
        
        user.reload
        expect(user.balance).to eq(1025.0)
      end
    end

    context 'with negative adjustment' do
      it 'processes negative adjustment successfully' do
        result = service.process_adjustment(user, -25.0, 'Balance correction')
        
        expect(result).to be_a(Transaction)
        expect(result.transaction_type).to eq('adjustment')
        expect(result.amount).to eq(-25.0)
        expect(result.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(975.0)
      end
    end

    context 'with negative adjustment exceeding balance' do
      it 'raises insufficient balance error' do
        expect {
          service.process_adjustment(user, -1500.0, 'Large negative adjustment')
        }.to raise_error(/Insufficient balance/)
      end
    end
  end

  describe 'private methods' do
    describe '#validate_amount!' do
      it 'passes for positive amounts when positive required' do
        expect {
          service.send(:validate_amount!, 100.0, positive: true)
        }.not_to raise_error
      end

      it 'raises error for negative amounts when positive required' do
        expect {
          service.send(:validate_amount!, -100.0, positive: true)
        }.to raise_error(/Amount must be positive/)
      end

      it 'raises error for zero amounts when positive required' do
        expect {
          service.send(:validate_amount!, 0.0, positive: true)
        }.to raise_error(/Amount must be positive/)
      end

      it 'passes for negative amounts when positive not required' do
        expect {
          service.send(:validate_amount!, -100.0, positive: false)
        }.not_to raise_error
      end
    end

    describe '#validate_user!' do
      it 'passes for valid persisted user' do
        expect {
          service.send(:validate_user!, user)
        }.not_to raise_error
      end

      it 'raises error for nil user' do
        expect {
          service.send(:validate_user!, nil)
        }.to raise_error(/User cannot be nil/)
      end

      it 'raises error for non-persisted user' do
        new_user = User.new
        expect {
          service.send(:validate_user!, new_user)
        }.to raise_error(/User must be persisted/)
      end
    end

    describe '#notify_balance_update' do
      let(:transaction) { create(:transaction, user: user) }

      it 'sends notification via NotificationService' do
        notification_service = instance_double(NotificationService)
        expect(NotificationService).to receive(:new).and_return(notification_service)
        expect(notification_service).to receive(:notify_balance_update).with(user, transaction)
        
        service.send(:notify_balance_update, user, transaction)
      end
    end
  end
end
