require 'rails_helper'

RSpec.describe RoomManagementService, type: :service do
  let(:service) { described_class.instance }
  let(:admin_user) { create(:user, role: 'admin') }
  let(:regular_user) { create(:user, role: 'player') }
  let(:room) { create(:room, :waiting, max_players: 4, bet_amount: 100) }
  let(:notification_service) { instance_double(NotificationService) }

  before do
    allow(NotificationService).to receive(:instance).and_return(notification_service)
    allow(notification_service).to receive(:publish_player_kicked)
    allow(notification_service).to receive(:notify_kicked_from_room)
    allow(notification_service).to receive(:publish_room_update)
  end

  describe '#kick_player_from_room' do
    context 'when player is in the room' do
      let!(:game_session) do
        create(:game_session, 
               room: room, 
               user: regular_user, 
               status: 'pending',
               bet_amount: 100)
      end

      before do
        room.update!(current_players: 1)
      end

      it 'successfully kicks the player' do
        result = service.kick_player_from_room(
          room, 
          regular_user, 
          reason: 'Inappropriate behavior',
          kicked_by_user: admin_user
        )

        expect(result[:success]).to be true
        expect(result[:kicked_user][:id]).to eq(regular_user.id.to_s)
        expect(result[:kicked_user][:username]).to eq(regular_user.username)
        expect(result[:reason]).to eq('Inappropriate behavior')
        expect(result[:kicked_by]).to eq(admin_user.username)
        expect(result[:refund_amount]).to eq(100.0)
      end

      it 'removes player from room' do
        expect {
          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        }.to change { room.reload.current_players }.from(1).to(0)
      end

      it 'cancels the game session' do
        service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        
        expect(game_session.reload.status).to eq('cancelled')
        expect(game_session.cancellation_reason).to include('Kicked by administrator')
      end

      it 'publishes kick notification to other services' do
        service.kick_player_from_room(
          room, 
          regular_user, 
          reason: 'Test reason',
          kicked_by_user: admin_user
        )

        expect(notification_service).to have_received(:publish_player_kicked)
          .with(room, regular_user, 'Test reason', admin_user.username)
      end

      it 'notifies the kicked player' do
        service.kick_player_from_room(
          room, 
          regular_user, 
          reason: 'Test reason',
          kicked_by_user: admin_user
        )

        expect(notification_service).to have_received(:notify_kicked_from_room)
          .with(regular_user, room, 'Test reason')
      end

      it 'logs audit information' do
        expect(Rails.logger).to receive(:info).with(
          a_string_matching(/Kick player audit log/)
        )

        service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
      end

      context 'when notification fails' do
        before do
          allow(notification_service).to receive(:publish_player_kicked)
            .and_raise(StandardError.new('Redis connection failed'))
        end

        it 'still completes the kick operation' do
          expect {
            result = service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
            expect(result[:success]).to be true
          }.not_to raise_error
        end

        it 'logs the notification error' do
          expect(Rails.logger).to receive(:error).with(
            a_string_matching(/Failed to publish kick notification/)
          )

          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        end
      end
    end

    context 'when player is not in the room' do
      it 'raises an ArgumentError' do
        expect {
          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        }.to raise_error(ArgumentError, 'User is not in this room')
      end
    end

    context 'when player has already left' do
      let!(:game_session) do
        create(:game_session, 
               room: room, 
               user: regular_user, 
               status: 'cancelled')
      end

      it 'raises an ArgumentError' do
        expect {
          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        }.to raise_error(ArgumentError, 'User is not in this room')
      end
    end

    context 'with default parameters' do
      let!(:game_session) do
        create(:game_session, 
               room: room, 
               user: regular_user, 
               status: 'pending',
               bet_amount: 100)
      end

      before do
        room.update!(current_players: 1)
      end

      it 'uses default reason when none provided' do
        result = service.kick_player_from_room(room, regular_user)

        expect(result[:reason]).to eq('Kicked by administrator')
        expect(result[:kicked_by]).to eq('System')
      end
    end

    context 'when room removal fails' do
      let!(:game_session) do
        create(:game_session, 
               room: room, 
               user: regular_user, 
               status: 'pending',
               bet_amount: 100)
      end

      before do
        room.update!(current_players: 1)
        allow(room).to receive(:remove_player!).and_raise(StandardError.new('Database error'))
      end

      it 'propagates the error' do
        expect {
          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        }.to raise_error(StandardError, 'Database error')
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(
          a_string_matching(/Failed to kick player/)
        )

        expect {
          service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
        }.to raise_error(StandardError)
      end
    end
  end

  describe 'integration with existing remove_player_from_room' do
    let!(:game_session) do
      create(:game_session, 
             room: room, 
             user: regular_user, 
             status: 'pending',
             bet_amount: 100)
    end

    before do
      room.update!(current_players: 1)
    end

    it 'kick_player_from_room uses the same underlying removal logic' do
      # Both methods should result in the same final state
      expect {
        service.kick_player_from_room(room, regular_user, kicked_by_user: admin_user)
      }.to change { room.reload.current_players }.from(1).to(0)
        .and change { game_session.reload.status }.from('pending').to('cancelled')
    end
  end
end
