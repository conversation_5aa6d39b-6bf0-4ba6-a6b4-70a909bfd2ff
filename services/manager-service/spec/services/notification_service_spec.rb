require 'rails_helper'

RSpec.describe NotificationService, type: :service do
  let(:user) { create(:user) }
  let(:service) { described_class.new }

  before do
    # Mock Redis for testing
    allow($redis).to receive(:publish)
  end

  describe '#notify_balance_update' do
    let(:transaction) { create(:transaction, :deposit, user: user, amount: 100.0) }

    it 'publishes balance update notification' do
      expect($redis).to receive(:publish).with(
        'balance_updates',
        hash_including(
          'type' => 'balance_updated',
          'user_id' => user.id.to_s,
          'new_balance' => user.balance.to_s,
          'transaction_id' => transaction.id.to_s
        ).to_json
      )

      service.notify_balance_update(user, transaction)
    end

    it 'includes transaction details in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['transaction']['type']).to eq('deposit')
        expect(data['transaction']['amount']).to eq('100.0')
        expect(data['transaction']['description']).to eq(transaction.description)
      end

      service.notify_balance_update(user, transaction)
    end

    it 'logs notification sending' do
      expect(Rails.logger).to receive(:info).with(/Balance update notification sent/)
      service.notify_balance_update(user, transaction)
    end

    context 'when Redis fails' do
      before do
        allow($redis).to receive(:publish).and_raise(Redis::CannotConnectError)
      end

      it 'logs error and does not raise exception' do
        expect(Rails.logger).to receive(:error).with(/Failed to send balance update notification/)
        
        expect {
          service.notify_balance_update(user, transaction)
        }.not_to raise_error
      end
    end
  end

  describe '#notify_transaction_completed' do
    let(:transaction) { create(:transaction, :completed, user: user) }

    it 'publishes transaction completed notification' do
      expect($redis).to receive(:publish).with(
        'transaction_updates',
        hash_including(
          'type' => 'transaction_completed',
          'user_id' => user.id.to_s,
          'transaction_id' => transaction.id.to_s
        ).to_json
      )

      service.notify_transaction_completed(transaction)
    end

    it 'includes transaction status in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['transaction']['status']).to eq('completed')
        expect(data['transaction']['processed_at']).to be_present
      end

      service.notify_transaction_completed(transaction)
    end
  end

  describe '#notify_game_session_update' do
    let(:game_session) { create(:game_session, :active, user: user) }

    it 'publishes game session update notification' do
      expect($redis).to receive(:publish).with(
        'game_session_updates',
        hash_including(
          'type' => 'game_session_updated',
          'user_id' => user.id.to_s,
          'session_id' => game_session.id.to_s
        ).to_json
      )

      service.notify_game_session_update(game_session)
    end

    it 'includes session details in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['session']['status']).to eq('active')
        expect(data['session']['game_type']).to eq(game_session.game_type)
        expect(data['session']['bet_amount']).to eq(game_session.bet_amount.to_s)
      end

      service.notify_game_session_update(game_session)
    end
  end

  describe '#notify_room_update' do
    let(:room) { create(:room, creator: user) }

    it 'publishes room update notification' do
      expect($redis).to receive(:publish).with(
        'room_updates',
        hash_including(
          'type' => 'room_updated',
          'room_id' => room.id.to_s,
          'creator_id' => user.id.to_s
        ).to_json
      )

      service.notify_room_update(room)
    end

    it 'includes room details in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['room']['name']).to eq(room.name)
        expect(data['room']['status']).to eq(room.status)
        expect(data['room']['current_players']).to eq(room.current_players)
        expect(data['room']['max_players']).to eq(room.max_players)
      end

      service.notify_room_update(room)
    end
  end

  describe '#notify_user_status_change' do
    context 'when user status changes' do
      it 'publishes user status change notification' do
        expect($redis).to receive(:publish).with(
          'user_updates',
          hash_including(
            'type' => 'user_status_changed',
            'user_id' => user.id.to_s,
            'old_status' => 'active',
            'new_status' => 'suspended'
          ).to_json
        )

        service.notify_user_status_change(user, 'active', 'suspended')
      end

      it 'includes user details in notification' do
        expect($redis).to receive(:publish) do |channel, message|
          data = JSON.parse(message)
          expect(data['user']['username']).to eq(user.username)
          expect(data['user']['role']).to eq(user.role)
        end

        service.notify_user_status_change(user, 'active', 'inactive')
      end
    end
  end

  describe '#notify_system_alert' do
    let(:alert_data) do
      {
        level: 'warning',
        message: 'High server load detected',
        details: { cpu_usage: 85, memory_usage: 78 }
      }
    end

    it 'publishes system alert notification' do
      expect($redis).to receive(:publish).with(
        'system_alerts',
        hash_including(
          'type' => 'system_alert',
          'level' => 'warning',
          'message' => 'High server load detected'
        ).to_json
      )

      service.notify_system_alert(alert_data)
    end

    it 'includes alert details in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['details']['cpu_usage']).to eq(85)
        expect(data['details']['memory_usage']).to eq(78)
        expect(data['timestamp']).to be_present
      end

      service.notify_system_alert(alert_data)
    end
  end

  describe '#notify_admin_action' do
    let(:admin) { create(:user, :admin) }
    let(:target_user) { create(:user) }

    it 'publishes admin action notification' do
      expect($redis).to receive(:publish).with(
        'admin_actions',
        hash_including(
          'type' => 'admin_action',
          'admin_id' => admin.id.to_s,
          'action' => 'user_suspended',
          'target_user_id' => target_user.id.to_s
        ).to_json
      )

      service.notify_admin_action(admin, 'user_suspended', target_user.id.to_s, 'Violation of terms')
    end

    it 'includes action details in notification' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['reason']).to eq('Violation of terms')
        expect(data['admin']['username']).to eq(admin.username)
        expect(data['timestamp']).to be_present
      end

      service.notify_admin_action(admin, 'user_suspended', target_user.id.to_s, 'Violation of terms')
    end
  end

  describe 'error handling' do
    context 'when Redis connection fails' do
      before do
        allow($redis).to receive(:publish).and_raise(Redis::CannotConnectError.new('Connection failed'))
      end

      it 'logs error for balance update notification' do
        transaction = create(:transaction, user: user)
        expect(Rails.logger).to receive(:error).with(/Failed to send balance update notification/)
        
        service.notify_balance_update(user, transaction)
      end

      it 'logs error for transaction notification' do
        transaction = create(:transaction, user: user)
        expect(Rails.logger).to receive(:error).with(/Failed to send transaction completed notification/)
        
        service.notify_transaction_completed(transaction)
      end

      it 'logs error for game session notification' do
        game_session = create(:game_session, user: user)
        expect(Rails.logger).to receive(:error).with(/Failed to send game session update notification/)
        
        service.notify_game_session_update(game_session)
      end
    end

    context 'when JSON serialization fails' do
      before do
        allow(JSON).to receive(:generate).and_raise(JSON::GeneratorError.new('Invalid JSON'))
      end

      it 'logs error and does not raise exception' do
        transaction = create(:transaction, user: user)
        expect(Rails.logger).to receive(:error).with(/Failed to serialize notification data/)
        
        expect {
          service.notify_balance_update(user, transaction)
        }.not_to raise_error
      end
    end
  end

  describe 'notification formatting' do
    let(:transaction) { create(:transaction, :deposit, user: user, amount: 100.0) }

    it 'formats timestamps correctly' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['timestamp']).to match(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      end

      service.notify_balance_update(user, transaction)
    end

    it 'includes service metadata' do
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data['service']).to eq('manager-service')
        expect(data['version']).to be_present
      end

      service.notify_balance_update(user, transaction)
    end

    it 'sanitizes sensitive data' do
      user.update!(password_digest: 'sensitive_hash')
      
      expect($redis).to receive(:publish) do |channel, message|
        data = JSON.parse(message)
        expect(data.to_s).not_to include('password_digest')
        expect(data.to_s).not_to include('sensitive_hash')
      end

      service.notify_balance_update(user, transaction)
    end
  end

  describe 'channel routing' do
    it 'uses correct channels for different notification types' do
      transaction = create(:transaction, user: user)
      game_session = create(:game_session, user: user)
      room = create(:room, creator: user)

      expect($redis).to receive(:publish).with('balance_updates', anything)
      service.notify_balance_update(user, transaction)

      expect($redis).to receive(:publish).with('transaction_updates', anything)
      service.notify_transaction_completed(transaction)

      expect($redis).to receive(:publish).with('game_session_updates', anything)
      service.notify_game_session_update(game_session)

      expect($redis).to receive(:publish).with('room_updates', anything)
      service.notify_room_update(room)

      expect($redis).to receive(:publish).with('user_updates', anything)
      service.notify_user_status_change(user, 'active', 'inactive')

      expect($redis).to receive(:publish).with('system_alerts', anything)
      service.notify_system_alert({ level: 'info', message: 'Test alert' })
    end
  end
end
