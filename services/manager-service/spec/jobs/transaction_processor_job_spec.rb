require 'rails_helper'

RSpec.describe TransactionProcessorJob, type: :job do
  let(:user) { create(:user, balance: 1000.0) }
  let(:transaction) { create(:transaction, :pending, user: user, amount: 100.0) }

  describe '#perform' do
    context 'with valid pending transaction' do
      it 'processes transaction successfully' do
        described_class.new.perform(transaction.id.to_s)
        
        transaction.reload
        expect(transaction.status).to eq('completed')
        expect(transaction.processed_at).to be_present
      end

      it 'updates user balance for deposit' do
        deposit_transaction = create(:transaction, :deposit, :pending, user: user, amount: 100.0)
        
        expect {
          described_class.new.perform(deposit_transaction.id.to_s)
          user.reload
        }.to change { user.balance }.by(100.0)
      end

      it 'updates user balance for withdrawal' do
        withdrawal_transaction = create(:transaction, :withdrawal, :pending, user: user, amount: -50.0)
        
        expect {
          described_class.new.perform(withdrawal_transaction.id.to_s)
          user.reload
        }.to change { user.balance }.by(-50.0)
      end

      it 'logs successful processing' do
        expect(Rails.logger).to receive(:info).with(/Transaction processed successfully/)
        described_class.new.perform(transaction.id.to_s)
      end

      it 'sends notification after processing' do
        notification_service = instance_double(NotificationService)
        expect(NotificationService).to receive(:new).and_return(notification_service)
        expect(notification_service).to receive(:notify_transaction_completed).with(transaction)
        
        described_class.new.perform(transaction.id.to_s)
      end
    end

    context 'with non-existent transaction' do
      it 'logs error and does not raise exception' do
        fake_id = BSON::ObjectId.new.to_s
        expect(Rails.logger).to receive(:error).with(/Transaction not found/)
        
        expect {
          described_class.new.perform(fake_id)
        }.not_to raise_error
      end
    end

    context 'with already processed transaction' do
      let(:completed_transaction) { create(:transaction, :deposit, status: 'completed', user: user) }

      it 'skips processing and logs warning' do
        expect(Rails.logger).to receive(:warn).with(/Transaction already processed/)
        
        described_class.new.perform(completed_transaction.id.to_s)
        
        # Should not change anything
        completed_transaction.reload
        expect(completed_transaction.status).to eq('completed')
      end
    end

    context 'with failed transaction' do
      let(:failed_transaction) { create(:transaction, :failed, user: user) }

      it 'skips processing and logs warning' do
        expect(Rails.logger).to receive(:warn).with(/Transaction already processed/)
        
        described_class.new.perform(failed_transaction.id.to_s)
        
        failed_transaction.reload
        expect(failed_transaction.status).to eq('failed')
      end
    end

    context 'when balance update fails' do
      before do
        allow_any_instance_of(User).to receive(:update_balance!).and_raise(StandardError.new('Database error'))
      end

      it 'marks transaction as failed' do
        described_class.new.perform(transaction.id.to_s)
        
        transaction.reload
        expect(transaction.status).to eq('failed')
        expect(transaction.metadata['failure_reason']).to eq('Database error')
      end

      it 'logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to process transaction/)
        described_class.new.perform(transaction.id.to_s)
      end

      it 'does not update user balance' do
        original_balance = user.balance
        described_class.new.perform(transaction.id.to_s)
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end
    end

    context 'with insufficient balance for withdrawal' do
      let(:large_withdrawal) { create(:transaction, :withdrawal, :pending, user: user, amount: -2000.0) }

      it 'marks transaction as failed' do
        described_class.new.perform(large_withdrawal.id.to_s)
        
        large_withdrawal.reload
        expect(large_withdrawal.status).to eq('failed')
        expect(large_withdrawal.metadata['failure_reason']).to include('Insufficient balance')
      end

      it 'does not update user balance' do
        original_balance = user.balance
        described_class.new.perform(large_withdrawal.id.to_s)
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end
    end

    context 'with bet placement transaction' do
      let(:bet_transaction) { create(:transaction, :bet_placed, :pending, user: user, amount: -25.0) }

      it 'processes bet placement successfully' do
        described_class.new.perform(bet_transaction.id.to_s)
        
        bet_transaction.reload
        expect(bet_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(975.0)
      end

      it 'includes bet-specific metadata' do
        described_class.new.perform(bet_transaction.id.to_s)
        
        bet_transaction.reload
        expect(bet_transaction.metadata['processed_by']).to eq('TransactionProcessorJob')
        expect(bet_transaction.metadata['processing_time']).to be_present
      end
    end

    context 'with bet win transaction' do
      let(:win_transaction) { create(:transaction, :bet_won, :pending, user: user, amount: 150.0) }

      it 'processes bet win successfully' do
        described_class.new.perform(win_transaction.id.to_s)
        
        win_transaction.reload
        expect(win_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(1150.0)
      end
    end

    context 'with adjustment transaction' do
      let(:adjustment_transaction) { create(:transaction, :adjustment, :pending, user: user, amount: 25.0) }

      it 'processes adjustment successfully' do
        described_class.new.perform(adjustment_transaction.id.to_s)
        
        adjustment_transaction.reload
        expect(adjustment_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(1025.0)
      end
    end

    context 'with refund transaction' do
      let(:refund_transaction) { create(:transaction, :refund, :pending, user: user, amount: 50.0) }

      it 'processes refund successfully' do
        described_class.new.perform(refund_transaction.id.to_s)
        
        refund_transaction.reload
        expect(refund_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(1050.0)
      end
    end

    context 'with bonus transaction' do
      let(:bonus_transaction) { create(:transaction, :bonus, :pending, user: user, amount: 100.0) }

      it 'processes bonus successfully' do
        described_class.new.perform(bonus_transaction.id.to_s)
        
        bonus_transaction.reload
        expect(bonus_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(1100.0)
      end
    end

    context 'with reconciliation transaction' do
      let(:reconciliation_transaction) { create(:transaction, :reconciliation, :pending, user: user, amount: 5.0) }

      it 'processes reconciliation successfully' do
        described_class.new.perform(reconciliation_transaction.id.to_s)
        
        reconciliation_transaction.reload
        expect(reconciliation_transaction.status).to eq('completed')
        
        user.reload
        expect(user.balance).to eq(1005.0)
      end
    end
  end

  describe 'job configuration' do
    it 'is configured with correct queue' do
      expect(described_class.queue_name).to eq('transactions')
    end

    it 'has retry configuration' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'has dead job configuration' do
      expect(described_class.sidekiq_options['dead']).to be true
    end
  end

  describe 'error handling and retries' do
    context 'when job fails multiple times' do
      before do
        allow_any_instance_of(User).to receive(:update_balance!).and_raise(StandardError.new('Persistent error'))
      end

      it 'marks transaction as failed after max retries' do
        # Simulate job failure after retries
        job = described_class.new
        allow(job).to receive(:retry_count).and_return(3)
        
        job.perform(transaction.id.to_s)
        
        transaction.reload
        expect(transaction.status).to eq('failed')
        expect(transaction.metadata['failure_reason']).to eq('Persistent error')
        expect(transaction.metadata['retry_count']).to eq(3)
      end
    end

    context 'when notification fails' do
      before do
        notification_service = instance_double(NotificationService)
        allow(NotificationService).to receive(:new).and_return(notification_service)
        allow(notification_service).to receive(:notify_transaction_completed).and_raise(StandardError.new('Notification error'))
      end

      it 'still completes transaction processing' do
        expect(Rails.logger).to receive(:error).with(/Failed to send notification/)
        
        described_class.new.perform(transaction.id.to_s)
        
        transaction.reload
        expect(transaction.status).to eq('completed')
      end
    end
  end

  describe 'performance and timing' do
    it 'records processing time in metadata' do
      start_time = Time.current
      Timecop.freeze(start_time) do
        Timecop.travel(0.5.seconds) do
          described_class.new.perform(transaction.id.to_s)
        end
      end
      
      transaction.reload
      expect(transaction.metadata['processing_time']).to be_present
      expect(transaction.metadata['processing_time']).to be >= 0.5
    end

    it 'sets processed_at timestamp' do
      freeze_time = Time.current
      Timecop.freeze(freeze_time) do
        described_class.new.perform(transaction.id.to_s)
      end
      
      transaction.reload
      expect(transaction.processed_at).to eq(freeze_time)
    end
  end

  describe 'transaction atomicity' do
    context 'when user balance update succeeds but transaction update fails' do
      before do
        allow_any_instance_of(Transaction).to receive(:update!).and_raise(StandardError.new('Update error'))
      end

      it 'rolls back user balance changes' do
        original_balance = user.balance
        
        expect {
          described_class.new.perform(transaction.id.to_s)
        }.to raise_error('Update error')
        
        user.reload
        expect(user.balance).to eq(original_balance)
      end
    end
  end
end
