require 'rails_helper'

RSpec.describe GameSettingsSyncJob, type: :job do
  let(:game_service_client) { instance_double(GameServiceClient) }

  before do
    allow(GameServiceClient).to receive(:new).and_return(game_service_client)
    
    # Create test settings
    create(:game_setting, :prizewheel_max_players, is_active: true)
    create(:game_setting, :prizewheel_min_bet, is_active: true)
    create(:game_setting, :amidakuji_max_players, is_active: true)
    create(:game_setting, :global_maintenance, is_active: true)
  end

  describe '#perform' do
    context 'with specific game type' do
      context 'for prizewheel settings' do
        it 'syncs prizewheel settings successfully' do
          expect(game_service_client).to receive(:update_game_settings).with(
            'prizewheel',
            hash_including('max_players' => 8, 'min_bet_amount' => 10.0)
          ).and_return(true)
          
          described_class.new.perform('prizewheel')
        end

        it 'logs successful sync' do
          allow(game_service_client).to receive(:update_game_settings).and_return(true)
          expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for prizewheel/)
          
          described_class.new.perform('prizewheel')
        end
      end

      context 'for amidakuji settings' do
        it 'syncs amidakuji settings successfully' do
          expect(game_service_client).to receive(:update_game_settings).with(
            'amidakuji',
            hash_including('max_players' => 16)
          ).and_return(true)
          
          described_class.new.perform('amidakuji')
        end
      end

      context 'for global settings' do
        it 'syncs global settings successfully' do
          expect(game_service_client).to receive(:update_game_settings).with(
            'global',
            hash_including('maintenance_mode' => false)
          ).and_return(true)
          
          described_class.new.perform('global')
        end
      end
    end

    context 'without game type (sync all)' do
      it 'syncs all game types' do
        expect(game_service_client).to receive(:update_game_settings).with('prizewheel', anything).and_return(true)
        expect(game_service_client).to receive(:update_game_settings).with('amidakuji', anything).and_return(true)
        expect(game_service_client).to receive(:update_game_settings).with('global', anything).and_return(true)
        
        described_class.new.perform
      end

      it 'logs sync for each game type' do
        allow(game_service_client).to receive(:update_game_settings).and_return(true)
        
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for prizewheel/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for amidakuji/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for global/)
        
        described_class.new.perform
      end
    end

    context 'with invalid game type' do
      it 'logs error and skips invalid game type' do
        expect(Rails.logger).to receive(:error).with(/Invalid game type: invalid_game/)
        expect(game_service_client).not_to receive(:update_game_settings)
        
        described_class.new.perform('invalid_game')
      end
    end

    context 'when Game Service is unavailable' do
      before do
        allow(game_service_client).to receive(:update_game_settings).and_raise(StandardError.new('Service unavailable'))
      end

      it 'logs error and re-raises for retry' do
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        
        expect {
          described_class.new.perform('prizewheel')
        }.to raise_error('Service unavailable')
      end

      it 'does not affect other game types when syncing all' do
        allow(game_service_client).to receive(:update_game_settings).with('prizewheel', anything).and_raise(StandardError.new('Service unavailable'))
        allow(game_service_client).to receive(:update_game_settings).with('amidakuji', anything).and_return(true)
        allow(game_service_client).to receive(:update_game_settings).with('global', anything).and_return(true)
        
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for amidakuji/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for global/)
        
        described_class.new.perform
      end
    end

    context 'when no settings exist for game type' do
      before do
        GameSetting.by_game_type('prizewheel').update_all(is_active: false)
      end

      it 'syncs empty configuration' do
        expect(game_service_client).to receive(:update_game_settings).with('prizewheel', {}).and_return(true)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for prizewheel/)
        
        described_class.new.perform('prizewheel')
      end
    end

    context 'with mixed active and inactive settings' do
      before do
        create(:game_setting, :prizewheel_max_bet, is_active: true)
        create(:game_setting, game_type: 'prizewheel', setting_key: 'inactive_setting', setting_value: 'test', is_active: false)
      end

      it 'only syncs active settings' do
        expect(game_service_client).to receive(:update_game_settings).with(
          'prizewheel',
          hash_including('max_players' => 8, 'min_bet_amount' => 10.0, 'max_bet_amount' => 1000.0)
        ).and_return(true)
        
        described_class.new.perform('prizewheel')
      end

      it 'does not include inactive settings' do
        expect(game_service_client).to receive(:update_game_settings) do |game_type, config|
          expect(config).not_to have_key('inactive_setting')
          true
        end
        
        described_class.new.perform('prizewheel')
      end
    end
  end

  describe 'job configuration' do
    it 'is configured with correct queue' do
      expect(described_class.queue_name).to eq('game_settings')
    end

    it 'has retry configuration' do
      expect(described_class.sidekiq_options['retry']).to eq(5)
    end

    it 'has dead job configuration' do
      expect(described_class.sidekiq_options['dead']).to be true
    end
  end

  describe 'error handling and retries' do
    context 'when sync fails temporarily' do
      before do
        allow(game_service_client).to receive(:update_game_settings).and_raise(StandardError.new('Temporary error'))
      end

      it 'logs error and allows retry' do
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        
        expect {
          described_class.new.perform('prizewheel')
        }.to raise_error('Temporary error')
      end
    end

    context 'when sync fails permanently' do
      before do
        allow(game_service_client).to receive(:update_game_settings).and_raise(StandardError.new('Permanent error'))
      end

      it 'logs error after max retries' do
        job = described_class.new
        allow(job).to receive(:retry_count).and_return(5)
        
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        expect(Rails.logger).to receive(:error).with(/Max retries exceeded for game settings sync/)
        
        expect {
          job.perform('prizewheel')
        }.to raise_error('Permanent error')
      end
    end

    context 'when GameServiceClient initialization fails' do
      before do
        allow(GameServiceClient).to receive(:new).and_raise(StandardError.new('Client initialization error'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to initialize GameServiceClient/)
        
        expect {
          described_class.new.perform('prizewheel')
        }.to raise_error('Client initialization error')
      end
    end
  end

  describe 'performance and monitoring' do
    it 'logs sync duration' do
      allow(game_service_client).to receive(:update_game_settings).and_return(true)
      
      expect(Rails.logger).to receive(:info) do |message|
        expect(message).to match(/Game settings synced successfully for prizewheel/)
        expect(message).to match(/Duration: \d+\.\d+ms/)
      end
      
      described_class.new.perform('prizewheel')
    end

    it 'logs number of settings synced' do
      allow(game_service_client).to receive(:update_game_settings).and_return(true)
      
      expect(Rails.logger).to receive(:info) do |message|
        expect(message).to match(/2 settings/)
      end
      
      described_class.new.perform('prizewheel')
    end
  end

  describe 'integration with GameSettingsService' do
    let(:game_settings_service) { instance_double(GameSettingsService) }

    before do
      allow(GameSettingsService).to receive(:new).and_return(game_settings_service)
    end

    it 'delegates to GameSettingsService for sync logic' do
      expect(game_settings_service).to receive(:sync_settings_to_game_service).with('prizewheel').and_return(true)
      
      described_class.new.perform('prizewheel')
    end

    context 'when GameSettingsService sync fails' do
      before do
        allow(game_settings_service).to receive(:sync_settings_to_game_service).and_raise(StandardError.new('Service error'))
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        
        expect {
          described_class.new.perform('prizewheel')
        }.to raise_error('Service error')
      end
    end
  end

  describe 'batch processing' do
    context 'when syncing multiple game types' do
      it 'processes each game type independently' do
        call_order = []
        
        allow(game_service_client).to receive(:update_game_settings) do |game_type, _|
          call_order << game_type
          true
        end
        
        described_class.new.perform
        
        expect(call_order).to contain_exactly('prizewheel', 'amidakuji', 'global')
      end

      it 'continues processing even if one game type fails' do
        allow(game_service_client).to receive(:update_game_settings).with('prizewheel', anything).and_raise(StandardError.new('Prizewheel error'))
        allow(game_service_client).to receive(:update_game_settings).with('amidakuji', anything).and_return(true)
        allow(game_service_client).to receive(:update_game_settings).with('global', anything).and_return(true)
        
        expect(Rails.logger).to receive(:error).with(/Failed to sync game settings for prizewheel/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for amidakuji/)
        expect(Rails.logger).to receive(:info).with(/Game settings synced successfully for global/)
        
        described_class.new.perform
      end
    end
  end
end
