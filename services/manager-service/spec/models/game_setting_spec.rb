require 'rails_helper'

RSpec.describe GameSetting, type: :model do
  describe 'validations' do
    let(:game_setting) { build(:game_setting) }

    it 'is valid with valid attributes' do
      expect(game_setting).to be_valid
    end

    describe 'game_type validation' do
      it 'requires game_type to be present' do
        game_setting.game_type = nil
        expect(game_setting).not_to be_valid
        expect(game_setting.errors[:game_type]).to include("can't be blank")
      end

      %w[prizewheel amidakuji global].each do |game_type|
        it "allows #{game_type} game type" do
          game_setting.game_type = game_type
          expect(game_setting).to be_valid
        end
      end

      it 'rejects invalid game types' do
        game_setting.game_type = 'invalid_game'
        expect(game_setting).not_to be_valid
        expect(game_setting.errors[:game_type]).to include('is not included in the list')
      end
    end

    describe 'setting_key validation' do
      it 'requires setting_key to be present' do
        game_setting.setting_key = nil
        expect(game_setting).not_to be_valid
        expect(game_setting.errors[:setting_key]).to include("can't be blank")
      end

      it 'requires unique setting_key within game_type for active settings' do
        create(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: true)
        game_setting.game_type = 'prizewheel'
        game_setting.setting_key = 'max_players'
        game_setting.is_active = true
        expect(game_setting).not_to be_valid
        expect(game_setting.errors[:setting_key]).to include('is already taken')
      end

      it 'allows duplicate setting_key for inactive settings' do
        create(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: false)
        game_setting.game_type = 'prizewheel'
        game_setting.setting_key = 'max_players'
        game_setting.is_active = false
        expect(game_setting).to be_valid
      end

      it 'allows same setting_key for different game_types' do
        create(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: true)
        game_setting.game_type = 'amidakuji'
        game_setting.setting_key = 'max_players'
        game_setting.is_active = true
        expect(game_setting).to be_valid
      end
    end

    describe 'setting_value validation' do
      it 'requires setting_value to be present' do
        game_setting.setting_value = nil
        expect(game_setting).not_to be_valid
        expect(game_setting.errors[:setting_value]).to include("can't be blank")
      end
    end
  end

  describe 'scopes' do
    let!(:active_prizewheel_setting) { create(:game_setting, :prizewheel_max_players, is_active: true) }
    let!(:inactive_prizewheel_setting) { create(:game_setting, :prizewheel_min_bet, is_active: false) }
    let!(:active_amidakuji_setting) { create(:game_setting, :amidakuji_max_players, is_active: true) }
    let!(:active_global_setting) { create(:game_setting, :global_maintenance, is_active: true) }

    describe '.active' do
      it 'returns only active settings' do
        expect(GameSetting.active).to contain_exactly(
          active_prizewheel_setting, 
          active_amidakuji_setting, 
          active_global_setting
        )
      end
    end

    describe '.inactive' do
      it 'returns only inactive settings' do
        expect(GameSetting.inactive).to contain_exactly(inactive_prizewheel_setting)
      end
    end

    describe '.by_game_type' do
      it 'returns settings for specified game type' do
        expect(GameSetting.by_game_type('prizewheel')).to contain_exactly(
          active_prizewheel_setting, 
          inactive_prizewheel_setting
        )
      end
    end

    describe '.by_key' do
      it 'returns settings with specified key' do
        expect(GameSetting.by_key('max_players')).to contain_exactly(
          active_prizewheel_setting, 
          active_amidakuji_setting
        )
      end
    end

    describe '.recent' do
      it 'orders settings by created_at desc' do
        expect(GameSetting.recent.first).to eq(active_global_setting)
      end
    end
  end

  describe 'class methods' do
    before do
      create(:game_setting, :prizewheel_max_players, is_active: true)
      create(:game_setting, :prizewheel_min_bet, is_active: true)
      create(:game_setting, :amidakuji_max_players, is_active: true)
      create(:game_setting, :global_maintenance, is_active: true)
    end

    describe '.get_game_config' do
      it 'returns configuration hash for prizewheel' do
        config = GameSetting.get_game_config('prizewheel')
        expect(config).to include('max_players' => 8, 'min_bet_amount' => 10.0)
      end

      it 'returns configuration hash for amidakuji' do
        config = GameSetting.get_game_config('amidakuji')
        expect(config).to include('max_players' => 16)
      end

      it 'returns configuration hash for global' do
        config = GameSetting.get_game_config('global')
        expect(config).to include('maintenance_mode' => false)
      end

      it 'returns empty hash for unknown game type' do
        config = GameSetting.get_game_config('unknown')
        expect(config).to eq({})
      end
    end

    describe '.get_setting' do
      it 'returns setting value for existing key' do
        value = GameSetting.get_setting('prizewheel', 'max_players')
        expect(value).to eq(8)
      end

      it 'returns default value for non-existing key' do
        value = GameSetting.get_setting('prizewheel', 'non_existing', 'default')
        expect(value).to eq('default')
      end

      it 'returns nil for non-existing key without default' do
        value = GameSetting.get_setting('prizewheel', 'non_existing')
        expect(value).to be_nil
      end
    end

    describe '.set_setting' do
      it 'creates new setting when none exists' do
        expect {
          GameSetting.set_setting('prizewheel', 'new_setting', 'new_value', 'New setting description')
        }.to change { GameSetting.count }.by(1)

        setting = GameSetting.find_by(game_type: 'prizewheel', setting_key: 'new_setting')
        expect(setting.setting_value).to eq('new_value')
        expect(setting.description).to eq('New setting description')
        expect(setting.is_active).to be true
      end

      it 'deactivates existing setting and creates new one' do
        existing_setting = create(:game_setting, game_type: 'prizewheel', setting_key: 'existing', is_active: true)
        
        expect {
          GameSetting.set_setting('prizewheel', 'existing', 'updated_value')
        }.to change { GameSetting.count }.by(1)

        existing_setting.reload
        expect(existing_setting.is_active).to be false

        new_setting = GameSetting.active.find_by(game_type: 'prizewheel', setting_key: 'existing')
        expect(new_setting.setting_value).to eq('updated_value')
      end
    end

    describe '.sync_all_pending' do
      it 'schedules sync jobs for all game types' do
        expect(GameSettingsSyncJob).to receive(:perform_async).with('prizewheel')
        expect(GameSettingsSyncJob).to receive(:perform_async).with('amidakuji')
        expect(GameSettingsSyncJob).to receive(:perform_async).with('global')

        GameSetting.sync_all_pending
      end
    end

    describe '.deactivate_all_for_game_type' do
      it 'deactivates all settings for specified game type' do
        GameSetting.deactivate_all_for_game_type('prizewheel')
        
        prizewheel_settings = GameSetting.by_game_type('prizewheel')
        expect(prizewheel_settings.all? { |s| !s.is_active }).to be true
        
        # Other game types should remain active
        amidakuji_settings = GameSetting.by_game_type('amidakuji')
        expect(amidakuji_settings.any?(&:is_active)).to be true
      end
    end
  end

  describe 'instance methods' do
    let(:game_setting) { create(:game_setting) }

    describe '#activate!' do
      it 'activates the setting' do
        game_setting.update!(is_active: false)
        game_setting.activate!
        expect(game_setting.is_active).to be true
      end
    end

    describe '#deactivate!' do
      it 'deactivates the setting' do
        game_setting.update!(is_active: true)
        game_setting.deactivate!
        expect(game_setting.is_active).to be false
      end
    end

    describe '#active?' do
      it 'returns true for active settings' do
        game_setting.is_active = true
        expect(game_setting.active?).to be true
      end

      it 'returns false for inactive settings' do
        game_setting.is_active = false
        expect(game_setting.active?).to be false
      end
    end

    describe '#to_config_hash' do
      it 'returns setting as key-value pair' do
        game_setting.setting_key = 'max_players'
        game_setting.setting_value = 8
        
        expect(game_setting.to_config_hash).to eq({ 'max_players' => 8 })
      end
    end

    describe '#formatted_value' do
      it 'returns the setting value in appropriate format' do
        # Test with different value types
        game_setting.setting_value = 8
        expect(game_setting.formatted_value).to eq(8)

        game_setting.setting_value = 'string_value'
        expect(game_setting.formatted_value).to eq('string_value')

        game_setting.setting_value = [1, 2, 3]
        expect(game_setting.formatted_value).to eq([1, 2, 3])

        game_setting.setting_value = { 'key' => 'value' }
        expect(game_setting.formatted_value).to eq({ 'key' => 'value' })
      end
    end
  end

  describe 'callbacks and validations integration' do
    it 'prevents creating duplicate active settings' do
      create(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: true)
      
      duplicate_setting = build(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: true)
      expect(duplicate_setting).not_to be_valid
    end

    it 'allows creating inactive settings with same key' do
      create(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: true)
      
      inactive_setting = build(:game_setting, game_type: 'prizewheel', setting_key: 'max_players', is_active: false)
      expect(inactive_setting).to be_valid
    end
  end
end
