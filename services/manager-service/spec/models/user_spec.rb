require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'validations' do
    let(:user) { build(:user) }

    it 'is valid with valid attributes' do
      expect(user).to be_valid
    end

    describe 'username validation' do
      it 'requires username to be present' do
        user.username = nil
        expect(user).not_to be_valid
        expect(user.errors[:username]).to include("can't be blank")
      end

      it 'requires username to be unique' do
        create(:user, username: 'testuser')
        user.username = 'testuser'
        expect(user).not_to be_valid
        expect(user.errors[:username]).to include('is already taken')
      end

      it 'requires username to be at least 3 characters' do
        user.username = 'ab'
        expect(user).not_to be_valid
        expect(user.errors[:username]).to include('is too short (minimum is 3 characters)')
      end

      it 'requires username to be at most 50 characters' do
        user.username = 'a' * 51
        expect(user).not_to be_valid
        expect(user.errors[:username]).to include('is too long (maximum is 50 characters)')
      end
    end

    describe 'email validation' do
      it 'requires email to be present' do
        user.email = nil
        expect(user).not_to be_valid
        expect(user.errors[:email]).to include("can't be blank")
      end

      it 'requires email to be unique' do
        create(:user, email: '<EMAIL>')
        user.email = '<EMAIL>'
        expect(user).not_to be_valid
        expect(user.errors[:email]).to include('is already taken')
      end

      it 'requires valid email format' do
        user.email = 'invalid-email'
        expect(user).not_to be_valid
        expect(user.errors[:email]).to include('is invalid')
      end
    end

    describe 'password validation' do
      it 'requires password to be present for new users' do
        user.password = nil
        expect(user).not_to be_valid
        expect(user.errors[:password]).to include("can't be blank")
      end

      it 'requires password to be at least 6 characters' do
        user.password = '12345'
        expect(user).not_to be_valid
        expect(user.errors[:password]).to include('is too short (minimum is 6 characters)')
      end
    end

    describe 'balance validation' do
      it 'requires balance to be non-negative' do
        user.balance = -10.0
        expect(user).not_to be_valid
        expect(user.errors[:balance]).to include('must be greater than or equal to 0')
      end

      it 'allows zero balance' do
        user.balance = 0.0
        expect(user).to be_valid
      end
    end

    describe 'status validation' do
      it 'validates status inclusion' do
        user.status = 'invalid_status'
        expect(user).not_to be_valid
        expect(user.errors[:status]).to include('is not included in the list')
      end

      %w[active inactive suspended banned].each do |status|
        it "allows #{status} status" do
          user.status = status
          expect(user).to be_valid
        end
      end
    end

    describe 'role validation' do
      it 'validates role inclusion' do
        user.role = 'invalid_role'
        expect(user).not_to be_valid
        expect(user.errors[:role]).to include('is not included in the list')
      end

      %w[player admin moderator].each do |role|
        it "allows #{role} role" do
          user.role = role
          expect(user).to be_valid
        end
      end
    end
  end

  describe 'associations' do
    it { should have_many(:game_sessions).dependent(:destroy) }
    it { should have_many(:transactions).dependent(:destroy) }
    it { should have_many(:user_achievements).dependent(:destroy) }
  end

  describe 'scopes' do
    let!(:active_user) { create(:user, status: 'active') }
    let!(:inactive_user) { create(:user, :inactive) }
    let!(:suspended_user) { create(:user, :suspended) }
    let!(:banned_user) { create(:user, :banned) }
    let!(:admin_user) { create(:user, :admin) }
    let!(:moderator_user) { create(:user, :moderator) }
    let!(:recent_login_user) { create(:user, :recent_login) }
    let!(:old_login_user) { create(:user, :old_login) }

    describe '.active' do
      it 'returns only active users' do
        expect(User.active).to contain_exactly(active_user, admin_user, moderator_user, recent_login_user, old_login_user)
      end
    end

    describe '.players' do
      it 'returns only players' do
        expect(User.players).to contain_exactly(active_user, inactive_user, suspended_user, banned_user, recent_login_user, old_login_user)
      end
    end

    describe '.admins' do
      it 'returns only admins' do
        expect(User.admins).to contain_exactly(admin_user)
      end
    end

    describe '.moderators' do
      it 'returns only moderators' do
        expect(User.moderators).to contain_exactly(moderator_user)
      end
    end

    describe '.suspended' do
      it 'returns only suspended users' do
        expect(User.suspended).to contain_exactly(suspended_user)
      end
    end

    describe '.banned' do
      it 'returns only banned users' do
        expect(User.banned).to contain_exactly(banned_user)
      end
    end

    describe '.inactive' do
      it 'returns only inactive users' do
        expect(User.inactive).to contain_exactly(inactive_user)
      end
    end

    describe '.recent_login' do
      it 'returns users who logged in within 30 days' do
        expect(User.recent_login).to include(recent_login_user)
        expect(User.recent_login).not_to include(old_login_user)
      end
    end
  end

  describe 'callbacks' do
    describe 'before_save :encrypt_password' do
      it 'encrypts password when password is present' do
        user = build(:user, password: 'newpassword')
        user.save!
        expect(user.password_digest).to be_present
        expect(user.password_digest).not_to eq('newpassword')
      end
    end

    describe 'before_save :normalize_email' do
      it 'normalizes email to lowercase' do
        user = build(:user, email: '<EMAIL>')
        user.save!
        expect(user.email).to eq('<EMAIL>')
      end

      it 'strips whitespace from email' do
        user = build(:user, email: '  <EMAIL>  ')
        user.save!
        expect(user.email).to eq('<EMAIL>')
      end
    end

    describe 'before_validation :set_default_balance' do
      it 'sets default balance if not provided' do
        user = build(:user, balance: nil)
        user.valid?
        expect(user.balance).to eq(0.0)
      end
    end
  end

  describe 'instance methods' do
    let(:user) { create(:user, password: 'password123') }

    describe '#authenticate' do
      it 'returns true for correct password' do
        expect(user.authenticate('password123')).to be_truthy
      end

      it 'returns false for incorrect password' do
        expect(user.authenticate('wrongpassword')).to be_falsey
      end
    end

    describe '#admin?' do
      it 'returns true for admin users' do
        admin = create(:user, :admin)
        expect(admin.admin?).to be true
      end

      it 'returns false for non-admin users' do
        expect(user.admin?).to be false
      end
    end

    describe '#player?' do
      it 'returns true for player users' do
        expect(user.player?).to be true
      end

      it 'returns false for admin users' do
        admin = create(:user, :admin)
        expect(admin.player?).to be false
      end
    end

    describe '#active?' do
      it 'returns true for active users' do
        expect(user.active?).to be true
      end

      it 'returns false for inactive users' do
        inactive_user = create(:user, :inactive)
        expect(inactive_user.active?).to be false
      end
    end

    describe '#can_play?' do
      it 'returns true for active users with non-negative balance' do
        expect(user.can_play?).to be true
      end

      it 'returns false for inactive users' do
        user.status = 'inactive'
        expect(user.can_play?).to be false
      end

      it 'returns false for users with negative balance' do
        user.balance = -10.0
        expect(user.can_play?).to be false
      end
    end
  end
end
