require 'rails_helper'

RSpec.describe Room, type: :model do
  describe 'Room Reconnection Logic' do
    let(:user) { create(:user, balance: 1000) }
    let(:room) { create(:room, bet_amount: 100, max_players: 4) }

    describe '#add_player!' do
      context 'when player is joining for the first time' do
        it 'creates a new game session and increments player count' do
          expect {
            result = room.add_player!(user)
            expect(result).to be_a(GameSession)
            expect(result.user).to eq(user)
            expect(result.status).to eq('pending')
          }.to change(room, :current_players).by(1)
            .and change(GameSession, :count).by(1)
        end

        it 'does not update prize pool on initial join' do
          expect {
            room.add_player!(user)
          }.not_to change(room, :prize_pool)
        end
      end

      context 'when player is already in the room (reconnection)' do
        let!(:existing_session) do
          room.game_sessions.create!(
            user: user,
            room: room,
            game_type: room.game_type,
            bet_amount: room.bet_amount,
            status: 'pending',
            metadata: { 'is_ready' => false }
          )
        end

        before do
          room.update!(current_players: 1)
        end

        it 'returns existing session without creating new one' do
          expect {
            result = room.add_player!(user)
            expect(result).to eq(existing_session)
          }.not_to change(GameSession, :count)
        end

        it 'does not increment player count on reconnection' do
          expect {
            room.add_player!(user)
          }.not_to change(room, :current_players)
        end

        it 'preserves existing player state' do
          existing_session.update!(metadata: { 'is_ready' => true })
          
          result = room.add_player!(user)
          expect(result.metadata['is_ready']).to be true
        end
      end

      context 'when room is full' do
        before do
          room.update!(current_players: room.max_players)
        end

        it 'raises an error for new players' do
          expect {
            room.add_player!(user)
          }.to raise_error(ArgumentError, 'Room is full')
        end

        it 'allows reconnection even when room appears full' do
          # Create existing session
          room.game_sessions.create!(
            user: user,
            room: room,
            game_type: room.game_type,
            bet_amount: room.bet_amount,
            status: 'pending'
          )

          expect {
            result = room.add_player!(user)
            expect(result).to be_a(GameSession)
          }.not_to raise_error
        end
      end
    end

    describe '#set_player_ready!' do
      let!(:session) do
        room.game_sessions.create!(
          user: user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending',
          metadata: { 'is_ready' => false }
        )
      end

      before do
        room.update!(current_players: 1)
      end

      context 'when player becomes ready' do
        it 'updates prize pool by bet amount' do
          expect {
            room.set_player_ready!(user, true)
          }.to change(room, :prize_pool).by(room.bet_amount)
        end

        it 'updates session metadata' do
          room.set_player_ready!(user, true)
          session.reload
          expect(session.metadata['is_ready']).to be true
        end
      end

      context 'when player becomes not ready' do
        before do
          session.update!(metadata: { 'is_ready' => true })
          room.update!(prize_pool: room.bet_amount)
        end

        it 'reduces prize pool by bet amount' do
          expect {
            room.set_player_ready!(user, false)
          }.to change(room, :prize_pool).by(-room.bet_amount)
        end

        it 'updates session metadata' do
          room.set_player_ready!(user, false)
          session.reload
          expect(session.metadata['is_ready']).to be false
        end
      end

      context 'when ready status does not change' do
        it 'does not update prize pool when already ready' do
          session.update!(metadata: { 'is_ready' => true })
          room.update!(prize_pool: room.bet_amount)

          expect {
            room.set_player_ready!(user, true)
          }.not_to change(room, :prize_pool)
        end

        it 'does not update prize pool when already not ready' do
          expect {
            room.set_player_ready!(user, false)
          }.not_to change(room, :prize_pool)
        end
      end

      context 'when player is not in room' do
        let(:other_user) { create(:user) }

        it 'raises an error' do
          expect {
            room.set_player_ready!(other_user, true)
          }.to raise_error(ArgumentError, 'Player not found in room')
        end
      end
    end

    describe '#remove_player!' do
      let!(:session) do
        room.game_sessions.create!(
          user: user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending',
          metadata: { 'is_ready' => false }
        )
      end

      before do
        room.update!(current_players: 1)
      end

      context 'when player is not ready' do
        it 'decrements player count but not prize pool' do
          expect {
            room.remove_player!(user)
          }.to change(room, :current_players).by(-1)
            .and not_change(room, :prize_pool)
        end
      end

      context 'when player is ready' do
        before do
          session.update!(metadata: { 'is_ready' => true })
          room.update!(prize_pool: room.bet_amount)
        end

        it 'decrements both player count and prize pool' do
          expect {
            room.remove_player!(user)
          }.to change(room, :current_players).by(-1)
            .and change(room, :prize_pool).by(-room.bet_amount)
        end
      end
    end

    describe 'helper methods' do
      let!(:ready_session) do
        room.game_sessions.create!(
          user: user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending',
          metadata: { 'is_ready' => true }
        )
      end

      let(:other_user) { create(:user) }
      let!(:not_ready_session) do
        room.game_sessions.create!(
          user: other_user,
          room: room,
          game_type: room.game_type,
          bet_amount: room.bet_amount,
          status: 'pending',
          metadata: { 'is_ready' => false }
        )
      end

      before do
        room.update!(current_players: 2)
      end

      describe '#player_ready?' do
        it 'returns true for ready player' do
          expect(room.player_ready?(user)).to be true
        end

        it 'returns false for not ready player' do
          expect(room.player_ready?(other_user)).to be false
        end

        it 'returns false for player not in room' do
          non_player = create(:user)
          expect(room.player_ready?(non_player)).to be false
        end
      end

      describe '#ready_players_count' do
        it 'returns correct count of ready players' do
          expect(room.ready_players_count).to eq(1)
        end
      end
    end
  end
end
