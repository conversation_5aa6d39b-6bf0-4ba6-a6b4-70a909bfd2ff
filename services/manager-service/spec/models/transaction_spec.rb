require 'rails_helper'

RSpec.describe Transaction, type: :model do
  describe 'validations' do
    let(:transaction) { build(:transaction) }

    it 'is valid with valid attributes' do
      expect(transaction).to be_valid
    end

    describe 'transaction_id validation' do
      it 'requires transaction_id to be present' do
        transaction.transaction_id = nil
        expect(transaction).not_to be_valid
        expect(transaction.errors[:transaction_id]).to include("can't be blank")
      end

      it 'requires transaction_id to be unique' do
        create(:transaction, transaction_id: 'txn_123')
        transaction.transaction_id = 'txn_123'
        expect(transaction).not_to be_valid
        expect(transaction.errors[:transaction_id]).to include('is already taken')
      end
    end

    describe 'transaction_type validation' do
      it 'requires transaction_type to be present' do
        transaction.transaction_type = nil
        expect(transaction).not_to be_valid
        expect(transaction.errors[:transaction_type]).to include("can't be blank")
      end

      valid_types = %w[deposit admin_deposit withdrawal bet_placed bet_won bet_lost
                       adjustment refund bonus reconciliation bet_reserved]
      
      valid_types.each do |type|
        it "allows #{type} transaction type" do
          transaction.transaction_type = type
          expect(transaction).to be_valid
        end
      end

      it 'rejects invalid transaction types' do
        transaction.transaction_type = 'invalid_type'
        expect(transaction).not_to be_valid
        expect(transaction.errors[:transaction_type]).to include('is not included in the list')
      end
    end

    describe 'status validation' do
      %w[pending completed failed cancelled].each do |status|
        it "allows #{status} status" do
          transaction.status = status
          expect(transaction).to be_valid
        end
      end

      it 'rejects invalid status' do
        transaction.status = 'invalid_status'
        expect(transaction).not_to be_valid
        expect(transaction.errors[:status]).to include('is not included in the list')
      end
    end

    describe 'amount validation' do
      it 'validates amount is numeric' do
        transaction.amount = 'not_a_number'
        expect(transaction).not_to be_valid
      end

      it 'allows positive amounts' do
        transaction.amount = 100.0
        expect(transaction).to be_valid
      end

      it 'allows negative amounts for debits' do
        transaction.amount = -50.0
        expect(transaction).to be_valid
      end

      it 'allows zero amounts' do
        transaction.amount = 0.0
        expect(transaction).to be_valid
      end
    end

    describe 'balance validations' do
      it 'validates balance_before is non-negative' do
        transaction.balance_before = -10.0
        expect(transaction).not_to be_valid
      end

      it 'validates balance_after is non-negative' do
        transaction.balance_after = -10.0
        expect(transaction).not_to be_valid
      end
    end
  end

  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'callbacks' do
    describe 'before_validation :generate_transaction_id' do
      it 'generates transaction_id on create' do
        transaction = build(:transaction, transaction_id: nil)
        transaction.save!
        expect(transaction.transaction_id).to be_present
        expect(transaction.transaction_id).to start_with('txn_')
      end

      it 'does not override existing transaction_id' do
        original_id = 'custom_txn_123'
        transaction = build(:transaction, transaction_id: original_id)
        transaction.save!
        expect(transaction.transaction_id).to eq(original_id)
      end
    end

    describe 'before_save :set_processed_at' do
      it 'sets processed_at for completed transactions' do
        transaction = create(:transaction, status: 'pending', processed_at: nil)
        transaction.update!(status: 'completed')
        expect(transaction.processed_at).to be_present
      end
    end
  end

  describe 'scopes' do
    let!(:completed_transaction) { create(:transaction, :deposit, status: 'completed') }
    let!(:pending_transaction) { create(:transaction, :withdrawal, status: 'pending') }
    let!(:failed_transaction) { create(:transaction, :bet_placed, status: 'failed') }
    let!(:deposit_transaction) { create(:transaction, :deposit) }
    let!(:admin_deposit_transaction) { create(:transaction, :admin_deposit) }
    let!(:withdrawal_transaction) { create(:transaction, :withdrawal) }
    let!(:bet_transaction) { create(:transaction, :bet_placed) }

    describe '.completed' do
      it 'returns only completed transactions' do
        expect(Transaction.completed).to include(completed_transaction, deposit_transaction, admin_deposit_transaction, withdrawal_transaction, bet_transaction)
      end
    end

    describe '.pending' do
      it 'returns only pending transactions' do
        expect(Transaction.pending).to contain_exactly(pending_transaction)
      end
    end

    describe '.failed' do
      it 'returns only failed transactions' do
        expect(Transaction.failed).to contain_exactly(failed_transaction)
      end
    end

    describe '.by_type' do
      it 'returns transactions of specified type' do
        expect(Transaction.by_type('deposit')).to contain_exactly(deposit_transaction)
      end
    end

    describe '.deposits' do
      it 'returns deposit and admin_deposit transactions' do
        expect(Transaction.deposits).to contain_exactly(deposit_transaction, admin_deposit_transaction)
      end
    end

    describe '.withdrawals' do
      it 'returns withdrawal transactions' do
        expect(Transaction.withdrawals).to contain_exactly(withdrawal_transaction)
      end
    end

    describe '.bets' do
      it 'returns bet-related transactions' do
        expect(Transaction.bets).to contain_exactly(bet_transaction)
      end
    end

    describe '.admin_deposits' do
      it 'returns only admin deposit transactions' do
        expect(Transaction.admin_deposits).to contain_exactly(admin_deposit_transaction)
      end
    end

    describe '.recent' do
      it 'orders transactions by created_at desc' do
        expect(Transaction.recent.first).to eq(bet_transaction)
      end
    end
  end

  describe 'instance methods' do
    let(:transaction) { create(:transaction, status: 'pending') }

    describe '#complete!' do
      it 'marks transaction as completed' do
        transaction.complete!
        expect(transaction.status).to eq('completed')
        expect(transaction.processed_at).to be_present
      end
    end

    describe '#fail!' do
      it 'marks transaction as failed' do
        reason = 'Insufficient funds'
        transaction.fail!(reason)
        expect(transaction.status).to eq('failed')
        expect(transaction.processed_at).to be_present
        expect(transaction.metadata['failure_reason']).to eq(reason)
      end
    end

    describe '#cancel!' do
      it 'marks transaction as cancelled' do
        reason = 'User request'
        transaction.cancel!(reason)
        expect(transaction.status).to eq('cancelled')
        expect(transaction.processed_at).to be_present
        expect(transaction.metadata['cancellation_reason']).to eq(reason)
      end
    end

    describe '#debit?' do
      it 'returns true for debit transaction types' do
        %w[withdrawal bet_placed bet_reserved].each do |type|
          transaction = build(:transaction, transaction_type: type)
          expect(transaction.debit?).to be true
        end
      end

      it 'returns false for credit transaction types' do
        %w[deposit admin_deposit bet_won bonus].each do |type|
          transaction = build(:transaction, transaction_type: type)
          expect(transaction.debit?).to be false
        end
      end
    end

    describe '#credit?' do
      it 'returns true for credit transaction types with positive amount' do
        %w[deposit admin_deposit bet_won bonus adjustment refund].each do |type|
          transaction = build(:transaction, transaction_type: type, amount: 100.0)
          expect(transaction.credit?).to be true
        end
      end

      it 'returns false for credit transaction types with zero or negative amount' do
        transaction = build(:transaction, transaction_type: 'deposit', amount: 0.0)
        expect(transaction.credit?).to be false
      end
    end

    describe '#completed?' do
      it 'returns true for completed transactions' do
        transaction.status = 'completed'
        expect(transaction.completed?).to be true
      end

      it 'returns false for non-completed transactions' do
        expect(transaction.completed?).to be false
      end
    end

    describe '#pending?' do
      it 'returns true for pending transactions' do
        expect(transaction.pending?).to be true
      end

      it 'returns false for non-pending transactions' do
        transaction.status = 'completed'
        expect(transaction.pending?).to be false
      end
    end

    describe '#failed?' do
      it 'returns true for failed transactions' do
        transaction.status = 'failed'
        expect(transaction.failed?).to be true
      end

      it 'returns false for non-failed transactions' do
        expect(transaction.failed?).to be false
      end
    end

    describe '#cancelled?' do
      it 'returns true for cancelled transactions' do
        transaction.status = 'cancelled'
        expect(transaction.cancelled?).to be true
      end

      it 'returns false for non-cancelled transactions' do
        expect(transaction.cancelled?).to be false
      end
    end
  end
end
