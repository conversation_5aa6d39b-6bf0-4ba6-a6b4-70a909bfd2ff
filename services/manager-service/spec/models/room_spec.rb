require 'rails_helper'

RSpec.describe Room, type: :model do
  describe 'validations' do
    let(:room) { build(:room) }

    it 'is valid with valid attributes' do
      expect(room).to be_valid
    end

    describe 'name validation' do
      it 'requires name to be present' do
        room.name = nil
        expect(room).not_to be_valid
        expect(room.errors[:name]).to include("can't be blank")
      end

      it 'requires name to be at least 3 characters' do
        room.name = 'ab'
        expect(room).not_to be_valid
        expect(room.errors[:name]).to include('is too short (minimum is 3 characters)')
      end

      it 'requires name to be at most 100 characters' do
        room.name = 'a' * 101
        expect(room).not_to be_valid
        expect(room.errors[:name]).to include('is too long (maximum is 100 characters)')
      end
    end

    describe 'game_type validation' do
      it 'requires game_type to be present' do
        room.game_type = nil
        expect(room).not_to be_valid
        expect(room.errors[:game_type]).to include("can't be blank")
      end

      %w[prizewheel amidakuji].each do |game_type|
        it "allows #{game_type} game type" do
          room.game_type = game_type
          expect(room).to be_valid
        end
      end

      it 'rejects invalid game types' do
        room.game_type = 'invalid_game'
        expect(room).not_to be_valid
        expect(room.errors[:game_type]).to include('is not included in the list')
      end
    end

    describe 'status validation' do
      %w[waiting full playing finished cancelled].each do |status|
        it "allows #{status} status" do
          room.status = status
          expect(room).to be_valid
        end
      end

      it 'rejects invalid status' do
        room.status = 'invalid_status'
        expect(room).not_to be_valid
        expect(room.errors[:status]).to include('is not included in the list')
      end
    end

    describe 'max_players validation' do
      it 'requires max_players to be present' do
        room.max_players = nil
        expect(room).not_to be_valid
        expect(room.errors[:max_players]).to include("can't be blank")
      end

      it 'requires max_players to be at least 2' do
        room.max_players = 1
        expect(room).not_to be_valid
        expect(room.errors[:max_players]).to include('must be greater than or equal to 2')
      end

      it 'requires max_players to be at most 50' do
        room.max_players = 51
        expect(room).not_to be_valid
        expect(room.errors[:max_players]).to include('must be less than or equal to 50')
      end
    end

    describe 'current_players validation' do
      it 'requires current_players to be non-negative' do
        room.current_players = -1
        expect(room).not_to be_valid
        expect(room.errors[:current_players]).to include('must be greater than or equal to 0')
      end

      it 'requires current_players to not exceed max_players' do
        room.max_players = 8
        room.current_players = 9
        expect(room).not_to be_valid
        expect(room.errors[:current_players]).to include('cannot exceed max_players')
      end
    end

    describe 'bet_amount validation' do
      it 'requires bet_amount to be present' do
        room.bet_amount = nil
        expect(room).not_to be_valid
        expect(room.errors[:bet_amount]).to include("can't be blank")
      end

      it 'requires bet_amount to be positive' do
        room.bet_amount = 0
        expect(room).not_to be_valid
        expect(room.errors[:bet_amount]).to include('must be greater than 0')
      end

      it 'requires bet_amount to be positive for negative values' do
        room.bet_amount = -10
        expect(room).not_to be_valid
        expect(room.errors[:bet_amount]).to include('must be greater than 0')
      end
    end

    describe 'currency validation' do
      it 'requires currency to be present' do
        room.currency = nil
        expect(room).not_to be_valid
        expect(room.errors[:currency]).to include("can't be blank")
      end

      %w[USD EUR GBP].each do |currency|
        it "allows #{currency} currency" do
          room.currency = currency
          expect(room).to be_valid
        end
      end

      it 'rejects invalid currencies' do
        room.currency = 'INVALID'
        expect(room).not_to be_valid
        expect(room.errors[:currency]).to include('is not included in the list')
      end
    end
  end

  describe 'associations' do
    it { should belong_to(:creator).class_name('User') }
    it { should have_many(:game_sessions).dependent(:destroy) }
  end

  describe 'callbacks' do
    describe 'before_create :generate_external_room_id' do
      it 'generates external_room_id when blank' do
        room = build(:room, external_room_id: nil)
        room.save!
        expect(room.external_room_id).to be_present
        expect(room.external_room_id).to start_with('room_')
      end

      it 'does not override existing external_room_id' do
        original_id = 'custom_room_123'
        room = build(:room, external_room_id: original_id)
        room.save!
        expect(room.external_room_id).to eq(original_id)
      end
    end

    describe 'before_save :encrypt_password' do
      it 'encrypts password when password is present' do
        room = build(:room, password: 'secret123')
        room.save!
        expect(room.password_hash).to be_present
        expect(room.password_hash).not_to eq('secret123')
      end
    end

    describe 'after_update :sync_with_game_service' do
      it 'schedules sync job when sync is required' do
        room = create(:room)
        expect(RoomSettingsSyncJob).to receive(:perform_async).with(room.id.to_s)
        room.update!(sync_required: true)
      end
    end
  end

  describe 'scopes' do
    let!(:waiting_room) { create(:room, status: 'waiting') }
    let!(:full_room) { create(:room, :full_room) }
    let!(:playing_room) { create(:room, :playing) }
    let!(:finished_room) { create(:room, :finished) }
    let!(:cancelled_room) { create(:room, :cancelled) }
    let!(:prizewheel_room) { create(:room, game_type: 'prizewheel') }
    let!(:amidakuji_room) { create(:room, :amidakuji) }
    let!(:public_room) { create(:room, is_private: false) }
    let!(:private_room) { create(:room, :private_room) }
    let!(:needs_sync_room) { create(:room, :needs_sync) }

    describe '.waiting' do
      it 'returns only waiting rooms' do
        expect(Room.waiting).to contain_exactly(waiting_room, prizewheel_room, amidakuji_room, public_room, private_room, needs_sync_room)
      end
    end

    describe '.full' do
      it 'returns only full rooms' do
        expect(Room.full).to contain_exactly(full_room)
      end
    end

    describe '.playing' do
      it 'returns only playing rooms' do
        expect(Room.playing).to contain_exactly(playing_room)
      end
    end

    describe '.finished' do
      it 'returns only finished rooms' do
        expect(Room.finished).to contain_exactly(finished_room)
      end
    end

    describe '.cancelled' do
      it 'returns only cancelled rooms' do
        expect(Room.cancelled).to contain_exactly(cancelled_room)
      end
    end

    describe '.by_game_type' do
      it 'returns rooms of specified game type' do
        expect(Room.by_game_type('amidakuji')).to contain_exactly(amidakuji_room)
      end
    end

    describe '.public_rooms' do
      it 'returns only public rooms' do
        expect(Room.public_rooms).to include(public_room, waiting_room, prizewheel_room, amidakuji_room, needs_sync_room)
        expect(Room.public_rooms).not_to include(private_room)
      end
    end

    describe '.private_rooms' do
      it 'returns only private rooms' do
        expect(Room.private_rooms).to contain_exactly(private_room)
      end
    end

    describe '.needs_sync' do
      it 'returns rooms that need synchronization' do
        expect(Room.needs_sync).to contain_exactly(needs_sync_room)
      end
    end

    describe '.recent' do
      it 'orders rooms by created_at desc' do
        expect(Room.recent.first).to eq(needs_sync_room)
      end
    end
  end

  describe 'instance methods' do
    let(:room) { create(:room) }

    describe '#waiting?' do
      it 'returns true for waiting rooms' do
        room.status = 'waiting'
        expect(room.waiting?).to be true
      end

      it 'returns false for non-waiting rooms' do
        room.status = 'playing'
        expect(room.waiting?).to be false
      end
    end

    describe '#playing?' do
      it 'returns true for playing rooms' do
        room.status = 'playing'
        expect(room.playing?).to be true
      end

      it 'returns false for non-playing rooms' do
        expect(room.playing?).to be false
      end
    end

    describe '#finished?' do
      it 'returns true for finished rooms' do
        room.status = 'finished'
        expect(room.finished?).to be true
      end

      it 'returns false for non-finished rooms' do
        expect(room.finished?).to be false
      end
    end

    describe '#cancelled?' do
      it 'returns true for cancelled rooms' do
        room.status = 'cancelled'
        expect(room.cancelled?).to be true
      end

      it 'returns false for non-cancelled rooms' do
        expect(room.cancelled?).to be false
      end
    end

    describe '#full?' do
      it 'returns true when current_players equals max_players' do
        room.current_players = room.max_players
        expect(room.full?).to be true
      end

      it 'returns false when current_players is less than max_players' do
        room.current_players = room.max_players - 1
        expect(room.full?).to be false
      end
    end

    describe '#empty?' do
      it 'returns true when current_players is zero' do
        room.current_players = 0
        expect(room.empty?).to be true
      end

      it 'returns false when current_players is greater than zero' do
        room.current_players = 1
        expect(room.empty?).to be false
      end
    end

    describe '#has_password?' do
      it 'returns true when password_hash is present' do
        room = create(:room, :private_room)
        expect(room.has_password?).to be true
      end

      it 'returns false when password_hash is blank' do
        expect(room.has_password?).to be false
      end
    end

    describe '#valid_password?' do
      let(:private_room) { create(:room, :private_room) }

      it 'returns true for correct password' do
        expect(private_room.valid_password?('secret123')).to be true
      end

      it 'returns false for incorrect password' do
        expect(private_room.valid_password?('wrongpassword')).to be false
      end

      it 'returns true for rooms without password' do
        expect(room.valid_password?('anypassword')).to be true
      end
    end
  end

  describe 'class methods' do
    describe '.create_with_game_service' do
      let(:creator) { create(:user) }
      let(:room_params) { { name: 'Test Room', game_type: 'prizewheel', bet_amount: 100 } }

      it 'delegates to RoomManagementService' do
        room_service = instance_double(RoomManagementService)
        expect(RoomManagementService).to receive(:new).and_return(room_service)
        expect(room_service).to receive(:create_room).with(creator, room_params)

        Room.create_with_game_service(creator, room_params)
      end
    end

    describe '.sync_all_pending' do
      it 'schedules sync jobs for rooms that need sync' do
        room1 = create(:room, :needs_sync)
        room2 = create(:room, :needs_sync)
        room3 = create(:room, :recently_synced)

        expect(RoomSettingsSyncJob).to receive(:perform_async).with(room1.id.to_s)
        expect(RoomSettingsSyncJob).to receive(:perform_async).with(room2.id.to_s)
        expect(RoomSettingsSyncJob).not_to receive(:perform_async).with(room3.id.to_s)

        Room.sync_all_pending
      end
    end

    describe '.cleanup_finished_rooms' do
      it 'removes finished rooms older than 24 hours' do
        old_finished_room = create(:room, :finished)
        old_finished_room.update!(updated_at: 25.hours.ago)
        
        recent_finished_room = create(:room, :finished)
        recent_finished_room.update!(updated_at: 1.hour.ago)

        expect { Room.cleanup_finished_rooms }.to change { Room.count }.by(-1)
        expect(Room.exists?(old_finished_room.id)).to be false
        expect(Room.exists?(recent_finished_room.id)).to be true
      end
    end
  end
end
