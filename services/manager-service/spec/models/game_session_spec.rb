require 'rails_helper'

RSpec.describe GameSession, type: :model do
  describe 'validations' do
    let(:game_session) { build(:game_session) }

    it 'is valid with valid attributes' do
      expect(game_session).to be_valid
    end

    describe 'session_id validation' do
      it 'requires session_id to be present' do
        game_session.session_id = nil
        expect(game_session).not_to be_valid
        expect(game_session.errors[:session_id]).to include("can't be blank")
      end

      it 'requires session_id to be unique' do
        create(:game_session, session_id: 'session_123')
        game_session.session_id = 'session_123'
        expect(game_session).not_to be_valid
        expect(game_session.errors[:session_id]).to include('is already taken')
      end
    end

    describe 'game_type validation' do
      it 'requires game_type to be present' do
        game_session.game_type = nil
        expect(game_session).not_to be_valid
        expect(game_session.errors[:game_type]).to include("can't be blank")
      end

      %w[prizewheel amidakuji].each do |game_type|
        it "allows #{game_type} game type" do
          game_session.game_type = game_type
          expect(game_session).to be_valid
        end
      end

      it 'rejects invalid game types' do
        game_session.game_type = 'invalid_game'
        expect(game_session).not_to be_valid
        expect(game_session.errors[:game_type]).to include('is not included in the list')
      end
    end

    describe 'status validation' do
      %w[pending active completed cancelled].each do |status|
        it "allows #{status} status" do
          game_session.status = status
          expect(game_session).to be_valid
        end
      end

      it 'rejects invalid status' do
        game_session.status = 'invalid_status'
        expect(game_session).not_to be_valid
        expect(game_session.errors[:status]).to include('is not included in the list')
      end
    end

    describe 'bet_amount validation' do
      it 'requires bet_amount to be present' do
        game_session.bet_amount = nil
        expect(game_session).not_to be_valid
        expect(game_session.errors[:bet_amount]).to include("can't be blank")
      end

      it 'requires bet_amount to be positive' do
        game_session.bet_amount = 0
        expect(game_session).not_to be_valid
        expect(game_session.errors[:bet_amount]).to include('must be greater than 0')
      end

      it 'requires bet_amount to be positive for negative values' do
        game_session.bet_amount = -10
        expect(game_session).not_to be_valid
        expect(game_session.errors[:bet_amount]).to include('must be greater than 0')
      end
    end

    describe 'win_amount validation' do
      it 'allows zero win_amount' do
        game_session.win_amount = 0
        expect(game_session).to be_valid
      end

      it 'allows positive win_amount' do
        game_session.win_amount = 100
        expect(game_session).to be_valid
      end

      it 'rejects negative win_amount' do
        game_session.win_amount = -10
        expect(game_session).not_to be_valid
        expect(game_session.errors[:win_amount]).to include('must be greater than or equal to 0')
      end
    end
  end

  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'callbacks' do
    describe 'before_create :generate_session_id' do
      it 'generates session_id when blank' do
        game_session = build(:game_session, session_id: nil)
        game_session.save!
        expect(game_session.session_id).to be_present
        expect(game_session.session_id).to start_with('session_')
      end

      it 'does not override existing session_id' do
        original_id = 'custom_session_123'
        game_session = build(:game_session, session_id: original_id)
        game_session.save!
        expect(game_session.session_id).to eq(original_id)
      end
    end

    describe 'before_save :set_timestamps' do
      it 'sets timestamps appropriately' do
        game_session = create(:game_session)
        expect(game_session.created_at).to be_present
        expect(game_session.updated_at).to be_present
      end
    end
  end

  describe 'scopes' do
    let!(:active_session) { create(:game_session, :active) }
    let!(:completed_session) { create(:game_session, :completed) }
    let!(:cancelled_session) { create(:game_session, :cancelled) }
    let!(:pending_session) { create(:game_session, status: 'pending') }
    let!(:prizewheel_session) { create(:game_session, game_type: 'prizewheel') }
    let!(:amidakuji_session) { create(:game_session, :amidakuji) }

    describe '.active' do
      it 'returns only active sessions' do
        expect(GameSession.active).to contain_exactly(active_session)
      end
    end

    describe '.completed' do
      it 'returns only completed sessions' do
        expect(GameSession.completed).to contain_exactly(completed_session)
      end
    end

    describe '.by_game_type' do
      it 'returns sessions of specified game type' do
        expect(GameSession.by_game_type('amidakuji')).to contain_exactly(amidakuji_session)
      end
    end

    describe '.recent' do
      it 'orders sessions by created_at desc' do
        expect(GameSession.recent.first).to eq(amidakuji_session)
      end
    end
  end

  describe 'instance methods' do
    let(:game_session) { create(:game_session, status: 'pending') }

    describe '#start!' do
      it 'marks session as active and sets started_at' do
        freeze_time = Time.current
        Timecop.freeze(freeze_time) do
          game_session.start!
          expect(game_session.status).to eq('active')
          expect(game_session.started_at).to eq(freeze_time)
        end
      end
    end

    describe '#complete!' do
      it 'marks session as completed and sets ended_at' do
        result_data = { 'winner_position' => 1, 'prize_pool' => 800.0 }
        freeze_time = Time.current
        
        Timecop.freeze(freeze_time) do
          game_session.complete!(result_data)
          expect(game_session.status).to eq('completed')
          expect(game_session.ended_at).to eq(freeze_time)
          expect(game_session.result).to eq(result_data)
        end
      end

      it 'works without result data' do
        freeze_time = Time.current
        
        Timecop.freeze(freeze_time) do
          game_session.complete!
          expect(game_session.status).to eq('completed')
          expect(game_session.ended_at).to eq(freeze_time)
          expect(game_session.result).to eq({})
        end
      end
    end

    describe '#cancel!' do
      it 'marks session as cancelled and sets ended_at' do
        reason = 'Insufficient players'
        freeze_time = Time.current
        
        Timecop.freeze(freeze_time) do
          game_session.cancel!(reason)
          expect(game_session.status).to eq('cancelled')
          expect(game_session.ended_at).to eq(freeze_time)
          expect(game_session.result['cancellation_reason']).to eq(reason)
        end
      end

      it 'works without reason' do
        freeze_time = Time.current
        
        Timecop.freeze(freeze_time) do
          game_session.cancel!
          expect(game_session.status).to eq('cancelled')
          expect(game_session.ended_at).to eq(freeze_time)
        end
      end
    end

    describe '#pending?' do
      it 'returns true for pending sessions' do
        expect(game_session.pending?).to be true
      end

      it 'returns false for non-pending sessions' do
        game_session.status = 'active'
        expect(game_session.pending?).to be false
      end
    end

    describe '#active?' do
      it 'returns true for active sessions' do
        game_session.status = 'active'
        expect(game_session.active?).to be true
      end

      it 'returns false for non-active sessions' do
        expect(game_session.active?).to be false
      end
    end

    describe '#completed?' do
      it 'returns true for completed sessions' do
        game_session.status = 'completed'
        expect(game_session.completed?).to be true
      end

      it 'returns false for non-completed sessions' do
        expect(game_session.completed?).to be false
      end
    end

    describe '#cancelled?' do
      it 'returns true for cancelled sessions' do
        game_session.status = 'cancelled'
        expect(game_session.cancelled?).to be true
      end

      it 'returns false for non-cancelled sessions' do
        expect(game_session.cancelled?).to be false
      end
    end

    describe '#duration' do
      it 'calculates duration for completed sessions' do
        start_time = 1.hour.ago
        end_time = Time.current
        
        game_session.update!(
          started_at: start_time,
          ended_at: end_time,
          status: 'completed'
        )
        
        expect(game_session.duration).to be_within(1.second).of(3600)
      end

      it 'returns nil for sessions without both timestamps' do
        expect(game_session.duration).to be_nil
      end
    end

    describe '#won?' do
      it 'returns true when win_amount is greater than bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 200)
        expect(game_session.won?).to be true
      end

      it 'returns false when win_amount equals bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 100)
        expect(game_session.won?).to be false
      end

      it 'returns false when win_amount is less than bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 50)
        expect(game_session.won?).to be false
      end
    end

    describe '#lost?' do
      it 'returns true when win_amount is less than bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 50)
        expect(game_session.lost?).to be true
      end

      it 'returns false when win_amount equals bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 100)
        expect(game_session.lost?).to be false
      end

      it 'returns false when win_amount is greater than bet_amount' do
        game_session.update!(bet_amount: 100, win_amount: 200)
        expect(game_session.lost?).to be false
      end
    end

    describe '#profit' do
      it 'calculates profit correctly' do
        game_session.update!(bet_amount: 100, win_amount: 200)
        expect(game_session.profit).to eq(100)
      end

      it 'returns negative profit for losses' do
        game_session.update!(bet_amount: 100, win_amount: 50)
        expect(game_session.profit).to eq(-50)
      end

      it 'returns zero for break-even' do
        game_session.update!(bet_amount: 100, win_amount: 100)
        expect(game_session.profit).to eq(0)
      end
    end
  end
end
