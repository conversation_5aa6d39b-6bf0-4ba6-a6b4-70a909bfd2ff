# Dashboard Service Updates Test Plan

## Overview
This document outlines the testing plan for the updated dashboard service that now follows the new deposit/withdrawal flow and includes username filtering for transactions.

## Changes Made

### 1. API Client Updates (`src/lib/api.ts`)
- ✅ Updated `createDeposit` and `createWithdrawal` methods to return both transaction and user data
- ✅ Modified API calls to match new manager service endpoints

### 2. Type Definitions (`src/types/index.ts`)
- ✅ Added `username` field to `TransactionFilters`
- ✅ Updated `Transaction` interface to use `transaction_type` instead of `type`
- ✅ Added new fields: `username`, `reference_id`, `reference_type`, `updated_at`
- ✅ Added `bet_reserved` to transaction types

### 3. Transaction Page (`src/app/transactions/page.tsx`)
- ✅ Added username filter input field
- ✅ Updated table columns to display username and user_id
- ✅ Changed from 4-column to 5-column filter layout
- ✅ Updated transaction type field name from `type` to `transaction_type`
- ✅ Added balance_after column to show post-transaction balance

### 4. User Detail Page (`src/app/users/[id]/page.tsx`)
- ✅ Updated deposit/withdrawal handlers to use new API response structure
- ✅ Added immediate balance updates from API response
- ✅ Enhanced success messages to include transaction IDs
- ✅ Updated transaction columns to use `transaction_type`

### 5. Form Components
- ✅ **DepositForm**: Added $10,000 per transaction limit validation
- ✅ **WithdrawForm**: Added $5,000 per withdrawal limit validation
- ✅ Enhanced error handling and validation

## Testing Checklist

### Transaction Filtering
- [ ] Test username filtering works correctly
- [ ] Test transaction type filtering includes new types (bet_reserved)
- [ ] Test date range filtering
- [ ] Test status filtering
- [ ] Test search functionality
- [ ] Test pagination with filters

### Deposit Functionality
- [ ] Test deposit form validation (amount > 0, description required)
- [ ] Test deposit amount limit ($10,000)
- [ ] Test successful deposit creates admin_deposit transaction
- [ ] Test user balance updates immediately
- [ ] Test transaction ID is displayed in success message
- [ ] Test error handling for insufficient permissions
- [ ] Test error handling for invalid amounts

### Withdrawal Functionality
- [ ] Test withdrawal form validation (amount > 0, description required)
- [ ] Test withdrawal amount limit ($5,000)
- [ ] Test insufficient balance validation
- [ ] Test successful withdrawal updates user balance
- [ ] Test transaction ID is displayed in success message
- [ ] Test error handling for insufficient balance
- [ ] Test error handling for invalid amounts

### Transaction Display
- [ ] Test transaction table shows username and user_id
- [ ] Test transaction type displays correctly (admin_deposit, withdrawal, etc.)
- [ ] Test balance_after column shows correct values
- [ ] Test transaction_id displays in monospace font
- [ ] Test transaction status badges show correct colors
- [ ] Test date formatting is correct

### API Integration
- [ ] Test API calls use correct endpoints (/transactions/deposit, /transactions/withdraw)
- [ ] Test API responses include both transaction and user data
- [ ] Test error responses are handled correctly
- [ ] Test authentication headers are included
- [ ] Test timeout handling

## Expected Behavior

### New Flow Compliance
1. **Deposits**: Should create `admin_deposit` type transactions
2. **Withdrawals**: Should validate sufficient balance server-side
3. **Transaction IDs**: Should follow new format (txn_userId_timestamp_hash)
4. **Balance Updates**: Should be atomic with transaction creation
5. **Error Handling**: Should provide clear validation messages

### Username Filtering
1. **Filter Input**: Should allow typing username to filter transactions
2. **Real-time Filtering**: Should update results as user types
3. **Case Sensitivity**: Should handle case-insensitive matching
4. **Clear Filter**: Should allow clearing the username filter

### Enhanced Display
1. **User Information**: Should show both username and user_id
2. **Transaction Types**: Should display all new transaction types
3. **Balance Information**: Should show balance after each transaction
4. **Transaction IDs**: Should be easily readable and copyable

## Success Criteria
- All existing functionality continues to work
- New username filtering works correctly
- Deposit/withdrawal follow new manager service flow
- Error handling provides clear feedback
- UI remains responsive and user-friendly
- Transaction data displays accurately

## Notes
- Ensure manager service is running and accessible
- Test with admin user account for deposit/withdrawal permissions
- Verify transaction limits are enforced both client and server-side
- Check that all transaction types display with appropriate colors
