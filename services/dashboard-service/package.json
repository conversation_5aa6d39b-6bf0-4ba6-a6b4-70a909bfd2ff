{"name": "xz-game-manager-dashboard", "version": "1.0.0", "description": "Next.js dashboard for XZ Game Manager Service", "private": true, "scripts": {"dev": "next dev --turbopack --port 3004", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}