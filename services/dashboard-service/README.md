# XZ Game Manager Dashboard

A modern Next.js dashboard for managing the XZ Game Manager Service. This dashboard provides a comprehensive interface for administrators to manage users, transactions, game sessions, rooms, and system settings.

## Features

- **User Management**: View, search, and manage user accounts with role-based access control
- **Transaction Monitoring**: Real-time transaction tracking and financial analytics
- **Game Session Management**: Monitor active games and session statistics
- **Room Administration**: Create and manage game rooms with custom configurations
- **Game Settings**: Configure game parameters and synchronize with the game service
- **Analytics Dashboard**: Charts and metrics for system performance monitoring
- **Authentication**: Secure JWT-based authentication with role-based permissions
- **Real-time Updates**: Live data updates and notifications
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS

## Technology Stack

- **Framework**: Next.js 15.3+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts
- **Icons**: Heroicons
- **HTTP Client**: Axios
- **Date Handling**: date-fns

## Project Structure

```
dashboard/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── login/             # Authentication pages
│   │   ├── users/             # User management pages
│   │   ├── transactions/      # Transaction pages
│   │   ├── game-sessions/     # Game session pages
│   │   ├── rooms/             # Room management pages
│   │   ├── analytics/         # Analytics pages
│   │   └── settings/          # Settings pages
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Basic UI components
│   │   └── layout/           # Layout components
│   ├── lib/                  # Utilities and configurations
│   │   ├── api.ts            # API client
│   │   └── utils.ts          # Utility functions
│   ├── hooks/                # Custom React hooks
│   ├── stores/               # Zustand state stores
│   │   ├── auth.ts           # Authentication store
│   │   └── dashboard.ts      # Dashboard store
│   └── types/                # TypeScript type definitions
├── public/                   # Static assets
├── .env.local               # Environment variables
├── package.json
├── tsconfig.json
├── tailwind.config.js
└── next.config.ts
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Running Manager Service API (Ruby on Rails)

### Installation

1. Navigate to the dashboard directory:
```bash
cd services/manager-service/dashboard
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Configure environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002
NEXT_PUBLIC_APP_NAME="XZ Game Manager"
NEXT_PUBLIC_APP_VERSION="1.0.0"
```

4. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Default Login

Use the following credentials to log in (ensure the Manager Service is running):
- **Username**: admin
- **Password**: password

## API Integration

The dashboard integrates with the Manager Service API running on port 3002. Key endpoints include:

- **Authentication**: `/auth/login`, `/auth/logout`, `/auth/me`
- **Users**: `/users`, `/users/:id`, `/users/:id/balance`
- **Transactions**: `/transactions`, `/transactions/stats`
- **Game Sessions**: `/game_sessions`
- **Rooms**: `/admin/rooms`
- **Settings**: `/admin/game-settings`

## Development

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Style

The project uses:
- ESLint for code linting
- TypeScript for type safety
- Prettier for code formatting (configured in ESLint)
- Tailwind CSS for styling

### Adding New Features

1. **Create Types**: Add TypeScript interfaces in `src/types/`
2. **API Integration**: Extend the API client in `src/lib/api.ts`
3. **State Management**: Create or extend Zustand stores in `src/stores/`
4. **UI Components**: Build reusable components in `src/components/`
5. **Pages**: Add new pages in `src/app/`

## Authentication & Authorization

The dashboard implements role-based access control:

- **Admin**: Full access to all features
- **Moderator**: Limited access to user and room management
- **Player**: Read-only access to personal data

Authentication is handled via JWT tokens stored in localStorage with automatic refresh.

## Deployment

### Production Build

```bash
npm run build
npm run start
```

### Docker Deployment

Create a Dockerfile in the dashboard directory:

```dockerfile
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=base /app/node_modules ./node_modules
COPY --from=build /app/.next ./.next
COPY --from=build /app/public ./public
COPY --from=build /app/package.json ./package.json

EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables

Production environment variables:

```env
NEXT_PUBLIC_API_BASE_URL=https://api.xzgame.com
NEXT_PUBLIC_APP_NAME="XZ Game Manager"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NODE_ENV=production
```

## Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for new features
3. Write tests for new components and utilities
4. Update documentation for significant changes
5. Ensure responsive design for mobile devices

## Troubleshooting

### Common Issues

1. **API Connection Failed**: Ensure the Manager Service is running on the correct port
2. **Authentication Errors**: Check JWT token expiration and API credentials
3. **Build Errors**: Verify all dependencies are installed and TypeScript types are correct
4. **Styling Issues**: Check Tailwind CSS configuration and class names

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

## License

This project is part of the XZ Game Manager system and follows the same licensing terms.
