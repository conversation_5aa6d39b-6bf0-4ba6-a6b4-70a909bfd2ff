import { NextRequest, NextResponse } from 'next/server';

// This API route redirects to the proper profile page
// It handles cases where someone might try to access /api/auth/me
export async function GET(request: NextRequest) {
  // Redirect to the profile page
  return NextResponse.redirect(new URL('/profile', request.url));
}

export async function POST(request: NextRequest) {
  // Redirect to the profile page
  return NextResponse.redirect(new URL('/profile', request.url));
}
