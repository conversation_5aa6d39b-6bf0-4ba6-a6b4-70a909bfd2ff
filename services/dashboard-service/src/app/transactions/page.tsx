'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { Transaction, TransactionFilters, PaginatedResponse } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import Table from '@/components/ui/Table';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  EyeIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

// Simple debounce function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const TransactionsPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [showFilters, setShowFilters] = useState(false);

  const [filters, setFilters] = useState<TransactionFilters>({
    page: 1,
    per_page: 25,
  });

  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 25,
    has_next: false,
    has_prev: false,
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchTransactions = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response: PaginatedResponse<Transaction> = await apiClient.getTransactions(filters);
      if (response.success && response.data) {
        setTransactions((response.data.transactions as Transaction[]) || []);
        setPagination(response.data.pagination);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch transactions';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchTransactions();
    }
  }, [isAuthenticated, fetchTransactions]);

  const handleFilterChange = (key: keyof TransactionFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Debounced search to avoid too many API calls
  const debouncedFilterChange = useCallback(
    debounce((key: keyof TransactionFilters, value: string | number) => {
      handleFilterChange(key, value);
    }, 500),
    []
  );

  const handleSearchChange = (value: string) => {
    debouncedFilterChange('search', value);
  };

  const handleUsernameChange = (value: string) => {
    debouncedFilterChange('username', value);
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRefresh = () => {
    fetchTransactions();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'deposit':
        return 'bg-green-100 text-green-800';
      case 'admin_deposit':
        return 'bg-blue-100 text-blue-800';
      case 'withdrawal':
        return 'bg-red-100 text-red-800';
      case 'bet_placed':
        return 'bg-orange-100 text-orange-800';
      case 'bet_won':
        return 'bg-emerald-100 text-emerald-800';
      case 'bet_lost':
        return 'bg-red-100 text-red-800';
      case 'adjustment':
        return 'bg-purple-100 text-purple-800';
      case 'refund':
        return 'bg-yellow-100 text-yellow-800';
      case 'bonus':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const columns = [
    {
      key: 'transaction_id',
      label: 'Transaction ID',
      render: (value: unknown) => (
        <span className="font-mono text-sm">{String(value)}</span>
      ),
    },
    {
      key: 'user',
      label: 'User',
      render: (value: unknown, item: Transaction) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">
            {item.user?.username || 'N/A'}
          </span>
          {item.user?.id && (
            <span className="text-xs text-gray-500 font-mono">{item.user.id}</span>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getTypeColor(String(value))}`}>
          {String(value).replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      render: (value: unknown) => (
        <span className="font-semibold">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'balance_after',
      label: 'Balance After',
      render: (value: unknown) => (
        <span className="text-sm text-gray-600">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      render: (value: unknown) => formatDate(String(value)),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: Transaction) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => console.log('View transaction:', item.id)}
          className="flex items-center space-x-1"
        >
          <EyeIcon className="h-4 w-4" />
          <span>View</span>
        </Button>
      ),
    },
  ];

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transactions</h1>
            <p className="text-gray-800">Monitor and manage financial transactions</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col space-y-4">
              {/* Main Search Bar */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by transaction ID, description, or username..."
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="w-full"
                  />
                </div>
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-600" />
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                      <Input
                        placeholder="Filter by username..."
                        onChange={(e) => handleUsernameChange(e.target.value)}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                      <select
                        value={filters.type || ''}
                        onChange={(e) => handleFilterChange('type', e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                      >
                        <option value="">All Types</option>
                        <option value="deposit">Deposit</option>
                        <option value="admin_deposit">Admin Deposit</option>
                        <option value="withdrawal">Withdrawal</option>
                        <option value="bet_placed">Bet Placed</option>
                        <option value="bet_won">Bet Won</option>
                        <option value="bet_lost">Bet Lost</option>
                        <option value="bet_reserved">Bet Reserved</option>
                        <option value="adjustment">Adjustment</option>
                        <option value="refund">Refund</option>
                        <option value="bonus">Bonus</option>
                        <option value="reconciliation">Reconciliation</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                      <select
                        value={filters.status || ''}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                      >
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="failed">Failed</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                      <input
                        type="date"
                        value={filters.start_date || ''}
                        onChange={(e) => handleFilterChange('start_date', e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                      <input
                        type="date"
                        value={filters.end_date || ''}
                        onChange={(e) => handleFilterChange('end_date', e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                      />
                    </div>
                  </div>

                  {/* Clear Filters Button */}
                  <div className="mt-4 flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setFilters({ page: 1, per_page: 25 })}
                      className="text-sm"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Transactions Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CurrencyDollarIcon className="h-5 w-5 mr-2" />
              Transactions ({pagination.total_count})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loading size="md" text="Loading transactions..." />
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table
                  data={transactions}
                  columns={columns}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  isLoading={isLoading}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default TransactionsPage;
