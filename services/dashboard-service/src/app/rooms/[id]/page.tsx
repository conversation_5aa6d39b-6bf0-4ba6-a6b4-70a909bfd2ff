'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { DetailedRoom, RoomPlayer, KickPlayerForm } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';
import Table from '@/components/ui/Table';
import {
  ArrowLeftIcon,
  ArrowPathIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  UserMinusIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

const RoomDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const roomId = params.id as string;
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();

  const [room, setRoom] = useState<DetailedRoom | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isKicking, setIsKicking] = useState<string | null>(null);
  const [showKickModal, setShowKickModal] = useState<RoomPlayer | null>(null);
  const [kickReason, setKickReason] = useState('');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchRoomDetails = useCallback(async () => {
    if (!roomId) return;

    setIsLoading(true);
    setError(null);

    try {
      const roomData = await apiClient.getDetailedRoom(roomId);
      setRoom(roomData);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch room details';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [roomId]);

  useEffect(() => {
    if (isAuthenticated && roomId) {
      fetchRoomDetails();
    }
  }, [isAuthenticated, roomId, fetchRoomDetails]);

  const handleKickPlayer = async (player: RoomPlayer) => {
    if (!user?.role || !['admin', 'moderator'].includes(user.role)) {
      setError('You do not have permission to kick players');
      return;
    }

    setIsKicking(player.user_id);
    try {
      const kickData: KickPlayerForm = {
        user_id: player.user_id,
        reason: kickReason || 'Kicked by administrator',
        notify_player: true
      };

      await apiClient.kickPlayer(roomId, kickData);
      setShowKickModal(null);
      setKickReason('');
      fetchRoomDetails(); // Refresh room data
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to kick player';
      setError(errorMessage);
    } finally {
      setIsKicking(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'playing':
        return 'bg-green-100 text-green-800';
      case 'finished':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlayerStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm:ss');
    } catch {
      return 'Invalid date';
    }
  };

  const playerColumns = [
    {
      key: 'username',
      label: 'Player',
      render: (value: unknown, item: RoomPlayer) => (
        <div className="flex items-center space-x-2">
          <span className="font-medium">{String(value)}</span>
          {item.is_ready && (
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Ready
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'bet_amount',
      label: 'Bet Amount',
      render: (value: unknown) => (
        <span className="font-semibold">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'position',
      label: 'Position',
      render: (value: unknown) => (
        <span>{value ? `#${value}` : 'N/A'}</span>
      ),
    },
    {
      key: 'session_count',
      label: 'Sessions',
      render: (value: unknown) => (
        <span className="text-sm text-gray-600">{value || 1}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getPlayerStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'joined_at',
      label: 'Joined At',
      render: (value: unknown) => formatDate(String(value)),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: RoomPlayer) => (
        <div className="flex items-center space-x-2">
          {(user?.role === 'admin' || user?.role === 'moderator') && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowKickModal(item)}
              disabled={isKicking === item.user_id}
              className="flex items-center space-x-1 text-red-600 hover:text-red-700"
            >
              <UserMinusIcon className="h-4 w-4" />
              <span>Kick</span>
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center py-8">
          <Loading size="lg" text="Loading room details..." />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/rooms')}
              className="flex items-center space-x-2"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Back to Rooms</span>
            </Button>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!room) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/rooms')}
              className="flex items-center space-x-2"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Back to Rooms</span>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-gray-600">Room not found</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/rooms')}
              className="flex items-center space-x-2"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Back to Rooms</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{room.name}</h1>
              <p className="text-gray-800">Room Details & Player Management</p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={fetchRoomDetails}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
        </div>

        {/* Room Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UsersIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Players</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {room.current_players}/{room.max_players}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Prize Pool</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatAmount(room.prize_pool)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Status</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(room.status)}`}>
                    {room.status}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Bet Amount</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatAmount(room.bet_amount)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Room Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Room Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Game Type</p>
                    <p className="text-sm text-gray-900 capitalize">{room.game_type}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Currency</p>
                    <p className="text-sm text-gray-900">{room.currency}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Created By</p>
                    <p className="text-sm text-gray-900">{room.creator?.username || 'Unknown'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Created At</p>
                    <p className="text-sm text-gray-900">{formatDate(room.created_at)}</p>
                  </div>
                </div>
                {room.configuration && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Game Configuration</p>
                    <div className="bg-gray-50 rounded-md p-3">
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="font-medium">Duration:</span> {room.configuration.game_duration}s
                        </div>
                        {room.configuration.wheel_sections && (
                          <div>
                            <span className="font-medium">Wheel Sections:</span> {room.configuration.wheel_sections}
                          </div>
                        )}
                        {room.configuration.spin_duration && (
                          <div>
                            <span className="font-medium">Spin Duration:</span> {room.configuration.spin_duration}ms
                          </div>
                        )}
                        {room.configuration.rows_per_player && (
                          <div>
                            <span className="font-medium">Rows per Player:</span> {room.configuration.rows_per_player}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Game Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {room.game_sessions && room.game_sessions.length > 0 ? (
                  room.game_sessions.slice(0, 5).map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div>
                        <p className="text-sm font-medium text-gray-900">Session {session.session_id}</p>
                        <p className="text-xs text-gray-600">
                          {session.status} • {formatAmount(session.bet_amount)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {formatAmount(session.win_amount)}
                        </p>
                        <p className="text-xs text-gray-600">
                          {session.ended_at ? formatDate(session.ended_at) : 'In Progress'}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-600">No game sessions yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Players Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UsersIcon className="h-5 w-5 mr-2" />
              Players in Room ({room.players?.length || 0})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {room.players && room.players.length > 0 ? (
              <Table
                data={room.players}
                columns={playerColumns}
                pagination={{
                  current_page: 1,
                  total_pages: 1,
                  total_count: room.players.length,
                  per_page: room.players.length,
                  has_next: false,
                  has_prev: false,
                }}
                onPageChange={() => {}}
                isLoading={false}
              />
            ) : (
              <div className="text-center py-8">
                <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No players</h3>
                <p className="mt-1 text-sm text-gray-600">This room is currently empty.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Kick Player Modal */}
        {showKickModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="ml-4 text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Kick Player
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        Are you sure you want to kick <strong>{showKickModal.username}</strong> from this room?
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason (optional)
                  </label>
                  <textarea
                    value={kickReason}
                    onChange={(e) => setKickReason(e.target.value)}
                    placeholder="Enter reason for kicking this player..."
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    rows={3}
                  />
                </div>
                <div className="flex justify-end space-x-3 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowKickModal(null);
                      setKickReason('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => handleKickPlayer(showKickModal)}
                    isLoading={isKicking === showKickModal.user_id}
                    disabled={isKicking === showKickModal.user_id}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Kick Player
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default RoomDetailPage;