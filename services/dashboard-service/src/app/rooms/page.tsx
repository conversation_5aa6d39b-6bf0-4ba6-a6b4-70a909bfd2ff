'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { Room, RoomFilters, PaginatedResponse, CreateRoomForm } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import Table from '@/components/ui/Table';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  EyeIcon,
  PlusIcon,
  TrashIcon,
  BuildingOfficeIcon,
  UsersIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

const RoomsPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const [filters, setFilters] = useState<RoomFilters>({
    page: 1,
    per_page: 25,
  });

  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 25,
    has_next: false,
    has_prev: false,
  });

  const [createForm, setCreateForm] = useState<CreateRoomForm>({
    name: '',
    game_type: 'prizewheel',
    max_players: 4,
    bet_amount: 10,
    currency: 'USD',
    game_duration: 300,
    is_private: false,
    game_specific_config: {},
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchRooms = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response: PaginatedResponse<Room> = await apiClient.getRooms(filters);
      if (response.success && response.data) {
        setRooms((response.data.rooms as Room[]) || []);
        setPagination(response.data.pagination);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch rooms';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchRooms();
    }
  }, [isAuthenticated, fetchRooms]);

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (key: keyof RoomFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRefresh = () => {
    fetchRooms();
  };

  const handleCreateRoom = async () => {
    if (!user?.role || !['admin', 'moderator'].includes(user.role)) {
      setError('You do not have permission to create rooms');
      return;
    }

    setIsCreating(true);
    try {
      await apiClient.createRoom(createForm);
      setShowCreateForm(false);
      setCreateForm({
        name: '',
        game_type: 'prizewheel',
        max_players: 4,
        bet_amount: 10,
        currency: 'USD',
        game_duration: 300,
        is_private: false,
        game_specific_config: {},
      });
      fetchRooms();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create room';
      setError(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteRoom = async (roomId: string) => {
    if (!user?.role || user.role !== 'admin') {
      setError('You do not have permission to delete rooms');
      return;
    }

    if (!confirm('Are you sure you want to delete this room?')) {
      return;
    }

    setIsDeleting(roomId);
    try {
      await apiClient.deleteRoom(roomId, 'Administrative deletion');
      fetchRooms();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete room';
      setError(errorMessage);
    } finally {
      setIsDeleting(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'playing':
        return 'bg-green-100 text-green-800';
      case 'finished':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGameTypeColor = (gameType: string) => {
    switch (gameType) {
      case 'prizewheel':
        return 'bg-purple-100 text-purple-800';
      case 'amidakuji':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Room Name',
      render: (value: unknown) => (
        <span className="font-medium">{String(value)}</span>
      ),
    },
    {
      key: 'game_type',
      label: 'Game Type',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getGameTypeColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'players',
      label: 'Players',
      render: (_: unknown, item: Room) => (
        <div className="flex items-center space-x-1">
          <UsersIcon className="h-4 w-4 text-gray-600" />
          <span>{item.current_players}/{item.max_players}</span>
        </div>
      ),
    },
    {
      key: 'bet_amount',
      label: 'Bet Amount',
      render: (value: unknown) => (
        <span className="font-semibold">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'prize_pool',
      label: 'Prize Pool',
      render: (value: unknown) => (
        <span className="font-semibold text-green-600">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value: unknown) => formatDate(String(value)),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: Room) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/rooms/${item.id}`)}
            className="flex items-center space-x-1"
          >
            <EyeIcon className="h-4 w-4" />
            <span>Details</span>
          </Button>
          {user?.role === 'admin' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteRoom(item.id)}
              disabled={isDeleting === item.id}
              className="flex items-center space-x-1 text-red-600 hover:text-red-700"
            >
              <TrashIcon className="h-4 w-4" />
              <span>Delete</span>
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Rooms</h1>
            <p className="text-gray-800">Manage game rooms and sessions</p>
          </div>
          <div className="flex items-center space-x-3">
            {(user?.role === 'admin' || user?.role === 'moderator') && (
              <Button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Create Room</span>
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>
        </div>

        {/* Create Room Form */}
        {showCreateForm && (
          <Card>
            <CardHeader>
              <CardTitle>Create New Room</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Room Name</label>
                  <Input
                    value={createForm.name}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter room name..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Game Type</label>
                  <select
                    value={createForm.game_type}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, game_type: e.target.value as 'prizewheel' | 'amidakuji' }))}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="prizewheel">Prize Wheel</option>
                    <option value="amidakuji">Amidakuji</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Max Players</label>
                  <Input
                    type="number"
                    min="2"
                    max="10"
                    value={createForm.max_players}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, max_players: parseInt(e.target.value) }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Bet Amount</label>
                  <Input
                    type="number"
                    min="1"
                    step="0.01"
                    value={createForm.bet_amount}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, bet_amount: parseFloat(e.target.value) }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Game Duration (seconds)</label>
                  <Input
                    type="number"
                    min="60"
                    max="3600"
                    value={createForm.game_duration}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, game_duration: parseInt(e.target.value) }))}
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_private"
                    checked={createForm.is_private}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, is_private: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                  <label htmlFor="is_private" className="ml-2 block text-sm text-gray-900">
                    Private Room
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateRoom}
                  isLoading={isCreating}
                  disabled={!createForm.name || isCreating}
                >
                  Create Room
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search rooms..."
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full"
                  />
                </div>
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-600" />
              </div>

              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Game Type</label>
                    <select
                      value={filters.game_type || ''}
                      onChange={(e) => handleFilterChange('game_type', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="">All Games</option>
                      <option value="prizewheel">Prize Wheel</option>
                      <option value="amidakuji">Amidakuji</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={filters.status || ''}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="">All Statuses</option>
                      <option value="waiting">Waiting</option>
                      <option value="playing">Playing</option>
                      <option value="finished">Finished</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Created After</label>
                    <input
                      type="date"
                      value={filters.created_after || ''}
                      onChange={(e) => handleFilterChange('created_after', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Created Before</label>
                    <input
                      type="date"
                      value={filters.created_before || ''}
                      onChange={(e) => handleFilterChange('created_before', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Rooms Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BuildingOfficeIcon className="h-5 w-5 mr-2" />
              Rooms ({pagination.total_count})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loading size="md" text="Loading rooms..." />
              </div>
            ) : (
              <Table
                data={rooms}
                columns={columns}
                pagination={pagination}
                onPageChange={handlePageChange}
                isLoading={isLoading}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default RoomsPage;
