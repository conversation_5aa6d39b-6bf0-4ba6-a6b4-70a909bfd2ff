'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { useDashboardStats, useDashboardActions } from '@/stores/dashboard';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Loading from '@/components/ui/Loading';
import { formatCurrency, formatNumber } from '@/lib/utils';
import {
  UsersIcon,
  CreditCardIcon,
  PuzzlePieceIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const DashboardPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { stats, isLoading, error } = useDashboardStats();
  const { fetchStats } = useDashboardActions();

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchStats();
    }
  }, [isAuthenticated, fetchStats]);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-800">Welcome to the XZ Game Manager dashboard</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <Loading size="lg" text="Loading dashboard stats..." />
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">Error loading dashboard: {error}</p>
          </div>
        ) : stats ? (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <UsersIcon className="h-4 w-4 text-gray-700" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(stats.users.total)}</div>
                  <p className="text-xs text-gray-800">
                    {formatNumber(stats.users.active)} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
                  <CreditCardIcon className="h-4 w-4 text-gray-700" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(stats.transactions.total_volume)}</div>
                  <p className="text-xs text-gray-800">
                    {formatCurrency(stats.transactions.daily_volume)} today
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Games</CardTitle>
                  <PuzzlePieceIcon className="h-4 w-4 text-gray-700" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(stats.games.active_sessions)}</div>
                  <p className="text-xs text-gray-800">
                    {formatNumber(stats.games.completed_today)} completed today
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Status</CardTitle>
                  <ChartBarIcon className="h-4 w-4 text-gray-700" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold capitalize">{stats.system.status}</div>
                  <p className="text-xs text-gray-800">
                    Uptime: {stats.system.uptime}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <button
                      onClick={() => router.push('/users')}
                      className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium">Manage Users</div>
                      <div className="text-sm text-gray-800">View and manage user accounts</div>
                    </button>
                    <button
                      onClick={() => router.push('/transactions')}
                      className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium">View Transactions</div>
                      <div className="text-sm text-gray-800">Monitor financial transactions</div>
                    </button>
                    <button
                      onClick={() => router.push('/rooms')}
                      className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium">Manage Rooms</div>
                      <div className="text-sm text-gray-800">Create and manage game rooms</div>
                    </button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm text-gray-800">
                      No recent activity data available. This would show recent user actions,
                      transactions, and system events.
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        ) : null}
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;
