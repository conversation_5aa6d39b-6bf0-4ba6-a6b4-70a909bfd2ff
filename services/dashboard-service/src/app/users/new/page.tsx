'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { CreateUserForm } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Alert from '@/components/ui/Alert';
import { ArrowLeftIcon, UserPlusIcon } from '@heroicons/react/24/outline';

export default function NewUserPage() {
  const router = useRouter();
  const { user: currentUser, isAuthenticated, isLoading } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<CreateUserForm>({
    username: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'player',
    balance: 0
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username = 'Username can only contain letters, numbers, and underscores';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.password_confirmation) {
      errors.password_confirmation = 'Passwords do not match';
    }

    if (formData.balance !== undefined && formData.balance < 0) {
      errors.balance = 'Balance cannot be negative';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser?.role || currentUser.role !== 'admin') {
      setError('Access Denied - You do not have permission to create users.');
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsCreating(true);
    setError(null);
    setSuccess(null);

    try {
      const newUser = await apiClient.createUser(formData);

      setSuccess('User created successfully');

      // Clear form
      setFormData({
        username: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'player',
        balance: 0
      });
      setFormErrors({});

      // Redirect to user detail page after a short delay
      setTimeout(() => {
        router.push(`/users/${newUser.id}`);
      }, 1500);

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create user';
      setError(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };

  const handleInputChange = (field: keyof CreateUserForm, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Check if user has admin permissions
  if (!currentUser?.role || currentUser.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <UserPlusIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-700 mb-4">You do not have permission to create users.</p>
            <Button onClick={() => router.push('/users')}>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/users')}
            className="flex items-center space-x-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New User</h1>
            <p className="text-gray-700">Create a new user account</p>
          </div>
        </div>

        {/* Success Message */}
        {success && (
          <Alert variant="success">
            {success}
          </Alert>
        )}

        {/* Error Message */}
        {error && (
          <Alert
            variant={error.startsWith('Access Denied') ? 'access-denied' : 'error'}
            title={error.startsWith('Access Denied') ? 'Access Denied' : 'Error'}
          >
            {error.startsWith('Access Denied') ?
              error.replace('Access Denied - ', '') :
              error
            }
          </Alert>
        )}

        {/* Create User Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <UserPlusIcon className="h-5 w-5" />
              <span>User Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Username"
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  error={formErrors.username}
                  placeholder="Enter username"
                  required
                />

                <Input
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  error={formErrors.email}
                  placeholder="Enter email address"
                  required
                />

                <Input
                  label="Password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  error={formErrors.password}
                  placeholder="Enter password"
                  required
                />

                <Input
                  label="Confirm Password"
                  type="password"
                  value={formData.password_confirmation}
                  onChange={(e) => handleInputChange('password_confirmation', e.target.value)}
                  error={formErrors.password_confirmation}
                  placeholder="Confirm password"
                  required
                />

                <Input
                  label="Initial Balance"
                  type="number"
                  value={formData.balance?.toString() || '0'}
                  onChange={(e) => handleInputChange('balance', parseFloat(e.target.value) || 0)}
                  error={formErrors.balance}
                  placeholder="Enter initial balance"
                  min="0"
                  step="0.01"
                />

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => handleInputChange('role', e.target.value)}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="player">Player</option>
                    <option value="moderator">Moderator</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/users')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  isLoading={isCreating}
                  disabled={isCreating}
                  className="flex items-center space-x-2"
                >
                  <UserPlusIcon className="h-4 w-4" />
                  <span>Create User</span>
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
