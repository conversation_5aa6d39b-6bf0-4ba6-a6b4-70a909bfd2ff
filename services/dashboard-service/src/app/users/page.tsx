'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import DataTable, { TableColumn } from '@/components/ui/Table';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Badge from '@/components/ui/Badge';
import Loading from '@/components/ui/Loading';
import { apiClient } from '@/lib/api';
import { formatDate, formatCurrency, getStatusColor, getRoleColor } from '@/lib/utils';
import { User, UserFilters } from '@/types';
import { MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/24/outline';

// Utility function for debouncing
function debounce<T extends (...args: unknown[]) => unknown>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout;
  return ((...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
}

const UsersPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    per_page: 25,
  });
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 25,
    has_next: false,
    has_prev: false,
  });

  // Define table columns
  const columns: TableColumn<User>[] = [
    {
      key: 'username',
      label: 'User',
      render: (value, user) => (
        <div className="font-medium">{user.username}</div>
      )
    },
    {
      key: 'email',
      label: 'Email'
    },
    {
      key: 'role',
      label: 'Role',
      render: (value, user) => (
        <Badge className={getRoleColor(user.role)}>
          {user.role}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value, user) => (
        <Badge className={getStatusColor(user.status)}>
          {user.status}
        </Badge>
      )
    },
    {
      key: 'balance',
      label: 'Balance',
      render: (value, user) => formatCurrency(user.balance)
    },
    {
      key: 'last_login_at',
      label: 'Last Login',
      render: (value, user) => user.last_login_at ? formatDate(user.last_login_at) : 'Never'
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value, user) => formatDate(user.created_at)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value, user) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/users/${user.id}`)}
        >
          View
        </Button>
      )
    }
  ];

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.getUsers(filters);
      if (response.success && response.data) {
        setUsers((response.data.users as User[]) || []);
        setPagination(response.data.pagination);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch users';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchUsers();
    }
  }, [isAuthenticated, fetchUsers]);

  const handleFilterChange = useCallback((key: keyof UserFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  }, []);

  // Debounced search to avoid too many API calls
  const debouncedFilterChange = useCallback(
    debounce((key: keyof UserFilters, value: string | number) => {
      handleFilterChange(key, value);
    }, 500),
    [handleFilterChange]
  );

  const handleSearchChange = (value: string) => {
    debouncedFilterChange('search', value);
  };

  const handleStatusFilter = (status: string) => {
    handleFilterChange('status', status || undefined);
  };

  const handleRoleFilter = (role: string) => {
    handleFilterChange('role', role || undefined);
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Users</h1>
            <p className="text-gray-800">Manage user accounts and permissions</p>
          </div>
          <Button onClick={() => router.push('/users/new')}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-600" />
                  <Input
                    placeholder="Search users..."
                    className="pl-10"
                    onChange={(e) => handleSearchChange(e.target.value)}
                  />
                </div>
              </div>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                onChange={(e) => handleStatusFilter(e.target.value)}
                value={filters.status || ''}
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="banned">Banned</option>
              </select>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                onChange={(e) => handleRoleFilter(e.target.value)}
                value={filters.role || ''}
              >
                <option value="">All Roles</option>
                <option value="player">Player</option>
                <option value="moderator">Moderator</option>
                <option value="admin">Admin</option>
              </select>
            </div>

            {/* Error State */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            {/* Users DataTable */}
            <DataTable
              data={users}
              columns={columns}
              pagination={pagination}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              emptyMessage="No users found"
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default UsersPage;
