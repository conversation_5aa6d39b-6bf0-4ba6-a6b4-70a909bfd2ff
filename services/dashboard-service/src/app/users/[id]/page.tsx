'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { User, Transaction, GameSession, DepositForm, WithdrawForm } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import Table from '@/components/ui/Table';
import DepositFormComponent from '@/components/forms/DepositForm';
import WithdrawFormComponent from '@/components/forms/WithdrawForm';
import {
  UserIcon,
  ArrowLeftIcon,
  PencilIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  MinusIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

const UserDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const userId = params?.id as string;
  const { user: currentUser, isAuthenticated, isLoading: authLoading } = useAuth();

  const [user, setUser] = useState<User | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [gameSessions, setGameSessions] = useState<GameSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'sessions'>('overview');
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDepositForm, setShowDepositForm] = useState(false);
  const [showWithdrawForm, setShowWithdrawForm] = useState(false);
  const [isProcessingTransaction, setIsProcessingTransaction] = useState(false);

  const [editForm, setEditForm] = useState({
    balance: 0,
    status: 'active' as 'active' | 'inactive' | 'suspended' | 'banned',
    role: 'player' as 'player' | 'admin' | 'moderator',
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    if (isAuthenticated && userId) {
      fetchUserDetails();
    }
  }, [isAuthenticated, userId]);

  const fetchUserDetails = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch user details
      const user = await apiClient.getUser(userId);
      setUser(user);
      setEditForm({
        balance: user.balance,
        status: user.status,
        role: user.role,
      });

      // Fetch user transactions
      const transactionsResponse = await apiClient.getTransactions({
        user_id: userId,
        per_page: 10
      });
      if (transactionsResponse.success && transactionsResponse.data?.transactions) {
        setTransactions(transactionsResponse.data.transactions);
      }

      // Fetch user game sessions
      const sessionsResponse = await apiClient.getGameSessions({
        user_id: userId,
        per_page: 10
      });
      if (sessionsResponse.success && sessionsResponse.data?.game_sessions) {
        setGameSessions(sessionsResponse.data.game_sessions);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user details';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!currentUser?.role || currentUser.role !== 'admin') {
      setError('You do not have permission to update users');
      return;
    }

    setIsUpdating(true);
    setError(null);
    setSuccess(null);

    try {
      await apiClient.updateUser(userId, editForm);
      setSuccess('User updated successfully');
      setShowEditForm(false);
      fetchUserDetails();

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user';
      setError(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeposit = async (depositData: DepositForm) => {
    setIsProcessingTransaction(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await apiClient.createDeposit(depositData);
      setSuccess(`Deposit processed successfully. Transaction ID: ${result.transaction.transaction_id}`);
      setShowDepositForm(false);

      // Update user balance immediately with the new data
      if (user && result.user) {
        setUser({ ...user, balance: result.user.balance });
      }

      // Refresh user details to get latest data
      fetchUserDetails();

      // Clear success message after 5 seconds
      setTimeout(() => setSuccess(null), 5000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process deposit';
      setError(errorMessage);
    } finally {
      setIsProcessingTransaction(false);
    }
  };

  const handleWithdraw = async (withdrawData: WithdrawForm) => {
    setIsProcessingTransaction(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await apiClient.createWithdrawal(withdrawData);
      setSuccess(`Withdrawal processed successfully. Transaction ID: ${result.transaction.transaction_id}`);
      setShowWithdrawForm(false);

      // Update user balance immediately with the new data
      if (user && result.user) {
        setUser({ ...user, balance: result.user.balance });
      }

      // Refresh user details to get latest data
      fetchUserDetails();

      // Clear success message after 5 seconds
      setTimeout(() => setSuccess(null), 5000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process withdrawal';
      setError(errorMessage);
    } finally {
      setIsProcessingTransaction(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      case 'banned':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'moderator':
        return 'bg-yellow-100 text-yellow-800';
      case 'player':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    try {
      return format(new Date(dateString), 'PPpp');
    } catch {
      return 'Invalid date';
    }
  };

  const transactionColumns = [
    {
      key: 'type',
      label: 'Type',
      render: (value: unknown) => {
        const type = String(value);
        const displayType = type.replace('_', ' ');
        const colorClass = type === 'admin_deposit' ? 'bg-blue-100 text-blue-800' :
                          type === 'deposit' ? 'bg-green-100 text-green-800' :
                          type === 'withdrawal' ? 'bg-red-100 text-red-800' :
                          type === 'bet_placed' ? 'bg-orange-100 text-orange-800' :
                          type === 'bet_won' ? 'bg-emerald-100 text-emerald-800' :
                          type === 'bet_lost' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800';
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${colorClass}`}>
            {displayType}
          </span>
        );
      },
    },
    {
      key: 'amount',
      label: 'Amount',
      render: (value: unknown) => formatAmount(Number(value)),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      render: (value: unknown) => format(new Date(String(value)), 'MMM dd, HH:mm'),
    },
  ];

  const sessionColumns = [
    {
      key: 'game_type',
      label: 'Game',
      render: (value: unknown) => (
        <span className="capitalize">{String(value)}</span>
      ),
    },
    {
      key: 'bet_amount',
      label: 'Bet',
      render: (value: unknown) => formatAmount(Number(value)),
    },
    {
      key: 'win_amount',
      label: 'Win',
      render: (value: unknown, item: GameSession) => {
        const winAmount = Number(value);
        const betAmount = item.bet_amount;
        const isWin = winAmount > betAmount;
        return (
          <span className={`font-semibold ${isWin ? 'text-green-600' : 'text-red-600'}`}>
            {formatAmount(winAmount)}
          </span>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className="capitalize">{String(value)}</span>
      ),
    },
    {
      key: 'started_at',
      label: 'Date',
      render: (value: unknown) => format(new Date(String(value)), 'MMM dd, HH:mm'),
    },
  ];

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-96 flex items-center justify-center">
          <Loading size="lg" text="Loading user details..." />
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">User Not Found</h3>
            <p className="text-gray-600 mb-4">The requested user could not be found.</p>
            <Button onClick={() => router.push('/users')}>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/users')}
              className="flex items-center space-x-2"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{user.username}</h1>
              <p className="text-gray-600">User Details</p>
            </div>
          </div>
          {currentUser?.role === 'admin' && (
            <Button
              onClick={() => setShowEditForm(!showEditForm)}
              className="flex items-center space-x-2"
            >
              <PencilIcon className="h-4 w-4" />
              <span>Edit User</span>
            </Button>
          )}
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <p className="ml-3 text-sm text-green-600">{success}</p>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XCircleIcon className="h-5 w-5 text-red-400" />
              <p className="ml-3 text-sm text-red-600">{error}</p>
            </div>
          </div>
        )}

        {/* Edit Form */}
        {showEditForm && currentUser?.role === 'admin' && (
          <Card>
            <CardHeader>
              <CardTitle>Edit User</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Balance</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={editForm.balance}
                    onChange={(e) => setEditForm(prev => ({ ...prev, balance: parseFloat(e.target.value) }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={editForm.status}
                    onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value as typeof editForm.status }))}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                    <option value="banned">Banned</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <select
                    value={editForm.role}
                    onChange={(e) => setEditForm(prev => ({ ...prev, role: e.target.value as typeof editForm.role }))}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="player">Player</option>
                    <option value="moderator">Moderator</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowEditForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateUser}
                  isLoading={isUpdating}
                  disabled={isUpdating}
                >
                  Update User
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Deposit Form */}
        {showDepositForm && currentUser?.role === 'admin' && user && (
          <DepositFormComponent
            userId={user.id}
            username={user.username}
            onSubmit={handleDeposit}
            onCancel={() => setShowDepositForm(false)}
            isLoading={isProcessingTransaction}
          />
        )}

        {/* Withdraw Form */}
        {showWithdrawForm && currentUser?.role === 'admin' && user && (
          <WithdrawFormComponent
            userId={user.id}
            username={user.username}
            currentBalance={user.balance}
            onSubmit={handleWithdraw}
            onCancel={() => setShowWithdrawForm(false)}
            isLoading={isProcessingTransaction}
          />
        )}

        {/* User Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserIcon className="h-5 w-5 mr-2" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Username</span>
                <span className="text-sm text-gray-900">{user.username}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Email</span>
                <span className="text-sm text-gray-900">{user.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">User ID</span>
                <span className="text-sm text-gray-900 font-mono">{user.id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Role</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getRoleColor(user.role)}`}>
                  {user.role}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Status</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(user.status)}`}>
                  {user.status}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                  Financial Information
                </div>
                {currentUser?.role === 'admin' && (
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => setShowDepositForm(true)}
                      className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
                    >
                      <PlusIcon className="h-4 w-4" />
                      <span>Deposit</span>
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setShowWithdrawForm(true)}
                      className="flex items-center space-x-1 bg-red-600 hover:bg-red-700"
                    >
                      <MinusIcon className="h-4 w-4" />
                      <span>Withdraw</span>
                    </Button>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <p className="text-sm font-medium text-gray-500">Current Balance</p>
                <p className="text-2xl font-bold text-green-600">{formatAmount(user.balance)}</p>
              </div>
              {user.statistics && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Bet</span>
                    <span className="text-sm text-gray-900">{formatAmount(user.statistics.total_bet)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Won</span>
                    <span className="text-sm text-gray-900">{formatAmount(user.statistics.total_won)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Net P&L</span>
                    <span className={`text-sm font-semibold ${user.statistics.total_won - user.statistics.total_bet >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatAmount(user.statistics.total_won - user.statistics.total_bet)}
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Activity Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                Activity Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Last Login</span>
                <span className="text-sm text-gray-900">{formatDate(user.last_login_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Account Created</span>
                <span className="text-sm text-gray-900">{formatDate(user.created_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Last Updated</span>
                <span className="text-sm text-gray-900">{formatDate(user.updated_at)}</span>
              </div>
              {user.statistics && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Games</span>
                    <span className="text-sm text-gray-900">{user.statistics.total_games}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Games Won</span>
                    <span className="text-sm text-gray-900">{user.statistics.games_won}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">Win Rate</span>
                    <span className="text-sm text-gray-900">{(user.statistics.win_rate * 100).toFixed(1)}%</span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'transactions', name: 'Recent Transactions', count: transactions.length },
              { id: 'sessions', name: 'Recent Sessions', count: gameSessions.length },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as typeof activeTab)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'transactions' && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <Table
                  data={transactions}
                  columns={transactionColumns}
                  pagination={{
                    current_page: 1,
                    total_pages: 1,
                    total_count: transactions.length,
                    per_page: 10,
                    has_next: false,
                    has_prev: false,
                  }}
                  onPageChange={() => {}}
                  isLoading={false}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No transactions found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {activeTab === 'sessions' && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Game Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              {gameSessions.length > 0 ? (
                <Table
                  data={gameSessions}
                  columns={sessionColumns}
                  pagination={{
                    current_page: 1,
                    total_pages: 1,
                    total_count: gameSessions.length,
                    per_page: 10,
                    has_next: false,
                    has_prev: false,
                  }}
                  onPageChange={() => {}}
                  isLoading={false}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No game sessions found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default UserDetailPage;
