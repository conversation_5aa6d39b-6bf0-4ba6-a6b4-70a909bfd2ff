'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import {
  CogIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface GameSettings {
  [key: string]: unknown;
}

const SettingsPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedGameType, setSelectedGameType] = useState<string>('prizewheel');

  const [prizewheelSettings, setPrizewheelSettings] = useState<GameSettings>({
    min_players: 2,
    max_players: 8,
    min_bet: 1,
    max_bet: 1000,
    house_edge: 0.05,
    waiting_timeout: 300,
    playing_timeout: 600,
    wheel_sections: 8,
    spin_duration: 5,
    animation_steps: 100,
  });

  const [amidakujiSettings, setAmidakujiSettings] = useState<GameSettings>({
    min_players: 2,
    max_players: 6,
    min_bet: 1,
    max_bet: 500,
    house_edge: 0.03,
    waiting_timeout: 300,
    playing_timeout: 900,
    rows_per_player: 10,
    line_probability: 0.3,
    animation_duration: 3,
  });

  const [globalSettings, setGlobalSettings] = useState<GameSettings>({
    maintenance_mode: false,
    max_concurrent_games: 100,
    default_currency: 'USD',
    session_timeout: 1800,
    max_bet_multiplier: 10,
    min_balance_threshold: 0,
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    if (isAuthenticated && user?.role === 'admin') {
      fetchSettings();
    }
  }, [isAuthenticated, user]);

  const fetchSettings = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch settings for each game type
      const [prizewheelResponse, amidakujiResponse, globalResponse] = await Promise.all([
        apiClient.getGameSettings('prizewheel'),
        apiClient.getGameSettings('amidakuji'),
        apiClient.getGameSettings('global'),
      ]);

      if (prizewheelResponse.success && prizewheelResponse.data?.game_settings) {
        setPrizewheelSettings(prev => ({ ...prev, ...prizewheelResponse.data.game_settings }));
      }

      if (amidakujiResponse.success && amidakujiResponse.data?.game_settings) {
        setAmidakujiSettings(prev => ({ ...prev, ...amidakujiResponse.data.game_settings }));
      }

      if (globalResponse.success && globalResponse.data?.game_settings) {
        setGlobalSettings(prev => ({ ...prev, ...globalResponse.data.game_settings }));
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch settings';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async (gameType: string, settings: GameSettings) => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      await apiClient.updateGameSettings(gameType, settings);
      setSuccess(`${gameType} settings updated successfully`);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings';
      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSettingChange = (gameType: string, key: string, value: unknown) => {
    switch (gameType) {
      case 'prizewheel':
        setPrizewheelSettings(prev => ({ ...prev, [key]: value }));
        break;
      case 'amidakuji':
        setAmidakujiSettings(prev => ({ ...prev, [key]: value }));
        break;
      case 'global':
        setGlobalSettings(prev => ({ ...prev, [key]: value }));
        break;
    }
  };

  const renderSettingsForm = (gameType: string, settings: GameSettings) => {
    const settingsConfig = getSettingsConfig(gameType);

    return (
      <div className="space-y-4">
        {settingsConfig.map((config) => (
          <div key={config.key} className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {config.label}
              </label>
              <p className="text-xs text-gray-700 mt-1">{config.description}</p>
            </div>
            <div>
              {config.type === 'boolean' ? (
                <input
                  type="checkbox"
                  checked={Boolean(settings[config.key])}
                  onChange={(e) => handleSettingChange(gameType, config.key, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              ) : config.type === 'select' ? (
                <select
                  value={String(settings[config.key] || '')}
                  onChange={(e) => handleSettingChange(gameType, config.key, e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  {config.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <Input
                  type={config.type}
                  value={String(settings[config.key] || '')}
                  onChange={(e) => {
                    const value = config.type === 'number' ? parseFloat(e.target.value) : e.target.value;
                    handleSettingChange(gameType, config.key, value);
                  }}
                  min={config.min}
                  max={config.max}
                  step={config.step}
                />
              )}
            </div>
          </div>
        ))}

        <div className="flex justify-end pt-4 border-t">
          <Button
            onClick={() => handleSaveSettings(gameType, settings)}
            isLoading={isSaving}
            disabled={isSaving}
            className="flex items-center space-x-2"
          >
            <CheckCircleIcon className="h-4 w-4" />
            <span>Save {gameType} Settings</span>
          </Button>
        </div>
      </div>
    );
  };

  const getSettingsConfig = (gameType: string) => {
    switch (gameType) {
      case 'prizewheel':
        return [
          { key: 'min_players', label: 'Minimum Players', description: 'Minimum number of players required', type: 'number', min: 1, max: 10 },
          { key: 'max_players', label: 'Maximum Players', description: 'Maximum number of players allowed', type: 'number', min: 1, max: 20 },
          { key: 'min_bet', label: 'Minimum Bet', description: 'Minimum bet amount', type: 'number', min: 0.01, step: 0.01 },
          { key: 'max_bet', label: 'Maximum Bet', description: 'Maximum bet amount', type: 'number', min: 1, step: 0.01 },
          { key: 'house_edge', label: 'House Edge', description: 'House edge percentage (0.0 - 1.0)', type: 'number', min: 0, max: 1, step: 0.01 },
          { key: 'waiting_timeout', label: 'Waiting Timeout (seconds)', description: 'Time to wait for players', type: 'number', min: 30, max: 600 },
          { key: 'playing_timeout', label: 'Playing Timeout (seconds)', description: 'Maximum game duration', type: 'number', min: 60, max: 1800 },
          { key: 'wheel_sections', label: 'Wheel Sections', description: 'Number of sections on the wheel', type: 'number', min: 4, max: 20 },
          { key: 'spin_duration', label: 'Spin Duration (seconds)', description: 'How long the wheel spins', type: 'number', min: 1, max: 10 },
          { key: 'animation_steps', label: 'Animation Steps', description: 'Number of animation frames', type: 'number', min: 10, max: 200 },
        ];
      case 'amidakuji':
        return [
          { key: 'min_players', label: 'Minimum Players', description: 'Minimum number of players required', type: 'number', min: 1, max: 10 },
          { key: 'max_players', label: 'Maximum Players', description: 'Maximum number of players allowed', type: 'number', min: 1, max: 20 },
          { key: 'min_bet', label: 'Minimum Bet', description: 'Minimum bet amount', type: 'number', min: 0.01, step: 0.01 },
          { key: 'max_bet', label: 'Maximum Bet', description: 'Maximum bet amount', type: 'number', min: 1, step: 0.01 },
          { key: 'house_edge', label: 'House Edge', description: 'House edge percentage (0.0 - 1.0)', type: 'number', min: 0, max: 1, step: 0.01 },
          { key: 'waiting_timeout', label: 'Waiting Timeout (seconds)', description: 'Time to wait for players', type: 'number', min: 30, max: 600 },
          { key: 'playing_timeout', label: 'Playing Timeout (seconds)', description: 'Maximum game duration', type: 'number', min: 60, max: 1800 },
          { key: 'rows_per_player', label: 'Rows per Player', description: 'Number of rows in the ladder per player', type: 'number', min: 5, max: 20 },
          { key: 'line_probability', label: 'Line Probability', description: 'Probability of horizontal lines (0.0 - 1.0)', type: 'number', min: 0, max: 1, step: 0.01 },
          { key: 'animation_duration', label: 'Animation Duration (seconds)', description: 'How long the ball animation takes', type: 'number', min: 1, max: 10 },
        ];
      case 'global':
        return [
          { key: 'maintenance_mode', label: 'Maintenance Mode', description: 'Enable maintenance mode', type: 'boolean' },
          { key: 'max_concurrent_games', label: 'Max Concurrent Games', description: 'Maximum number of simultaneous games', type: 'number', min: 1, max: 1000 },
          { key: 'default_currency', label: 'Default Currency', description: 'Default currency for the system', type: 'select', options: [
            { value: 'USD', label: 'US Dollar' },
            { value: 'EUR', label: 'Euro' },
            { value: 'GBP', label: 'British Pound' },
            { value: 'JPY', label: 'Japanese Yen' },
          ]},
          { key: 'session_timeout', label: 'Session Timeout (seconds)', description: 'User session timeout', type: 'number', min: 300, max: 7200 },
          { key: 'max_bet_multiplier', label: 'Max Bet Multiplier', description: 'Maximum bet as multiple of balance', type: 'number', min: 1, max: 100 },
          { key: 'min_balance_threshold', label: 'Min Balance Threshold', description: 'Minimum balance required to play', type: 'number', min: 0, step: 0.01 },
        ];
      default:
        return [];
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (user?.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-800">You need admin privileges to access settings.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-800">Configure game parameters and system settings</p>
          </div>
          <Button
            variant="outline"
            onClick={fetchSettings}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <p className="ml-3 text-sm text-green-600">{success}</p>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <p className="ml-3 text-sm text-red-600">{error}</p>
            </div>
          </div>
        )}

        {/* Settings Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'prizewheel', name: 'Prize Wheel', icon: CogIcon },
              { id: 'amidakuji', name: 'Amidakuji', icon: CogIcon },
              { id: 'global', name: 'Global', icon: CogIcon },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedGameType(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedGameType === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-700 hover:text-gray-900 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Settings Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CogIcon className="h-5 w-5 mr-2" />
              {selectedGameType === 'prizewheel' && 'Prize Wheel Settings'}
              {selectedGameType === 'amidakuji' && 'Amidakuji Settings'}
              {selectedGameType === 'global' && 'Global Settings'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loading size="md" text="Loading settings..." />
              </div>
            ) : (
              <>
                {selectedGameType === 'prizewheel' && renderSettingsForm('prizewheel', prizewheelSettings)}
                {selectedGameType === 'amidakuji' && renderSettingsForm('amidakuji', amidakujiSettings)}
                {selectedGameType === 'global' && renderSettingsForm('global', globalSettings)}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default SettingsPage;
