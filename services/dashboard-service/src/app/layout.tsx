'use client';

import React, { useEffect } from 'react';
import { Inter } from 'next/font/google';
import { useAuthStore } from '@/stores/auth';
import "./globals.css";

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { getCurrentUser, token } = useAuthStore();

  useEffect(() => {
    // Initialize auth state on app load
    if (token) {
      getCurrentUser();
    }
  }, [getCurrentUser, token]);

  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
