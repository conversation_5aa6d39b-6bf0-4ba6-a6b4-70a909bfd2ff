'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { GameSession, GameSessionFilters, PaginatedResponse } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import Table from '@/components/ui/Table';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  EyeIcon,
  PuzzlePieceIcon,
  PlayIcon,
  StopIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

const GameSessionsPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [gameSessions, setGameSessions] = useState<GameSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSession, setSelectedSession] = useState<GameSession | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const [filters, setFilters] = useState<GameSessionFilters>({
    page: 1,
    per_page: 25,
  });

  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 25,
    has_next: false,
    has_prev: false,
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchGameSessions = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response: PaginatedResponse<GameSession> = await apiClient.getGameSessions(filters);
      if (response.success && response.data) {
        setGameSessions((response.data.game_sessions as GameSession[]) || []);
        setPagination(response.data.pagination);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch game sessions';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchGameSessions();
    }
  }, [isAuthenticated, fetchGameSessions]);

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (key: keyof GameSessionFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRefresh = () => {
    fetchGameSessions();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'active':
        return <PlayIcon className="h-4 w-4" />;
      case 'pending':
        return <StopIcon className="h-4 w-4" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <StopIcon className="h-4 w-4" />;
    }
  };

  const getGameTypeColor = (gameType: string) => {
    switch (gameType) {
      case 'prizewheel':
        return 'bg-purple-100 text-purple-800';
      case 'amidakuji':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const calculateDuration = (startedAt: string | null, endedAt: string | null) => {
    if (!startedAt || !endedAt) return 'N/A';
    try {
      const start = new Date(startedAt);
      const end = new Date(endedAt);
      const durationMs = end.getTime() - start.getTime();
      const minutes = Math.floor(durationMs / 60000);
      const seconds = Math.floor((durationMs % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    } catch {
      return 'N/A';
    }
  };

  const columns = [
    {
      key: 'session_id',
      label: 'Session ID',
      render: (value: unknown) => (
        <span className="font-mono text-sm">{String(value)}</span>
      ),
    },
    {
      key: 'game_type',
      label: 'Game Type',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getGameTypeColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(String(value))}`}>
          {getStatusIcon(String(value))}
          <span className="ml-1">{String(value)}</span>
        </span>
      ),
    },
    {
      key: 'bet_amount',
      label: 'Bet Amount',
      render: (value: unknown) => (
        <span className="font-semibold text-orange-600">{formatAmount(Number(value))}</span>
      ),
    },
    {
      key: 'win_amount',
      label: 'Win Amount',
      render: (value: unknown, item: GameSession) => {
        const winAmount = Number(value);
        const betAmount = item.bet_amount;
        const isWin = winAmount > betAmount;
        return (
          <span className={`font-semibold ${isWin ? 'text-green-600' : 'text-red-600'}`}>
            {formatAmount(winAmount)}
          </span>
        );
      },
    },
    {
      key: 'started_at',
      label: 'Started',
      render: (value: unknown) => formatDate(String(value)),
    },
    {
      key: 'duration',
      label: 'Duration',
      render: (_: unknown, item: GameSession) => (
        <span className="text-sm text-gray-800">
          {calculateDuration(item.started_at, item.ended_at)}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: GameSession) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSelectedSession(item)}
          className="flex items-center space-x-1"
        >
          <EyeIcon className="h-4 w-4" />
          <span>View</span>
        </Button>
      ),
    },
  ];

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Game Sessions</h1>
            <p className="text-gray-800">Monitor active and completed game sessions</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by session ID or user..."
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full"
                  />
                </div>
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-600" />
              </div>

              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Game Type</label>
                    <select
                      value={filters.game_type || ''}
                      onChange={(e) => handleFilterChange('game_type', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="">All Games</option>
                      <option value="prizewheel">Prize Wheel</option>
                      <option value="amidakuji">Amidakuji</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={filters.status || ''}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="">All Statuses</option>
                      <option value="pending">Pending</option>
                      <option value="active">Active</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                    <Input
                      placeholder="Filter by user ID..."
                      value={filters.user_id || ''}
                      onChange={(e) => handleFilterChange('user_id', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Game Sessions Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PuzzlePieceIcon className="h-5 w-5 mr-2" />
              Game Sessions ({pagination.total_count})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loading size="md" text="Loading game sessions..." />
              </div>
            ) : (
              <Table
                data={gameSessions}
                columns={columns}
                pagination={pagination}
                onPageChange={handlePageChange}
                isLoading={isLoading}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default GameSessionsPage;
