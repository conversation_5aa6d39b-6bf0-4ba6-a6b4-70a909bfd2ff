'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Loading from '@/components/ui/Loading';

const AuthMePage: React.FC = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the proper profile page
    router.replace('/profile');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Loading size="lg" text="Redirecting to profile..." />
    </div>
  );
};

export default AuthMePage;
