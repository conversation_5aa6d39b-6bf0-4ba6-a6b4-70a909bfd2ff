'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { GameConfiguration, GameStateDurations, GameConfigurationsResponse } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Loading from '@/components/ui/Loading';
import {
  CogIcon,
  ClockIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

export default function GameConfigPage() {
  const router = useRouter();
  const { user, isAuthenticated, authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [configurations, setConfigurations] = useState<GameConfigurationsResponse | null>(null);
  const [activeTab, setActiveTab] = useState<'prizewheel' | 'amidakuji' | 'global'>('prizewheel');

  // Form states for each game type
  const [prizewheelDurations, setPrizewheelDurations] = useState<GameStateDurations>({
    starting: 10,
    playing: 30,
    end: 15,
  });

  const [amidakujiDurations, setAmidakujiDurations] = useState<GameStateDurations>({
    starting: 10,
    playing: 60,
    end: 15,
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    if (isAuthenticated && user?.role === 'admin') {
      fetchConfigurations();
    }
  }, [isAuthenticated, user]);

  const fetchConfigurations = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getGameConfigurations();
      setConfigurations(data);
      
      // Update form states with current values
      if (data.configurations.prizewheel?.state_durations) {
        setPrizewheelDurations(data.configurations.prizewheel.state_durations);
      }
      if (data.configurations.amidakuji?.state_durations) {
        setAmidakujiDurations(data.configurations.amidakuji.state_durations);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch configurations';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateDurations = async (gameType: 'prizewheel' | 'amidakuji') => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const durations = gameType === 'prizewheel' ? prizewheelDurations : amidakujiDurations;
      await apiClient.updateGameStateDurations(gameType, durations);
      
      setSuccess(`${gameType} state durations updated successfully`);
      await fetchConfigurations();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update durations';
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleResetDefaults = async (gameType?: string) => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      await apiClient.resetGameConfigurationDefaults(gameType);
      
      const message = gameType 
        ? `${gameType} configuration reset to defaults`
        : 'All configurations reset to defaults';
      setSuccess(message);
      await fetchConfigurations();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset defaults';
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const renderDurationForm = (
    gameType: 'prizewheel' | 'amidakuji',
    durations: GameStateDurations,
    setDurations: React.Dispatch<React.SetStateAction<GameStateDurations>>
  ) => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Input
          label="Starting Duration (seconds)"
          type="number"
          min="1"
          value={durations.starting}
          onChange={(e) => setDurations(prev => ({ ...prev, starting: parseInt(e.target.value) || 0 }))}
          placeholder="10"
          helperText="Countdown before game starts"
        />

        <Input
          label="Playing Duration (seconds)"
          type="number"
          min="1"
          value={durations.playing}
          onChange={(e) => setDurations(prev => ({ ...prev, playing: parseInt(e.target.value) || 0 }))}
          placeholder={gameType === 'prizewheel' ? '30' : '60'}
          helperText="Actual game play time"
        />

        <Input
          label="End Duration (seconds)"
          type="number"
          min="1"
          value={durations.end}
          onChange={(e) => setDurations(prev => ({ ...prev, end: parseInt(e.target.value) || 0 }))}
          placeholder="15"
          helperText="Showing results before cleanup"
        />
      </div>

      <div className="flex items-center space-x-3">
        <Button
          onClick={() => handleUpdateDurations(gameType)}
          disabled={saving}
          className="flex items-center space-x-2"
        >
          <CheckCircleIcon className="h-4 w-4" />
          <span>{saving ? 'Saving...' : 'Save Durations'}</span>
        </Button>
        
        <Button
          onClick={() => handleResetDefaults(gameType)}
          disabled={saving}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <ArrowPathIcon className="h-4 w-4" />
          <span>Reset to Defaults</span>
        </Button>
      </div>
    </div>
  );

  if (!isAuthenticated || authLoading) {
    return null;
  }

  if (user?.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600">You need admin privileges to access game configurations.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Game Configuration</h1>
            <p className="text-gray-800">Manage game state durations and settings</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={fetchConfigurations}
              disabled={loading}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
            <Button
              onClick={() => handleResetDefaults()}
              disabled={saving}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>Reset All Defaults</span>
            </Button>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <Loading />
        ) : (
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { key: 'prizewheel', label: 'Prize Wheel', icon: CogIcon },
                  { key: 'amidakuji', label: 'Amidakuji', icon: ClockIcon },
                  { key: 'global', label: 'Global Settings', icon: CogIcon },
                ].map(({ key, label, icon: Icon }) => (
                  <button
                    key={key}
                    onClick={() => setActiveTab(key as any)}
                    className={`${
                      activeTab === key
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'prizewheel' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CogIcon className="h-5 w-5" />
                    <span>Prize Wheel State Durations</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {renderDurationForm('prizewheel', prizewheelDurations, setPrizewheelDurations)}
                </CardContent>
              </Card>
            )}

            {activeTab === 'amidakuji' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ClockIcon className="h-5 w-5" />
                    <span>Amidakuji State Durations</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {renderDurationForm('amidakuji', amidakujiDurations, setAmidakujiDurations)}
                </CardContent>
              </Card>
            )}

            {activeTab === 'global' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CogIcon className="h-5 w-5" />
                    <span>Global Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-gray-600">Global settings configuration coming soon...</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
