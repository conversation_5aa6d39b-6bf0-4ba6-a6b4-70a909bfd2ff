import React from 'react';
import { cn } from '@/lib/utils';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  ShieldExclamationIcon
} from '@heroicons/react/24/outline';

interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'success' | 'error' | 'warning' | 'info' | 'access-denied';
  title?: string;
  children: React.ReactNode;
  showIcon?: boolean;
}

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className, variant = 'info', title, children, showIcon = true, ...props }, ref) => {
    const variants = {
      success: {
        container: 'bg-green-50 border-green-200 text-green-800',
        icon: CheckCircleIcon,
        iconColor: 'text-green-400',
        title: 'text-green-800',
        content: 'text-green-700'
      },
      error: {
        container: 'bg-red-50 border-red-200 text-red-800',
        icon: XCircleIcon,
        iconColor: 'text-red-400',
        title: 'text-red-800',
        content: 'text-red-700'
      },
      warning: {
        container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
        icon: ExclamationTriangleIcon,
        iconColor: 'text-yellow-400',
        title: 'text-yellow-800',
        content: 'text-yellow-700'
      },
      info: {
        container: 'bg-blue-50 border-blue-200 text-blue-800',
        icon: InformationCircleIcon,
        iconColor: 'text-blue-400',
        title: 'text-blue-800',
        content: 'text-blue-700'
      },
      'access-denied': {
        container: 'bg-red-50 border-red-200 text-red-800',
        icon: ShieldExclamationIcon,
        iconColor: 'text-red-400',
        title: 'text-red-800',
        content: 'text-red-700'
      }
    };

    const config = variants[variant];
    const IconComponent = config.icon;

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-md border p-4',
          config.container,
          className
        )}
        {...props}
      >
        <div className="flex">
          {showIcon && (
            <div className="flex-shrink-0">
              <IconComponent className={cn('h-5 w-5', config.iconColor)} />
            </div>
          )}
          <div className={cn('ml-3', !showIcon && 'ml-0')}>
            {title && (
              <h3 className={cn('text-sm font-medium', config.title)}>
                {title}
              </h3>
            )}
            <div className={cn(
              'text-sm',
              config.content,
              title && 'mt-2'
            )}>
              {children}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

Alert.displayName = 'Alert';

export default Alert;
