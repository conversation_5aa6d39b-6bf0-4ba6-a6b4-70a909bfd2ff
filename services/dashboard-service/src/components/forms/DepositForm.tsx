'use client';

import React, { useState } from 'react';
import { DepositForm as DepositFormType } from '@/types';
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { PlusIcon } from '@heroicons/react/24/outline';

interface DepositFormProps {
  userId: string;
  username: string;
  onSubmit: (data: DepositFormType) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const DepositForm: React.FC<DepositFormProps> = ({
  userId,
  username,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<DepositFormType>({
    user_id: userId,
    amount: 0,
    description: '',
    payment_method: '',
    notes: ''
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    if (!formData.description?.trim()) {
      errors.description = 'Description is required';
    }

    if (formData.amount > 10000) {
      errors.amount = 'Amount cannot exceed $10,000 per transaction';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      // Error handling is done by parent component
      console.error('Deposit form submission error:', error);
    }
  };

  const handleInputChange = (field: keyof DepositFormType, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <PlusIcon className="h-5 w-5" />
          <span>Deposit Funds for {username}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Amount"
              type="number"
              step="0.01"
              min="0.01"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
              error={formErrors.amount}
              placeholder="Enter deposit amount"
              required
            />

            <Input
              label="Payment Method"
              type="text"
              value={formData.payment_method}
              onChange={(e) => handleInputChange('payment_method', e.target.value)}
              error={formErrors.payment_method}
              placeholder="e.g., Credit Card, Bank Transfer"
            />
          </div>

          <Input
            label="Description"
            type="text"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            error={formErrors.description}
            placeholder="Enter deposit description"
            required
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Notes (Optional)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about this deposit"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>{isLoading ? 'Processing...' : 'Create Deposit'}</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default DepositForm;
