'use client';

import React, { useState } from 'react';
import { WithdrawForm as WithdrawFormType } from '@/types';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { MinusIcon } from '@heroicons/react/24/outline';

interface WithdrawFormProps {
  userId: string;
  username: string;
  currentBalance: number;
  onSubmit: (data: WithdrawFormType) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const WithdrawForm: React.FC<WithdrawFormProps> = ({
  userId,
  username,
  currentBalance,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<WithdrawFormType>({
    user_id: userId,
    amount: 0,
    description: '',
    payment_method: '',
    notes: ''
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    } else if (formData.amount > currentBalance) {
      errors.amount = `Amount cannot exceed current balance (${currentBalance.toFixed(2)})`;
    }

    if (!formData.description?.trim()) {
      errors.description = 'Description is required';
    }

    if (formData.amount > 5000) {
      errors.amount = 'Amount cannot exceed $5,000 per withdrawal';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      // Error handling is done by parent component
      console.error('Withdrawal form submission error:', error);
    }
  };

  const handleInputChange = (field: keyof WithdrawFormType, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MinusIcon className="h-5 w-5" />
          <span>Withdraw Funds for {username}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-700">
            Current Balance: <span className="font-semibold">${currentBalance.toFixed(2)}</span>
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Amount"
              type="number"
              step="0.01"
              min="0.01"
              max={currentBalance}
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
              error={formErrors.amount}
              placeholder="Enter withdrawal amount"
              required
            />

            <Input
              label="Payment Method"
              type="text"
              value={formData.payment_method}
              onChange={(e) => handleInputChange('payment_method', e.target.value)}
              error={formErrors.payment_method}
              placeholder="e.g., Bank Transfer, Check"
            />
          </div>

          <Input
            label="Description"
            type="text"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            error={formErrors.description}
            placeholder="Enter withdrawal description"
            required
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Notes (Optional)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about this withdrawal"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700"
            >
              <MinusIcon className="h-4 w-4" />
              <span>{isLoading ? 'Processing...' : 'Create Withdrawal'}</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default WithdrawForm;
