// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginationMeta {
  current_page: number;
  total_pages: number;
  total_count: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message?: string;
  errors?: string[];
  data: {
    [key: string]: T[] | PaginationMeta;
    pagination: PaginationMeta;
  };
}

// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  balance: number;
  status: 'active' | 'inactive' | 'suspended' | 'banned';
  role: 'player' | 'admin' | 'moderator';
  last_login_at: string | null;
  created_at: string;
  updated_at: string;
  statistics?: UserStatistics;
}

export interface UserStatistics {
  total_games: number;
  games_won: number;
  total_bet: number;
  total_won: number;
  win_rate: number;
}

// Transaction Types
export interface Transaction {
  id: string;
  transaction_id: string;
  type: 'deposit' | 'admin_deposit' | 'withdrawal' | 'bet_placed' | 'bet_won' | 'bet_lost' | 'adjustment' | 'refund' | 'bonus' | 'reconciliation' | 'bet_reserved';
  amount: number;
  balance_before: number;
  balance_after: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  created_at: string;
  processed_at?: string;
  reference_id?: string | null;
  reference_type?: string | null;
  user?: {
    id: string;
    username: string;
  };
  metadata?: Record<string, unknown>;
}

export interface TransactionStats {
  total_deposits: number;
  total_withdrawals: number;
  total_bets: number;
  total_winnings: number;
}

// Game Session Types
export interface GameSession {
  id: string;
  session_id: string;
  game_type: 'prizewheel' | 'amidakuji';
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  bet_amount: number;
  win_amount: number;
  result: Record<string, unknown>;
  started_at: string | null;
  ended_at: string | null;
  user_id: string;
  metadata: Record<string, unknown>;
}

// Room Types
export interface Room {
  id: string;
  external_room_id: string;
  name: string;
  game_type: 'prizewheel' | 'amidakuji';
  status: 'starting' | 'playing' | 'end' | 'waiting' | 'finished' | 'cancelled';
  creator_id: string;
  creator_username?: string;
  max_players: number;
  current_players: number;
  bet_amount: number;
  currency: string;
  prize_pool: number;
  configuration: RoomConfiguration;
  created_at: string;
  updated_at: string;
  players?: RoomPlayer[];
}

export interface RoomPlayer {
  user_id: string;
  username: string;
  bet_amount: number;
  position?: number;
  is_ready: boolean;
  joined_at: string;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  session_count?: number;
}

export interface DetailedRoom extends Room {
  players: RoomPlayer[];
  creator: {
    id: string;
    username: string;
  };
  game_sessions: GameSession[];
}

export interface RoomConfiguration {
  game_duration: number;
  wheel_sections?: number;
  spin_duration?: number;
  animation_steps?: number;
  rows_per_player?: number;
  line_probability?: number;
  animation_duration?: number;
}

// Game Settings Types
export interface GameSetting {
  id: string;
  game_type: 'prizewheel' | 'amidakuji' | 'global';
  setting_key: string;
  setting_value: unknown;
  data_type: 'string' | 'integer' | 'float' | 'boolean' | 'hash' | 'array';
  description: string;
  is_active: boolean;
  version: number;
  last_synced_at: string | null;
  sync_required: boolean;
}

export interface GameConfiguration {
  [key: string]: unknown;
  min_players?: number;
  max_players?: number;
  min_bet?: number;
  max_bet?: number;
  house_edge?: number;
  waiting_timeout?: number;
  playing_timeout?: number;
  idle_timeout?: number;
}

// Auth Types
export interface AuthUser {
  id: string;
  username: string;
  email: string;
  role: 'player' | 'admin' | 'moderator';
  balance: number;
  status: string;
  last_login_at: string | null;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: AuthUser;
  token: string;
  expires_at: string;
}

// Dashboard Stats Types
export interface DashboardStats {
  users: {
    total: number;
    active: number;
    new_today: number;
  };
  transactions: {
    total_volume: number;
    daily_volume: number;
    total_count: number;
    daily_count: number;
  };
  games: {
    active_sessions: number;
    completed_today: number;
    total_rooms: number;
    active_rooms: number;
  };
  system: {
    uptime: string;
    version: string;
    status: 'operational' | 'maintenance' | 'degraded';
  };
}

// Filter and Search Types
export interface UserFilters {
  status?: string;
  role?: string;
  search?: string;
  page?: number;
  per_page?: number;
}

export interface TransactionFilters {
  type?: string;
  status?: string;
  user_id?: string;
  username?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
  page?: number;
  per_page?: number;
}

export interface GameSessionFilters {
  game_type?: string;
  status?: string;
  user_id?: string;
  search?: string;
  page?: number;
  per_page?: number;
}

export interface RoomFilters {
  game_type?: string;
  status?: string;
  creator_id?: string;
  created_after?: string;
  created_before?: string;
  search?: string;
  page?: number;
  per_page?: number;
}

// Form Types
export interface CreateUserForm {
  username: string;
  email: string;
  password: string;
  password_confirmation: string;
  role?: string;
  balance?: number;
}

export interface UpdateUserForm {
  email?: string;
  status?: string;
  role?: string;
}

export interface UpdateBalanceForm {
  amount: number;
  transaction_type: string;
  description: string;
}

export interface DepositForm {
  user_id: string;
  amount: number;
  description?: string;
  payment_method?: string;
  notes?: string;
}

export interface WithdrawForm {
  user_id: string;
  amount: number;
  description?: string;
  payment_method?: string;
  notes?: string;
}

export interface CreateRoomForm {
  name: string;
  game_type: 'prizewheel' | 'amidakuji';
  max_players: number;
  bet_amount: number;
  currency: string;
  game_duration: number;
  is_private: boolean;
  password?: string;
  game_specific_config: Record<string, unknown>;
}

// Game Configuration Types
export interface GameStateDurations {
  starting: number;
  playing: number;
  end: number;
}

export interface GameConfiguration {
  game_type: 'prizewheel' | 'amidakuji' | 'global';
  general_config: Record<string, unknown>;
  state_durations?: GameStateDurations; // Optional because global doesn't have state durations
  last_updated?: string;
}

export interface GameConfigurationUpdate {
  general_config?: Record<string, unknown>;
  state_durations?: Partial<GameStateDurations>;
}

export interface GameConfigurationsResponse {
  configurations: {
    prizewheel: GameConfiguration;
    amidakuji: GameConfiguration;
    global: GameConfiguration;
  };
}

export interface KickPlayerForm {
  user_id: string;
  reason?: string;
  notify_player?: boolean;
}

export interface AdminAction {
  id: string;
  action_type: 'kick_player' | 'force_start' | 'force_end' | 'delete_room';
  room_id: string;
  target_user_id?: string;
  reason: string;
  performed_by: string;
  performed_at: string;
  metadata?: Record<string, unknown>;
}

// Component Props Types
export interface TableColumn<T> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  render?: (value: unknown, item: T) => React.ReactNode;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  [key: string]: unknown;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}
