import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthUser, User, LoginRequest } from '@/types';
import { apiClient } from '@/lib/api';

interface AuthState {
  user: AuthUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.login(credentials);

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          await apiClient.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API call failed:', error);
        } finally {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      getCurrentUser: async () => {
        const { token } = get();

        if (!token) {
          set({ isAuthenticated: false, user: null });
          return;
        }

        set({ isLoading: true, error: null });

        try {
          const userData = await apiClient.getCurrentUser();

          // Convert User to AuthUser format
          const user: AuthUser = {
            id: userData.id,
            username: userData.username,
            email: userData.email,
            role: userData.role,
            balance: userData.balance,
            status: userData.status,
            last_login_at: userData.last_login_at,
          };

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to get user';

          // If unauthorized, clear auth state
          if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as { response?: { status?: number } };
            if (axiosError.response?.status === 401) {
              set({
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false,
                error: null,
              });
            } else {
              set({
                isLoading: false,
                error: errorMessage,
              });
            }
          } else {
            set({
              isLoading: false,
              error: errorMessage,
            });
          }
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // Set the token in the API client when rehydrating
        if (state?.token) {
          apiClient.setToken(state.token);
        }
      },
    }
  )
);

// Selectors for easier access to specific parts of the state
export const useAuth = () => {
  const { user, isAuthenticated, isLoading, error } = useAuthStore();
  return { user, isAuthenticated, isLoading, error };
};

export const useAuthActions = () => {
  const { login, logout, getCurrentUser, clearError, setLoading } = useAuthStore();
  return { login, logout, getCurrentUser, clearError, setLoading };
};

// Helper hooks
export const useIsAdmin = () => {
  const { user } = useAuth();
  return user?.role === 'admin';
};

export const useIsModerator = () => {
  const { user } = useAuth();
  return user?.role === 'moderator' || user?.role === 'admin';
};

export const useCanManageUsers = () => {
  const { user } = useAuth();
  return user?.role === 'admin';
};

export const useCanManageRooms = () => {
  const { user } = useAuth();
  return user?.role === 'admin' || user?.role === 'moderator';
};

export const useCanViewTransactions = () => {
  const { user } = useAuth();
  return user?.role === 'admin' || user?.role === 'moderator';
};
