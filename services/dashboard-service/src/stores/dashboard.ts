import { create } from 'zustand';
import { DashboardStats } from '@/types';
import { apiClient } from '@/lib/api';

interface DashboardState {
  stats: DashboardStats | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
}

interface DashboardActions {
  fetchStats: () => Promise<void>;
  setAutoRefresh: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  clearError: () => void;
  reset: () => void;
}

type DashboardStore = DashboardState & DashboardActions;

const initialState: DashboardState = {
  stats: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
  autoRefresh: true,
  refreshInterval: 30, // 30 seconds
};

export const useDashboardStore = create<DashboardStore>((set) => ({
  ...initialState,

  fetchStats: async () => {
    set({ isLoading: true, error: null });

    try {
      const stats = await apiClient.getDashboardStats();

      set({
        stats,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard stats';

      set({
        isLoading: false,
        error: errorMessage,
      });
    }
  },

  setAutoRefresh: (enabled: boolean) => {
    set({ autoRefresh: enabled });
  },

  setRefreshInterval: (interval: number) => {
    set({ refreshInterval: interval });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set(initialState);
  },
}));

// Selectors
export const useDashboardStats = () => {
  const { stats, isLoading, error, lastUpdated } = useDashboardStore();
  return { stats, isLoading, error, lastUpdated };
};

export const useDashboardActions = () => {
  const { fetchStats, setAutoRefresh, setRefreshInterval, clearError, reset } = useDashboardStore();
  return { fetchStats, setAutoRefresh, setRefreshInterval, clearError, reset };
};

export const useDashboardSettings = () => {
  const { autoRefresh, refreshInterval } = useDashboardStore();
  return { autoRefresh, refreshInterval };
};
