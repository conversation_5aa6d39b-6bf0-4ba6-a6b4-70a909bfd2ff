import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  ApiResponse,
  PaginatedResponse,
  User,
  Transaction,
  GameSession,
  Room,
  DetailedRoom,
  RoomPlayer,
  LoginRequest,
  LoginResponse,
  DashboardStats,
  UserFilters,
  TransactionFilters,
  GameSessionFilters,
  RoomFilters,
  CreateUserForm,
  UpdateUserForm,
  UpdateBalanceForm,
  DepositForm,
  WithdrawForm,
  CreateRoomForm,
  KickPlayerForm,
  GameConfiguration,
  GameConfigurationUpdate,
  GameConfigurationsResponse,
  GameStateDurations,
} from '@/types';

class ApiClient {
  private client: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3002',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: unknown) => {
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { status?: number } };
          if (axiosError.response?.status === 401) {
            this.clearToken();
            // Redirect to login if needed
            if (typeof window !== 'undefined') {
              window.location.href = '/login';
            }
          }
        }
        return Promise.reject(error);
      }
    );

    // Load token from localStorage on initialization
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response: AxiosResponse<ApiResponse<LoginResponse>> = await this.client.post('/auth/login', credentials);
    if (response.data.success && response.data.data) {
      this.setToken(response.data.data.token);
      return response.data.data;
    }
    throw new Error(response.data.message || 'Login failed');
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/auth/logout');
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<ApiResponse<{ user: User }>> = await this.client.get('/auth/me');
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }
    throw new Error(response.data.message || 'Failed to get current user');
  }

  // Dashboard stats
  async getDashboardStats(): Promise<DashboardStats> {
    // This would be a custom endpoint for dashboard stats
    // For now, we'll aggregate from existing endpoints
    const [usersResponse, transactionsResponse] = await Promise.all([
      this.client.get('/users?per_page=1'),
      this.client.get('/transactions/stats')
    ]);

    return {
      users: {
        total: usersResponse.data.data?.pagination?.total_count || 0,
        active: 0, // Would need specific endpoint
        new_today: 0, // Would need specific endpoint
      },
      transactions: transactionsResponse.data.data || {
        total_volume: 0,
        daily_volume: 0,
        total_count: 0,
        daily_count: 0,
      },
      games: {
        active_sessions: 0, // Would need specific endpoint
        completed_today: 0, // Would need specific endpoint
        total_rooms: 0, // Would need specific endpoint
        active_rooms: 0, // Would need specific endpoint
      },
      system: {
        uptime: '0h 0m',
        version: '1.0.0',
        status: 'operational',
      },
    };
  }

  // User management
  async getUsers(filters: UserFilters = {}): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<PaginatedResponse<User>> = await this.client.get(`/users?${params}`);
    return response.data;
  }

  async getUser(id: string): Promise<User> {
    const response: AxiosResponse<ApiResponse<{ user: User }>> = await this.client.get(`/users/${id}`);
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }
    throw new Error(response.data.message || 'Failed to get user');
  }

  async createUser(userData: CreateUserForm): Promise<User> {
    const response: AxiosResponse<ApiResponse<{ user: User }>> = await this.client.post('/users', { user: userData });
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }
    throw new Error(response.data.message || 'Failed to create user');
  }

  async updateUser(id: string, userData: UpdateUserForm): Promise<User> {
    const response: AxiosResponse<ApiResponse<{ user: User }>> = await this.client.put(`/users/${id}`, { user: userData });
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }
    throw new Error(response.data.message || 'Failed to update user');
  }

  async updateUserBalance(id: string, balanceData: UpdateBalanceForm): Promise<void> {
    const response: AxiosResponse<ApiResponse<Record<string, unknown>>> = await this.client.put(`/users/${id}/balance`, balanceData);
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update balance');
    }
  }

  async getUserTransactions(id: string, filters: TransactionFilters = {}): Promise<PaginatedResponse<Transaction>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<PaginatedResponse<Transaction>> = await this.client.get(`/users/${id}/transactions?${params}`);
    return response.data;
  }

  // Transaction management
  async getTransactions(filters: TransactionFilters = {}): Promise<PaginatedResponse<Transaction>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<PaginatedResponse<Transaction>> = await this.client.get(`/transactions?${params}`);
    return response.data;
  }

  async getTransaction(id: string): Promise<Transaction> {
    const response: AxiosResponse<ApiResponse<{ transaction: Transaction }>> = await this.client.get(`/transactions/${id}`);
    if (response.data.success && response.data.data) {
      return response.data.data.transaction;
    }
    throw new Error(response.data.message || 'Failed to get transaction');
  }

  async createDeposit(depositData: DepositForm): Promise<{ transaction: Transaction; user: User }> {
    const response: AxiosResponse<ApiResponse<{ transaction: Transaction; user: User }>> = await this.client.post('/transactions/deposit', { deposit: depositData });
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || 'Failed to create deposit');
  }

  async createWithdrawal(withdrawData: WithdrawForm): Promise<{ transaction: Transaction; user: User }> {
    const response: AxiosResponse<ApiResponse<{ transaction: Transaction; user: User }>> = await this.client.post('/transactions/withdraw', { withdraw: withdrawData });
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || 'Failed to create withdrawal');
  }

  // Game session management
  async getGameSessions(filters: GameSessionFilters = {}): Promise<PaginatedResponse<GameSession>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<PaginatedResponse<GameSession>> = await this.client.get(`/game_sessions?${params}`);
    return response.data;
  }

  async getGameSession(id: string): Promise<GameSession> {
    const response: AxiosResponse<ApiResponse<{ game_session: GameSession }>> = await this.client.get(`/game_sessions/${id}`);
    if (response.data.success && response.data.data) {
      return response.data.data.game_session;
    }
    throw new Error(response.data.message || 'Failed to get game session');
  }

  // Room management
  async getRooms(filters: RoomFilters = {}): Promise<PaginatedResponse<Room>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<PaginatedResponse<Room>> = await this.client.get(`/admin/rooms?${params}`);
    return response.data;
  }

  async getRoom(id: string): Promise<Room> {
    const response: AxiosResponse<ApiResponse<{ room: Room }>> = await this.client.get(`/admin/rooms/${id}`);
    if (response.data.success && response.data.data) {
      return response.data.data.room;
    }
    throw new Error(response.data.message || 'Failed to get room');
  }

  async getDetailedRoom(id: string): Promise<DetailedRoom> {
    const response: AxiosResponse<ApiResponse<{ room: DetailedRoom }>> = await this.client.get(`/admin/rooms/${id}/details`);
    if (response.data.success && response.data.data) {
      return response.data.data.room;
    }
    throw new Error(response.data.message || 'Failed to get detailed room information');
  }

  async getRoomPlayers(id: string): Promise<RoomPlayer[]> {
    const response: AxiosResponse<ApiResponse<{ players: RoomPlayer[] }>> = await this.client.get(`/admin/rooms/${id}/players`);
    if (response.data.success && response.data.data) {
      return response.data.data.players;
    }
    throw new Error(response.data.message || 'Failed to get room players');
  }

  async createRoom(roomData: CreateRoomForm): Promise<Room> {
    const response: AxiosResponse<ApiResponse<{ room: Room }>> = await this.client.post('/admin/rooms', { room: roomData });
    if (response.data.success && response.data.data) {
      return response.data.data.room;
    }
    throw new Error(response.data.message || 'Failed to create room');
  }

  async deleteRoom(id: string, reason?: string): Promise<void> {
    const response: AxiosResponse<ApiResponse<Record<string, unknown>>> = await this.client.delete(`/admin/rooms/${id}`, {
      data: { reason: reason || 'Administrative deletion' }
    });
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete room');
    }
  }

  async kickPlayer(roomId: string, kickData: KickPlayerForm): Promise<void> {
    const response: AxiosResponse<ApiResponse<Record<string, unknown>>> = await this.client.post(`/admin/rooms/${roomId}/kick-player`, {
      user_id: kickData.user_id,
      reason: kickData.reason || 'Kicked by administrator',
      notify_player: kickData.notify_player !== false
    });
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to kick player');
    }
  }

  // Game settings management
  async getGameSettings(gameType?: string): Promise<ApiResponse<{ game_settings: Record<string, unknown> }>> {
    const params = gameType ? `?game_type=${gameType}` : '';
    const response: AxiosResponse<ApiResponse<{ game_settings: Record<string, unknown> }>> =
      await this.client.get(`/admin/game-settings${params}`);
    return response.data;
  }

  async updateGameSettings(gameType: string, settings: Record<string, unknown>): Promise<void> {
    const response: AxiosResponse<ApiResponse<Record<string, unknown>>> = await this.client.post('/admin/game-settings', {
      game_type: gameType,
      settings,
      sync_to_game_service: true
    });
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update game settings');
    }
  }

  // Game Configuration management
  async getGameConfigurations(): Promise<GameConfigurationsResponse> {
    const response: AxiosResponse<ApiResponse<GameConfigurationsResponse>> = await this.client.get('/admin/game_configurations');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || 'Failed to get game configurations');
  }

  async getGameConfiguration(gameType: string): Promise<GameConfiguration> {
    const response: AxiosResponse<ApiResponse<{ configuration: GameConfiguration }>> = await this.client.get(`/admin/game_configurations/${gameType}`);
    if (response.data.success && response.data.data) {
      return response.data.data.configuration;
    }
    throw new Error(response.data.message || 'Failed to get game configuration');
  }

  async updateGameConfiguration(gameType: string, configData: GameConfigurationUpdate): Promise<GameConfiguration> {
    const response: AxiosResponse<ApiResponse<{ configuration: GameConfiguration }>> = await this.client.put(`/admin/game_configurations/${gameType}`, configData);
    if (response.data.success && response.data.data) {
      return response.data.data.configuration;
    }
    throw new Error(response.data.message || 'Failed to update game configuration');
  }

  async getGameStateDurations(gameType: string): Promise<GameStateDurations> {
    const response: AxiosResponse<ApiResponse<{ state_durations: GameStateDurations }>> = await this.client.get(`/admin/game_configurations/${gameType}/state_durations`);
    if (response.data.success && response.data.data) {
      return response.data.data.state_durations;
    }
    throw new Error(response.data.message || 'Failed to get game state durations');
  }

  async updateGameStateDurations(gameType: string, durations: Partial<GameStateDurations>): Promise<GameStateDurations> {
    const response: AxiosResponse<ApiResponse<{ state_durations: GameStateDurations }>> = await this.client.put(`/admin/game_configurations/${gameType}/state_durations`, { state_durations: durations });
    if (response.data.success && response.data.data) {
      return response.data.data.state_durations;
    }
    throw new Error(response.data.message || 'Failed to update game state durations');
  }

  async resetGameConfigurationDefaults(gameType?: string): Promise<void> {
    const data = gameType ? { game_type: gameType } : {};
    const response: AxiosResponse<ApiResponse<Record<string, unknown>>> = await this.client.post('/admin/game_configurations/reset_defaults', data);
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to reset game configuration defaults');
    }
  }
}

export const apiClient = new ApiClient();
export default apiClient;
