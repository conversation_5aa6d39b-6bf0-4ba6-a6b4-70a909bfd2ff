import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format, formatDistanceToNow, parseISO } from 'date-fns';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Date formatting utilities
export function formatDate(date: string | Date, formatStr: string = 'MMM dd, yyyy'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, formatStr);
}

export function formatDateTime(date: string | Date): string {
  return formatDate(date, 'MMM dd, yyyy HH:mm');
}

export function formatRelativeTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return formatDistanceToNow(dateObj, { addSuffix: true });
}

// Number formatting utilities
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

export function formatNumber(num: number, options?: Intl.NumberFormatOptions): string {
  return new Intl.NumberFormat('en-US', options).format(num);
}

export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

// Status formatting utilities
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    // User statuses
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    suspended: 'bg-yellow-100 text-yellow-800',
    banned: 'bg-red-100 text-red-800',

    // Transaction statuses
    pending: 'bg-yellow-100 text-yellow-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800',

    // Game session statuses
    waiting: 'bg-blue-100 text-blue-800',
    playing: 'bg-purple-100 text-purple-800',
    finished: 'bg-green-100 text-green-800',

    // System statuses
    operational: 'bg-green-100 text-green-800',
    maintenance: 'bg-yellow-100 text-yellow-800',
    degraded: 'bg-red-100 text-red-800',
  };

  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';
}

export function getRoleColor(role: string): string {
  const roleColors: Record<string, string> = {
    player: 'bg-blue-100 text-blue-800',
    admin: 'bg-purple-100 text-purple-800',
    moderator: 'bg-orange-100 text-orange-800',
  };

  return roleColors[role.toLowerCase()] || 'bg-gray-100 text-gray-800';
}

export function getGameTypeColor(gameType: string): string {
  const gameTypeColors: Record<string, string> = {
    prizewheel: 'bg-indigo-100 text-indigo-800',
    amidakuji: 'bg-teal-100 text-teal-800',
  };

  return gameTypeColors[gameType.toLowerCase()] || 'bg-gray-100 text-gray-800';
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUsername(username: string): boolean {
  // Username should be 3-50 characters, alphanumeric and underscores
  const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;
  return usernameRegex.test(username);
}

export function isValidPassword(password: string): boolean {
  // Password should be at least 6 characters
  return password.length >= 6;
}

// Data transformation utilities
export function transformUserForTable(user: Record<string, unknown>) {
  return {
    ...user,
    formattedBalance: formatCurrency(user.balance as number),
    formattedLastLogin: user.last_login_at ? formatRelativeTime(user.last_login_at as string) : 'Never',
    formattedCreatedAt: formatDate(user.created_at as string),
    statusColor: getStatusColor(user.status as string),
    roleColor: getRoleColor(user.role as string),
  };
}

export function transformTransactionForTable(transaction: Record<string, unknown>) {
  return {
    ...transaction,
    formattedAmount: formatCurrency(Math.abs(transaction.amount as number)),
    formattedBalanceBefore: formatCurrency(transaction.balance_before as number),
    formattedBalanceAfter: formatCurrency(transaction.balance_after as number),
    formattedCreatedAt: formatDateTime(transaction.created_at as string),
    statusColor: getStatusColor(transaction.status as string),
    isDebit: (transaction.amount as number) < 0,
  };
}

export function transformGameSessionForTable(session: Record<string, unknown>) {
  const winAmount = session.win_amount as number;
  const betAmount = session.bet_amount as number;
  return {
    ...session,
    formattedBetAmount: formatCurrency(betAmount),
    formattedWinAmount: formatCurrency(winAmount),
    formattedStartedAt: session.started_at ? formatDateTime(session.started_at as string) : '-',
    formattedEndedAt: session.ended_at ? formatDateTime(session.ended_at as string) : '-',
    statusColor: getStatusColor(session.status as string),
    gameTypeColor: getGameTypeColor(session.game_type as string),
    profitLoss: winAmount - betAmount,
    formattedProfitLoss: formatCurrency(winAmount - betAmount),
  };
}

export function transformRoomForTable(room: Record<string, unknown>) {
  const currentPlayers = room.current_players as number;
  const maxPlayers = room.max_players as number;
  return {
    ...room,
    formattedBetAmount: formatCurrency(room.bet_amount as number),
    formattedPrizePool: formatCurrency(room.prize_pool as number),
    formattedCreatedAt: formatDateTime(room.created_at as string),
    statusColor: getStatusColor(room.status as string),
    gameTypeColor: getGameTypeColor(room.game_type as string),
    occupancyRate: (currentPlayers / maxPlayers) * 100,
    formattedOccupancy: `${currentPlayers}/${maxPlayers}`,
  };
}

// Chart data utilities
export function prepareChartData(data: Record<string, unknown>[], xKey: string, yKey: string) {
  return data.map(item => ({
    name: item[xKey],
    value: item[yKey],
  }));
}

export function aggregateDataByDate(data: Record<string, unknown>[], dateKey: string, valueKey: string) {
  const aggregated: Record<string, number> = {};

  data.forEach(item => {
    const date = formatDate(item[dateKey] as string, 'yyyy-MM-dd');
    aggregated[date] = (aggregated[date] || 0) + ((item[valueKey] as number) || 0);
  });

  return Object.entries(aggregated).map(([date, value]) => ({
    date,
    value,
    name: formatDate(date, 'MMM dd'),
  }));
}

// Error handling utilities
export function getErrorMessage(error: unknown): string {
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response?: { data?: { message?: string } } };
    if (axiosError.response?.data?.message) {
      return axiosError.response.data.message;
    }
  }
  if (error instanceof Error && error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
}

export function getValidationErrors(error: unknown): Record<string, string[]> {
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response?: { data?: { errors?: Record<string, string[]> } } };
    if (axiosError.response?.data?.errors) {
      return axiosError.response.data.errors;
    }
  }
  return {};
}

// Local storage utilities
export function getFromStorage(key: string, defaultValue: unknown = null) {
  if (typeof window === 'undefined') return defaultValue;

  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch {
    return defaultValue;
  }
}

export function setToStorage(key: string, value: unknown) {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch {
    // Ignore storage errors
  }
}

export function removeFromStorage(key: string) {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem(key);
  } catch {
    // Ignore storage errors
  }
}

// Debounce utility
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// URL utilities
export function buildQueryString(params: Record<string, unknown>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  return searchParams.toString();
}

// Copy to clipboard utility
export async function copyToClipboard(text: string): Promise<boolean> {
  if (typeof window === 'undefined') return false;

  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch {
      return false;
    }
  }
}
