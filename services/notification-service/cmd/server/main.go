package main

import (
"context"
"fmt"
"log"
"net"
"net/http"
"os"
"os/signal"
"syscall"
"time"

"github.com/sirupsen/logrus"
"google.golang.org/grpc"
"google.golang.org/grpc/health"
"google.golang.org/grpc/health/grpc_health_v1"
"google.golang.org/grpc/reflection"

"github.com/xzgame/notification-service/internal/config"
"github.com/xzgame/notification-service/internal/handlers"
"github.com/xzgame/notification-service/internal/models"
"github.com/xzgame/notification-service/internal/services"
"github.com/xzgame/notification-service/pkg/redis"
wshandler "github.com/xzgame/notification-service/pkg/websocket"
pb "github.com/xzgame/notification-service/proto"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Invalid configuration: %v", err)
	}

	logger.WithFields(logrus.Fields{
"port":        cfg.Port,
"ws_port":     cfg.WSPort,
"environment": cfg.Environment,
"log_level":   cfg.LogLevel,
}).Info("Starting Notification Service")

	// Set log level
	if level, err := logrus.ParseLevel(cfg.LogLevel); err == nil {
		logger.SetLevel(level)
	}

	// Initialize Redis client
	redisClient, err := redis.NewRedisClient(cfg.RedisURL, cfg.RedisPassword, cfg.RedisDB)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to Redis")
	}
	defer redisClient.Close()

	// Test Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := redisClient.Ping(ctx); err != nil {
		logger.WithError(err).Fatal("Failed to ping Redis")
	}
	logger.Info("Successfully connected to Redis")

	// Initialize rate limiter
	var rateLimiter handlers.RateLimiter
	if cfg.RateLimitEnabled {
		rateLimiter = handlers.NewTokenBucketRateLimiter(
cfg.RateLimitPerSecond,
cfg.RateLimitBurst,
cfg.RateLimitCleanupInterval,
)
	} else {
		rateLimiter = handlers.NewNoOpRateLimiter()
	}

	// Initialize WebSocket handler
	wsConfig := wshandler.WebSocketConfig{
		ReadTimeout:    cfg.WSReadTimeout,
		WriteTimeout:   cfg.WSWriteTimeout,
		PingInterval:   cfg.WSPingInterval,
		MaxConnections: cfg.WSMaxConnections,
		BufferSize:     cfg.WSBufferSize,
		AllowedOrigins: cfg.WSAllowedOrigins,
		OriginCheck:    cfg.WSOriginCheck,
	}

	authFunc := func(token string) (*models.WebSocketConnection, error) {
		// Simple mock authentication - in production, validate with auth service
		return &models.WebSocketConnection{
			ID:          fmt.Sprintf("conn_%d", time.Now().UnixNano()),
			UserID:      "user_123",
			Username:    "test_user",
			ConnectedAt: time.Now(),
		}, nil
	}

	wsHandler := wshandler.NewWebSocketHandler(wsConfig, authFunc, rateLimiter, logger)

	// Initialize notification service
	notificationService := services.NewNotificationService(
redisClient,
wsHandler,
cfg,
logger,
)

	// Start event processor
	if err := notificationService.StartEventProcessor(ctx); err != nil {
		logger.WithError(err).Fatal("Failed to start event processor")
	}

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Create gRPC service wrapper (simplified for build test)
	grpcService := &GRPCServer{
		notificationService: notificationService,
		logger:              logger,
	}

	// Register notification service
	pb.RegisterNotificationServiceServer(grpcServer, grpcService)

	// Register health check service
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(grpcServer, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// Enable reflection for development
	if cfg.IsDevelopment() {
		reflection.Register(grpcServer)
	}

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		logger.WithError(err).Fatal("Failed to create gRPC listener")
	}

	go func() {
		logger.WithField("address", grpcListener.Addr().String()).Info("Notification Service gRPC server starting")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC server")
		}
	}()

	// Start WebSocket server
	http.HandleFunc(cfg.WSPath, wsHandler.HandleWebSocket)
	wsServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.WSPort),
		Handler: nil,
	}

	go func() {
		logger.WithField("address", wsServer.Addr).Info("WebSocket server starting")
		if err := wsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve WebSocket server")
		}
	}()

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("Shutting down Notification Service...")

	// Graceful shutdown
	grpcServer.GracefulStop()
	
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()
	wsServer.Shutdown(shutdownCtx)

	notificationService.StopEventProcessor(ctx)
	
	logger.Info("Notification Service shutdown complete")
}

// GRPCServer implements the gRPC NotificationService interface (simplified)
type GRPCServer struct {
	pb.UnimplementedNotificationServiceServer
	notificationService services.NotificationService
	logger              *logrus.Logger
}
