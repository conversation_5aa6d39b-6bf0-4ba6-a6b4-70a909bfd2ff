package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
)

// NotificationRouter handles routing and filtering of incoming events
type NotificationRouter struct {
	redisClient      *redis.RedisClient
	subscriptionMgr  *SubscriptionManager
	realTimeNotifier *RealTimeNotifier
	logger           *logrus.Logger
	eventProcessors  map[string]EventProcessor
	isRunning        bool
	stopChannel      chan bool
}

// EventProcessor defines the interface for processing specific event types
type EventProcessor interface {
	ProcessEvent(ctx context.Context, event *models.IncomingEvent) error
	GetSupportedEventTypes() []string
}

// NewNotificationRouter creates a new notification router
func NewNotificationRouter(
	redisClient *redis.RedisClient,
	subscriptionMgr *SubscriptionManager,
	realTimeNotifier *RealTimeNotifier,
	logger *logrus.Logger,
) *NotificationRouter {
	return &NotificationRouter{
		redisClient:      redisClient,
		subscriptionMgr:  subscriptionMgr,
		realTimeNotifier: realTimeNotifier,
		logger:           logger,
		eventProcessors:  make(map[string]EventProcessor),
		stopChannel:      make(chan bool),
	}
}

// StartEventProcessor starts the event processing loop
func (nr *NotificationRouter) StartEventProcessor(ctx context.Context) error {
	nr.logger.Info("Starting notification router event processor")

	nr.isRunning = true

	// Subscribe to all relevant Redis channels
	patterns := []string{
		"room:*:events",
		"game:room:*",
		"user:*:notifications",
		"game:lobby:updates",
		"global:*",
		"system:events",
		"socket:events",
	}

	pubsub := nr.redisClient.PSubscribe(ctx, patterns...)
	defer pubsub.Close()

	// Process incoming messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			nr.logger.Info("Event processor stopped due to context cancellation")
			nr.isRunning = false
			return ctx.Err()
		case <-nr.stopChannel:
			nr.logger.Info("Event processor stopped")
			nr.isRunning = false
			return nil
		case msg := <-ch:
			if err := nr.processIncomingMessage(ctx, msg.Channel, msg.Payload); err != nil {
				nr.logger.WithError(err).WithField("channel", msg.Channel).Error("Failed to process incoming message")
			}
		}
	}
}

// StopEventProcessor stops the event processing loop
func (nr *NotificationRouter) StopEventProcessor() {
	if nr.isRunning {
		nr.stopChannel <- true
	}
}

// RegisterEventProcessor registers a custom event processor
func (nr *NotificationRouter) RegisterEventProcessor(processor EventProcessor) {
	for _, eventType := range processor.GetSupportedEventTypes() {
		nr.eventProcessors[eventType] = processor
		nr.logger.WithField("event_type", eventType).Info("Registered event processor")
	}
}

// processIncomingMessage processes an incoming Redis message
func (nr *NotificationRouter) processIncomingMessage(ctx context.Context, channel, payload string) error {
	nr.logger.WithField("channel", channel).Debug("Processing incoming message")

	// Parse the incoming event
	event, err := nr.parseIncomingEvent(channel, payload)
	if err != nil {
		return fmt.Errorf("failed to parse incoming event: %w", err)
	}

	// Route the event based on its type and channel
	return nr.routeEvent(ctx, event)
}

// parseIncomingEvent parses an incoming Redis message into a structured event
func (nr *NotificationRouter) parseIncomingEvent(channel, payload string) (*models.IncomingEvent, error) {
	// Try to parse as JSON
	var eventData map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &eventData); err != nil {
		// If JSON parsing fails, treat as plain text
		eventData = map[string]interface{}{
			"message": payload,
		}
	}

	// Extract event information
	event := &models.IncomingEvent{
		Channel:    channel,
		RawPayload: payload,
		EventData:  eventData,
		ReceivedAt: time.Now(),
	}

	// Extract event type from different possible structures
	if eventObj, exists := eventData["event"]; exists {
		if eventMap, ok := eventObj.(map[string]interface{}); ok {
			if eventType, exists := eventMap["type"]; exists {
				if typeStr, ok := eventType.(string); ok {
					event.EventType = typeStr
				}
			}
			if payload, exists := eventMap["payload"]; exists {
				if payloadMap, ok := payload.(map[string]interface{}); ok {
					event.Payload = payloadMap
				}
			}
		}
	} else if eventType, exists := eventData["type"]; exists {
		if typeStr, ok := eventType.(string); ok {
			event.EventType = typeStr
		}
	}

	// Extract metadata
	if metadata, exists := eventData["metadata"]; exists {
		if metadataMap, ok := metadata.(map[string]interface{}); ok {
			event.Metadata = metadataMap
		}
	}

	// Determine channel type and ID from channel name
	event.ChannelType, event.ChannelID = nr.parseChannelInfo(channel)

	return event, nil
}

// routeEvent routes an event to appropriate handlers
func (nr *NotificationRouter) routeEvent(ctx context.Context, event *models.IncomingEvent) error {
	nr.logger.WithFields(logrus.Fields{
		"event_type":   event.EventType,
		"channel":      event.Channel,
		"channel_type": event.ChannelType,
		"channel_id":   event.ChannelID,
	}).Debug("Routing event")

	// Check if there's a custom processor for this event type
	if processor, exists := nr.eventProcessors[event.EventType]; exists {
		if err := processor.ProcessEvent(ctx, event); err != nil {
			nr.logger.WithError(err).WithField("event_type", event.EventType).Error("Custom processor failed")
		}
	}

	// Route to appropriate handler based on channel type
	switch event.ChannelType {
	case "room":
		return nr.handleRoomEvent(ctx, event)
	case "game":
		return nr.handleGameEvent(ctx, event)
	case "user":
		return nr.handleUserEvent(ctx, event)
	case "lobby":
		return nr.handleLobbyEvent(ctx, event)
	case "system":
		return nr.handleSystemEvent(ctx, event)
	case "global":
		return nr.handleGlobalEvent(ctx, event)
	case "socket":
		return nr.handleSocketEvent(ctx, event)
	default:
		nr.logger.WithField("channel_type", event.ChannelType).Warn("Unknown channel type")
		return nil
	}
}

// handleRoomEvent handles room-specific events
func (nr *NotificationRouter) handleRoomEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get subscriptions for this room
	channelPattern := fmt.Sprintf("room:%s:events", event.ChannelID)
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, channelPattern)
	if err != nil {
		return fmt.Errorf("failed to get room subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleGameEvent handles game-specific events
func (nr *NotificationRouter) handleGameEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get subscriptions for this game session
	channelPattern := fmt.Sprintf("game:room:%s", event.ChannelID)
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, channelPattern)
	if err != nil {
		return fmt.Errorf("failed to get game subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleUserEvent handles user-specific events
func (nr *NotificationRouter) handleUserEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get subscriptions for this user
	channelPattern := fmt.Sprintf("user:%s:notifications", event.ChannelID)
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, channelPattern)
	if err != nil {
		return fmt.Errorf("failed to get user subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleLobbyEvent handles lobby events
func (nr *NotificationRouter) handleLobbyEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get lobby subscriptions
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, "game:lobby:updates")
	if err != nil {
		return fmt.Errorf("failed to get lobby subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleSystemEvent handles system events
func (nr *NotificationRouter) handleSystemEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get system event subscriptions
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, "system:events")
	if err != nil {
		return fmt.Errorf("failed to get system subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleGlobalEvent handles global events
func (nr *NotificationRouter) handleGlobalEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Get global subscriptions
	subscriptions, err := nr.subscriptionMgr.GetChannelSubscriptions(ctx, "global:*")
	if err != nil {
		return fmt.Errorf("failed to get global subscriptions: %w", err)
	}

	// Filter and notify subscribers
	return nr.notifySubscribers(ctx, event, subscriptions)
}

// handleSocketEvent handles socket gateway events
func (nr *NotificationRouter) handleSocketEvent(ctx context.Context, event *models.IncomingEvent) error {
	// Forward socket events to real-time notifier
	return nr.realTimeNotifier.HandleSocketEvent(ctx, event)
}

// notifySubscribers notifies all matching subscribers
func (nr *NotificationRouter) notifySubscribers(ctx context.Context, event *models.IncomingEvent, subscriptions []*models.EnhancedEventSubscription) error {
	notifiedCount := 0

	for _, subscription := range subscriptions {
		// Check if event type matches subscription
		if len(subscription.EventTypes) > 0 {
			eventMatches := false
			for _, eventType := range subscription.EventTypes {
				if eventType == event.EventType || eventType == "*" {
					eventMatches = true
					break
				}
			}
			if !eventMatches {
				continue
			}
		}

		// Apply filters if any
		if len(subscription.Filters) > 0 {
			if !nr.applyFilters(event, subscription.Filters) {
				continue
			}
		}

		// Send notification to user
		if err := nr.realTimeNotifier.NotifyUser(ctx, subscription.UserID, event); err != nil {
			nr.logger.WithError(err).WithField("user_id", subscription.UserID).Error("Failed to notify user")
		} else {
			notifiedCount++
		}
	}

	nr.logger.WithFields(logrus.Fields{
		"event_type":     event.EventType,
		"channel":        event.Channel,
		"notified_count": notifiedCount,
	}).Debug("Event routed to subscribers")

	return nil
}

// applyFilters applies subscription filters to an event
func (nr *NotificationRouter) applyFilters(event *models.IncomingEvent, filters map[string]interface{}) bool {
	// Simple filter implementation - can be extended
	for key, expectedValue := range filters {
		if actualValue, exists := event.Payload[key]; exists {
			if actualValue != expectedValue {
				return false
			}
		} else if actualValue, exists := event.EventData[key]; exists {
			if actualValue != expectedValue {
				return false
			}
		} else {
			return false // Filter key not found
		}
	}
	return true
}

// parseChannelInfo extracts channel type and ID from channel name
func (nr *NotificationRouter) parseChannelInfo(channel string) (string, string) {
	parts := strings.Split(channel, ":")
	if len(parts) >= 2 {
		channelType := parts[0]
		channelID := parts[1]
		return channelType, channelID
	}
	return "unknown", ""
}

// GetRouterStats returns router statistics
func (nr *NotificationRouter) GetRouterStats() *models.RouterStats {
	return &models.RouterStats{
		IsRunning:         nr.isRunning,
		SupportedChannels: []string{"room", "game", "user", "lobby", "system", "global", "socket"},
		ProcessorCount:    len(nr.eventProcessors),
		Version:           "1.0.0",
	}
}
