package services

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
)

// ColorEventHandler handles color-related events from game-engine-service
type ColorEventHandler struct {
	eventBroadcaster *EventBroadcaster
	redisClient      *redis.RedisClient
	logger           *logrus.Logger
}

// NewColorEventHandler creates a new color event handler
func NewColorEventHandler(eventBroadcaster *EventBroadcaster, redisClient *redis.RedisClient, logger *logrus.Logger) *ColorEventHandler {
	return &ColorEventHandler{
		eventBroadcaster: eventBroadcaster,
		redisClient:      redisClient,
		logger:           logger,
	}
}

// ColorAvailabilityEvent represents a color availability update event
type ColorAvailabilityEvent struct {
	RoomID    string                 `json:"roomId"`
	Timestamp string                 `json:"timestamp"`
	Colors    map[string]interface{} `json:"colors"`
	Message   string                 `json:"message"`
}

// HandleColorAvailabilityUpdate processes color availability updates from game-engine-service
func (h *ColorEventHandler) HandleColorAvailabilityUpdate(ctx context.Context, eventData map[string]interface{}) error {
	h.logger.WithFields(logrus.Fields{
		"event_type": "color_availability_updated",
		"source":     "game-engine-service",
	}).Info("Processing color availability update")

	// Parse the event data
	var colorEvent ColorAvailabilityEvent
	eventBytes, err := json.Marshal(eventData)
	if err != nil {
		return fmt.Errorf("failed to marshal event data: %w", err)
	}

	if err := json.Unmarshal(eventBytes, &colorEvent); err != nil {
		return fmt.Errorf("failed to unmarshal color event: %w", err)
	}

	// Validate required fields
	if colorEvent.RoomID == "" {
		return fmt.Errorf("missing roomId in color availability event")
	}

	// Broadcast to room subscribers via socket gateway
	if err := h.broadcastToSocketGateway(ctx, &colorEvent); err != nil {
		h.logger.WithError(err).Error("Failed to broadcast to socket gateway")
		return err
	}

	// Broadcast to room-specific notification channels
	if err := h.broadcastToRoomChannels(ctx, &colorEvent); err != nil {
		h.logger.WithError(err).Error("Failed to broadcast to room channels")
		return err
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":         colorEvent.RoomID,
		"availableCount": h.getAvailableCount(colorEvent.Colors),
		"selectedCount":  h.getSelectedCount(colorEvent.Colors),
	}).Info("Color availability update processed successfully")

	return nil
}

// broadcastToSocketGateway sends the event to socket gateway for real-time client updates
func (h *ColorEventHandler) broadcastToSocketGateway(ctx context.Context, colorEvent *ColorAvailabilityEvent) error {
	// Create socket gateway compatible event
	socketEvent := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "color_availability_updated",
			"payload":   colorEvent,
			"timestamp": colorEvent.Timestamp,
		},
		"metadata": map[string]interface{}{
			"serviceId": "notification-service",
			"version":   "1.0.0",
			"priority":  1,
		},
	}

	// Publish to room-specific channel that socket gateway subscribes to
	roomChannel := fmt.Sprintf("room:%s:events", colorEvent.RoomID)
	eventJSON, err := json.Marshal(socketEvent)
	if err != nil {
		return fmt.Errorf("failed to marshal socket event: %w", err)
	}

	if err := h.redisClient.Publish(ctx, roomChannel, string(eventJSON)); err != nil {
		return fmt.Errorf("failed to publish to room channel %s: %w", roomChannel, err)
	}

	h.logger.WithFields(logrus.Fields{
		"channel": roomChannel,
		"roomId":  colorEvent.RoomID,
	}).Debug("Color availability event sent to socket gateway")

	return nil
}

// broadcastToRoomChannels sends the event to room-specific notification channels
func (h *ColorEventHandler) broadcastToRoomChannels(ctx context.Context, colorEvent *ColorAvailabilityEvent) error {
	// Create room event request
	roomEventRequest := &models.RoomEventRequest{
		RoomID:            colorEvent.RoomID,
		EventType:         "color_availability_updated",
		EventData:         map[string]interface{}{
			"roomId":    colorEvent.RoomID,
			"timestamp": colorEvent.Timestamp,
			"colors":    colorEvent.Colors,
			"message":   colorEvent.Message,
		},
		PlayerIDs:         []string{}, // Will be broadcasted to all room subscribers
		BroadcastToSocket: false,      // Already handled above
	}

	// Use the event broadcaster to handle the room event
	return h.eventBroadcaster.BroadcastRoomEvent(ctx, roomEventRequest)
}

// Helper methods to extract statistics from color data
func (h *ColorEventHandler) getAvailableCount(colors map[string]interface{}) int {
	if available, ok := colors["available"].([]interface{}); ok {
		return len(available)
	}
	return 0
}

func (h *ColorEventHandler) getSelectedCount(colors map[string]interface{}) int {
	if selected, ok := colors["selected"].([]interface{}); ok {
		return len(selected)
	}
	return 0
}

// StartColorEventListener starts listening for color events from game-engine-service
func (h *ColorEventHandler) StartColorEventListener(ctx context.Context) error {
	h.logger.Info("Starting color event listener")

	// Subscribe to notification events channel
	pubsub := h.redisClient.Subscribe(ctx, "notification:events")
	defer pubsub.Close()

	// Listen for messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			h.logger.Info("Color event listener stopped")
			return ctx.Err()
		case msg := <-ch:
			h.handleRedisMessage(ctx, msg.Payload)
		}
	}
}

// handleRedisMessage processes incoming Redis messages
func (h *ColorEventHandler) handleRedisMessage(ctx context.Context, payload string) {
	var message map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &message); err != nil {
		h.logger.WithError(err).Error("Failed to parse Redis message")
		return
	}

	// Check if it's a color availability event
	if event, ok := message["event"].(map[string]interface{}); ok {
		if eventType, ok := event["type"].(string); ok && eventType == "color_availability_updated" {
			if eventPayload, ok := event["payload"].(map[string]interface{}); ok {
				if err := h.HandleColorAvailabilityUpdate(ctx, eventPayload); err != nil {
					h.logger.WithError(err).Error("Failed to handle color availability update")
				}
			}
		}
	}
}

// GetColorEventStats returns statistics about color event processing
func (h *ColorEventHandler) GetColorEventStats() map[string]interface{} {
	return map[string]interface{}{
		"supported_events": []string{
			"color_availability_updated",
		},
		"channels": map[string]interface{}{
			"input":  []string{"notification:events"},
			"output": []string{"room:*:events"},
		},
		"version": "1.0.0",
		"status":  "active",
	}
}
