package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
)

// RealTimeNotifier handles real-time notification delivery
type RealTimeNotifier struct {
	redisClient     *redis.RedisClient
	logger          *logrus.Logger
	activeUsers     map[string]*models.UserConnection // user_id -> connection info
	userMutex       sync.RWMutex
	deliveryQueue   chan *models.NotificationDelivery
	isRunning       bool
	stopChannel     chan bool
	deliveryWorkers int
}

// NewRealTimeNotifier creates a new real-time notifier
func NewRealTimeNotifier(redisClient *redis.RedisClient, logger *logrus.Logger) *RealTimeNotifier {
	return &RealTimeNotifier{
		redisClient:     redisClient,
		logger:          logger,
		activeUsers:     make(map[string]*models.UserConnection),
		deliveryQueue:   make(chan *models.NotificationDelivery, 1000), // Buffer for 1000 notifications
		stopChannel:     make(chan bool),
		deliveryWorkers: 5, // Number of delivery workers
	}
}

// Start starts the real-time notification delivery system
func (rtn *RealTimeNotifier) Start(ctx context.Context) error {
	rtn.logger.Info("Starting real-time notifier")

	rtn.isRunning = true

	// Start delivery workers
	for i := 0; i < rtn.deliveryWorkers; i++ {
		go rtn.deliveryWorker(ctx, i)
	}

	// Start connection cleanup routine
	go rtn.connectionCleanupRoutine(ctx)

	rtn.logger.WithField("workers", rtn.deliveryWorkers).Info("Real-time notifier started")

	return nil
}

// Stop stops the real-time notification delivery system
func (rtn *RealTimeNotifier) Stop() {
	if rtn.isRunning {
		rtn.logger.Info("Stopping real-time notifier")
		rtn.isRunning = false
		close(rtn.stopChannel)
		close(rtn.deliveryQueue)
	}
}

// RegisterUserConnection registers a user's connection for real-time notifications
func (rtn *RealTimeNotifier) RegisterUserConnection(ctx context.Context, request *models.RegisterConnectionRequest) error {
	rtn.logger.WithFields(logrus.Fields{
		"user_id":       request.UserID,
		"connection_id": request.ConnectionID,
		"connection_type": request.ConnectionType,
	}).Info("Registering user connection")

	rtn.userMutex.Lock()
	defer rtn.userMutex.Unlock()

	connection := &models.UserConnection{
		UserID:         request.UserID,
		ConnectionID:   request.ConnectionID,
		ConnectionType: request.ConnectionType,
		SocketChannel:  request.SocketChannel,
		IsActive:       true,
		ConnectedAt:    time.Now(),
		LastActivity:   time.Now(),
		Metadata:       request.Metadata,
	}

	rtn.activeUsers[request.UserID] = connection

	rtn.logger.WithField("user_id", request.UserID).Info("User connection registered")

	return nil
}

// UnregisterUserConnection removes a user's connection
func (rtn *RealTimeNotifier) UnregisterUserConnection(ctx context.Context, userID string) error {
	rtn.logger.WithField("user_id", userID).Info("Unregistering user connection")

	rtn.userMutex.Lock()
	defer rtn.userMutex.Unlock()

	delete(rtn.activeUsers, userID)

	rtn.logger.WithField("user_id", userID).Info("User connection unregistered")

	return nil
}

// NotifyUser sends a real-time notification to a specific user
func (rtn *RealTimeNotifier) NotifyUser(ctx context.Context, userID string, event *models.IncomingEvent) error {
	rtn.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"event_type": event.EventType,
	}).Debug("Queuing user notification")

	// Check if user is connected
	rtn.userMutex.RLock()
	connection, isConnected := rtn.activeUsers[userID]
	rtn.userMutex.RUnlock()

	if !isConnected {
		rtn.logger.WithField("user_id", userID).Debug("User not connected, skipping real-time notification")
		return nil
	}

	// Create notification delivery
	delivery := &models.NotificationDelivery{
		UserID:     userID,
		Connection: connection,
		Event:      event,
		CreatedAt:  time.Now(),
		Attempts:   0,
		MaxAttempts: 3,
	}

	// Queue for delivery
	select {
	case rtn.deliveryQueue <- delivery:
		return nil
	default:
		rtn.logger.WithField("user_id", userID).Warn("Delivery queue full, dropping notification")
		return fmt.Errorf("delivery queue full")
	}
}

// NotifyMultipleUsers sends notifications to multiple users
func (rtn *RealTimeNotifier) NotifyMultipleUsers(ctx context.Context, userIDs []string, event *models.IncomingEvent) error {
	rtn.logger.WithFields(logrus.Fields{
		"user_count": len(userIDs),
		"event_type": event.EventType,
	}).Debug("Queuing multiple user notifications")

	for _, userID := range userIDs {
		if err := rtn.NotifyUser(ctx, userID, event); err != nil {
			rtn.logger.WithError(err).WithField("user_id", userID).Error("Failed to queue user notification")
		}
	}

	return nil
}

// HandleSocketEvent handles socket gateway events
func (rtn *RealTimeNotifier) HandleSocketEvent(ctx context.Context, event *models.IncomingEvent) error {
	rtn.logger.WithField("event_type", event.EventType).Debug("Handling socket event")

	// Extract room ID or user ID from event data
	var targetUsers []string

	if roomID, exists := event.EventData["room_id"]; exists {
		if roomIDStr, ok := roomID.(string); ok {
			// Get all users connected to this room
			targetUsers = rtn.getUsersInRoom(roomIDStr)
		}
	} else if userIDs, exists := event.EventData["user_ids"]; exists {
		if userIDsSlice, ok := userIDs.([]interface{}); ok {
			for _, userID := range userIDsSlice {
				if userIDStr, ok := userID.(string); ok {
					targetUsers = append(targetUsers, userIDStr)
				}
			}
		}
	}

	// Notify target users
	if len(targetUsers) > 0 {
		return rtn.NotifyMultipleUsers(ctx, targetUsers, event)
	}

	return nil
}

// deliveryWorker processes notification deliveries
func (rtn *RealTimeNotifier) deliveryWorker(ctx context.Context, workerID int) {
	rtn.logger.WithField("worker_id", workerID).Debug("Starting delivery worker")

	for {
		select {
		case <-ctx.Done():
			rtn.logger.WithField("worker_id", workerID).Debug("Delivery worker stopped due to context")
			return
		case <-rtn.stopChannel:
			rtn.logger.WithField("worker_id", workerID).Debug("Delivery worker stopped")
			return
		case delivery, ok := <-rtn.deliveryQueue:
			if !ok {
				rtn.logger.WithField("worker_id", workerID).Debug("Delivery queue closed")
				return
			}
			rtn.processDelivery(ctx, delivery, workerID)
		}
	}
}

// processDelivery processes a single notification delivery
func (rtn *RealTimeNotifier) processDelivery(ctx context.Context, delivery *models.NotificationDelivery, workerID int) {
	delivery.Attempts++

	rtn.logger.WithFields(logrus.Fields{
		"worker_id":   workerID,
		"user_id":     delivery.UserID,
		"event_type":  delivery.Event.EventType,
		"attempt":     delivery.Attempts,
	}).Debug("Processing notification delivery")

	// Create notification message
	notification := rtn.createNotificationMessage(delivery.Event, delivery.Connection)

	// Deliver based on connection type
	var err error
	switch delivery.Connection.ConnectionType {
	case "websocket":
		err = rtn.deliverViaWebSocket(ctx, delivery.Connection, notification)
	case "sse":
		err = rtn.deliverViaSSE(ctx, delivery.Connection, notification)
	case "redis":
		err = rtn.deliverViaRedis(ctx, delivery.Connection, notification)
	default:
		err = fmt.Errorf("unsupported connection type: %s", delivery.Connection.ConnectionType)
	}

	if err != nil {
		rtn.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    delivery.UserID,
			"attempt":    delivery.Attempts,
			"max_attempts": delivery.MaxAttempts,
		}).Error("Notification delivery failed")

		// Retry if not exceeded max attempts
		if delivery.Attempts < delivery.MaxAttempts {
			// Re-queue with delay
			go func() {
				time.Sleep(time.Duration(delivery.Attempts) * time.Second)
				select {
				case rtn.deliveryQueue <- delivery:
				default:
					rtn.logger.WithField("user_id", delivery.UserID).Warn("Failed to re-queue delivery")
				}
			}()
		} else {
			rtn.logger.WithField("user_id", delivery.UserID).Error("Max delivery attempts exceeded")
		}
	} else {
		rtn.logger.WithFields(logrus.Fields{
			"user_id":    delivery.UserID,
			"event_type": delivery.Event.EventType,
		}).Debug("Notification delivered successfully")

		// Update last activity
		rtn.userMutex.Lock()
		if conn, exists := rtn.activeUsers[delivery.UserID]; exists {
			conn.LastActivity = time.Now()
		}
		rtn.userMutex.Unlock()
	}
}

// createNotificationMessage creates a standardized notification message
func (rtn *RealTimeNotifier) createNotificationMessage(event *models.IncomingEvent, connection *models.UserConnection) map[string]interface{} {
	return map[string]interface{}{
		"type":      "notification",
		"event":     event.EventType,
		"payload":   event.Payload,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"metadata": map[string]interface{}{
			"channel":       event.Channel,
			"connection_id": connection.ConnectionID,
			"user_id":       connection.UserID,
		},
	}
}

// deliverViaWebSocket delivers notification via WebSocket
func (rtn *RealTimeNotifier) deliverViaWebSocket(ctx context.Context, connection *models.UserConnection, notification map[string]interface{}) error {
	// Publish to WebSocket gateway channel
	socketChannel := fmt.Sprintf("websocket:%s", connection.SocketChannel)
	return rtn.publishNotification(ctx, socketChannel, notification)
}

// deliverViaSSE delivers notification via Server-Sent Events
func (rtn *RealTimeNotifier) deliverViaSSE(ctx context.Context, connection *models.UserConnection, notification map[string]interface{}) error {
	// Publish to SSE gateway channel
	sseChannel := fmt.Sprintf("sse:%s", connection.SocketChannel)
	return rtn.publishNotification(ctx, sseChannel, notification)
}

// deliverViaRedis delivers notification via Redis pub/sub
func (rtn *RealTimeNotifier) deliverViaRedis(ctx context.Context, connection *models.UserConnection, notification map[string]interface{}) error {
	// Publish directly to user's Redis channel
	userChannel := fmt.Sprintf("user:%s:realtime", connection.UserID)
	return rtn.publishNotification(ctx, userChannel, notification)
}

// publishNotification publishes a notification to a Redis channel
func (rtn *RealTimeNotifier) publishNotification(ctx context.Context, channel string, notification map[string]interface{}) error {
	notificationJSON, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	return rtn.redisClient.Publish(ctx, channel, string(notificationJSON))
}

// connectionCleanupRoutine periodically cleans up inactive connections
func (rtn *RealTimeNotifier) connectionCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Minute) // Cleanup every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rtn.stopChannel:
			return
		case <-ticker.C:
			rtn.cleanupInactiveConnections()
		}
	}
}

// cleanupInactiveConnections removes inactive user connections
func (rtn *RealTimeNotifier) cleanupInactiveConnections() {
	rtn.userMutex.Lock()
	defer rtn.userMutex.Unlock()

	inactiveThreshold := time.Now().Add(-30 * time.Minute) // 30 minutes
	cleanedCount := 0

	for userID, connection := range rtn.activeUsers {
		if connection.LastActivity.Before(inactiveThreshold) {
			delete(rtn.activeUsers, userID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		rtn.logger.WithField("cleaned_count", cleanedCount).Info("Cleaned up inactive connections")
	}
}

// getUsersInRoom gets all users connected to a specific room
func (rtn *RealTimeNotifier) getUsersInRoom(roomID string) []string {
	rtn.userMutex.RLock()
	defer rtn.userMutex.RUnlock()

	var users []string
	for userID, connection := range rtn.activeUsers {
		// Check if user is in the room (this would need room membership data)
		if connection.IsActive {
			users = append(users, userID)
		}
	}

	return users
}

// GetNotifierStats returns notifier statistics
func (rtn *RealTimeNotifier) GetNotifierStats() *models.NotifierStats {
	rtn.userMutex.RLock()
	defer rtn.userMutex.RUnlock()

	activeConnections := 0
	for _, connection := range rtn.activeUsers {
		if connection.IsActive {
			activeConnections++
		}
	}

	return &models.NotifierStats{
		IsRunning:           rtn.isRunning,
		ActiveConnections:   activeConnections,
		TotalConnections:    len(rtn.activeUsers),
		DeliveryWorkers:     rtn.deliveryWorkers,
		QueueSize:          len(rtn.deliveryQueue),
		SupportedTypes:     []string{"websocket", "sse", "redis"},
	}
}
