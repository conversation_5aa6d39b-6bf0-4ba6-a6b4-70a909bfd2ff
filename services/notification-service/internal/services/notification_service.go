package services

import (
	"context"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/config"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
	"github.com/xzgame/notification-service/pkg/websocket"
)

type NotificationService interface {
	SendNotification(ctx context.Context, request models.SendNotificationRequest) (*models.Notification, error)
	BroadcastNotification(ctx context.Context, request models.BroadcastNotificationRequest) (*models.Notification, error)
	GetNotifications(ctx context.Context, request models.GetNotificationsRequest) ([]*models.Notification, error)
	MarkNotifications(ctx context.Context, request models.MarkNotificationRequest) error
	Subscribe(ctx context.Context, request models.SubscribeRequest) (*models.EventSubscription, error)
	Unsubscribe(ctx context.Context, request models.UnsubscribeRequest) error
	GetSubscriptions(ctx context.Context, userID string) ([]*models.EventSubscription, error)
	HandleWebSocketConnection(ctx context.Context, conn *models.WebSocketConnection) error
	DisconnectWebSocket(ctx context.Context, connectionID string) error
	GetActiveConnections(ctx context.Context, userID string) ([]*models.WebSocketConnection, error)
	ProcessEvent(ctx context.Context, eventType string, eventData map[string]interface{}) error
	StartEventProcessor(ctx context.Context) error
	StopEventProcessor(ctx context.Context) error
	GetNotificationStats(ctx context.Context) (map[string]interface{}, error)
	GetConnectionStats(ctx context.Context) (map[string]interface{}, error)
}

type notificationService struct {
	redisClient *redis.RedisClient
	wsHandler   *websocket.WebSocketHandler
	config      *config.Config
	logger      *logrus.Logger

	// Modular components
	eventBroadcaster    *EventBroadcaster
	subscriptionManager *SubscriptionManager
	notificationRouter  *NotificationRouter
	realTimeNotifier    *RealTimeNotifier
}

func NewNotificationService(
	redisClient *redis.RedisClient,
	wsHandler *websocket.WebSocketHandler,
	config *config.Config,
	logger *logrus.Logger,
) NotificationService {
	// Create modular components
	eventBroadcaster := NewEventBroadcaster(redisClient, logger)
	subscriptionManager := NewSubscriptionManager(redisClient, logger)
	realTimeNotifier := NewRealTimeNotifier(redisClient, logger)
	notificationRouter := NewNotificationRouter(redisClient, subscriptionManager, realTimeNotifier, logger)

	return &notificationService{
		redisClient:         redisClient,
		wsHandler:           wsHandler,
		config:              config,
		logger:              logger,
		eventBroadcaster:    eventBroadcaster,
		subscriptionManager: subscriptionManager,
		notificationRouter:  notificationRouter,
		realTimeNotifier:    realTimeNotifier,
	}
}

func (s *notificationService) SendNotification(ctx context.Context, request models.SendNotificationRequest) (*models.Notification, error) {
	return &models.Notification{}, nil
}

func (s *notificationService) BroadcastNotification(ctx context.Context, request models.BroadcastNotificationRequest) (*models.Notification, error) {
	return &models.Notification{}, nil
}

func (s *notificationService) GetNotifications(ctx context.Context, request models.GetNotificationsRequest) ([]*models.Notification, error) {
	return []*models.Notification{}, nil
}

func (s *notificationService) MarkNotifications(ctx context.Context, request models.MarkNotificationRequest) error {
	return nil
}

func (s *notificationService) Subscribe(ctx context.Context, request models.SubscribeRequest) (*models.EventSubscription, error) {
	// Convert to enhanced request - use event type as channel type for compatibility
	enhancedRequest := &models.EnhancedSubscribeRequest{
		UserID:      request.UserID,
		ChannelType: "event",
		ChannelID:   request.EventType,
		EventTypes:  []string{request.EventType},
		Filters:     make(map[string]interface{}),
	}

	enhancedSub, err := s.subscriptionManager.Subscribe(ctx, enhancedRequest)
	if err != nil {
		return nil, err
	}

	// Convert back to legacy format
	return &models.EventSubscription{
		ID:        enhancedSub.ID,
		UserID:    enhancedSub.UserID,
		EventType: enhancedSub.ChannelID, // Use ChannelID as EventType for compatibility
		CreatedAt: enhancedSub.CreatedAt,
		UpdatedAt: enhancedSub.UpdatedAt,
	}, nil
}

func (s *notificationService) Unsubscribe(ctx context.Context, request models.UnsubscribeRequest) error {
	unsubRequest := &models.UnsubscribeRequest{
		UserID:         request.UserID,
		SubscriptionID: request.SubscriptionID,
	}
	return s.subscriptionManager.Unsubscribe(ctx, unsubRequest)
}

func (s *notificationService) GetSubscriptions(ctx context.Context, userID string) ([]*models.EventSubscription, error) {
	enhancedSubs, err := s.subscriptionManager.GetUserSubscriptions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert to legacy format
	subs := make([]*models.EventSubscription, len(enhancedSubs))
	for i, enhancedSub := range enhancedSubs {
		subs[i] = &models.EventSubscription{
			ID:        enhancedSub.ID,
			UserID:    enhancedSub.UserID,
			EventType: enhancedSub.ChannelID, // Use ChannelID as EventType for compatibility
			CreatedAt: enhancedSub.CreatedAt,
			UpdatedAt: enhancedSub.UpdatedAt,
		}
	}

	return subs, nil
}

func (s *notificationService) HandleWebSocketConnection(ctx context.Context, conn *models.WebSocketConnection) error {
	return nil
}

func (s *notificationService) DisconnectWebSocket(ctx context.Context, connectionID string) error {
	return nil
}

func (s *notificationService) GetActiveConnections(ctx context.Context, userID string) ([]*models.WebSocketConnection, error) {
	return []*models.WebSocketConnection{}, nil
}

func (s *notificationService) ProcessEvent(ctx context.Context, eventType string, eventData map[string]interface{}) error {
	return nil
}

func (s *notificationService) StartEventProcessor(ctx context.Context) error {
	return nil
}

func (s *notificationService) StopEventProcessor(ctx context.Context) error {
	return nil
}

func (s *notificationService) GetNotificationStats(ctx context.Context) (map[string]interface{}, error) {
	return map[string]interface{}{}, nil
}

func (s *notificationService) GetConnectionStats(ctx context.Context) (map[string]interface{}, error) {
	return map[string]interface{}{}, nil
}
