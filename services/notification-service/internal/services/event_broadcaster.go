package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
)

// EventBroadcaster handles broadcasting events to multiple channels and subscribers
type EventBroadcaster struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewEventBroadcaster creates a new event broadcaster
func NewEventBroadcaster(redisClient *redis.RedisClient, logger *logrus.Logger) *EventBroadcaster {
	return &EventBroadcaster{
		redisClient: redisClient,
		logger:      logger,
	}
}

// BroadcastRoomEvent broadcasts an event to all players in a room
func (eb *EventBroadcaster) BroadcastRoomEvent(ctx context.Context, request *models.RoomEventRequest) error {
	eb.logger.WithFields(logrus.Fields{
		"room_id":      request.RoomID,
		"event_type":   request.EventType,
		"player_count": len(request.PlayerIDs),
	}).Info("Broadcasting room event")

	// Create standardized event format
	event := eb.createStandardEvent(request.EventType, request.EventData, "room_event")

	// Broadcast to room channel
	roomChannel := fmt.Sprintf("room:%s:events", request.RoomID)
	if err := eb.publishToChannel(ctx, roomChannel, event); err != nil {
		return fmt.Errorf("failed to broadcast to room channel: %w", err)
	}

	// Broadcast to individual player channels if specified
	if len(request.PlayerIDs) > 0 {
		for _, playerID := range request.PlayerIDs {
			playerChannel := fmt.Sprintf("user:%s:notifications", playerID)
			if err := eb.publishToChannel(ctx, playerChannel, event); err != nil {
				eb.logger.WithError(err).WithField("player_id", playerID).Warn("Failed to broadcast to player channel")
			}
		}
	}

	// Broadcast to socket gateway if enabled
	if request.BroadcastToSocket {
		socketChannel := "socket:events"
		socketEvent := eb.createSocketEvent(request.EventType, request.EventData, request.RoomID)
		if err := eb.publishToChannel(ctx, socketChannel, socketEvent); err != nil {
			eb.logger.WithError(err).Warn("Failed to broadcast to socket gateway")
		}
	}

	eb.logger.WithFields(logrus.Fields{
		"room_id":    request.RoomID,
		"event_type": request.EventType,
		"channels":   []string{roomChannel, "socket:events"},
	}).Info("Room event broadcasted successfully")

	return nil
}

// BroadcastGameEvent broadcasts game-specific events
func (eb *EventBroadcaster) BroadcastGameEvent(ctx context.Context, request *models.GameEventRequest) error {
	eb.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"event_type": request.EventType,
		"game_type":  request.GameType,
	}).Info("Broadcasting game event")

	// Create game event
	event := eb.createStandardEvent(request.EventType, request.EventData, "game_event")

	// Broadcast to game session channel
	gameChannel := fmt.Sprintf("game:room:%s", request.SessionID)
	if err := eb.publishToChannel(ctx, gameChannel, event); err != nil {
		return fmt.Errorf("failed to broadcast to game channel: %w", err)
	}

	// Broadcast to room channel if room ID is provided
	if request.RoomID != "" {
		roomChannel := fmt.Sprintf("room:%s:events", request.RoomID)
		if err := eb.publishToChannel(ctx, roomChannel, event); err != nil {
			eb.logger.WithError(err).Warn("Failed to broadcast to room channel")
		}
	}

	// Broadcast to game type channel for global listeners
	gameTypeChannel := fmt.Sprintf("game_type:%s", request.GameType)
	if err := eb.publishToChannel(ctx, gameTypeChannel, event); err != nil {
		eb.logger.WithError(err).Warn("Failed to broadcast to game type channel")
	}

	channels := []string{gameChannel, gameTypeChannel}
	if request.RoomID != "" {
		channels = append(channels, fmt.Sprintf("room:%s:events", request.RoomID))
	}

	eb.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"event_type": request.EventType,
		"channels":   channels,
	}).Info("Game event broadcasted successfully")

	return nil
}

// BroadcastLobbyEvent broadcasts lobby update events
func (eb *EventBroadcaster) BroadcastLobbyEvent(ctx context.Context, request *models.LobbyEventRequest) error {
	eb.logger.WithFields(logrus.Fields{
		"event_type": request.EventType,
		"action":     request.Action,
	}).Info("Broadcasting lobby event")

	// Create lobby event
	event := eb.createStandardEvent(request.EventType, request.EventData, "lobby_event")

	// Broadcast to lobby updates channel
	lobbyChannel := "game:lobby:updates"
	if err := eb.publishToChannel(ctx, lobbyChannel, event); err != nil {
		return fmt.Errorf("failed to broadcast to lobby channel: %w", err)
	}

	// Broadcast to global room updates if it's a room-related event
	if request.Action != "" {
		globalChannel := "global:room_updates"
		if err := eb.publishToChannel(ctx, globalChannel, event); err != nil {
			eb.logger.WithError(err).Warn("Failed to broadcast to global channel")
		}
	}

	eb.logger.WithFields(logrus.Fields{
		"event_type": request.EventType,
		"channels":   []string{lobbyChannel, "global:room_updates"},
	}).Info("Lobby event broadcasted successfully")

	return nil
}

// BroadcastUserNotification broadcasts notifications to specific users
func (eb *EventBroadcaster) BroadcastUserNotification(ctx context.Context, request *models.UserNotificationRequest) error {
	eb.logger.WithFields(logrus.Fields{
		"user_count":        len(request.UserIDs),
		"notification_type": request.NotificationType,
	}).Info("Broadcasting user notification")

	// Create user notification event
	event := eb.createStandardEvent("user_notification", map[string]interface{}{
		"notification_type": request.NotificationType,
		"data":              request.Data,
		"priority":          request.Priority,
	}, "user_notification")

	// Broadcast to individual user channels
	for _, userID := range request.UserIDs {
		userChannel := fmt.Sprintf("user:%s:notifications", userID)
		if err := eb.publishToChannel(ctx, userChannel, event); err != nil {
			eb.logger.WithError(err).WithField("user_id", userID).Warn("Failed to broadcast to user channel")
		}
	}

	// Broadcast to global notifications channel if specified
	if request.BroadcastGlobally {
		globalChannel := "global:user_notifications"
		globalEvent := event
		globalEvent["user_ids"] = request.UserIDs
		if err := eb.publishToChannel(ctx, globalChannel, globalEvent); err != nil {
			eb.logger.WithError(err).Warn("Failed to broadcast to global notifications channel")
		}
	}

	eb.logger.WithFields(logrus.Fields{
		"user_count":        len(request.UserIDs),
		"notification_type": request.NotificationType,
	}).Info("User notification broadcasted successfully")

	return nil
}

// BroadcastSystemEvent broadcasts system-wide events
func (eb *EventBroadcaster) BroadcastSystemEvent(ctx context.Context, request *models.SystemEventRequest) error {
	eb.logger.WithFields(logrus.Fields{
		"event_type": request.EventType,
		"severity":   request.Severity,
	}).Info("Broadcasting system event")

	// Create system event
	event := eb.createStandardEvent(request.EventType, request.EventData, "system_event")
	event["severity"] = request.Severity

	// Broadcast to system events channel
	systemChannel := "system:events"
	if err := eb.publishToChannel(ctx, systemChannel, event); err != nil {
		return fmt.Errorf("failed to broadcast to system channel: %w", err)
	}

	// Broadcast to health channel if it's a health-related event
	if request.EventType == "health_check" || request.EventType == "service_status" {
		healthChannel := "global:health"
		if err := eb.publishToChannel(ctx, healthChannel, event); err != nil {
			eb.logger.WithError(err).Warn("Failed to broadcast to health channel")
		}
	}

	eb.logger.WithFields(logrus.Fields{
		"event_type": request.EventType,
		"channels":   []string{systemChannel, "global:health"},
	}).Info("System event broadcasted successfully")

	return nil
}

// Helper methods

// createStandardEvent creates a standardized event format
func (eb *EventBroadcaster) createStandardEvent(eventType string, eventData map[string]interface{}, category string) map[string]interface{} {
	return map[string]interface{}{
		"event": map[string]interface{}{
			"type":      eventType,
			"payload":   eventData,
			"timestamp": time.Now().UTC().Format(time.RFC3339),
			"category":  category,
		},
		"metadata": map[string]interface{}{
			"serviceId":     "notification-service",
			"version":       "1.0.0",
			"correlationId": fmt.Sprintf("%s-%d", eventType, time.Now().UnixNano()),
			"priority":      1,
		},
	}
}

// createSocketEvent creates a socket gateway compatible event
func (eb *EventBroadcaster) createSocketEvent(eventType string, eventData map[string]interface{}, roomID string) map[string]interface{} {
	return map[string]interface{}{
		"type":      eventType,
		"payload":   eventData,
		"room_id":   roomID,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"source":    "notification-service",
	}
}

// publishToChannel publishes an event to a Redis channel
func (eb *EventBroadcaster) publishToChannel(ctx context.Context, channel string, event map[string]interface{}) error {
	// Convert event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	// Publish to Redis
	if err := eb.redisClient.Publish(ctx, channel, string(eventJSON)); err != nil {
		return fmt.Errorf("failed to publish to channel %s: %w", channel, err)
	}

	eb.logger.WithFields(logrus.Fields{
		"channel":    channel,
		"event_type": event["event"].(map[string]interface{})["type"],
	}).Debug("Event published to channel")

	return nil
}

// GetBroadcastStats returns broadcasting statistics
func (eb *EventBroadcaster) GetBroadcastStats() *models.BroadcastStats {
	return &models.BroadcastStats{
		SupportedChannels: []string{
			"room:*:events",
			"game:room:*",
			"user:*:notifications",
			"game:lobby:updates",
			"global:room_updates",
			"global:user_notifications",
			"system:events",
			"global:health",
			"socket:events",
		},
		EventFormats: []string{"standard", "socket_gateway"},
		Version:      "1.0.0",
	}
}
