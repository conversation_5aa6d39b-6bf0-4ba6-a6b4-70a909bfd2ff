package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/notification-service/internal/models"
	"github.com/xzgame/notification-service/pkg/redis"
)

// SubscriptionManager handles event subscriptions and routing
type SubscriptionManager struct {
	redisClient   *redis.RedisClient
	logger        *logrus.Logger
	subscriptions map[string]*models.EnhancedEventSubscription // subscription_id -> subscription
	userSubs      map[string][]string                          // user_id -> []subscription_id
	channelSubs   map[string][]string                          // channel -> []subscription_id
	mutex         sync.RWMutex
}

// NewSubscriptionManager creates a new subscription manager
func NewSubscriptionManager(redisClient *redis.RedisClient, logger *logrus.Logger) *SubscriptionManager {
	return &SubscriptionManager{
		redisClient:   redisClient,
		logger:        logger,
		subscriptions: make(map[string]*models.EnhancedEventSubscription),
		userSubs:      make(map[string][]string),
		channelSubs:   make(map[string][]string),
	}
}

// Subscribe creates a new event subscription
func (sm *SubscriptionManager) Subscribe(ctx context.Context, request *models.EnhancedSubscribeRequest) (*models.EnhancedEventSubscription, error) {
	sm.logger.WithFields(logrus.Fields{
		"user_id":      request.UserID,
		"channel_type": request.ChannelType,
		"channel_id":   request.ChannelID,
	}).Info("Creating event subscription")

	// Validate request
	if err := sm.validateSubscribeRequest(request); err != nil {
		return nil, err
	}

	// Generate subscription ID
	subscriptionID := sm.generateSubscriptionID(request.UserID, request.ChannelType, request.ChannelID)

	// Create subscription
	subscription := &models.EnhancedEventSubscription{
		ID:          subscriptionID,
		UserID:      request.UserID,
		ChannelType: request.ChannelType,
		ChannelID:   request.ChannelID,
		EventTypes:  request.EventTypes,
		Filters:     request.Filters,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Determine Redis channel pattern
	channelPattern := sm.buildChannelPattern(request.ChannelType, request.ChannelID)
	subscription.ChannelPattern = channelPattern

	// Store subscription
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// Check for existing subscription
	if existing, exists := sm.subscriptions[subscriptionID]; exists {
		sm.logger.WithField("subscription_id", subscriptionID).Info("Updating existing subscription")
		existing.EventTypes = request.EventTypes
		existing.Filters = request.Filters
		existing.UpdatedAt = time.Now()
		return existing, nil
	}

	// Add new subscription
	sm.subscriptions[subscriptionID] = subscription

	// Update user subscriptions index
	if _, exists := sm.userSubs[request.UserID]; !exists {
		sm.userSubs[request.UserID] = make([]string, 0)
	}
	sm.userSubs[request.UserID] = append(sm.userSubs[request.UserID], subscriptionID)

	// Update channel subscriptions index
	if _, exists := sm.channelSubs[channelPattern]; !exists {
		sm.channelSubs[channelPattern] = make([]string, 0)
	}
	sm.channelSubs[channelPattern] = append(sm.channelSubs[channelPattern], subscriptionID)

	sm.logger.WithFields(logrus.Fields{
		"subscription_id": subscriptionID,
		"user_id":         request.UserID,
		"channel_pattern": channelPattern,
	}).Info("Event subscription created successfully")

	return subscription, nil
}

// Unsubscribe removes an event subscription
func (sm *SubscriptionManager) Unsubscribe(ctx context.Context, request *models.UnsubscribeRequest) error {
	sm.logger.WithFields(logrus.Fields{
		"user_id":         request.UserID,
		"subscription_id": request.SubscriptionID,
	}).Info("Removing event subscription")

	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// Find subscription
	subscription, exists := sm.subscriptions[request.SubscriptionID]
	if !exists {
		return models.NewNotificationError("subscription not found", map[string]interface{}{
			"subscription_id": request.SubscriptionID,
		})
	}

	// Verify ownership
	if subscription.UserID != request.UserID {
		return models.NewNotificationError("unauthorized unsubscribe attempt", map[string]interface{}{
			"subscription_id": request.SubscriptionID,
			"user_id":         request.UserID,
		})
	}

	// Remove from subscriptions
	delete(sm.subscriptions, request.SubscriptionID)

	// Remove from user subscriptions index
	if userSubs, exists := sm.userSubs[request.UserID]; exists {
		sm.userSubs[request.UserID] = sm.removeFromSlice(userSubs, request.SubscriptionID)
		if len(sm.userSubs[request.UserID]) == 0 {
			delete(sm.userSubs, request.UserID)
		}
	}

	// Remove from channel subscriptions index
	if channelSubs, exists := sm.channelSubs[subscription.ChannelPattern]; exists {
		sm.channelSubs[subscription.ChannelPattern] = sm.removeFromSlice(channelSubs, request.SubscriptionID)
		if len(sm.channelSubs[subscription.ChannelPattern]) == 0 {
			delete(sm.channelSubs, subscription.ChannelPattern)
		}
	}

	sm.logger.WithField("subscription_id", request.SubscriptionID).Info("Event subscription removed successfully")

	return nil
}

// GetUserSubscriptions returns all subscriptions for a user
func (sm *SubscriptionManager) GetUserSubscriptions(ctx context.Context, userID string) ([]*models.EnhancedEventSubscription, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	subscriptionIDs, exists := sm.userSubs[userID]
	if !exists {
		return []*models.EnhancedEventSubscription{}, nil
	}

	subscriptions := make([]*models.EnhancedEventSubscription, 0, len(subscriptionIDs))
	for _, subID := range subscriptionIDs {
		if sub, exists := sm.subscriptions[subID]; exists && sub.IsActive {
			subscriptions = append(subscriptions, sub)
		}
	}

	return subscriptions, nil
}

// GetChannelSubscriptions returns all subscriptions for a channel pattern
func (sm *SubscriptionManager) GetChannelSubscriptions(ctx context.Context, channelPattern string) ([]*models.EnhancedEventSubscription, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	subscriptionIDs, exists := sm.channelSubs[channelPattern]
	if !exists {
		return []*models.EnhancedEventSubscription{}, nil
	}

	subscriptions := make([]*models.EnhancedEventSubscription, 0, len(subscriptionIDs))
	for _, subID := range subscriptionIDs {
		if sub, exists := sm.subscriptions[subID]; exists && sub.IsActive {
			subscriptions = append(subscriptions, sub)
		}
	}

	return subscriptions, nil
}

// UpdateSubscription updates an existing subscription
func (sm *SubscriptionManager) UpdateSubscription(ctx context.Context, request *models.UpdateSubscriptionRequest) (*models.EnhancedEventSubscription, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	subscription, exists := sm.subscriptions[request.SubscriptionID]
	if !exists {
		return nil, models.NewNotificationError("subscription not found", map[string]interface{}{
			"subscription_id": request.SubscriptionID,
		})
	}

	// Verify ownership
	if subscription.UserID != request.UserID {
		return nil, models.NewNotificationError("unauthorized update attempt", map[string]interface{}{
			"subscription_id": request.SubscriptionID,
			"user_id":         request.UserID,
		})
	}

	// Update subscription
	if request.EventTypes != nil {
		subscription.EventTypes = request.EventTypes
	}
	if request.Filters != nil {
		subscription.Filters = request.Filters
	}
	if request.IsActive != nil {
		subscription.IsActive = *request.IsActive
	}
	subscription.UpdatedAt = time.Now()

	sm.logger.WithField("subscription_id", request.SubscriptionID).Info("Subscription updated successfully")

	return subscription, nil
}

// CleanupInactiveSubscriptions removes inactive or expired subscriptions
func (sm *SubscriptionManager) CleanupInactiveSubscriptions(ctx context.Context) error {
	sm.logger.Info("Starting cleanup of inactive subscriptions")

	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	cleanedCount := 0
	expiredThreshold := time.Now().Add(-24 * time.Hour) // 24 hours ago

	for subID, subscription := range sm.subscriptions {
		// Remove inactive subscriptions or very old ones
		if !subscription.IsActive || subscription.UpdatedAt.Before(expiredThreshold) {
			// Remove from all indexes
			delete(sm.subscriptions, subID)

			// Remove from user index
			if userSubs, exists := sm.userSubs[subscription.UserID]; exists {
				sm.userSubs[subscription.UserID] = sm.removeFromSlice(userSubs, subID)
				if len(sm.userSubs[subscription.UserID]) == 0 {
					delete(sm.userSubs, subscription.UserID)
				}
			}

			// Remove from channel index
			if channelSubs, exists := sm.channelSubs[subscription.ChannelPattern]; exists {
				sm.channelSubs[subscription.ChannelPattern] = sm.removeFromSlice(channelSubs, subID)
				if len(sm.channelSubs[subscription.ChannelPattern]) == 0 {
					delete(sm.channelSubs, subscription.ChannelPattern)
				}
			}

			cleanedCount++
		}
	}

	sm.logger.WithField("cleaned_count", cleanedCount).Info("Subscription cleanup completed")

	return nil
}

// Helper methods

// validateSubscribeRequest validates a subscribe request
func (sm *SubscriptionManager) validateSubscribeRequest(request *models.EnhancedSubscribeRequest) error {
	if request.UserID == "" {
		return models.NewNotificationError("user ID cannot be empty", nil)
	}

	if request.ChannelType == "" {
		return models.NewNotificationError("channel type cannot be empty", nil)
	}

	validChannelTypes := []string{"room", "user", "game", "lobby", "system", "global"}
	isValidType := false
	for _, validType := range validChannelTypes {
		if request.ChannelType == validType {
			isValidType = true
			break
		}
	}

	if !isValidType {
		return models.NewNotificationError("invalid channel type", map[string]interface{}{
			"channel_type":        request.ChannelType,
			"valid_channel_types": validChannelTypes,
		})
	}

	return nil
}

// generateSubscriptionID generates a unique subscription ID
func (sm *SubscriptionManager) generateSubscriptionID(userID, channelType, channelID string) string {
	return fmt.Sprintf("%s_%s_%s_%d", userID, channelType, channelID, time.Now().UnixNano())
}

// buildChannelPattern builds a Redis channel pattern from subscription details
func (sm *SubscriptionManager) buildChannelPattern(channelType, channelID string) string {
	switch channelType {
	case "room":
		if channelID == "*" {
			return "room:*:events"
		}
		return fmt.Sprintf("room:%s:events", channelID)
	case "user":
		if channelID == "*" {
			return "user:*:notifications"
		}
		return fmt.Sprintf("user:%s:notifications", channelID)
	case "game":
		if channelID == "*" {
			return "game:room:*"
		}
		return fmt.Sprintf("game:room:%s", channelID)
	case "lobby":
		return "game:lobby:updates"
	case "system":
		return "system:events"
	case "global":
		return "global:*"
	default:
		return fmt.Sprintf("%s:%s", channelType, channelID)
	}
}

// removeFromSlice removes an element from a string slice
func (sm *SubscriptionManager) removeFromSlice(slice []string, element string) []string {
	for i, v := range slice {
		if v == element {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice
}

// GetSubscriptionStats returns subscription statistics
func (sm *SubscriptionManager) GetSubscriptionStats() *models.SubscriptionStats {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	activeCount := 0
	for _, sub := range sm.subscriptions {
		if sub.IsActive {
			activeCount++
		}
	}

	return &models.SubscriptionStats{
		TotalSubscriptions:  len(sm.subscriptions),
		ActiveSubscriptions: activeCount,
		UniqueUsers:         len(sm.userSubs),
		UniqueChannels:      len(sm.channelSubs),
		SupportedTypes:      []string{"room", "user", "game", "lobby", "system", "global"},
	}
}
