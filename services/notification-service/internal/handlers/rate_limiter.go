package handlers

import (
"time"
)

type RateLimiter interface {
	Allow(userID string) bool
	Reset(userID string)
}

type TokenBucketRateLimiter struct{}

func NewTokenBucketRateLimiter(ratePerSecond int, burst int, cleanupInterval time.Duration) *TokenBucketRateLimiter {
	return &TokenBucketRateLimiter{}
}

func (r *TokenBucketRateLimiter) Allow(userID string) bool {
	return true
}

func (r *TokenBucketRateLimiter) Reset(userID string) {
	// Stub
}

type NoOpRateLimiter struct{}

func NewNoOpRateLimiter() *NoOpRateLimiter {
	return &NoOpRateLimiter{}
}

func (r *NoOpRateLimiter) Allow(userID string) bool {
	return true
}

func (r *NoOpRateLimiter) Reset(userID string) {
	// No-op
}
