package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

type Config struct {
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`

	WSPort           int           `json:"ws_port"`
	WSPath           string        `json:"ws_path"`
	WSOriginCheck    bool          `json:"ws_origin_check"`
	WSAllowedOrigins []string      `json:"ws_allowed_origins"`
	WSReadTimeout    time.Duration `json:"ws_read_timeout"`
	WSWriteTimeout   time.Duration `json:"ws_write_timeout"`
	WSPingInterval   time.Duration `json:"ws_ping_interval"`
	WSMaxConnections int           `json:"ws_max_connections"`
	WSBufferSize     int           `json:"ws_buffer_size"`

	RedisURL      string        `json:"redis_url"`
	RedisPassword string        `json:"redis_password"`
	RedisDB       int           `json:"redis_db"`
	RedisTTL      time.Duration `json:"redis_ttl"`

	NotificationTTL time.Duration `json:"notification_ttl"`
	EventChannels   []string      `json:"event_channels"`

	RateLimitEnabled         bool          `json:"rate_limit_enabled"`
	RateLimitPerSecond       int           `json:"rate_limit_per_second"`
	RateLimitBurst           int           `json:"rate_limit_burst"`
	RateLimitCleanupInterval time.Duration `json:"rate_limit_cleanup_interval"`
}

func Load() (*Config, error) {
	config := &Config{
		Port:        8084,
		Environment: "development",
		LogLevel:    "info",

		WSPort:           8085,
		WSPath:           "/ws",
		WSOriginCheck:    true,
		WSAllowedOrigins: []string{"*"},
		WSReadTimeout:    60 * time.Second,
		WSWriteTimeout:   10 * time.Second,
		WSPingInterval:   30 * time.Second,
		WSMaxConnections: 10000,
		WSBufferSize:     1024,

		RedisURL:      "redis://localhost:6379",
		RedisPassword: "",
		RedisDB:       0,
		RedisTTL:      24 * time.Hour,

		NotificationTTL: 7 * 24 * time.Hour,
		EventChannels: []string{
			"room_events:*",
			"game_events:*",
		},

		RateLimitEnabled:         true,
		RateLimitPerSecond:       100,
		RateLimitBurst:           200,
		RateLimitCleanupInterval: 1 * time.Minute,
	}

	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		config.RedisURL = redisURL
	}

	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		config.RedisPassword = redisPassword
	}

	if redisDB := os.Getenv("REDIS_DB"); redisDB != "" {
		if db, err := strconv.Atoi(redisDB); err == nil {
			config.RedisDB = db
		}
	}

	return config, nil
}

func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}
	return nil
}

func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}
