package models

import (
	"time"
)

type NotificationType string
type NotificationPriority string
type DeliveryMethod string
type NotificationStatus string

const (
	NotificationTypePlayerJoined NotificationType = "player_joined"
	NotificationTypePlayerLeft   NotificationType = "player_left"
	NotificationTypeGameStarted  NotificationType = "game_started"
	NotificationTypeGameFinished NotificationType = "game_finished"

	PriorityNormal NotificationPriority = "normal"
	PriorityHigh   NotificationPriority = "high"

	DeliveryWebSocket DeliveryMethod = "websocket"

	StatusPending NotificationStatus = "pending"
	StatusSent    NotificationStatus = "sent"
)

type WebSocketConnection struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"user_id"`
	Username    string                 `json:"username"`
	RoomID      string                 `json:"room_id,omitempty"`
	ConnectedAt time.Time              `json:"connected_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

type WebSocketMessage struct {
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

type SendNotificationRequest struct {
	Type       NotificationType       `json:"type"`
	Priority   NotificationPriority   `json:"priority"`
	Title      string                 `json:"title"`
	Message    string                 `json:"message"`
	Data       map[string]interface{} `json:"data,omitempty"`
	Recipients []string               `json:"recipients"`
	Channels   []DeliveryMethod       `json:"channels"`
}

func (r *SendNotificationRequest) Validate() error {
	return nil
}

type Notification struct {
	ID       string                 `json:"id"`
	Type     NotificationType       `json:"type"`
	Priority NotificationPriority   `json:"priority"`
	Title    string                 `json:"title"`
	Message  string                 `json:"message"`
	Data     map[string]interface{} `json:"data"`
	Status   NotificationStatus     `json:"status"`
}

type EventSubscription struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	EventType string    `json:"event_type"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type SubscribeRequest struct {
	UserID    string `json:"user_id"`
	EventType string `json:"event_type"`
}

type UnsubscribeRequest struct {
	UserID         string `json:"user_id"`
	SubscriptionID string `json:"subscription_id,omitempty"`
	EventType      string `json:"event_type,omitempty"`
}

type GetNotificationsRequest struct {
	UserID string `json:"user_id"`
	Limit  int    `json:"limit"`
}

type MarkNotificationRequest struct {
	UserID          string             `json:"user_id"`
	NotificationIDs []string           `json:"notification_ids"`
	Status          NotificationStatus `json:"status"`
}

type BroadcastNotificationRequest struct {
	Type     NotificationType       `json:"type"`
	Priority NotificationPriority   `json:"priority"`
	Title    string                 `json:"title"`
	Message  string                 `json:"message"`
	Data     map[string]interface{} `json:"data,omitempty"`
	Channels []DeliveryMethod       `json:"channels"`
}

type BroadcastFilter struct {
	RoomID     string   `json:"room_id,omitempty"`
	UserIDs    []string `json:"user_ids,omitempty"`
	ExcludeIDs []string `json:"exclude_ids,omitempty"`
}

// Additional models for modular notification service

// Event broadcasting models

// RoomEventRequest represents a request to broadcast a room event
type RoomEventRequest struct {
	RoomID            string                 `json:"roomId" validate:"required"`
	EventType         string                 `json:"eventType" validate:"required"`
	EventData         map[string]interface{} `json:"eventData"`
	PlayerIDs         []string               `json:"playerIds"`
	BroadcastToSocket bool                   `json:"broadcastToSocket"`
}

// GameEventRequest represents a request to broadcast a game event
type GameEventRequest struct {
	SessionID string                 `json:"sessionId" validate:"required"`
	RoomID    string                 `json:"roomId,omitempty"`
	EventType string                 `json:"eventType" validate:"required"`
	GameType  string                 `json:"gameType" validate:"required"`
	EventData map[string]interface{} `json:"eventData"`
}

// LobbyEventRequest represents a request to broadcast a lobby event
type LobbyEventRequest struct {
	EventType string                 `json:"eventType" validate:"required"`
	Action    string                 `json:"action,omitempty"`
	EventData map[string]interface{} `json:"eventData"`
}

// UserNotificationRequest represents a request to send user notifications
type UserNotificationRequest struct {
	UserIDs           []string               `json:"userIds" validate:"required"`
	NotificationType  string                 `json:"notificationType" validate:"required"`
	Data              map[string]interface{} `json:"data"`
	Priority          int                    `json:"priority"`
	BroadcastGlobally bool                   `json:"broadcastGlobally"`
}

// SystemEventRequest represents a request to broadcast system events
type SystemEventRequest struct {
	EventType string                 `json:"eventType" validate:"required"`
	Severity  string                 `json:"severity" validate:"required"`
	EventData map[string]interface{} `json:"eventData"`
}

// Enhanced subscription models

// EnhancedSubscribeRequest represents an enhanced subscription request
type EnhancedSubscribeRequest struct {
	UserID      string                 `json:"userId" validate:"required"`
	ChannelType string                 `json:"channelType" validate:"required"`
	ChannelID   string                 `json:"channelId" validate:"required"`
	EventTypes  []string               `json:"eventTypes"`
	Filters     map[string]interface{} `json:"filters"`
}

// UpdateSubscriptionRequest represents a request to update a subscription
type UpdateSubscriptionRequest struct {
	UserID         string                 `json:"userId" validate:"required"`
	SubscriptionID string                 `json:"subscriptionId" validate:"required"`
	EventTypes     []string               `json:"eventTypes,omitempty"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
	IsActive       *bool                  `json:"isActive,omitempty"`
}

// EnhancedEventSubscription represents an enhanced event subscription
type EnhancedEventSubscription struct {
	ID             string                 `json:"id"`
	UserID         string                 `json:"userId"`
	ChannelType    string                 `json:"channelType"`
	ChannelID      string                 `json:"channelId"`
	ChannelPattern string                 `json:"channelPattern"`
	EventTypes     []string               `json:"eventTypes"`
	Filters        map[string]interface{} `json:"filters"`
	IsActive       bool                   `json:"isActive"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
}

// Real-time notification models

// RegisterConnectionRequest represents a request to register a user connection
type RegisterConnectionRequest struct {
	UserID         string                 `json:"userId" validate:"required"`
	ConnectionID   string                 `json:"connectionId" validate:"required"`
	ConnectionType string                 `json:"connectionType" validate:"required"`
	SocketChannel  string                 `json:"socketChannel,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// UserConnection represents a user's connection information
type UserConnection struct {
	UserID         string                 `json:"userId"`
	ConnectionID   string                 `json:"connectionId"`
	ConnectionType string                 `json:"connectionType"`
	SocketChannel  string                 `json:"socketChannel"`
	IsActive       bool                   `json:"isActive"`
	ConnectedAt    time.Time              `json:"connectedAt"`
	LastActivity   time.Time              `json:"lastActivity"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// IncomingEvent represents an incoming event for processing
type IncomingEvent struct {
	Channel     string                 `json:"channel"`
	ChannelType string                 `json:"channelType"`
	ChannelID   string                 `json:"channelId"`
	EventType   string                 `json:"eventType"`
	RawPayload  string                 `json:"rawPayload"`
	EventData   map[string]interface{} `json:"eventData"`
	Payload     map[string]interface{} `json:"payload"`
	Metadata    map[string]interface{} `json:"metadata"`
	ReceivedAt  time.Time              `json:"receivedAt"`
}

// NotificationDelivery represents a notification delivery task
type NotificationDelivery struct {
	UserID      string          `json:"userId"`
	Connection  *UserConnection `json:"connection"`
	Event       *IncomingEvent  `json:"event"`
	CreatedAt   time.Time       `json:"createdAt"`
	Attempts    int             `json:"attempts"`
	MaxAttempts int             `json:"maxAttempts"`
}

// Stats models for notification service components

// BroadcastStats represents broadcasting statistics
type BroadcastStats struct {
	SupportedChannels []string `json:"supportedChannels"`
	EventFormats      []string `json:"eventFormats"`
	Version           string   `json:"version"`
}

// SubscriptionStats represents subscription statistics
type SubscriptionStats struct {
	TotalSubscriptions  int      `json:"totalSubscriptions"`
	ActiveSubscriptions int      `json:"activeSubscriptions"`
	UniqueUsers         int      `json:"uniqueUsers"`
	UniqueChannels      int      `json:"uniqueChannels"`
	SupportedTypes      []string `json:"supportedTypes"`
}

// RouterStats represents router statistics
type RouterStats struct {
	IsRunning         bool     `json:"isRunning"`
	SupportedChannels []string `json:"supportedChannels"`
	ProcessorCount    int      `json:"processorCount"`
	Version           string   `json:"version"`
}

// NotifierStats represents notifier statistics
type NotifierStats struct {
	IsRunning         bool     `json:"isRunning"`
	ActiveConnections int      `json:"activeConnections"`
	TotalConnections  int      `json:"totalConnections"`
	DeliveryWorkers   int      `json:"deliveryWorkers"`
	QueueSize         int      `json:"queueSize"`
	SupportedTypes    []string `json:"supportedTypes"`
}

// NotificationServiceStats represents comprehensive notification service statistics
type NotificationServiceStats struct {
	BroadcastStats    *BroadcastStats    `json:"broadcastStats"`
	SubscriptionStats *SubscriptionStats `json:"subscriptionStats"`
	RouterStats       *RouterStats       `json:"routerStats"`
	NotifierStats     *NotifierStats     `json:"notifierStats"`
	Version           string             `json:"version"`
}

// Error models

// NotificationError represents a notification service error
type NotificationError struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// Error implements the error interface
func (e *NotificationError) Error() string {
	return e.Message
}

// NewNotificationError creates a new notification error
func NewNotificationError(message string, details map[string]interface{}) *NotificationError {
	return &NotificationError{
		Code:    "NOTIFICATION_ERROR",
		Message: message,
		Details: details,
	}
}
