// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/notification.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type NotificationType int32

const (
	NotificationType_NOTIFICATION_TYPE_UNSPECIFIED    NotificationType = 0
	NotificationType_NOTIFICATION_TYPE_ROOM_UPDATE    NotificationType = 1
	NotificationType_NOTIFICATION_TYPE_PLAYER_JOINED  NotificationType = 2
	NotificationType_NOTIFICATION_TYPE_PLAYER_LEFT    NotificationType = 3
	NotificationType_NOTIFICATION_TYPE_PLAYER_READY   NotificationType = 4
	NotificationType_NOTIFICATION_TYPE_GAME_STARTING  NotificationType = 5
	NotificationType_NOTIFICATION_TYPE_GAME_STARTED   NotificationType = 6
	NotificationType_NOTIFICATION_TYPE_GAME_UPDATE    NotificationType = 7
	NotificationType_NOTIFICATION_TYPE_GAME_FINISHED  NotificationType = 8
	NotificationType_NOTIFICATION_TYPE_GAME_CANCELLED NotificationType = 9
	NotificationType_NOTIFICATION_TYPE_USER_MESSAGE   NotificationType = 10
	NotificationType_NOTIFICATION_TYPE_SYSTEM_MESSAGE NotificationType = 11
	NotificationType_NOTIFICATION_TYPE_ERROR          NotificationType = 12
	NotificationType_NOTIFICATION_TYPE_WARNING        NotificationType = 13
	NotificationType_NOTIFICATION_TYPE_INFO           NotificationType = 14
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0:  "NOTIFICATION_TYPE_UNSPECIFIED",
		1:  "NOTIFICATION_TYPE_ROOM_UPDATE",
		2:  "NOTIFICATION_TYPE_PLAYER_JOINED",
		3:  "NOTIFICATION_TYPE_PLAYER_LEFT",
		4:  "NOTIFICATION_TYPE_PLAYER_READY",
		5:  "NOTIFICATION_TYPE_GAME_STARTING",
		6:  "NOTIFICATION_TYPE_GAME_STARTED",
		7:  "NOTIFICATION_TYPE_GAME_UPDATE",
		8:  "NOTIFICATION_TYPE_GAME_FINISHED",
		9:  "NOTIFICATION_TYPE_GAME_CANCELLED",
		10: "NOTIFICATION_TYPE_USER_MESSAGE",
		11: "NOTIFICATION_TYPE_SYSTEM_MESSAGE",
		12: "NOTIFICATION_TYPE_ERROR",
		13: "NOTIFICATION_TYPE_WARNING",
		14: "NOTIFICATION_TYPE_INFO",
	}
	NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":    0,
		"NOTIFICATION_TYPE_ROOM_UPDATE":    1,
		"NOTIFICATION_TYPE_PLAYER_JOINED":  2,
		"NOTIFICATION_TYPE_PLAYER_LEFT":    3,
		"NOTIFICATION_TYPE_PLAYER_READY":   4,
		"NOTIFICATION_TYPE_GAME_STARTING":  5,
		"NOTIFICATION_TYPE_GAME_STARTED":   6,
		"NOTIFICATION_TYPE_GAME_UPDATE":    7,
		"NOTIFICATION_TYPE_GAME_FINISHED":  8,
		"NOTIFICATION_TYPE_GAME_CANCELLED": 9,
		"NOTIFICATION_TYPE_USER_MESSAGE":   10,
		"NOTIFICATION_TYPE_SYSTEM_MESSAGE": 11,
		"NOTIFICATION_TYPE_ERROR":          12,
		"NOTIFICATION_TYPE_WARNING":        13,
		"NOTIFICATION_TYPE_INFO":           14,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[0].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[0]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{0}
}

type NotificationPriority int32

const (
	NotificationPriority_NOTIFICATION_PRIORITY_UNSPECIFIED NotificationPriority = 0
	NotificationPriority_NOTIFICATION_PRIORITY_LOW         NotificationPriority = 1
	NotificationPriority_NOTIFICATION_PRIORITY_NORMAL      NotificationPriority = 2
	NotificationPriority_NOTIFICATION_PRIORITY_HIGH        NotificationPriority = 3
	NotificationPriority_NOTIFICATION_PRIORITY_CRITICAL    NotificationPriority = 4
)

// Enum value maps for NotificationPriority.
var (
	NotificationPriority_name = map[int32]string{
		0: "NOTIFICATION_PRIORITY_UNSPECIFIED",
		1: "NOTIFICATION_PRIORITY_LOW",
		2: "NOTIFICATION_PRIORITY_NORMAL",
		3: "NOTIFICATION_PRIORITY_HIGH",
		4: "NOTIFICATION_PRIORITY_CRITICAL",
	}
	NotificationPriority_value = map[string]int32{
		"NOTIFICATION_PRIORITY_UNSPECIFIED": 0,
		"NOTIFICATION_PRIORITY_LOW":         1,
		"NOTIFICATION_PRIORITY_NORMAL":      2,
		"NOTIFICATION_PRIORITY_HIGH":        3,
		"NOTIFICATION_PRIORITY_CRITICAL":    4,
	}
)

func (x NotificationPriority) Enum() *NotificationPriority {
	p := new(NotificationPriority)
	*p = x
	return p
}

func (x NotificationPriority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationPriority) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[1].Descriptor()
}

func (NotificationPriority) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[1]
}

func (x NotificationPriority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationPriority.Descriptor instead.
func (NotificationPriority) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{1}
}

type DeliveryMethod int32

const (
	DeliveryMethod_DELIVERY_METHOD_UNSPECIFIED DeliveryMethod = 0
	DeliveryMethod_DELIVERY_METHOD_WEBSOCKET   DeliveryMethod = 1
	DeliveryMethod_DELIVERY_METHOD_PUSH        DeliveryMethod = 2
	DeliveryMethod_DELIVERY_METHOD_EMAIL       DeliveryMethod = 3
	DeliveryMethod_DELIVERY_METHOD_SMS         DeliveryMethod = 4
	DeliveryMethod_DELIVERY_METHOD_IN_APP      DeliveryMethod = 5
)

// Enum value maps for DeliveryMethod.
var (
	DeliveryMethod_name = map[int32]string{
		0: "DELIVERY_METHOD_UNSPECIFIED",
		1: "DELIVERY_METHOD_WEBSOCKET",
		2: "DELIVERY_METHOD_PUSH",
		3: "DELIVERY_METHOD_EMAIL",
		4: "DELIVERY_METHOD_SMS",
		5: "DELIVERY_METHOD_IN_APP",
	}
	DeliveryMethod_value = map[string]int32{
		"DELIVERY_METHOD_UNSPECIFIED": 0,
		"DELIVERY_METHOD_WEBSOCKET":   1,
		"DELIVERY_METHOD_PUSH":        2,
		"DELIVERY_METHOD_EMAIL":       3,
		"DELIVERY_METHOD_SMS":         4,
		"DELIVERY_METHOD_IN_APP":      5,
	}
)

func (x DeliveryMethod) Enum() *DeliveryMethod {
	p := new(DeliveryMethod)
	*p = x
	return p
}

func (x DeliveryMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[2].Descriptor()
}

func (DeliveryMethod) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[2]
}

func (x DeliveryMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryMethod.Descriptor instead.
func (DeliveryMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{2}
}

type NotificationStatus int32

const (
	NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED NotificationStatus = 0
	NotificationStatus_NOTIFICATION_STATUS_PENDING     NotificationStatus = 1
	NotificationStatus_NOTIFICATION_STATUS_SENT        NotificationStatus = 2
	NotificationStatus_NOTIFICATION_STATUS_DELIVERED   NotificationStatus = 3
	NotificationStatus_NOTIFICATION_STATUS_READ        NotificationStatus = 4
	NotificationStatus_NOTIFICATION_STATUS_FAILED      NotificationStatus = 5
	NotificationStatus_NOTIFICATION_STATUS_EXPIRED     NotificationStatus = 6
)

// Enum value maps for NotificationStatus.
var (
	NotificationStatus_name = map[int32]string{
		0: "NOTIFICATION_STATUS_UNSPECIFIED",
		1: "NOTIFICATION_STATUS_PENDING",
		2: "NOTIFICATION_STATUS_SENT",
		3: "NOTIFICATION_STATUS_DELIVERED",
		4: "NOTIFICATION_STATUS_READ",
		5: "NOTIFICATION_STATUS_FAILED",
		6: "NOTIFICATION_STATUS_EXPIRED",
	}
	NotificationStatus_value = map[string]int32{
		"NOTIFICATION_STATUS_UNSPECIFIED": 0,
		"NOTIFICATION_STATUS_PENDING":     1,
		"NOTIFICATION_STATUS_SENT":        2,
		"NOTIFICATION_STATUS_DELIVERED":   3,
		"NOTIFICATION_STATUS_READ":        4,
		"NOTIFICATION_STATUS_FAILED":      5,
		"NOTIFICATION_STATUS_EXPIRED":     6,
	}
)

func (x NotificationStatus) Enum() *NotificationStatus {
	p := new(NotificationStatus)
	*p = x
	return p
}

func (x NotificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[3].Descriptor()
}

func (NotificationStatus) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[3]
}

func (x NotificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationStatus.Descriptor instead.
func (NotificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{3}
}

// Core data structures
type Notification struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Id            string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          NotificationType         `protobuf:"varint,2,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Priority      NotificationPriority     `protobuf:"varint,3,opt,name=priority,proto3,enum=notification.NotificationPriority" json:"priority,omitempty"`
	Title         string                   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                   `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Data          map[string]string        `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Recipients    []*NotificationRecipient `protobuf:"bytes,7,rep,name=recipients,proto3" json:"recipients,omitempty"`
	Channels      []DeliveryMethod         `protobuf:"varint,8,rep,packed,name=channels,proto3,enum=notification.DeliveryMethod" json:"channels,omitempty"`
	Status        NotificationStatus       `protobuf:"varint,9,opt,name=status,proto3,enum=notification.NotificationStatus" json:"status,omitempty"`
	ScheduledAt   *timestamppb.Timestamp   `protobuf:"bytes,10,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	SentAt        *timestamppb.Timestamp   `protobuf:"bytes,11,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	ExpiresAt     *timestamppb.Timestamp   `protobuf:"bytes,12,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp   `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp   `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_proto_notification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{0}
}

func (x *Notification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Notification) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *Notification) GetPriority() NotificationPriority {
	if x != nil {
		return x.Priority
	}
	return NotificationPriority_NOTIFICATION_PRIORITY_UNSPECIFIED
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Notification) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Notification) GetRecipients() []*NotificationRecipient {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *Notification) GetChannels() []DeliveryMethod {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *Notification) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

func (x *Notification) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Notification) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *Notification) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Notification) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type NotificationRecipient struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Status        NotificationStatus     `protobuf:"varint,3,opt,name=status,proto3,enum=notification.NotificationStatus" json:"status,omitempty"`
	DeliveredAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=delivered_at,json=deliveredAt,proto3" json:"delivered_at,omitempty"`
	ReadAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	FailureReason string                 `protobuf:"bytes,6,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
	RetryCount    int32                  `protobuf:"varint,7,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	LastRetryAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_retry_at,json=lastRetryAt,proto3" json:"last_retry_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationRecipient) Reset() {
	*x = NotificationRecipient{}
	mi := &file_proto_notification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationRecipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationRecipient) ProtoMessage() {}

func (x *NotificationRecipient) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationRecipient.ProtoReflect.Descriptor instead.
func (*NotificationRecipient) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{1}
}

func (x *NotificationRecipient) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *NotificationRecipient) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *NotificationRecipient) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

func (x *NotificationRecipient) GetDeliveredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeliveredAt
	}
	return nil
}

func (x *NotificationRecipient) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

func (x *NotificationRecipient) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

func (x *NotificationRecipient) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *NotificationRecipient) GetLastRetryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRetryAt
	}
	return nil
}

type EventSubscription struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EventType     string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	Filter        *EventFilter           `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventSubscription) Reset() {
	*x = EventSubscription{}
	mi := &file_proto_notification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventSubscription) ProtoMessage() {}

func (x *EventSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventSubscription.ProtoReflect.Descriptor instead.
func (*EventSubscription) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{2}
}

func (x *EventSubscription) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EventSubscription) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *EventSubscription) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *EventSubscription) GetFilter() *EventFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *EventSubscription) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EventSubscription) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type EventFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	GameType      string                 `protobuf:"bytes,2,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	UserIds       []string               `protobuf:"bytes,3,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	EventTypes    []string               `protobuf:"bytes,4,rep,name=event_types,json=eventTypes,proto3" json:"event_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventFilter) Reset() {
	*x = EventFilter{}
	mi := &file_proto_notification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventFilter) ProtoMessage() {}

func (x *EventFilter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventFilter.ProtoReflect.Descriptor instead.
func (*EventFilter) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{3}
}

func (x *EventFilter) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *EventFilter) GetGameType() string {
	if x != nil {
		return x.GameType
	}
	return ""
}

func (x *EventFilter) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *EventFilter) GetEventTypes() []string {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

type WebSocketConnection struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	RoomId        string                 `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ConnectedAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=connected_at,json=connectedAt,proto3" json:"connected_at,omitempty"`
	LastPingAt    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_ping_at,json=lastPingAt,proto3" json:"last_ping_at,omitempty"`
	Subscriptions []string               `protobuf:"bytes,7,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebSocketConnection) Reset() {
	*x = WebSocketConnection{}
	mi := &file_proto_notification_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebSocketConnection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSocketConnection) ProtoMessage() {}

func (x *WebSocketConnection) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSocketConnection.ProtoReflect.Descriptor instead.
func (*WebSocketConnection) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{4}
}

func (x *WebSocketConnection) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WebSocketConnection) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WebSocketConnection) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *WebSocketConnection) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *WebSocketConnection) GetConnectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ConnectedAt
	}
	return nil
}

func (x *WebSocketConnection) GetLastPingAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPingAt
	}
	return nil
}

func (x *WebSocketConnection) GetSubscriptions() []string {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *WebSocketConnection) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type BroadcastFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	GameType      string                 `protobuf:"bytes,2,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	UserIds       []string               `protobuf:"bytes,3,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	ExcludeIds    []string               `protobuf:"bytes,4,rep,name=exclude_ids,json=excludeIds,proto3" json:"exclude_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastFilter) Reset() {
	*x = BroadcastFilter{}
	mi := &file_proto_notification_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastFilter) ProtoMessage() {}

func (x *BroadcastFilter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastFilter.ProtoReflect.Descriptor instead.
func (*BroadcastFilter) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{5}
}

func (x *BroadcastFilter) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *BroadcastFilter) GetGameType() string {
	if x != nil {
		return x.GameType
	}
	return ""
}

func (x *BroadcastFilter) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *BroadcastFilter) GetExcludeIds() []string {
	if x != nil {
		return x.ExcludeIds
	}
	return nil
}

// SendNotification
type SendNotificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          NotificationType       `protobuf:"varint,1,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Priority      NotificationPriority   `protobuf:"varint,2,opt,name=priority,proto3,enum=notification.NotificationPriority" json:"priority,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	Data          map[string]string      `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Recipients    []string               `protobuf:"bytes,6,rep,name=recipients,proto3" json:"recipients,omitempty"`
	Channels      []DeliveryMethod       `protobuf:"varint,7,rep,packed,name=channels,proto3,enum=notification.DeliveryMethod" json:"channels,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationRequest) Reset() {
	*x = SendNotificationRequest{}
	mi := &file_proto_notification_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationRequest) ProtoMessage() {}

func (x *SendNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendNotificationRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{6}
}

func (x *SendNotificationRequest) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *SendNotificationRequest) GetPriority() NotificationPriority {
	if x != nil {
		return x.Priority
	}
	return NotificationPriority_NOTIFICATION_PRIORITY_UNSPECIFIED
}

func (x *SendNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendNotificationRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendNotificationRequest) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SendNotificationRequest) GetRecipients() []string {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *SendNotificationRequest) GetChannels() []DeliveryMethod {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *SendNotificationRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *SendNotificationRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type SendNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notification  *Notification          `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationResponse) Reset() {
	*x = SendNotificationResponse{}
	mi := &file_proto_notification_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationResponse) ProtoMessage() {}

func (x *SendNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendNotificationResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{7}
}

func (x *SendNotificationResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *SendNotificationResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// BroadcastNotification
type BroadcastNotificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          NotificationType       `protobuf:"varint,1,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Priority      NotificationPriority   `protobuf:"varint,2,opt,name=priority,proto3,enum=notification.NotificationPriority" json:"priority,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	Data          map[string]string      `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Filter        *BroadcastFilter       `protobuf:"bytes,6,opt,name=filter,proto3" json:"filter,omitempty"`
	Channels      []DeliveryMethod       `protobuf:"varint,7,rep,packed,name=channels,proto3,enum=notification.DeliveryMethod" json:"channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastNotificationRequest) Reset() {
	*x = BroadcastNotificationRequest{}
	mi := &file_proto_notification_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastNotificationRequest) ProtoMessage() {}

func (x *BroadcastNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastNotificationRequest.ProtoReflect.Descriptor instead.
func (*BroadcastNotificationRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{8}
}

func (x *BroadcastNotificationRequest) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *BroadcastNotificationRequest) GetPriority() NotificationPriority {
	if x != nil {
		return x.Priority
	}
	return NotificationPriority_NOTIFICATION_PRIORITY_UNSPECIFIED
}

func (x *BroadcastNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BroadcastNotificationRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BroadcastNotificationRequest) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BroadcastNotificationRequest) GetFilter() *BroadcastFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *BroadcastNotificationRequest) GetChannels() []DeliveryMethod {
	if x != nil {
		return x.Channels
	}
	return nil
}

type BroadcastNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notification  *Notification          `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastNotificationResponse) Reset() {
	*x = BroadcastNotificationResponse{}
	mi := &file_proto_notification_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastNotificationResponse) ProtoMessage() {}

func (x *BroadcastNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastNotificationResponse.ProtoReflect.Descriptor instead.
func (*BroadcastNotificationResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{9}
}

func (x *BroadcastNotificationResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *BroadcastNotificationResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetNotifications
type GetNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status        NotificationStatus     `protobuf:"varint,2,opt,name=status,proto3,enum=notification.NotificationStatus" json:"status,omitempty"`
	Type          NotificationType       `protobuf:"varint,3,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Limit         int32                  `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        int32                  `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	SinceId       string                 `protobuf:"bytes,6,opt,name=since_id,json=sinceId,proto3" json:"since_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationsRequest) Reset() {
	*x = GetNotificationsRequest{}
	mi := &file_proto_notification_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsRequest) ProtoMessage() {}

func (x *GetNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{10}
}

func (x *GetNotificationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetNotificationsRequest) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

func (x *GetNotificationsRequest) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *GetNotificationsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetNotificationsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetNotificationsRequest) GetSinceId() string {
	if x != nil {
		return x.SinceId
	}
	return ""
}

type GetNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notifications []*Notification        `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationsResponse) Reset() {
	*x = GetNotificationsResponse{}
	mi := &file_proto_notification_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsResponse) ProtoMessage() {}

func (x *GetNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{11}
}

func (x *GetNotificationsResponse) GetNotifications() []*Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *GetNotificationsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetNotificationsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// MarkNotifications
type MarkNotificationsRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NotificationIds []string               `protobuf:"bytes,2,rep,name=notification_ids,json=notificationIds,proto3" json:"notification_ids,omitempty"`
	Status          NotificationStatus     `protobuf:"varint,3,opt,name=status,proto3,enum=notification.NotificationStatus" json:"status,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MarkNotificationsRequest) Reset() {
	*x = MarkNotificationsRequest{}
	mi := &file_proto_notification_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationsRequest) ProtoMessage() {}

func (x *MarkNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationsRequest.ProtoReflect.Descriptor instead.
func (*MarkNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{12}
}

func (x *MarkNotificationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MarkNotificationsRequest) GetNotificationIds() []string {
	if x != nil {
		return x.NotificationIds
	}
	return nil
}

func (x *MarkNotificationsRequest) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

type MarkNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         string                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationsResponse) Reset() {
	*x = MarkNotificationsResponse{}
	mi := &file_proto_notification_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationsResponse) ProtoMessage() {}

func (x *MarkNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationsResponse.ProtoReflect.Descriptor instead.
func (*MarkNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{13}
}

func (x *MarkNotificationsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// Subscribe
type SubscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EventType     string                 `protobuf:"bytes,2,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	Filter        *EventFilter           `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeRequest) Reset() {
	*x = SubscribeRequest{}
	mi := &file_proto_notification_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequest) ProtoMessage() {}

func (x *SubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequest.ProtoReflect.Descriptor instead.
func (*SubscribeRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{14}
}

func (x *SubscribeRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SubscribeRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *SubscribeRequest) GetFilter() *EventFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type SubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Subscription  *EventSubscription     `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeResponse) Reset() {
	*x = SubscribeResponse{}
	mi := &file_proto_notification_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeResponse) ProtoMessage() {}

func (x *SubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeResponse.ProtoReflect.Descriptor instead.
func (*SubscribeResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{15}
}

func (x *SubscribeResponse) GetSubscription() *EventSubscription {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *SubscribeResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// Unsubscribe
type UnsubscribeRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SubscriptionId string                 `protobuf:"bytes,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	EventType      string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UnsubscribeRequest) Reset() {
	*x = UnsubscribeRequest{}
	mi := &file_proto_notification_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsubscribeRequest) ProtoMessage() {}

func (x *UnsubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsubscribeRequest.ProtoReflect.Descriptor instead.
func (*UnsubscribeRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{16}
}

func (x *UnsubscribeRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnsubscribeRequest) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *UnsubscribeRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

type UnsubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         string                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsubscribeResponse) Reset() {
	*x = UnsubscribeResponse{}
	mi := &file_proto_notification_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsubscribeResponse) ProtoMessage() {}

func (x *UnsubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsubscribeResponse.ProtoReflect.Descriptor instead.
func (*UnsubscribeResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{17}
}

func (x *UnsubscribeResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetSubscriptions
type GetSubscriptionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubscriptionsRequest) Reset() {
	*x = GetSubscriptionsRequest{}
	mi := &file_proto_notification_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionsRequest) ProtoMessage() {}

func (x *GetSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*GetSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{18}
}

func (x *GetSubscriptionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetSubscriptionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Subscriptions []*EventSubscription   `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubscriptionsResponse) Reset() {
	*x = GetSubscriptionsResponse{}
	mi := &file_proto_notification_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionsResponse) ProtoMessage() {}

func (x *GetSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*GetSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{19}
}

func (x *GetSubscriptionsResponse) GetSubscriptions() []*EventSubscription {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *GetSubscriptionsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetActiveConnections
type GetActiveConnectionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveConnectionsRequest) Reset() {
	*x = GetActiveConnectionsRequest{}
	mi := &file_proto_notification_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveConnectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveConnectionsRequest) ProtoMessage() {}

func (x *GetActiveConnectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveConnectionsRequest.ProtoReflect.Descriptor instead.
func (*GetActiveConnectionsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{20}
}

func (x *GetActiveConnectionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetActiveConnectionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Connections   []*WebSocketConnection `protobuf:"bytes,1,rep,name=connections,proto3" json:"connections,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveConnectionsResponse) Reset() {
	*x = GetActiveConnectionsResponse{}
	mi := &file_proto_notification_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveConnectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveConnectionsResponse) ProtoMessage() {}

func (x *GetActiveConnectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveConnectionsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveConnectionsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{21}
}

func (x *GetActiveConnectionsResponse) GetConnections() []*WebSocketConnection {
	if x != nil {
		return x.Connections
	}
	return nil
}

func (x *GetActiveConnectionsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// ProcessEvent
type ProcessEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventType     string                 `protobuf:"bytes,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	EventData     map[string]string      `protobuf:"bytes,2,rep,name=event_data,json=eventData,proto3" json:"event_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessEventRequest) Reset() {
	*x = ProcessEventRequest{}
	mi := &file_proto_notification_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEventRequest) ProtoMessage() {}

func (x *ProcessEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessEventRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{22}
}

func (x *ProcessEventRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *ProcessEventRequest) GetEventData() map[string]string {
	if x != nil {
		return x.EventData
	}
	return nil
}

type ProcessEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         string                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessEventResponse) Reset() {
	*x = ProcessEventResponse{}
	mi := &file_proto_notification_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEventResponse) ProtoMessage() {}

func (x *ProcessEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessEventResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{23}
}

func (x *ProcessEventResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetNotificationStats
type GetNotificationStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationStatsRequest) Reset() {
	*x = GetNotificationStatsRequest{}
	mi := &file_proto_notification_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationStatsRequest) ProtoMessage() {}

func (x *GetNotificationStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationStatsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{24}
}

type GetNotificationStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    map[string]string      `protobuf:"bytes,1,rep,name=statistics,proto3" json:"statistics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationStatsResponse) Reset() {
	*x = GetNotificationStatsResponse{}
	mi := &file_proto_notification_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationStatsResponse) ProtoMessage() {}

func (x *GetNotificationStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationStatsResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{25}
}

func (x *GetNotificationStatsResponse) GetStatistics() map[string]string {
	if x != nil {
		return x.Statistics
	}
	return nil
}

func (x *GetNotificationStatsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetConnectionStats
type GetConnectionStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConnectionStatsRequest) Reset() {
	*x = GetConnectionStatsRequest{}
	mi := &file_proto_notification_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConnectionStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionStatsRequest) ProtoMessage() {}

func (x *GetConnectionStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionStatsRequest.ProtoReflect.Descriptor instead.
func (*GetConnectionStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{26}
}

type GetConnectionStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    map[string]string      `protobuf:"bytes,1,rep,name=statistics,proto3" json:"statistics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConnectionStatsResponse) Reset() {
	*x = GetConnectionStatsResponse{}
	mi := &file_proto_notification_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConnectionStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionStatsResponse) ProtoMessage() {}

func (x *GetConnectionStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionStatsResponse.ProtoReflect.Descriptor instead.
func (*GetConnectionStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{27}
}

func (x *GetConnectionStatsResponse) GetStatistics() map[string]string {
	if x != nil {
		return x.Statistics
	}
	return nil
}

func (x *GetConnectionStatsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

var File_proto_notification_proto protoreflect.FileDescriptor

const file_proto_notification_proto_rawDesc = "" +
	"\n" +
	"\x18proto/notification.proto\x12\fnotification\x1a\x1fgoogle/protobuf/timestamp.proto\"\x93\x06\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x122\n" +
	"\x04type\x18\x02 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12>\n" +
	"\bpriority\x18\x03 \x01(\x0e2\".notification.NotificationPriorityR\bpriority\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x128\n" +
	"\x04data\x18\x06 \x03(\v2$.notification.Notification.DataEntryR\x04data\x12C\n" +
	"\n" +
	"recipients\x18\a \x03(\v2#.notification.NotificationRecipientR\n" +
	"recipients\x128\n" +
	"\bchannels\x18\b \x03(\x0e2\x1c.notification.DeliveryMethodR\bchannels\x128\n" +
	"\x06status\x18\t \x01(\x0e2 .notification.NotificationStatusR\x06status\x12=\n" +
	"\fscheduled_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x123\n" +
	"\asent_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\x06sentAt\x129\n" +
	"\n" +
	"expires_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x129\n" +
	"\n" +
	"created_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x82\x03\n" +
	"\x15NotificationRecipient\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x128\n" +
	"\x06status\x18\x03 \x01(\x0e2 .notification.NotificationStatusR\x06status\x12=\n" +
	"\fdelivered_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\vdeliveredAt\x123\n" +
	"\aread_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x06readAt\x12%\n" +
	"\x0efailure_reason\x18\x06 \x01(\tR\rfailureReason\x12\x1f\n" +
	"\vretry_count\x18\a \x01(\x05R\n" +
	"retryCount\x12>\n" +
	"\rlast_retry_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vlastRetryAt\"\x84\x02\n" +
	"\x11EventSubscription\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x03 \x01(\tR\teventType\x121\n" +
	"\x06filter\x18\x04 \x01(\v2\x19.notification.EventFilterR\x06filter\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x7f\n" +
	"\vEventFilter\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1b\n" +
	"\tgame_type\x18\x02 \x01(\tR\bgameType\x12\x19\n" +
	"\buser_ids\x18\x03 \x03(\tR\auserIds\x12\x1f\n" +
	"\vevent_types\x18\x04 \x03(\tR\n" +
	"eventTypes\"\xa0\x03\n" +
	"\x13WebSocketConnection\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x17\n" +
	"\aroom_id\x18\x04 \x01(\tR\x06roomId\x12=\n" +
	"\fconnected_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vconnectedAt\x12<\n" +
	"\flast_ping_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastPingAt\x12$\n" +
	"\rsubscriptions\x18\a \x03(\tR\rsubscriptions\x12K\n" +
	"\bmetadata\x18\b \x03(\v2/.notification.WebSocketConnection.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x83\x01\n" +
	"\x0fBroadcastFilter\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1b\n" +
	"\tgame_type\x18\x02 \x01(\tR\bgameType\x12\x19\n" +
	"\buser_ids\x18\x03 \x03(\tR\auserIds\x12\x1f\n" +
	"\vexclude_ids\x18\x04 \x03(\tR\n" +
	"excludeIds\"\x8f\x04\n" +
	"\x17SendNotificationRequest\x122\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12>\n" +
	"\bpriority\x18\x02 \x01(\x0e2\".notification.NotificationPriorityR\bpriority\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12C\n" +
	"\x04data\x18\x05 \x03(\v2/.notification.SendNotificationRequest.DataEntryR\x04data\x12\x1e\n" +
	"\n" +
	"recipients\x18\x06 \x03(\tR\n" +
	"recipients\x128\n" +
	"\bchannels\x18\a \x03(\x0e2\x1c.notification.DeliveryMethodR\bchannels\x12=\n" +
	"\fscheduled_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x129\n" +
	"\n" +
	"expires_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"p\n" +
	"\x18SendNotificationResponse\x12>\n" +
	"\fnotification\x18\x01 \x01(\v2\x1a.notification.NotificationR\fnotification\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xb6\x03\n" +
	"\x1cBroadcastNotificationRequest\x122\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12>\n" +
	"\bpriority\x18\x02 \x01(\x0e2\".notification.NotificationPriorityR\bpriority\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12H\n" +
	"\x04data\x18\x05 \x03(\v24.notification.BroadcastNotificationRequest.DataEntryR\x04data\x125\n" +
	"\x06filter\x18\x06 \x01(\v2\x1d.notification.BroadcastFilterR\x06filter\x128\n" +
	"\bchannels\x18\a \x03(\x0e2\x1c.notification.DeliveryMethodR\bchannels\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\x1dBroadcastNotificationResponse\x12>\n" +
	"\fnotification\x18\x01 \x01(\v2\x1a.notification.NotificationR\fnotification\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xe9\x01\n" +
	"\x17GetNotificationsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x128\n" +
	"\x06status\x18\x02 \x01(\x0e2 .notification.NotificationStatusR\x06status\x122\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x05 \x01(\x05R\x06offset\x12\x19\n" +
	"\bsince_id\x18\x06 \x01(\tR\asinceId\"\x93\x01\n" +
	"\x18GetNotificationsResponse\x12@\n" +
	"\rnotifications\x18\x01 \x03(\v2\x1a.notification.NotificationR\rnotifications\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"\x98\x01\n" +
	"\x18MarkNotificationsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12)\n" +
	"\x10notification_ids\x18\x02 \x03(\tR\x0fnotificationIds\x128\n" +
	"\x06status\x18\x03 \x01(\x0e2 .notification.NotificationStatusR\x06status\"1\n" +
	"\x19MarkNotificationsResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\tR\x05error\"}\n" +
	"\x10SubscribeRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x02 \x01(\tR\teventType\x121\n" +
	"\x06filter\x18\x03 \x01(\v2\x19.notification.EventFilterR\x06filter\"n\n" +
	"\x11SubscribeResponse\x12C\n" +
	"\fsubscription\x18\x01 \x01(\v2\x1f.notification.EventSubscriptionR\fsubscription\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"u\n" +
	"\x12UnsubscribeRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12'\n" +
	"\x0fsubscription_id\x18\x02 \x01(\tR\x0esubscriptionId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x03 \x01(\tR\teventType\"+\n" +
	"\x13UnsubscribeResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\tR\x05error\"2\n" +
	"\x17GetSubscriptionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"w\n" +
	"\x18GetSubscriptionsResponse\x12E\n" +
	"\rsubscriptions\x18\x01 \x03(\v2\x1f.notification.EventSubscriptionR\rsubscriptions\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"6\n" +
	"\x1bGetActiveConnectionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"y\n" +
	"\x1cGetActiveConnectionsResponse\x12C\n" +
	"\vconnections\x18\x01 \x03(\v2!.notification.WebSocketConnectionR\vconnections\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xc3\x01\n" +
	"\x13ProcessEventRequest\x12\x1d\n" +
	"\n" +
	"event_type\x18\x01 \x01(\tR\teventType\x12O\n" +
	"\n" +
	"event_data\x18\x02 \x03(\v20.notification.ProcessEventRequest.EventDataEntryR\teventData\x1a<\n" +
	"\x0eEventDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\",\n" +
	"\x14ProcessEventResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\tR\x05error\"\x1d\n" +
	"\x1bGetNotificationStatsRequest\"\xcf\x01\n" +
	"\x1cGetNotificationStatsResponse\x12Z\n" +
	"\n" +
	"statistics\x18\x01 \x03(\v2:.notification.GetNotificationStatsResponse.StatisticsEntryR\n" +
	"statistics\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x1a=\n" +
	"\x0fStatisticsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x1b\n" +
	"\x19GetConnectionStatsRequest\"\xcb\x01\n" +
	"\x1aGetConnectionStatsResponse\x12X\n" +
	"\n" +
	"statistics\x18\x01 \x03(\v28.notification.GetConnectionStatsResponse.StatisticsEntryR\n" +
	"statistics\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x1a=\n" +
	"\x0fStatisticsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*\x9d\x04\n" +
	"\x10NotificationType\x12!\n" +
	"\x1dNOTIFICATION_TYPE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dNOTIFICATION_TYPE_ROOM_UPDATE\x10\x01\x12#\n" +
	"\x1fNOTIFICATION_TYPE_PLAYER_JOINED\x10\x02\x12!\n" +
	"\x1dNOTIFICATION_TYPE_PLAYER_LEFT\x10\x03\x12\"\n" +
	"\x1eNOTIFICATION_TYPE_PLAYER_READY\x10\x04\x12#\n" +
	"\x1fNOTIFICATION_TYPE_GAME_STARTING\x10\x05\x12\"\n" +
	"\x1eNOTIFICATION_TYPE_GAME_STARTED\x10\x06\x12!\n" +
	"\x1dNOTIFICATION_TYPE_GAME_UPDATE\x10\a\x12#\n" +
	"\x1fNOTIFICATION_TYPE_GAME_FINISHED\x10\b\x12$\n" +
	" NOTIFICATION_TYPE_GAME_CANCELLED\x10\t\x12\"\n" +
	"\x1eNOTIFICATION_TYPE_USER_MESSAGE\x10\n" +
	"\x12$\n" +
	" NOTIFICATION_TYPE_SYSTEM_MESSAGE\x10\v\x12\x1b\n" +
	"\x17NOTIFICATION_TYPE_ERROR\x10\f\x12\x1d\n" +
	"\x19NOTIFICATION_TYPE_WARNING\x10\r\x12\x1a\n" +
	"\x16NOTIFICATION_TYPE_INFO\x10\x0e*\xc2\x01\n" +
	"\x14NotificationPriority\x12%\n" +
	"!NOTIFICATION_PRIORITY_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19NOTIFICATION_PRIORITY_LOW\x10\x01\x12 \n" +
	"\x1cNOTIFICATION_PRIORITY_NORMAL\x10\x02\x12\x1e\n" +
	"\x1aNOTIFICATION_PRIORITY_HIGH\x10\x03\x12\"\n" +
	"\x1eNOTIFICATION_PRIORITY_CRITICAL\x10\x04*\xba\x01\n" +
	"\x0eDeliveryMethod\x12\x1f\n" +
	"\x1bDELIVERY_METHOD_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19DELIVERY_METHOD_WEBSOCKET\x10\x01\x12\x18\n" +
	"\x14DELIVERY_METHOD_PUSH\x10\x02\x12\x19\n" +
	"\x15DELIVERY_METHOD_EMAIL\x10\x03\x12\x17\n" +
	"\x13DELIVERY_METHOD_SMS\x10\x04\x12\x1a\n" +
	"\x16DELIVERY_METHOD_IN_APP\x10\x05*\xfa\x01\n" +
	"\x12NotificationStatus\x12#\n" +
	"\x1fNOTIFICATION_STATUS_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bNOTIFICATION_STATUS_PENDING\x10\x01\x12\x1c\n" +
	"\x18NOTIFICATION_STATUS_SENT\x10\x02\x12!\n" +
	"\x1dNOTIFICATION_STATUS_DELIVERED\x10\x03\x12\x1c\n" +
	"\x18NOTIFICATION_STATUS_READ\x10\x04\x12\x1e\n" +
	"\x1aNOTIFICATION_STATUS_FAILED\x10\x05\x12\x1f\n" +
	"\x1bNOTIFICATION_STATUS_EXPIRED\x10\x062\xd6\b\n" +
	"\x13NotificationService\x12a\n" +
	"\x10SendNotification\x12%.notification.SendNotificationRequest\x1a&.notification.SendNotificationResponse\x12p\n" +
	"\x15BroadcastNotification\x12*.notification.BroadcastNotificationRequest\x1a+.notification.BroadcastNotificationResponse\x12a\n" +
	"\x10GetNotifications\x12%.notification.GetNotificationsRequest\x1a&.notification.GetNotificationsResponse\x12d\n" +
	"\x11MarkNotifications\x12&.notification.MarkNotificationsRequest\x1a'.notification.MarkNotificationsResponse\x12L\n" +
	"\tSubscribe\x12\x1e.notification.SubscribeRequest\x1a\x1f.notification.SubscribeResponse\x12R\n" +
	"\vUnsubscribe\x12 .notification.UnsubscribeRequest\x1a!.notification.UnsubscribeResponse\x12a\n" +
	"\x10GetSubscriptions\x12%.notification.GetSubscriptionsRequest\x1a&.notification.GetSubscriptionsResponse\x12m\n" +
	"\x14GetActiveConnections\x12).notification.GetActiveConnectionsRequest\x1a*.notification.GetActiveConnectionsResponse\x12U\n" +
	"\fProcessEvent\x12!.notification.ProcessEventRequest\x1a\".notification.ProcessEventResponse\x12m\n" +
	"\x14GetNotificationStats\x12).notification.GetNotificationStatsRequest\x1a*.notification.GetNotificationStatsResponse\x12g\n" +
	"\x12GetConnectionStats\x12'.notification.GetConnectionStatsRequest\x1a(.notification.GetConnectionStatsResponseB.Z,github.com/xzgame/notification-service/protob\x06proto3"

var (
	file_proto_notification_proto_rawDescOnce sync.Once
	file_proto_notification_proto_rawDescData []byte
)

func file_proto_notification_proto_rawDescGZIP() []byte {
	file_proto_notification_proto_rawDescOnce.Do(func() {
		file_proto_notification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_notification_proto_rawDesc), len(file_proto_notification_proto_rawDesc)))
	})
	return file_proto_notification_proto_rawDescData
}

var file_proto_notification_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_proto_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_proto_notification_proto_goTypes = []any{
	(NotificationType)(0),                 // 0: notification.NotificationType
	(NotificationPriority)(0),             // 1: notification.NotificationPriority
	(DeliveryMethod)(0),                   // 2: notification.DeliveryMethod
	(NotificationStatus)(0),               // 3: notification.NotificationStatus
	(*Notification)(nil),                  // 4: notification.Notification
	(*NotificationRecipient)(nil),         // 5: notification.NotificationRecipient
	(*EventSubscription)(nil),             // 6: notification.EventSubscription
	(*EventFilter)(nil),                   // 7: notification.EventFilter
	(*WebSocketConnection)(nil),           // 8: notification.WebSocketConnection
	(*BroadcastFilter)(nil),               // 9: notification.BroadcastFilter
	(*SendNotificationRequest)(nil),       // 10: notification.SendNotificationRequest
	(*SendNotificationResponse)(nil),      // 11: notification.SendNotificationResponse
	(*BroadcastNotificationRequest)(nil),  // 12: notification.BroadcastNotificationRequest
	(*BroadcastNotificationResponse)(nil), // 13: notification.BroadcastNotificationResponse
	(*GetNotificationsRequest)(nil),       // 14: notification.GetNotificationsRequest
	(*GetNotificationsResponse)(nil),      // 15: notification.GetNotificationsResponse
	(*MarkNotificationsRequest)(nil),      // 16: notification.MarkNotificationsRequest
	(*MarkNotificationsResponse)(nil),     // 17: notification.MarkNotificationsResponse
	(*SubscribeRequest)(nil),              // 18: notification.SubscribeRequest
	(*SubscribeResponse)(nil),             // 19: notification.SubscribeResponse
	(*UnsubscribeRequest)(nil),            // 20: notification.UnsubscribeRequest
	(*UnsubscribeResponse)(nil),           // 21: notification.UnsubscribeResponse
	(*GetSubscriptionsRequest)(nil),       // 22: notification.GetSubscriptionsRequest
	(*GetSubscriptionsResponse)(nil),      // 23: notification.GetSubscriptionsResponse
	(*GetActiveConnectionsRequest)(nil),   // 24: notification.GetActiveConnectionsRequest
	(*GetActiveConnectionsResponse)(nil),  // 25: notification.GetActiveConnectionsResponse
	(*ProcessEventRequest)(nil),           // 26: notification.ProcessEventRequest
	(*ProcessEventResponse)(nil),          // 27: notification.ProcessEventResponse
	(*GetNotificationStatsRequest)(nil),   // 28: notification.GetNotificationStatsRequest
	(*GetNotificationStatsResponse)(nil),  // 29: notification.GetNotificationStatsResponse
	(*GetConnectionStatsRequest)(nil),     // 30: notification.GetConnectionStatsRequest
	(*GetConnectionStatsResponse)(nil),    // 31: notification.GetConnectionStatsResponse
	nil,                                   // 32: notification.Notification.DataEntry
	nil,                                   // 33: notification.WebSocketConnection.MetadataEntry
	nil,                                   // 34: notification.SendNotificationRequest.DataEntry
	nil,                                   // 35: notification.BroadcastNotificationRequest.DataEntry
	nil,                                   // 36: notification.ProcessEventRequest.EventDataEntry
	nil,                                   // 37: notification.GetNotificationStatsResponse.StatisticsEntry
	nil,                                   // 38: notification.GetConnectionStatsResponse.StatisticsEntry
	(*timestamppb.Timestamp)(nil),         // 39: google.protobuf.Timestamp
}
var file_proto_notification_proto_depIdxs = []int32{
	0,  // 0: notification.Notification.type:type_name -> notification.NotificationType
	1,  // 1: notification.Notification.priority:type_name -> notification.NotificationPriority
	32, // 2: notification.Notification.data:type_name -> notification.Notification.DataEntry
	5,  // 3: notification.Notification.recipients:type_name -> notification.NotificationRecipient
	2,  // 4: notification.Notification.channels:type_name -> notification.DeliveryMethod
	3,  // 5: notification.Notification.status:type_name -> notification.NotificationStatus
	39, // 6: notification.Notification.scheduled_at:type_name -> google.protobuf.Timestamp
	39, // 7: notification.Notification.sent_at:type_name -> google.protobuf.Timestamp
	39, // 8: notification.Notification.expires_at:type_name -> google.protobuf.Timestamp
	39, // 9: notification.Notification.created_at:type_name -> google.protobuf.Timestamp
	39, // 10: notification.Notification.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 11: notification.NotificationRecipient.status:type_name -> notification.NotificationStatus
	39, // 12: notification.NotificationRecipient.delivered_at:type_name -> google.protobuf.Timestamp
	39, // 13: notification.NotificationRecipient.read_at:type_name -> google.protobuf.Timestamp
	39, // 14: notification.NotificationRecipient.last_retry_at:type_name -> google.protobuf.Timestamp
	7,  // 15: notification.EventSubscription.filter:type_name -> notification.EventFilter
	39, // 16: notification.EventSubscription.created_at:type_name -> google.protobuf.Timestamp
	39, // 17: notification.EventSubscription.updated_at:type_name -> google.protobuf.Timestamp
	39, // 18: notification.WebSocketConnection.connected_at:type_name -> google.protobuf.Timestamp
	39, // 19: notification.WebSocketConnection.last_ping_at:type_name -> google.protobuf.Timestamp
	33, // 20: notification.WebSocketConnection.metadata:type_name -> notification.WebSocketConnection.MetadataEntry
	0,  // 21: notification.SendNotificationRequest.type:type_name -> notification.NotificationType
	1,  // 22: notification.SendNotificationRequest.priority:type_name -> notification.NotificationPriority
	34, // 23: notification.SendNotificationRequest.data:type_name -> notification.SendNotificationRequest.DataEntry
	2,  // 24: notification.SendNotificationRequest.channels:type_name -> notification.DeliveryMethod
	39, // 25: notification.SendNotificationRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	39, // 26: notification.SendNotificationRequest.expires_at:type_name -> google.protobuf.Timestamp
	4,  // 27: notification.SendNotificationResponse.notification:type_name -> notification.Notification
	0,  // 28: notification.BroadcastNotificationRequest.type:type_name -> notification.NotificationType
	1,  // 29: notification.BroadcastNotificationRequest.priority:type_name -> notification.NotificationPriority
	35, // 30: notification.BroadcastNotificationRequest.data:type_name -> notification.BroadcastNotificationRequest.DataEntry
	9,  // 31: notification.BroadcastNotificationRequest.filter:type_name -> notification.BroadcastFilter
	2,  // 32: notification.BroadcastNotificationRequest.channels:type_name -> notification.DeliveryMethod
	4,  // 33: notification.BroadcastNotificationResponse.notification:type_name -> notification.Notification
	3,  // 34: notification.GetNotificationsRequest.status:type_name -> notification.NotificationStatus
	0,  // 35: notification.GetNotificationsRequest.type:type_name -> notification.NotificationType
	4,  // 36: notification.GetNotificationsResponse.notifications:type_name -> notification.Notification
	3,  // 37: notification.MarkNotificationsRequest.status:type_name -> notification.NotificationStatus
	7,  // 38: notification.SubscribeRequest.filter:type_name -> notification.EventFilter
	6,  // 39: notification.SubscribeResponse.subscription:type_name -> notification.EventSubscription
	6,  // 40: notification.GetSubscriptionsResponse.subscriptions:type_name -> notification.EventSubscription
	8,  // 41: notification.GetActiveConnectionsResponse.connections:type_name -> notification.WebSocketConnection
	36, // 42: notification.ProcessEventRequest.event_data:type_name -> notification.ProcessEventRequest.EventDataEntry
	37, // 43: notification.GetNotificationStatsResponse.statistics:type_name -> notification.GetNotificationStatsResponse.StatisticsEntry
	38, // 44: notification.GetConnectionStatsResponse.statistics:type_name -> notification.GetConnectionStatsResponse.StatisticsEntry
	10, // 45: notification.NotificationService.SendNotification:input_type -> notification.SendNotificationRequest
	12, // 46: notification.NotificationService.BroadcastNotification:input_type -> notification.BroadcastNotificationRequest
	14, // 47: notification.NotificationService.GetNotifications:input_type -> notification.GetNotificationsRequest
	16, // 48: notification.NotificationService.MarkNotifications:input_type -> notification.MarkNotificationsRequest
	18, // 49: notification.NotificationService.Subscribe:input_type -> notification.SubscribeRequest
	20, // 50: notification.NotificationService.Unsubscribe:input_type -> notification.UnsubscribeRequest
	22, // 51: notification.NotificationService.GetSubscriptions:input_type -> notification.GetSubscriptionsRequest
	24, // 52: notification.NotificationService.GetActiveConnections:input_type -> notification.GetActiveConnectionsRequest
	26, // 53: notification.NotificationService.ProcessEvent:input_type -> notification.ProcessEventRequest
	28, // 54: notification.NotificationService.GetNotificationStats:input_type -> notification.GetNotificationStatsRequest
	30, // 55: notification.NotificationService.GetConnectionStats:input_type -> notification.GetConnectionStatsRequest
	11, // 56: notification.NotificationService.SendNotification:output_type -> notification.SendNotificationResponse
	13, // 57: notification.NotificationService.BroadcastNotification:output_type -> notification.BroadcastNotificationResponse
	15, // 58: notification.NotificationService.GetNotifications:output_type -> notification.GetNotificationsResponse
	17, // 59: notification.NotificationService.MarkNotifications:output_type -> notification.MarkNotificationsResponse
	19, // 60: notification.NotificationService.Subscribe:output_type -> notification.SubscribeResponse
	21, // 61: notification.NotificationService.Unsubscribe:output_type -> notification.UnsubscribeResponse
	23, // 62: notification.NotificationService.GetSubscriptions:output_type -> notification.GetSubscriptionsResponse
	25, // 63: notification.NotificationService.GetActiveConnections:output_type -> notification.GetActiveConnectionsResponse
	27, // 64: notification.NotificationService.ProcessEvent:output_type -> notification.ProcessEventResponse
	29, // 65: notification.NotificationService.GetNotificationStats:output_type -> notification.GetNotificationStatsResponse
	31, // 66: notification.NotificationService.GetConnectionStats:output_type -> notification.GetConnectionStatsResponse
	56, // [56:67] is the sub-list for method output_type
	45, // [45:56] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_proto_notification_proto_init() }
func file_proto_notification_proto_init() {
	if File_proto_notification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_notification_proto_rawDesc), len(file_proto_notification_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_notification_proto_goTypes,
		DependencyIndexes: file_proto_notification_proto_depIdxs,
		EnumInfos:         file_proto_notification_proto_enumTypes,
		MessageInfos:      file_proto_notification_proto_msgTypes,
	}.Build()
	File_proto_notification_proto = out.File
	file_proto_notification_proto_goTypes = nil
	file_proto_notification_proto_depIdxs = nil
}
