syntax = "proto3";

package notification;

option go_package = "github.com/xzgame/notification-service/proto";

import "google/protobuf/timestamp.proto";

// NotificationService provides real-time notification and messaging capabilities
service NotificationService {
  // Notification management
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc BroadcastNotification(BroadcastNotificationRequest) returns (BroadcastNotificationResponse);
  rpc GetNotifications(GetNotificationsRequest) returns (GetNotificationsResponse);
  rpc MarkNotifications(MarkNotificationsRequest) returns (MarkNotificationsResponse);
  
  // Subscription management
  rpc Subscribe(SubscribeRequest) returns (SubscribeResponse);
  rpc Unsubscribe(UnsubscribeRequest) returns (UnsubscribeResponse);
  rpc GetSubscriptions(GetSubscriptionsRequest) returns (GetSubscriptionsResponse);
  
  // WebSocket management
  rpc GetActiveConnections(GetActiveConnectionsRequest) returns (GetActiveConnectionsResponse);
  
  // Event processing
  rpc ProcessEvent(ProcessEventRequest) returns (ProcessEventResponse);
  
  // Statistics and monitoring
  rpc GetNotificationStats(GetNotificationStatsRequest) returns (GetNotificationStatsResponse);
  rpc GetConnectionStats(GetConnectionStatsRequest) returns (GetConnectionStatsResponse);
}

// Enums
enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_ROOM_UPDATE = 1;
  NOTIFICATION_TYPE_PLAYER_JOINED = 2;
  NOTIFICATION_TYPE_PLAYER_LEFT = 3;
  NOTIFICATION_TYPE_PLAYER_READY = 4;
  NOTIFICATION_TYPE_GAME_STARTING = 5;
  NOTIFICATION_TYPE_GAME_STARTED = 6;
  NOTIFICATION_TYPE_GAME_UPDATE = 7;
  NOTIFICATION_TYPE_GAME_FINISHED = 8;
  NOTIFICATION_TYPE_GAME_CANCELLED = 9;
  NOTIFICATION_TYPE_USER_MESSAGE = 10;
  NOTIFICATION_TYPE_SYSTEM_MESSAGE = 11;
  NOTIFICATION_TYPE_ERROR = 12;
  NOTIFICATION_TYPE_WARNING = 13;
  NOTIFICATION_TYPE_INFO = 14;
}

enum NotificationPriority {
  NOTIFICATION_PRIORITY_UNSPECIFIED = 0;
  NOTIFICATION_PRIORITY_LOW = 1;
  NOTIFICATION_PRIORITY_NORMAL = 2;
  NOTIFICATION_PRIORITY_HIGH = 3;
  NOTIFICATION_PRIORITY_CRITICAL = 4;
}

enum DeliveryMethod {
  DELIVERY_METHOD_UNSPECIFIED = 0;
  DELIVERY_METHOD_WEBSOCKET = 1;
  DELIVERY_METHOD_PUSH = 2;
  DELIVERY_METHOD_EMAIL = 3;
  DELIVERY_METHOD_SMS = 4;
  DELIVERY_METHOD_IN_APP = 5;
}

enum NotificationStatus {
  NOTIFICATION_STATUS_UNSPECIFIED = 0;
  NOTIFICATION_STATUS_PENDING = 1;
  NOTIFICATION_STATUS_SENT = 2;
  NOTIFICATION_STATUS_DELIVERED = 3;
  NOTIFICATION_STATUS_READ = 4;
  NOTIFICATION_STATUS_FAILED = 5;
  NOTIFICATION_STATUS_EXPIRED = 6;
}

// Core data structures
message Notification {
  string id = 1;
  NotificationType type = 2;
  NotificationPriority priority = 3;
  string title = 4;
  string message = 5;
  map<string, string> data = 6;
  repeated NotificationRecipient recipients = 7;
  repeated DeliveryMethod channels = 8;
  NotificationStatus status = 9;
  google.protobuf.Timestamp scheduled_at = 10;
  google.protobuf.Timestamp sent_at = 11;
  google.protobuf.Timestamp expires_at = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
}

message NotificationRecipient {
  string user_id = 1;
  string username = 2;
  NotificationStatus status = 3;
  google.protobuf.Timestamp delivered_at = 4;
  google.protobuf.Timestamp read_at = 5;
  string failure_reason = 6;
  int32 retry_count = 7;
  google.protobuf.Timestamp last_retry_at = 8;
}

message EventSubscription {
  string id = 1;
  string user_id = 2;
  string event_type = 3;
  EventFilter filter = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message EventFilter {
  string room_id = 1;
  string game_type = 2;
  repeated string user_ids = 3;
  repeated string event_types = 4;
}

message WebSocketConnection {
  string id = 1;
  string user_id = 2;
  string username = 3;
  string room_id = 4;
  google.protobuf.Timestamp connected_at = 5;
  google.protobuf.Timestamp last_ping_at = 6;
  repeated string subscriptions = 7;
  map<string, string> metadata = 8;
}

message BroadcastFilter {
  string room_id = 1;
  string game_type = 2;
  repeated string user_ids = 3;
  repeated string exclude_ids = 4;
}

// Request/Response messages

// SendNotification
message SendNotificationRequest {
  NotificationType type = 1;
  NotificationPriority priority = 2;
  string title = 3;
  string message = 4;
  map<string, string> data = 5;
  repeated string recipients = 6;
  repeated DeliveryMethod channels = 7;
  google.protobuf.Timestamp scheduled_at = 8;
  google.protobuf.Timestamp expires_at = 9;
}

message SendNotificationResponse {
  Notification notification = 1;
  string error = 2;
}

// BroadcastNotification
message BroadcastNotificationRequest {
  NotificationType type = 1;
  NotificationPriority priority = 2;
  string title = 3;
  string message = 4;
  map<string, string> data = 5;
  BroadcastFilter filter = 6;
  repeated DeliveryMethod channels = 7;
}

message BroadcastNotificationResponse {
  Notification notification = 1;
  string error = 2;
}

// GetNotifications
message GetNotificationsRequest {
  string user_id = 1;
  NotificationStatus status = 2;
  NotificationType type = 3;
  int32 limit = 4;
  int32 offset = 5;
  string since_id = 6;
}

message GetNotificationsResponse {
  repeated Notification notifications = 1;
  int32 total_count = 2;
  string error = 3;
}

// MarkNotifications
message MarkNotificationsRequest {
  string user_id = 1;
  repeated string notification_ids = 2;
  NotificationStatus status = 3;
}

message MarkNotificationsResponse {
  string error = 1;
}

// Subscribe
message SubscribeRequest {
  string user_id = 1;
  string event_type = 2;
  EventFilter filter = 3;
}

message SubscribeResponse {
  EventSubscription subscription = 1;
  string error = 2;
}

// Unsubscribe
message UnsubscribeRequest {
  string user_id = 1;
  string subscription_id = 2;
  string event_type = 3;
}

message UnsubscribeResponse {
  string error = 1;
}

// GetSubscriptions
message GetSubscriptionsRequest {
  string user_id = 1;
}

message GetSubscriptionsResponse {
  repeated EventSubscription subscriptions = 1;
  string error = 2;
}

// GetActiveConnections
message GetActiveConnectionsRequest {
  string user_id = 1;
}

message GetActiveConnectionsResponse {
  repeated WebSocketConnection connections = 1;
  string error = 2;
}

// ProcessEvent
message ProcessEventRequest {
  string event_type = 1;
  map<string, string> event_data = 2;
}

message ProcessEventResponse {
  string error = 1;
}

// GetNotificationStats
message GetNotificationStatsRequest {
}

message GetNotificationStatsResponse {
  map<string, string> statistics = 1;
  string error = 2;
}

// GetConnectionStats
message GetConnectionStatsRequest {
}

message GetConnectionStatsResponse {
  map<string, string> statistics = 1;
  string error = 2;
}
