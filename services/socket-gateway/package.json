{"name": "xzgame-socket-gateway", "version": "1.0.0", "description": "Socket Gateway service for XZ Game backend - handles real-time bidirectional communication using Socket.io", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "build": "echo 'No build step required for Node.js'", "docker:build:dev": "docker build -f Dockerfile.dev -t xzgame-socket-gateway:dev .", "docker:build:prod": "docker build -f Dockerfile.prod -t xzgame-socket-gateway:prod .", "docker:run:dev": "docker run -p 3001:3001 --env-file .env.dev xzgame-socket-gateway:dev", "docker:run:prod": "docker run -p 3001:3001 --env-file .env.prod xzgame-socket-gateway:prod"}, "keywords": ["socket.io", "websocket", "real-time", "game", "microservice", "nodejs", "express"], "author": "XZ Game Development Team", "license": "MIT", "dependencies": {"@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@socket.io/redis-adapter": "^8.2.1", "ajv-formats": "^3.0.1", "axios": "^1.9.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongodb": "^6.16.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "node-cron": "^3.0.3", "prom-client": "^15.1.0", "redis": "^4.6.11", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.8", "chai": "^5.2.0", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.6.0", "faker": "^6.6.6", "jest": "^29.7.0", "mocha": "^11.5.0", "mongodb-memory-server": "^9.1.3", "nock": "^13.4.0", "nodemon": "^3.0.2", "nyc": "^17.1.0", "redis-memory-server": "^0.7.0", "sinon": "^20.0.0", "socket.io-client": "^4.7.4", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/xzgame/backend.git", "directory": "services/socket-gateway"}, "bugs": {"url": "https://github.com/xzgame/backend/issues"}, "homepage": "https://github.com/xzgame/backend/tree/main/services/socket-gateway#readme"}