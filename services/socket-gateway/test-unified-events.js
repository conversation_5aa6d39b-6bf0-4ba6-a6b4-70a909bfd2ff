#!/usr/bin/env node

/**
 * Integration test for unified room_info_updated events
 * This script tests that the optimized event structure works correctly
 */

const io = require('socket.io-client');

// Test configuration
const SOCKET_URL = 'http://localhost:3001';
const TEST_ROOM_ID = '684f8da83e02d7af17d57846'; // Use the room from the user's example
const TEST_USER_ID = '684d9c83a533d3e5fea7dc36';
const TEST_USERNAME = 'test_user_unified';

let socket;
let eventCount = 0;
let receivedEvents = [];

function connectSocket() {
  return new Promise((resolve, reject) => {
    console.log('🔌 Connecting to socket gateway...');
    
    socket = io(SOCKET_URL, {
      transports: ['websocket'],
      timeout: 5000
    });

    socket.on('connect', () => {
      console.log('✅ Connected to socket gateway');
      
      // Set user context
      socket.userId = TEST_USER_ID;
      socket.username = TEST_USERNAME;
      
      resolve();
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Connection failed:', error.message);
      reject(error);
    });

    socket.on('disconnect', () => {
      console.log('🔌 Disconnected from socket gateway');
    });
  });
}

function setupEventListeners() {
  console.log('👂 Setting up event listeners...');
  
  // Listen for the unified room_info_updated event
  socket.on('room_info_updated', (data) => {
    eventCount++;
    receivedEvents.push({
      type: 'room_info_updated',
      timestamp: new Date().toISOString(),
      data: data
    });
    
    console.log(`📨 Received room_info_updated event #${eventCount}:`);
    console.log(`   Reason: ${data.reason}`);
    console.log(`   Room ID: ${data.roomId}`);
    console.log(`   Players Total: ${data.roomInfo?.players?.total || 0}`);
    console.log(`   Players with Colors: ${data.roomInfo?.players?.withColors || 0}`);
    
    if (data.roomInfo?.colors) {
      console.log(`   Available Colors: ${data.roomInfo.colors.available?.length || 0}`);
      console.log(`   Taken Colors: ${data.roomInfo.colors.taken?.length || 0}`);
    }
    
    console.log('');
  });

  // Listen for deprecated events (these should NOT be received)
  const deprecatedEvents = ['available_colors', 'color_selection_update', 'color_state_sync'];
  
  deprecatedEvents.forEach(eventName => {
    socket.on(eventName, (data) => {
      console.log(`⚠️  DEPRECATED EVENT RECEIVED: ${eventName}`);
      console.log('   This should not happen with the unified structure!');
      receivedEvents.push({
        type: eventName,
        timestamp: new Date().toISOString(),
        data: data
      });
    });
  });
}

function subscribeToRoom() {
  return new Promise((resolve, reject) => {
    console.log(`🏠 Subscribing to room ${TEST_ROOM_ID}...`);
    
    socket.emit('subscribe_room', {
      roomId: TEST_ROOM_ID,
      userId: TEST_USER_ID,
      username: TEST_USERNAME
    }, (response) => {
      if (response && response.success) {
        console.log('✅ Successfully subscribed to room');
        resolve();
      } else {
        console.error('❌ Failed to subscribe to room:', response);
        reject(new Error('Room subscription failed'));
      }
    });
  });
}

function selectColor() {
  return new Promise((resolve, reject) => {
    console.log('🎨 Selecting color for prize wheel...');
    
    socket.emit('select_color', {
      roomId: TEST_ROOM_ID,
      color: 'green'
    }, (response) => {
      if (response && response.success) {
        console.log('✅ Successfully selected color');
        resolve();
      } else {
        console.error('❌ Failed to select color:', response);
        reject(new Error('Color selection failed'));
      }
    });
  });
}

function unsubscribeFromRoom() {
  return new Promise((resolve, reject) => {
    console.log(`🏠 Unsubscribing from room ${TEST_ROOM_ID}...`);
    
    socket.emit('unsubscribe_room', {
      roomId: TEST_ROOM_ID,
      userId: TEST_USER_ID,
      username: TEST_USERNAME
    }, (response) => {
      if (response && response.success) {
        console.log('✅ Successfully unsubscribed from room');
        resolve();
      } else {
        console.error('❌ Failed to unsubscribe from room:', response);
        reject(new Error('Room unsubscription failed'));
      }
    });
  });
}

function analyzeResults() {
  console.log('\n📊 Test Results Analysis:');
  console.log(`   Total events received: ${receivedEvents.length}`);
  
  const roomInfoUpdatedEvents = receivedEvents.filter(e => e.type === 'room_info_updated');
  const deprecatedEvents = receivedEvents.filter(e => e.type !== 'room_info_updated');
  
  console.log(`   room_info_updated events: ${roomInfoUpdatedEvents.length}`);
  console.log(`   Deprecated events: ${deprecatedEvents.length}`);
  
  if (deprecatedEvents.length > 0) {
    console.log('\n⚠️  DEPRECATED EVENTS DETECTED:');
    deprecatedEvents.forEach(event => {
      console.log(`   - ${event.type} at ${event.timestamp}`);
    });
  }
  
  if (roomInfoUpdatedEvents.length > 0) {
    console.log('\n✅ UNIFIED EVENTS RECEIVED:');
    roomInfoUpdatedEvents.forEach((event, index) => {
      console.log(`   ${index + 1}. Reason: ${event.data.reason} at ${event.timestamp}`);
    });
  }
  
  // Check if we have the expected structure
  const hasValidStructure = roomInfoUpdatedEvents.every(event => {
    const data = event.data;
    return data.reason && data.roomId && data.roomInfo && 
           data.roomInfo.room && data.roomInfo.players && data.roomInfo.game;
  });
  
  console.log(`\n🔍 Structure Validation: ${hasValidStructure ? '✅ PASSED' : '❌ FAILED'}`);
  
  return {
    totalEvents: receivedEvents.length,
    unifiedEvents: roomInfoUpdatedEvents.length,
    deprecatedEvents: deprecatedEvents.length,
    structureValid: hasValidStructure
  };
}

async function runTest() {
  try {
    console.log('🚀 Starting Unified Room Info Events Test\n');
    
    // Connect to socket
    await connectSocket();
    
    // Setup event listeners
    setupEventListeners();
    
    // Wait a moment for setup
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Subscribe to room (should trigger room_info_updated)
    await subscribeToRoom();
    
    // Wait for events
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Select a color (should trigger room_info_updated with color data)
    await selectColor();
    
    // Wait for events
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Unsubscribe from room
    await unsubscribeFromRoom();
    
    // Wait for final events
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Analyze results
    const results = analyzeResults();
    
    // Disconnect
    socket.disconnect();
    
    console.log('\n🎯 Test Summary:');
    if (results.deprecatedEvents === 0 && results.unifiedEvents > 0 && results.structureValid) {
      console.log('✅ SUCCESS: Unified event structure is working correctly!');
      console.log('   - No deprecated events received');
      console.log('   - Unified events have correct structure');
      console.log('   - Events triggered for subscription and color selection');
    } else {
      console.log('❌ ISSUES DETECTED:');
      if (results.deprecatedEvents > 0) {
        console.log(`   - ${results.deprecatedEvents} deprecated events still being sent`);
      }
      if (results.unifiedEvents === 0) {
        console.log('   - No unified events received');
      }
      if (!results.structureValid) {
        console.log('   - Event structure validation failed');
      }
    }
    
    process.exit(results.deprecatedEvents === 0 && results.unifiedEvents > 0 && results.structureValid ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (socket) {
      socket.disconnect();
    }
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  if (socket) {
    socket.disconnect();
  }
  process.exit(1);
});

// Run the test
runTest();
