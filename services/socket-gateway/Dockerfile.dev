# Development Dockerfile for Socket Gateway Service
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S socketgateway -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=development && \
    npm cache clean --force

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs && \
    chown -R socketgateway:nodejs /app

# Switch to non-root user
USER socketgateway

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start application in development mode
CMD ["npm", "run", "dev"]
