module.exports = {
  extends: [
    'airbnb-base',
    'plugin:jest/recommended',
  ],
  env: {
    node: true,
    jest: true,
    es2020: true,
  },
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  rules: {
    // Disable some overly strict rules for this project
    'no-console': 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error',

    // Allow parameter reassignment for socket objects and similar
    'no-param-reassign': ['error', {
      props: true,
      ignorePropertyModificationsFor: ['socket', 'req', 'res', 'ret', 'player']
    }],

    // Allow dangling underscores for MongoDB fields
    'no-underscore-dangle': ['error', {
      allow: ['_id', '__v']
    }],

    // Allow unary operators
    'no-plusplus': 'off',

    // Allow unnamed functions in some contexts
    'func-names': 'off',

    // Allow longer lines for some cases
    'max-len': ['error', {
      code: 120,
      ignoreUrls: true,
      ignoreStrings: true,
      ignoreTemplateLiterals: true
    }],

    // Allow require() in some contexts
    'global-require': 'off',

    // Allow for-in loops
    'no-restricted-syntax': 'off',

    // Allow await in loops
    'no-await-in-loop': 'off',

    // Allow nested ternary
    'no-nested-ternary': 'off',

    // Allow function hoisting
    'no-use-before-define': ['error', { functions: false }],

    // Allow unused variables in function parameters
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],

    // Allow inconsistent returns
    'consistent-return': 'off',

    // Allow class methods that don't use this
    'class-methods-use-this': 'off',

    // Allow variable shadowing in some cases
    'no-shadow': ['error', { allow: ['error', 'data', 'result'] }],

    // Jest specific rules
    'jest/no-done-callback': 'off',
    'jest/expect-expect': 'off',
  },
  overrides: [
    {
      files: ['tests/**/*.js'],
      rules: {
        // More lenient rules for test files
        'no-unused-vars': 'off',
        'prefer-arrow-callback': 'off',
        'func-names': 'off',
      },
    },
  ],
};
