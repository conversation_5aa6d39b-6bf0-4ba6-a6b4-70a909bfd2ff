require('dotenv').config();

const config = {
  // Environment
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3001,

  // Database
  mongodb: {
    url: process.env.MONGODB_URL || 'mongodb://localhost:27017/xzgame',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    options: {
      retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY, 10) || 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES, 10) || 3,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: parseInt(process.env.REDIS_TIMEOUT, 10) || 5000,
      commandTimeout: parseInt(process.env.REDIS_TIMEOUT, 10) || 5000,
    },
    poolSize: parseInt(process.env.REDIS_POOL_SIZE, 10) || 50,
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your_jwt_secret_here',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'xzgame-auth-service', // Accept tokens with Auth Service as issuer
    audience: process.env.JWT_AUDIENCE ? process.env.JWT_AUDIENCE.split(',') : ['xzgame-api', 'xzgame-game-service', 'xzgame-socket-gateway'], // Match Auth Service audience format
  },

  // CORS
  cors: {
    origins: process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : ['http://localhost:3000', 'http://localhost:8080'],
    credentials: true,
    optionsSuccessStatus: 200,
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW, 10) || 60000,
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
    gameActions: parseInt(process.env.RATE_LIMIT_GAME_ACTIONS, 10) || 10,
    chatMessages: parseInt(process.env.RATE_LIMIT_CHAT_MESSAGES, 10) || 20,
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Socket.io
  socket: {
    pingTimeout: parseInt(process.env.SOCKET_PING_TIMEOUT, 10) || 60000,
    pingInterval: parseInt(process.env.SOCKET_PING_INTERVAL, 10) || 25000,
    upgradeTimeout: parseInt(process.env.SOCKET_UPGRADE_TIMEOUT, 10) || 10000,
    maxHttpBufferSize: parseInt(process.env.SOCKET_MAX_HTTP_BUFFER_SIZE, 10) || 1048576,
    allowEIO3: true,
    transports: ['websocket', 'polling'],
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED !== 'false',
      filename: process.env.LOG_FILENAME || 'logs/socket-gateway.log',
      maxSize: process.env.LOG_MAX_SIZE || '20m',
      maxFiles: process.env.LOG_MAX_FILES || '14d',
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
      colorize: process.env.LOG_COLORIZE !== 'false',
    },
  },

  // Monitoring
  monitoring: {
    enabled: process.env.METRICS_ENABLED !== 'false',
    port: parseInt(process.env.METRICS_PORT, 10) || 9091,
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000,
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your_session_secret_here',
    csrfEnabled: process.env.CSRF_ENABLED === 'true',
    httpsOnly: process.env.HTTPS_ONLY === 'true',
  },

  // Performance
  performance: {
    connectionPoolSize: parseInt(process.env.CONNECTION_POOL_SIZE, 10) || 100,
    maxConnectionsPerIP: parseInt(process.env.MAX_CONNECTIONS_PER_IP, 10) || 10,
    cleanupInterval: parseInt(process.env.CLEANUP_INTERVAL, 10) || 300000,
    memoryThreshold: parseFloat(process.env.MEMORY_THRESHOLD) || 0.8,
  },

  // External Services
  services: {
    authService: {
      url: process.env.AUTH_SERVICE_URL || 'http://localhost:3000',
      timeout: 10000,
    },
    gameService: {
      url: process.env.GAME_SERVICE_URL || 'http://localhost:8080',
      timeout: 15000,
    },
    managerService: {
      url: process.env.MANAGER_SERVICE_URL || 'http://localhost:3002',
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
  },

  // Game Configuration
  game: {
    maxRooms: parseInt(process.env.MAX_ROOMS, 10) || 1000,
    maxPlayersPerRoom: parseInt(process.env.MAX_PLAYERS_PER_ROOM, 10) || 8,
    roomTimeout: parseInt(process.env.ROOM_TIMEOUT, 10) || 3600000,
    gameTimeout: parseInt(process.env.GAME_TIMEOUT, 10) || 300000,
    joinRoomTimeout: parseInt(process.env.JOIN_ROOM_TIMEOUT, 10) || 30000, // 30 seconds for join room requests
    leaveRoomTimeout: parseInt(process.env.LEAVE_ROOM_TIMEOUT, 10) || 15000, // 15 seconds for leave room requests
    playerReadyTimeout: parseInt(process.env.PLAYER_READY_TIMEOUT, 10) || 15000, // 15 seconds for player ready requests
  },

  // Shutdown Configuration
  shutdown: {
    timeout: parseInt(process.env.SHUTDOWN_TIMEOUT, 10) || 15000, // 15 seconds total shutdown timeout
    serviceTimeout: parseInt(process.env.SERVICE_SHUTDOWN_TIMEOUT, 10) || 5000, // 5 seconds per service
    gracefulSocketDisconnect: process.env.GRACEFUL_SOCKET_DISCONNECT !== 'false', // Enable graceful socket disconnect
  },

  // Debug
  debug: {
    socketLogs: process.env.ENABLE_SOCKET_LOGS === 'true',
    redisLogs: process.env.ENABLE_REDIS_LOGS === 'true',
    performanceLogs: process.env.ENABLE_PERFORMANCE_LOGS === 'true',
    shutdownLogs: process.env.ENABLE_SHUTDOWN_LOGS !== 'false', // Enable shutdown debugging by default
  },
};

// Validate required configuration
const requiredEnvVars = ['JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter((envVar) => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

module.exports = config;
