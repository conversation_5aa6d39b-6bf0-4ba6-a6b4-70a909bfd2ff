/**
 * Operation Monitor Service
 * 
 * Tracks and monitors join/leave room operations for health and performance metrics
 */

const logger = require('../utils/logger');

class OperationMonitor {
  constructor() {
    this.metrics = {
      joinRoom: {
        total: 0,
        successful: 0,
        failed: 0,
        rollbacks: 0,
        averageResponseTime: 0,
        lastOperationTime: null,
      },
      leaveRoom: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0,
        lastOperationTime: null,
      },
      operationLocks: {
        acquired: 0,
        released: 0,
        timeouts: 0,
        concurrent: 0,
        currentLocks: 0,
      },
      circuitBreaker: {
        openCount: 0,
        halfOpenCount: 0,
        closedCount: 0,
        lastStateChange: null,
      },
      errors: {
        authenticationFailed: 0,
        validationFailed: 0,
        serviceUnavailable: 0,
        connectionLost: 0,
        operationTimeout: 0,
      },
    };

    this.responseTimes = {
      joinRoom: [],
      leaveRoom: [],
    };

    this.maxResponseTimeHistory = 100; // Keep last 100 response times
    this.alertThresholds = {
      failureRate: 0.1, // 10%
      averageResponseTime: 5000, // 5 seconds
      concurrentOperations: 50,
    };

    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  /**
   * Record join room operation start
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   * @returns {Object} Operation context
   */
  startJoinRoomOperation(userId, roomId) {
    const context = {
      operation: 'join_room',
      userId,
      roomId,
      startTime: Date.now(),
      id: `join_${userId}_${roomId}_${Date.now()}`,
    };

    this.metrics.joinRoom.total++;
    
    logger.debug('Join room operation started', {
      operationId: context.id,
      userId,
      roomId,
    });

    return context;
  }

  /**
   * Record join room operation completion
   * @param {Object} context - Operation context
   * @param {boolean} success - Whether operation succeeded
   * @param {string} reason - Completion reason
   */
  completeJoinRoomOperation(context, success, reason = 'completed') {
    const duration = Date.now() - context.startTime;
    
    if (success) {
      this.metrics.joinRoom.successful++;
    } else {
      this.metrics.joinRoom.failed++;
      
      // Track specific error types
      this.trackErrorType(reason);
    }

    // Update response time metrics
    this.updateResponseTime('joinRoom', duration);
    this.metrics.joinRoom.lastOperationTime = Date.now();

    logger.debug('Join room operation completed', {
      operationId: context.id,
      success,
      reason,
      duration,
      userId: context.userId,
      roomId: context.roomId,
    });

    // Check for alerts
    this.checkAlerts('joinRoom');
  }

  /**
   * Record leave room operation start
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   * @returns {Object} Operation context
   */
  startLeaveRoomOperation(userId, roomId) {
    const context = {
      operation: 'leave_room',
      userId,
      roomId,
      startTime: Date.now(),
      id: `leave_${userId}_${roomId}_${Date.now()}`,
    };

    this.metrics.leaveRoom.total++;
    
    logger.debug('Leave room operation started', {
      operationId: context.id,
      userId,
      roomId,
    });

    return context;
  }

  /**
   * Record leave room operation completion
   * @param {Object} context - Operation context
   * @param {boolean} success - Whether operation succeeded
   * @param {string} reason - Completion reason
   */
  completeLeaveRoomOperation(context, success, reason = 'completed') {
    const duration = Date.now() - context.startTime;
    
    if (success) {
      this.metrics.leaveRoom.successful++;
    } else {
      this.metrics.leaveRoom.failed++;
      this.trackErrorType(reason);
    }

    this.updateResponseTime('leaveRoom', duration);
    this.metrics.leaveRoom.lastOperationTime = Date.now();

    logger.debug('Leave room operation completed', {
      operationId: context.id,
      success,
      reason,
      duration,
      userId: context.userId,
      roomId: context.roomId,
    });

    this.checkAlerts('leaveRoom');
  }

  /**
   * Record rollback operation
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   * @param {string} reason - Rollback reason
   */
  recordRollback(userId, roomId, reason) {
    this.metrics.joinRoom.rollbacks++;
    
    logger.warn('Operation rollback recorded', {
      userId,
      roomId,
      reason,
      totalRollbacks: this.metrics.joinRoom.rollbacks,
    });
  }

  /**
   * Record operation lock events
   * @param {string} event - Event type (acquired, released, timeout, concurrent)
   * @param {string} userId - User ID
   */
  recordLockEvent(event, userId) {
    this.metrics.operationLocks[event]++;
    
    if (event === 'acquired') {
      this.metrics.operationLocks.currentLocks++;
    } else if (event === 'released') {
      this.metrics.operationLocks.currentLocks = Math.max(0, this.metrics.operationLocks.currentLocks - 1);
    }

    logger.debug('Operation lock event', {
      event,
      userId,
      currentLocks: this.metrics.operationLocks.currentLocks,
    });
  }

  /**
   * Update response time metrics
   * @param {string} operation - Operation type
   * @param {number} duration - Duration in milliseconds
   */
  updateResponseTime(operation, duration) {
    if (!this.responseTimes[operation]) {
      this.responseTimes[operation] = [];
    }

    this.responseTimes[operation].push(duration);
    
    // Keep only recent response times
    if (this.responseTimes[operation].length > this.maxResponseTimeHistory) {
      this.responseTimes[operation].shift();
    }

    // Calculate average
    const times = this.responseTimes[operation];
    const average = times.reduce((sum, time) => sum + time, 0) / times.length;
    this.metrics[operation].averageResponseTime = Math.round(average);
  }

  /**
   * Track specific error types
   * @param {string} reason - Error reason
   */
  trackErrorType(reason) {
    const errorMappings = {
      'authentication_failed': 'authenticationFailed',
      'validation_failed': 'validationFailed',
      'service_unavailable': 'serviceUnavailable',
      'connection_lost': 'connectionLost',
      'timeout': 'operationTimeout',
    };

    const errorType = errorMappings[reason] || 'other';
    if (this.metrics.errors[errorType] !== undefined) {
      this.metrics.errors[errorType]++;
    }
  }

  /**
   * Check for alert conditions
   * @param {string} operation - Operation type
   */
  checkAlerts(operation) {
    const metrics = this.metrics[operation];
    const failureRate = metrics.total > 0 ? metrics.failed / metrics.total : 0;
    
    // Check failure rate
    if (failureRate > this.alertThresholds.failureRate && metrics.total >= 10) {
      logger.warn('High failure rate detected', {
        operation,
        failureRate: (failureRate * 100).toFixed(2) + '%',
        threshold: (this.alertThresholds.failureRate * 100).toFixed(2) + '%',
        totalOperations: metrics.total,
        failedOperations: metrics.failed,
      });
    }

    // Check response time
    if (metrics.averageResponseTime > this.alertThresholds.averageResponseTime) {
      logger.warn('High response time detected', {
        operation,
        averageResponseTime: metrics.averageResponseTime,
        threshold: this.alertThresholds.averageResponseTime,
      });
    }

    // Check concurrent operations
    if (this.metrics.operationLocks.currentLocks > this.alertThresholds.concurrentOperations) {
      logger.warn('High concurrent operations detected', {
        currentLocks: this.metrics.operationLocks.currentLocks,
        threshold: this.alertThresholds.concurrentOperations,
      });
    }
  }

  /**
   * Get current metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      uptime: process.uptime(),
    };
  }

  /**
   * Get health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    const joinFailureRate = this.metrics.joinRoom.total > 0 ? 
      this.metrics.joinRoom.failed / this.metrics.joinRoom.total : 0;
    const leaveFailureRate = this.metrics.leaveRoom.total > 0 ? 
      this.metrics.leaveRoom.failed / this.metrics.leaveRoom.total : 0;

    const isHealthy = 
      joinFailureRate < this.alertThresholds.failureRate &&
      leaveFailureRate < this.alertThresholds.failureRate &&
      this.metrics.joinRoom.averageResponseTime < this.alertThresholds.averageResponseTime &&
      this.metrics.leaveRoom.averageResponseTime < this.alertThresholds.averageResponseTime;

    return {
      healthy: isHealthy,
      joinFailureRate: (joinFailureRate * 100).toFixed(2) + '%',
      leaveFailureRate: (leaveFailureRate * 100).toFixed(2) + '%',
      averageJoinTime: this.metrics.joinRoom.averageResponseTime,
      averageLeaveTime: this.metrics.leaveRoom.averageResponseTime,
      currentLocks: this.metrics.operationLocks.currentLocks,
      totalRollbacks: this.metrics.joinRoom.rollbacks,
    };
  }

  /**
   * Reset metrics (for testing or maintenance)
   */
  resetMetrics() {
    Object.keys(this.metrics).forEach(key => {
      if (typeof this.metrics[key] === 'object') {
        Object.keys(this.metrics[key]).forEach(subKey => {
          if (typeof this.metrics[key][subKey] === 'number') {
            this.metrics[key][subKey] = 0;
          } else if (this.metrics[key][subKey] === null) {
            this.metrics[key][subKey] = null;
          }
        });
      }
    });

    this.responseTimes = {
      joinRoom: [],
      leaveRoom: [],
    };

    logger.info('Operation metrics reset');
  }

  /**
   * Start periodic cleanup of old data
   */
  startPeriodicCleanup() {
    setInterval(() => {
      // Clean up old response times
      Object.keys(this.responseTimes).forEach(operation => {
        if (this.responseTimes[operation].length > this.maxResponseTimeHistory) {
          this.responseTimes[operation] = this.responseTimes[operation].slice(-this.maxResponseTimeHistory);
        }
      });
    }, 60000); // Every minute
  }
}

module.exports = new OperationMonitor();
