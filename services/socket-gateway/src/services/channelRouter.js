/**
 * Channel Router
 * 
 * Provides fast, standardized routing for Redis channel events
 * Replaces the complex conditional logic in socketService.js
 */

const { CHANNELS, CHANNEL_PATTERNS, ChannelUtils } = require('../constants/channels');
const messageBuilder = require('../utils/messageBuilder');
const logger = require('../utils/logger');

class ChannelRouter {
  constructor() {
    // Pre-compiled routing table for exact matches
    this.exactRoutes = new Map();
    
    // Pattern-based routing for dynamic channels
    this.patternRoutes = new Map();
    
    // Legacy channel mappings (for migration period)
    this.legacyRoutes = new Map();
    
    this.setupRoutes();
  }

  /**
   * Set up routing tables
   */
  setupRoutes() {
    // Exact match routes
    this.exactRoutes.set(CHANNELS.LOBBY_EVENTS, 'handleLobbyEvent');
    this.exactRoutes.set(CHANNELS.GLOBAL_EVENTS, 'handleGlobalEvent');
    this.exactRoutes.set(CHANNELS.MONITORING_EVENTS, 'handleMonitoringEvent');
    
    // Pattern-based routes
    this.patternRoutes.set(/^room:([^:]+):events$/, 'handleRoomEvent');
    this.patternRoutes.set(/^user:([^:]+):events$/, 'handleUserEvent');
    this.patternRoutes.set(/^service:([^:]+):requests$/, 'handleServiceRequest');
    
    // Legacy routes (for backward compatibility during migration)
    this.legacyRoutes.set('socket:events', 'handleLegacySocketEvent');
    this.legacyRoutes.set(/^game:room:(.+)$/, 'handleLegacyGameRoomEvent');
    this.legacyRoutes.set('game:lobby:updates', 'handleLegacyLobbyEvent');
    this.legacyRoutes.set('game:global:events', 'handleLegacyGlobalEvent');
    this.legacyRoutes.set(/^user:([^:]+):notifications$/, 'handleLegacyUserEvent');
  }

  /**
   * Route incoming Redis message to appropriate handler
   * @param {string} channel - Redis channel name
   * @param {Object} message - Message payload
   * @param {Object} context - Handler context (usually socketService instance)
   * @returns {Promise<boolean>} - True if message was handled
   */
  async routeMessage(channel, message, context) {
    try {
      // Validate message format
      const validation = messageBuilder.validateIncomingMessage(message);
      if (!validation.isValid) {
        logger.warn('Invalid message format received', {
          channel,
          errors: validation.errors,
          message: validation.message
        });
        return false;
      }

      // Extract event info
      const eventInfo = messageBuilder.extractEventInfo(message);
      if (!eventInfo) {
        logger.warn('Could not extract event info from message', { channel, message });
        return false;
      }

      // Try exact match first
      const exactHandler = this.exactRoutes.get(channel);
      if (exactHandler) {
        await this.callHandler(context, exactHandler, channel, message, eventInfo);
        return true;
      }

      // Try pattern matching for standard channels
      for (const [pattern, handlerName] of this.patternRoutes) {
        const match = channel.match(pattern);
        if (match) {
          await this.callHandler(context, handlerName, channel, message, eventInfo, match);
          return true;
        }
      }

      // Try legacy routes (with deprecation warning)
      const legacyHandler = this.findLegacyHandler(channel);
      if (legacyHandler) {
        logger.warn('Legacy channel pattern detected - please migrate to standard format', {
          legacyChannel: channel,
          standardChannel: ChannelUtils.convertLegacyChannel(channel),
          eventType: eventInfo.type
        });
        
        await this.callHandler(context, legacyHandler.handlerName, channel, message, eventInfo, legacyHandler.match);
        return true;
      }

      // Unknown channel
      logger.debug('Unknown channel pattern', {
        channel,
        eventType: eventInfo.type,
        isStandard: ChannelUtils.isStandardChannel(channel),
        isLegacy: ChannelUtils.isLegacyChannel(channel)
      });

      return false;

    } catch (error) {
      logger.logError(error, {
        context: 'Channel routing',
        channel,
        messageType: message.event?.type
      });
      return false;
    }
  }

  /**
   * Find legacy handler for channel
   * @param {string} channel - Channel name
   * @returns {Object|null} - Handler info or null
   */
  findLegacyHandler(channel) {
    // Check exact legacy matches
    const exactHandler = this.legacyRoutes.get(channel);
    if (exactHandler) {
      return { handlerName: exactHandler, match: null };
    }

    // Check pattern legacy matches
    for (const [pattern, handlerName] of this.legacyRoutes) {
      if (pattern instanceof RegExp) {
        const match = channel.match(pattern);
        if (match) {
          return { handlerName, match };
        }
      }
    }

    return null;
  }

  /**
   * Call handler method on context
   * @param {Object} context - Handler context
   * @param {string} handlerName - Handler method name
   * @param {string} channel - Channel name
   * @param {Object} message - Message payload
   * @param {Object} eventInfo - Extracted event info
   * @param {Array} match - Regex match results
   */
  async callHandler(context, handlerName, channel, message, eventInfo, match = null) {
    if (typeof context[handlerName] !== 'function') {
      logger.error('Handler method not found', {
        handlerName,
        channel,
        eventType: eventInfo.type
      });
      return;
    }

    try {
      await context[handlerName](channel, message, eventInfo, match);
    } catch (error) {
      logger.logError(error, {
        context: `Handler: ${handlerName}`,
        channel,
        eventType: eventInfo.type
      });
    }
  }

  /**
   * Get routing statistics
   * @returns {Object} - Routing statistics
   */
  getStats() {
    return {
      exactRoutes: this.exactRoutes.size,
      patternRoutes: this.patternRoutes.size,
      legacyRoutes: this.legacyRoutes.size,
      totalRoutes: this.exactRoutes.size + this.patternRoutes.size + this.legacyRoutes.size
    };
  }

  /**
   * Add custom route
   * @param {string|RegExp} pattern - Channel pattern
   * @param {string} handlerName - Handler method name
   * @param {boolean} isLegacy - Whether this is a legacy route
   */
  addRoute(pattern, handlerName, isLegacy = false) {
    if (isLegacy) {
      this.legacyRoutes.set(pattern, handlerName);
    } else if (typeof pattern === 'string') {
      this.exactRoutes.set(pattern, handlerName);
    } else {
      this.patternRoutes.set(pattern, handlerName);
    }
  }

  /**
   * Remove route
   * @param {string|RegExp} pattern - Channel pattern to remove
   */
  removeRoute(pattern) {
    this.exactRoutes.delete(pattern);
    this.patternRoutes.delete(pattern);
    this.legacyRoutes.delete(pattern);
  }

  /**
   * List all registered routes
   * @returns {Object} - All registered routes
   */
  listRoutes() {
    return {
      exact: Array.from(this.exactRoutes.entries()),
      pattern: Array.from(this.patternRoutes.entries()).map(([pattern, handler]) => [pattern.source, handler]),
      legacy: Array.from(this.legacyRoutes.entries()).map(([pattern, handler]) => [
        pattern instanceof RegExp ? pattern.source : pattern,
        handler
      ])
    };
  }

  /**
   * Check if channel has a registered route
   * @param {string} channel - Channel name
   * @returns {boolean} - True if route exists
   */
  hasRoute(channel) {
    // Check exact routes
    if (this.exactRoutes.has(channel)) {
      return true;
    }

    // Check pattern routes
    for (const pattern of this.patternRoutes.keys()) {
      if (pattern.test(channel)) {
        return true;
      }
    }

    // Check legacy routes
    if (this.legacyRoutes.has(channel)) {
      return true;
    }

    for (const pattern of this.legacyRoutes.keys()) {
      if (pattern instanceof RegExp && pattern.test(channel)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get suggested standard channel for legacy channel
   * @param {string} legacyChannel - Legacy channel name
   * @returns {string|null} - Suggested standard channel or null
   */
  getSuggestedStandardChannel(legacyChannel) {
    return ChannelUtils.convertLegacyChannel(legacyChannel);
  }
}

// Create singleton instance
const channelRouter = new ChannelRouter();

module.exports = channelRouter;
