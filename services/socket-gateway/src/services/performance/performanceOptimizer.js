/**
 * Advanced Performance Optimizer
 * 
 * Implements intelligent performance optimization strategies including
 * connection pooling, message batching, memory management, and load balancing.
 */

const EventEmitter = require('events');
const logger = require('../../utils/logger');

class PerformanceOptimizer extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.config = {
      // Connection Management
      maxConnections: options.maxConnections || 10000,
      connectionPoolSize: options.connectionPoolSize || 100,
      connectionTimeout: options.connectionTimeout || 30000,
      
      // Message Batching
      batchSize: options.batchSize || 50,
      batchTimeout: options.batchTimeout || 100, // ms
      maxBatchSize: options.maxBatchSize || 200,
      
      // Memory Management
      memoryThreshold: options.memoryThreshold || 0.8, // 80%
      gcInterval: options.gcInterval || 60000, // 1 minute
      cacheCleanupInterval: options.cacheCleanupInterval || 300000, // 5 minutes
      
      // Performance Monitoring
      metricsInterval: options.metricsInterval || 10000, // 10 seconds
      performanceLogInterval: options.performanceLogInterval || 60000, // 1 minute
      
      // Load Balancing
      loadBalancingEnabled: options.loadBalancingEnabled !== false,
      maxCpuUsage: options.maxCpuUsage || 0.8, // 80%
      maxMemoryUsage: options.maxMemoryUsage || 0.8, // 80%
    };
    
    // Performance State
    this.state = {
      connections: new Map(),
      messageBatches: new Map(),
      performanceMetrics: {
        connectionsPerSecond: 0,
        messagesPerSecond: 0,
        averageResponseTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        errorRate: 0,
      },
      isOptimizing: false,
      lastOptimization: null,
    };
    
    // Message Batching
    this.messageBatches = new Map();
    this.batchTimers = new Map();
    
    // Performance Monitoring
    this.performanceHistory = [];
    this.connectionHistory = [];
    
    this.initialize();
  }

  /**
   * Initialize performance optimizer
   */
  initialize() {
    logger.info('Initializing Performance Optimizer');
    
    // Start performance monitoring
    this.startPerformanceMonitoring();
    
    // Start memory management
    this.startMemoryManagement();
    
    // Start connection monitoring
    this.startConnectionMonitoring();
    
    logger.info('Performance Optimizer initialized', {
      config: this.config,
      features: [
        'Connection Pooling',
        'Message Batching',
        'Memory Management',
        'Performance Monitoring',
        'Load Balancing',
      ],
    });
  }

  /**
   * Optimize socket connection
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} connectionInfo - Connection information
   */
  optimizeConnection(socket, connectionInfo = {}) {
    try {
      const connectionId = socket.id;
      const startTime = Date.now();
      
      // Store connection info
      this.state.connections.set(connectionId, {
        socket,
        connectedAt: startTime,
        lastActivity: startTime,
        messageCount: 0,
        errorCount: 0,
        ...connectionInfo,
      });
      
      // Apply connection optimizations
      this.applyConnectionOptimizations(socket);
      
      // Set up connection monitoring
      this.setupConnectionMonitoring(socket);
      
      logger.debug('Connection optimized', {
        connectionId,
        totalConnections: this.state.connections.size,
        optimizationTime: Date.now() - startTime,
      });
      
      this.emit('connectionOptimized', { connectionId, socket });
      
    } catch (error) {
      logger.logError(error, {
        context: 'Optimize connection',
        socketId: socket.id,
      });
    }
  }

  /**
   * Apply connection-level optimizations
   * @param {Socket} socket - Socket.io socket instance
   */
  applyConnectionOptimizations(socket) {
    // Set socket timeout
    socket.timeout = this.config.connectionTimeout;
    
    // Enable compression for large messages
    socket.compress(true);
    
    // Set up efficient event handling
    socket.setMaxListeners(50); // Increase listener limit
    
    // Apply rate limiting per connection
    this.applyConnectionRateLimit(socket);
  }

  /**
   * Apply rate limiting to connection
   * @param {Socket} socket - Socket.io socket instance
   */
  applyConnectionRateLimit(socket) {
    const connectionId = socket.id;
    const rateLimitWindow = 60000; // 1 minute
    const maxRequestsPerWindow = 100;
    
    socket._rateLimitData = {
      requests: [],
      windowStart: Date.now(),
    };
    
    const originalEmit = socket.emit.bind(socket);
    socket.emit = (...args) => {
      const now = Date.now();
      const rateLimitData = socket._rateLimitData;
      
      // Clean old requests
      rateLimitData.requests = rateLimitData.requests.filter(
        timestamp => now - timestamp < rateLimitWindow
      );
      
      // Check rate limit
      if (rateLimitData.requests.length >= maxRequestsPerWindow) {
        logger.warn('Rate limit exceeded for connection', {
          connectionId,
          requestCount: rateLimitData.requests.length,
        });
        return false;
      }
      
      rateLimitData.requests.push(now);
      return originalEmit(...args);
    };
  }

  /**
   * Batch messages for efficient processing
   * @param {string} channel - Channel name
   * @param {Object} message - Message to batch
   * @param {Object} options - Batching options
   */
  batchMessage(channel, message, options = {}) {
    try {
      const batchKey = options.batchKey || channel;
      const priority = options.priority || 'normal';
      
      // Initialize batch if not exists
      if (!this.messageBatches.has(batchKey)) {
        this.messageBatches.set(batchKey, {
          messages: [],
          priority,
          createdAt: Date.now(),
          channel,
        });
      }
      
      const batch = this.messageBatches.get(batchKey);
      batch.messages.push({
        message,
        timestamp: Date.now(),
        ...options,
      });
      
      // Check if batch should be processed
      if (this.shouldProcessBatch(batch)) {
        this.processBatch(batchKey, batch);
      } else {
        // Set timer for batch processing
        this.scheduleBatchProcessing(batchKey);
      }
      
    } catch (error) {
      logger.logError(error, {
        context: 'Batch message',
        channel,
        batchKey: options.batchKey,
      });
    }
  }

  /**
   * Check if batch should be processed immediately
   * @param {Object} batch - Message batch
   * @returns {boolean} Should process batch
   */
  shouldProcessBatch(batch) {
    const now = Date.now();
    const batchAge = now - batch.createdAt;
    
    return (
      batch.messages.length >= this.config.batchSize ||
      batch.messages.length >= this.config.maxBatchSize ||
      batchAge >= this.config.batchTimeout ||
      batch.priority === 'high'
    );
  }

  /**
   * Schedule batch processing
   * @param {string} batchKey - Batch key
   */
  scheduleBatchProcessing(batchKey) {
    if (this.batchTimers.has(batchKey)) {
      return; // Timer already set
    }
    
    const timer = setTimeout(() => {
      const batch = this.messageBatches.get(batchKey);
      if (batch && batch.messages.length > 0) {
        this.processBatch(batchKey, batch);
      }
      this.batchTimers.delete(batchKey);
    }, this.config.batchTimeout);
    
    this.batchTimers.set(batchKey, timer);
  }

  /**
   * Process message batch
   * @param {string} batchKey - Batch key
   * @param {Object} batch - Message batch
   */
  processBatch(batchKey, batch) {
    try {
      const startTime = Date.now();
      
      logger.debug('Processing message batch', {
        batchKey,
        messageCount: batch.messages.length,
        batchAge: startTime - batch.createdAt,
      });
      
      // Clear batch timer
      if (this.batchTimers.has(batchKey)) {
        clearTimeout(this.batchTimers.get(batchKey));
        this.batchTimers.delete(batchKey);
      }
      
      // Emit batch processed event
      this.emit('batchProcessed', {
        batchKey,
        messages: batch.messages,
        channel: batch.channel,
        processingTime: Date.now() - startTime,
      });
      
      // Clear batch
      this.messageBatches.delete(batchKey);
      
    } catch (error) {
      logger.logError(error, {
        context: 'Process batch',
        batchKey,
        messageCount: batch.messages.length,
      });
    }
  }

  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, this.config.metricsInterval);
    
    setInterval(() => {
      this.logPerformanceReport();
    }, this.config.performanceLogInterval);
  }

  /**
   * Collect performance metrics
   */
  collectPerformanceMetrics() {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      const metrics = {
        timestamp: Date.now(),
        connections: this.state.connections.size,
        memoryUsage: memUsage.heapUsed / memUsage.heapTotal,
        memoryHeapUsed: memUsage.heapUsed,
        memoryHeapTotal: memUsage.heapTotal,
        memoryExternal: memUsage.external,
        cpuUser: cpuUsage.user,
        cpuSystem: cpuUsage.system,
        activeBatches: this.messageBatches.size,
        pendingTimers: this.batchTimers.size,
      };
      
      this.state.performanceMetrics = {
        ...this.state.performanceMetrics,
        ...metrics,
      };
      
      this.performanceHistory.push(metrics);
      
      // Keep only last 100 entries
      if (this.performanceHistory.length > 100) {
        this.performanceHistory.shift();
      }
      
      // Check for performance issues
      this.checkPerformanceThresholds(metrics);
      
    } catch (error) {
      logger.logError(error, { context: 'Collect performance metrics' });
    }
  }

  /**
   * Check performance thresholds and trigger optimizations
   * @param {Object} metrics - Current performance metrics
   */
  checkPerformanceThresholds(metrics) {
    const issues = [];
    
    if (metrics.memoryUsage > this.config.memoryThreshold) {
      issues.push('high_memory_usage');
    }
    
    if (metrics.connections > this.config.maxConnections * 0.9) {
      issues.push('high_connection_count');
    }
    
    if (metrics.activeBatches > 100) {
      issues.push('high_batch_count');
    }
    
    if (issues.length > 0) {
      this.triggerOptimization(issues, metrics);
    }
  }

  /**
   * Trigger performance optimization
   * @param {Array} issues - Performance issues detected
   * @param {Object} metrics - Current metrics
   */
  triggerOptimization(issues, metrics) {
    if (this.state.isOptimizing) {
      return; // Already optimizing
    }
    
    this.state.isOptimizing = true;
    this.state.lastOptimization = Date.now();
    
    logger.warn('Performance optimization triggered', {
      issues,
      metrics: {
        memoryUsage: metrics.memoryUsage,
        connections: metrics.connections,
        activeBatches: metrics.activeBatches,
      },
    });
    
    // Apply optimizations based on issues
    this.applyOptimizations(issues);
    
    setTimeout(() => {
      this.state.isOptimizing = false;
    }, 5000); // Prevent rapid re-optimization
  }

  /**
   * Apply performance optimizations
   * @param {Array} issues - Issues to address
   */
  applyOptimizations(issues) {
    issues.forEach(issue => {
      switch (issue) {
        case 'high_memory_usage':
          this.optimizeMemoryUsage();
          break;
        case 'high_connection_count':
          this.optimizeConnections();
          break;
        case 'high_batch_count':
          this.optimizeBatching();
          break;
      }
    });
  }

  /**
   * Start memory management
   */
  startMemoryManagement() {
    setInterval(() => {
      this.performMemoryCleanup();
    }, this.config.cacheCleanupInterval);
    
    setInterval(() => {
      if (global.gc) {
        global.gc();
        logger.debug('Manual garbage collection triggered');
      }
    }, this.config.gcInterval);
  }

  /**
   * Start connection monitoring
   */
  startConnectionMonitoring() {
    setInterval(() => {
      this.monitorConnections();
    }, 30000); // Every 30 seconds
  }

  /**
   * Setup connection monitoring for individual socket
   * @param {Socket} socket - Socket.io socket instance
   */
  setupConnectionMonitoring(socket) {
    const connectionId = socket.id;
    
    socket.on('disconnect', () => {
      this.state.connections.delete(connectionId);
      logger.debug('Connection removed from optimizer', {
        connectionId,
        remainingConnections: this.state.connections.size,
      });
    });
    
    // Monitor socket events
    const originalEmit = socket.emit.bind(socket);
    socket.emit = (...args) => {
      const connection = this.state.connections.get(connectionId);
      if (connection) {
        connection.lastActivity = Date.now();
        connection.messageCount++;
      }
      return originalEmit(...args);
    };
  }

  /**
   * Get performance statistics
   * @returns {Object} Performance statistics
   */
  getPerformanceStats() {
    return {
      current: this.state.performanceMetrics,
      connections: this.state.connections.size,
      batches: {
        active: this.messageBatches.size,
        pendingTimers: this.batchTimers.size,
      },
      optimization: {
        isOptimizing: this.state.isOptimizing,
        lastOptimization: this.state.lastOptimization,
      },
      history: this.performanceHistory.slice(-10), // Last 10 entries
    };
  }

  /**
   * Log performance report
   */
  logPerformanceReport() {
    const stats = this.getPerformanceStats();
    
    logger.info('Performance Report', {
      connections: stats.connections,
      memoryUsage: `${(stats.current.memoryUsage * 100).toFixed(1)}%`,
      heapUsed: `${(stats.current.memoryHeapUsed / 1024 / 1024).toFixed(1)}MB`,
      activeBatches: stats.batches.active,
      isOptimizing: stats.optimization.isOptimizing,
    });
  }

  /**
   * Optimize memory usage
   */
  optimizeMemoryUsage() {
    logger.info('Optimizing memory usage');

    // Clear old performance history
    if (this.performanceHistory.length > 50) {
      this.performanceHistory.splice(0, this.performanceHistory.length - 50);
    }

    // Clear old connection history
    if (this.connectionHistory.length > 100) {
      this.connectionHistory.splice(0, this.connectionHistory.length - 100);
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    logger.info('Memory optimization completed');
  }

  /**
   * Optimize connections
   */
  optimizeConnections() {
    logger.info('Optimizing connections');

    const now = Date.now();
    const inactiveThreshold = 300000; // 5 minutes
    let removedCount = 0;

    // Remove inactive connections
    for (const [connectionId, connection] of this.state.connections) {
      if (now - connection.lastActivity > inactiveThreshold) {
        if (connection.socket && connection.socket.connected) {
          connection.socket.disconnect(true);
        }
        this.state.connections.delete(connectionId);
        removedCount++;
      }
    }

    logger.info('Connection optimization completed', {
      removedConnections: removedCount,
      activeConnections: this.state.connections.size,
    });
  }

  /**
   * Optimize message batching
   */
  optimizeBatching() {
    logger.info('Optimizing message batching');

    // Process all pending batches immediately
    const processedBatches = [];
    for (const [batchKey, batch] of this.messageBatches) {
      this.processBatch(batchKey, batch);
      processedBatches.push(batchKey);
    }

    logger.info('Batching optimization completed', {
      processedBatches: processedBatches.length,
    });
  }

  /**
   * Perform memory cleanup
   */
  performMemoryCleanup() {
    const memUsage = process.memoryUsage();
    const memoryUsagePercent = memUsage.heapUsed / memUsage.heapTotal;

    if (memoryUsagePercent > this.config.memoryThreshold) {
      logger.debug('Performing memory cleanup', {
        memoryUsage: `${(memoryUsagePercent * 100).toFixed(1)}%`,
      });

      this.optimizeMemoryUsage();
    }
  }

  /**
   * Monitor connections for issues
   */
  monitorConnections() {
    const now = Date.now();
    const issues = [];

    // Check for connections with high error rates
    for (const [connectionId, connection] of this.state.connections) {
      const errorRate = connection.errorCount / Math.max(connection.messageCount, 1);

      if (errorRate > 0.1) { // 10% error rate
        issues.push({
          type: 'high_error_rate',
          connectionId,
          errorRate,
        });
      }

      // Check for very old connections
      const connectionAge = now - connection.connectedAt;
      if (connectionAge > 3600000) { // 1 hour
        issues.push({
          type: 'old_connection',
          connectionId,
          age: connectionAge,
        });
      }
    }

    if (issues.length > 0) {
      logger.warn('Connection monitoring detected issues', {
        issueCount: issues.length,
        issues: issues.slice(0, 5), // Log first 5 issues
      });
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    logger.info('Cleaning up Performance Optimizer');

    // Clear all batch timers
    for (const timer of this.batchTimers.values()) {
      clearTimeout(timer);
    }
    this.batchTimers.clear();

    // Clear message batches
    this.messageBatches.clear();

    // Clear connections
    this.state.connections.clear();

    logger.info('Performance Optimizer cleanup completed');
  }
}

module.exports = PerformanceOptimizer;
