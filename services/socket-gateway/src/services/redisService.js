const Redis = require('redis');
const config = require('../config');
const logger = require('../utils/logger');

class RedisService {
  constructor() {
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.isConnected = false;
    this.subscriptions = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
  }

  async initialize() {
    try {
      // Create main client
      this.client = Redis.createClient({
        url: config.redis.url,
        ...config.redis.options,
      });

      // Create subscriber client
      this.subscriber = this.client.duplicate();

      // Create publisher client
      this.publisher = this.client.duplicate();

      // Set up error handlers
      this.setupErrorHandlers();

      // Connect all clients
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect(),
      ]);

      this.isConnected = true;
      this.reconnectAttempts = 0;

      logger.info('Redis service initialized successfully');

      // Subscribe to default channels
      await this.subscribeToDefaultChannels();
    } catch (error) {
      logger.logError(error, { context: 'Redis initialization' });
      throw error;
    }
  }

  setupErrorHandlers() {
    const handleError = (client, clientName) => {
      client.on('error', (error) => {
        logger.logError(error, { context: `Redis ${clientName} error` });
        this.isConnected = false;
      });

      client.on('connect', () => {
        logger.info(`Redis ${clientName} connected`);
      });

      client.on('ready', () => {
        logger.info(`Redis ${clientName} ready`);
        this.isConnected = true;
        this.reconnectAttempts = 0;
      });

      client.on('end', () => {
        logger.warn(`Redis ${clientName} connection ended`);
        this.isConnected = false;
      });

      client.on('reconnecting', () => {
        this.reconnectAttempts++;
        logger.info(`Redis ${clientName} reconnecting (attempt ${this.reconnectAttempts})`);

        if (this.reconnectAttempts > this.maxReconnectAttempts) {
          logger.error(`Redis ${clientName} max reconnection attempts exceeded`);
          client.disconnect();
        }
      });
    };

    handleError(this.client, 'main');
    handleError(this.subscriber, 'subscriber');
    handleError(this.publisher, 'publisher');
  }

  async subscribeToDefaultChannels() {
    // Standard channel patterns (new format)
    const standardChannels = [
      'room:*:events',        // Room events (standard format)
      'lobby:events',         // Lobby events
      'user:*:events',        // User events
      'global:events',        // Global events
      'service:*:requests',   // Service communication
      'monitoring:events',    // Monitoring events
    ];

    // Legacy channel patterns (for backward compatibility during migration)
    const legacyChannels = [
      'game:room:*',          // Legacy room events
      'game:lobby:updates',   // Legacy lobby events
      'game:global',          // Legacy global events
      'user:*:notifications', // Legacy user notifications
      'socket:events',        // Legacy socket events
      'admin:notifications',  // Admin notifications (keep for now)
      'admin:system:*',       // Admin system events (keep for now)
    ];

    // Subscribe to standard channels first
    for (const pattern of standardChannels) {
      await this.subscribe(pattern);
    }

    // Subscribe to legacy channels with deprecation warning
    for (const pattern of legacyChannels) {
      await this.subscribe(pattern);
      logger.warn('Subscribed to legacy channel pattern - consider migrating to standard format', {
        legacyPattern: pattern,
        suggestedStandard: this.getSuggestedStandardPattern(pattern)
      });
    }
  }

  /**
   * Get suggested standard pattern for legacy channel
   * @param {string} legacyPattern - Legacy channel pattern
   * @returns {string} - Suggested standard pattern
   */
  getSuggestedStandardPattern(legacyPattern) {
    const mappings = {
      'game:room:*': 'room:*:events',
      'game:lobby:updates': 'lobby:events',
      'game:global': 'global:events',
      'user:*:notifications': 'user:*:events',
      'socket:events': 'global:events'
    };

    return mappings[legacyPattern] || 'No standard equivalent';
  }

  async subscribe(pattern, handler = null) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      // Use pSubscribe for pattern matching
      if (pattern.includes('*')) {
        await this.subscriber.pSubscribe(pattern, (message, channel) => {
          this.handleMessage(message, channel, handler);
        });
      } else {
        await this.subscriber.subscribe(pattern, (message, channel) => {
          this.handleMessage(message, channel, handler);
        });
      }

      this.subscriptions.set(pattern, handler);
      logger.info(`Subscribed to Redis channel pattern: ${pattern}`);
    } catch (error) {
      logger.logError(error, { context: 'Redis subscription', pattern });
      throw error;
    }
  }

  async unsubscribe(pattern) {
    try {
      if (!this.isConnected) {
        return;
      }

      if (pattern.includes('*')) {
        await this.subscriber.pUnsubscribe(pattern);
      } else {
        await this.subscriber.unsubscribe(pattern);
      }

      this.subscriptions.delete(pattern);
      logger.info(`Unsubscribed from Redis channel pattern: ${pattern}`);
    } catch (error) {
      logger.logError(error, { context: 'Redis unsubscription', pattern });
    }
  }

  handleMessage(message, channel, customHandler = null) {
    try {
      const parsedMessage = JSON.parse(message);

      if (config.debug.redisLogs) {
        logger.debug('Redis message received', {
          channel,
          messageType: parsedMessage.event && parsedMessage.event.type,
          sequenceId: parsedMessage.event && parsedMessage.event.sequenceId,
        });
      }

      // Use custom handler if provided, otherwise emit to socket service
      if (customHandler) {
        customHandler(parsedMessage, channel);
      } else {
        // Emit to socket service (will be set up in socket service)
        this.emit('message', { message: parsedMessage, channel });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Redis message parsing',
        channel,
        rawMessage: message,
      });
    }
  }

  async publish(channel, message) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      const messageString = typeof message === 'string' ? message : JSON.stringify(message);
      await this.publisher.publish(channel, messageString);

      if (config.debug.redisLogs) {
        logger.debug('Redis message published', {
          channel,
          messageType: message.event && message.event.type,
        });
      }
    } catch (error) {
      logger.logError(error, { context: 'Redis publish', channel });
      throw error;
    }
  }

  async get(key) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      return await this.client.get(key);
    } catch (error) {
      logger.logError(error, { context: 'Redis get', key });
      throw error;
    }
  }

  async set(key, value, ttl = null) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      const valueString = typeof value === 'string' ? value : JSON.stringify(value);

      if (ttl) {
        await this.client.setEx(key, ttl, valueString);
      } else {
        await this.client.set(key, valueString);
      }
    } catch (error) {
      logger.logError(error, { context: 'Redis set', key });
      throw error;
    }
  }

  /**
   * Set a key-value pair with TTL (alias for set with TTL)
   * @param {string} key - Redis key
   * @param {number} ttl - Time to live in seconds
   * @param {*} value - Value to store
   */
  async setex(key, ttl, value) {
    return this.set(key, value, ttl);
  }

  async del(key) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      return await this.client.del(key);
    } catch (error) {
      logger.logError(error, { context: 'Redis delete', key });
      throw error;
    }
  }

  async ttl(key) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      return await this.client.ttl(key);
    } catch (error) {
      logger.logError(error, { context: 'Redis TTL', key });
      throw error;
    }
  }

  async expire(key, seconds) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      return await this.client.expire(key, seconds);
    } catch (error) {
      logger.logError(error, { context: 'Redis expire', key });
      throw error;
    }
  }

  async keys(pattern) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      return await this.client.keys(pattern);
    } catch (error) {
      logger.logError(error, { context: 'Redis keys', pattern });
      throw error;
    }
  }

  /**
   * Execute Redis transaction
   * @param {Function} transactionFn - Function that receives multi object
   * @returns {Array} Transaction results
   */
  async executeTransaction(transactionFn) {
    try {
      if (!this.isConnected) {
        throw new Error('Redis not connected');
      }

      const multi = this.client.multi();
      await transactionFn(multi);
      const results = await multi.exec();

      if (!results) {
        throw new Error('Transaction failed - results is null');
      }

      // Check for errors in transaction results
      for (let i = 0; i < results.length; i++) {
        const [error, result] = results[i];
        if (error) {
          throw new Error(`Transaction command ${i} failed: ${error.message}`);
        }
      }

      return results.map(([error, result]) => result);

    } catch (error) {
      logger.logError(error, { context: 'Redis transaction' });
      throw error;
    }
  }

  /**
   * Set multiple keys atomically
   * @param {Object} keyValuePairs - Object with key-value pairs
   * @param {number} ttl - Optional TTL in seconds
   * @returns {Array} Results
   */
  async setMultiple(keyValuePairs, ttl = null) {
    return this.executeTransaction((multi) => {
      for (const [key, value] of Object.entries(keyValuePairs)) {
        const valueString = typeof value === 'string' ? value : JSON.stringify(value);

        if (ttl) {
          multi.setEx(key, ttl, valueString);
        } else {
          multi.set(key, valueString);
        }
      }
    });
  }

  /**
   * Delete multiple keys atomically
   * @param {Array} keys - Array of keys to delete
   * @returns {Array} Results
   */
  async deleteMultiple(keys) {
    return this.executeTransaction((multi) => {
      for (const key of keys) {
        multi.del(key);
      }
    });
  }

  async healthCheck() {
    try {
      if (!this.isConnected) {
        return false;
      }

      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      logger.logError(error, { context: 'Redis health check' });
      return false;
    }
  }

  async disconnect() {
    try {
      logger.info('Starting enhanced Redis service disconnect...');

      // Disconnect clients with individual error handling
      const disconnectPromises = [];

      if (this.client) {
        disconnectPromises.push(
          this.client.disconnect().catch(error => {
            logger.warn('Redis main client disconnect warning:', error.message);
          })
        );
      }

      if (this.subscriber) {
        disconnectPromises.push(
          this.subscriber.disconnect().catch(error => {
            logger.warn('Redis subscriber client disconnect warning:', error.message);
          })
        );
      }

      if (this.publisher) {
        disconnectPromises.push(
          this.publisher.disconnect().catch(error => {
            logger.warn('Redis publisher client disconnect warning:', error.message);
          })
        );
      }

      // Wait for all disconnections to complete
      await Promise.allSettled(disconnectPromises);

      // Clear state
      this.isConnected = false;
      this.subscriptions.clear();

      logger.info('Enhanced Redis service disconnect completed');
    } catch (error) {
      logger.logError(error, { context: 'Enhanced Redis disconnect' });
      throw error; // Re-throw to allow caller to handle
    }
  }

  // Event emitter functionality for socket service integration
  emit(event, data) {
    if (this.eventHandlers && this.eventHandlers[event]) {
      this.eventHandlers[event](data);
    }
  }

  on(event, handler) {
    if (!this.eventHandlers) {
      this.eventHandlers = {};
    }
    this.eventHandlers[event] = handler;
  }

  /**
   * Publish standardized message
   * @param {string} channel - Channel to publish to
   * @param {Object} message - Standardized message object
   * @returns {Promise<number>} Number of subscribers that received the message
   */
  async publishStandardMessage(channel, message) {
    try {
      const { validateMessage } = require('../constants/eventStandards');

      // Validate message format
      const validation = validateMessage(message);
      if (!validation.valid) {
        throw new Error(`Invalid message format: ${validation.errors}`);
      }

      return await this.publish(channel, message);
    } catch (error) {
      logger.logError(error, {
        context: 'Publish standard message',
        channel,
        messageType: message?.event?.type,
      });
      throw error;
    }
  }

  /**
   * Subscribe to standardized events
   * @param {string} channel - Channel to subscribe to
   * @param {Function} handler - Message handler function
   * @param {Object} options - Subscription options
   */
  async subscribeStandard(channel, handler, options = {}) {
    try {
      const { validateMessage } = require('../constants/eventStandards');

      // Wrap handler with validation
      const validatingHandler = (message, receivedChannel) => {
        try {
          // Validate message format
          const validation = validateMessage(message);
          if (!validation.valid) {
            logger.warn('Received invalid message format', {
              channel: receivedChannel,
              errors: validation.errors,
            });
            return;
          }

          // Call original handler
          return handler(message, receivedChannel);
        } catch (error) {
          logger.logError(error, {
            context: 'Standard message handler',
            channel: receivedChannel,
            messageType: message?.event?.type,
          });
        }
      };

      return await this.subscribe(channel, validatingHandler);
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe standard',
        channel,
      });
      throw error;
    }
  }
}

module.exports = new RedisService();
