const logger = require('../utils/logger');
const roomStateManager = require('./roomStateManager');
const subscriptionService = require('./subscriptionService');

// Import the new orchestrator
const SocketServiceOrchestrator = require('./socket/socketServiceOrchestrator');

/**
 * Socket Service
 *
 * Main socket service class that delegates most functionality to the orchestrator.
 * Maintains backward compatibility while providing a clean, modular architecture.
 */
class SocketService {
  constructor() {
    // Initialize the orchestrator which handles all the modular components
    this.orchestrator = new SocketServiceOrchestrator();

    // Expose io for backward compatibility
    this.io = null; // Will be set after initialization
    this.server = null;

    // Legacy properties for backward compatibility
    this.userRooms = roomStateManager.userRooms;
    this.roomUsers = roomStateManager.roomUsers;

    // Delegate component access to orchestrator for backward compatibility
    this.connectionManager = this.orchestrator.connectionManager;
    this.eventHandlers = this.orchestrator.eventHandlers;
    this.redisMessageHandler = this.orchestrator.redisMessageHandler;
    this.metricsCollector = this.orchestrator.metricsCollector;
    this.lobbyHandlers = this.orchestrator.lobbyHandlers;
    this.enhancedLobbyHandlers = this.orchestrator.enhancedLobbyHandlers;
    this.roomHandlers = this.orchestrator.roomHandlers;
    this.gameHandlers = this.orchestrator.gameHandlers;

    // New modular components
    this.eventChannelRouter = this.orchestrator.eventChannelRouter;
    this.gameServiceCommunicator = this.orchestrator.gameServiceCommunicator;
    this.roomCacheManager = this.orchestrator.roomCacheManager;
    this.userBalanceManager = this.orchestrator.userBalanceManager;
    this.eventProcessor = this.orchestrator.eventProcessor;

    // Legacy properties for backward compatibility (delegated to room cache manager)
    this.recentRoomInfoUpdates = this.roomCacheManager.recentRoomInfoUpdates;
    this.roomInfoDeduplicationTTL = this.roomCacheManager.roomInfoDeduplicationTTL;
    this.recentRoomInfo = this.roomCacheManager.recentRoomInfo;

    // Legacy metrics for backward compatibility
    this.connectionCount = 0;
    this.connectedUsers = new Map();
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      errorCount: 0,
    };
  }

  /**
   * Initialize the socket service
   * @param {Object} httpServer - HTTP server instance
   */
  async initialize(httpServer) {
    try {
      // Initialize the orchestrator
      await this.orchestrator.initialize(httpServer);

      // Expose io for backward compatibility
      this.io = this.orchestrator.io;
      this.server = httpServer;

      logger.info('Socket service initialized successfully');
    } catch (error) {
      logger.logError(error, { context: 'Socket service initialization' });
      throw error;
    }
  }

  /**
   * Cleanup resources on shutdown
   */
  async cleanup() {
    await this.orchestrator.cleanup();
  }

  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    return this.orchestrator.getServiceStats();
  }

  /**
   * Get metrics for health checks
   * @returns {Object} Service metrics
   */
  getMetrics() {
    try {
      const stats = this.getServiceStats();
      return {
        activeConnections: stats.activeConnections || 0,
        activeRooms: stats.activeRooms || 0,
        connectedUsers: stats.connectedUsers || 0,
        totalConnections: this.metrics.totalConnections || 0,
        errorCount: this.metrics.errorCount || 0,
      };
    } catch (error) {
      logger.logError(error, { context: 'Get metrics' });
      return {
        activeConnections: 0,
        activeRooms: 0,
        connectedUsers: 0,
        totalConnections: 0,
        errorCount: 0,
      };
    }
  }

  // ===== DELEGATED METHODS FOR BACKWARD COMPATIBILITY =====

  /**
   * Handle Redis messages (delegated to orchestrator)
   * @param {Object} message - Redis message
   * @param {string} channel - Redis channel
   */
  async handleRedisMessage(message, channel) {
    return this.orchestrator.handleRedisMessage(message, channel);
  }

  /**
   * Fetch user balance (delegated to user balance manager)
   * @param {string} userId - User ID
   * @param {string} token - User's JWT token
   * @returns {Promise<number>} User balance
   */
  async fetchUserBalance(userId, token) {
    return this.userBalanceManager.fetchUserBalance(userId, token);
  }

  /**
   * Get current room list (delegated to room cache manager)
   * @param {boolean} forceRefresh - Force refresh from source
   * @returns {Promise<Array>} Array of room objects
   */
  async fetchCurrentRoomList(forceRefresh = false) {
    return this.roomCacheManager.getCurrentRoomList(forceRefresh);
  }

  /**
   * Update room in cache (delegated to room cache manager)
   * @param {Object} room - Room object
   * @param {string} action - Action type
   */
  async updateRoomInCache(room, action) {
    return this.roomCacheManager.updateRoomInCache(room, action);
  }

  /**
   * Clean up old cached room info (delegated to room cache manager)
   */
  cleanupOldRoomInfoCache() {
    return this.roomCacheManager.cleanupOldRoomInfoCache();
  }

  /**
   * Get socket room name (utility method)
   * @param {string} roomId - Room ID
   * @returns {string} Socket room name
   */
  getSocketRoomName(roomId) {
    return `room:${roomId}`;
  }

  // ===== EVENT HANDLERS (DELEGATED TO ORCHESTRATOR) =====

  /**
   * Handle room events (delegated to orchestrator)
   */
  async handleRoomEvent(channel, message, eventInfo, match) {
    return this.orchestrator.handleRoomEvent(channel, message, eventInfo, match);
  }

  /**
   * Handle lobby events (delegated to orchestrator)
   */
  async handleLobbyEvent(channel, message, eventInfo) {
    return this.orchestrator.handleLobbyEvent(channel, message, eventInfo);
  }

  /**
   * Handle user events (delegated to orchestrator)
   */
  async handleUserEvent(channel, message, eventInfo, match) {
    return this.orchestrator.handleUserEvent(channel, message, eventInfo, match);
  }

  /**
   * Handle global events (delegated to orchestrator)
   */
  async handleGlobalEvent(channel, message, eventInfo) {
    return this.orchestrator.handleGlobalEvent(channel, message, eventInfo);
  }

  /**
   * Handle service requests (delegated to orchestrator)
   */
  async handleServiceRequest(channel, message, eventInfo, match) {
    return this.orchestrator.handleServiceRequest(channel, message, eventInfo, match);
  }

  /**
   * Handle monitoring events (delegated to orchestrator)
   */
  async handleMonitoringEvent(channel, message, eventInfo) {
    return this.orchestrator.handleMonitoringEvent(channel, message, eventInfo);
  }

  // ===== LEGACY METHODS FOR BACKWARD COMPATIBILITY =====
  // These methods delegate to the appropriate modular components

  /**
   * Handle connection (delegated to connection manager)
   */
  async handleConnection(socket) {
    return this.connectionManager.handleConnection(
      socket,
      (socket) => this.eventHandlers.setupEventHandlers(socket),
      (userId, token) => this.userBalanceManager.fetchUserBalance(userId, token)
    );
  }

  /**
   * Handle disconnection (delegated to connection manager)
   */
  handleDisconnection(socket, reason) {
    return this.connectionManager.handleDisconnection(socket, reason);
  }

  /**
   * Handle socket error (delegated to connection manager)
   */
  handleSocketError(socket, error) {
    return this.connectionManager.handleSocketError(socket, error);
  }

  /**
   * Setup socket event handlers (delegated to event handlers)
   */
  setupSocketEventHandlers(socket) {
    return this.eventHandlers.setupEventHandlers(socket);
  }

  /**
   * Handle subscribe lobby (delegated to lobby handlers)
   */
  handleSubscribeLobby(socket, callback) {
    return this.lobbyHandlers.handleSubscribeLobby(socket, callback);
  }

  /**
   * Handle unsubscribe lobby (delegated to lobby handlers)
   */
  handleUnsubscribeLobby(socket, callback) {
    return this.lobbyHandlers.handleUnsubscribeLobby(socket, callback);
  }

  /**
   * Handle join room (delegated to room handlers)
   */
  handleJoinRoom(socket, data, callback) {
    return this.roomHandlers.handleJoinRoom(socket, data, callback);
  }

  /**
   * Handle leave room (delegated to room handlers)
   */
  handleLeaveRoom(socket, data, callback) {
    return this.roomHandlers.handleLeaveRoom(socket, data, callback);
  }

  /**
   * Handle subscribe room (delegated to room handlers)
   */
  handleSubscribeRoom(socket, data, callback) {
    return this.roomHandlers.handleSubscribeRoom(socket, data, callback);
  }

  /**
   * Handle unsubscribe room (delegated to room handlers)
   */
  handleUnsubscribeRoom(socket, data, callback) {
    return this.roomHandlers.handleUnsubscribeRoom(socket, data, callback);
  }

  /**
   * Handle player ready (delegated to game handlers)
   */
  handlePlayerReady(socket, data, callback) {
    return this.gameHandlers.handlePlayerReady(socket, data, callback);
  }

  /**
   * Handle player unready (delegated to game handlers)
   */
  handlePlayerUnready(socket, data, callback) {
    return this.gameHandlers.handlePlayerUnready(socket, data, callback);
  }

  /**
   * Handle select wheel color (delegated to game handlers)
   */
  handleSelectWheelColor(socket, data, callback) {
    return this.gameHandlers.handleSelectWheelColor(socket, data, callback);
  }

  /**
   * Handle select color spec (delegated to game handlers)
   */
  handleSelectColorSpec(socket, data, callback) {
    return this.gameHandlers.handleSelectColorSpec(socket, data, callback);
  }

  /**
   * Handle select position spec (delegated to game handlers)
   */
  handleSelectPositionSpec(socket, data, callback) {
    return this.gameHandlers.handleSelectPositionSpec(socket, data, callback);
  }

  /**
   * Handle player ready spec (delegated to game handlers)
   */
  handlePlayerReadySpec(socket, data, callback) {
    return this.gameHandlers.handlePlayerReadySpec(socket, data, callback);
  }

  /**
   * Handle player unready spec (delegated to game handlers)
   */
  handlePlayerUnreadySpec(socket, data, callback) {
    return this.gameHandlers.handlePlayerUnreadySpec(socket, data, callback);
  }

  /**
   * Handle ping (delegated to connection manager)
   */
  handlePing(socket, callback) {
    return this.connectionManager.handlePing(socket, callback);
  }

  /**
   * Handle generic message (delegated to event handlers)
   */
  handleGenericMessage(socket, data) {
    return this.eventHandlers.handleGenericMessage(socket, data);
  }

  /**
   * Shutdown the socket service gracefully
   */
  async shutdown() {
    try {
      logger.info('Shutting down socket service...');

      // Clean up connection manager
      if (this.connectionManager && typeof this.connectionManager.cleanup === 'function') {
        this.connectionManager.cleanup();
      }

      // Clean up orchestrator
      if (this.orchestrator && typeof this.orchestrator.cleanup === 'function') {
        await this.orchestrator.cleanup();
      }

      logger.info('Socket service shutdown completed');
    } catch (error) {
      logger.logError(error, { context: 'Socket service shutdown' });
    }
  }
}

module.exports = new SocketService();
