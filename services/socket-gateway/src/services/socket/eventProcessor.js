/**
 * Event Processor
 * 
 * Processes specific event types and handles the business logic for each event.
 * Coordinates between different managers and services to handle events properly.
 */

const logger = require('../../utils/logger');
const { EVENT_TYPES } = require('../../constants/events');

class EventProcessor {
  constructor() {
    this.socketService = null; // Will be injected
    this.roomCacheManager = null; // Will be injected
    this.userBalanceManager = null; // Will be injected
  }

  /**
   * Set dependencies
   * @param {Object} dependencies - Required dependencies
   */
  setDependencies(dependencies) {
    this.socketService = dependencies.socketService;
    this.roomCacheManager = dependencies.roomCacheManager;
    this.userBalanceManager = dependencies.userBalanceManager;
  }

  /**
   * Process room events
   * @param {string} roomId - Room ID
   * @param {string} eventType - Event type
   * @param {Object} payload - Event payload
   */
  async processRoomEvent(roomId, eventType, payload) {
    try {
      switch (eventType) {
        case EVENT_TYPES.ROOM.PLAYER_JOINED:
          await this.handlePlayerJoined(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.PLAYER_LEFT:
          await this.handlePlayerLeft(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.PLAYER_READY_CHANGED:
          await this.handlePlayerReadyChanged(payload);
          break;
        case EVENT_TYPES.ROOM.INFO_UPDATED:
          await this.handleRoomInfoUpdated(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.GAME_STARTED:
          await this.handleGameStarted(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.GAME_FINISHED:
          await this.handleGameFinished(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_SELECTED:
          await this.handleColorSelected(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_UNSELECTED:
          await this.handleColorUnselected(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_STATE_SYNC:
          await this.handleColorStateSync(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_AVAILABILITY_UPDATED:
          await this.handleColorAvailabilityUpdated(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_STARTED:
          await this.handleCountdownStarted(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_STOPPED:
          await this.handleCountdownStopped(roomId, payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_UPDATED:
          await this.handleCountdownUpdated(roomId, payload);
          break;
        default:
          logger.debug('Unhandled room event type', {
            roomId,
            eventType,
            payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Process room event',
        roomId,
        eventType,
      });
    }
  }

  /**
   * Process lobby events
   * @param {string} eventType - Event type
   * @param {Object} payload - Event payload
   */
  async processLobbyEvent(eventType, payload) {
    try {
      switch (eventType) {
        case EVENT_TYPES.LOBBY.ROOM_CREATED:
          await this.handleRoomCreated(payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_DELETED:
          await this.handleRoomDeleted(payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_LIST_UPDATED:
          await this.handleRoomListUpdated(payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_STATUS_CHANGED:
          await this.handleRoomStatusChanged(payload);
          break;
        default:
          logger.debug('Unhandled lobby event type', {
            eventType,
            payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Process lobby event',
        eventType,
      });
    }
  }

  /**
   * Process user events
   * @param {string} userId - User ID
   * @param {string} eventType - Event type
   * @param {Object} payload - Event payload
   */
  async processUserEvent(userId, eventType, payload) {
    try {
      switch (eventType) {
        case EVENT_TYPES.USER.BALANCE_UPDATED:
          await this.handleBalanceUpdated(userId, payload);
          break;
        case EVENT_TYPES.USER.TRANSACTION_COMPLETED:
          await this.handleTransactionCompleted(userId, payload);
          break;
        case EVENT_TYPES.USER.NOTIFICATION_RECEIVED:
          await this.handleNotificationReceived(userId, payload);
          break;
        default:
          logger.debug('Unhandled user event type', {
            userId,
            eventType,
            payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Process user event',
        userId,
        eventType,
      });
    }
  }

  // ===== ROOM EVENT HANDLERS =====

  async handlePlayerJoined(roomId, payload) {
    logger.debug('Processing player joined event', { roomId, payload });
    
    // Update room cache if room info is provided
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'player_joined');
    }
    
    // Broadcast to room subscribers
    this.socketService.io.to(`room:${roomId}`).emit('player_joined', payload);
  }

  async handlePlayerLeft(roomId, payload) {
    logger.debug('Processing player left event', { roomId, payload });
    
    // Update room cache if room info is provided
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'player_left');
    }
    
    // Broadcast to room subscribers
    this.socketService.io.to(`room:${roomId}`).emit('player_left', payload);
  }

  async handlePlayerReadyChanged(payload) {
    logger.debug('Processing player ready changed event', { payload });
    
    // Broadcast to room if roomId is provided
    if (payload.roomId) {
      this.socketService.io.to(`room:${payload.roomId}`).emit('player_ready_changed', payload);
    }
  }

  async handleRoomInfoUpdated(roomId, payload) {
    // Check for deduplication
    if (this.roomCacheManager.shouldDeduplicateRoomInfo(roomId, payload)) {
      logger.debug('Deduplicated room info update', { roomId });
      return;
    }

    logger.debug('Processing room info updated event', { roomId, reason: payload.reason });

    // Update room cache - handle unified structure from services
    const roomData = payload.roomInfo?.room || payload.room || payload.data;
    if (roomData) {
      await this.roomCacheManager.updateRoomInCache(roomData, 'updated');
    }

    // Broadcast to room subscribers - services provide unified structure
    this.socketService.io.to(`room:${roomId}`).emit('room_info_updated', payload);
  }

  async handleGameStarted(roomId, payload) {
    logger.debug('Processing game started event', { roomId, payload });
    
    // Update room cache
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'started');
    }
    
    // Broadcast to room subscribers
    this.socketService.io.to(`room:${roomId}`).emit('game_started', payload);
  }

  async handleGameFinished(roomId, payload) {
    logger.debug('Processing game finished event', { roomId, payload });
    
    // Update room cache
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'finished');
    }
    
    // Broadcast to room subscribers
    this.socketService.io.to(`room:${roomId}`).emit('game_finished', payload);
  }

  async handleColorSelected(roomId, payload) {
    logger.debug('Processing color selected event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('color_selected', payload);
  }

  async handleColorUnselected(roomId, payload) {
    logger.debug('Processing color unselected event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('color_unselected', payload);
  }

  async handleColorStateSync(roomId, payload) {
    logger.debug('Processing color state sync event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('color_state_sync', payload);
  }

  async handleColorAvailabilityUpdated(roomId, payload) {
    logger.debug('Processing color availability updated event', {
      roomId,
      availableCount: payload.colors?.available?.length || 0,
      selectedCount: payload.colors?.selected?.length || 0
    });
    this.socketService.io.to(`room:${roomId}`).emit('color_availability_updated', payload);
  }

  async handleAvailableColors(roomId, payload) {
    logger.debug('Processing available colors event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('available_colors', payload);
  }

  async handleColorSelectionUpdate(roomId, payload) {
    logger.debug('Processing color selection update event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('color_selection_update', payload);
  }

  async handleColorStateUpdated(roomId, payload) {
    logger.debug('Processing color state updated event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('color_state_updated', payload);
  }

  async handleCountdownStarted(roomId, payload) {
    logger.debug('Processing countdown started event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('countdown_started', payload);
  }

  async handleCountdownStopped(roomId, payload) {
    logger.debug('Processing countdown stopped event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('countdown_stopped', payload);
  }

  async handleCountdownUpdated(roomId, payload) {
    logger.debug('Processing countdown updated event', { roomId, payload });
    this.socketService.io.to(`room:${roomId}`).emit('countdown_updated', payload);
  }

  // ===== LOBBY EVENT HANDLERS =====

  async handleRoomCreated(payload) {
    logger.debug('Processing room created event', { payload });
    
    // Update room cache
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'created');
    }
    
    // Broadcast to lobby subscribers
    this.socketService.io.to('lobby').emit('room_created', payload);
  }

  async handleRoomDeleted(payload) {
    logger.debug('Processing room deleted event', { payload });
    
    // Update room cache
    if (payload.room || payload.roomId) {
      const room = payload.room || { id: payload.roomId };
      await this.roomCacheManager.updateRoomInCache(room, 'deleted');
    }
    
    // Broadcast to lobby subscribers
    this.socketService.io.to('lobby').emit('room_deleted', payload);
  }

  async handleRoomListUpdated(payload) {
    logger.debug('Processing room list updated event', { payload });
    
    // Update room list cache
    if (payload.rooms && Array.isArray(payload.rooms)) {
      this.roomCacheManager.roomListCache = payload.rooms;
      this.roomCacheManager.lastRoomListUpdate = Date.now();
      await this.roomCacheManager.updateRoomListInRedis(payload.rooms);
    }
    
    // Broadcast to lobby subscribers
    this.socketService.io.to('lobby').emit('room_list_updated', payload);
  }

  async handleRoomStatusChanged(payload) {
    logger.debug('Processing room status changed event', { payload });
    
    // Update room cache
    if (payload.room) {
      await this.roomCacheManager.updateRoomInCache(payload.room, 'status_changed');
    }
    
    // Broadcast to lobby subscribers
    this.socketService.io.to('lobby').emit('room_status_changed', payload);
  }

  // ===== USER EVENT HANDLERS =====

  async handleBalanceUpdated(userId, payload) {
    logger.debug('Processing balance updated event', { userId, payload });
    
    // Update balance cache
    if (this.userBalanceManager && typeof payload.newBalance === 'number') {
      this.userBalanceManager.handleBalanceUpdate(userId, payload);
    }
    
    // Send to specific user
    this.socketService.io.to(userId).emit('balance_updated', payload);
  }

  async handleTransactionCompleted(userId, payload) {
    logger.debug('Processing transaction completed event', { userId, payload });
    
    // Update balance if provided
    if (this.userBalanceManager && typeof payload.newBalance === 'number') {
      this.userBalanceManager.updateCachedBalance(userId, payload.newBalance);
    }
    
    // Send to specific user
    this.socketService.io.to(userId).emit('transaction_completed', payload);
  }

  async handleNotificationReceived(userId, payload) {
    logger.debug('Processing notification received event', { userId, payload });
    
    // Send to specific user
    this.socketService.io.to(userId).emit('notification_received', payload);
  }
}

module.exports = EventProcessor;
