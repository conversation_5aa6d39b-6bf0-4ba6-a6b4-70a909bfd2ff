/**
 * Redis Message Handler
 * 
 * Handles Redis pub/sub messages and routes them to appropriate handlers.
 * Provides centralized message processing and error handling.
 */

const logger = require('../../utils/logger');
const channelRouter = require('../channelRouter');
const { CHANNELS, EVENT_TYPES, ChannelUtils } = require('../../constants/channels');

class RedisMessageHandler {
  constructor() {
    this.socketService = null; // Will be injected
    this.messageCount = 0;
    this.errorCount = 0;
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  /**
   * Handle incoming Redis message
   * @param {Object} message - Redis message
   * @param {string} channel - Redis channel
   */
  async handleRedisMessage(message, channel) {
    try {
      this.messageCount++;

      // Log incoming message with improved formatting
      logger.debug('Redis message received', {
        channel,
        messageType: message.event?.type,
        isStandardChannel: ChannelUtils.isStandardChannel(channel),
        isLegacyChannel: ChannelUtils.isLegacyChannel(channel),
        messageId: this.messageCount,
      });

      // Route message using standardized channel router
      const handled = await channelRouter.routeMessage(channel, message, this.socketService);

      if (!handled) {
        logger.warn('Unhandled Redis message', {
          channel,
          messageType: message.event?.type,
          suggestedChannel: ChannelUtils.convertLegacyChannel(channel),
          messageId: this.messageCount,
        });
      }

    } catch (error) {
      this.errorCount++;
      logger.logError(error, {
        context: 'Redis message handling',
        channel,
        messageType: message.event?.type,
        messageId: this.messageCount,
      });
    }
  }

  /**
   * Handle room events from Redis
   * @param {string} roomId - Room ID
   * @param {Object} eventInfo - Event information
   */
  async handleRoomEvent(roomId, eventInfo) {
    try {
      logger.debug('Processing room event', {
        roomId,
        eventType: eventInfo.type,
      });

      switch (eventInfo.type) {
        case EVENT_TYPES.ROOM.PLAYER_JOINED:
          await this.handlePlayerJoinedEvent(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.PLAYER_LEFT:
          await this.handlePlayerLeftEvent(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.PLAYER_READY_CHANGED:
          await this.handlePlayerReadyChangedEvent(eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.ROOM_INFO_UPDATED:
          await this.handleRoomInfoUpdated(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.GAME_STARTED:
          await this.handleGameStarted(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.GAME_FINISHED:
          await this.handleGameFinished(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_SELECTED:
          await this.handleColorSelected(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_UNSELECTED:
          await this.handleColorUnselected(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COLOR_STATE_SYNC:
          await this.handleColorStateSync(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_STARTED:
          await this.handleGameStarting(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_STOPPED:
          await this.handleCountdownStopped(roomId, eventInfo.payload);
          break;
        case EVENT_TYPES.ROOM.COUNTDOWN_UPDATED:
          await this.handleCountdownUpdate(roomId, eventInfo.payload);
          break;
        default:
          logger.debug('Unhandled room event type', {
            roomId,
            eventType: eventInfo.type,
            payload: eventInfo.payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Room event handling',
        roomId,
        eventType: eventInfo.type,
      });
    }
  }

  /**
   * Handle lobby events from Redis
   * @param {Object} eventInfo - Event information
   */
  async handleLobbyEvent(eventInfo) {
    try {
      logger.debug('Processing lobby event', {
        eventType: eventInfo.type,
      });

      switch (eventInfo.type) {
        case EVENT_TYPES.LOBBY.ROOM_CREATED:
          await this.handleRoomCreatedEvent(eventInfo.payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_DELETED:
          await this.handleRoomDeletedEvent(eventInfo.payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_LIST_UPDATED:
          await this.handleRoomListUpdatedEvent(eventInfo.payload);
          break;
        case EVENT_TYPES.LOBBY.ROOM_STATUS_CHANGED:
          await this.handleRoomStatusChangedEvent(eventInfo.payload);
          break;
        default:
          logger.debug('Unhandled lobby event type', {
            eventType: eventInfo.type,
            payload: eventInfo.payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Lobby event handling',
        eventType: eventInfo.type,
      });
    }
  }

  /**
   * Handle user events from Redis
   * @param {string} userId - User ID
   * @param {Object} eventInfo - Event information
   */
  async handleUserEvent(userId, eventInfo) {
    try {
      logger.debug('Processing user event', {
        userId,
        eventType: eventInfo.type,
      });

      switch (eventInfo.type) {
        case EVENT_TYPES.USER.BALANCE_UPDATED:
          await this.handleBalanceUpdatedEvent(userId, eventInfo.payload);
          break;
        case EVENT_TYPES.USER.TRANSACTION_COMPLETED:
          await this.handleTransactionCompletedEvent(userId, eventInfo.payload);
          break;
        case EVENT_TYPES.USER.NOTIFICATION_RECEIVED:
          await this.handleNotificationReceivedEvent(userId, eventInfo.payload);
          break;
        default:
          logger.debug('Unhandled user event type', {
            userId,
            eventType: eventInfo.type,
            payload: eventInfo.payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'User event handling',
        userId,
        eventType: eventInfo.type,
      });
    }
  }

  /**
   * Delegate to socket service methods (these will be moved to specialized handlers)
   */
  async handlePlayerJoinedEvent(roomId, payload) {
    if (this.socketService && this.socketService.handlePlayerJoinedEvent) {
      return this.socketService.handlePlayerJoinedEvent(roomId, payload);
    }
  }

  async handlePlayerLeftEvent(roomId, payload) {
    if (this.socketService && this.socketService.handlePlayerLeftEvent) {
      return this.socketService.handlePlayerLeftEvent(roomId, payload);
    }
  }

  async handlePlayerReadyChangedEvent(payload) {
    if (this.socketService && this.socketService.handlePlayerReadyChangedEvent) {
      return this.socketService.handlePlayerReadyChangedEvent(payload);
    }
  }

  async handleRoomInfoUpdated(roomId, payload) {
    if (this.socketService && this.socketService.handleRoomInfoUpdated) {
      return this.socketService.handleRoomInfoUpdated(roomId, payload);
    }
  }

  async handleGameStarted(roomId, payload) {
    if (this.socketService && this.socketService.handleGameStarted) {
      return this.socketService.handleGameStarted(roomId, payload);
    }
  }

  async handleGameFinished(roomId, payload) {
    if (this.socketService && this.socketService.handleGameFinished) {
      return this.socketService.handleGameFinished(roomId, payload);
    }
  }

  async handleColorSelected(roomId, payload) {
    if (this.socketService && this.socketService.handleColorSelected) {
      return this.socketService.handleColorSelected(roomId, payload);
    }
  }

  async handleColorUnselected(roomId, payload) {
    if (this.socketService && this.socketService.handleColorUnselected) {
      return this.socketService.handleColorUnselected(roomId, payload);
    }
  }

  async handleColorStateSync(roomId, payload) {
    if (this.socketService && this.socketService.handleColorStateSync) {
      return this.socketService.handleColorStateSync(roomId, payload);
    }
  }

  async handleGameStarting(roomId, payload) {
    if (this.socketService && this.socketService.handleGameStarting) {
      return this.socketService.handleGameStarting(roomId, payload);
    }
  }

  async handleCountdownStopped(roomId, payload) {
    if (this.socketService && this.socketService.handleCountdownStopped) {
      return this.socketService.handleCountdownStopped(roomId, payload);
    }
  }

  async handleCountdownUpdate(roomId, payload) {
    if (this.socketService && this.socketService.handleCountdownUpdate) {
      return this.socketService.handleCountdownUpdate(roomId, payload);
    }
  }

  // Lobby event delegates
  async handleRoomCreatedEvent(payload) {
    if (this.socketService && this.socketService.handleRoomCreatedEvent) {
      return this.socketService.handleRoomCreatedEvent(payload);
    }
  }

  async handleRoomDeletedEvent(payload) {
    if (this.socketService && this.socketService.handleRoomDeletedEvent) {
      return this.socketService.handleRoomDeletedEvent(payload);
    }
  }

  async handleRoomListUpdatedEvent(payload) {
    if (this.socketService && this.socketService.handleRoomListUpdatedEvent) {
      return this.socketService.handleRoomListUpdatedEvent(payload);
    }
  }

  async handleRoomStatusChangedEvent(payload) {
    if (this.socketService && this.socketService.handleRoomStatusChangedEvent) {
      return this.socketService.handleRoomStatusChangedEvent(payload);
    }
  }

  // User event delegates
  async handleBalanceUpdatedEvent(userId, payload) {
    if (this.socketService && this.socketService.handleBalanceUpdatedEvent) {
      return this.socketService.handleBalanceUpdatedEvent(userId, payload);
    }
  }

  async handleTransactionCompletedEvent(userId, payload) {
    if (this.socketService && this.socketService.handleTransactionCompletedEvent) {
      return this.socketService.handleTransactionCompletedEvent(userId, payload);
    }
  }

  async handleNotificationReceivedEvent(userId, payload) {
    if (this.socketService && this.socketService.handleNotificationReceivedEvent) {
      return this.socketService.handleNotificationReceivedEvent(userId, payload);
    }
  }

  /**
   * Get message handling statistics
   * @returns {Object} Statistics
   */
  getStats() {
    return {
      messageCount: this.messageCount,
      errorCount: this.errorCount,
      successRate: this.messageCount > 0 ? ((this.messageCount - this.errorCount) / this.messageCount) * 100 : 100,
    };
  }
}

module.exports = RedisMessageHandler;
