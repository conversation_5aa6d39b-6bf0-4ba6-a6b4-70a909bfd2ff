/**
 * Manager Service Communicator
 * 
 * Handles communication with the Manager Service for room management,
 * user management, and other business logic operations.
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');
const logger = require('../../utils/logger');
const config = require('../../config');
const CircuitBreaker = require('../../utils/circuitBreaker');

class ManagerServiceCommunicator {
  constructor() {
    this.baseURL = config.services.managerService?.url || process.env.MANAGER_SERVICE_URL || 'http://localhost:3002';
    this.timeout = config.services.managerService?.timeout || 10000;
    this.retryAttempts = config.services.managerService?.retryAttempts || 3;
    this.retryDelay = config.services.managerService?.retryDelay || 1000;
    this.jwtSecret = process.env.JWT_SECRET || 'dev_jwt_secret_key_change_in_production';

    // Initialize circuit breaker
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      monitoringPeriod: 10000, // 10 seconds
    });

    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Socket-Gateway/1.0.0',
      },
    });

    // Set up request/response interceptors
    this.setupInterceptors();

    logger.info('Manager Service Communicator initialized', {
      baseURL: this.baseURL,
      timeout: this.timeout,
      hasJwtSecret: !!this.jwtSecret,
    });
  }

  /**
   * Generate service-to-service JWT token
   * @returns {string} JWT token
   */
  generateServiceToken() {
    const payload = {
      service: 'socket-gateway',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour expiration
    };

    return jwt.sign(payload, this.jwtSecret);
  }

  /**
   * Generate user JWT token for service-to-service communication
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @returns {string} JWT token
   */
  generateUserToken(userId, username) {
    const payload = {
      sub: userId,
      user_id: userId,
      username: username,
      role: 'user', // Default role
      sessionId: `socket-gateway-${Date.now()}`,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour expiration
      iss: 'xzgame', // Match socket-gateway expected issuer
      aud: 'xzgame-services', // Match socket-gateway expected audience
    };

    return jwt.sign(payload, this.jwtSecret);
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add JWT authentication header for service-to-service communication
        const token = this.generateServiceToken();
        config.headers.Authorization = `Bearer ${token}`;

        logger.debug('Manager Service request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
          hasAuth: !!config.headers.Authorization,
        });
        return config;
      },
      (error) => {
        logger.logError(error, { context: 'Manager Service request interceptor' });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Manager Service response', {
          status: response.status,
          url: response.config.url,
          dataSize: JSON.stringify(response.data).length,
        });
        return response;
      },
      (error) => {
        logger.logError(error, {
          context: 'Manager Service response interceptor',
          status: error.response?.status,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get room list from Manager Service
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Room list response
   */
  async getRoomList(options = {}) {
    try {
      const {
        page = 1,
        per_page = 25,
        game_type = null,
        status = null,
        has_space = null,
        id = null,
      } = options;

      const params = {
        page,
        per_page,
      };

      // Add filters if provided
      if (game_type) params.game_type = game_type;
      if (status) params.status = status;
      if (has_space !== null) params.has_space = has_space;
      if (id) params.id = id;

      logger.debug('Requesting room list from Manager Service', {
        params,
        baseURL: this.baseURL,
      });

      const response = await this.client.get('/rooms', { params });

      if (response.data.success) {
        logger.info('Room list retrieved from Manager Service', {
          roomCount: response.data.data?.rooms?.length || 0,
          totalCount: response.data.data?.pagination?.total_count || 0,
          page: response.data.data?.pagination?.current_page || 1,
        });

        return {
          success: true,
          rooms: response.data.data.rooms || [],
          pagination: response.data.data.pagination || {},
        };
      } else {
        throw new Error(response.data.error?.message || 'Failed to get room list');
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Get room list from Manager Service',
        options,
      });

      return {
        success: false,
        error: error.message,
        rooms: [],
      };
    }
  }

  /**
   * Get specific room details from Manager Service
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Room details response
   */
  async getRoomDetails(roomId) {
    try {
      logger.debug('Requesting room details from Manager Service', {
        roomId,
      });

      // First try the direct room endpoint
      try {
        const response = await this.client.get(`/rooms/${roomId}`);

        if (response.data.success && response.data.data?.room) {
          const room = response.data.data.room;

          // Check if the room has meaningful data (not all null)
          if (room.name && room.game_type && room.max_players) {
            logger.info('Room details retrieved from direct endpoint', {
              roomId,
              roomName: room.name,
              gameType: room.game_type,
            });

            return {
              success: true,
              room: room,
            };
          } else {
            logger.warn('Direct endpoint returned room with null data, trying room list approach', {
              roomId,
              roomData: room,
            });
          }
        }
      } catch (directError) {
        logger.warn('Direct room endpoint failed, trying room list approach', {
          roomId,
          error: directError.message,
        });
      }

      // Fallback: Use room list endpoint with ID filter
      logger.info('🔄 Trying room list endpoint with ID filter', { roomId });
      const listResponse = await this.getRoomList({ id: roomId });

      if (listResponse.success && listResponse.rooms && listResponse.rooms.length > 0) {
        const room = listResponse.rooms[0];

        logger.info('✅ Room details retrieved from room list endpoint', {
          roomId,
          roomName: room.name,
          gameType: room.game_type,
          maxPlayers: room.max_players,
          betAmount: room.bet_amount,
        });

        return {
          success: true,
          room: room,
        };
      } else {
        throw new Error('Room not found in room list');
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Get room details from Manager Service',
        roomId,
      });

      return {
        success: false,
        error: error.message,
        room: null,
      };
    }
  }

  /**
   * Get filtered room list with enhanced options
   * @param {Object} filters - Room filters
   * @returns {Promise<Object>} Filtered room list response
   */
  async getFilteredRoomList(filters = {}) {
    try {
      const {
        gameType = null,
        status = 'waiting', // Manager Service uses lowercase status
        hasSpace = null, // Default to null (show all rooms)
        minBetAmount = null,
        maxBetAmount = null,
        isPrivate = null,
        page = 1,
        limit = 25,
      } = filters;

      const options = {
        page,
        per_page: limit,
        game_type: gameType,
        status,
        has_space: hasSpace,
      };

      // Note: Manager Service doesn't currently support bet amount filtering
      // This would need to be added to the Manager Service API
      if (minBetAmount || maxBetAmount) {
        logger.warn('Bet amount filtering not yet supported by Manager Service', {
          minBetAmount,
          maxBetAmount,
        });
      }

      const result = await this.getRoomList(options);

      // Apply client-side filtering for unsupported filters
      if (result.success && result.rooms) {
        let filteredRooms = result.rooms;

        // Filter by bet amount if specified
        if (minBetAmount !== null) {
          filteredRooms = filteredRooms.filter(room => 
            parseFloat(room.bet_amount) >= minBetAmount
          );
        }

        if (maxBetAmount !== null) {
          filteredRooms = filteredRooms.filter(room => 
            parseFloat(room.bet_amount) <= maxBetAmount
          );
        }

        // Filter by private status if specified
        if (isPrivate !== null) {
          filteredRooms = filteredRooms.filter(room => 
            room.is_private === isPrivate
          );
        }

        return {
          success: true,
          rooms: filteredRooms,
          pagination: result.pagination,
          appliedFilters: filters,
        };
      }

      return result;

    } catch (error) {
      logger.logError(error, {
        context: 'Get filtered room list from Manager Service',
        filters,
      });

      return {
        success: false,
        error: error.message,
        rooms: [],
      };
    }
  }

  /**
   * Transform room data to enhanced format
   * @param {Object} room - Room data from Manager Service
   * @returns {Object} Enhanced room data
   */
  transformToEnhancedFormat(room) {
    if (!room) return null;

    // Use actual players array length instead of current_players field to handle data inconsistencies
    const playersArray = room.players || [];
    const actualPlayerCount = playersArray.length;
    const maxPlayers = room.max_players || 4;

    return {
      id: room.id,
      name: room.name,
      game_type: room.game_type,
      status: room.status,
      current_players: actualPlayerCount, // Use actual player count from players array
      max_players: maxPlayers,
      min_players: room.min_players || 2,
      bet_amount: parseFloat(room.bet_amount || 0),
      currency: room.currency || 'USD',
      prize_pool: parseFloat(room.prize_pool || 0),
      is_private: room.is_private || false,
      has_password: room.has_password || false,
      has_space: actualPlayerCount < maxPlayers, // Use actual player count for space calculation
      is_featured: room.is_featured || false, // Manager Service doesn't have this yet
      creator_id: room.creator_id,
      created_at: room.created_at,
      updated_at: room.updated_at,
      // Include players data from manager service
      players: playersArray,
    };
  }

  /**
   * Get enhanced room list with transformed data
   * @param {Object} filters - Room filters
   * @returns {Promise<Object>} Enhanced room list response
   */
  async getEnhancedRoomList(filters = {}) {
    try {
      const result = await this.getFilteredRoomList(filters);

      if (result.success && result.rooms) {
        const enhancedRooms = result.rooms.map(room => 
          this.transformToEnhancedFormat(room)
        ).filter(room => room !== null);

        return {
          success: true,
          rooms: enhancedRooms,
          pagination: result.pagination,
          appliedFilters: result.appliedFilters,
          source: 'manager-service',
        };
      }

      return result;

    } catch (error) {
      logger.logError(error, {
        context: 'Get enhanced room list from Manager Service',
        filters,
      });

      return {
        success: false,
        error: error.message,
        rooms: [],
      };
    }
  }

  /**
   * Join a room via Manager Service
   * @param {Object} joinData - Join room data
   * @param {string} joinData.roomId - Room ID
   * @param {string} joinData.userId - User ID
   * @param {string} joinData.username - Username
   * @param {number} joinData.betAmount - Bet amount
   * @param {string} joinData.password - Room password (optional)
   * @returns {Promise<Object>} Join result
   */
  async joinRoom(joinData) {
    const { roomId, userId, username, betAmount, password } = joinData;

    try {
      logger.debug('Joining room via Manager Service', {
        roomId,
        userId,
        username,
        betAmount,
        hasPassword: !!password,
      });

      const requestData = {
        user_id: userId,
        username: username,
        bet_amount: betAmount,
      };

      // Add password if provided
      if (password) {
        requestData.password = password;
      }

      // Generate user token for this specific request
      const userToken = this.generateUserToken(userId, username);

      // Make request with user token instead of service token
      const response = await this.client.post(`/rooms/${roomId}/join`, requestData, {
        headers: {
          'Authorization': `Bearer ${userToken}`,
        },
      });

      if (response.data.success) {
        logger.info('Successfully joined room via Manager Service', {
          roomId,
          userId,
          username,
          playerCount: response.data.data?.room?.current_players || 0,
        });

        return {
          success: true,
          room: response.data.data?.room,
          player: response.data.data?.player,
          message: response.data.message,
        };
      } else {
        logger.warn('Failed to join room via Manager Service', {
          roomId,
          userId,
          error: response.data.error,
        });

        return {
          success: false,
          error: response.data.error?.message || response.data.error || 'Failed to join room',
          code: response.data.error?.code || 'JOIN_FAILED',
        };
      }
    } catch (error) {
      logger.logError('Exception during room join via Manager Service', error, {
        roomId,
        userId,
        username,
      });

      // Handle specific HTTP errors
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;

        return {
          success: false,
          error: errorData?.error?.message || errorData?.error || `HTTP ${statusCode} error`,
          code: errorData?.error?.code || 'HTTP_ERROR',
        };
      }

      return {
        success: false,
        error: error.message || 'Manager Service communication failed',
        code: 'COMMUNICATION_ERROR',
      };
    }
  }

  /**
   * Leave a room
   * @param {Object} leaveData - Leave room data
   * @returns {Promise<Object>} Leave room response
   */
  async leaveRoom(leaveData) {
    try {
      logger.debug('Leaving room via Manager Service', {
        roomId: leaveData.roomId,
        userId: leaveData.userId,
        username: leaveData.username,
      });

      const response = await this.makeRequest('POST', '/rooms/leave', leaveData);

      if (response.success) {
        logger.info('Successfully left room via Manager Service', {
          roomId: leaveData.roomId,
          userId: leaveData.userId,
        });

        return {
          success: true,
          room: response.data?.room,
          message: response.message || 'Successfully left room',
        };
      } else {
        logger.warn('Failed to leave room via Manager Service', {
          roomId: leaveData.roomId,
          userId: leaveData.userId,
          error: response.error,
        });

        return {
          success: false,
          error: response.error || 'Failed to leave room',
          code: response.code || 'LEAVE_ROOM_FAILED',
        };
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Leave room via Manager Service',
        roomId: leaveData.roomId,
        userId: leaveData.userId,
      });

      return {
        success: false,
        error: error.message || 'Manager Service communication failed',
        code: 'COMMUNICATION_ERROR',
      };
    }
  }

  /**
   * Make a request to Manager Service with circuit breaker protection
   * @param {string} method - HTTP method
   * @param {string} path - API path
   * @param {Object} data - Request data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(method, path, data = null, options = {}) {
    const requestFn = async () => {
      const config = {
        method: method.toLowerCase(),
        url: path,
        ...options,
      };

      if (data && ['post', 'put', 'patch'].includes(config.method)) {
        config.data = data;
      } else if (data && config.method === 'get') {
        config.params = data;
      }

      const response = await this.client.request(config);

      if (response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.error?.message || response.data.error || 'Request failed');
      }
    };

    try {
      return await this.circuitBreaker.execute(requestFn);
    } catch (error) {
      logger.error('Manager Service request failed', {
        method,
        path,
        error: error.message,
        circuitBreakerState: this.circuitBreaker.getStatus().state,
      });

      return {
        success: false,
        error: error.message,
        code: error.message.includes('Circuit breaker') ? 'CIRCUIT_BREAKER_OPEN' : 'REQUEST_FAILED',
      };
    }
  }

  /**
   * Get circuit breaker status
   * @returns {Object} Circuit breaker status
   */
  getCircuitBreakerStatus() {
    return this.circuitBreaker.getStatus();
  }

  /**
   * Health check for Manager Service
   * @returns {Promise<boolean>} Service health status
   */
  async healthCheck() {
    try {
      const response = await this.client.get('/health', {
        timeout: 5000, // Shorter timeout for health checks
      });

      const isHealthy = response.status === 200;

      logger.debug('Manager Service health check', {
        status: response.status,
        healthy: isHealthy,
      });

      return isHealthy;

    } catch (error) {
      logger.warn('Manager Service health check failed', {
        error: error.message,
        baseURL: this.baseURL,
      });

      return false;
    }
  }

  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getStats() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      retryAttempts: this.retryAttempts,
      retryDelay: this.retryDelay,
      circuitBreaker: this.circuitBreaker.getStatus(),
    };
  }
}

module.exports = ManagerServiceCommunicator;
