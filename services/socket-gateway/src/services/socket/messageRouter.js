/**
 * Message Router - Pure Routing Logic
 * 
 * RESPONSIBILITIES:
 * 1. Route messages to appropriate services
 * 2. Handle service discovery
 * 3. Load balancing (if multiple instances)
 * 4. Circuit breaker integration
 * 5. Timeout handling
 * 
 * NO BUSINESS LOGIC PROCESSING!
 */

const logger = require('../../utils/logger');
const CircuitBreaker = require('../../utils/circuitBreaker');

class MessageRouter {
  constructor() {
    this.serviceEndpoints = new Map();
    this.circuitBreakers = new Map();
    this.loadBalancers = new Map();
    
    this.initializeServiceEndpoints();
    this.initializeCircuitBreakers();
  }

  /**
   * Initialize service endpoints configuration
   */
  initializeServiceEndpoints() {
    this.serviceEndpoints.set('room_orchestrator', {
      type: 'http',
      baseUrl: process.env.ROOM_ORCHESTRATOR_URL || 'http://localhost:3003',
      timeout: 10000,
    });

    this.serviceEndpoints.set('game_orchestrator', {
      type: 'http', 
      baseUrl: process.env.GAME_ORCHESTRATOR_URL || 'http://localhost:3004',
      timeout: 10000,
    });

    this.serviceEndpoints.set('user_service', {
      type: 'http',
      baseUrl: process.env.USER_SERVICE_URL || 'http://localhost:3005',
      timeout: 5000,
    });

    this.serviceEndpoints.set('lobby_service', {
      type: 'http',
      baseUrl: process.env.LOBBY_SERVICE_URL || 'http://localhost:3006',
      timeout: 5000,
    });

    this.serviceEndpoints.set('connection_service', {
      type: 'redis',
      channel: 'connection:events',
      timeout: 2000,
    });
  }

  /**
   * Initialize circuit breakers for each service
   */
  initializeCircuitBreakers() {
    this.serviceEndpoints.forEach((config, serviceName) => {
      this.circuitBreakers.set(serviceName, new CircuitBreaker({
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      }));
    });
  }

  /**
   * Initialize router
   */
  async initialize() {
    logger.info('Initializing Message Router');
    
    // Test connectivity to all services
    await this.testServiceConnectivity();
    
    logger.info('Message Router initialized successfully');
  }

  /**
   * Route message to service
   * @param {string} service - Target service name
   * @param {string} event - Event type
   * @param {Object} data - Message data
   * @returns {Promise<Object>} Service response
   */
  async route(service, event, data) {
    const serviceConfig = this.serviceEndpoints.get(service);
    if (!serviceConfig) {
      throw new Error(`Unknown service: ${service}`);
    }

    const circuitBreaker = this.circuitBreakers.get(service);
    
    // Execute with circuit breaker protection
    return await circuitBreaker.execute(async () => {
      switch (serviceConfig.type) {
        case 'http':
          return await this.routeHttpMessage(service, event, data, serviceConfig);
        case 'redis':
          return await this.routeRedisMessage(service, event, data, serviceConfig);
        case 'grpc':
          return await this.routeGrpcMessage(service, event, data, serviceConfig);
        default:
          throw new Error(`Unsupported service type: ${serviceConfig.type}`);
      }
    });
  }

  /**
   * Route HTTP message to service
   * @param {string} service - Service name
   * @param {string} event - Event type
   * @param {Object} data - Message data
   * @param {Object} config - Service configuration
   * @returns {Promise<Object>} Response
   */
  async routeHttpMessage(service, event, data, config) {
    const axios = require('axios');
    
    const requestConfig = {
      method: 'POST',
      url: `${config.baseUrl}/api/v1/${event}`,
      data: {
        event,
        payload: data,
        metadata: {
          source: 'socket-gateway',
          timestamp: new Date().toISOString(),
          correlationId: this.generateCorrelationId(),
        },
      },
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Source-Service': 'socket-gateway',
      },
    };

    logger.debug('Routing HTTP message', {
      service,
      event,
      url: requestConfig.url,
      correlationId: requestConfig.data.metadata.correlationId,
    });

    try {
      const response = await axios(requestConfig);
      
      logger.debug('HTTP message routed successfully', {
        service,
        event,
        status: response.status,
        correlationId: requestConfig.data.metadata.correlationId,
      });

      return response.data;
    } catch (error) {
      logger.error('HTTP message routing failed', {
        service,
        event,
        error: error.message,
        status: error.response?.status,
        correlationId: requestConfig.data.metadata.correlationId,
      });
      
      throw new Error(`HTTP routing failed: ${error.message}`);
    }
  }

  /**
   * Route Redis message to service
   * @param {string} service - Service name
   * @param {string} event - Event type
   * @param {Object} data - Message data
   * @param {Object} config - Service configuration
   * @returns {Promise<Object>} Response
   */
  async routeRedisMessage(service, event, data, config) {
    const redisService = require('../redisService');
    
    const message = {
      event,
      payload: data,
      metadata: {
        source: 'socket-gateway',
        timestamp: new Date().toISOString(),
        correlationId: this.generateCorrelationId(),
      },
    };

    logger.debug('Routing Redis message', {
      service,
      event,
      channel: config.channel,
      correlationId: message.metadata.correlationId,
    });

    try {
      await redisService.publish(config.channel, message);
      
      logger.debug('Redis message routed successfully', {
        service,
        event,
        channel: config.channel,
        correlationId: message.metadata.correlationId,
      });

      // For Redis pub/sub, we don't wait for response
      return { success: true, async: true };
    } catch (error) {
      logger.error('Redis message routing failed', {
        service,
        event,
        channel: config.channel,
        error: error.message,
        correlationId: message.metadata.correlationId,
      });
      
      throw new Error(`Redis routing failed: ${error.message}`);
    }
  }

  /**
   * Route gRPC message to service
   * @param {string} service - Service name
   * @param {string} event - Event type
   * @param {Object} data - Message data
   * @param {Object} config - Service configuration
   * @returns {Promise<Object>} Response
   */
  async routeGrpcMessage(service, event, data, config) {
    // gRPC implementation would go here
    throw new Error('gRPC routing not implemented yet');
  }

  /**
   * Test connectivity to all services
   */
  async testServiceConnectivity() {
    const results = [];
    
    for (const [serviceName, config] of this.serviceEndpoints) {
      try {
        if (config.type === 'http') {
          const axios = require('axios');
          await axios.get(`${config.baseUrl}/health`, { timeout: 5000 });
          results.push({ service: serviceName, status: 'healthy' });
        } else {
          // For non-HTTP services, assume healthy for now
          results.push({ service: serviceName, status: 'assumed_healthy' });
        }
      } catch (error) {
        logger.warn('Service connectivity test failed', {
          service: serviceName,
          error: error.message,
        });
        results.push({ service: serviceName, status: 'unhealthy', error: error.message });
      }
    }

    logger.info('Service connectivity test results', { results });
    return results;
  }

  /**
   * Generate correlation ID for request tracking
   * @returns {string} Correlation ID
   */
  generateCorrelationId() {
    return `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get router health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    const circuitBreakerStatus = {};
    this.circuitBreakers.forEach((cb, service) => {
      circuitBreakerStatus[service] = cb.getStatus();
    });

    return {
      status: 'healthy',
      services: Array.from(this.serviceEndpoints.keys()),
      circuitBreakers: circuitBreakerStatus,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Cleanup router resources
   */
  async cleanup() {
    logger.info('Cleaning up Message Router');
    
    // Reset circuit breakers
    this.circuitBreakers.forEach(cb => cb.reset());
    
    logger.info('Message Router cleanup complete');
  }
}

module.exports = MessageRouter;
