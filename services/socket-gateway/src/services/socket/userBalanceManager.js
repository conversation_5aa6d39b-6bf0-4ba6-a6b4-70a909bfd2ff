/**
 * User Balance Manager
 * 
 * Handles user balance fetching, caching, and updates.
 * Manages balance synchronization with the Game Service.
 */

const logger = require('../../utils/logger');
const redisService = require('../redisService');

class UserBalanceManager {
  constructor() {
    this.balanceCache = new Map(); // userId -> { balance, timestamp, token }
    this.balanceCacheTTL = 60000; // 1 minute
    this.gameServiceCommunicator = null; // Will be injected
  }

  /**
   * Set the game service communicator
   * @param {Object} communicator - Game service communicator instance
   */
  setGameServiceCommunicator(communicator) {
    this.gameServiceCommunicator = communicator;
  }

  /**
   * Fetch user balance with caching
   * @param {string} userId - User ID
   * @param {string} token - User's JWT token
   * @param {boolean} forceRefresh - Force refresh from game service
   * @returns {Promise<number>} User balance
   */
  async fetchUserBalance(userId, token, forceRefresh = false) {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedBalance = this.getCachedBalance(userId);
        if (cachedBalance !== null) {
          logger.debug('Using cached balance', {
            userId,
            balance: cachedBalance,
          });
          return cachedBalance;
        }
      }

      // Fetch from game service
      const balance = await this.fetchBalanceFromGameService(userId, token);
      
      // Cache the result
      this.cacheBalance(userId, balance, token);
      
      return balance;
    } catch (error) {
      logger.logError(error, {
        context: 'Fetch user balance',
        userId,
        hasToken: !!token,
      });

      // Return cached balance if available, otherwise 0
      const cachedBalance = this.getCachedBalance(userId, true); // Allow expired cache
      return cachedBalance !== null ? cachedBalance : 0;
    }
  }

  /**
   * Fetch balance from game service
   * @param {string} userId - User ID
   * @param {string} token - User's JWT token
   * @returns {Promise<number>} User balance
   */
  async fetchBalanceFromGameService(userId, token) {
    if (!this.gameServiceCommunicator) {
      throw new Error('Game service communicator not set');
    }

    try {
      // Try Redis request first (faster)
      const redisResponse = await this.fetchBalanceViaRedis(userId, token);
      if (redisResponse !== null) {
        return redisResponse;
      }

      // Fallback to HTTP request
      return await this.fetchBalanceViaHttp(userId, token);
    } catch (error) {
      logger.logError(error, {
        context: 'Fetch balance from game service',
        userId,
      });
      throw error;
    }
  }

  /**
   * Fetch balance via Redis pub/sub
   * @param {string} userId - User ID
   * @param {string} token - User's JWT token
   * @returns {Promise<number|null>} User balance or null if failed
   */
  async fetchBalanceViaRedis(userId, token) {
    try {
      const response = await this.gameServiceCommunicator.sendRedisRequest(
        'get_user_balance',
        { userId, token },
        { timeout: 5000 }
      );

      if (response.success && typeof response.balance === 'number') {
        logger.debug('Fetched balance via Redis', {
          userId,
          balance: response.balance,
        });
        return response.balance;
      }

      logger.warn('Invalid balance response via Redis', {
        userId,
        response,
      });
      return null;
    } catch (error) {
      logger.debug('Failed to fetch balance via Redis, will try HTTP', {
        userId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Fetch balance via HTTP request
   * @param {string} userId - User ID
   * @param {string} token - User's JWT token
   * @returns {Promise<number>} User balance
   */
  async fetchBalanceViaHttp(userId, token) {
    try {
      const response = await this.gameServiceCommunicator.sendHttpRequest(
        'GET',
        `/users/${userId}/balance`,
        {},
        { token, timeout: 8000 }
      );

      if (response.success && typeof response.balance === 'number') {
        logger.debug('Fetched balance via HTTP', {
          userId,
          balance: response.balance,
        });
        return response.balance;
      }

      throw new Error('Invalid balance response format');
    } catch (error) {
      logger.logError(error, {
        context: 'Fetch balance via HTTP',
        userId,
      });
      throw error;
    }
  }

  /**
   * Get cached balance if available and not expired
   * @param {string} userId - User ID
   * @param {boolean} allowExpired - Allow expired cache
   * @returns {number|null} Cached balance or null
   */
  getCachedBalance(userId, allowExpired = false) {
    const cached = this.balanceCache.get(userId);
    
    if (!cached) {
      return null;
    }

    const now = Date.now();
    const isExpired = (now - cached.timestamp) > this.balanceCacheTTL;

    if (isExpired && !allowExpired) {
      // Remove expired cache
      this.balanceCache.delete(userId);
      return null;
    }

    return cached.balance;
  }

  /**
   * Cache user balance
   * @param {string} userId - User ID
   * @param {number} balance - User balance
   * @param {string} token - User's JWT token
   */
  cacheBalance(userId, balance, token) {
    this.balanceCache.set(userId, {
      balance,
      timestamp: Date.now(),
      token,
    });

    logger.debug('Cached user balance', {
      userId,
      balance,
    });
  }

  /**
   * Update cached balance
   * @param {string} userId - User ID
   * @param {number} newBalance - New balance
   */
  updateCachedBalance(userId, newBalance) {
    const cached = this.balanceCache.get(userId);
    
    if (cached) {
      cached.balance = newBalance;
      cached.timestamp = Date.now();
      
      logger.debug('Updated cached balance', {
        userId,
        newBalance,
      });
    } else {
      // Cache new balance without token (will be updated on next fetch)
      this.cacheBalance(userId, newBalance, null);
    }
  }

  /**
   * Handle balance update event from game service
   * @param {string} userId - User ID
   * @param {Object} payload - Balance update payload
   */
  handleBalanceUpdate(userId, payload) {
    try {
      const { newBalance, oldBalance, reason, transactionId } = payload;

      // Update cached balance
      this.updateCachedBalance(userId, newBalance);

      logger.info('Processed balance update', {
        userId,
        oldBalance,
        newBalance,
        reason,
        transactionId,
      });

      return {
        success: true,
        userId,
        newBalance,
        oldBalance,
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Handle balance update',
        userId,
        payload,
      });

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Invalidate cached balance for user
   * @param {string} userId - User ID
   */
  invalidateBalance(userId) {
    const wasPresent = this.balanceCache.has(userId);
    this.balanceCache.delete(userId);

    if (wasPresent) {
      logger.debug('Invalidated cached balance', { userId });
    }
  }

  /**
   * Clean up expired balance cache entries
   */
  cleanupExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [userId, cached] of this.balanceCache.entries()) {
      if ((now - cached.timestamp) > this.balanceCacheTTL) {
        this.balanceCache.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('Cleaned up expired balance cache', {
        cleanedCount,
        remaining: this.balanceCache.size,
      });
    }

    return cleanedCount;
  }

  /**
   * Get balance cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    let expiredCount = 0;

    for (const cached of this.balanceCache.values()) {
      if ((now - cached.timestamp) > this.balanceCacheTTL) {
        expiredCount++;
      }
    }

    return {
      totalCached: this.balanceCache.size,
      expiredCount,
      validCount: this.balanceCache.size - expiredCount,
      cacheTTL: this.balanceCacheTTL,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Clear all cached balances
   */
  clearAllCache() {
    const count = this.balanceCache.size;
    this.balanceCache.clear();

    logger.info('Cleared all balance cache', {
      clearedCount: count,
    });

    return count;
  }

  /**
   * Get all cached balances (for debugging)
   * @returns {Object} Map of userId to balance info
   */
  getAllCachedBalances() {
    const result = {};
    const now = Date.now();

    for (const [userId, cached] of this.balanceCache.entries()) {
      result[userId] = {
        balance: cached.balance,
        age: now - cached.timestamp,
        expired: (now - cached.timestamp) > this.balanceCacheTTL,
      };
    }

    return result;
  }
}

module.exports = UserBalanceManager;
