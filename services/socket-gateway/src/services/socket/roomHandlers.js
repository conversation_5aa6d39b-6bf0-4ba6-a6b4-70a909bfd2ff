/**
 * Room Handlers
 *
 * Handles room-related socket events and operations.
 * Manages room joining, leaving, subscriptions, and room state management.
 */

const logger = require('../../utils/logger');
const subscriptionService = require('../subscriptionService');
const roomStateManager = require('../roomStateManager');
const redisService = require('../redisService');
const ManagerServiceCommunicator = require('./managerServiceCommunicator');
const operationMonitor = require('../operationMonitor');

class RoomHandlers {
  constructor() {
    this.socketService = null; // Will be injected
    this.managerServiceCommunicator = new ManagerServiceCommunicator();

    // Note: Operation mutex now handled by Room Orchestrator
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  // Note: Operation locks now handled by Room Orchestrator

  /**
   * Handle room join request
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Join room data
   * @param {Function} callback - Response callback
   */
  async handleJoinRoom(socket, data, callback) {
    const { userId, username } = socket;

    try {
      const { roomId, password } = data;
      let { betAmount } = data;

      // If betAmount is not provided, fetch it from the room data
      if (!betAmount || betAmount === 0) {
        try {
          const roomResponse = await this.managerServiceCommunicator.getRoomList({
            id: roomId,
            page: 1,
            per_page: 1,
          });

          if (roomResponse.success && roomResponse.rooms && roomResponse.rooms.length > 0) {
            betAmount = parseFloat(roomResponse.rooms[0].bet_amount);
            logger.debug('Fetched bet amount from room data', {
              roomId,
              betAmount,
            });
          } else {
            throw new Error('Room not found');
          }
        } catch (error) {
          logger.warn('Failed to fetch room bet amount', {
            roomId,
            error: error.message,
          });

          const errorResponse = {
            success: false,
            error: 'Failed to get room information',
            code: 'ROOM_INFO_ERROR',
          };
          if (callback) callback(errorResponse);
          return;
        }
      }

      logger.info('Routing join room request to Room Orchestrator', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        betAmount,
      });

      // Route to Room Orchestrator via Redis pub/sub
      const response = await this.routeToRoomOrchestrator('join_room', {
        userId,
        username,
        roomId,
        password,
        betAmount,
        socketId: socket.id,
      });

      // Handle response
      if (response.success) {
        // Handle broadcast events
        if (response.broadcast) {
          await this.handleBroadcastEvent(response.broadcast);
        }

        logger.info('Join room completed successfully', {
          socketId: socket.id,
          userId,
          roomId,
        });
      } else {
        logger.warn('Join room failed', {
          socketId: socket.id,
          userId,
          roomId,
          error: response.error,
          code: response.code,
        });
      }

      if (callback) callback(response);

    } catch (error) {
      logger.logError(error, {
        context: 'Join room routing',
        socketId: socket.id,
        userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Service temporarily unavailable',
        code: 'ROUTING_ERROR',
      };

      if (callback) callback(errorResponse);
    }
  }



  /**
   * Handle room leave request
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Leave room data
   * @param {Function} callback - Response callback
   */
  async handleLeaveRoom(socket, data, callback) {
    const { userId, username } = socket;

    try {
      const { roomId } = data;

      // Basic validation
      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      logger.info('Routing leave room request to Room Orchestrator', {
        socketId: socket.id,
        userId,
        username,
        roomId,
      });

      // Route to Room Orchestrator via Redis pub/sub
      const response = await this.routeToRoomOrchestrator('leave_room', {
        userId,
        username,
        roomId,
        socketId: socket.id,
      });

      // Handle response
      if (response.success) {
        // Handle broadcast events
        if (response.broadcast) {
          await this.handleBroadcastEvent(response.broadcast);
        }

        logger.info('Leave room completed successfully', {
          socketId: socket.id,
          userId,
          roomId,
        });
      } else {
        logger.warn('Leave room failed', {
          socketId: socket.id,
          userId,
          roomId,
          error: response.error,
          code: response.code,
        });
      }

      if (callback) callback(response);

    } catch (error) {
      logger.logError(error, {
        context: 'Leave room routing',
        socketId: socket.id,
        userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Service temporarily unavailable',
        code: 'ROUTING_ERROR',
      };

      if (callback) callback(errorResponse);
    }
  }



  /**
   * Handle room subscription
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Subscribe room data
   * @param {Function} callback - Response callback
   */
  async handleSubscribeRoom(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Step 1: Handle local socket subscription (gateway responsibility)
      await subscriptionService.subscribeRoom(socket, userId, username, roomId);

      // Step 2: Notify room-service about the subscription (business logic)
      await this.notifyRoomServiceSubscription(userId, username, roomId, 'subscribe');

      logger.logSocketEvent('subscribe_room', socket.id, userId, {
        roomId,
        username,
      });

      // Send success response
      if (callback) {
        callback({ success: true });
      }

      // Note: Room info will be sent via Redis broadcast from room-service
      // The room-service will handle the subscription event and send unified room info

    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe room',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        error: 'Failed to subscribe to room',
        code: 'SUBSCRIBE_ROOM_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle room unsubscription
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Unsubscribe room data
   * @param {Function} callback - Response callback
   */
  async handleUnsubscribeRoom(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Step 1: Handle local socket unsubscription (gateway responsibility)
      await subscriptionService.unsubscribeRoom(socket, userId, roomId);

      // Step 2: Notify room-service about the unsubscription (business logic)
      await this.notifyRoomServiceSubscription(userId, username, roomId, 'unsubscribe');

      logger.logSocketEvent('unsubscribe_room', socket.id, userId, {
        roomId,
        username,
      });

      // Send success response
      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe room',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        error: 'Failed to unsubscribe from room',
        code: 'UNSUBSCRIBE_ROOM_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Notify room-service about subscription changes
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {string} roomId - Room ID
   * @param {string} action - Action type ('subscribe' or 'unsubscribe')
   */
  async notifyRoomServiceSubscription(userId, username, roomId, action) {
    try {
      const eventType = action === 'subscribe' ? 'room_subscription' : 'room_unsubscription';

      const payload = {
        userId,
        username,
        roomId,
        action,
        timestamp: new Date().toISOString(),
      };

      // Publish to room-service via Redis
      await redisService.publish('room:subscription:events', {
        event: {
          type: eventType,
          payload,
          timestamp: new Date().toISOString(),
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId: `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          priority: 1,
        },
      });

      logger.debug('Notified room-service about subscription change', {
        userId,
        username,
        roomId,
        action,
        eventType,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Notify room-service subscription',
        userId,
        username,
        roomId,
        action,
      });
      // Don't throw error - subscription notification failure shouldn't break the subscription
    }
  }











  /**
   * Send current room info to socket
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {string} reason - Reason for sending
   */
  async sendCurrentRoomInfo(socket, roomId, reason = 'subscription') {
    try {
      logger.debug('Fetching current room info for socket', {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        reason,
      });

      // Fetch room information with enhanced player data
      const roomInfo = await this.fetchRoomInfoFromServiceWithContext(roomId, socket, reason);

      if (roomInfo) {
        // Send room info to the socket - structure will be unified by the originating service
        socket.emit('room_info_updated', roomInfo);

        logger.info('Sent current room info to socket', {
          socketId: socket.id,
          userId: socket.userId,
          roomId,
          reason,
          playerCount: roomInfo.currentPlayers || roomInfo.roomInfo?.players?.total || 0,
        });
      } else {
        logger.warn('Room info not found for socket', {
          socketId: socket.id,
          userId: socket.userId,
          roomId,
          reason,
        });
      }
    } catch (error) {
      logger.logError('Failed to send current room info to socket', error, {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        reason,
      });
    }
  }

  /**
   * Broadcast room info to all subscribers (including current socket)
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {string} reason - Reason for broadcast
   */
  async broadcastRoomInfoToAllSubscribers(socket, roomId, reason = 'update') {
    try {
      logger.debug('Broadcasting room info to all subscribers', {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        reason,
      });

      // Fetch room information from room-service
      const roomInfo = await this.fetchRoomInfoFromServiceWithContext(roomId, socket, reason);

      if (roomInfo) {
        const socketRoomName = this.getSocketRoomName(roomId);

        // Send room_info_updated event to ALL subscribers - structure unified by originating service
        socket.to(socketRoomName).emit('room_info_updated', roomInfo);

        // Also send to the current socket
        socket.emit('room_info_updated', roomInfo);

        logger.info('Broadcasted room info update to all subscribers', {
          socketId: socket.id,
          userId: socket.userId,
          roomId,
          reason,
          playerCount: roomInfo.currentPlayers || roomInfo.roomInfo?.players?.total || 0,
          socketRoom: socketRoomName,
        });
      } else {
        logger.warn('Room info not found for broadcasting', {
          socketId: socket.id,
          userId: socket.userId,
          roomId,
          reason,
        });
      }
    } catch (error) {
      logger.logError('Failed to broadcast room info to subscribers', error, {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        reason,
      });
    }
  }

  /**
   * Fetch room information from room-service
   * @param {string} roomId - Room ID
   * @param {number} retryCount - Number of retries for consistency check
   * @returns {Object|null} Room information or null if not found
   */
  async fetchRoomInfoFromService(roomId, retryCount = 0) {
    try {
      logger.debug('Fetching room info from room-service', { roomId, retryCount });

      // Get room details from room-service via gRPC
      const roomInfo = await this.fetchRoomDataFromRoomService(roomId);

      logger.debug('Room-service response received', {
        roomId,
        hasRoom: !!roomInfo,
        retryCount
      });

      if (roomInfo) {
        logger.info('Successfully retrieved real room data from room-service', {
          roomId,
          name: roomInfo.name,
          maxPlayers: roomInfo.maxPlayers,
          betAmount: roomInfo.betAmount,
          currentPlayers: roomInfo.currentPlayers,
        });

        // Room info is already in the correct format from room-service
        // Just ensure we have the expected structure
        const formattedRoomInfo = {
          roomId: roomInfo.roomId || roomId,
          name: roomInfo.name,
          gameType: roomInfo.gameType,
          status: roomInfo.status,
          currentPlayers: roomInfo.currentPlayers || 0,
          maxPlayers: roomInfo.maxPlayers,
          minPlayers: roomInfo.minPlayers || 2,
          betAmount: roomInfo.betAmount,
          players: roomInfo.players || [],
          isPrivate: roomInfo.isPrivate || false,
          autoStart: roomInfo.autoStart !== undefined ? roomInfo.autoStart : true,
          createdAt: roomInfo.createdAt,
          lastActivity: roomInfo.lastActivity,
          canJoin: roomInfo.canJoin !== undefined ? roomInfo.canJoin : (roomInfo.currentPlayers < roomInfo.maxPlayers),
          canStart: roomInfo.canStart !== undefined ? roomInfo.canStart : (roomInfo.currentPlayers >= (roomInfo.minPlayers || 2)),
        };

        // Check for data consistency - if current_players > 0 but players array is empty,
        // retry once to handle potential race conditions
        if (formattedRoomInfo.currentPlayers > 0 && formattedRoomInfo.players.length === 0 && retryCount === 0) {
          logger.warn('Data inconsistency detected: currentPlayers > 0 but players array is empty. Retrying...', {
            roomId,
            currentPlayers: formattedRoomInfo.currentPlayers,
            playersLength: formattedRoomInfo.players.length,
          });

          // Wait a bit and retry once
          await new Promise(resolve => setTimeout(resolve, 200));
          return this.fetchRoomInfoFromService(roomId, retryCount + 1);
        }

        logger.debug('Returning real room data from room-service', {
          roomId,
          name: formattedRoomInfo.name,
          maxPlayers: formattedRoomInfo.maxPlayers,
          betAmount: formattedRoomInfo.betAmount,
          currentPlayers: formattedRoomInfo.currentPlayers,
          playersArrayLength: formattedRoomInfo.players.length,
          playersData: formattedRoomInfo.players.map(p => ({ userId: p.userId, username: p.username })),
          retryCount,
        });

        return formattedRoomInfo;
      } else {
        logger.warn('⚠️ Room-service returned no data', {
          roomId,
          hasRoom: !!roomInfo
        });
        logger.warn('🔄 Falling back to mock room info', { roomId });
        return this.getFallbackRoomInfo(roomId);
      }
    } catch (error) {
      logger.logError('❌ Exception occurred while fetching from room-service', error, { roomId });

      // Fallback to mock data if room-service is unavailable
      logger.warn('Using fallback mock room info due to exception', { roomId });
      return this.getFallbackRoomInfo(roomId);
    }
  }

  /**
   * Transform manager service player data to socket gateway format
   * @param {Array} managerPlayers - Player data from manager service
   * @returns {Array} Transformed player data
   */
  transformManagerServicePlayers(managerPlayers) {
    if (!Array.isArray(managerPlayers)) {
      return [];
    }

    return managerPlayers.map(player => ({
      userId: player.user_id,
      username: player.username,
      position: player.position,
      isReady: player.is_ready || false,
      betAmount: parseFloat(player.bet_amount) || 0,
      joinedAt: player.joined_at,
      status: player.status || 'active',
      sessionId: player.session_id,
      balance: parseFloat(player.balance) || 0,
    }));
  }

  /**
   * Get players for a room (attempts to get real player data)
   * @param {string} roomId - Room ID
   * @param {number} currentPlayers - Current player count
   * @param {number} realBetAmount - Real bet amount from room data
   * @returns {Array} Array of player objects
   */
  async getPlayersForRoom(roomId, currentPlayers, realBetAmount = null) {
    try {
      // Try to get real player data from room subscriptions first
      const subscribedPlayers = this.getSubscribedPlayersForRoom(roomId);

      if (subscribedPlayers && subscribedPlayers.length > 0) {
        logger.debug('Using real subscribed player data', {
          roomId,
          playerCount: subscribedPlayers.length,
          realBetAmount,
        });

        // Use real subscribed players with correct bet amount
        return subscribedPlayers.map((player, index) => ({
          userId: player.userId,
          username: player.username,
          position: index + 1,
          isReady: player.isReady || false,
          betAmount: realBetAmount || player.betAmount || 1000,
          joinedAt: player.joinedAt || new Date().toISOString(),
          status: player.status || 'active',
        }));
      }

      // Fallback: Generate mock players with correct bet amount
      logger.debug('No real player data available, generating mock players', {
        roomId,
        currentPlayers,
        realBetAmount,
      });

      const players = [];
      for (let i = 0; i < currentPlayers; i++) {
        players.push({
          userId: `player_${roomId}_${i + 1}`,
          username: `Player${i + 1}`,
          position: i + 1,
          isReady: Math.random() > 0.5,
          betAmount: realBetAmount || 1000, // Use real bet amount if available
          joinedAt: new Date(Date.now() - Math.random() * 3600000).toISOString(),
          status: 'active',
        });
      }

      return players;
    } catch (error) {
      logger.logError('Failed to get players for room', error, { roomId });
      return this.getMockPlayersForRoom(roomId, currentPlayers, realBetAmount);
    }
  }

  /**
   * Fetch room info from service with current socket context
   * @param {string} roomId - Room ID
   * @param {Socket} socket - Current socket instance
   * @param {string} reason - Reason for fetching (e.g., 'player_subscribed')
   * @returns {Object|null} Room information with real player data
   */
  async fetchRoomInfoFromServiceWithContext(roomId, socket, reason = 'update') {
    try {
      logger.debug('Fetching room info with socket context', {
        roomId,
        socketId: socket.id,
        userId: socket.userId,
        reason
      });

      // Use the same method as fetchRoomInfoFromService to get room data with players
      // This ensures consistent behavior and uses room service player data
      const roomInfo = await this.fetchRoomInfoFromService(roomId, 0);

      if (!roomInfo) {
        return null;
      }

      logger.debug('Enhanced room info with real player data', {
        roomId,
        playerCount: roomInfo.players.length,
        realPlayers: roomInfo.players.map(p => ({ userId: p.userId, username: p.username })),
      });

      return roomInfo;
    } catch (error) {
      logger.logError('Failed to fetch room info with context', error, { roomId });
      return await this.fetchRoomInfoFromService(roomId, 0); // Fallback to base method
    }
  }



  /**
   * Get real players for a room from the authoritative room service
   * @param {string} roomId - Room ID
   * @param {Socket} socket - Current socket instance
   * @param {string} reason - Reason for fetching
   * @param {number} betAmount - Room bet amount
   * @returns {Array} Array of real player objects
   */
  async getRealPlayersForRoom(roomId, socket, reason, betAmount) {
    try {
      logger.debug('Getting real players for room from room service', {
        roomId,
        currentSocketId: socket.id,
        currentUserId: socket.userId,
        reason,
      });

      // Fetch current room state from the authoritative room service
      // Room service handles all position calculation and player management
      const roomServiceResponse = await this.fetchRoomDataFromRoomService(roomId);

      if (roomServiceResponse && roomServiceResponse.players) {
        logger.debug('Retrieved players from room service', {
          roomId,
          playerCount: roomServiceResponse.players.length,
          players: roomServiceResponse.players.map(p => ({
            userId: p.userId,
            username: p.username,
            position: p.position
          })),
        });
        return roomServiceResponse.players;
      }

      logger.warn('No players found in room service response', { roomId });
      return [];
    } catch (error) {
      logger.logError('Failed to get real players from room service', error, { roomId });
      return [];
    }
  }



  /**
   * Fetch room data from room service via gRPC
   * @param {string} roomId - Room ID
   * @returns {Object|null} Room data from room service
   */
  async fetchRoomDataFromRoomService(roomId) {
    try {
      // Use the existing gRPC room service client
      const roomServiceClient = require('../grpc/roomServiceClient');

      const roomInfo = await roomServiceClient.getRoomInfo(roomId);

      if (roomInfo) {
        logger.debug('Successfully fetched room data from room service via gRPC', {
          roomId,
          playerCount: roomInfo.players?.length || 0,
        });
        return roomInfo;
      }

      logger.warn('Room service returned no data via gRPC', { roomId });
      return null;
    } catch (error) {
      logger.warn('Failed to fetch room data from room service via gRPC', {
        roomId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Get subscribed players for a room from subscription service
   * @param {string} roomId - Room ID
   * @returns {Array} Array of subscribed players
   */
  getSubscribedPlayersForRoom(roomId) {
    try {
      // Get all subscriber socket IDs for this room
      const subscriberSocketIds = subscriptionService.getRoomSubscribers(roomId);

      if (!subscriberSocketIds || subscriberSocketIds.length === 0) {
        logger.debug('No subscribers found for room', { roomId });
        return [];
      }

      logger.debug('Found subscriber socket IDs', {
        roomId,
        subscriberCount: subscriberSocketIds.length,
        socketIds: subscriberSocketIds,
      });

      // For now, we can't easily get socket objects from socket IDs
      // This would require access to the Socket.IO server instance
      // TODO: Enhance subscription service to store user info with socket IDs

      // Return empty array to fall back to mock players with correct bet amounts
      // This is better than trying to access undefined properties
      logger.debug('Cannot resolve socket IDs to user data, falling back to mock players');
      return [];

    } catch (error) {
      logger.logError('Failed to get subscribed players', error, { roomId });
      return [];
    }
  }

  /**
   * Get fallback room info when manager-service is unavailable
   * @param {string} roomId - Room ID
   * @returns {Object} Fallback room information
   */
  getFallbackRoomInfo(roomId) {
    const playerCount = roomStateManager.getRoomPlayerCount ? roomStateManager.getRoomPlayerCount(roomId) : 0;

    logger.debug('Generating fallback room info', {
      roomId,
      source: 'fallback-mock',
    });

    return {
      roomId,
      name: `Room ${roomId}`,
      gameType: 'prizewheel',
      status: 'waiting',
      currentPlayers: playerCount,
      maxPlayers: 6,
      minPlayers: 2,
      betAmount: 1000,
      players: this.getMockPlayersForRoom(roomId, playerCount, 1000),
      isPrivate: false,
      autoStart: true,
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      canJoin: true,
      canStart: false,
    };
  }

  /**
   * Get mock players for a room (fallback implementation)
   * @param {string} roomId - Room ID
   * @param {number} playerCount - Number of players to generate
   * @param {number} realBetAmount - Real bet amount from room data
   * @returns {Array} Array of mock players
   */
  getMockPlayersForRoom(roomId, playerCount = 0, realBetAmount = null) {
    // Return empty array - player data should come from manager service
    return [];
  }

  /**
   * Auto-subscribe socket back to lobby after leaving room
   * @param {Socket} socket - Socket.io socket instance
   */
  async autoSubscribeToLobby(socket) {
    try {
      await subscriptionService.subscribeLobby(socket, socket.userId, socket.username);
      logger.debug('Auto-subscribed to lobby after leaving room', {
        socketId: socket.id,
        userId: socket.userId,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Auto-subscribe to lobby',
        socketId: socket.id,
        userId: socket.userId,
      });
    }
  }

  /**
   * Broadcast room update to all room subscribers
   * @param {string} roomId - Room ID
   * @param {Object} updateData - Update data to broadcast
   */
  async broadcastRoomUpdate(roomId, updateData) {
    try {
      // Broadcast to room subscribers
      this.socketService.io.to(this.getSocketRoomName(roomId)).emit('room_info_updated', updateData);

      // Also broadcast to lobby for room list updates
      this.socketService.io.to('lobby').emit('room_updated', updateData);

      logger.debug('Broadcasted room update', {
        roomId,
        action: updateData.action,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast room update',
        roomId,
        action: updateData.action,
      });
    }
  }

  /**
   * Route request to Room Orchestrator via Redis pub/sub
   * @param {string} eventType - Event type (join_room, leave_room)
   * @param {Object} payload - Request payload
   * @returns {Promise<Object>} Response from orchestrator
   */
  async routeToRoomOrchestrator(eventType, payload) {
    const correlationId = `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const responseChannel = `socket:responses:${correlationId}`;

    // Build standardized Redis message
    const message = {
      event: {
        type: eventType,
        payload: payload,
        timestamp: new Date().toISOString(),
      },
      metadata: {
        serviceId: 'socket-gateway',
        version: '1.0.0',
        correlationId: correlationId,
        responseChannel: responseChannel,
        priority: 1,
      },
    };

    logger.debug('Sending request to Room Orchestrator', {
      eventType,
      correlationId,
      responseChannel,
    });

    // Set up response listener
    const responsePromise = this.setupResponseListener(responseChannel, correlationId, 10000); // 10 second timeout

    // Publish request to Room Orchestrator
    await redisService.publish('room:orchestrator:requests', message);

    // Wait for response
    const response = await responsePromise;

    logger.debug('Received response from Room Orchestrator', {
      eventType,
      correlationId,
      success: response.success,
    });

    return response;
  }

  /**
   * Set up response listener for Redis request/response pattern
   * @param {string} responseChannel - Channel to listen for response
   * @param {string} correlationId - Correlation ID for tracking
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<Object>} Response promise
   */
  async setupResponseListener(responseChannel, correlationId, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutHandle = setTimeout(() => {
        redisService.unsubscribe(responseChannel);
        reject(new Error('Response timeout'));
      }, timeout);

      // Subscribe to response channel
      redisService.subscribe(responseChannel, (message) => {
        try {
          clearTimeout(timeoutHandle);
          redisService.unsubscribe(responseChannel);

          let parsedMessage;

          // Handle both string and already-parsed object messages
          if (typeof message === 'string') {
            // Message is a JSON string, parse it
            try {
              parsedMessage = JSON.parse(message);
            } catch (parseError) {
              logger.error('Failed to parse Redis message as JSON', {
                message: message.substring(0, 200), // Log first 200 chars
                correlationId,
                error: parseError.message,
              });
              reject(new Error('Invalid JSON response'));
              return;
            }
          } else if (typeof message === 'object' && message !== null) {
            // Message is already parsed by redisService.handleMessage
            parsedMessage = message;
          } else {
            logger.warn('Received invalid Redis message type', {
              messageType: typeof message,
              correlationId,
            });
            reject(new Error('Invalid message format'));
            return;
          }

          // Verify correlation ID
          if (parsedMessage.metadata?.correlationId === correlationId) {
            resolve(parsedMessage.event.payload);
          } else {
            logger.warn('Correlation ID mismatch', {
              expected: correlationId,
              received: parsedMessage.metadata?.correlationId,
            });
            reject(new Error('Correlation ID mismatch'));
          }
        } catch (error) {
          logger.error('Error in Redis response handler', {
            error: error.message,
            correlationId,
          });
          reject(error);
        }
      });
    });
  }

  /**
   * Handle broadcast events from orchestrator
   * @param {Object} broadcast - Broadcast event configuration
   */
  async handleBroadcastEvent(broadcast) {
    try {
      const { type, target, event, data } = broadcast;

      switch (type) {
        case 'room':
          // Broadcast to room subscribers
          this.socketService.io.to(`room:${target}`).emit(event, data);
          break;
        case 'lobby':
          // Broadcast to lobby subscribers
          this.socketService.io.to('lobby').emit(event, data);
          break;
        case 'user':
          // Find user's socket and emit
          const userSocket = this.socketService.getUserSocket(target);
          if (userSocket) {
            userSocket.emit(event, data);
          }
          break;
        case 'global':
          // Broadcast to all connected sockets
          this.socketService.io.emit(event, data);
          break;
        default:
          logger.warn('Unknown broadcast type', { type, target, event });
      }

      logger.debug('Broadcast event handled', {
        type,
        target,
        event,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle broadcast event',
        broadcast,
      });
    }
  }

  /**
   * Get socket room name for broadcasting
   * @param {string} roomId - Room ID
   * @returns {string} Socket room name
   */
  getSocketRoomName(roomId) {
    return `room_${roomId}`;
  }
}

module.exports = RoomHandlers;
