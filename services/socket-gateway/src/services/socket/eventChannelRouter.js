/**
 * Event Channel Router
 * 
 * Routes Redis channel events to appropriate handlers based on channel patterns.
 * Handles standardized channel routing for room, lobby, user, global, service, and monitoring events.
 */

const logger = require('../../utils/logger');
const { EVENT_TYPES, CHANNELS } = require('../../constants/events');

class EventChannelRouter {
  constructor() {
    this.socketService = null; // Will be injected
    
    // Define channel patterns and their handlers
    this.channelPatterns = [
      {
        pattern: /^room:([^:]+):events$/,
        handler: 'handleRoomEvent',
        description: 'Room-specific events'
      },
      {
        pattern: /^lobby:events$/,
        handler: 'handleLobbyEvent',
        description: 'Lobby events'
      },
      {
        pattern: /^user:([^:]+):events$/,
        handler: 'handleUserEvent',
        description: 'User-specific events'
      },
      {
        pattern: /^global:events$/,
        handler: 'handleGlobalEvent',
        description: 'Global system events'
      },
      {
        pattern: /^service:([^:]+):requests$/,
        handler: 'handleServiceRequest',
        description: 'Service-to-service requests'
      },
      {
        pattern: /^monitoring:events$/,
        handler: 'handleMonitoringEvent',
        description: 'Monitoring and health events'
      },
      {
        pattern: /^socket:events$/,
        handler: 'handleLegacySocketEvent',
        description: 'Legacy socket events (deprecated)'
      }
    ];
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  /**
   * Route channel message to appropriate handler
   * @param {string} channel - Redis channel name
   * @param {Object} message - Message payload
   * @returns {Promise<boolean>} True if message was handled
   */
  async routeChannelMessage(channel, message) {
    try {
      // Validate message structure
      if (!this.isValidMessage(message)) {
        logger.warn('Invalid message structure received', {
          channel,
          messageType: typeof message,
          hasEvent: !!message?.event,
        });
        return false;
      }

      const eventInfo = message.event;
      
      // Find matching pattern
      for (const { pattern, handler, description } of this.channelPatterns) {
        const match = channel.match(pattern);
        if (match) {
          logger.debug('Routing channel message', {
            channel,
            handler,
            eventType: eventInfo.type,
            description,
          });

          // Call the appropriate handler
          await this.callHandler(handler, channel, message, eventInfo, match);
          return true;
        }
      }

      // No pattern matched
      logger.debug('No handler found for channel', {
        channel,
        eventType: eventInfo.type,
        availablePatterns: this.channelPatterns.map(p => p.description),
      });

      return false;
    } catch (error) {
      logger.logError(error, {
        context: 'Event channel routing',
        channel,
        eventType: message?.event?.type,
      });
      return false;
    }
  }

  /**
   * Validate message structure
   * @param {Object} message - Message to validate
   * @returns {boolean} True if message is valid
   */
  isValidMessage(message) {
    return (
      message &&
      typeof message === 'object' &&
      message.event &&
      typeof message.event === 'object' &&
      typeof message.event.type === 'string'
    );
  }

  /**
   * Call the appropriate handler method
   * @param {string} handlerName - Name of handler method
   * @param {string} channel - Channel name
   * @param {Object} message - Full message
   * @param {Object} eventInfo - Event information
   * @param {Array} match - Regex match results
   */
  async callHandler(handlerName, channel, message, eventInfo, match) {
    if (!this.socketService) {
      throw new Error('Socket service not set in event channel router');
    }

    const handler = this.socketService[handlerName];
    if (typeof handler !== 'function') {
      throw new Error(`Handler method ${handlerName} not found in socket service`);
    }

    // Call handler with appropriate parameters
    await handler.call(this.socketService, channel, message, eventInfo, match);
  }

  /**
   * Get channel pattern information
   * @returns {Array} Array of channel pattern info
   */
  getChannelPatterns() {
    return this.channelPatterns.map(({ pattern, description }) => ({
      pattern: pattern.source,
      description,
    }));
  }

  /**
   * Check if channel matches any known pattern
   * @param {string} channel - Channel name to check
   * @returns {Object|null} Pattern info if matched, null otherwise
   */
  getChannelInfo(channel) {
    for (const patternInfo of this.channelPatterns) {
      const match = channel.match(patternInfo.pattern);
      if (match) {
        return {
          ...patternInfo,
          match,
          extractedParams: this.extractChannelParams(patternInfo.pattern, match),
        };
      }
    }
    return null;
  }

  /**
   * Extract parameters from channel match
   * @param {RegExp} pattern - Channel pattern
   * @param {Array} match - Regex match results
   * @returns {Object} Extracted parameters
   */
  extractChannelParams(pattern, match) {
    const params = {};
    
    // Extract common parameters based on pattern
    if (pattern.source.includes('room:([^:]+)')) {
      params.roomId = match[1];
    }
    if (pattern.source.includes('user:([^:]+)')) {
      params.userId = match[1];
    }
    if (pattern.source.includes('service:([^:]+)')) {
      params.targetService = match[1];
    }
    
    return params;
  }

  /**
   * Handle lobby events
   * @param {string} channel - Channel name
   * @param {Object} message - Event message
   * @param {Object} params - Extracted parameters
   */
  async handleLobbyEvent(channel, message, params) {
    try {
      logger.debug('Processing lobby event', {
        channel,
        eventType: message.event?.type,
        action: message.event?.payload?.action,
      });

      // Route to enhanced lobby handlers if available
      if (this.socketService?.enhancedLobbyHandlers) {
        await this.socketService.enhancedLobbyHandlers.handleRoomEvent(message);
      }

      // Also route to original lobby handlers for backward compatibility
      if (this.socketService?.lobbyHandlers) {
        // Convert standardized message to legacy format if needed
        const legacyMessage = this.convertToLegacyFormat(message);
        // Note: Original lobby handlers don't have a handleRoomEvent method
        // They handle events through Redis subscriptions directly
      }

      logger.debug('Lobby event processed successfully', {
        channel,
        eventType: message.event?.type,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle lobby event',
        channel,
        eventType: message.event?.type,
      });
    }
  }

  /**
   * Convert standardized message to legacy format
   * @param {Object} standardMessage - Standardized message
   * @returns {Object} Legacy format message
   */
  convertToLegacyFormat(standardMessage) {
    try {
      const { event, metadata } = standardMessage;

      // Convert lobby event types
      let legacyAction = event.payload?.action || 'updated';
      if (event.type?.startsWith('lobby:')) {
        legacyAction = event.type.replace('lobby:', '');
      }

      return {
        action: legacyAction,
        room: event.payload?.room,
        rooms: event.payload?.rooms,
        timestamp: event.timestamp,
        serviceId: metadata?.serviceId,
        correlationId: metadata?.correlationId,
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Convert to legacy format',
        message: standardMessage,
      });
      return standardMessage; // Return original if conversion fails
    }
  }

  /**
   * Get routing statistics
   * @returns {Object} Routing statistics
   */
  getRoutingStats() {
    return {
      totalPatterns: this.channelPatterns.length,
      patterns: this.channelPatterns.map(p => ({
        description: p.description,
        handler: p.handler,
      })),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Validate event type against known types
   * @param {string} eventType - Event type to validate
   * @param {string} category - Event category (ROOM, LOBBY, USER, etc.)
   * @returns {boolean} True if event type is known
   */
  isKnownEventType(eventType, category) {
    if (!EVENT_TYPES[category]) {
      return false;
    }
    
    return Object.values(EVENT_TYPES[category]).includes(eventType);
  }

  /**
   * Log unhandled event for monitoring
   * @param {string} channel - Channel name
   * @param {string} eventType - Event type
   * @param {string} category - Event category
   */
  logUnhandledEvent(channel, eventType, category) {
    logger.info('Unhandled event type detected', {
      channel,
      eventType,
      category,
      knownTypes: EVENT_TYPES[category] ? Object.values(EVENT_TYPES[category]) : [],
      suggestion: 'Consider adding handler or updating EVENT_TYPES constant',
    });
  }
}

module.exports = EventChannelRouter;
