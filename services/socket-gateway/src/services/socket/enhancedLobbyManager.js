/**
 * Enhanced Lobby Manager
 * 
 * Provides advanced lobby management with filtering, caching, and real-time updates.
 * Integrates with the enhanced Game Service lobby functionality.
 */

const logger = require('../../utils/logger');
const redisService = require('../redisService');

class EnhancedLobbyManager {
  constructor() {
    // Lobby state management
    this.lobbySubscribers = new Map(); // socketId -> { userId, username, filters, joinedAt }
    this.roomCache = new Map(); // roomId -> room data
    this.lastCacheUpdate = null;
    this.cacheTimeout = 30000; // 30 seconds cache timeout
    
    // Statistics tracking
    this.stats = {
      totalSubscriptions: 0,
      totalUnsubscriptions: 0,
      totalRoomUpdates: 0,
      averageResponseTime: 0,
      lastActivity: new Date(),
    };
  }

  /**
   * Subscribe user to lobby with enhanced filtering
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {Object} filters - Optional lobby filters
   * @returns {Object} Subscription result
   */
  async subscribeLobby(socket, userId, username, filters = {}) {
    const startTime = Date.now();
    
    try {
      const socketId = socket.id;
      
      logger.info('Enhanced lobby subscription', {
        socketId,
        userId,
        username,
        filters,
        currentSubscribers: this.lobbySubscribers.size,
      });

      // Store subscription with filters
      const subscription = {
        userId,
        username,
        socketId,
        filters: this.normalizeFilters(filters),
        joinedAt: new Date(),
        lastActivity: new Date(),
      };

      this.lobbySubscribers.set(socketId, subscription);

      // Join lobby socket room
      await this.joinLobbySocketRoom(socket);

      // Get initial room list with filters
      const roomList = await this.getFilteredRoomList(subscription.filters);

      // Send initial room list to user
      socket.emit('room_list_updated', {
        action: 'initial_load',
        rooms: roomList,
        filters: subscription.filters,
        timestamp: new Date().toISOString(),
        totalCount: roomList.length,
      });

      // Update statistics
      this.stats.totalSubscriptions++;
      this.stats.lastActivity = new Date();
      this.updateAverageResponseTime(startTime);

      logger.info('Enhanced lobby subscription successful', {
        socketId,
        userId,
        roomCount: roomList.length,
        subscriberCount: this.lobbySubscribers.size,
        responseTime: Date.now() - startTime,
      });

      return {
        success: true,
        channel: 'lobby',
        socketId,
        roomCount: roomList.length,
        filters: subscription.filters,
        timestamp: Date.now(),
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced lobby subscription',
        socketId: socket.id,
        userId,
        filters,
      });
      throw error;
    }
  }

  /**
   * Unsubscribe user from lobby
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @returns {Object} Unsubscription result
   */
  async unsubscribeLobby(socket, userId) {
    try {
      const socketId = socket.id;
      const subscription = this.lobbySubscribers.get(socketId);
      
      logger.info('Enhanced lobby unsubscription', {
        socketId,
        userId,
        hadSubscription: !!subscription,
        currentSubscribers: this.lobbySubscribers.size,
      });

      // Remove subscription
      this.lobbySubscribers.delete(socketId);

      // Leave lobby socket room
      await this.leaveLobbySocketRoom(socket);

      // Update statistics
      this.stats.totalUnsubscriptions++;
      this.stats.lastActivity = new Date();

      logger.info('Enhanced lobby unsubscription successful', {
        socketId,
        userId,
        subscriberCount: this.lobbySubscribers.size,
      });

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced lobby unsubscription',
        socketId: socket.id,
        userId,
      });
      throw error;
    }
  }

  /**
   * Update lobby filters for a subscribed user
   * @param {string} socketId - Socket ID
   * @param {Object} newFilters - New filter settings
   * @returns {Object} Update result
   */
  async updateLobbyFilters(socketId, newFilters) {
    try {
      const subscription = this.lobbySubscribers.get(socketId);
      if (!subscription) {
        throw new Error('User not subscribed to lobby');
      }

      // Update filters
      subscription.filters = this.normalizeFilters(newFilters);
      subscription.lastActivity = new Date();

      // Get updated room list with new filters
      const roomList = await this.getFilteredRoomList(subscription.filters);

      logger.info('Lobby filters updated', {
        socketId,
        userId: subscription.userId,
        newFilters: subscription.filters,
        roomCount: roomList.length,
      });

      return {
        success: true,
        rooms: roomList,
        filters: subscription.filters,
        totalCount: roomList.length,
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Update lobby filters',
        socketId,
        newFilters,
      });
      throw error;
    }
  }

  /**
   * Broadcast room update to relevant lobby subscribers
   * @param {Object} roomUpdate - Room update data
   * @param {Object} io - Socket.io server instance
   */
  async broadcastRoomUpdate(roomUpdate, io) {
    try {
      const { action, room, roomId } = roomUpdate;
      
      logger.debug('Broadcasting room update to lobby', {
        action,
        roomId: roomId || room?.id,
        subscriberCount: this.lobbySubscribers.size,
      });

      // Update room cache
      if (room) {
        this.updateRoomCache(room);
      } else if (action === 'room_deleted' && roomId) {
        this.removeFromRoomCache(roomId);
      }

      // Get subscribers who should receive this update
      const relevantSubscribers = this.getRelevantSubscribers(roomUpdate);

      // Send personalized updates to each relevant subscriber
      for (const subscription of relevantSubscribers) {
        const socket = io.sockets.sockets.get(subscription.socketId);
        if (socket) {
          const personalizedUpdate = await this.personalizeRoomUpdate(roomUpdate, subscription);
          socket.emit('room_list_updated', personalizedUpdate);
        }
      }

      // Update statistics
      this.stats.totalRoomUpdates++;
      this.stats.lastActivity = new Date();

      logger.debug('Room update broadcast completed', {
        action,
        roomId: roomId || room?.id,
        notifiedSubscribers: relevantSubscribers.length,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast room update',
        roomUpdate,
      });
    }
  }

  /**
   * Get lobby statistics
   * @returns {Object} Statistics object
   */
  getStatistics() {
    return {
      ...this.stats,
      currentSubscribers: this.lobbySubscribers.size,
      cacheSize: this.roomCache.size,
      lastCacheUpdate: this.lastCacheUpdate,
      uptime: Date.now() - this.stats.lastActivity.getTime(),
      subscriberDetails: Array.from(this.lobbySubscribers.values()).map(sub => ({
        userId: sub.userId,
        username: sub.username,
        joinedAt: sub.joinedAt,
        filters: sub.filters,
      })),
    };
  }

  /**
   * Normalize and validate lobby filters
   * @param {Object} filters - Raw filters
   * @returns {Object} Normalized filters
   */
  normalizeFilters(filters) {
    const normalized = {
      gameType: filters.gameType || null,
      status: filters.status || 'WAITING',
      hasSpace: filters.hasSpace !== undefined ? filters.hasSpace : null, // Default to null (show all rooms)
      minPlayers: filters.minPlayers || null,
      maxPlayers: filters.maxPlayers || null,
      betRange: filters.betRange || null,
      isPrivate: filters.isPrivate || false,
      sortBy: filters.sortBy || 'created_at',
      sortOrder: filters.sortOrder || 'desc',
    };

    // Validate bet range
    if (normalized.betRange) {
      normalized.betRange = {
        min: Math.max(0, normalized.betRange.min || 0),
        max: Math.max(normalized.betRange.min || 0, normalized.betRange.max || Number.MAX_SAFE_INTEGER),
      };
    }

    return normalized;
  }

  /**
   * Get filtered room list from cache or Game Service
   * @param {Object} filters - Lobby filters
   * @returns {Array} Filtered room list
   */
  async getFilteredRoomList(filters) {
    try {
      // Check cache first
      if (this.isCacheValid()) {
        return this.filterCachedRooms(filters);
      }

      // Request from Game Service
      const roomList = await this.requestRoomListFromGameService(filters);
      
      // Update cache
      this.updateRoomListCache(roomList);
      
      return roomList;
    } catch (error) {
      logger.logError(error, {
        context: 'Get filtered room list',
        filters,
      });
      
      // Fallback to cached data if available
      if (this.roomCache.size > 0) {
        logger.warn('Using cached room data due to service error');
        return this.filterCachedRooms(filters);
      }
      
      return [];
    }
  }

  /**
   * Request room list from Game Service
   * @param {Object} filters - Lobby filters
   * @returns {Array} Room list from Game Service
   */
  async requestRoomListFromGameService(filters) {
    try {
      const correlationId = `lobby-rooms-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const requestMessage = {
        event: {
          type: 'get_lobby_rooms',
          payload: {
            filters,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId,
          priority: 1,
        },
      };

      // For now, return empty array as this would require Game Service enhancement
      // TODO: Implement actual Game Service communication
      logger.debug('Would request room list from Game Service', {
        correlationId,
        filters,
      });

      return [];
    } catch (error) {
      logger.logError(error, {
        context: 'Request room list from Game Service',
        filters,
      });
      throw error;
    }
  }

  /**
   * Check if room cache is valid
   * @returns {boolean} True if cache is valid
   */
  isCacheValid() {
    if (!this.lastCacheUpdate) return false;
    return (Date.now() - this.lastCacheUpdate) < this.cacheTimeout;
  }

  /**
   * Filter cached rooms based on filters
   * @param {Object} filters - Lobby filters
   * @returns {Array} Filtered rooms
   */
  filterCachedRooms(filters) {
    const rooms = Array.from(this.roomCache.values());

    return rooms.filter(room => {
      // Game type filter
      if (filters.gameType && room.game_type !== filters.gameType) {
        return false;
      }

      // Status filter
      if (filters.status && room.status !== filters.status) {
        return false;
      }

      // Has space filter
      if (filters.hasSpace && !room.has_space) {
        return false;
      }

      // Private filter
      if (filters.isPrivate !== null && room.is_private !== filters.isPrivate) {
        return false;
      }

      // Bet range filter
      if (filters.betRange) {
        if (room.bet_amount < filters.betRange.min || room.bet_amount > filters.betRange.max) {
          return false;
        }
      }

      return true;
    }).sort((a, b) => {
      // Sort by specified field
      const field = filters.sortBy || 'created_at';
      const order = filters.sortOrder === 'asc' ? 1 : -1;

      if (a[field] < b[field]) return -1 * order;
      if (a[field] > b[field]) return 1 * order;
      return 0;
    });
  }

  /**
   * Update room cache with new room data
   * @param {Object} room - Room data
   */
  updateRoomCache(room) {
    if (room && room.id) {
      this.roomCache.set(room.id, {
        ...room,
        cached_at: Date.now(),
      });
      this.lastCacheUpdate = Date.now();
    }
  }

  /**
   * Update entire room list cache
   * @param {Array} rooms - Array of room data
   */
  updateRoomListCache(rooms) {
    this.roomCache.clear();
    rooms.forEach(room => this.updateRoomCache(room));
    this.lastCacheUpdate = Date.now();
  }

  /**
   * Remove room from cache
   * @param {string} roomId - Room ID to remove
   */
  removeFromRoomCache(roomId) {
    this.roomCache.delete(roomId);
  }

  /**
   * Get subscribers relevant to a room update
   * @param {Object} roomUpdate - Room update data
   * @returns {Array} Relevant subscribers
   */
  getRelevantSubscribers(roomUpdate) {
    const { action, room, roomId } = roomUpdate;
    const relevantSubscribers = [];

    for (const subscription of this.lobbySubscribers.values()) {
      // Check if this subscriber should receive this update based on their filters
      if (this.shouldReceiveUpdate(subscription, roomUpdate)) {
        relevantSubscribers.push(subscription);
      }
    }

    return relevantSubscribers;
  }

  /**
   * Check if a subscriber should receive a room update
   * @param {Object} subscription - Subscriber data
   * @param {Object} roomUpdate - Room update data
   * @returns {boolean} True if should receive update
   */
  shouldReceiveUpdate(subscription, roomUpdate) {
    const { room } = roomUpdate;

    if (!room) return true; // Send all non-room-specific updates

    const filters = subscription.filters;

    // Apply same filtering logic as getFilteredRoomList
    if (filters.gameType && room.game_type !== filters.gameType) {
      return false;
    }

    if (filters.status && room.status !== filters.status) {
      return false;
    }

    if (filters.hasSpace && !room.has_space) {
      return false;
    }

    if (filters.isPrivate !== null && room.is_private !== filters.isPrivate) {
      return false;
    }

    if (filters.betRange) {
      if (room.bet_amount < filters.betRange.min || room.bet_amount > filters.betRange.max) {
        return false;
      }
    }

    return true;
  }

  /**
   * Personalize room update for specific subscriber
   * @param {Object} roomUpdate - Room update data
   * @param {Object} subscription - Subscriber data
   * @returns {Object} Personalized update
   */
  async personalizeRoomUpdate(roomUpdate, subscription) {
    const personalizedUpdate = {
      ...roomUpdate,
      timestamp: new Date().toISOString(),
      subscriber: {
        userId: subscription.userId,
        filters: subscription.filters,
      },
    };

    // Add filtered room list for certain actions
    if (roomUpdate.action === 'room_created' || roomUpdate.action === 'room_deleted') {
      personalizedUpdate.rooms = await this.getFilteredRoomList(subscription.filters);
      personalizedUpdate.totalCount = personalizedUpdate.rooms.length;
    }

    return personalizedUpdate;
  }

  /**
   * Join lobby socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   */
  async joinLobbySocketRoom(socket) {
    try {
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          logger.warn('Socket.join lobby timeout, proceeding anyway', {
            socketId: socket.id,
          });
          resolve();
        }, 1000);

        socket.join('lobby', (err) => {
          clearTimeout(timeout);
          if (err) {
            logger.warn('Socket.join lobby error, proceeding anyway', {
              socketId: socket.id,
              error: err.message,
            });
          } else {
            logger.debug('Socket joined enhanced lobby channel', {
              socketId: socket.id,
            });
          }
          resolve();
        });
      });
    } catch (error) {
      logger.warn('Socket.join lobby failed, continuing anyway', {
        socketId: socket.id,
        error: error.message,
      });
    }
  }

  /**
   * Leave lobby socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   */
  async leaveLobbySocketRoom(socket) {
    try {
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          logger.warn('Socket.leave lobby timeout, proceeding anyway', {
            socketId: socket.id,
          });
          resolve();
        }, 1000);

        socket.leave('lobby', (err) => {
          clearTimeout(timeout);
          if (err) {
            logger.warn('Socket.leave lobby error, proceeding anyway', {
              socketId: socket.id,
              error: err.message,
            });
          } else {
            logger.debug('Socket left enhanced lobby channel', {
              socketId: socket.id,
            });
          }
          resolve();
        });
      });
    } catch (error) {
      logger.warn('Socket.leave lobby failed, continuing anyway', {
        socketId: socket.id,
        error: error.message,
      });
    }
  }

  /**
   * Update average response time statistic
   * @param {number} startTime - Request start time
   */
  updateAverageResponseTime(startTime) {
    const responseTime = Date.now() - startTime;
    this.stats.averageResponseTime = (this.stats.averageResponseTime + responseTime) / 2;
  }

  /**
   * Clean up subscriber on disconnect
   * @param {string} socketId - Socket ID to clean up
   * @returns {boolean} True if subscriber was removed
   */
  cleanupSubscriber(socketId) {
    const wasSubscribed = this.lobbySubscribers.has(socketId);
    this.lobbySubscribers.delete(socketId);

    if (wasSubscribed) {
      logger.debug('Cleaned up enhanced lobby subscriber', {
        socketId,
        remainingSubscribers: this.lobbySubscribers.size,
      });
    }

    return wasSubscribed;
  }

  /**
   * Get subscriber by socket ID
   * @param {string} socketId - Socket ID
   * @returns {Object|null} Subscriber data or null
   */
  getSubscriber(socketId) {
    return this.lobbySubscribers.get(socketId) || null;
  }

  /**
   * Check if socket is subscribed to lobby
   * @param {string} socketId - Socket ID
   * @returns {boolean} True if subscribed
   */
  isSubscribed(socketId) {
    return this.lobbySubscribers.has(socketId);
  }
}

module.exports = EnhancedLobbyManager;
