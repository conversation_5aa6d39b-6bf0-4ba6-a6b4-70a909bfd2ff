/**
 * Socket Event Handlers
 * 
 * Handles socket event registration and basic event handling.
 * Delegates complex business logic to specialized handlers.
 */

const logger = require('../../utils/logger');
const subscriptionService = require('../subscriptionService');

class SocketEventHandlers {
  constructor() {
    // Will be injected by the main socket service
    this.lobbyHandlers = null;
    this.roomHandlers = null;
    this.gameHandlers = null;
  }

  /**
   * Inject handler dependencies
   * @param {Object} handlers - Handler instances
   */
  setHandlers(handlers) {
    this.lobbyHandlers = handlers.lobbyHandlers;
    this.enhancedLobbyHandlers = handlers.enhancedLobbyHandlers;
    this.roomHandlers = handlers.roomHandlers;
    this.gameHandlers = handlers.gameHandlers;
  }

  /**
   * Setup all socket event handlers
   * @param {Socket} socket - Socket.io socket instance
   */
  setupEventHandlers(socket) {
    try {
      logger.debug('Starting socket event handlers setup', {
        socketId: socket.id,
        userId: socket.userId,
        handlersAvailable: {
          lobbyHandlers: !!this.lobbyHandlers,
          roomHandlers: !!this.roomHandlers,
          gameHandlers: !!this.gameHandlers,
        }
      });

      // Lobby events
      logger.debug('Setting up lobby events');
      this.setupLobbyEvents(socket);
      logger.debug('Lobby events setup complete');

      // Room events
      logger.debug('Setting up room events');
      this.setupRoomEvents(socket);
      logger.debug('Room events setup complete');

      // Game events
      logger.debug('Setting up game events');
      this.setupGameEvents(socket);
      logger.debug('Game events setup complete');

      // Utility events
      logger.debug('Setting up utility events');
      this.setupUtilityEvents(socket);
      logger.debug('Utility events setup complete');

      logger.debug('Socket event handlers configured', {
        socketId: socket.id,
        userId: socket.userId,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Setting up socket event handlers',
        socketId: socket.id,
        userId: socket.userId,
        handlersAvailable: {
          lobbyHandlers: !!this.lobbyHandlers,
          roomHandlers: !!this.roomHandlers,
          gameHandlers: !!this.gameHandlers,
        }
      });
    }
  }

  /**
   * Setup lobby-related events
   * @param {Socket} socket - Socket.io socket instance
   */
  setupLobbyEvents(socket) {
    // Original lobby events
    socket.on('subscribe_lobby', (callback) => {
      this.handleWithErrorWrapper(
        () => this.lobbyHandlers.handleSubscribeLobby(socket, callback),
        'subscribe_lobby',
        socket,
        callback
      );
    });

    socket.on('unsubscribe_lobby', (callback) => {
      this.handleWithErrorWrapper(
        () => this.lobbyHandlers.handleUnsubscribeLobby(socket, callback),
        'unsubscribe_lobby',
        socket,
        callback
      );
    });

    // Enhanced lobby events
    socket.on('subscribe_lobby_enhanced', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.enhancedLobbyHandlers.handleSubscribeLobby(socket, data, callback),
        'subscribe_lobby_enhanced',
        socket,
        callback
      );
    });

    socket.on('unsubscribe_lobby_enhanced', (callback) => {
      this.handleWithErrorWrapper(
        () => this.enhancedLobbyHandlers.handleUnsubscribeLobby(socket, callback),
        'unsubscribe_lobby_enhanced',
        socket,
        callback
      );
    });

    socket.on('update_lobby_filters', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.enhancedLobbyHandlers.handleUpdateLobbyFilters(socket, data, callback),
        'update_lobby_filters',
        socket,
        callback
      );
    });

    socket.on('get_lobby_stats', (callback) => {
      this.handleWithErrorWrapper(
        () => this.enhancedLobbyHandlers.handleGetLobbyStats(socket, callback),
        'get_lobby_stats',
        socket,
        callback
      );
    });
  }

  /**
   * Setup room-related events
   * @param {Socket} socket - Socket.io socket instance
   */
  setupRoomEvents(socket) {
    socket.on('join_room', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.roomHandlers.handleJoinRoom(socket, data, callback),
        'join_room',
        socket,
        callback,
        data
      );
    });

    socket.on('leave_room', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.roomHandlers.handleLeaveRoom(socket, data, callback),
        'leave_room',
        socket,
        callback,
        data
      );
    });

    socket.on('subscribe_room', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.roomHandlers.handleSubscribeRoom(socket, data, callback),
        'subscribe_room',
        socket,
        callback,
        data
      );
    });

    socket.on('unsubscribe_room', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.roomHandlers.handleUnsubscribeRoom(socket, data, callback),
        'unsubscribe_room',
        socket,
        callback,
        data
      );
    });
  }

  /**
   * Setup game-related events
   * @param {Socket} socket - Socket.io socket instance
   */
  setupGameEvents(socket) {
    // Player ready/unready events
    socket.on('player_ready', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handlePlayerReady(socket, data, callback),
        'player_ready',
        socket,
        callback,
        data
      );
    });

    socket.on('player_unready', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handlePlayerUnready(socket, data, callback),
        'player_unready',
        socket,
        callback,
        data
      );
    });

    // Specification format events
    socket.on('player_ready_spec', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handlePlayerReadySpec(socket, data, callback),
        'player_ready_spec',
        socket,
        callback,
        data
      );
    });

    socket.on('player_unready_spec', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handlePlayerUnreadySpec(socket, data, callback),
        'player_unready_spec',
        socket,
        callback,
        data
      );
    });

    // Game-specific events
    socket.on('select_wheel_color', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handleSelectWheelColor(socket, data, callback),
        'select_wheel_color',
        socket,
        callback,
        data
      );
    });

    socket.on('select_color_spec', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handleSelectColorSpec(socket, data, callback),
        'select_color_spec',
        socket,
        callback,
        data
      );
    });

    // Color state management events
    socket.on('get_available_colors', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handleGetAvailableColors(socket, data, callback),
        'get_available_colors',
        socket,
        callback,
        data
      );
    });

    socket.on('get_color_state_sync', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handleGetColorStateSync(socket, data, callback),
        'get_color_state_sync',
        socket,
        callback,
        data
      );
    });

    socket.on('select_position_spec', (data, callback) => {
      this.handleWithErrorWrapper(
        () => this.gameHandlers.handleSelectPositionSpec(socket, data, callback),
        'select_position_spec',
        socket,
        callback,
        data
      );
    });
  }

  /**
   * Setup utility events
   * @param {Socket} socket - Socket.io socket instance
   */
  setupUtilityEvents(socket) {
    // Heartbeat/ping events
    socket.on('ping', (callback) => {
      this.handlePing(socket, callback);
    });

    // Generic message handler
    socket.on('message', (data) => {
      this.handleGenericMessage(socket, data);
    });
  }

  /**
   * Handle ping events
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   */
  handlePing(socket, callback) {
    try {
      const pongData = {
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      };

      if (callback) {
        callback(pongData);
      } else {
        socket.emit('pong', pongData);
      }

      logger.debug('Ping handled', {
        socketId: socket.id,
        userId: socket.userId,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Ping handling',
        socketId: socket.id,
        userId: socket.userId,
      });
    }
  }

  /**
   * Handle generic messages
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Message data
   */
  handleGenericMessage(socket, data) {
    try {
      logger.debug('Generic message received', {
        socketId: socket.id,
        userId: socket.userId,
        messageType: data.type,
        hasPayload: !!data.payload,
      });

      // Echo back for now - can be extended for specific message types
      socket.emit('message_ack', {
        received: true,
        timestamp: new Date().toISOString(),
        originalType: data.type,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Generic message handling',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });
    }
  }

  /**
   * Wrapper for handling events with error catching
   * @param {Function} handler - Event handler function
   * @param {string} eventName - Event name for logging
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   * @param {Object} data - Event data
   */
  async handleWithErrorWrapper(handler, eventName, socket, callback, data = null) {
    try {
      await handler();
    } catch (error) {
      logger.logError(error, {
        context: `Socket event: ${eventName}`,
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: `Failed to handle ${eventName}`,
        code: `${eventName.toUpperCase()}_FAILED`,
        timestamp: new Date().toISOString(),
      };

      if (callback) {
        callback(errorResponse);
      }
    }
  }
}

module.exports = SocketEventHandlers;
