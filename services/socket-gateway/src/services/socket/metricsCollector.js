/**
 * Socket Metrics Collector
 * 
 * Collects and manages socket-related metrics and statistics.
 * Provides performance monitoring and health check capabilities.
 */

const logger = require('../../utils/logger');

class SocketMetrics {
  constructor() {
    this.metrics = {
      // Connection metrics
      totalConnections: 0,
      activeConnections: 0,
      peakConnections: 0,
      connectionErrors: 0,
      
      // Message metrics
      totalMessages: 0,
      messagesPerSecond: 0,
      messageErrors: 0,
      
      // Room metrics
      activeRooms: 0,
      totalRoomJoins: 0,
      totalRoomLeaves: 0,
      
      // Performance metrics
      averageResponseTime: 0,
      lastResponseTime: 0,
      
      // Error metrics
      totalErrors: 0,
      errorRate: 0,
    };

    this.responseTimes = [];
    this.messageTimestamps = [];
    this.maxResponseTimeHistory = 100;
    this.maxMessageHistory = 1000;

    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  /**
   * Record a new connection
   */
  recordConnection() {
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;
    
    if (this.metrics.activeConnections > this.metrics.peakConnections) {
      this.metrics.peakConnections = this.metrics.activeConnections;
    }

    logger.debug('Connection recorded', {
      totalConnections: this.metrics.totalConnections,
      activeConnections: this.metrics.activeConnections,
    });
  }

  /**
   * Record a disconnection
   */
  recordDisconnection() {
    this.metrics.activeConnections = Math.max(0, this.metrics.activeConnections - 1);

    logger.debug('Disconnection recorded', {
      activeConnections: this.metrics.activeConnections,
    });
  }

  /**
   * Record a connection error
   */
  recordConnectionError() {
    this.metrics.connectionErrors++;
    this.metrics.totalErrors++;
    this.updateErrorRate();

    logger.debug('Connection error recorded', {
      connectionErrors: this.metrics.connectionErrors,
      totalErrors: this.metrics.totalErrors,
    });
  }

  /**
   * Record a message
   * @param {string} eventType - Type of message/event
   */
  recordMessage(eventType = 'unknown') {
    this.metrics.totalMessages++;
    this.messageTimestamps.push({
      timestamp: Date.now(),
      eventType,
    });

    // Keep only recent messages for rate calculation
    if (this.messageTimestamps.length > this.maxMessageHistory) {
      this.messageTimestamps = this.messageTimestamps.slice(-this.maxMessageHistory);
    }

    this.updateMessagesPerSecond();

    logger.debug('Message recorded', {
      eventType,
      totalMessages: this.metrics.totalMessages,
      messagesPerSecond: this.metrics.messagesPerSecond,
    });
  }

  /**
   * Record a message error
   * @param {string} eventType - Type of message/event that failed
   */
  recordMessageError(eventType = 'unknown') {
    this.metrics.messageErrors++;
    this.metrics.totalErrors++;
    this.updateErrorRate();

    logger.debug('Message error recorded', {
      eventType,
      messageErrors: this.metrics.messageErrors,
      totalErrors: this.metrics.totalErrors,
    });
  }

  /**
   * Record response time
   * @param {number} responseTime - Response time in milliseconds
   */
  recordResponseTime(responseTime) {
    this.metrics.lastResponseTime = responseTime;
    this.responseTimes.push(responseTime);

    // Keep only recent response times
    if (this.responseTimes.length > this.maxResponseTimeHistory) {
      this.responseTimes = this.responseTimes.slice(-this.maxResponseTimeHistory);
    }

    this.updateAverageResponseTime();

    logger.debug('Response time recorded', {
      responseTime,
      averageResponseTime: this.metrics.averageResponseTime,
    });
  }

  /**
   * Record room activity
   * @param {string} action - 'join' or 'leave'
   * @param {number} activeRooms - Current number of active rooms
   */
  recordRoomActivity(action, activeRooms = null) {
    if (action === 'join') {
      this.metrics.totalRoomJoins++;
    } else if (action === 'leave') {
      this.metrics.totalRoomLeaves++;
    }

    if (activeRooms !== null) {
      this.metrics.activeRooms = activeRooms;
    }

    logger.debug('Room activity recorded', {
      action,
      activeRooms: this.metrics.activeRooms,
      totalJoins: this.metrics.totalRoomJoins,
      totalLeaves: this.metrics.totalRoomLeaves,
    });
  }

  /**
   * Update messages per second calculation
   */
  updateMessagesPerSecond() {
    const now = Date.now();
    const oneSecondAgo = now - 1000;
    
    const recentMessages = this.messageTimestamps.filter(
      msg => msg.timestamp > oneSecondAgo
    );
    
    this.metrics.messagesPerSecond = recentMessages.length;
  }

  /**
   * Update average response time
   */
  updateAverageResponseTime() {
    if (this.responseTimes.length === 0) {
      this.metrics.averageResponseTime = 0;
      return;
    }

    const sum = this.responseTimes.reduce((acc, time) => acc + time, 0);
    this.metrics.averageResponseTime = Math.round(sum / this.responseTimes.length);
  }

  /**
   * Update error rate calculation
   */
  updateErrorRate() {
    const totalOperations = this.metrics.totalMessages + this.metrics.totalConnections;
    if (totalOperations === 0) {
      this.metrics.errorRate = 0;
      return;
    }

    this.metrics.errorRate = Math.round((this.metrics.totalErrors / totalOperations) * 100 * 100) / 100;
  }

  /**
   * Get current metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  /**
   * Get detailed metrics with additional statistics
   * @returns {Object} Detailed metrics
   */
  getDetailedMetrics() {
    const baseMetrics = this.getMetrics();
    
    return {
      ...baseMetrics,
      responseTimeStats: this.getResponseTimeStats(),
      messageStats: this.getMessageStats(),
      healthStatus: this.getHealthStatus(),
    };
  }

  /**
   * Get response time statistics
   * @returns {Object} Response time stats
   */
  getResponseTimeStats() {
    if (this.responseTimes.length === 0) {
      return {
        min: 0,
        max: 0,
        median: 0,
        p95: 0,
        p99: 0,
      };
    }

    const sorted = [...this.responseTimes].sort((a, b) => a - b);
    const length = sorted.length;

    return {
      min: sorted[0],
      max: sorted[length - 1],
      median: sorted[Math.floor(length / 2)],
      p95: sorted[Math.floor(length * 0.95)],
      p99: sorted[Math.floor(length * 0.99)],
    };
  }

  /**
   * Get message statistics
   * @returns {Object} Message stats
   */
  getMessageStats() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const fiveMinutesAgo = now - 300000;

    const lastMinute = this.messageTimestamps.filter(msg => msg.timestamp > oneMinuteAgo).length;
    const lastFiveMinutes = this.messageTimestamps.filter(msg => msg.timestamp > fiveMinutesAgo).length;

    return {
      lastMinute,
      lastFiveMinutes,
      averagePerMinute: Math.round(lastFiveMinutes / 5),
    };
  }

  /**
   * Get health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    const isHealthy = this.metrics.errorRate < 5 && this.metrics.averageResponseTime < 1000;
    
    return {
      status: isHealthy ? 'healthy' : 'degraded',
      errorRate: this.metrics.errorRate,
      averageResponseTime: this.metrics.averageResponseTime,
      activeConnections: this.metrics.activeConnections,
    };
  }

  /**
   * Reset metrics (useful for testing)
   */
  reset() {
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      peakConnections: 0,
      connectionErrors: 0,
      totalMessages: 0,
      messagesPerSecond: 0,
      messageErrors: 0,
      activeRooms: 0,
      totalRoomJoins: 0,
      totalRoomLeaves: 0,
      averageResponseTime: 0,
      lastResponseTime: 0,
      totalErrors: 0,
      errorRate: 0,
    };

    this.responseTimes = [];
    this.messageTimestamps = [];

    logger.info('Socket metrics reset');
  }

  /**
   * Start periodic cleanup of old data
   */
  startPeriodicCleanup() {
    setInterval(() => {
      this.cleanupOldData();
    }, 60000); // Clean up every minute
  }

  /**
   * Clean up old data to prevent memory leaks
   */
  cleanupOldData() {
    const now = Date.now();
    const tenMinutesAgo = now - 600000;

    // Clean up old message timestamps
    this.messageTimestamps = this.messageTimestamps.filter(
      msg => msg.timestamp > tenMinutesAgo
    );

    // Update messages per second after cleanup
    this.updateMessagesPerSecond();

    logger.debug('Metrics cleanup completed', {
      messageTimestamps: this.messageTimestamps.length,
      responseTimes: this.responseTimes.length,
    });
  }
}

module.exports = SocketMetrics;
