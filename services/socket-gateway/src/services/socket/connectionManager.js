/**
 * Socket Connection Manager
 * 
 * Handles socket connection lifecycle, authentication, and basic connection management.
 * Follows single responsibility principle and keeps file under 200 lines.
 */

const logger = require('../../utils/logger');
const subscriptionService = require('../subscriptionService');
const redisService = require('../redisService');

class SocketConnectionManager {
  constructor() {
    this.connectedUsers = new Map(); // userId -> Set of socket IDs
    this.connectionCount = 0;
    this.redisService = redisService;
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      totalMessages: 0,
      errorCount: 0,
    };
  }

  /**
   * Handle new socket connection
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} setupEventHandlers - Function to setup event handlers
   * @param {Function} fetchUserBalance - Function to fetch user balance
   */
  async handleConnection(socket, setupEventHandlers, fetchUserBalance) {
    try {
      const { userId, username, role } = socket;

      // Debug logging for connection details
      logger.info('Socket connection established', {
        socketId: socket.id,
        userId,
        username,
        role,
        sessionId: socket.sessionId,
        clientAddress: socket.handshake.address,
        userAgent: socket.handshake.headers['user-agent'],
      });

      // Update connection tracking
      this.updateConnectionMetrics(userId, socket.id);

      // Set up event handlers FIRST so socket can handle events immediately
      setupEventHandlers(socket);

      // Fetch user balance (this can be slow/timeout, so do it after event handlers)
      let balance = 0;
      try {
        balance = await fetchUserBalance(userId, socket.token);
      } catch (error) {
        logger.logError(error, {
          context: 'Fetching user balance on connection',
          socketId: socket.id,
          userId,
        });
        // Continue with default balance of 0 if fetch fails
      }

      // Send connection acknowledgment
      const connectAckData = {
        userId,
        username,
        sessionId: socket.sessionId,
        serverTime: new Date().toISOString(),
        balance,
      };

      socket.emit('connect_ack', connectAckData);

      logger.info('Connection acknowledgment sent', {
        socketId: socket.id,
        userId,
        balance,
      });

      // Set up disconnection handler
      socket.on('disconnect', (reason) => {
        this.handleDisconnection(socket, reason);
      });

      // Set up error handler
      socket.on('error', (error) => {
        this.handleSocketError(socket, error);
      });

      logger.logSocketEvent('connect', socket.id, userId, {
        username,
        role,
        totalConnections: this.connectionCount,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Socket connection handling',
        socketId: socket.id,
        userId: socket.userId,
      });

      // Emit error to client and disconnect
      socket.emit('error', {
        code: 'CONNECTION_ERROR',
        message: 'Connection handling failed',
        timestamp: new Date().toISOString(),
      });
      socket.disconnect(true);
    }
  }

  /**
   * Handle socket disconnection
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} reason - Disconnection reason
   */
  async handleDisconnection(socket, reason) {
    try {
      const { userId, username } = socket;

      logger.logSocketEvent('disconnect_start', socket.id, userId, {
        username,
        reason,
        totalConnections: this.connectionCount,
      });

      // Handle automatic room leave on disconnect
      await this.handleAutoRoomLeaveOnDisconnect(socket, reason);

      // Update connection tracking
      this.connectionCount--;
      this.metrics.activeConnections--;

      // Remove from user connections
      if (this.connectedUsers.has(userId)) {
        const userSockets = this.connectedUsers.get(userId);
        userSockets.delete(socket.id);

        if (userSockets.size === 0) {
          this.connectedUsers.delete(userId);
        }
      }

      // Clean up subscriptions
      subscriptionService.handleDisconnect(socket);

      logger.logSocketEvent('disconnect', socket.id, userId, {
        username,
        reason,
        totalConnections: this.connectionCount,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Socket disconnection handling',
        socketId: socket.id,
        userId: socket.userId,
        reason,
      });
    }
  }

  /**
   * Handle automatic room leave when socket disconnects
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} reason - Disconnection reason
   */
  async handleAutoRoomLeaveOnDisconnect(socket, reason) {
    try {
      const { userId, username } = socket;

      if (!userId) {
        return; // No user associated with socket
      }

      // Get current room subscription
      const subscription = subscriptionService.getUserSubscription(userId);

      if (!subscription || subscription.channel !== 'room') {
        return; // User not subscribed to any room
      }

      const roomId = subscription.roomId;

      logger.info('Auto-leaving room on disconnect', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        disconnectReason: reason,
      });

      // Use the room orchestrator directly via Redis pub/sub to avoid circular dependencies
      await this.routeToRoomOrchestrator('leave_room', {
        userId,
        username,
        roomId,
        socketId: socket.id,
        autoLeave: true, // Flag to indicate this is an automatic leave
        disconnectReason: reason,
      });

      logger.info('Auto-leave room request sent', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        disconnectReason: reason,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Auto room leave on disconnect',
        socketId: socket.id,
        userId: socket.userId,
        reason,
      });

      // Don't throw error - disconnection should continue even if room leave fails
    }
  }

  /**
   * Route request to Room Orchestrator via Redis pub/sub
   * @param {string} eventType - Event type (join_room, leave_room)
   * @param {Object} payload - Request payload
   * @returns {Promise<Object>} Response from orchestrator
   */
  async routeToRoomOrchestrator(eventType, payload) {
    const correlationId = `gw-disconnect-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const requestMessage = {
      event: {
        type: eventType,
        payload,
        timestamp: new Date().toISOString(),
      },
      metadata: {
        serviceId: 'socket-gateway',
        version: '1.0.0',
        correlationId,
        source: 'auto_disconnect',
      },
    };

    try {
      // Publish to room orchestrator channel (matching room service subscription)
      await this.redisService.publish('room:orchestrator:requests', JSON.stringify(requestMessage));

      logger.debug('Routed request to room orchestrator', {
        eventType,
        correlationId,
        payload: {
          userId: payload.userId,
          roomId: payload.roomId,
          autoLeave: payload.autoLeave,
        },
      });

      // For disconnect scenarios, we don't wait for response to avoid blocking
      return { success: true, correlationId };

    } catch (error) {
      logger.logError(error, {
        context: 'Room orchestrator routing',
        eventType,
        correlationId,
        payload,
      });

      throw error;
    }
  }

  /**
   * Handle socket errors
   * @param {Socket} socket - Socket.io socket instance
   * @param {Error} error - Error object
   */
  handleSocketError(socket, error) {
    this.metrics.errorCount++;

    logger.logError(error, {
      context: 'Socket error',
      socketId: socket.id,
      userId: socket.userId,
    });

    socket.emit('error', {
      code: 'SOCKET_ERROR',
      message: 'An error occurred',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Update connection metrics
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   */
  updateConnectionMetrics(userId, socketId) {
    this.connectionCount++;
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;

    // Track user connections
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId).add(socketId);
  }

  /**
   * Get connection metrics
   * @returns {Object} Connection metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      connectedUsers: this.connectedUsers.size,
      totalSockets: this.connectionCount,
    };
  }

  /**
   * Get connection statistics (alias for getMetrics for compatibility)
   * @returns {Object} Connection statistics
   */
  getConnectionStats() {
    return this.getMetrics();
  }

  /**
   * Get connected users for a specific user ID
   * @param {string} userId - User ID
   * @returns {Set|null} Set of socket IDs or null if user not connected
   */
  getUserSockets(userId) {
    return this.connectedUsers.get(userId) || null;
  }

  /**
   * Check if user is connected
   * @param {string} userId - User ID
   * @returns {boolean} True if user has active connections
   */
  isUserConnected(userId) {
    return this.connectedUsers.has(userId) && this.connectedUsers.get(userId).size > 0;
  }

  /**
   * Get total connection count
   * @returns {number} Total active connections
   */
  getConnectionCount() {
    return this.connectionCount;
  }

  /**
   * Clean up resources
   */
  cleanup() {
    this.connectedUsers.clear();
    this.connectionCount = 0;
    this.metrics.activeConnections = 0;
  }
}

module.exports = SocketConnectionManager;
