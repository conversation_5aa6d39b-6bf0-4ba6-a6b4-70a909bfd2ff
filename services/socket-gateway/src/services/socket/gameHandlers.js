/**
 * Game Handlers
 * 
 * Handles game-related socket events and operations.
 * Manages player ready states, game actions, and game-specific functionality.
 */

const logger = require('../../utils/logger');
const redisService = require('../redisService');
const ColorStateManager = require('../colorStateManager');

class GameHandlers {
  constructor() {
    this.socketService = null; // Will be injected
    this.colorStateManager = new ColorStateManager();
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  /**
   * Handle player ready event
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Ready data
   * @param {Function} callback - Response callback
   */
  async handlePlayerReady(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username, token } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Send ready request to Game Service
      await this.sendPlayerReadyToGameService(socket, roomId, true);

      logger.logSocketEvent('player_ready', socket.id, userId, {
        roomId,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Player ready',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to set player ready',
        code: 'PLAYER_READY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle player unready event
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Unready data
   * @param {Function} callback - Response callback
   */
  async handlePlayerUnready(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username, token } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Send unready request to Game Service
      await this.sendPlayerReadyToGameService(socket, roomId, false);

      logger.logSocketEvent('player_unready', socket.id, userId, {
        roomId,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Player unready',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to set player unready',
        code: 'PLAYER_UNREADY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle player ready spec event (specification format)
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Ready data
   * @param {Function} callback - Response callback
   */
  async handlePlayerReadySpec(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Send ready request to Game Service using spec format
      await this.sendPlayerReadySpecToGameService(socket, roomId, true);

      logger.logSocketEvent('player_ready_spec', socket.id, userId, {
        roomId,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Player ready spec',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to set player ready',
        code: 'PLAYER_READY_SPEC_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle player unready spec event (specification format)
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Unready data
   * @param {Function} callback - Response callback
   */
  async handlePlayerUnreadySpec(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId, username } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Send unready request to Game Service using spec format
      await this.sendPlayerReadySpecToGameService(socket, roomId, false);

      logger.logSocketEvent('player_unready_spec', socket.id, userId, {
        roomId,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Player unready spec',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to set player unready',
        code: 'PLAYER_UNREADY_SPEC_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle select wheel color event (Prize Wheel game)
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Color selection data
   * @param {Function} callback - Response callback
   */
  async handleSelectWheelColor(socket, data, callback) {
    try {
      const { roomId, color } = data;
      const { userId, username } = socket;

      if (!roomId || !color) {
        const error = {
          success: false,
          error: 'Room ID and color are required',
          code: 'INVALID_COLOR_SELECTION',
        };
        if (callback) callback(error);
        return;
      }

      // Send color selection to Game Service
      await this.sendColorSelectionToGameService(socket, roomId, color);

      logger.logSocketEvent('select_wheel_color', socket.id, userId, {
        roomId,
        color,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Select wheel color',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to select color',
        code: 'COLOR_SELECTION_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle select color spec event (specification format)
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Color selection data
   * @param {Function} callback - Response callback
   */
  async handleSelectColorSpec(socket, data, callback) {
    try {
      const { userId, username } = socket;

      // Extract color from different possible formats
      let color, roomId;

      if (data.payload && data.payload.color) {
        // Format: {"payload":{"color":"red"}}
        color = data.payload.color;
        roomId = data.payload.roomId || socket.currentRoomId;
      } else if (data.color) {
        // Format: {"roomId":"room_123","color":"red"}
        color = data.color;
        roomId = data.roomId || socket.currentRoomId;
      } else {
        const error = {
          success: false,
          error: 'Color is required in payload',
          code: 'INVALID_COLOR_SELECTION',
        };
        if (callback) callback(error);
        return;
      }

      // Get roomId from socket context if not provided
      if (!roomId) {
        roomId = await this.getCurrentRoomId(socket);
      }

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required - player must be in a room',
          code: 'NO_ROOM_CONTEXT',
        };
        if (callback) callback(error);
        return;
      }

      // Validate color
      if (!this.isValidWheelColor(color)) {
        const error = {
          success: false,
          error: `Invalid color: ${color}. Valid colors: red, blue, green, yellow, purple, orange, pink, teal`,
          code: 'INVALID_COLOR',
        };
        if (callback) callback(error);
        return;
      }

      logger.info('Processing color selection', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        color,
        dataFormat: data.payload ? 'payload' : 'direct',
      });

      // Send color selection to Game Service using spec format
      await this.sendColorSelectionSpecToGameService(socket, roomId, color);

      logger.logSocketEvent('select_color_spec', socket.id, userId, {
        roomId,
        color,
        username,
      });

      if (callback) {
        callback({
          success: true,
          data: {
            roomId,
            color,
            userId,
            timestamp: new Date().toISOString(),
          }
        });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Select color spec',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to select color',
        code: 'COLOR_SELECTION_SPEC_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle select position spec event (Amidakuji game)
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Position selection data
   * @param {Function} callback - Response callback
   */
  async handleSelectPositionSpec(socket, data, callback) {
    try {
      const { roomId, position } = data;
      const { userId, username } = socket;

      if (!roomId || position === undefined) {
        const error = {
          success: false,
          error: 'Room ID and position are required',
          code: 'INVALID_POSITION_SELECTION',
        };
        if (callback) callback(error);
        return;
      }

      // Send position selection to Game Service
      await this.sendPositionSelectionToGameService(socket, roomId, position);

      logger.logSocketEvent('select_position_spec', socket.id, userId, {
        roomId,
        position,
        username,
      });

      if (callback) {
        callback({ success: true });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Select position spec',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to select position',
        code: 'POSITION_SELECTION_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle get available colors request
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Request data
   * @param {Function} callback - Response callback
   */
  async handleGetAvailableColors(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Get current color state from Redis
      const colorState = await this.getColorStateFromRedis(roomId);

      // Generate available colors event
      const availableColorsEvent = this.colorStateManager.generateAvailableColorsEvent(colorState);

      logger.debug('Sending available colors to client', {
        socketId: socket.id,
        userId,
        roomId,
        availableCount: availableColorsEvent.data.availableCount,
      });

      if (callback) {
        callback({
          success: true,
          data: availableColorsEvent.data,
        });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Get available colors',
        socketId: socket.id,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to get available colors',
        code: 'GET_AVAILABLE_COLORS_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle get color state sync request
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Request data
   * @param {Function} callback - Response callback
   */
  async handleGetColorStateSync(socket, data, callback) {
    try {
      const { roomId } = data;
      const { userId } = socket;

      if (!roomId) {
        const error = {
          success: false,
          error: 'Room ID is required',
          code: 'ROOM_ID_REQUIRED',
        };
        if (callback) callback(error);
        return;
      }

      // Get current color state from Redis
      const colorState = await this.getColorStateFromRedis(roomId);

      // Generate comprehensive color state sync event
      const colorStateSyncEvent = this.colorStateManager.generateColorStateSyncEvent(colorState, roomId);

      logger.debug('Sending color state sync to client', {
        socketId: socket.id,
        userId,
        roomId,
        playerCount: colorStateSyncEvent.data.statistics.totalPlayers,
      });

      if (callback) {
        callback({
          success: true,
          data: colorStateSyncEvent.data,
        });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Get color state sync',
        socketId: socket.id,
        data,
      });

      const errorResponse = {
        success: false,
        error: 'Failed to get color state sync',
        code: 'GET_COLOR_STATE_SYNC_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Broadcast real-time color events to room
   * @param {string} roomId - Room ID
   * @param {string} userId - User ID who made the selection
   * @param {string} username - Username who made the selection
   * @param {string} colorId - Selected color ID
   * @param {Object} colorState - Updated color state
   */
  async broadcastColorEvents(roomId, userId, username, colorId, colorState) {
    try {
      if (!this.socketService || !this.socketService.io) {
        logger.warn('Socket service not available for broadcasting color events');
        return;
      }

      // Generate and broadcast color selection update event
      const colorSelectionEvent = this.colorStateManager.generateColorSelectionEvent(
        userId, username, colorId, colorState
      );

      if (colorSelectionEvent) {
        this.socketService.io.to(`room:${roomId}`).emit('color_selection_update', colorSelectionEvent);

        logger.debug('Broadcasted color selection update', {
          roomId,
          userId,
          colorId,
          eventType: 'color_selection_update',
        });
      }

      // Generate and broadcast available colors update
      const availableColorsEvent = this.colorStateManager.generateAvailableColorsEvent(colorState);
      this.socketService.io.to(`room:${roomId}`).emit('available_colors', availableColorsEvent);

      // Generate and broadcast comprehensive color state sync
      const colorStateSyncEvent = this.colorStateManager.generateColorStateSyncEvent(colorState, roomId);
      this.socketService.io.to(`room:${roomId}`).emit('color_state_sync', colorStateSyncEvent);

      logger.debug('Broadcasted all color events', {
        roomId,
        userId,
        colorId,
        events: ['color_selection_update', 'available_colors', 'color_state_sync'],
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast color events',
        roomId,
        userId,
        colorId,
      });
    }
  }

  /**
   * Get color state from Redis
   * @param {string} roomId - Room ID
   * @returns {Object} Color state object
   */
  async getColorStateFromRedis(roomId) {
    try {
      const cacheKey = `room:${roomId}:color_selections`;

      // Try to get from Redis (this would normally work when Redis is connected)
      // For now, return a default structure
      return {
        AvailableColors: this.colorStateManager.getAllColors(),
        PlayerColors: {},
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Get color state from Redis',
        roomId,
      });

      // Return default state on error
      return {
        AvailableColors: this.colorStateManager.getAllColors(),
        PlayerColors: {},
      };
    }
  }

  /**
   * Send player ready state to Game Service
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {boolean} isReady - Ready state
   */
  async sendPlayerReadyToGameService(socket, roomId, isReady) {
    try {
      const message = {
        event: {
          type: 'player_ready_change',
          payload: {
            roomId,
            userId: socket.userId,
            username: socket.username,
            isReady,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId: `ready-${socket.userId}-${Date.now()}`,
        },
      };

      await redisService.publish('game:requests', message);

      logger.debug('Sent player ready state to Game Service', {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        isReady,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Send player ready to Game Service',
        socketId: socket.id,
        roomId,
        isReady,
      });
      throw error;
    }
  }

  /**
   * Send player ready state to Game Service (spec format)
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {boolean} isReady - Ready state
   */
  async sendPlayerReadySpecToGameService(socket, roomId, isReady) {
    // TODO: Implement spec format communication
    return this.sendPlayerReadyToGameService(socket, roomId, isReady);
  }

  /**
   * Send color selection to Game Service
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {string} color - Selected color
   */
  async sendColorSelectionToGameService(socket, roomId, color) {
    try {
      const message = {
        event: {
          type: 'color_selection',
          payload: {
            roomId,
            userId: socket.userId,
            username: socket.username,
            color,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId: `color-${socket.userId}-${Date.now()}`,
        },
      };

      await redisService.publish('game:requests', message);

      logger.debug('Sent color selection to Game Service', {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        color,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Send color selection to Game Service',
        socketId: socket.id,
        roomId,
        color,
      });
      throw error;
    }
  }

  /**
   * Send color selection to Game Service (spec format)
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {string} color - Selected color
   */
  async sendColorSelectionSpecToGameService(socket, roomId, color) {
    // TODO: Implement spec format communication
    return this.sendColorSelectionToGameService(socket, roomId, color);
  }

  /**
   * Send position selection to Game Service
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   * @param {number} position - Selected position
   */
  async sendPositionSelectionToGameService(socket, roomId, position) {
    try {
      const message = {
        event: {
          type: 'position_selection',
          payload: {
            roomId,
            userId: socket.userId,
            username: socket.username,
            position,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId: `position-${socket.userId}-${Date.now()}`,
        },
      };

      await redisService.publish('game:requests', message);

      logger.debug('Sent position selection to Game Service', {
        socketId: socket.id,
        userId: socket.userId,
        roomId,
        position,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Send position selection to Game Service',
        socketId: socket.id,
        roomId,
        position,
      });
      throw error;
    }
  }

  /**
   * Get current room ID for a socket
   * @param {Socket} socket - Socket.io socket instance
   * @returns {string|null} Room ID or null if not in a room
   */
  async getCurrentRoomId(socket) {
    try {
      // Check if socket has currentRoomId property
      if (socket.currentRoomId) {
        return socket.currentRoomId;
      }

      // Check socket rooms (Socket.IO rooms)
      const rooms = Array.from(socket.rooms);
      const roomPattern = /^room:/;

      for (const room of rooms) {
        if (roomPattern.test(room)) {
          return room.replace('room:', '');
        }
      }

      return null;
    } catch (error) {
      logger.logError(error, {
        context: 'Get current room ID',
        socketId: socket.id,
      });
      return null;
    }
  }

  /**
   * Validate if a color is valid for wheel games
   * @param {string} color - Color to validate
   * @returns {boolean} True if valid
   */
  isValidWheelColor(color) {
    const validColors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'teal'];
    return validColors.includes(color.toLowerCase());
  }
}

module.exports = GameHandlers;
