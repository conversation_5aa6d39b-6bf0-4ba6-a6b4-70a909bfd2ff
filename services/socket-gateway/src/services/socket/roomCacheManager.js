/**
 * Room Cache Manager
 * 
 * Manages room information caching, updates, and synchronization.
 * Handles room list caching, deduplication, and efficient updates.
 */

const logger = require('../../utils/logger');
const redisService = require('../redisService');

class RoomCacheManager {
  constructor() {
    this.roomCache = new Map(); // roomId -> room data
    this.roomListCache = []; // Array of room objects
    this.lastRoomListUpdate = 0;
    this.roomListCacheTTL = 30000; // 30 seconds
    
    // Deduplication tracking for room_info_updated events
    this.recentRoomInfoUpdates = new Map(); // roomId -> { timestamp, hash }
    this.roomInfoDeduplicationTTL = 2000; // 2 seconds
    
    // Cache for recent room info from join process
    this.recentRoomInfo = new Map(); // roomId -> { room, players, timestamp, userId }
    this.recentRoomInfoTTL = 10000; // 10 seconds
    
    // Setup cleanup intervals
    this.setupCleanupIntervals();
  }

  /**
   * Setup cleanup intervals for cache maintenance
   */
  setupCleanupIntervals() {
    // Clean up old room info cache every 10 seconds
    setInterval(() => {
      this.cleanupOldRoomInfoCache();
    }, 10000);

    // Clean up deduplication cache every 30 seconds
    setInterval(() => {
      this.cleanupDeduplicationCache();
    }, 30000);
  }

  /**
   * Get current room list from cache or fetch if expired
   * @param {boolean} forceRefresh - Force refresh from source
   * @returns {Promise<Array>} Array of room objects
   */
  async getCurrentRoomList(forceRefresh = false) {
    const now = Date.now();
    const cacheExpired = (now - this.lastRoomListUpdate) > this.roomListCacheTTL;

    if (forceRefresh || cacheExpired || this.roomListCache.length === 0) {
      logger.debug('Refreshing room list cache', {
        forceRefresh,
        cacheExpired,
        cacheEmpty: this.roomListCache.length === 0,
        lastUpdate: this.lastRoomListUpdate,
      });

      await this.refreshRoomListCache();
    }

    return [...this.roomListCache]; // Return copy to prevent mutations
  }

  /**
   * Refresh room list cache from Redis or external source
   */
  async refreshRoomListCache() {
    try {
      // Try to get from Redis cache first
      const cachedList = await this.getRoomListFromRedis();
      
      if (cachedList && cachedList.length > 0) {
        this.roomListCache = cachedList;
        this.lastRoomListUpdate = Date.now();
        
        logger.debug('Room list refreshed from Redis cache', {
          roomCount: cachedList.length,
        });
        return;
      }

      // If no Redis cache, this would trigger a fetch from game service
      // For now, keep existing cache
      logger.debug('No room list found in Redis cache, keeping existing cache', {
        existingRoomCount: this.roomListCache.length,
      });
      
    } catch (error) {
      logger.logError(error, {
        context: 'Room list cache refresh',
      });
    }
  }

  /**
   * Get room list from Redis cache
   * @returns {Promise<Array|null>} Cached room list or null
   */
  async getRoomListFromRedis() {
    try {
      const cacheKey = 'socket_gateway:room_list';
      const cachedData = await redisService.get(cacheKey);
      
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        return parsed.rooms || [];
      }
      
      return null;
    } catch (error) {
      logger.logError(error, {
        context: 'Get room list from Redis',
      });
      return null;
    }
  }

  /**
   * Update room list cache in Redis
   * @param {Array} rooms - Array of room objects
   */
  async updateRoomListInRedis(rooms) {
    try {
      const cacheKey = 'socket_gateway:room_list';
      const cacheData = {
        rooms,
        timestamp: new Date().toISOString(),
        updatedBy: 'socket-gateway',
      };
      
      // Cache for 5 minutes
      await redisService.setex(cacheKey, 300, JSON.stringify(cacheData));
      
      logger.debug('Updated room list in Redis cache', {
        roomCount: rooms.length,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Update room list in Redis',
      });
    }
  }

  /**
   * Update or add room in cache
   * @param {Object} room - Room object
   * @param {string} action - Action type (created, updated, deleted, etc.)
   */
  async updateRoomInCache(room, action) {
    try {
      // Update individual room cache
      if (action === 'deleted') {
        this.roomCache.delete(room.id);
      } else {
        this.roomCache.set(room.id, { ...room, lastUpdated: Date.now() });
      }

      // Update room list cache
      let updatedRooms = [...this.roomListCache];

      if (action === 'created' || action === 'updated' || action === 'player_count_changed') {
        // Remove existing room if it exists, then add the new/updated one
        updatedRooms = updatedRooms.filter((r) => r.id !== room.id);

        // Only add rooms that are waiting and have space
        if (room.status === 'waiting' && room.playerCount < room.maxPlayers) {
          updatedRooms.push(room);
        }
      } else if (action === 'deleted' || action === 'started' || action === 'finished') {
        // Remove the room from cache
        updatedRooms = updatedRooms.filter((r) => r.id !== room.id);
      }

      // Update cache
      this.roomListCache = updatedRooms;
      this.lastRoomListUpdate = Date.now();

      // Update Redis cache
      await this.updateRoomListInRedis(updatedRooms);

      logger.debug('Updated room in cache', {
        roomId: room.id,
        action,
        totalRooms: updatedRooms.length,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Update room in cache',
        roomId: room.id,
        action,
      });
    }
  }

  /**
   * Get room from cache
   * @param {string} roomId - Room ID
   * @returns {Object|null} Room object or null if not found
   */
  getRoomFromCache(roomId) {
    return this.roomCache.get(roomId) || null;
  }

  /**
   * Cache recent room info from join process
   * @param {string} roomId - Room ID
   * @param {Object} roomInfo - Room information
   * @param {string} userId - User ID who joined
   */
  cacheRecentRoomInfo(roomId, roomInfo, userId) {
    this.recentRoomInfo.set(roomId, {
      ...roomInfo,
      timestamp: Date.now(),
      userId,
    });

    logger.debug('Cached recent room info', {
      roomId,
      userId,
    });
  }

  /**
   * Get recent room info from cache
   * @param {string} roomId - Room ID
   * @returns {Object|null} Recent room info or null
   */
  getRecentRoomInfo(roomId) {
    const info = this.recentRoomInfo.get(roomId);
    
    if (info && (Date.now() - info.timestamp) <= this.recentRoomInfoTTL) {
      return info;
    }
    
    // Remove expired info
    if (info) {
      this.recentRoomInfo.delete(roomId);
    }
    
    return null;
  }

  /**
   * Check if room info update should be deduplicated
   * @param {string} roomId - Room ID
   * @param {Object} roomInfo - Room information
   * @returns {boolean} True if should be deduplicated
   */
  shouldDeduplicateRoomInfo(roomId, roomInfo) {
    const hash = this.generateRoomInfoHash(roomInfo);
    const recent = this.recentRoomInfoUpdates.get(roomId);
    
    if (recent && recent.hash === hash) {
      const timeDiff = Date.now() - recent.timestamp;
      if (timeDiff < this.roomInfoDeduplicationTTL) {
        logger.debug('Deduplicating room info update', {
          roomId,
          timeDiff,
          hash,
        });
        return true;
      }
    }
    
    // Update deduplication cache
    this.recentRoomInfoUpdates.set(roomId, {
      timestamp: Date.now(),
      hash,
    });
    
    return false;
  }

  /**
   * Generate hash for room info deduplication
   * @param {Object} roomInfo - Room information
   * @returns {string} Hash string
   */
  generateRoomInfoHash(roomInfo) {
    // Create a simple hash based on key room properties
    const key = JSON.stringify({
      playerCount: roomInfo.playerCount,
      readyCount: roomInfo.readyCount,
      status: roomInfo.status,
      gameInProgress: roomInfo.gameInProgress,
    });
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString();
  }

  /**
   * Clean up old cached room info
   */
  cleanupOldRoomInfoCache() {
    if (!this.recentRoomInfo || this.recentRoomInfo.size === 0) return;

    const now = Date.now();
    let cleanedCount = 0;

    for (const [roomId, info] of this.recentRoomInfo.entries()) {
      if (now - info.timestamp > this.recentRoomInfoTTL) {
        this.recentRoomInfo.delete(roomId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('Cleaned up old cached room info', {
        cleanedCount,
        remaining: this.recentRoomInfo.size,
      });
    }
  }

  /**
   * Clean up deduplication cache
   */
  cleanupDeduplicationCache() {
    if (!this.recentRoomInfoUpdates || this.recentRoomInfoUpdates.size === 0) return;

    const now = Date.now();
    let cleanedCount = 0;

    for (const [roomId, info] of this.recentRoomInfoUpdates.entries()) {
      if (now - info.timestamp > this.roomInfoDeduplicationTTL * 5) { // Keep longer for safety
        this.recentRoomInfoUpdates.delete(roomId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('Cleaned up deduplication cache', {
        cleanedCount,
        remaining: this.recentRoomInfoUpdates.size,
      });
    }
  }

  /**
   * Clear all caches
   */
  clearAllCaches() {
    this.roomCache.clear();
    this.roomListCache = [];
    this.recentRoomInfo.clear();
    this.recentRoomInfoUpdates.clear();
    this.lastRoomListUpdate = 0;

    logger.info('Cleared all room caches');
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      roomCache: this.roomCache.size,
      roomListCache: this.roomListCache.length,
      recentRoomInfo: this.recentRoomInfo.size,
      recentRoomInfoUpdates: this.recentRoomInfoUpdates.size,
      lastRoomListUpdate: this.lastRoomListUpdate,
      cacheAge: Date.now() - this.lastRoomListUpdate,
      timestamp: new Date().toISOString(),
    };
  }
}

module.exports = RoomCacheManager;
