/**
 * Enhanced Lobby Handlers
 * 
 * Advanced lobby event handlers with filtering, caching, and real-time updates.
 * Integrates with EnhancedLobbyManager for better state management.
 */

const logger = require('../../utils/logger');
const EnhancedLobbyManager = require('./enhancedLobbyManager');
const redisService = require('../redisService');

class EnhancedLobbyHandlers {
  constructor() {
    this.socketService = null; // Will be injected
    this.lobbyManager = new EnhancedLobbyManager();
    
    // Event type mappings
    this.eventHandlers = {
      'room_created': this.handleRoomCreatedEvent.bind(this),
      'room_updated': this.handleRoomUpdatedEvent.bind(this),
      'room_deleted': this.handleRoomDeletedEvent.bind(this),
      'room_status_changed': this.handleRoomStatusChangedEvent.bind(this),
      'player_count_changed': this.handlePlayerCountChangedEvent.bind(this),
      'room_list_updated': this.handleRoomListUpdatedEvent.bind(this),
    };
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  /**
   * Set the manager service communicator
   * @param {Object} managerServiceCommunicator - Manager service communicator instance
   */
  setManagerServiceCommunicator(managerServiceCommunicator) {
    this.managerServiceCommunicator = managerServiceCommunicator;
  }

  /**
   * Handle enhanced lobby subscription with filters
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - Subscription data with optional filters
   * @param {Function} callback - Response callback
   */
  async handleSubscribeLobby(socket, data = {}, callback) {
    try {
      const { userId, username } = socket;
      const { filters } = data;

      logger.logSocketEvent('subscribe_lobby_enhanced', socket.id, userId, {
        username,
        filters,
      });

      // Use enhanced lobby manager
      const result = await this.lobbyManager.subscribeLobby(socket, userId, username, filters);

      // Send success response
      if (callback) {
        callback({ 
          success: true,
          roomCount: result.roomCount,
          filters: result.filters,
        });
      }

      // Request fresh room list from Manager Service
      await this.requestRoomListFromManagerService(userId, username, socket.id, filters);

      logger.info('Enhanced lobby subscription completed', {
        socketId: socket.id,
        userId,
        roomCount: result.roomCount,
        hasFilters: !!filters,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced subscribe lobby',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        error: 'Failed to subscribe to lobby',
        code: 'ENHANCED_SUBSCRIBE_LOBBY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle lobby unsubscription
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   */
  async handleUnsubscribeLobby(socket, callback) {
    try {
      const { userId, username } = socket;

      logger.logSocketEvent('unsubscribe_lobby_enhanced', socket.id, userId, {
        username,
      });

      // Use enhanced lobby manager
      await this.lobbyManager.unsubscribeLobby(socket, userId);

      // Send success response
      if (callback) {
        callback({ success: true });
      }

      logger.info('Enhanced lobby unsubscription completed', {
        socketId: socket.id,
        userId,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced unsubscribe lobby',
        socketId: socket.id,
        userId: socket.userId,
      });

      const errorResponse = {
        error: 'Failed to unsubscribe from lobby',
        code: 'ENHANCED_UNSUBSCRIBE_LOBBY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle lobby filter updates
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} data - New filter data
   * @param {Function} callback - Response callback
   */
  async handleUpdateLobbyFilters(socket, data, callback) {
    try {
      const { userId } = socket;
      const { filters } = data;

      logger.logSocketEvent('update_lobby_filters', socket.id, userId, {
        filters,
      });

      // Update filters using enhanced lobby manager
      const result = await this.lobbyManager.updateLobbyFilters(socket.id, filters);

      // Send updated room list to user
      socket.emit('room_list_updated', {
        action: 'filters_updated',
        rooms: result.rooms,
        filters: result.filters,
        totalCount: result.totalCount,
        timestamp: new Date().toISOString(),
      });

      // Send success response
      if (callback) {
        callback({ 
          success: true,
          roomCount: result.totalCount,
          filters: result.filters,
        });
      }

      logger.info('Lobby filters updated successfully', {
        socketId: socket.id,
        userId,
        newFilters: result.filters,
        roomCount: result.totalCount,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Update lobby filters',
        socketId: socket.id,
        userId: socket.userId,
        data,
      });

      const errorResponse = {
        error: 'Failed to update lobby filters',
        code: 'UPDATE_LOBBY_FILTERS_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle lobby statistics request
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   */
  async handleGetLobbyStats(socket, callback) {
    try {
      const { userId } = socket;

      logger.logSocketEvent('get_lobby_stats', socket.id, userId);

      // Get statistics from enhanced lobby manager
      const stats = this.lobbyManager.getStatistics();

      // Send statistics response
      if (callback) {
        callback({ 
          success: true,
          stats,
        });
      }

      logger.debug('Lobby statistics retrieved', {
        socketId: socket.id,
        userId,
        subscriberCount: stats.currentSubscribers,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Get lobby statistics',
        socketId: socket.id,
        userId: socket.userId,
      });

      const errorResponse = {
        error: 'Failed to get lobby statistics',
        code: 'GET_LOBBY_STATS_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle room events from Redis
   * @param {Object} eventData - Event data from Redis
   */
  async handleRoomEvent(eventData) {
    try {
      const { event } = eventData;
      const eventType = event.type;
      
      logger.debug('Processing room event for lobby', {
        eventType,
        hasPayload: !!event.payload,
      });

      // Route to appropriate handler
      const handler = this.eventHandlers[eventType];
      if (handler) {
        await handler(event.payload);
      } else {
        logger.warn('Unknown room event type for lobby', {
          eventType,
          availableHandlers: Object.keys(this.eventHandlers),
        });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room event for lobby',
        eventData,
      });
    }
  }

  /**
   * Handle room created event
   * @param {Object} payload - Event payload
   */
  async handleRoomCreatedEvent(payload) {
    try {
      logger.debug('Room created event for lobby', { payload });

      const roomUpdate = {
        action: 'room_created',
        room: payload.room,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Room created event broadcasted to lobby subscribers', {
        roomId: payload.room?.id,
        roomName: payload.room?.name,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room created event for lobby',
        payload,
      });
    }
  }

  /**
   * Handle room updated event
   * @param {Object} payload - Event payload
   */
  async handleRoomUpdatedEvent(payload) {
    try {
      logger.debug('Room updated event for lobby', { payload });

      const roomUpdate = {
        action: 'room_updated',
        room: payload.room,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Room updated event broadcasted to lobby subscribers', {
        roomId: payload.room?.id,
        roomName: payload.room?.name,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room updated event for lobby',
        payload,
      });
    }
  }

  /**
   * Handle room deleted event
   * @param {Object} payload - Event payload
   */
  async handleRoomDeletedEvent(payload) {
    try {
      logger.debug('Room deleted event for lobby', { payload });

      const roomUpdate = {
        action: 'room_deleted',
        roomId: payload.roomId,
        room: payload.room,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Room deleted event broadcasted to lobby subscribers', {
        roomId: payload.roomId,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room deleted event for lobby',
        payload,
      });
    }
  }

  /**
   * Handle room status changed event
   * @param {Object} payload - Event payload
   */
  async handleRoomStatusChangedEvent(payload) {
    try {
      logger.debug('Room status changed event for lobby', { payload });

      const roomUpdate = {
        action: 'room_status_changed',
        roomId: payload.roomId,
        status: payload.status,
        room: payload.room,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Room status changed event broadcasted to lobby subscribers', {
        roomId: payload.roomId,
        status: payload.status,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room status changed event for lobby',
        payload,
      });
    }
  }

  /**
   * Handle player count changed event
   * @param {Object} payload - Event payload
   */
  async handlePlayerCountChangedEvent(payload) {
    try {
      logger.debug('Player count changed event for lobby', { payload });

      const roomUpdate = {
        action: 'player_count_changed',
        roomId: payload.roomId,
        playerCount: payload.playerCount,
        room: payload.room,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Player count changed event broadcasted to lobby subscribers', {
        roomId: payload.roomId,
        playerCount: payload.playerCount,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle player count changed event for lobby',
        payload,
      });
    }
  }

  /**
   * Handle room list updated event
   * @param {Object} payload - Event payload
   */
  async handleRoomListUpdatedEvent(payload) {
    try {
      logger.debug('Room list updated event for lobby', { payload });

      const roomUpdate = {
        action: 'list_updated',
        rooms: payload.rooms,
        timestamp: payload.timestamp || new Date().toISOString(),
      };

      // Broadcast using enhanced lobby manager
      await this.lobbyManager.broadcastRoomUpdate(roomUpdate, this.socketService?.io);

      logger.debug('Room list updated event broadcasted to lobby subscribers', {
        roomCount: payload.rooms?.length || 0,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Handle room list updated event for lobby',
        payload,
      });
    }
  }

  /**
   * Request room list from Manager Service
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {string} socketId - Socket ID
   * @param {Object} filters - Optional filters
   */
  async requestRoomListFromManagerService(userId, username, socketId, filters = {}) {
    try {
      if (!this.managerServiceCommunicator) {
        logger.warn('Manager Service communicator not available', {
          userId,
          socketId,
        });
        return;
      }

      logger.debug('Requesting room list from Manager Service', {
        userId,
        socketId,
        filters,
      });

      // Get enhanced room list from Manager Service
      const response = await this.managerServiceCommunicator.getEnhancedRoomList(filters);

      if (response.success && response.rooms) {
        // Get the socket and check if still subscribed
        const socket = this.socketService?.io?.sockets?.sockets?.get(socketId);
        const subscriber = this.lobbyManager.getSubscriber(socketId);

        if (socket && subscriber) {
          // Filter rooms based on subscriber's filters
          const filteredRooms = this.filterRoomsForSubscriber(response.rooms, subscriber.filters);

          socket.emit('room_list_updated', {
            action: 'initial_load_enhanced',
            rooms: filteredRooms,
            filters: subscriber.filters,
            totalCount: filteredRooms.length,
            source: 'manager-service',
            timestamp: new Date().toISOString(),
          });

          logger.info('Sent enhanced initial room list from Manager Service', {
            socketId,
            userId,
            roomCount: filteredRooms.length,
            totalAvailable: response.rooms.length,
            hasFilters: Object.keys(subscriber.filters).length > 0,
          });
        }
      } else {
        logger.warn('Failed to get room list from Manager Service', {
          userId,
          socketId,
          error: response.error || 'Unknown error',
        });
      }

      const requestMessage = {
        event: {
          type: 'subscribe_lobby',
          payload: {
            userId,
            username,
            socketId,
            filters,
            enhanced: true,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway-enhanced',
          version: '2.0.0',
          correlationId,
          responseChannel,
          priority: 1,
        },
      };

      // Publish request to game service
      await redisService.publish('game:requests', requestMessage);

      logger.debug('Sent enhanced lobby subscription request to game service', {
        correlationId,
        userId,
        socketId,
        filters,
        responseChannel,
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Request room list from game service (enhanced)',
        userId,
        socketId,
        filters,
      });
      throw error;
    }
  }

  /**
   * Filter rooms for a specific subscriber based on their filters
   * @param {Array} rooms - Array of rooms
   * @param {Object} filters - Subscriber filters
   * @returns {Array} Filtered rooms
   */
  filterRoomsForSubscriber(rooms, filters) {
    if (!filters || Object.keys(filters).length === 0) {
      return rooms;
    }

    return rooms.filter(room => {
      // Game type filter
      if (filters.gameType && room.game_type !== filters.gameType) {
        return false;
      }

      // Status filter
      if (filters.status && room.status !== filters.status) {
        return false;
      }

      // Has space filter
      if (filters.hasSpace && !room.has_space) {
        return false;
      }

      // Private filter
      if (filters.isPrivate !== null && room.is_private !== filters.isPrivate) {
        return false;
      }

      // Bet range filter
      if (filters.betRange) {
        if (room.bet_amount < filters.betRange.min || room.bet_amount > filters.betRange.max) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Handle socket disconnect cleanup
   * @param {string} socketId - Socket ID to clean up
   */
  handleDisconnect(socketId) {
    const wasSubscribed = this.lobbyManager.cleanupSubscriber(socketId);

    if (wasSubscribed) {
      logger.info('Enhanced lobby subscriber cleaned up on disconnect', {
        socketId,
        remainingSubscribers: this.lobbyManager.getStatistics().currentSubscribers,
      });
    }

    return wasSubscribed;
  }

  /**
   * Get lobby manager instance (for testing/debugging)
   * @returns {EnhancedLobbyManager} Lobby manager instance
   */
  getLobbyManager() {
    return this.lobbyManager;
  }
}

module.exports = EnhancedLobbyHandlers;
