/**
 * <PERSON>bby Handlers
 * 
 * Handles lobby-related socket events and operations.
 * Manages lobby subscriptions, room list updates, and lobby-specific functionality.
 */

const logger = require('../../utils/logger');
const subscriptionService = require('../subscriptionService');
const redisService = require('../redisService');

class LobbyHandlers {
  constructor() {
    this.socketService = null; // Will be injected
  }

  /**
   * Set the socket service reference
   * @param {Object} socketService - Socket service instance
   */
  setSocketService(socketService) {
    this.socketService = socketService;
  }

  /**
   * Set the manager service communicator
   * @param {Object} managerServiceCommunicator - Manager service communicator instance
   */
  setManagerServiceCommunicator(managerServiceCommunicator) {
    this.managerServiceCommunicator = managerServiceCommunicator;
  }

  /**
   * Handle lobby subscription
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   */
  async handleSubscribeLobby(socket, callback) {
    try {
      const { userId, username } = socket;

      // Use subscription service for proper state management
      await subscriptionService.subscribeLobby(socket, userId, username);

      logger.logSocketEvent('subscribe_lobby', socket.id, userId, {
        username,
      });

      // Send success response FIRST
      if (callback) {
        callback({ success: true });
      }

      // Request current room list from Manager Service
      try {
        // Send subscribe_lobby request to Manager Service
        await this.sendSubscribeLobbyToManagerService(userId, username, socket.id);

        logger.debug('Sent subscribe_lobby request to manager service', {
          socketId: socket.id,
          userId,
        });

        // Manager service will respond with room_list_updated event
        // No fallback needed - game service should always respond
      } catch (requestError) {
        logger.logError(requestError, {
          context: 'Requesting room list from game service',
          socketId: socket.id,
          userId,
        });

        // Only fallback on Redis/network errors, not timeout
        // Check if user is still subscribed to lobby before sending
        const userSubscription = subscriptionService.getUserSubscription(userId);
        if (userSubscription && userSubscription.channel === 'lobby') {
          socket.emit('room_list_updated', {
            action: 'initial_load',
            rooms: [],
            timestamp: new Date().toISOString(),
            error: 'Failed to fetch room list',
          });

          logger.warn('Sent empty room list due to request error (lobby subscriber)', {
            socketId: socket.id,
            userId,
          });
        } else {
          logger.debug('Skipped empty room list - user not subscribed to lobby', {
            socketId: socket.id,
            userId,
            currentSubscription: userSubscription?.channel,
          });
        }
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe lobby',
        socketId: socket.id,
        userId: socket.userId,
      });

      const errorResponse = {
        error: 'Failed to subscribe to lobby',
        code: 'SUBSCRIBE_LOBBY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Handle lobby unsubscription
   * @param {Socket} socket - Socket.io socket instance
   * @param {Function} callback - Response callback
   */
  async handleUnsubscribeLobby(socket, callback) {
    try {
      const { userId, username } = socket;

      // Use subscription service for proper state management
      await subscriptionService.unsubscribeLobby(socket, userId);

      logger.logSocketEvent('unsubscribe_lobby', socket.id, userId, {
        username,
      });

      // Send success response
      if (callback) {
        callback({ success: true });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe lobby',
        socketId: socket.id,
        userId: socket.userId,
      });

      const errorResponse = {
        error: 'Failed to unsubscribe from lobby',
        code: 'UNSUBSCRIBE_LOBBY_FAILED',
      };

      if (callback) callback(errorResponse);
    }
  }

  /**
   * Send subscribe lobby request to Manager Service
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {string} socketId - Socket ID
   */
  async sendSubscribeLobbyToManagerService(userId, username, socketId) {
    try {
      if (!this.managerServiceCommunicator) {
        logger.warn('Manager Service communicator not available', {
          userId,
          socketId,
        });
        return;
      }

      logger.debug('Requesting room list from Manager Service', {
        userId,
        socketId,
      });

      // Get room list from Manager Service
      const response = await this.managerServiceCommunicator.getEnhancedRoomList({
        gameType: null,
        status: 'waiting', // Manager Service uses lowercase status
        hasSpace: null, // Show all rooms, including full ones
        page: 1,
        limit: 50,
      });

      if (response.success && response.rooms) {
        // Emit room list to the subscribing user
        const socket = this.socketService?.io?.sockets?.sockets?.get(socketId);
        if (socket) {
          socket.emit('room_list_updated', {
            action: 'subscribe',
            count: response.rooms.length,
            rooms: response.rooms,
            socketId,
            userId,
            username,
            source: 'manager-service',
            timestamp: new Date().toISOString(),
          });

          logger.info('Sent initial room list from Manager Service to lobby subscriber', {
            socketId,
            userId,
            username,
            roomCount: response.rooms.length,
            source: 'manager-service',
          });
        }
      } else {
        logger.warn('Failed to get room list from Manager Service', {
          userId,
          socketId,
          error: response.error || 'Unknown error',
        });
      }

    } catch (error) {
      logger.logError(error, {
        context: 'Send subscribe lobby to manager service',
        userId,
        socketId,
      });
      throw error;
    }
  }

  /**
   * Handle room created event
   * @param {Object} payload - Event payload
   */
  async handleRoomCreatedEvent(payload) {
    try {
      logger.debug('Room created event received', { payload });

      // Broadcast to all lobby subscribers
      if (this.socketService?.io) {
        this.socketService.io.to('lobby').emit('room_list_updated', {
          action: 'room_created',
          room: payload.room,
          timestamp: payload.timestamp || new Date().toISOString(),
        });

        logger.debug('Room created event broadcasted to lobby', {
          roomId: payload.room?.id,
          roomName: payload.room?.name,
        });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Handle room created event',
        payload,
      });
    }
  }

  /**
   * Handle room deleted event
   * @param {Object} payload - Event payload
   */
  async handleRoomDeletedEvent(payload) {
    try {
      logger.debug('Room deleted event received', { payload });

      // Broadcast to all lobby subscribers
      if (this.socketService?.io) {
        this.socketService.io.to('lobby').emit('room_list_updated', {
          action: 'room_deleted',
          roomId: payload.roomId,
          timestamp: payload.timestamp || new Date().toISOString(),
        });

        logger.debug('Room deleted event broadcasted to lobby', {
          roomId: payload.roomId,
        });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Handle room deleted event',
        payload,
      });
    }
  }

  /**
   * Handle room list updated event
   * @param {Object} payload - Event payload
   */
  async handleRoomListUpdatedEvent(payload) {
    try {
      logger.debug('Room list updated event received', { payload });

      // Broadcast to all lobby subscribers
      if (this.socketService?.io) {
        this.socketService.io.to('lobby').emit('room_list_updated', {
          action: 'list_updated',
          rooms: payload.rooms,
          timestamp: payload.timestamp || new Date().toISOString(),
        });

        logger.debug('Room list updated event broadcasted to lobby', {
          roomCount: payload.rooms?.length || 0,
        });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Handle room list updated event',
        payload,
      });
    }
  }

  /**
   * Handle room status changed event
   * @param {Object} payload - Event payload
   */
  async handleRoomStatusChangedEvent(payload) {
    try {
      logger.debug('Room status changed event received', { payload });

      // Broadcast to all lobby subscribers
      if (this.socketService?.io) {
        this.socketService.io.to('lobby').emit('room_list_updated', {
          action: 'room_status_changed',
          roomId: payload.roomId,
          status: payload.status,
          room: payload.room,
          timestamp: payload.timestamp || new Date().toISOString(),
        });

        logger.debug('Room status changed event broadcasted to lobby', {
          roomId: payload.roomId,
          status: payload.status,
        });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Handle room status changed event',
        payload,
      });
    }
  }
}

module.exports = LobbyHandlers;
