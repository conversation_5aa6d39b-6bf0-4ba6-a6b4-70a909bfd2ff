/**
 * Game Service Communicator
 * 
 * Handles all communication with the Game Service via Redis pub/sub and HTTP.
 * Manages requests, responses, timeouts, and error handling for game service interactions.
 */

const axios = require('axios');
const logger = require('../../utils/logger');
const redisService = require('../redisService');
const config = require('../../config');

class GameServiceCommunicator {
  constructor() {
    this.pendingRequests = new Map(); // correlationId -> { resolve, reject, timeout }
    this.defaultTimeout = 10000; // 10 seconds
    this.gameServiceUrl = process.env.GAME_SERVICE_URL || 'http://localhost:3002';
  }

  /**
   * Send request to Game Service via Redis pub/sub
   * @param {string} eventType - Type of event to send
   * @param {Object} payload - Event payload
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response from game service
   */
  async sendRedisRequest(eventType, payload, options = {}) {
    const {
      timeout = this.defaultTimeout,
      priority = 1,
      expectResponse = true,
    } = options;

    const correlationId = this.generateCorrelationId(eventType);
    const responseChannel = `game:responses:${correlationId}`;

    try {
      // Set up response listener if expecting response
      if (expectResponse) {
        await this.setupResponseListener(correlationId, responseChannel, timeout);
      }

      // Build request message
      const requestMessage = {
        event: {
          type: eventType,
          payload: {
            ...payload,
            timestamp: new Date().toISOString(),
          },
        },
        metadata: {
          serviceId: 'socket-gateway',
          version: '1.0.0',
          correlationId,
          responseChannel: expectResponse ? responseChannel : null,
          priority,
        },
      };

      // Send request
      await redisService.publish('game:requests', requestMessage);

      logger.debug('Sent Redis request to Game Service', {
        eventType,
        correlationId,
        expectResponse,
        timeout,
      });

      // Return promise that resolves when response is received
      if (expectResponse) {
        return this.pendingRequests.get(correlationId).promise;
      }

      return { success: true, correlationId };
    } catch (error) {
      // Clean up pending request
      this.cleanupPendingRequest(correlationId);
      
      logger.logError(error, {
        context: 'Game Service Redis request',
        eventType,
        correlationId,
      });
      
      throw error;
    }
  }

  /**
   * Send HTTP request to Game Service
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response from game service
   */
  async sendHttpRequest(method, endpoint, data = {}, options = {}) {
    const {
      timeout = this.defaultTimeout,
      headers = {},
      token = null,
    } = options;

    try {
      const requestConfig = {
        method,
        url: `${this.gameServiceUrl}${endpoint}`,
        timeout,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      // Add authorization header if token provided
      if (token) {
        requestConfig.headers.Authorization = `Bearer ${token}`;
      }

      // Add data for non-GET requests
      if (method !== 'GET' && Object.keys(data).length > 0) {
        requestConfig.data = data;
      } else if (method === 'GET' && Object.keys(data).length > 0) {
        requestConfig.params = data;
      }

      logger.debug('Sending HTTP request to Game Service', {
        method,
        endpoint,
        url: requestConfig.url,
        hasToken: !!token,
        timeout,
      });

      const response = await axios(requestConfig);

      logger.debug('Received HTTP response from Game Service', {
        method,
        endpoint,
        status: response.status,
        success: response.data?.success,
      });

      return response.data;
    } catch (error) {
      logger.logError(error, {
        context: 'Game Service HTTP request',
        method,
        endpoint,
        status: error.response?.status,
        message: error.response?.data?.message,
      });

      // Transform axios error to standard format
      if (error.response) {
        throw new Error(`Game Service HTTP ${error.response.status}: ${error.response.data?.message || 'Request failed'}`);
      } else if (error.request) {
        throw new Error('Game Service unreachable');
      } else {
        throw error;
      }
    }
  }

  /**
   * Setup response listener for Redis request
   * @param {string} correlationId - Request correlation ID
   * @param {string} responseChannel - Redis response channel
   * @param {number} timeout - Request timeout in milliseconds
   */
  async setupResponseListener(correlationId, responseChannel, timeout) {
    return new Promise((resolve, reject) => {
      // Set up timeout
      const timeoutId = setTimeout(() => {
        this.cleanupPendingRequest(correlationId);
        reject(new Error(`Game Service request timeout after ${timeout}ms`));
      }, timeout);

      // Store pending request
      this.pendingRequests.set(correlationId, {
        promise: new Promise((res, rej) => {
          this.pendingRequests.set(correlationId, {
            resolve: res,
            reject: rej,
            timeout: timeoutId,
            responseChannel,
          });
        }),
        resolve,
        reject,
        timeout: timeoutId,
        responseChannel,
      });

      // Set up Redis response listener
      redisService.subscribe(responseChannel, (message, channel) => {
        if (channel === responseChannel) {
          this.handleRedisResponse(correlationId, message);
        }
      });

      resolve();
    });
  }

  /**
   * Handle Redis response
   * @param {string} correlationId - Request correlation ID
   * @param {Object} message - Response message
   */
  handleRedisResponse(correlationId, message) {
    const pendingRequest = this.pendingRequests.get(correlationId);
    if (!pendingRequest) {
      logger.warn('Received response for unknown correlation ID', {
        correlationId,
      });
      return;
    }

    try {
      // Parse response if it's a string
      const response = typeof message === 'string' ? JSON.parse(message) : message;
      
      logger.debug('Received Redis response from Game Service', {
        correlationId,
        success: response.success,
      });

      // Resolve the pending request
      pendingRequest.resolve(response);
    } catch (error) {
      logger.logError(error, {
        context: 'Game Service Redis response parsing',
        correlationId,
      });
      
      pendingRequest.reject(new Error('Invalid response format from Game Service'));
    } finally {
      // Clean up
      this.cleanupPendingRequest(correlationId);
    }
  }

  /**
   * Clean up pending request
   * @param {string} correlationId - Request correlation ID
   */
  cleanupPendingRequest(correlationId) {
    const pendingRequest = this.pendingRequests.get(correlationId);
    if (pendingRequest) {
      // Clear timeout
      if (pendingRequest.timeout) {
        clearTimeout(pendingRequest.timeout);
      }
      
      // Unsubscribe from response channel
      if (pendingRequest.responseChannel) {
        redisService.unsubscribe(pendingRequest.responseChannel);
      }
      
      // Remove from pending requests
      this.pendingRequests.delete(correlationId);
    }
  }

  /**
   * Generate correlation ID for request tracking
   * @param {string} eventType - Event type
   * @returns {string} Unique correlation ID
   */
  generateCorrelationId(eventType) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${eventType}-${timestamp}-${random}`;
  }

  /**
   * Get pending request statistics
   * @returns {Object} Statistics about pending requests
   */
  getPendingRequestStats() {
    return {
      totalPending: this.pendingRequests.size,
      pendingRequests: Array.from(this.pendingRequests.keys()),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Clean up all pending requests (for shutdown)
   */
  cleanup() {
    logger.info('Cleaning up Game Service communicator', {
      pendingRequests: this.pendingRequests.size,
    });

    // Reject all pending requests
    for (const [correlationId, pendingRequest] of this.pendingRequests.entries()) {
      if (pendingRequest.reject) {
        pendingRequest.reject(new Error('Service shutting down'));
      }
      this.cleanupPendingRequest(correlationId);
    }

    this.pendingRequests.clear();
  }

  /**
   * Subscribe to lobby with enhanced filters
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {string} socketId - Socket ID
   * @param {Object} filters - Lobby filters
   * @returns {Promise<Object>} Subscription response
   */
  async subscribeLobbyEnhanced(userId, username, socketId, filters = {}) {
    try {
      const payload = {
        userId,
        username,
        socketId,
        filters,
        enhanced: true,
      };

      const response = await this.sendRedisRequest('subscribe_lobby_enhanced', payload, {
        timeout: 15000, // Longer timeout for enhanced requests
        priority: 1,
      });

      logger.debug('Enhanced lobby subscription response', {
        userId,
        socketId,
        success: response.success,
        roomCount: response.rooms?.length || 0,
      });

      return response;
    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced lobby subscription',
        userId,
        socketId,
        filters,
      });
      throw error;
    }
  }

  /**
   * Get filtered room list from Game Service
   * @param {Object} filters - Room filters
   * @returns {Promise<Object>} Room list response
   */
  async getFilteredRoomList(filters = {}) {
    try {
      const payload = {
        filters,
        enhanced: true,
      };

      const response = await this.sendRedisRequest('get_filtered_room_list', payload, {
        timeout: 10000,
        priority: 2,
      });

      logger.debug('Filtered room list response', {
        filters,
        success: response.success,
        roomCount: response.rooms?.length || 0,
      });

      return response;
    } catch (error) {
      logger.logError(error, {
        context: 'Get filtered room list',
        filters,
      });
      throw error;
    }
  }

  /**
   * Get lobby statistics from Game Service
   * @returns {Promise<Object>} Lobby statistics
   */
  async getLobbyStatistics() {
    try {
      const response = await this.sendRedisRequest('get_lobby_statistics', {}, {
        timeout: 8000,
        priority: 3,
      });

      logger.debug('Lobby statistics response', {
        success: response.success,
        stats: response.stats,
      });

      return response;
    } catch (error) {
      logger.logError(error, {
        context: 'Get lobby statistics',
      });
      throw error;
    }
  }

  /**
   * Get featured rooms from Game Service
   * @param {number} limit - Maximum number of featured rooms
   * @returns {Promise<Object>} Featured rooms response
   */
  async getFeaturedRooms(limit = 5) {
    try {
      const payload = {
        limit,
        enhanced: true,
      };

      const response = await this.sendRedisRequest('get_featured_rooms', payload, {
        timeout: 8000,
        priority: 2,
      });

      logger.debug('Featured rooms response', {
        limit,
        success: response.success,
        roomCount: response.rooms?.length || 0,
      });

      return response;
    } catch (error) {
      logger.logError(error, {
        context: 'Get featured rooms',
        limit,
      });
      throw error;
    }
  }

  /**
   * Search rooms in Game Service
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Search results
   */
  async searchRooms(query, filters = {}) {
    try {
      const payload = {
        query,
        filters,
        enhanced: true,
      };

      const response = await this.sendRedisRequest('search_rooms', payload, {
        timeout: 12000,
        priority: 2,
      });

      logger.debug('Room search response', {
        query,
        filters,
        success: response.success,
        roomCount: response.rooms?.length || 0,
      });

      return response;
    } catch (error) {
      logger.logError(error, {
        context: 'Search rooms',
        query,
        filters,
      });
      throw error;
    }
  }

  /**
   * Check Game Service health
   * @returns {Promise<Object>} Health status
   */
  async checkHealth() {
    try {
      const response = await this.sendHttpRequest('GET', '/health', {}, {
        timeout: 5000,
      });

      return {
        healthy: true,
        response,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

module.exports = GameServiceCommunicator;
