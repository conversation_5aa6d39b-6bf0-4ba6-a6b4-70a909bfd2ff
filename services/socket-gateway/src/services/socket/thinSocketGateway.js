/**
 * Thin Socket Gateway - Pure Gateway Pattern Implementation
 * 
 * RESPONSIBILITIES (ONLY):
 * 1. Connection management
 * 2. Authentication
 * 3. Message routing
 * 4. Response forwarding
 * 5. Basic metrics
 * 
 * NO BUSINESS LOGIC ALLOWED!
 */

const logger = require('../../utils/logger');
const MessageRouter = require('./messageRouter');
const ConnectionManager = require('./connectionManager');
const AuthenticationHandler = require('./authenticationHandler');
const MetricsCollector = require('./metricsCollector');

class ThinSocketGateway {
  constructor(io) {
    this.io = io;
    this.messageRouter = new MessageRouter();
    this.connectionManager = new ConnectionManager();
    this.authHandler = new AuthenticationHandler();
    this.metrics = new MetricsCollector();
    
    // Gateway should NOT have business logic services
    // NO room handlers, game handlers, state managers, etc.
  }

  /**
   * Initialize the gateway
   */
  async initialize() {
    logger.info('Initializing Thin Socket Gateway');
    
    // Set up socket connection handling
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    // Initialize components
    await this.messageRouter.initialize();
    await this.connectionManager.initialize();
    
    logger.info('Thin Socket Gateway initialized successfully');
  }

  /**
   * Handle new socket connection
   * @param {Socket} socket - Socket.io socket instance
   */
  async handleConnection(socket) {
    try {
      // 1. AUTHENTICATION (Gateway responsibility)
      const authResult = await this.authHandler.authenticate(socket);
      if (!authResult.success) {
        socket.emit('auth_error', authResult.error);
        socket.disconnect();
        return;
      }

      // 2. CONNECTION MANAGEMENT (Gateway responsibility)
      await this.connectionManager.addConnection(socket, authResult.user);

      // 3. METRICS (Gateway responsibility)
      this.metrics.recordConnection(authResult.user.userId);

      // 4. SET UP MESSAGE ROUTING (Gateway responsibility)
      this.setupMessageRouting(socket, authResult.user);

      // 5. SEND CONNECTION ACK (Gateway responsibility)
      socket.emit('connect_ack', {
        success: true,
        userId: authResult.user.userId,
        username: authResult.user.username,
        timestamp: new Date().toISOString(),
      });

      logger.info('Socket connected and authenticated', {
        socketId: socket.id,
        userId: authResult.user.userId,
        username: authResult.user.username,
      });

    } catch (error) {
      logger.error('Connection handling failed', {
        socketId: socket.id,
        error: error.message,
      });
      socket.disconnect();
    }
  }

  /**
   * Set up message routing for authenticated socket
   * @param {Socket} socket - Socket.io socket instance
   * @param {Object} user - Authenticated user data
   */
  setupMessageRouting(socket, user) {
    // Gateway ONLY routes messages - NO business logic processing

    // Room-related events -> Route to Room Orchestrator
    socket.on('join_room', (data, callback) => {
      this.routeMessage('room_orchestrator', 'join_room', socket, data, callback);
    });

    socket.on('leave_room', (data, callback) => {
      this.routeMessage('room_orchestrator', 'leave_room', socket, data, callback);
    });

    socket.on('subscribe_room', (data, callback) => {
      this.routeMessage('room_orchestrator', 'subscribe_room', socket, data, callback);
    });

    // Game-related events -> Route to Game Orchestrator
    socket.on('player_ready', (data, callback) => {
      this.routeMessage('game_orchestrator', 'player_ready', socket, data, callback);
    });

    socket.on('game_action', (data, callback) => {
      this.routeMessage('game_orchestrator', 'game_action', socket, data, callback);
    });

    socket.on('color_selection', (data, callback) => {
      this.routeMessage('game_orchestrator', 'color_selection', socket, data, callback);
    });

    // User-related events -> Route to User Service
    socket.on('get_balance', (data, callback) => {
      this.routeMessage('user_service', 'get_balance', socket, data, callback);
    });

    socket.on('update_profile', (data, callback) => {
      this.routeMessage('user_service', 'update_profile', socket, data, callback);
    });

    // Lobby events -> Route to Lobby Service
    socket.on('subscribe_lobby', (data, callback) => {
      this.routeMessage('lobby_service', 'subscribe_lobby', socket, data, callback);
    });

    socket.on('get_rooms', (data, callback) => {
      this.routeMessage('lobby_service', 'get_rooms', socket, data, callback);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket, user);
    });
  }

  /**
   * Route message to appropriate service (Gateway responsibility)
   * @param {string} service - Target service
   * @param {string} event - Event type
   * @param {Socket} socket - Socket instance
   * @param {Object} data - Event data
   * @param {Function} callback - Response callback
   */
  async routeMessage(service, event, socket, data, callback) {
    try {
      // Add gateway metadata
      const routingData = {
        ...data,
        _gateway: {
          socketId: socket.id,
          userId: socket.userId,
          username: socket.username,
          timestamp: new Date().toISOString(),
        },
      };

      // Record metrics
      this.metrics.recordMessage(service, event);

      // Route to service (NO business logic processing)
      const response = await this.messageRouter.route(service, event, routingData);

      // Forward response back to client
      if (callback) {
        callback(response);
      }

      // Handle any broadcast events from the response
      if (response.broadcast) {
        this.handleBroadcast(response.broadcast);
      }

    } catch (error) {
      logger.error('Message routing failed', {
        service,
        event,
        socketId: socket.id,
        error: error.message,
      });

      if (callback) {
        callback({
          success: false,
          error: 'Service temporarily unavailable',
          code: 'ROUTING_ERROR',
        });
      }
    }
  }

  /**
   * Handle socket disconnection
   * @param {Socket} socket - Socket instance
   * @param {Object} user - User data
   */
  async handleDisconnection(socket, user) {
    try {
      // 1. Remove from connection manager
      await this.connectionManager.removeConnection(socket.id);

      // 2. Record metrics
      this.metrics.recordDisconnection(user.userId);

      // 3. Notify services about disconnection (routing only)
      await this.messageRouter.route('connection_service', 'user_disconnected', {
        userId: user.userId,
        socketId: socket.id,
        timestamp: new Date().toISOString(),
      });

      logger.info('Socket disconnected', {
        socketId: socket.id,
        userId: user.userId,
        username: user.username,
      });

    } catch (error) {
      logger.error('Disconnection handling failed', {
        socketId: socket.id,
        error: error.message,
      });
    }
  }

  /**
   * Handle broadcast events from services
   * @param {Object} broadcast - Broadcast configuration
   */
  handleBroadcast(broadcast) {
    const { type, target, event, data } = broadcast;

    switch (type) {
      case 'room':
        this.io.to(`room:${target}`).emit(event, data);
        break;
      case 'lobby':
        this.io.to('lobby').emit(event, data);
        break;
      case 'user':
        // Find user's socket and emit
        const userSocket = this.connectionManager.getUserSocket(target);
        if (userSocket) {
          userSocket.emit(event, data);
        }
        break;
      case 'global':
        this.io.emit(event, data);
        break;
      default:
        logger.warn('Unknown broadcast type', { type, target, event });
    }
  }

  /**
   * Get gateway health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      connections: this.connectionManager.getConnectionCount(),
      metrics: this.metrics.getBasicMetrics(),
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Shutdown gateway gracefully
   */
  async shutdown() {
    logger.info('Shutting down Thin Socket Gateway');
    
    // Disconnect all sockets
    this.io.disconnectSockets();
    
    // Cleanup components
    await this.connectionManager.cleanup();
    await this.messageRouter.cleanup();
    
    logger.info('Thin Socket Gateway shutdown complete');
  }
}

module.exports = ThinSocketGateway;
