/**
 * Socket Service Orchestrator
 * 
 * Main orchestrator that coordinates all socket service components.
 * Handles initialization, dependency injection, and high-level coordination.
 */

const { Server } = require('socket.io');
const { createAdapter } = require('@socket.io/redis-adapter');
const config = require('../../config');
const logger = require('../../utils/logger');
const AuthMiddleware = require('../../middleware/auth');
const redisService = require('../redisService');

// Import all modular components
const SocketConnectionManager = require('./connectionManager');
const SocketEventHandlers = require('./eventHandlers');
const RedisMessageHandler = require('./redisMessageHandler');
const SocketMetrics = require('./metricsCollector');
const LobbyHandlers = require('./lobbyHandlers');
const EnhancedLobbyHandlers = require('./enhancedLobbyHandlers');
const RoomHandlers = require('./roomHandlers');
const GameHandlers = require('./gameHandlers');
const EventChannelRouter = require('./eventChannelRouter');
const GameServiceCommunicator = require('./gameServiceCommunicator');
const RoomCacheManager = require('./roomCacheManager');
const UserBalanceManager = require('./userBalanceManager');
const EventProcessor = require('./eventProcessor');

// Performance optimization components
const PerformanceOptimizer = require('../performance/performanceOptimizer');
const IntelligentCacheManager = require('../cache/intelligentCacheManager');
const AdvancedAnalytics = require('../analytics/advancedAnalytics');

// Manager Service communicator
const ManagerServiceCommunicator = require('./managerServiceCommunicator');

class SocketServiceOrchestrator {
  constructor() {
    this.io = null;
    this.server = null;
    
    // Initialize all components
    this.initializeComponents();
    
    // Setup component dependencies
    this.setupDependencies();
  }

  /**
   * Initialize all modular components
   */
  initializeComponents() {
    // Core socket components
    this.connectionManager = new SocketConnectionManager();
    this.eventHandlers = new SocketEventHandlers();
    this.redisMessageHandler = new RedisMessageHandler();
    this.metricsCollector = new SocketMetrics();

    // Specialized handlers
    this.lobbyHandlers = new LobbyHandlers();
    this.enhancedLobbyHandlers = new EnhancedLobbyHandlers();
    this.roomHandlers = new RoomHandlers();
    this.gameHandlers = new GameHandlers();

    // New modular components
    this.eventChannelRouter = new EventChannelRouter();
    this.gameServiceCommunicator = new GameServiceCommunicator();
    this.managerServiceCommunicator = new ManagerServiceCommunicator();
    this.roomCacheManager = new RoomCacheManager();
    this.userBalanceManager = new UserBalanceManager();
    this.eventProcessor = new EventProcessor();

    // Performance optimization components
    this.performanceOptimizer = new PerformanceOptimizer({
      maxConnections: 10000,
      batchSize: 50,
      memoryThreshold: 0.8,
      metricsInterval: 10000,
    });

    this.intelligentCache = new IntelligentCacheManager(redisService, {
      l1CacheSize: 1000,
      defaultTTL: 300,
      metricsEnabled: true,
    });

    this.analytics = new AdvancedAnalytics({
      realTimeEnabled: true,
      predictionEnabled: true,
      bottleneckDetection: true,
      alertThresholds: {
        errorRate: 0.05,
        responseTime: 1000,
        memoryUsage: 0.8,
        connectionCount: 5000,
      },
    });

    logger.info('Socket service components initialized', {
      components: [
        'Core Socket Components',
        'Specialized Handlers',
        'Modular Components',
        'Performance Optimizer',
        'Intelligent Cache',
        'Advanced Analytics',
      ],
    });
  }

  /**
   * Setup dependencies between components
   */
  setupDependencies() {
    // Set socket service reference in components that need it
    this.eventChannelRouter.setSocketService(this);
    this.redisMessageHandler.setSocketService(this);
    this.lobbyHandlers.setSocketService(this);
    this.lobbyHandlers.setManagerServiceCommunicator(this.managerServiceCommunicator);
    this.enhancedLobbyHandlers.setSocketService(this);
    this.enhancedLobbyHandlers.setManagerServiceCommunicator(this.managerServiceCommunicator);
    this.roomHandlers.setSocketService(this);
    this.gameHandlers.setSocketService(this);

    // Set game service communicator in balance manager
    this.userBalanceManager.setGameServiceCommunicator(this.gameServiceCommunicator);

    // Set dependencies in event processor
    this.eventProcessor.setDependencies({
      socketService: this,
      roomCacheManager: this.roomCacheManager,
      userBalanceManager: this.userBalanceManager,
    });

    // Set specialized handlers for event handlers
    this.eventHandlers.setHandlers({
      lobbyHandlers: this.lobbyHandlers,
      enhancedLobbyHandlers: this.enhancedLobbyHandlers,
      roomHandlers: this.roomHandlers,
      gameHandlers: this.gameHandlers,
    });

    // Set up performance optimization integrations
    this.setupPerformanceIntegrations();

    logger.info('Component dependencies configured');
  }

  /**
   * Setup performance optimization integrations
   */
  setupPerformanceIntegrations() {
    // Set up analytics event tracking
    this.analytics.on('alertCreated', (alert) => {
      logger.warn('Performance alert created', alert);
      // Could emit to admin dashboard or trigger notifications
    });

    this.analytics.on('bottlenecksDetected', (bottlenecks) => {
      logger.warn('Performance bottlenecks detected', {
        count: bottlenecks.length,
        types: bottlenecks.map(b => b.type),
      });
      // Could trigger automatic optimizations
    });

    // Set up performance optimizer event handling
    this.performanceOptimizer.on('connectionOptimized', ({ connectionId }) => {
      this.analytics.trackEvent('connection_optimized', { connectionId });
    });

    this.performanceOptimizer.on('batchProcessed', ({ batchKey, messages, processingTime }) => {
      this.analytics.trackEvent('batch_processed', {
        batchKey,
        messageCount: messages.length,
        processingTime,
      });
    });

    // Set up intelligent cache event handling
    this.intelligentCache.on('cacheSet', ({ key, ttl, size }) => {
      this.analytics.trackEvent('cache_set', { key, ttl, size });
    });

    this.intelligentCache.on('cacheInvalidated', ({ pattern, invalidatedKeys }) => {
      this.analytics.trackEvent('cache_invalidated', {
        pattern,
        invalidatedCount: invalidatedKeys.length,
      });
    });

    logger.info('Performance optimization integrations configured');
  }

  /**
   * Initialize the Socket.io server
   * @param {Object} httpServer - HTTP server instance
   */
  async initialize(httpServer) {
    try {
      // Create Socket.io server
      this.io = new Server(httpServer, {
        cors: {
          origin: config.cors.origins,
          credentials: config.cors.credentials,
        },
        pingTimeout: config.socket.pingTimeout,
        pingInterval: config.socket.pingInterval,
        upgradeTimeout: config.socket.upgradeTimeout,
        maxHttpBufferSize: config.socket.maxHttpBufferSize,
        allowEIO3: config.socket.allowEIO3,
        transports: config.socket.transports,
      });

      // Set up Redis adapter for horizontal scaling
      await this.setupRedisAdapter();

      // Set up authentication middleware
      this.io.use(AuthMiddleware.socketAuthWithGameService);

      // Set up connection handling
      this.setupConnectionHandling();

      // Set up Redis message handling
      this.setupRedisMessageHandling();

      logger.info('Socket service orchestrator initialized successfully');
    } catch (error) {
      logger.logError(error, { context: 'Socket service orchestrator initialization' });
      throw error;
    }
  }

  /**
   * Setup Redis adapter for Socket.io clustering
   */
  async setupRedisAdapter() {
    try {
      const pubClient = redisService.publisher;
      const subClient = redisService.subscriber;

      if (!pubClient || !subClient) {
        throw new Error('Redis clients not available for adapter setup');
      }

      const adapter = createAdapter(pubClient, subClient, {
        requestsTimeout: 5000,
      });

      // Set up error handlers on Redis clients
      pubClient.on('error', (error) => {
        logger.warn('Redis publisher error in adapter (non-critical)', {
          context: 'Redis adapter publisher',
          error: error.message,
          type: 'adapter_error'
        });
      });

      subClient.on('error', (error) => {
        logger.warn('Redis subscriber error in adapter (non-critical)', {
          context: 'Redis adapter subscriber',
          error: error.message,
          type: 'adapter_error'
        });
      });

      this.io.adapter(adapter);
      logger.info('Socket.io Redis adapter configured');
    } catch (error) {
      logger.logError(error, { context: 'Redis adapter setup' });
      logger.warn('Continuing without Redis adapter - clustering will not work');
    }
  }

  /**
   * Setup connection handling
   */
  setupConnectionHandling() {
    this.io.on('connection', (socket) => {
      try {
        // Track connection analytics
        this.analytics.trackConnection('connect', {
          socketId: socket.id,
          userId: socket.user?.id,
          userAgent: socket.handshake.headers['user-agent'],
          ipAddress: socket.handshake.address,
        });

        // Optimize connection
        this.performanceOptimizer.optimizeConnection(socket, {
          userId: socket.user?.id,
          userAgent: socket.handshake.headers['user-agent'],
        });

        // Use connection manager to handle connections
        this.connectionManager.handleConnection(
          socket,
          (socket) => this.setupOptimizedEventHandlers(socket),
          (userId, token) => this.userBalanceManager.fetchUserBalance(userId, token)
        );

        // Set up disconnect tracking
        socket.on('disconnect', (reason) => {
          this.analytics.trackConnection('disconnect', {
            socketId: socket.id,
            userId: socket.user?.id,
            reason,
          });
        });

      } catch (error) {
        this.analytics.trackError(error, {
          context: 'Socket connection handling',
          socketId: socket.id,
        });

        logger.logError(error, {
          context: 'Socket connection handling',
          socketId: socket.id
        });

        socket.emit('error', {
          code: 'CONNECTION_ERROR',
          message: 'Connection handling failed',
          timestamp: new Date().toISOString()
        });
        socket.disconnect(true);
      }
    });

    // Handle server-level errors
    this.io.on('error', (error) => {
      logger.logError(error, { context: 'Socket.io server error' });
    });

    // Handle connection errors
    this.io.engine.on('connection_error', (error) => {
      logger.warn('Socket.io connection error (non-critical)', {
        context: 'Socket.io engine',
        error: error.message,
        type: 'connection_error'
      });
    });
  }

  /**
   * Setup Redis message handling
   */
  setupRedisMessageHandling() {
    // Set up Redis message handler for game service events
    redisService.on('message', ({ message, channel }) => {
      this.handleRedisMessage(message, channel);
    });

    logger.info('Redis message handling configured for socket service');
  }

  /**
   * Handle Redis messages by routing through event channel router
   * @param {Object} message - Redis message
   * @param {string} channel - Redis channel
   */
  async handleRedisMessage(message, channel) {
    try {
      // Try to route through event channel router first
      const handled = await this.eventChannelRouter.routeChannelMessage(channel, message);
      
      if (!handled) {
        // Fallback to legacy Redis message handler
        await this.redisMessageHandler.handleRedisMessage(message, channel);
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Redis message handling',
        channel,
        eventType: message?.event?.type,
      });
    }
  }

  /**
   * Setup optimized event handlers with performance tracking
   * @param {Socket} socket - Socket.io socket instance
   */
  setupOptimizedEventHandlers(socket) {
    // Wrap original event handlers with performance tracking
    const originalSetup = this.eventHandlers.setupEventHandlers.bind(this.eventHandlers);

    // Set up performance-tracked event handlers
    const trackEventHandler = (eventName, originalHandler) => {
      return async (...args) => {
        const startTime = Date.now();

        try {
          // Track message event
          this.analytics.trackMessage(eventName, {
            socketId: socket.id,
            userId: socket.user?.id,
            args: args.length,
          });

          // Execute original handler
          const result = await originalHandler(...args);

          // Track response time
          const responseTime = Date.now() - startTime;
          this.analytics.trackMessage(eventName, {
            socketId: socket.id,
            userId: socket.user?.id,
            responseTime,
          }, responseTime);

          return result;

        } catch (error) {
          // Track error
          this.analytics.trackError(error, {
            eventName,
            socketId: socket.id,
            userId: socket.user?.id,
          });

          throw error;
        }
      };
    };

    // Apply performance tracking to event handlers
    const originalEmit = socket.emit.bind(socket);
    socket.emit = (...args) => {
      const [eventName, ...data] = args;

      // Track outgoing message
      this.analytics.trackMessage(`emit_${eventName}`, {
        socketId: socket.id,
        userId: socket.user?.id,
        dataSize: JSON.stringify(data).length,
      });

      return originalEmit(...args);
    };

    // Set up original event handlers
    originalSetup(socket);
  }

  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    return {
      connections: this.connectionManager.getConnectionStats(),
      metrics: this.metricsCollector.getMetrics(),
      roomCache: this.roomCacheManager.getCacheStats(),
      balanceCache: this.userBalanceManager.getCacheStats(),
      pendingRequests: this.gameServiceCommunicator.getPendingRequestStats(),
      managerService: this.managerServiceCommunicator.getStats(),
      performance: this.performanceOptimizer.getPerformanceStats(),
      cache: this.intelligentCache.getStats(),
      analytics: this.analytics.getDashboardData(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Cleanup resources on shutdown
   */
  async cleanup() {
    logger.info('Cleaning up socket service orchestrator');

    try {
      // Cleanup performance optimization components
      await this.performanceOptimizer.cleanup();
      await this.intelligentCache.cleanup();
      await this.analytics.cleanup();

      // Cleanup game service communicator
      this.gameServiceCommunicator.cleanup();

      // Clear caches
      this.roomCacheManager.clearAllCaches();
      this.userBalanceManager.clearAllCache();

      // Close Socket.io server
      if (this.io) {
        this.io.close();
      }

      logger.info('Socket service orchestrator cleanup completed');
    } catch (error) {
      logger.logError(error, { context: 'Socket service orchestrator cleanup' });
    }
  }

  // ===== DELEGATED EVENT HANDLERS =====
  // These methods delegate to the event processor for standardized handling

  async handleRoomEvent(channel, message, eventInfo, match) {
    const roomId = match[1];
    await this.eventProcessor.processRoomEvent(roomId, eventInfo.type, eventInfo.payload);
  }

  async handleLobbyEvent(channel, message, eventInfo) {
    await this.eventProcessor.processLobbyEvent(eventInfo.type, eventInfo.payload);
  }

  async handleUserEvent(channel, message, eventInfo, match) {
    const userId = match[1];
    await this.eventProcessor.processUserEvent(userId, eventInfo.type, eventInfo.payload);
  }

  async handleGlobalEvent(channel, message, eventInfo) {
    logger.debug('Processing global event', {
      eventType: eventInfo.type,
      channel
    });

    // Broadcast to all connected clients
    this.io.emit(eventInfo.type, eventInfo.payload);
  }

  async handleServiceRequest(channel, message, eventInfo, match) {
    const targetService = match[1];
    
    // Only handle requests targeted at socket-gateway
    if (targetService !== 'socket-gateway') {
      return;
    }

    logger.debug('Processing service request', {
      targetService,
      eventType: eventInfo.type,
      channel
    });

    // Handle specific service requests here
    // This could be expanded based on requirements
  }

  async handleMonitoringEvent(channel, message, eventInfo) {
    logger.debug('Processing monitoring event', {
      eventType: eventInfo.type,
      channel
    });

    // Handle monitoring events
    // This could trigger health checks, metrics collection, etc.
  }

  async handleLegacySocketEvent(channel, message, eventInfo) {
    logger.warn('Legacy socket:events channel used - please migrate to standard format', {
      channel,
      eventType: eventInfo.type,
    });

    // Delegate to legacy handler for backward compatibility
    await this.redisMessageHandler.handleRedisMessage(message, channel);
  }

  async handleLegacyLobbyEvent(channel, message, eventInfo) {
    logger.warn('Legacy lobby channel used - please migrate to standard format', {
      channel,
      eventType: eventInfo.type,
      suggestedChannel: 'lobby:events'
    });

    // Import event types
    const { EVENT_TYPES } = require('../../constants/events');

    // Process legacy lobby events and delegate to proper handlers
    try {
      switch (eventInfo.type) {
        case 'room_list_updated':
          // Delegate to lobby event processor using the correct event type constant
          await this.eventProcessor.processLobbyEvent(EVENT_TYPES.LOBBY.ROOM_LIST_UPDATED, eventInfo.payload);
          break;
        case 'lobby_subscribed':
          // Handle lobby subscription confirmation
          logger.debug('Legacy lobby subscription event received', {
            payload: eventInfo.payload
          });
          break;
        default:
          logger.debug('Unhandled legacy lobby event type', {
            eventType: eventInfo.type,
            payload: eventInfo.payload,
          });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Legacy lobby event handling',
        channel,
        eventType: eventInfo.type,
      });
    }
  }
}

module.exports = SocketServiceOrchestrator;
