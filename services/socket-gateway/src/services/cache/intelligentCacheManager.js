/**
 * Intelligent Multi-Level Cache Manager
 * 
 * Implements advanced caching strategies with multiple cache levels,
 * intelligent invalidation, and performance optimization.
 */

const EventEmitter = require('events');
const logger = require('../../utils/logger');

class IntelligentCacheManager extends EventEmitter {
  constructor(redisService, options = {}) {
    super();
    
    this.redisService = redisService;
    this.config = {
      // Cache Levels
      l1CacheSize: options.l1CacheSize || 1000, // In-memory cache size
      l2CacheSize: options.l2CacheSize || 10000, // Redis cache size
      
      // TTL Configuration
      defaultTTL: options.defaultTTL || 300, // 5 minutes
      shortTTL: options.shortTTL || 60, // 1 minute
      longTTL: options.longTTL || 3600, // 1 hour
      
      // Cache Strategies
      evictionPolicy: options.evictionPolicy || 'lru', // lru, lfu, fifo
      compressionEnabled: options.compressionEnabled !== false,
      encryptionEnabled: options.encryptionEnabled || false,
      
      // Performance
      batchSize: options.batchSize || 100,
      maxConcurrentOperations: options.maxConcurrentOperations || 50,
      
      // Monitoring
      metricsEnabled: options.metricsEnabled !== false,
      metricsInterval: options.metricsInterval || 30000, // 30 seconds
    };
    
    // Cache Levels
    this.l1Cache = new Map(); // In-memory cache (fastest)
    this.l1AccessOrder = []; // For LRU eviction
    this.l1AccessCount = new Map(); // For LFU eviction
    
    // Cache Metrics
    this.metrics = {
      l1: { hits: 0, misses: 0, sets: 0, deletes: 0, evictions: 0 },
      l2: { hits: 0, misses: 0, sets: 0, deletes: 0, evictions: 0 },
      performance: {
        averageGetTime: 0,
        averageSetTime: 0,
        totalOperations: 0,
      },
    };
    
    // Cache Invalidation
    this.invalidationRules = new Map();
    this.dependencyGraph = new Map();
    
    // Operation Queue
    this.operationQueue = [];
    this.activeOperations = 0;
    
    this.initialize();
  }

  /**
   * Initialize cache manager
   */
  initialize() {
    logger.info('Initializing Intelligent Cache Manager');
    
    // Start metrics collection
    if (this.config.metricsEnabled) {
      this.startMetricsCollection();
    }
    
    // Start cache maintenance
    this.startCacheMaintenance();
    
    // Set up invalidation rules
    this.setupInvalidationRules();
    
    logger.info('Intelligent Cache Manager initialized', {
      l1CacheSize: this.config.l1CacheSize,
      l2CacheSize: this.config.l2CacheSize,
      evictionPolicy: this.config.evictionPolicy,
      features: [
        'Multi-Level Caching',
        'Intelligent Invalidation',
        'Performance Optimization',
        'Metrics Collection',
        'Dependency Tracking',
      ],
    });
  }

  /**
   * Get value from cache with intelligent fallback
   * @param {string} key - Cache key
   * @param {Object} options - Get options
   * @returns {Promise<*>} Cached value or null
   */
  async get(key, options = {}) {
    const startTime = Date.now();
    
    try {
      // Try L1 cache first (in-memory)
      const l1Value = this.getFromL1(key);
      if (l1Value !== null) {
        this.metrics.l1.hits++;
        this.updateAccessPattern(key);
        return this.deserializeValue(l1Value);
      }
      
      this.metrics.l1.misses++;
      
      // Try L2 cache (Redis)
      const l2Value = await this.getFromL2(key);
      if (l2Value !== null) {
        this.metrics.l2.hits++;
        
        // Promote to L1 cache
        this.setToL1(key, l2Value, options);
        
        return this.deserializeValue(l2Value);
      }
      
      this.metrics.l2.misses++;
      return null;
      
    } catch (error) {
      logger.logError(error, { context: 'Cache get', key });
      return null;
    } finally {
      this.updatePerformanceMetrics('get', Date.now() - startTime);
    }
  }

  /**
   * Set value in cache with intelligent distribution
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {Object} options - Set options
   */
  async set(key, value, options = {}) {
    const startTime = Date.now();
    
    try {
      const serializedValue = this.serializeValue(value);
      const ttl = options.ttl || this.config.defaultTTL;
      
      // Set in L1 cache
      this.setToL1(key, serializedValue, { ttl });
      this.metrics.l1.sets++;
      
      // Set in L2 cache (Redis) for persistence
      await this.setToL2(key, serializedValue, { ttl });
      this.metrics.l2.sets++;
      
      // Update dependency graph
      if (options.dependencies) {
        this.updateDependencies(key, options.dependencies);
      }
      
      // Emit cache set event
      this.emit('cacheSet', { key, ttl, size: serializedValue.length });
      
    } catch (error) {
      logger.logError(error, { context: 'Cache set', key });
    } finally {
      this.updatePerformanceMetrics('set', Date.now() - startTime);
    }
  }

  /**
   * Delete value from cache
   * @param {string} key - Cache key
   */
  async delete(key) {
    try {
      // Delete from L1
      if (this.l1Cache.has(key)) {
        this.l1Cache.delete(key);
        this.removeFromAccessOrder(key);
        this.l1AccessCount.delete(key);
        this.metrics.l1.deletes++;
      }
      
      // Delete from L2
      await this.deleteFromL2(key);
      this.metrics.l2.deletes++;
      
      // Clean up dependencies
      this.cleanupDependencies(key);
      
      this.emit('cacheDelete', { key });
      
    } catch (error) {
      logger.logError(error, { context: 'Cache delete', key });
    }
  }

  /**
   * Invalidate cache based on patterns or dependencies
   * @param {string|Array} pattern - Invalidation pattern or dependency keys
   * @param {Object} options - Invalidation options
   */
  async invalidate(pattern, options = {}) {
    try {
      const startTime = Date.now();
      let invalidatedKeys = [];
      
      if (Array.isArray(pattern)) {
        // Invalidate specific keys
        for (const key of pattern) {
          await this.delete(key);
          invalidatedKeys.push(key);
        }
      } else if (typeof pattern === 'string') {
        // Pattern-based invalidation
        invalidatedKeys = await this.invalidateByPattern(pattern);
      }
      
      // Cascade invalidation based on dependencies
      if (options.cascade !== false) {
        const cascadeKeys = await this.cascadeInvalidation(invalidatedKeys);
        invalidatedKeys.push(...cascadeKeys);
      }
      
      logger.info('Cache invalidation completed', {
        pattern,
        invalidatedCount: invalidatedKeys.length,
        duration: Date.now() - startTime,
      });
      
      this.emit('cacheInvalidated', {
        pattern,
        invalidatedKeys,
        cascade: options.cascade !== false,
      });
      
      return invalidatedKeys;
      
    } catch (error) {
      logger.logError(error, { context: 'Cache invalidate', pattern });
      return [];
    }
  }

  /**
   * Get value from L1 cache (in-memory)
   * @param {string} key - Cache key
   * @returns {*} Cached value or null
   */
  getFromL1(key) {
    const entry = this.l1Cache.get(key);
    if (!entry) return null;
    
    // Check TTL
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.l1Cache.delete(key);
      this.removeFromAccessOrder(key);
      this.l1AccessCount.delete(key);
      return null;
    }
    
    return entry.value;
  }

  /**
   * Set value in L1 cache (in-memory)
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {Object} options - Set options
   */
  setToL1(key, value, options = {}) {
    const ttl = options.ttl || this.config.defaultTTL;
    const expiresAt = ttl > 0 ? Date.now() + (ttl * 1000) : null;
    
    // Check if cache is full and evict if necessary
    if (this.l1Cache.size >= this.config.l1CacheSize && !this.l1Cache.has(key)) {
      this.evictFromL1();
    }
    
    this.l1Cache.set(key, {
      value,
      createdAt: Date.now(),
      expiresAt,
      accessCount: 0,
    });
    
    this.updateAccessPattern(key);
  }

  /**
   * Get value from L2 cache (Redis)
   * @param {string} key - Cache key
   * @returns {Promise<*>} Cached value or null
   */
  async getFromL2(key) {
    try {
      return await this.redisService.get(key);
    } catch (error) {
      logger.logError(error, { context: 'L2 cache get', key });
      return null;
    }
  }

  /**
   * Set value in L2 cache (Redis)
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {Object} options - Set options
   */
  async setToL2(key, value, options = {}) {
    try {
      const ttl = options.ttl || this.config.defaultTTL;
      await this.redisService.set(key, value, ttl);
    } catch (error) {
      logger.logError(error, { context: 'L2 cache set', key });
    }
  }

  /**
   * Delete value from L2 cache (Redis)
   * @param {string} key - Cache key
   */
  async deleteFromL2(key) {
    try {
      await this.redisService.del(key);
    } catch (error) {
      logger.logError(error, { context: 'L2 cache delete', key });
    }
  }

  /**
   * Evict entry from L1 cache based on eviction policy
   */
  evictFromL1() {
    let keyToEvict;
    
    switch (this.config.evictionPolicy) {
      case 'lru':
        keyToEvict = this.l1AccessOrder[0];
        break;
      case 'lfu':
        keyToEvict = this.findLFUKey();
        break;
      case 'fifo':
        keyToEvict = this.l1Cache.keys().next().value;
        break;
      default:
        keyToEvict = this.l1AccessOrder[0];
    }
    
    if (keyToEvict) {
      this.l1Cache.delete(keyToEvict);
      this.removeFromAccessOrder(keyToEvict);
      this.l1AccessCount.delete(keyToEvict);
      this.metrics.l1.evictions++;
      
      logger.debug('L1 cache eviction', {
        evictedKey: keyToEvict,
        policy: this.config.evictionPolicy,
        cacheSize: this.l1Cache.size,
      });
    }
  }

  /**
   * Find least frequently used key
   * @returns {string} LFU key
   */
  findLFUKey() {
    let minCount = Infinity;
    let lfuKey = null;
    
    for (const [key, count] of this.l1AccessCount) {
      if (count < minCount) {
        minCount = count;
        lfuKey = key;
      }
    }
    
    return lfuKey;
  }

  /**
   * Update access pattern for cache key
   * @param {string} key - Cache key
   */
  updateAccessPattern(key) {
    // Update LRU order
    this.removeFromAccessOrder(key);
    this.l1AccessOrder.push(key);
    
    // Update LFU count
    const currentCount = this.l1AccessCount.get(key) || 0;
    this.l1AccessCount.set(key, currentCount + 1);
    
    // Update cache entry access count
    const entry = this.l1Cache.get(key);
    if (entry) {
      entry.accessCount++;
    }
  }

  /**
   * Remove key from access order array
   * @param {string} key - Cache key
   */
  removeFromAccessOrder(key) {
    const index = this.l1AccessOrder.indexOf(key);
    if (index > -1) {
      this.l1AccessOrder.splice(index, 1);
    }
  }

  /**
   * Serialize value for caching
   * @param {*} value - Value to serialize
   * @returns {string} Serialized value
   */
  serializeValue(value) {
    try {
      return JSON.stringify(value);
    } catch (error) {
      logger.logError(error, { context: 'Serialize cache value' });
      return null;
    }
  }

  /**
   * Deserialize cached value
   * @param {string} serializedValue - Serialized value
   * @returns {*} Deserialized value
   */
  deserializeValue(serializedValue) {
    try {
      return JSON.parse(serializedValue);
    } catch (error) {
      logger.logError(error, { context: 'Deserialize cache value' });
      return null;
    }
  }

  /**
   * Update performance metrics
   * @param {string} operation - Operation type
   * @param {number} duration - Operation duration in ms
   */
  updatePerformanceMetrics(operation, duration) {
    const metrics = this.metrics.performance;
    metrics.totalOperations++;
    
    if (operation === 'get') {
      metrics.averageGetTime = (metrics.averageGetTime + duration) / 2;
    } else if (operation === 'set') {
      metrics.averageSetTime = (metrics.averageSetTime + duration) / 2;
    }
  }

  /**
   * Start metrics collection
   */
  startMetricsCollection() {
    setInterval(() => {
      this.collectAndLogMetrics();
    }, this.config.metricsInterval);
  }

  /**
   * Collect and log cache metrics
   */
  collectAndLogMetrics() {
    const l1HitRate = this.metrics.l1.hits / (this.metrics.l1.hits + this.metrics.l1.misses) || 0;
    const l2HitRate = this.metrics.l2.hits / (this.metrics.l2.hits + this.metrics.l2.misses) || 0;
    const overallHitRate = (this.metrics.l1.hits + this.metrics.l2.hits) / 
                          (this.metrics.l1.hits + this.metrics.l1.misses + this.metrics.l2.misses) || 0;
    
    logger.info('Cache Performance Metrics', {
      l1: {
        size: this.l1Cache.size,
        hitRate: `${(l1HitRate * 100).toFixed(1)}%`,
        hits: this.metrics.l1.hits,
        misses: this.metrics.l1.misses,
        evictions: this.metrics.l1.evictions,
      },
      l2: {
        hitRate: `${(l2HitRate * 100).toFixed(1)}%`,
        hits: this.metrics.l2.hits,
        misses: this.metrics.l2.misses,
      },
      overall: {
        hitRate: `${(overallHitRate * 100).toFixed(1)}%`,
        avgGetTime: `${this.metrics.performance.averageGetTime.toFixed(2)}ms`,
        avgSetTime: `${this.metrics.performance.averageSetTime.toFixed(2)}ms`,
        totalOps: this.metrics.performance.totalOperations,
      },
    });
  }

  /**
   * Start cache maintenance tasks
   */
  startCacheMaintenance() {
    // Clean expired entries every 5 minutes
    setInterval(() => {
      this.cleanExpiredEntries();
    }, 300000);
    
    // Optimize cache every 10 minutes
    setInterval(() => {
      this.optimizeCache();
    }, 600000);
  }

  /**
   * Clean expired entries from L1 cache
   */
  cleanExpiredEntries() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, entry] of this.l1Cache) {
      if (entry.expiresAt && now > entry.expiresAt) {
        this.l1Cache.delete(key);
        this.removeFromAccessOrder(key);
        this.l1AccessCount.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug('Cleaned expired cache entries', {
        cleanedCount,
        remainingEntries: this.l1Cache.size,
      });
    }
  }

  /**
   * Optimize cache performance
   */
  optimizeCache() {
    logger.debug('Optimizing cache performance');
    
    // Clean up access patterns for deleted keys
    this.l1AccessOrder = this.l1AccessOrder.filter(key => this.l1Cache.has(key));
    
    // Remove access counts for deleted keys
    for (const key of this.l1AccessCount.keys()) {
      if (!this.l1Cache.has(key)) {
        this.l1AccessCount.delete(key);
      }
    }
    
    logger.debug('Cache optimization completed', {
      l1Size: this.l1Cache.size,
      accessOrderLength: this.l1AccessOrder.length,
      accessCountSize: this.l1AccessCount.size,
    });
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const l1HitRate = this.metrics.l1.hits / (this.metrics.l1.hits + this.metrics.l1.misses) || 0;
    const l2HitRate = this.metrics.l2.hits / (this.metrics.l2.hits + this.metrics.l2.misses) || 0;
    
    return {
      l1: {
        size: this.l1Cache.size,
        maxSize: this.config.l1CacheSize,
        hitRate: l1HitRate,
        metrics: this.metrics.l1,
      },
      l2: {
        hitRate: l2HitRate,
        metrics: this.metrics.l2,
      },
      performance: this.metrics.performance,
      config: this.config,
    };
  }

  /**
   * Setup invalidation rules
   */
  setupInvalidationRules() {
    // Room-related invalidation rules
    this.invalidationRules.set('room:*', [
      'lobby:rooms',
      'lobby:stats',
      'room:list:*',
    ]);

    // User-related invalidation rules
    this.invalidationRules.set('user:*', [
      'user:profile:*',
      'user:balance:*',
    ]);

    // Lobby-related invalidation rules
    this.invalidationRules.set('lobby:*', [
      'lobby:rooms',
      'lobby:stats',
      'lobby:featured',
    ]);
  }

  /**
   * Update dependencies for a cache key
   * @param {string} key - Cache key
   * @param {Array} dependencies - Dependency keys
   */
  updateDependencies(key, dependencies) {
    if (!Array.isArray(dependencies)) return;

    this.dependencyGraph.set(key, dependencies);

    // Create reverse dependencies
    dependencies.forEach(dep => {
      if (!this.dependencyGraph.has(`reverse:${dep}`)) {
        this.dependencyGraph.set(`reverse:${dep}`, []);
      }
      this.dependencyGraph.get(`reverse:${dep}`).push(key);
    });
  }

  /**
   * Clean up dependencies for a key
   * @param {string} key - Cache key
   */
  cleanupDependencies(key) {
    const dependencies = this.dependencyGraph.get(key);
    if (dependencies) {
      dependencies.forEach(dep => {
        const reverseDeps = this.dependencyGraph.get(`reverse:${dep}`);
        if (reverseDeps) {
          const index = reverseDeps.indexOf(key);
          if (index > -1) {
            reverseDeps.splice(index, 1);
          }
        }
      });
      this.dependencyGraph.delete(key);
    }
  }

  /**
   * Invalidate cache by pattern
   * @param {string} pattern - Invalidation pattern
   * @returns {Promise<Array>} Invalidated keys
   */
  async invalidateByPattern(pattern) {
    const invalidatedKeys = [];

    try {
      // Check L1 cache
      for (const key of this.l1Cache.keys()) {
        if (this.matchesPattern(key, pattern)) {
          await this.delete(key);
          invalidatedKeys.push(key);
        }
      }

      // Check L2 cache (Redis) - this would require scanning
      // For now, we'll use the invalidation rules
      const rules = this.invalidationRules.get(pattern);
      if (rules) {
        for (const rule of rules) {
          const ruleKeys = await this.getKeysByPattern(rule);
          for (const key of ruleKeys) {
            await this.delete(key);
            invalidatedKeys.push(key);
          }
        }
      }

    } catch (error) {
      logger.logError(error, { context: 'Invalidate by pattern', pattern });
    }

    return invalidatedKeys;
  }

  /**
   * Cascade invalidation based on dependencies
   * @param {Array} invalidatedKeys - Already invalidated keys
   * @returns {Promise<Array>} Additional invalidated keys
   */
  async cascadeInvalidation(invalidatedKeys) {
    const cascadeKeys = [];

    try {
      for (const key of invalidatedKeys) {
        const dependentKeys = this.dependencyGraph.get(`reverse:${key}`);
        if (dependentKeys) {
          for (const depKey of dependentKeys) {
            if (!invalidatedKeys.includes(depKey) && !cascadeKeys.includes(depKey)) {
              await this.delete(depKey);
              cascadeKeys.push(depKey);
            }
          }
        }
      }

      // Recursively cascade if there are more dependencies
      if (cascadeKeys.length > 0) {
        const moreCascadeKeys = await this.cascadeInvalidation(cascadeKeys);
        cascadeKeys.push(...moreCascadeKeys);
      }

    } catch (error) {
      logger.logError(error, { context: 'Cascade invalidation' });
    }

    return cascadeKeys;
  }

  /**
   * Check if key matches pattern
   * @param {string} key - Cache key
   * @param {string} pattern - Pattern to match
   * @returns {boolean} True if matches
   */
  matchesPattern(key, pattern) {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(key);
    }
    return key === pattern;
  }

  /**
   * Get keys by pattern (simplified implementation)
   * @param {string} pattern - Key pattern
   * @returns {Promise<Array>} Matching keys
   */
  async getKeysByPattern(pattern) {
    const keys = [];

    // Check L1 cache
    for (const key of this.l1Cache.keys()) {
      if (this.matchesPattern(key, pattern)) {
        keys.push(key);
      }
    }

    // For L2 cache (Redis), we would need to implement Redis SCAN
    // For now, return L1 matches
    return keys;
  }

  /**
   * Cleanup cache manager
   */
  async cleanup() {
    logger.info('Cleaning up Intelligent Cache Manager');

    this.l1Cache.clear();
    this.l1AccessOrder.length = 0;
    this.l1AccessCount.clear();
    this.invalidationRules.clear();
    this.dependencyGraph.clear();

    logger.info('Intelligent Cache Manager cleanup completed');
  }
}

module.exports = IntelligentCacheManager;
