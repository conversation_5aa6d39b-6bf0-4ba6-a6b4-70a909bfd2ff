/**
 * Color State Manager
 * 
 * Manages real-time color selection state for prize wheel games.
 * Provides events for available colors, color selections, and state synchronization.
 */

const logger = require('../utils/logger');

class ColorStateManager {
  constructor() {
    // Define all available colors with metadata
    this.allColors = [
      { id: 'red', name: 'Red', hex: '#FF0000', rgb: [255, 0, 0] },
      { id: 'blue', name: 'Blue', hex: '#0000FF', rgb: [0, 0, 255] },
      { id: 'green', name: 'Green', hex: '#00FF00', rgb: [0, 255, 0] },
      { id: 'yellow', name: 'Yellow', hex: '#FFFF00', rgb: [255, 255, 0] },
      { id: 'purple', name: 'Purple', hex: '#800080', rgb: [128, 0, 128] },
      { id: 'orange', name: 'Orange', hex: '#FFA500', rgb: [255, 165, 0] },
      { id: 'pink', name: 'Pink', hex: '#FFC0CB', rgb: [255, 192, 203] },
      { id: 'teal', name: 'Tea<PERSON>', hex: '#008080', rgb: [0, 128, 128] },
    ];

    // Create color lookup map
    this.colorMap = new Map();
    this.allColors.forEach(color => {
      this.colorMap.set(color.id, color);
    });
  }

  /**
   * Generate available colors event data
   * @param {Object} colorState - Current color state from Redis
   * @returns {Object} Available colors event payload
   */
  generateAvailableColorsEvent(colorState) {
    const takenColorIds = new Set();
    
    // Extract taken colors from player selections
    if (colorState.PlayerColors) {
      Object.values(colorState.PlayerColors).forEach(playerColor => {
        if (playerColor.colorId) {
          takenColorIds.add(playerColor.colorId);
        }
      });
    }

    // Filter available colors
    const availableColors = this.allColors.filter(color => 
      !takenColorIds.has(color.id)
    );

    const takenColors = this.allColors.filter(color => 
      takenColorIds.has(color.id)
    );

    return {
      type: 'available_colors',
      data: {
        availableColors,
        takenColors,
        totalColors: this.allColors.length,
        availableCount: availableColors.length,
        takenCount: takenColors.length,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate color selection update event data
   * @param {string} userId - User ID who made the selection
   * @param {string} username - Username who made the selection
   * @param {string} colorId - Selected color ID
   * @param {Object} colorState - Updated color state
   * @returns {Object} Color selection update event payload
   */
  generateColorSelectionEvent(userId, username, colorId, colorState) {
    const selectedColor = this.colorMap.get(colorId);
    
    if (!selectedColor) {
      logger.warn('Unknown color ID in selection event', { userId, colorId });
      return null;
    }

    // Get available colors after this selection
    const availableColorsData = this.generateAvailableColorsEvent(colorState);

    return {
      type: 'color_selection_update',
      data: {
        player: {
          userId,
          username,
          selectedColor,
        },
        action: 'selected',
        availableColors: availableColorsData.data.availableColors,
        takenColors: availableColorsData.data.takenColors,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate comprehensive color state sync event
   * @param {Object} colorState - Complete color state from Redis
   * @param {string} roomId - Room ID
   * @returns {Object} Color state sync event payload
   */
  generateColorStateSyncEvent(colorState, roomId) {
    const playerColorMappings = {};
    const playerList = [];
    const takenColorIds = new Set();

    // Process player color assignments
    if (colorState.PlayerColors) {
      Object.entries(colorState.PlayerColors).forEach(([userId, playerColor]) => {
        const colorData = this.colorMap.get(playerColor.colorId);
        
        if (colorData) {
          const playerInfo = {
            userId,
            username: playerColor.username,
            color: colorData,
            selectedAt: playerColor.selectedAt || new Date().toISOString(),
          };

          playerColorMappings[userId] = playerInfo;
          playerList.push(playerInfo);
          takenColorIds.add(playerColor.colorId);
        }
      });
    }

    // Get available colors
    const availableColors = this.allColors.filter(color => 
      !takenColorIds.has(color.id)
    );

    const takenColors = this.allColors.filter(color => 
      takenColorIds.has(color.id)
    );

    return {
      type: 'color_state_sync',
      data: {
        roomId,
        playerColorMappings,
        playerList,
        availableColors,
        takenColors,
        allColors: this.allColors,
        statistics: {
          totalPlayers: playerList.length,
          totalColors: this.allColors.length,
          availableCount: availableColors.length,
          takenCount: takenColors.length,
          selectionRate: this.allColors.length > 0 ? 
            (takenColors.length / this.allColors.length * 100).toFixed(1) : 0,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate color unselection event (when player leaves or changes color)
   * @param {string} userId - User ID who unselected
   * @param {string} username - Username who unselected
   * @param {string} colorId - Previously selected color ID
   * @param {Object} colorState - Updated color state
   * @returns {Object} Color unselection event payload
   */
  generateColorUnselectionEvent(userId, username, colorId, colorState) {
    const unselectedColor = this.colorMap.get(colorId);
    
    if (!unselectedColor) {
      logger.warn('Unknown color ID in unselection event', { userId, colorId });
      return null;
    }

    // Get available colors after this unselection
    const availableColorsData = this.generateAvailableColorsEvent(colorState);

    return {
      type: 'color_selection_update',
      data: {
        player: {
          userId,
          username,
          unselectedColor,
        },
        action: 'unselected',
        availableColors: availableColorsData.data.availableColors,
        takenColors: availableColorsData.data.takenColors,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get color metadata by ID
   * @param {string} colorId - Color ID
   * @returns {Object|null} Color metadata or null if not found
   */
  getColorById(colorId) {
    return this.colorMap.get(colorId) || null;
  }

  /**
   * Get all available colors metadata
   * @returns {Array} Array of all color objects
   */
  getAllColors() {
    return [...this.allColors];
  }

  /**
   * Validate if a color ID is valid
   * @param {string} colorId - Color ID to validate
   * @returns {boolean} True if valid
   */
  isValidColor(colorId) {
    return this.colorMap.has(colorId);
  }

  /**
   * Get random available color from the available colors
   * @param {Object} colorState - Current color state
   * @returns {Object|null} Random available color or null if none available
   */
  getRandomAvailableColor(colorState) {
    const availableColorsData = this.generateAvailableColorsEvent(colorState);
    const availableColors = availableColorsData.data.availableColors;
    
    if (availableColors.length === 0) {
      return null;
    }

    const randomIndex = Math.floor(Math.random() * availableColors.length);
    return availableColors[randomIndex];
  }
}

module.exports = ColorStateManager;
