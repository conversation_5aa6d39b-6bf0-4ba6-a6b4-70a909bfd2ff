/**
 * Advanced Analytics and Monitoring System
 *
 * Provides real-time analytics, performance monitoring, bottleneck detection,
 * and intelligent insights for the Socket Gateway.
 */

const EventEmitter = require('events');
const logger = require('../../utils/logger');

class AdvancedAnalytics extends EventEmitter {
  constructor(options = {}) {
    super();

    this.config = {
      // Analytics Configuration
      metricsRetention: options.metricsRetention || 86400000, // 24 hours
      aggregationInterval: options.aggregationInterval || 60000, // 1 minute
      alertThresholds: {
        errorRate: options.errorRateThreshold || 0.05, // 5%
        responseTime: options.responseTimeThreshold || 1000, // 1 second
        memoryUsage: options.memoryThreshold || 0.8, // 80%
        connectionCount: options.connectionThreshold || 5000,
        ...options.alertThresholds,
      },

      // Real-time Analytics
      realTimeEnabled: options.realTimeEnabled !== false,
      realTimeInterval: options.realTimeInterval || 5000, // 5 seconds

      // Predictive Analytics
      predictionEnabled: options.predictionEnabled !== false,
      predictionWindow: options.predictionWindow || 3600000, // 1 hour

      // Bottleneck Detection
      bottleneckDetection: options.bottleneckDetection !== false,
      bottleneckThreshold: options.bottleneckThreshold || 0.9, // 90%
    };

    // Analytics Data Storage
    this.metrics = {
      realTime: {
        connections: 0,
        messagesPerSecond: 0,
        errorsPerSecond: 0,
        averageResponseTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        networkIO: { in: 0, out: 0 },
      },
      historical: [],
      aggregated: {
        hourly: [],
        daily: [],
      },
      events: [],
      alerts: [],
    };

    // Performance Tracking
    this.performanceCounters = {
      totalConnections: 0,
      totalMessages: 0,
      totalErrors: 0,
      totalResponseTime: 0,
      operationCounts: new Map(),
      errorCounts: new Map(),
      userActivity: new Map(),
      roomActivity: new Map(),
    };

    // Bottleneck Detection
    this.bottlenecks = {
      detected: [],
      history: [],
      patterns: new Map(),
    };

    // Predictive Models
    this.predictions = {
      connectionGrowth: [],
      loadForecast: [],
      resourceUsage: [],
    };

    this.initialize();
  }

  /**
   * Initialize analytics system
   */
  initialize() {
    logger.info('Initializing Advanced Analytics System');

    // Start real-time monitoring
    if (this.config.realTimeEnabled) {
      this.startRealTimeMonitoring();
    }

    // Start data aggregation
    this.startDataAggregation();

    // Start bottleneck detection
    if (this.config.bottleneckDetection) {
      this.startBottleneckDetection();
    }

    // Start predictive analytics
    if (this.config.predictionEnabled) {
      this.startPredictiveAnalytics();
    }

    // Start alert monitoring
    this.startAlertMonitoring();

    logger.info('Advanced Analytics System initialized', {
      features: [
        'Real-time Monitoring',
        'Historical Analytics',
        'Bottleneck Detection',
        'Predictive Analytics',
        'Alert System',
        'Performance Tracking',
      ],
      config: this.config,
    });
  }

  /**
   * Track event for analytics
   * @param {string} eventType - Type of event
   * @param {Object} eventData - Event data
   * @param {Object} metadata - Additional metadata
   */
  trackEvent(eventType, eventData = {}, metadata = {}) {
    try {
      const timestamp = Date.now();
      const event = {
        type: eventType,
        data: eventData,
        metadata: {
          timestamp,
          source: 'socket-gateway',
          ...metadata,
        },
      };

      // Store event
      this.metrics.events.push(event);

      // Update performance counters
      this.updatePerformanceCounters(eventType, eventData);

      // Update real-time metrics
      this.updateRealTimeMetrics(eventType, eventData);

      // Check for alerts
      this.checkAlerts(eventType, eventData);

      // Emit event for external listeners
      this.emit('eventTracked', event);

      // Clean old events
      this.cleanOldEvents();

    } catch (error) {
      logger.logError(error, {
        context: 'Track analytics event',
        eventType,
      });
    }
  }

  /**
   * Track connection event
   * @param {string} action - Connection action (connect, disconnect)
   * @param {Object} connectionData - Connection data
   */
  trackConnection(action, connectionData = {}) {
    this.trackEvent('connection', {
      action,
      socketId: connectionData.socketId,
      userId: connectionData.userId,
      userAgent: connectionData.userAgent,
      ipAddress: connectionData.ipAddress,
    });

    // Update connection count
    if (action === 'connect') {
      this.performanceCounters.totalConnections++;
      this.metrics.realTime.connections++;
    } else if (action === 'disconnect') {
      this.metrics.realTime.connections = Math.max(0, this.metrics.realTime.connections - 1);
    }
  }

  /**
   * Track message event
   * @param {string} messageType - Type of message
   * @param {Object} messageData - Message data
   * @param {number} responseTime - Response time in ms
   */
  trackMessage(messageType, messageData = {}, responseTime = 0) {
    this.trackEvent('message', {
      type: messageType,
      size: JSON.stringify(messageData).length,
      responseTime,
      channel: messageData.channel,
      userId: messageData.userId,
    });

    // Update message counters
    this.performanceCounters.totalMessages++;
    this.performanceCounters.totalResponseTime += responseTime;

    // Update operation counts
    const currentCount = this.performanceCounters.operationCounts.get(messageType) || 0;
    this.performanceCounters.operationCounts.set(messageType, currentCount + 1);
  }

  /**
   * Track error event
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   */
  trackError(error, context = {}) {
    this.trackEvent('error', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      context,
    });

    // Update error counters
    this.performanceCounters.totalErrors++;

    const errorType = error.constructor.name;
    const currentCount = this.performanceCounters.errorCounts.get(errorType) || 0;
    this.performanceCounters.errorCounts.set(errorType, currentCount + 1);
  }

  /**
   * Track user activity
   * @param {string} userId - User ID
   * @param {string} activity - Activity type
   * @param {Object} activityData - Activity data
   */
  trackUserActivity(userId, activity, activityData = {}) {
    this.trackEvent('user_activity', {
      userId,
      activity,
      ...activityData,
    });

    // Update user activity map
    if (!this.performanceCounters.userActivity.has(userId)) {
      this.performanceCounters.userActivity.set(userId, {
        activities: [],
        lastActivity: Date.now(),
        totalActivities: 0,
      });
    }

    const userStats = this.performanceCounters.userActivity.get(userId);
    userStats.activities.push({
      type: activity,
      timestamp: Date.now(),
      data: activityData,
    });
    userStats.lastActivity = Date.now();
    userStats.totalActivities++;

    // Keep only last 100 activities per user
    if (userStats.activities.length > 100) {
      userStats.activities.shift();
    }
  }

  /**
   * Track room activity
   * @param {string} roomId - Room ID
   * @param {string} activity - Activity type
   * @param {Object} activityData - Activity data
   */
  trackRoomActivity(roomId, activity, activityData = {}) {
    this.trackEvent('room_activity', {
      roomId,
      activity,
      ...activityData,
    });

    // Update room activity map
    if (!this.performanceCounters.roomActivity.has(roomId)) {
      this.performanceCounters.roomActivity.set(roomId, {
        activities: [],
        lastActivity: Date.now(),
        totalActivities: 0,
        playerCount: 0,
      });
    }

    const roomStats = this.performanceCounters.roomActivity.get(roomId);
    roomStats.activities.push({
      type: activity,
      timestamp: Date.now(),
      data: activityData,
    });
    roomStats.lastActivity = Date.now();
    roomStats.totalActivities++;

    // Update player count based on activity
    if (activity === 'player_joined') {
      roomStats.playerCount++;
    } else if (activity === 'player_left') {
      roomStats.playerCount = Math.max(0, roomStats.playerCount - 1);
    }
  }

  /**
   * Update performance counters
   * @param {string} eventType - Event type
   * @param {Object} eventData - Event data
   */
  updatePerformanceCounters(eventType, eventData) {
    // Update operation-specific counters
    switch (eventType) {
      case 'connection':
        // Already handled in trackConnection
        break;
      case 'message':
        // Already handled in trackMessage
        break;
      case 'error':
        // Already handled in trackError
        break;
    }
  }

  /**
   * Update real-time metrics
   * @param {string} eventType - Event type
   * @param {Object} eventData - Event data
   */
  updateRealTimeMetrics(eventType, eventData) {
    const now = Date.now();
    const windowSize = this.config.realTimeInterval;

    // Calculate messages per second
    const recentMessages = this.metrics.events.filter(
      event => event.metadata.timestamp > now - windowSize && event.type === 'message'
    );
    this.metrics.realTime.messagesPerSecond = recentMessages.length / (windowSize / 1000);

    // Calculate errors per second
    const recentErrors = this.metrics.events.filter(
      event => event.metadata.timestamp > now - windowSize && event.type === 'error'
    );
    this.metrics.realTime.errorsPerSecond = recentErrors.length / (windowSize / 1000);

    // Calculate average response time
    const recentMessageEvents = this.metrics.events.filter(
      event => event.metadata.timestamp > now - windowSize &&
               event.type === 'message' &&
               event.data.responseTime
    );

    if (recentMessageEvents.length > 0) {
      const totalResponseTime = recentMessageEvents.reduce(
        (sum, event) => sum + event.data.responseTime, 0
      );
      this.metrics.realTime.averageResponseTime = totalResponseTime / recentMessageEvents.length;
    }

    // Update system metrics
    this.updateSystemMetrics();
  }

  /**
   * Update system metrics (memory, CPU, etc.)
   */
  updateSystemMetrics() {
    try {
      const memUsage = process.memoryUsage();
      this.metrics.realTime.memoryUsage = memUsage.heapUsed / memUsage.heapTotal;

      // CPU usage would require additional monitoring
      // For now, we'll estimate based on event processing rate
      const eventRate = this.metrics.realTime.messagesPerSecond + this.metrics.realTime.errorsPerSecond;
      this.metrics.realTime.cpuUsage = Math.min(eventRate / 100, 1); // Rough estimation

    } catch (error) {
      logger.logError(error, { context: 'Update system metrics' });
    }
  }

  /**
   * Start real-time monitoring
   */
  startRealTimeMonitoring() {
    setInterval(() => {
      this.collectRealTimeMetrics();
    }, this.config.realTimeInterval);

    logger.info('Real-time monitoring started', {
      interval: this.config.realTimeInterval,
    });
  }

  /**
   * Collect real-time metrics
   */
  collectRealTimeMetrics() {
    try {
      // Update real-time metrics
      this.updateSystemMetrics();

      // Store historical snapshot
      const snapshot = {
        timestamp: Date.now(),
        ...JSON.parse(JSON.stringify(this.metrics.realTime)),
      };

      this.metrics.historical.push(snapshot);

      // Keep only recent history
      const retentionTime = Date.now() - this.config.metricsRetention;
      this.metrics.historical = this.metrics.historical.filter(
        metric => metric.timestamp > retentionTime
      );

      // Emit real-time update
      this.emit('realTimeUpdate', snapshot);

    } catch (error) {
      logger.logError(error, { context: 'Collect real-time metrics' });
    }
  }

  /**
   * Start data aggregation
   */
  startDataAggregation() {
    setInterval(() => {
      this.aggregateData();
    }, this.config.aggregationInterval);

    logger.info('Data aggregation started', {
      interval: this.config.aggregationInterval,
    });
  }

  /**
   * Aggregate data for historical analysis
   */
  aggregateData() {
    try {
      const now = Date.now();
      const hourStart = new Date(now);
      hourStart.setMinutes(0, 0, 0);

      // Aggregate hourly data
      const hourlyData = this.aggregateTimeWindow(hourStart.getTime(), 3600000); // 1 hour
      if (hourlyData) {
        this.metrics.aggregated.hourly.push(hourlyData);

        // Keep only last 24 hours
        if (this.metrics.aggregated.hourly.length > 24) {
          this.metrics.aggregated.hourly.shift();
        }
      }

      // Aggregate daily data (every 24 hours)
      if (new Date(now).getHours() === 0) {
        const dayStart = new Date(now);
        dayStart.setHours(0, 0, 0, 0);

        const dailyData = this.aggregateTimeWindow(dayStart.getTime(), 86400000); // 24 hours
        if (dailyData) {
          this.metrics.aggregated.daily.push(dailyData);

          // Keep only last 30 days
          if (this.metrics.aggregated.daily.length > 30) {
            this.metrics.aggregated.daily.shift();
          }
        }
      }

    } catch (error) {
      logger.logError(error, { context: 'Aggregate data' });
    }
  }

  /**
   * Aggregate data for a specific time window
   * @param {number} startTime - Window start time
   * @param {number} duration - Window duration in ms
   * @returns {Object} Aggregated data
   */
  aggregateTimeWindow(startTime, duration) {
    const endTime = startTime + duration;
    const windowEvents = this.metrics.events.filter(
      event => event.metadata.timestamp >= startTime && event.metadata.timestamp < endTime
    );

    if (windowEvents.length === 0) return null;

    const messageEvents = windowEvents.filter(e => e.type === 'message');
    const errorEvents = windowEvents.filter(e => e.type === 'error');
    const connectionEvents = windowEvents.filter(e => e.type === 'connection');

    return {
      timestamp: startTime,
      duration,
      totalEvents: windowEvents.length,
      messages: {
        count: messageEvents.length,
        averageResponseTime: messageEvents.length > 0 ?
          messageEvents.reduce((sum, e) => sum + (e.data.responseTime || 0), 0) / messageEvents.length : 0,
        types: this.aggregateEventTypes(messageEvents),
      },
      errors: {
        count: errorEvents.length,
        types: this.aggregateEventTypes(errorEvents),
      },
      connections: {
        connects: connectionEvents.filter(e => e.data.action === 'connect').length,
        disconnects: connectionEvents.filter(e => e.data.action === 'disconnect').length,
      },
    };
  }

  /**
   * Aggregate event types
   * @param {Array} events - Events to aggregate
   * @returns {Object} Aggregated event types
   */
  aggregateEventTypes(events) {
    const types = {};
    events.forEach(event => {
      const type = event.data.type || event.type;
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }

  /**
   * Clean old events to prevent memory leaks
   */
  cleanOldEvents() {
    const retentionTime = Date.now() - this.config.metricsRetention;
    this.metrics.events = this.metrics.events.filter(
      event => event.metadata.timestamp > retentionTime
    );
  }

  /**
   * Get analytics dashboard data
   * @returns {Object} Dashboard data
   */
  getDashboardData() {
    return {
      realTime: this.metrics.realTime,
      historical: this.metrics.historical.slice(-60), // Last hour
      aggregated: {
        hourly: this.metrics.aggregated.hourly.slice(-24), // Last 24 hours
        daily: this.metrics.aggregated.daily.slice(-7), // Last 7 days
      },
      performance: {
        totalConnections: this.performanceCounters.totalConnections,
        totalMessages: this.performanceCounters.totalMessages,
        totalErrors: this.performanceCounters.totalErrors,
        averageResponseTime: this.performanceCounters.totalMessages > 0 ?
          this.performanceCounters.totalResponseTime / this.performanceCounters.totalMessages : 0,
        errorRate: this.performanceCounters.totalMessages > 0 ?
          this.performanceCounters.totalErrors / this.performanceCounters.totalMessages : 0,
      },
      topOperations: Array.from(this.performanceCounters.operationCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10),
      topErrors: Array.from(this.performanceCounters.errorCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10),
      activeUsers: this.performanceCounters.userActivity.size,
      activeRooms: this.performanceCounters.roomActivity.size,
      alerts: this.metrics.alerts.slice(-10), // Last 10 alerts
      bottlenecks: this.bottlenecks.detected,
    };
  }

  /**
   * Start alert monitoring
   */
  startAlertMonitoring() {
    setInterval(() => {
      this.checkSystemAlerts();
    }, 30000); // Check every 30 seconds

    logger.info('Alert monitoring started');
  }

  /**
   * Check for alerts based on current metrics
   * @param {string} eventType - Event type that triggered check
   * @param {Object} eventData - Event data
   */
  checkAlerts(eventType, eventData) {
    const now = Date.now();
    const thresholds = this.config.alertThresholds;

    // Check error rate
    const errorRate = this.performanceCounters.totalMessages > 0 ?
      this.performanceCounters.totalErrors / this.performanceCounters.totalMessages : 0;

    if (errorRate > thresholds.errorRate) {
      this.createAlert('high_error_rate', {
        currentRate: errorRate,
        threshold: thresholds.errorRate,
        totalErrors: this.performanceCounters.totalErrors,
        totalMessages: this.performanceCounters.totalMessages,
      });
    }

    // Check response time
    if (eventType === 'message' && eventData.responseTime > thresholds.responseTime) {
      this.createAlert('slow_response', {
        responseTime: eventData.responseTime,
        threshold: thresholds.responseTime,
        messageType: eventData.type,
      });
    }

    // Check connection count
    if (this.metrics.realTime.connections > thresholds.connectionCount) {
      this.createAlert('high_connection_count', {
        currentConnections: this.metrics.realTime.connections,
        threshold: thresholds.connectionCount,
      });
    }
  }

  /**
   * Check system-wide alerts
   */
  checkSystemAlerts() {
    const thresholds = this.config.alertThresholds;

    // Check memory usage
    if (this.metrics.realTime.memoryUsage > thresholds.memoryUsage) {
      this.createAlert('high_memory_usage', {
        currentUsage: this.metrics.realTime.memoryUsage,
        threshold: thresholds.memoryUsage,
      });
    }

    // Check messages per second spike
    const avgMessagesPerSecond = this.calculateAverageMessagesPerSecond();
    if (this.metrics.realTime.messagesPerSecond > avgMessagesPerSecond * 3) {
      this.createAlert('message_spike', {
        currentRate: this.metrics.realTime.messagesPerSecond,
        averageRate: avgMessagesPerSecond,
      });
    }
  }

  /**
   * Create an alert
   * @param {string} alertType - Type of alert
   * @param {Object} alertData - Alert data
   */
  createAlert(alertType, alertData) {
    const alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: alertType,
      severity: this.getAlertSeverity(alertType),
      data: alertData,
      timestamp: Date.now(),
      acknowledged: false,
    };

    this.metrics.alerts.push(alert);

    // Keep only last 100 alerts
    if (this.metrics.alerts.length > 100) {
      this.metrics.alerts.shift();
    }

    logger.warn('Alert created', {
      alertId: alert.id,
      type: alertType,
      severity: alert.severity,
      data: alertData,
    });

    this.emit('alertCreated', alert);
  }

  /**
   * Get alert severity level
   * @param {string} alertType - Alert type
   * @returns {string} Severity level
   */
  getAlertSeverity(alertType) {
    const severityMap = {
      high_error_rate: 'critical',
      high_memory_usage: 'critical',
      high_connection_count: 'warning',
      slow_response: 'warning',
      message_spike: 'info',
    };

    return severityMap[alertType] || 'info';
  }

  /**
   * Start bottleneck detection
   */
  startBottleneckDetection() {
    setInterval(() => {
      this.detectBottlenecks();
    }, 60000); // Check every minute

    logger.info('Bottleneck detection started');
  }

  /**
   * Detect performance bottlenecks
   */
  detectBottlenecks() {
    try {
      const bottlenecks = [];

      // Check for slow operations
      const slowOperations = this.findSlowOperations();
      if (slowOperations.length > 0) {
        bottlenecks.push({
          type: 'slow_operations',
          severity: 'warning',
          data: slowOperations,
          timestamp: Date.now(),
        });
      }

      // Check for memory bottlenecks
      if (this.metrics.realTime.memoryUsage > this.config.bottleneckThreshold) {
        bottlenecks.push({
          type: 'memory_bottleneck',
          severity: 'critical',
          data: {
            memoryUsage: this.metrics.realTime.memoryUsage,
            threshold: this.config.bottleneckThreshold,
          },
          timestamp: Date.now(),
        });
      }

      // Check for connection bottlenecks
      const connectionGrowthRate = this.calculateConnectionGrowthRate();
      if (connectionGrowthRate > 10) { // More than 10 connections per second
        bottlenecks.push({
          type: 'connection_bottleneck',
          severity: 'warning',
          data: {
            growthRate: connectionGrowthRate,
            currentConnections: this.metrics.realTime.connections,
          },
          timestamp: Date.now(),
        });
      }

      // Update detected bottlenecks
      this.bottlenecks.detected = bottlenecks;

      // Store in history
      if (bottlenecks.length > 0) {
        this.bottlenecks.history.push({
          timestamp: Date.now(),
          bottlenecks: [...bottlenecks],
        });

        // Keep only last 100 entries
        if (this.bottlenecks.history.length > 100) {
          this.bottlenecks.history.shift();
        }

        logger.warn('Bottlenecks detected', {
          count: bottlenecks.length,
          types: bottlenecks.map(b => b.type),
        });

        this.emit('bottlenecksDetected', bottlenecks);
      }

    } catch (error) {
      logger.logError(error, { context: 'Detect bottlenecks' });
    }
  }

  /**
   * Find slow operations
   * @returns {Array} Slow operations
   */
  findSlowOperations() {
    const slowOperations = [];
    const responseTimeThreshold = this.config.alertThresholds.responseTime;

    // Analyze recent message events
    const recentMessages = this.metrics.events.filter(
      event => event.type === 'message' &&
               event.metadata.timestamp > Date.now() - 300000 && // Last 5 minutes
               event.data.responseTime > responseTimeThreshold
    );

    // Group by message type
    const operationStats = {};
    recentMessages.forEach(event => {
      const type = event.data.type;
      if (!operationStats[type]) {
        operationStats[type] = {
          count: 0,
          totalTime: 0,
          maxTime: 0,
        };
      }

      operationStats[type].count++;
      operationStats[type].totalTime += event.data.responseTime;
      operationStats[type].maxTime = Math.max(operationStats[type].maxTime, event.data.responseTime);
    });

    // Find operations with high average response time
    Object.entries(operationStats).forEach(([type, stats]) => {
      const avgTime = stats.totalTime / stats.count;
      if (avgTime > responseTimeThreshold && stats.count >= 5) {
        slowOperations.push({
          operation: type,
          averageTime: avgTime,
          maxTime: stats.maxTime,
          count: stats.count,
          threshold: responseTimeThreshold,
        });
      }
    });

    return slowOperations;
  }

  /**
   * Calculate average messages per second over time
   * @returns {number} Average messages per second
   */
  calculateAverageMessagesPerSecond() {
    const recentHistory = this.metrics.historical.slice(-12); // Last 12 data points
    if (recentHistory.length === 0) return 0;

    const totalMessages = recentHistory.reduce((sum, metric) => sum + metric.messagesPerSecond, 0);
    return totalMessages / recentHistory.length;
  }

  /**
   * Calculate connection growth rate
   * @returns {number} Connections per second growth rate
   */
  calculateConnectionGrowthRate() {
    const recentHistory = this.metrics.historical.slice(-6); // Last 6 data points (30 seconds)
    if (recentHistory.length < 2) return 0;

    const oldConnections = recentHistory[0].connections;
    const newConnections = recentHistory[recentHistory.length - 1].connections;
    const timeDiff = (recentHistory[recentHistory.length - 1].timestamp - recentHistory[0].timestamp) / 1000;

    return timeDiff > 0 ? (newConnections - oldConnections) / timeDiff : 0;
  }

  /**
   * Start predictive analytics
   */
  startPredictiveAnalytics() {
    setInterval(() => {
      this.generatePredictions();
    }, 300000); // Every 5 minutes

    logger.info('Predictive analytics started');
  }

  /**
   * Generate predictions based on historical data
   */
  generatePredictions() {
    try {
      // Predict connection growth
      const connectionPrediction = this.predictConnectionGrowth();
      if (connectionPrediction) {
        this.predictions.connectionGrowth.push(connectionPrediction);

        // Keep only last 24 predictions
        if (this.predictions.connectionGrowth.length > 24) {
          this.predictions.connectionGrowth.shift();
        }
      }

      // Predict load forecast
      const loadPrediction = this.predictLoadForecast();
      if (loadPrediction) {
        this.predictions.loadForecast.push(loadPrediction);

        if (this.predictions.loadForecast.length > 24) {
          this.predictions.loadForecast.shift();
        }
      }

      this.emit('predictionsUpdated', {
        connectionGrowth: this.predictions.connectionGrowth,
        loadForecast: this.predictions.loadForecast,
      });

    } catch (error) {
      logger.logError(error, { context: 'Generate predictions' });
    }
  }

  /**
   * Predict connection growth using linear regression
   * @returns {Object} Connection growth prediction
   */
  predictConnectionGrowth() {
    const recentHistory = this.metrics.historical.slice(-60); // Last hour
    if (recentHistory.length < 10) return null;

    // Simple linear regression
    const n = recentHistory.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    recentHistory.forEach((point, index) => {
      const x = index;
      const y = point.connections;
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    });

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Predict next hour
    const nextHourConnections = slope * n + intercept;

    return {
      timestamp: Date.now(),
      currentConnections: this.metrics.realTime.connections,
      predictedConnections: Math.max(0, Math.round(nextHourConnections)),
      growthRate: slope,
      confidence: this.calculatePredictionConfidence(recentHistory, slope, intercept),
    };
  }

  /**
   * Predict load forecast
   * @returns {Object} Load forecast prediction
   */
  predictLoadForecast() {
    const recentHistory = this.metrics.historical.slice(-60);
    if (recentHistory.length < 10) return null;

    const avgMessagesPerSecond = recentHistory.reduce((sum, point) => sum + point.messagesPerSecond, 0) / recentHistory.length;
    const avgMemoryUsage = recentHistory.reduce((sum, point) => sum + point.memoryUsage, 0) / recentHistory.length;

    // Simple load prediction based on current trends
    const loadScore = (avgMessagesPerSecond / 100) + (avgMemoryUsage * 100) + (this.metrics.realTime.connections / 1000);

    return {
      timestamp: Date.now(),
      loadScore: Math.round(loadScore * 100) / 100,
      predictedLoad: loadScore > 5 ? 'high' : loadScore > 2 ? 'medium' : 'low',
      factors: {
        messagesPerSecond: avgMessagesPerSecond,
        memoryUsage: avgMemoryUsage,
        connections: this.metrics.realTime.connections,
      },
    };
  }

  /**
   * Calculate prediction confidence
   * @param {Array} data - Historical data
   * @param {number} slope - Regression slope
   * @param {number} intercept - Regression intercept
   * @returns {number} Confidence score (0-1)
   */
  calculatePredictionConfidence(data, slope, intercept) {
    if (data.length === 0) return 0;

    // Calculate R-squared
    const yMean = data.reduce((sum, point) => sum + point.connections, 0) / data.length;
    let ssRes = 0, ssTot = 0;

    data.forEach((point, index) => {
      const yPred = slope * index + intercept;
      const yActual = point.connections;

      ssRes += Math.pow(yActual - yPred, 2);
      ssTot += Math.pow(yActual - yMean, 2);
    });

    const rSquared = ssTot === 0 ? 0 : 1 - (ssRes / ssTot);
    return Math.max(0, Math.min(1, rSquared));
  }

  /**
   * Cleanup analytics system
   */
  async cleanup() {
    logger.info('Cleaning up Advanced Analytics System');

    // Clear all data
    this.metrics.events.length = 0;
    this.metrics.historical.length = 0;
    this.metrics.aggregated.hourly.length = 0;
    this.metrics.aggregated.daily.length = 0;
    this.metrics.alerts.length = 0;

    this.performanceCounters.operationCounts.clear();
    this.performanceCounters.errorCounts.clear();
    this.performanceCounters.userActivity.clear();
    this.performanceCounters.roomActivity.clear();

    this.bottlenecks.detected.length = 0;
    this.bottlenecks.history.length = 0;
    this.bottlenecks.patterns.clear();

    logger.info('Advanced Analytics System cleanup completed');
  }
}

module.exports = AdvancedAnalytics;