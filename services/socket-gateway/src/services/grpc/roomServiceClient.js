/**
 * Room Service gRPC Client
 * 
 * Handles gRPC communication with the Room Service for fetching
 * room information, player data, and room state.
 */

const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const logger = require('../../utils/logger');

// Proto file path - we'll need to copy the room.proto file
const PROTO_PATH = path.join(__dirname, '../../../proto/room.proto');

let roomServiceClient = null;
let isConnected = false;

/**
 * Load proto definition
 */
const loadProtoDefinition = () => {
  try {
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    });

    return grpc.loadPackageDefinition(packageDefinition);
  } catch (error) {
    logger.error('Failed to load room service proto definition:', error);
    throw error;
  }
};

/**
 * Initialize Room Service gRPC client
 */
const initialize = async () => {
  try {
    if (roomServiceClient && isConnected) {
      logger.info('Room service client already initialized');
      return;
    }

    const proto = loadProtoDefinition();

    // The service is nested in the package structure
    let RoomService;
    if (proto.RoomService) {
      RoomService = proto.RoomService;
    } else if (proto.room && proto.room.RoomService) {
      RoomService = proto.room.RoomService;
    } else {
      // Try to find the service in the loaded proto
      const keys = Object.keys(proto);
      logger.debug('Available proto keys:', keys);
      throw new Error('RoomService not found in proto definition');
    }

    const roomServiceUrl = process.env.ROOM_SERVICE_GRPC_URL || 'localhost:8082';

    roomServiceClient = new RoomService(
      roomServiceUrl,
      grpc.credentials.createInsecure(),
      {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000,
      }
    );

    // Test connection
    await healthCheck();

    isConnected = true;
    logger.info('Room service client initialized successfully', {
      url: roomServiceUrl,
    });
  } catch (error) {
    logger.error('Failed to initialize room service client:', error);
    throw error;
  }
};

/**
 * Close Room Service gRPC client
 */
const close = () => {
  if (roomServiceClient) {
    roomServiceClient.close();
    roomServiceClient = null;
  }
  isConnected = false;
  logger.info('Room service client closed');
};

/**
 * Make gRPC call with error handling
 */
const makeCall = async (method, request, timeout = 10000) => {
  if (!roomServiceClient || !isConnected) {
    throw new Error('Room service client not initialized');
  }

  const startTime = Date.now();

  try {
    const response = await new Promise((resolve, reject) => {
      const deadline = new Date(Date.now() + timeout);

      roomServiceClient[method](request, { deadline }, (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      });
    });

    const duration = Date.now() - startTime;
    logger.debug('Room service gRPC call completed', {
      method,
      duration,
      success: true,
    });

    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Room service gRPC call failed', {
      method,
      duration,
      error: error.message,
    });

    throw error;
  }
};

/**
 * Health check for Room Service
 */
const healthCheck = async () => {
  try {
    // For now, we'll skip the health check since we don't have a health endpoint
    // In a real implementation, you would call a health check method
    logger.debug('Room service health check skipped (not implemented)');
    return true;
  } catch (error) {
    logger.warn('Room service health check failed:', error);
    return false;
  }
};

/**
 * Get comprehensive room information
 * @param {string} roomId - Room ID
 * @returns {Object} Room information
 */
const getRoomInfo = async (roomId) => {
  try {
    const request = { room_id: roomId };
    const response = await makeCall('GetRoomInfo', request);

    if (response.error) {
      throw new Error(response.error);
    }

    // Convert protobuf response to JavaScript object
    const roomInfo = response.room_info;
    return {
      roomId: roomInfo.room_id,
      name: roomInfo.name,
      gameType: roomInfo.game_type,
      status: roomInfo.status,
      currentPlayers: roomInfo.current_players,
      maxPlayers: roomInfo.max_players,
      minPlayers: roomInfo.min_players,
      betAmount: roomInfo.bet_amount,
      players: roomInfo.players?.map(player => ({
        userId: player.user_id,
        username: player.username,
        position: player.position,
        isReady: player.is_ready,
        betAmount: player.bet_amount,
        joinedAt: player.joined_at ? (player.joined_at.toISOString ? player.joined_at.toISOString() : player.joined_at.toString()) : null,
        status: player.status,
      })) || [],
      isPrivate: roomInfo.is_private,
      autoStart: roomInfo.auto_start,
      createdAt: roomInfo.created_at ? (roomInfo.created_at.toISOString ? roomInfo.created_at.toISOString() : roomInfo.created_at.toString()) : null,
      lastActivity: roomInfo.last_activity ? (roomInfo.last_activity.toISOString ? roomInfo.last_activity.toISOString() : roomInfo.last_activity.toString()) : null,
      canJoin: roomInfo.can_join,
      canStart: roomInfo.can_start,
    };
  } catch (error) {
    logger.error('Failed to get room info from room service:', {
      roomId,
      error: error.message,
    });
    throw error;
  }
};

/**
 * Get basic room information (fallback method)
 * @param {string} roomId - Room ID
 * @returns {Object} Basic room information
 */
const getRoom = async (roomId) => {
  try {
    const request = { room_id: roomId };
    const response = await makeCall('GetRoom', request);

    if (response.error) {
      throw new Error(response.error);
    }

    return response.room;
  } catch (error) {
    logger.error('Failed to get room from room service:', {
      roomId,
      error: error.message,
    });
    throw error;
  }
};

module.exports = {
  initialize,
  close,
  healthCheck,
  getRoomInfo,
  getRoom,
  isConnected: () => isConnected,
};
