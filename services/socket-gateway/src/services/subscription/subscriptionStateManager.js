/**
 * Subscription State Manager
 * 
 * Manages the overall subscription state tracking for users and sockets.
 * Coordinates between lobby and room subscription managers.
 */

const logger = require('../../utils/logger');

class SubscriptionStateManager {
  constructor() {
    // Track user subscriptions: userId -> { channel, roomId?, socketId, timestamp }
    this.userSubscriptions = new Map();
    
    // Track socket subscriptions: socketId -> { userId, channel, roomId?, timestamp }
    this.socketSubscriptions = new Map();
  }

  /**
   * Update user subscription state
   * @param {string} userId - User ID
   * @param {Object} subscriptionData - Subscription data
   */
  updateUserSubscription(userId, subscriptionData) {
    this.userSubscriptions.set(userId, subscriptionData);
    
    logger.debug('Updated user subscription state', {
      userId,
      channel: subscriptionData.channel,
      roomId: subscriptionData.roomId,
      socketId: subscriptionData.socketId,
    });
  }

  /**
   * Update socket subscription state
   * @param {string} socketId - Socket ID
   * @param {Object} subscriptionData - Subscription data including userId
   */
  updateSocketSubscription(socketId, subscriptionData) {
    this.socketSubscriptions.set(socketId, subscriptionData);
    
    logger.debug('Updated socket subscription state', {
      socketId,
      userId: subscriptionData.userId,
      channel: subscriptionData.channel,
      roomId: subscriptionData.roomId,
    });
  }

  /**
   * Remove user subscription
   * @param {string} userId - User ID
   * @returns {Object|null} Removed subscription data
   */
  removeUserSubscription(userId) {
    const subscription = this.userSubscriptions.get(userId);
    this.userSubscriptions.delete(userId);
    
    if (subscription) {
      logger.debug('Removed user subscription', {
        userId,
        channel: subscription.channel,
        roomId: subscription.roomId,
      });
    }
    
    return subscription;
  }

  /**
   * Remove socket subscription
   * @param {string} socketId - Socket ID
   * @returns {Object|null} Removed subscription data
   */
  removeSocketSubscription(socketId) {
    const subscription = this.socketSubscriptions.get(socketId);
    this.socketSubscriptions.delete(socketId);
    
    if (subscription) {
      logger.debug('Removed socket subscription', {
        socketId,
        userId: subscription.userId,
        channel: subscription.channel,
        roomId: subscription.roomId,
      });
    }
    
    return subscription;
  }

  /**
   * Get user subscription
   * @param {string} userId - User ID
   * @returns {Object|null} User subscription data
   */
  getUserSubscription(userId) {
    return this.userSubscriptions.get(userId) || null;
  }

  /**
   * Get socket subscription
   * @param {string} socketId - Socket ID
   * @returns {Object|null} Socket subscription data
   */
  getSocketSubscription(socketId) {
    return this.socketSubscriptions.get(socketId) || null;
  }

  /**
   * Check if user is subscribed to lobby
   * @param {string} userId - User ID
   * @returns {boolean} True if subscribed to lobby
   */
  isUserSubscribedToLobby(userId) {
    const subscription = this.userSubscriptions.get(userId);
    return subscription && subscription.channel === 'lobby';
  }

  /**
   * Check if user is subscribed to a room
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID (optional, checks any room if not provided)
   * @returns {boolean} True if subscribed to the room
   */
  isUserSubscribedToRoom(userId, roomId = null) {
    const subscription = this.userSubscriptions.get(userId);
    if (!subscription || subscription.channel !== 'room') {
      return false;
    }
    
    return roomId ? subscription.roomId === roomId : true;
  }

  /**
   * Get user's current room subscription
   * @param {string} userId - User ID
   * @returns {string|null} Room ID if subscribed to a room, null otherwise
   */
  getUserCurrentRoom(userId) {
    const subscription = this.userSubscriptions.get(userId);
    return (subscription && subscription.channel === 'room') ? subscription.roomId : null;
  }

  /**
   * Handle socket disconnect cleanup
   * @param {string} socketId - Socket ID
   * @returns {Object} Cleanup result with removed subscription info
   */
  handleSocketDisconnect(socketId) {
    const socketSubscription = this.removeSocketSubscription(socketId);
    
    if (!socketSubscription) {
      return { removed: false };
    }

    // Also remove user subscription if it matches this socket
    const userSubscription = this.userSubscriptions.get(socketSubscription.userId);
    if (userSubscription && userSubscription.socketId === socketId) {
      this.removeUserSubscription(socketSubscription.userId);
    }

    logger.info('Cleaned up subscription state on disconnect', {
      socketId,
      userId: socketSubscription.userId,
      channel: socketSubscription.channel,
      roomId: socketSubscription.roomId,
    });

    return {
      removed: true,
      userId: socketSubscription.userId,
      channel: socketSubscription.channel,
      roomId: socketSubscription.roomId,
    };
  }

  /**
   * Transition user from lobby to room subscription
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   * @param {string} roomId - Room ID
   */
  transitionToRoom(userId, socketId, roomId) {
    const subscriptionData = {
      channel: 'room',
      roomId,
      socketId,
      timestamp: Date.now(),
    };

    this.updateUserSubscription(userId, subscriptionData);
    this.updateSocketSubscription(socketId, {
      userId,
      ...subscriptionData,
    });

    logger.info('Transitioned user subscription to room', {
      userId,
      socketId,
      roomId,
    });
  }

  /**
   * Transition user from room to lobby subscription
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   */
  transitionToLobby(userId, socketId) {
    const subscriptionData = {
      channel: 'lobby',
      socketId,
      timestamp: Date.now(),
    };

    this.updateUserSubscription(userId, subscriptionData);
    this.updateSocketSubscription(socketId, {
      userId,
      ...subscriptionData,
    });

    logger.info('Transitioned user subscription to lobby', {
      userId,
      socketId,
    });
  }

  /**
   * Get all users subscribed to a specific room
   * @param {string} roomId - Room ID
   * @returns {Array} Array of user IDs
   */
  getUsersInRoom(roomId) {
    const users = [];
    
    for (const [userId, subscription] of this.userSubscriptions.entries()) {
      if (subscription.channel === 'room' && subscription.roomId === roomId) {
        users.push(userId);
      }
    }
    
    return users;
  }

  /**
   * Get all users subscribed to lobby
   * @returns {Array} Array of user IDs
   */
  getUsersInLobby() {
    const users = [];
    
    for (const [userId, subscription] of this.userSubscriptions.entries()) {
      if (subscription.channel === 'lobby') {
        users.push(userId);
      }
    }
    
    return users;
  }

  /**
   * Clear all subscription state (for testing/cleanup)
   */
  clearAllState() {
    const userCount = this.userSubscriptions.size;
    const socketCount = this.socketSubscriptions.size;
    
    this.userSubscriptions.clear();
    this.socketSubscriptions.clear();
    
    logger.debug('Cleared all subscription state', {
      clearedUsers: userCount,
      clearedSockets: socketCount,
    });
    
    return { userCount, socketCount };
  }

  /**
   * Get subscription statistics
   * @returns {Object} Statistics object
   */
  getStatistics() {
    const stats = {
      totalUsers: this.userSubscriptions.size,
      totalSockets: this.socketSubscriptions.size,
      lobbyUsers: 0,
      roomUsers: 0,
      roomBreakdown: {},
      timestamp: new Date().toISOString(),
    };

    // Analyze user subscriptions
    for (const [userId, subscription] of this.userSubscriptions.entries()) {
      if (subscription.channel === 'lobby') {
        stats.lobbyUsers++;
      } else if (subscription.channel === 'room') {
        stats.roomUsers++;
        const roomId = subscription.roomId;
        if (!stats.roomBreakdown[roomId]) {
          stats.roomBreakdown[roomId] = 0;
        }
        stats.roomBreakdown[roomId]++;
      }
    }

    return stats;
  }

  /**
   * Validate subscription state consistency
   * @returns {Object} Validation result
   */
  validateState() {
    const issues = [];
    
    // Check for orphaned socket subscriptions
    for (const [socketId, socketSub] of this.socketSubscriptions.entries()) {
      const userSub = this.userSubscriptions.get(socketSub.userId);
      if (!userSub || userSub.socketId !== socketId) {
        issues.push({
          type: 'orphaned_socket',
          socketId,
          userId: socketSub.userId,
        });
      }
    }

    // Check for orphaned user subscriptions
    for (const [userId, userSub] of this.userSubscriptions.entries()) {
      const socketSub = this.socketSubscriptions.get(userSub.socketId);
      if (!socketSub || socketSub.userId !== userId) {
        issues.push({
          type: 'orphaned_user',
          userId,
          socketId: userSub.socketId,
        });
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      timestamp: new Date().toISOString(),
    };
  }
}

module.exports = SubscriptionStateManager;
