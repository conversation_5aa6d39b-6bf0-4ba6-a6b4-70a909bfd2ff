/**
 * Room Subscription Manager
 * 
 * Handles room subscription logic and state management.
 * Separated from main subscription service for better modularity.
 */

const logger = require('../../utils/logger');

class RoomSubscriptionManager {
  constructor() {
    // Track room subscribers: roomId -> Set of socketIds
    this.roomSubscribers = new Map();
  }

  /**
   * Subscribe socket to room
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   * @returns {Object} Subscription result
   */
  async subscribeRoom(socket, userId, roomId) {
    try {
      const socketId = socket.id;
      
      logger.info('Processing room subscription', {
        socketId,
        userId,
        roomId,
        currentRoomSubscribers: this.getRoomSubscriberCount(roomId),
      });

      // Join room socket room
      await this.joinRoomSocketRoom(socket, roomId);
      
      // Track room subscribers
      if (!this.roomSubscribers.has(roomId)) {
        this.roomSubscribers.set(roomId, new Set());
      }
      this.roomSubscribers.get(roomId).add(socketId);

      logger.info('Successfully subscribed to room', {
        socketId,
        userId,
        roomId,
        roomSubscriberCount: this.roomSubscribers.get(roomId).size,
      });

      return {
        success: true,
        channel: 'room',
        roomId,
        socketId,
        timestamp: Date.now(),
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe room',
        socketId: socket.id,
        userId,
        roomId,
      });
      throw error;
    }
  }

  /**
   * Unsubscribe socket from room
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   * @returns {Object} Unsubscription result
   */
  async unsubscribeRoom(socket, userId, roomId) {
    try {
      const socketId = socket.id;
      
      logger.info('Processing room unsubscription', {
        socketId,
        userId,
        roomId,
        currentRoomSubscribers: this.getRoomSubscriberCount(roomId),
      });

      // Leave room socket room
      await this.leaveRoomSocketRoom(socket, roomId);
      
      // Remove from room subscribers tracking
      if (this.roomSubscribers.has(roomId)) {
        this.roomSubscribers.get(roomId).delete(socketId);
        if (this.roomSubscribers.get(roomId).size === 0) {
          this.roomSubscribers.delete(roomId);
        }
      }

      logger.info('Successfully unsubscribed from room', {
        socketId,
        userId,
        roomId,
        roomSubscriberCount: this.getRoomSubscriberCount(roomId),
      });

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe room',
        socketId: socket.id,
        userId,
        roomId,
      });
      throw error;
    }
  }

  /**
   * Join room socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   */
  async joinRoomSocketRoom(socket, roomId) {
    const roomChannel = `room:${roomId}`;

    try {
      // Modern Socket.IO uses Promise-based API
      await socket.join(roomChannel);

      logger.debug('Socket joined room channel', {
        socketId: socket.id,
        roomId,
        roomChannel,
      });
    } catch (error) {
      logger.error('Socket.join room operation failed', {
        socketId: socket.id,
        roomId,
        roomChannel,
        error: error.message,
      });
      throw error; // Re-throw to fail the operation
    }
  }

  /**
   * Leave room socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} roomId - Room ID
   */
  async leaveRoomSocketRoom(socket, roomId) {
    const roomChannel = `room:${roomId}`;

    try {
      // Modern Socket.IO uses Promise-based API
      await socket.leave(roomChannel);

      logger.debug('Socket left room channel', {
        socketId: socket.id,
        roomId,
        roomChannel,
      });
    } catch (error) {
      logger.error('Socket.leave room operation failed', {
        socketId: socket.id,
        roomId,
        roomChannel,
        error: error.message,
      });
      throw error; // Re-throw to fail the operation
    }
  }

  /**
   * Check if socket is subscribed to a specific room
   * @param {string} socketId - Socket ID
   * @param {string} roomId - Room ID
   * @returns {boolean} True if subscribed to the room
   */
  isSubscribedToRoom(socketId, roomId) {
    return this.roomSubscribers.has(roomId) && 
           this.roomSubscribers.get(roomId).has(socketId);
  }

  /**
   * Get room subscriber count
   * @param {string} roomId - Room ID
   * @returns {number} Number of room subscribers
   */
  getRoomSubscriberCount(roomId) {
    return this.roomSubscribers.get(roomId)?.size || 0;
  }

  /**
   * Get all room subscribers
   * @param {string} roomId - Room ID
   * @returns {Array} Array of socket IDs
   */
  getRoomSubscribers(roomId) {
    return Array.from(this.roomSubscribers.get(roomId) || []);
  }

  /**
   * Get all rooms that have subscribers
   * @returns {Array} Array of room IDs
   */
  getActiveRooms() {
    return Array.from(this.roomSubscribers.keys());
  }

  /**
   * Remove socket from all room subscriptions (for cleanup)
   * @param {string} socketId - Socket ID
   * @returns {Array} Array of room IDs the socket was removed from
   */
  removeSocketFromAllRooms(socketId) {
    const removedFromRooms = [];
    
    for (const [roomId, subscribers] of this.roomSubscribers.entries()) {
      if (subscribers.has(socketId)) {
        subscribers.delete(socketId);
        removedFromRooms.push(roomId);
        
        // Clean up empty room subscriber sets
        if (subscribers.size === 0) {
          this.roomSubscribers.delete(roomId);
        }
      }
    }
    
    if (removedFromRooms.length > 0) {
      logger.debug('Removed socket from room subscriptions', {
        socketId,
        removedFromRooms,
        remainingActiveRooms: this.roomSubscribers.size,
      });
    }
    
    return removedFromRooms;
  }

  /**
   * Remove socket from specific room subscription
   * @param {string} socketId - Socket ID
   * @param {string} roomId - Room ID
   * @returns {boolean} True if socket was removed
   */
  removeSocketFromRoom(socketId, roomId) {
    if (!this.roomSubscribers.has(roomId)) {
      return false;
    }
    
    const subscribers = this.roomSubscribers.get(roomId);
    const wasSubscribed = subscribers.has(socketId);
    
    if (wasSubscribed) {
      subscribers.delete(socketId);
      
      // Clean up empty room subscriber set
      if (subscribers.size === 0) {
        this.roomSubscribers.delete(roomId);
      }
      
      logger.debug('Removed socket from room subscription', {
        socketId,
        roomId,
        remainingSubscribers: subscribers.size,
      });
    }
    
    return wasSubscribed;
  }

  /**
   * Clear all room subscriptions (for testing/cleanup)
   */
  clearAllSubscriptions() {
    const roomCount = this.roomSubscribers.size;
    let totalSubscribers = 0;
    
    for (const subscribers of this.roomSubscribers.values()) {
      totalSubscribers += subscribers.size;
    }
    
    this.roomSubscribers.clear();
    
    logger.debug('Cleared all room subscriptions', {
      clearedRooms: roomCount,
      clearedSubscribers: totalSubscribers,
    });
    
    return { roomCount, totalSubscribers };
  }

  /**
   * Get room subscription statistics
   * @returns {Object} Statistics object
   */
  getStatistics() {
    const stats = {
      totalRooms: this.roomSubscribers.size,
      totalSubscribers: 0,
      roomDetails: {},
      timestamp: new Date().toISOString(),
    };
    
    for (const [roomId, subscribers] of this.roomSubscribers.entries()) {
      const subscriberCount = subscribers.size;
      stats.totalSubscribers += subscriberCount;
      stats.roomDetails[roomId] = {
        subscriberCount,
        subscriberIds: Array.from(subscribers),
      };
    }
    
    return stats;
  }
}

module.exports = RoomSubscriptionManager;
