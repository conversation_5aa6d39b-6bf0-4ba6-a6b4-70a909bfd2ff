/**
 * Lobby Subscription Manager
 * 
 * Handles lobby subscription logic and state management.
 * Separated from main subscription service for better modularity.
 */

const logger = require('../../utils/logger');

class LobbySubscriptionManager {
  constructor() {
    // Track lobby subscribers: Set of socketIds
    this.lobbySubscribers = new Set();
  }

  /**
   * Subscribe socket to lobby
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @returns {Object} Subscription result
   */
  async subscribeLobby(socket, userId, username) {
    try {
      const socketId = socket.id;
      
      logger.info('Processing lobby subscription', {
        socketId,
        userId,
        username,
        currentLobbySubscribers: this.lobbySubscribers.size,
      });

      // Join lobby socket room
      await this.joinLobbySocketRoom(socket);
      
      // Add to lobby subscribers tracking
      this.lobbySubscribers.add(socketId);

      logger.info('Successfully subscribed to lobby', {
        socketId,
        userId,
        lobbySubscriberCount: this.lobbySubscribers.size,
      });

      return {
        success: true,
        channel: 'lobby',
        socketId,
        timestamp: Date.now(),
      };
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe lobby',
        socketId: socket.id,
        userId,
      });
      throw error;
    }
  }

  /**
   * Unsubscribe socket from lobby
   * @param {Socket} socket - Socket.io socket instance
   * @param {string} userId - User ID
   * @returns {Object} Unsubscription result
   */
  async unsubscribeLobby(socket, userId) {
    try {
      const socketId = socket.id;
      
      logger.info('Processing lobby unsubscription', {
        socketId,
        userId,
        currentLobbySubscribers: this.lobbySubscribers.size,
      });

      // Leave lobby socket room
      await this.leaveLobbySocketRoom(socket);
      
      // Remove from lobby subscribers tracking
      this.lobbySubscribers.delete(socketId);

      logger.info('Successfully unsubscribed from lobby', {
        socketId,
        userId,
        lobbySubscriberCount: this.lobbySubscribers.size,
      });

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe lobby',
        socketId: socket.id,
        userId,
      });
      throw error;
    }
  }

  /**
   * Join lobby socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   */
  async joinLobbySocketRoom(socket) {
    try {
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          logger.warn('Socket.join lobby timeout, proceeding anyway', {
            socketId: socket.id,
          });
          resolve();
        }, 1000); // 1 second timeout

        socket.join('lobby', (err) => {
          clearTimeout(timeout);
          if (err) {
            logger.warn('Socket.join lobby error, proceeding anyway', {
              socketId: socket.id,
              error: err.message,
            });
          } else {
            logger.debug('Socket joined lobby channel', {
              socketId: socket.id,
            });
          }
          resolve();
        });
      });
    } catch (error) {
      logger.warn('Socket.join lobby failed, continuing anyway', {
        socketId: socket.id,
        error: error.message,
      });
    }
  }

  /**
   * Leave lobby socket room with error handling
   * @param {Socket} socket - Socket.io socket instance
   */
  async leaveLobbySocketRoom(socket) {
    try {
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          logger.warn('Socket.leave lobby timeout, proceeding anyway', {
            socketId: socket.id,
          });
          resolve();
        }, 1000); // 1 second timeout

        socket.leave('lobby', (err) => {
          clearTimeout(timeout);
          if (err) {
            logger.warn('Socket.leave lobby error, proceeding anyway', {
              socketId: socket.id,
              error: err.message,
            });
          } else {
            logger.debug('Socket left lobby channel', {
              socketId: socket.id,
            });
          }
          resolve();
        });
      });
    } catch (error) {
      logger.warn('Socket.leave lobby failed, continuing anyway', {
        socketId: socket.id,
        error: error.message,
      });
    }
  }

  /**
   * Check if socket is subscribed to lobby
   * @param {string} socketId - Socket ID
   * @returns {boolean} True if subscribed to lobby
   */
  isSubscribedToLobby(socketId) {
    return this.lobbySubscribers.has(socketId);
  }

  /**
   * Get lobby subscriber count
   * @returns {number} Number of lobby subscribers
   */
  getLobbySubscriberCount() {
    return this.lobbySubscribers.size;
  }

  /**
   * Get all lobby subscribers
   * @returns {Array} Array of socket IDs
   */
  getLobbySubscribers() {
    return Array.from(this.lobbySubscribers);
  }

  /**
   * Remove socket from lobby tracking (for cleanup)
   * @param {string} socketId - Socket ID
   */
  removeSocketFromLobby(socketId) {
    const wasSubscribed = this.lobbySubscribers.has(socketId);
    this.lobbySubscribers.delete(socketId);
    
    if (wasSubscribed) {
      logger.debug('Removed socket from lobby tracking', {
        socketId,
        remainingSubscribers: this.lobbySubscribers.size,
      });
    }
    
    return wasSubscribed;
  }

  /**
   * Clear all lobby subscriptions (for testing/cleanup)
   */
  clearAllSubscriptions() {
    const count = this.lobbySubscribers.size;
    this.lobbySubscribers.clear();
    
    logger.debug('Cleared all lobby subscriptions', {
      clearedCount: count,
    });
    
    return count;
  }

  /**
   * Get lobby subscription statistics
   * @returns {Object} Statistics object
   */
  getStatistics() {
    return {
      totalSubscribers: this.lobbySubscribers.size,
      subscriberIds: Array.from(this.lobbySubscribers),
      timestamp: new Date().toISOString(),
    };
  }
}

module.exports = LobbySubscriptionManager;
