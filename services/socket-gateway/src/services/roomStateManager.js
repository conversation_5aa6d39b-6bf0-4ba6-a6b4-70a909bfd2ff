const config = require('../config');
const logger = require('../utils/logger');
const redisService = require('./redisService');
const subscriptionService = require('./subscriptionService');

/**
 * Room State Manager
 * Manages room state transitions and coordinates with Game Service
 */
class RoomStateManager {
  constructor() {
    this.userRooms = new Map(); // userId -> Set of roomIds
    this.roomUsers = new Map(); // roomId -> Set of userIds
    this.roomStates = new Map(); // roomId -> room state cache
  }

  /**
   * Execute room join - simplified to focus on Socket.io and local tracking
   * Game Service handles all business logic and state persistence
   * @param {Object} socket - Socket instance
   * @param {Object} joinData - Join operation data
   * @returns {Object} Join result
   */
  async executeRoomJoin(socket, joinData) {
    const { roomId, userId, username, joinResult } = joinData;

    try {
      // Update local tracking for real-time events
      this.updateLocalTrackingAfterJoin(userId, roomId);

      // Add a small delay to ensure database consistency after player join
      // This prevents race conditions where room subscription happens before
      // the database transaction is fully committed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Use subscription service for proper Socket.io room management
      // This ensures lobby unsubscription and room subscription are properly tracked
      await subscriptionService.subscribeRoom(socket, userId, roomId);

      logger.info('Socket room join completed', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        localPlayerCount: this.roomUsers.get(roomId)?.size || 0,
        subscriptionUpdated: true,
      });

      return {
        success: true,
        roomState: joinResult.roomState,
        playerCount: this.roomUsers.get(roomId)?.size || 0,
      };

    } catch (error) {
      logger.logError(error, {
        context: 'Executing socket room join',
        socketId: socket.id,
        userId,
        roomId,
      });

      // Rollback local tracking on error
      this.updateLocalTrackingAfterLeave(userId, roomId);

      throw new Error(`Failed to execute socket room join: ${error.message}`);
    }
  }

  /**
   * Execute room leave - simplified to focus on Socket.io and local tracking
   * Game Service handles all business logic and state persistence
   * @param {Object} socket - Socket instance
   * @param {Object} leaveData - Leave operation data
   * @returns {Object} Leave result
   */
  async executeRoomLeave(socket, leaveData) {
    const { roomId, userId, username, reason, leaveResult } = leaveData;

    try {
      // Update local tracking for real-time events
      this.updateLocalTrackingAfterLeave(userId, roomId);

      // Use subscription service for proper Socket.io room management
      // This ensures room unsubscription and lobby subscription are properly tracked
      await subscriptionService.unsubscribeRoom(socket, userId, roomId);

      logger.info('Socket room leave completed', {
        socketId: socket.id,
        userId,
        username,
        roomId,
        reason,
        localRemainingPlayers: this.roomUsers.get(roomId)?.size || 0,
        subscriptionUpdated: true,
      });

      return {
        success: true,
        roomState: leaveResult?.roomState,
        remainingPlayers: this.roomUsers.get(roomId)?.size || 0,
      };

    } catch (error) {
      logger.logError(error, {
        context: 'Executing socket room leave',
        socketId: socket.id,
        userId,
        roomId,
        reason,
      });

      throw new Error(`Failed to execute socket room leave: ${error.message}`);
    }
  }

  /**
   * Update local tracking after join
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   */
  updateLocalTrackingAfterJoin(userId, roomId) {
    // Track user rooms
    if (!this.userRooms.has(userId)) {
      this.userRooms.set(userId, new Set());
    }
    this.userRooms.get(userId).add(roomId);

    // Track room users
    if (!this.roomUsers.has(roomId)) {
      this.roomUsers.set(roomId, new Set());
    }
    this.roomUsers.get(roomId).add(userId);
  }

  /**
   * Update local tracking after leave
   * @param {string} userId - User ID
   * @param {string} roomId - Room ID
   */
  updateLocalTrackingAfterLeave(userId, roomId) {
    // Remove from user rooms
    if (this.userRooms.has(userId)) {
      this.userRooms.get(userId).delete(roomId);
      if (this.userRooms.get(userId).size === 0) {
        this.userRooms.delete(userId);
      }
    }

    // Remove from room users
    if (this.roomUsers.has(roomId)) {
      this.roomUsers.get(roomId).delete(userId);
      if (this.roomUsers.get(roomId).size === 0) {
        this.roomUsers.delete(roomId);
        // Clear room state cache
        this.roomStates.delete(roomId);
      }
    }
  }

  // Removed rollback method - Game Service handles state persistence

  /**
   * Get room state from cache or fetch from Game Service
   * @param {string} roomId - Room ID
   * @returns {Object|null} Room state
   */
  async getRoomState(roomId) {
    try {
      // Check local cache first
      if (this.roomStates.has(roomId)) {
        const cached = this.roomStates.get(roomId);
        if (Date.now() - cached.timestamp < 30000) { // 30 second cache
          return cached.data;
        }
      }

      // Fetch from Redis using Game Service key pattern
      const gameServiceRoomKey = `room:state:${roomId}`;
      const roomStateData = await redisService.get(gameServiceRoomKey);

      if (roomStateData) {
        const roomState = JSON.parse(roomStateData);

        // Update local cache
        this.roomStates.set(roomId, {
          data: roomState,
          timestamp: Date.now(),
        });

        return roomState;
      }

      // Also try Socket Gateway key pattern for backward compatibility
      const socketGatewayRoomKey = `room_state:${roomId}`;
      const socketRoomData = await redisService.get(socketGatewayRoomKey);

      if (socketRoomData) {
        const roomState = JSON.parse(socketRoomData);

        // Update local cache
        this.roomStates.set(roomId, {
          data: roomState,
          timestamp: Date.now(),
        });

        return roomState;
      }

      return null;

    } catch (error) {
      logger.logError(error, {
        context: 'Getting room state',
        roomId,
      });
      return null;
    }
  }

  /**
   * Check if user is in a room
   * @param {string} userId - User ID
   * @returns {string|null} Room ID or null
   */
  getUserCurrentRoom(userId) {
    const userRooms = this.userRooms.get(userId);
    if (userRooms && userRooms.size > 0) {
      // Return first room (users should only be in one room)
      return Array.from(userRooms)[0];
    }
    return null;
  }

  /**
   * Get room player count
   * @param {string} roomId - Room ID
   * @returns {number} Player count
   */
  getRoomPlayerCount(roomId) {
    return this.roomUsers.get(roomId)?.size || 0;
  }

  /**
   * Add user to room (simple method for basic tracking)
   * @param {string} roomId - Room ID
   * @param {string} userId - User ID
   */
  addUserToRoom(roomId, userId) {
    this.updateLocalTrackingAfterJoin(userId, roomId);
    logger.debug('User added to room tracking', {
      userId,
      roomId,
      roomPlayerCount: this.getRoomPlayerCount(roomId),
    });
  }

  /**
   * Remove user from room (simple method for basic tracking)
   * @param {string} roomId - Room ID
   * @param {string} userId - User ID
   */
  removeUserFromRoom(roomId, userId) {
    this.updateLocalTrackingAfterLeave(userId, roomId);
    logger.debug('User removed from room tracking', {
      userId,
      roomId,
      roomPlayerCount: this.getRoomPlayerCount(roomId),
    });
  }

  /**
   * Clean up empty rooms
   */
  async cleanupEmptyRooms() {
    const emptyRooms = [];
    
    for (const [roomId, users] of this.roomUsers.entries()) {
      if (users.size === 0) {
        emptyRooms.push(roomId);
      }
    }

    for (const roomId of emptyRooms) {
      this.roomUsers.delete(roomId);
      this.roomStates.delete(roomId);
      
      // Clean up Redis keys
      try {
        const multi = redisService.client.multi();
        multi.del(`room_players:${roomId}`);
        multi.del(`room_meta:${roomId}`);
        multi.del(`room_state:${roomId}`);
        await multi.exec();
      } catch (error) {
        logger.logError(error, {
          context: 'Cleaning up empty room',
          roomId,
        });
      }
    }

    if (emptyRooms.length > 0) {
      logger.info('Cleaned up empty rooms', {
        roomCount: emptyRooms.length,
        roomIds: emptyRooms,
      });
    }
  }
}

module.exports = new RoomStateManager();
