const logger = require('../utils/logger');
const redisService = require('./redisService');
const roomStateManager = require('./roomStateManager');

/**
 * Seat Synchronization Service
 * Handles real-time synchronization of seat assignments and updates
 */
class SeatSynchronizationService {
  constructor(socketService) {
    this.socketService = socketService;
    this.eventTypes = {
      SEAT_ASSIGNED: 'seat_assigned',
      SEAT_REMOVED: 'seat_removed',
      SEATS_REASSIGNED: 'seats_reassigned',
      SEAT_STATE_SYNC: 'seat_state_sync',
      PLAYER_KICKED_SEAT_UPDATED: 'player_kicked_seat_updated'
    };
  }

  /**
   * Broadcast seat assignment to room subscribers
   * @param {string} roomId - Room ID
   * @param {Object} seatData - Seat assignment data
   */
  async broadcastSeatAssignment(roomId, seatData) {
    try {
      logger.info('Broadcasting seat assignment', {
        roomId,
        userId: seatData.userId,
        position: seatData.position,
        isReconnection: seatData.isReconnection
      });

      const event = {
        type: this.eventTypes.SEAT_ASSIGNED,
        roomId,
        data: {
          userId: seatData.userId,
          username: seatData.username,
          position: seatData.position,
          isReconnection: seatData.isReconnection,
          totalPlayers: seatData.totalPlayers,
          timestamp: new Date().toISOString()
        }
      };

      // Broadcast to all room subscribers
      this.socketService.io.to(`room:${roomId}`).emit('seat_assigned', event);

      // Update room state cache
      await this.updateRoomSeatCache(roomId, seatData.seats);

      logger.debug('Seat assignment broadcasted successfully', { roomId, userId: seatData.userId });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast seat assignment',
        roomId,
        seatData
      });
    }
  }

  /**
   * Broadcast seat removal to room subscribers
   * @param {string} roomId - Room ID
   * @param {Object} removalData - Seat removal data
   */
  async broadcastSeatRemoval(roomId, removalData) {
    try {
      logger.info('Broadcasting seat removal', {
        roomId,
        userId: removalData.userId,
        removedPosition: removalData.removedPosition,
        reassignedPositions: removalData.reassignedPositions
      });

      const event = {
        type: this.eventTypes.SEAT_REMOVED,
        roomId,
        data: {
          userId: removalData.userId,
          username: removalData.username,
          removedPosition: removalData.removedPosition,
          reassignedPositions: removalData.reassignedPositions,
          updatedSeats: removalData.updatedSeats,
          remainingPlayers: removalData.remainingPlayers,
          timestamp: new Date().toISOString()
        }
      };

      // Broadcast to all room subscribers
      this.socketService.io.to(`room:${roomId}`).emit('seat_removed', event);

      // Update room state cache
      await this.updateRoomSeatCache(roomId, removalData.updatedSeats);

      logger.debug('Seat removal broadcasted successfully', { roomId, userId: removalData.userId });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast seat removal',
        roomId,
        removalData
      });
    }
  }

  /**
   * Broadcast seat reassignment to room subscribers
   * @param {string} roomId - Room ID
   * @param {Object} reassignmentData - Seat reassignment data
   */
  async broadcastSeatReassignment(roomId, reassignmentData) {
    try {
      logger.info('Broadcasting seat reassignment', {
        roomId,
        reassignedBy: reassignmentData.reassignedBy,
        seatCount: reassignmentData.newSeatArrangement?.length || 0
      });

      const event = {
        type: this.eventTypes.SEATS_REASSIGNED,
        roomId,
        data: {
          reassignedBy: reassignmentData.reassignedBy,
          newSeatArrangement: reassignmentData.newSeatArrangement,
          previousArrangement: reassignmentData.previousArrangement,
          reason: reassignmentData.reason || 'Administrative reassignment',
          timestamp: new Date().toISOString()
        }
      };

      // Broadcast to all room subscribers
      this.socketService.io.to(`room:${roomId}`).emit('seats_reassigned', event);

      // Update room state cache
      await this.updateRoomSeatCache(roomId, reassignmentData.newSeatArrangement);

      logger.debug('Seat reassignment broadcasted successfully', { roomId });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast seat reassignment',
        roomId,
        reassignmentData
      });
    }
  }

  /**
   * Broadcast player kick with seat updates
   * @param {string} roomId - Room ID
   * @param {Object} kickData - Player kick data with seat updates
   */
  async broadcastPlayerKickWithSeatUpdate(roomId, kickData) {
    try {
      logger.info('Broadcasting player kick with seat update', {
        roomId,
        kickedUserId: kickData.kickedUser?.id,
        kickedBy: kickData.kickedBy,
        seatReassignment: kickData.seatReassignment
      });

      const event = {
        type: this.eventTypes.PLAYER_KICKED_SEAT_UPDATED,
        roomId,
        data: {
          kickedUser: kickData.kickedUser,
          reason: kickData.reason,
          kickedBy: kickData.kickedBy,
          updatedSeats: kickData.updatedSeats,
          seatReassignment: kickData.seatReassignment,
          timestamp: kickData.timestamp || new Date().toISOString()
        }
      };

      // Broadcast to all room subscribers
      this.socketService.io.to(`room:${roomId}`).emit('player_kicked_seat_updated', event);

      // Send specific notification to kicked player
      if (kickData.kickedUser?.id) {
        this.socketService.io.to(kickData.kickedUser.id).emit('you_were_kicked', {
          roomId,
          reason: kickData.reason,
          kickedBy: kickData.kickedBy,
          timestamp: event.data.timestamp
        });
      }

      // Update room state cache
      await this.updateRoomSeatCache(roomId, kickData.updatedSeats);

      logger.debug('Player kick with seat update broadcasted successfully', { roomId });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast player kick with seat update',
        roomId,
        kickData
      });
    }
  }

  /**
   * Broadcast comprehensive seat state synchronization
   * @param {string} roomId - Room ID
   * @param {Object} seatState - Complete seat state
   */
  async broadcastSeatStateSync(roomId, seatState) {
    try {
      logger.info('Broadcasting seat state sync', {
        roomId,
        totalSeats: seatState.totalSeats,
        occupiedSeats: seatState.occupiedSeats,
        availableSeats: seatState.availableSeats
      });

      const event = {
        type: this.eventTypes.SEAT_STATE_SYNC,
        roomId,
        data: {
          seats: seatState.seats,
          statistics: {
            totalSeats: seatState.totalSeats,
            occupiedSeats: seatState.occupiedSeats,
            availableSeats: seatState.availableSeats,
            usedPositions: seatState.usedPositions,
            gaps: seatState.gaps,
            occupancyRate: seatState.occupancyRate
          },
          timestamp: new Date().toISOString()
        }
      };

      // Broadcast to all room subscribers
      this.socketService.io.to(`room:${roomId}`).emit('seat_state_sync', event);

      // Update room state cache
      await this.updateRoomSeatCache(roomId, seatState.seats);

      logger.debug('Seat state sync broadcasted successfully', { roomId });
    } catch (error) {
      logger.logError(error, {
        context: 'Broadcast seat state sync',
        roomId,
        seatState
      });
    }
  }

  /**
   * Handle seat-related Redis events
   * @param {string} channel - Redis channel
   * @param {Object} eventData - Event data
   */
  async handleSeatEvent(channel, eventData) {
    try {
      const { type, roomId, data } = eventData;

      logger.debug('Processing seat event from Redis', {
        channel,
        type,
        roomId
      });

      switch (type) {
        case this.eventTypes.SEAT_ASSIGNED:
          await this.broadcastSeatAssignment(roomId, data);
          break;
        case this.eventTypes.SEAT_REMOVED:
          await this.broadcastSeatRemoval(roomId, data);
          break;
        case this.eventTypes.SEATS_REASSIGNED:
          await this.broadcastSeatReassignment(roomId, data);
          break;
        case this.eventTypes.PLAYER_KICKED_SEAT_UPDATED:
          await this.broadcastPlayerKickWithSeatUpdate(roomId, data);
          break;
        case this.eventTypes.SEAT_STATE_SYNC:
          await this.broadcastSeatStateSync(roomId, data);
          break;
        default:
          logger.debug('Unhandled seat event type', { type, roomId });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Handle seat event',
        channel,
        eventData
      });
    }
  }

  /**
   * Update room seat cache
   * @param {string} roomId - Room ID
   * @param {Array} seats - Updated seats array
   */
  async updateRoomSeatCache(roomId, seats) {
    try {
      if (!seats || !Array.isArray(seats)) {
        logger.warn('Invalid seats data for cache update', { roomId, seats });
        return;
      }

      // Update local room state manager
      if (roomStateManager && typeof roomStateManager.updateRoomSeats === 'function') {
        roomStateManager.updateRoomSeats(roomId, seats);
      }

      // Update Redis cache
      const cacheKey = `room:seats:${roomId}`;
      const cacheData = {
        seats,
        lastUpdated: new Date().toISOString(),
        seatCount: seats.length
      };

      await redisService.setex(cacheKey, 300, JSON.stringify(cacheData)); // 5 minute cache

      logger.debug('Room seat cache updated', {
        roomId,
        seatCount: seats.length,
        cacheKey
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Update room seat cache',
        roomId,
        seatCount: seats?.length || 0
      });
    }
  }

  /**
   * Get cached seat data for a room
   * @param {string} roomId - Room ID
   * @returns {Object|null} Cached seat data
   */
  async getCachedSeatData(roomId) {
    try {
      const cacheKey = `room:seats:${roomId}`;
      const cachedData = await redisService.get(cacheKey);

      if (cachedData) {
        const seatData = JSON.parse(cachedData);
        logger.debug('Retrieved cached seat data', {
          roomId,
          seatCount: seatData.seatCount,
          lastUpdated: seatData.lastUpdated
        });
        return seatData;
      }

      return null;
    } catch (error) {
      logger.logError(error, {
        context: 'Get cached seat data',
        roomId
      });
      return null;
    }
  }

  /**
   * Clear seat cache for a room
   * @param {string} roomId - Room ID
   */
  async clearSeatCache(roomId) {
    try {
      const cacheKey = `room:seats:${roomId}`;
      await redisService.del(cacheKey);

      logger.debug('Seat cache cleared', { roomId, cacheKey });
    } catch (error) {
      logger.logError(error, {
        context: 'Clear seat cache',
        roomId
      });
    }
  }
}

module.exports = SeatSynchronizationService;
