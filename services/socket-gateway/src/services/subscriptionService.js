const logger = require('../utils/logger');
const LobbySubscriptionManager = require('./subscription/lobbySubscriptionManager');
const RoomSubscriptionManager = require('./subscription/roomSubscriptionManager');
const SubscriptionStateManager = require('./subscription/subscriptionStateManager');

/**
 * Enhanced Subscription Service
 * Manages client subscriptions to lobby and room channels with proper transitions
 * Refactored to use modular components for better maintainability
 */
class SubscriptionService {
  constructor() {
    // Initialize modular components
    this.lobbyManager = new LobbySubscriptionManager();
    this.roomManager = new RoomSubscriptionManager();
    this.stateManager = new SubscriptionStateManager();
  }

  /**
   * Subscribe user to lobby with proper state management
   */
  async subscribeLobby(socket, userId, username) {
    try {
      // Unsubscribe from any current room subscription
      await this.unsubscribeFromCurrentRoom(socket, userId);

      // Use lobby manager to handle subscription
      const result = await this.lobbyManager.subscribeLobby(socket, userId, username);

      // Update state tracking
      this.stateManager.updateUserSubscription(userId, result);
      this.stateManager.updateSocketSubscription(socket.id, {
        userId,
        ...result,
      });

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe lobby',
        socketId: socket.id,
        userId,
      });
      throw error;
    }
  }

  /**
   * Unsubscribe user from lobby
   */
  async unsubscribeLobby(socket, userId) {
    try {
      // Use lobby manager to handle unsubscription
      await this.lobbyManager.unsubscribeLobby(socket, userId);

      // Update state tracking if user was subscribed to lobby
      const currentSubscription = this.stateManager.getUserSubscription(userId);
      if (currentSubscription && currentSubscription.channel === 'lobby') {
        this.stateManager.removeUserSubscription(userId);
        this.stateManager.removeSocketSubscription(socket.id);
      }

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe lobby',
        socketId: socket.id,
        userId,
      });
      throw error;
    }
  }

  /**
   * Subscribe user to room with automatic lobby unsubscription
   */
  async subscribeRoom(socket, userId, username, roomId) {
    try {
      // Unsubscribe from lobby if currently subscribed
      if (this.lobbyManager.isSubscribedToLobby(socket.id)) {
        await this.lobbyManager.unsubscribeLobby(socket, userId);
      }

      // Unsubscribe from any other room
      await this.unsubscribeFromCurrentRoom(socket, userId);

      // Use room manager to handle subscription
      const result = await this.roomManager.subscribeRoom(socket, userId, roomId);

      // Update state tracking
      this.stateManager.transitionToRoom(userId, socket.id, roomId);

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Subscribe room',
        socketId: socket.id,
        userId,
        roomId,
      });
      throw error;
    }
  }

  /**
   * Unsubscribe user from room with automatic lobby subscription
   */
  async unsubscribeRoom(socket, userId, roomId) {
    try {
      // Use room manager to handle unsubscription
      await this.roomManager.unsubscribeRoom(socket, userId, roomId);

      // Update subscription tracking
      const currentSubscription = this.stateManager.getUserSubscription(userId);
      if (currentSubscription && currentSubscription.channel === 'room' && currentSubscription.roomId === roomId) {
        this.stateManager.removeUserSubscription(userId);
        this.stateManager.removeSocketSubscription(socket.id);
      }

      // Automatically subscribe to lobby after leaving room
      const lobbyResult = await this.lobbyManager.subscribeLobby(socket, userId, null);
      this.stateManager.transitionToLobby(userId, socket.id);

      return { success: true };
    } catch (error) {
      logger.logError(error, {
        context: 'Unsubscribe room',
        socketId: socket.id,
        userId,
        roomId,
      });
      throw error;
    }
  }

  /**
   * Handle socket disconnect - cleanup all subscriptions
   */
  handleDisconnect(socket) {
    const socketId = socket.id;

    // Use state manager to handle cleanup
    const cleanupResult = this.stateManager.handleSocketDisconnect(socketId);

    if (!cleanupResult.removed) {
      return;
    }

    // Clean up from managers
    this.lobbyManager.removeSocketFromLobby(socketId);
    this.roomManager.removeSocketFromAllRooms(socketId);

    logger.info('Subscription cleanup completed', {
      socketId,
      userId: cleanupResult.userId,
      channel: cleanupResult.channel,
      roomId: cleanupResult.roomId,
    });
  }

  /**
   * Unsubscribe from current room (helper method)
   */
  async unsubscribeFromCurrentRoom(socket, userId) {
    const currentSubscription = this.stateManager.getUserSubscription(userId);

    if (currentSubscription && currentSubscription.channel === 'room') {
      const roomId = currentSubscription.roomId;
      await this.roomManager.unsubscribeRoom(socket, userId, roomId);

      logger.info('Unsubscribed from current room', {
        socketId: socket.id,
        userId,
        roomId,
      });
    }
  }

  // Delegate to state manager
  getUserSubscription(userId) {
    return this.stateManager.getUserSubscription(userId);
  }

  getSocketSubscription(socketId) {
    return this.stateManager.getSocketSubscription(socketId);
  }

  // Delegate to managers
  getRoomSubscriberCount(roomId) {
    return this.roomManager.getRoomSubscriberCount(roomId);
  }

  getLobbySubscriberCount() {
    return this.lobbyManager.getLobbySubscriberCount();
  }

  getRoomSubscribers(roomId) {
    return this.roomManager.getRoomSubscribers(roomId);
  }

  getLobbySubscribers() {
    return this.lobbyManager.getLobbySubscribers();
  }
}

module.exports = new SubscriptionService();
