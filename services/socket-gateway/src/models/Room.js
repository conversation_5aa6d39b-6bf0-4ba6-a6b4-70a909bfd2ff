const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  gameType: {
    type: String,
    required: true,
    enum: ['prizewheel', 'amidakuji'],
  },
  status: {
    type: String,
    enum: ['waiting', 'starting', 'playing', 'finished', 'archived'],
    default: 'waiting',
  },
  configuration: {
    maxPlayers: {
      type: Number,
      required: true,
      min: 2,
      max: 8,
      default: 8,
    },
    minPlayers: {
      type: Number,
      required: true,
      min: 2,
      default: 2,
    },
    betAmount: {
      type: Number,
      required: true,
      min: 1,
    },
    currency: {
      type: String,
      required: true,
      enum: ['USD', 'EUR', 'GBP'],
      default: 'USD',
    },
    isPrivate: {
      type: Boolean,
      default: false,
    },
    password: String,
    autoStart: {
      type: Boolean,
      default: true,
    },
    autoStartDelay: {
      type: Number,
      default: 10000, // 10 seconds
    },
    gameTimeout: {
      type: Number,
      default: 300000, // 5 minutes
    },
  },
  players: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
    avatar: String,
    position: {
      type: Number,
      required: true,
    },
    isReady: {
      type: Boolean,
      default: false,
    },
    betAmount: {
      type: Number,
      required: true,
    },
    joinedAt: {
      type: Date,
      default: Date.now,
    },
    isHost: {
      type: Boolean,
      default: false,
    },
  }],
  currentGame: {
    gameId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Game',
    },
    startedAt: Date,
    estimatedEndAt: Date,
    phase: {
      type: String,
      enum: ['countdown', 'playing', 'results'],
    },
  },
  prizePool: {
    type: Number,
    default: 0,
  },
  statistics: {
    totalGamesPlayed: {
      type: Number,
      default: 0,
    },
    totalPlayersJoined: {
      type: Number,
      default: 0,
    },
    averageGameDuration: {
      type: Number,
      default: 0,
    },
    lastGameAt: Date,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  archivedAt: Date,
  archivedReason: String,
}, {
  timestamps: true,
  toJSON: {
    transform(doc, ret) {
      delete ret.__v;
      return ret;
    },
  },
});

// Indexes for performance
roomSchema.index({ status: 1 });
roomSchema.index({ gameType: 1 });
roomSchema.index({ 'configuration.isPrivate': 1 });
roomSchema.index({ createdAt: -1 });
roomSchema.index({ 'players.userId': 1 });
roomSchema.index({ 'currentGame.gameId': 1 });

// Virtual for current player count
roomSchema.virtual('playerCount').get(function () {
  return this.players.length;
});

// Virtual for ready player count
roomSchema.virtual('readyPlayerCount').get(function () {
  return this.players.filter((player) => player.isReady).length;
});

// Virtual for available slots
roomSchema.virtual('availableSlots').get(function () {
  return this.configuration.maxPlayers - this.players.length;
});

// Virtual for can start game
roomSchema.virtual('canStartGame').get(function () {
  return this.players.length >= this.configuration.minPlayers
         && this.readyPlayerCount === this.players.length
         && this.status === 'waiting';
});

// Instance methods
roomSchema.methods.addPlayer = function (user, betAmount) {
  // Check if room is full
  if (this.players.length >= this.configuration.maxPlayers) {
    throw new Error('Room is full');
  }

  // Check if user is already in room
  if (this.players.some((player) => player.userId.toString() === user._id.toString())) {
    throw new Error('User already in room');
  }

  // Check if room is accepting players
  if (this.status !== 'waiting') {
    throw new Error('Room is not accepting new players');
  }

  // Find next available position starting from 0
  const usedPositions = this.players.map((player) => player.position);
  let position = 0;
  while (usedPositions.includes(position)) {
    position++;
  }

  // Add player
  const player = {
    userId: user._id,
    username: user.username,
    avatar: user.profile && user.profile.avatar,
    position,
    betAmount: betAmount || this.configuration.betAmount,
    isHost: this.players.length === 0, // First player is host
  };

  this.players.push(player);
  this.prizePool += player.betAmount;
  this.statistics.totalPlayersJoined++;

  return this.save();
};

roomSchema.methods.removePlayer = function (userId) {
  const playerIndex = this.players.findIndex(
    (player) => player.userId.toString() === userId.toString(),
  );

  if (playerIndex === -1) {
    throw new Error('Player not found in room');
  }

  const player = this.players[playerIndex];
  this.prizePool -= player.betAmount;
  this.players.splice(playerIndex, 1);

  // If host left, assign new host
  if (player.isHost && this.players.length > 0) {
    this.players[0].isHost = true;
  }

  return this.save();
};

roomSchema.methods.setPlayerReady = function (userId, isReady = true) {
  const player = this.players.find(
    (playerItem) => playerItem.userId.toString() === userId.toString(),
  );

  if (!player) {
    throw new Error('Player not found in room');
  }

  player.isReady = isReady;
  return this.save();
};

roomSchema.methods.startGame = function (gameId) {
  if (!this.canStartGame) {
    throw new Error('Cannot start game - not all conditions met');
  }

  this.status = 'starting';
  this.currentGame = {
    gameId,
    startedAt: new Date(),
    estimatedEndAt: new Date(Date.now() + this.configuration.gameTimeout),
    phase: 'countdown',
  };

  return this.save();
};

roomSchema.methods.updateGamePhase = function (phase) {
  if (!this.currentGame) {
    throw new Error('No active game');
  }

  this.currentGame.phase = phase;

  if (phase === 'playing') {
    this.status = 'playing';
  }

  return this.save();
};

roomSchema.methods.finishGame = function () {
  this.status = 'finished';
  this.statistics.totalGamesPlayed++;
  this.statistics.lastGameAt = new Date();

  if (this.currentGame) {
    const gameDuration = Date.now() - this.currentGame.startedAt.getTime();
    this.statistics.averageGameDuration = (
      this.statistics.averageGameDuration * (this.statistics.totalGamesPlayed - 1) + gameDuration
    ) / this.statistics.totalGamesPlayed;
  }

  // Reset for next game
  this.players.forEach((player) => {
    player.isReady = false;
  });

  this.currentGame = undefined;
  this.status = 'waiting';

  return this.save();
};

roomSchema.methods.archive = function (reason = 'Manual archive') {
  this.status = 'archived';
  this.archivedAt = new Date();
  this.archivedReason = reason;
  return this.save();
};

roomSchema.methods.canJoin = function (user) {
  // Check if room is accepting players
  if (this.status !== 'waiting') {
    return { canJoin: false, reason: 'Room is not accepting new players' };
  }

  // Check if room is full
  if (this.players.length >= this.configuration.maxPlayers) {
    return { canJoin: false, reason: 'Room is full' };
  }

  // Check if user is already in room
  if (this.players.some((player) => player.userId.toString() === user._id.toString())) {
    return { canJoin: false, reason: 'User already in room' };
  }

  // Check if user has sufficient balance
  if (user.balance < this.configuration.betAmount) {
    return { canJoin: false, reason: 'Insufficient balance' };
  }

  return { canJoin: true };
};

// Static methods
roomSchema.statics.findAvailableRooms = function (gameType = null) {
  const query = {
    status: 'waiting',
    'configuration.isPrivate': false,
  };

  if (gameType) {
    query.gameType = gameType;
  }

  return this.find(query)
    .populate('players.userId', 'username profile.avatar')
    .sort({ createdAt: -1 });
};

roomSchema.statics.findByGameId = function (gameId) {
  return this.findOne({ 'currentGame.gameId': gameId });
};

roomSchema.statics.findUserRooms = function (userId) {
  return this.find({ 'players.userId': userId })
    .populate('players.userId', 'username profile.avatar')
    .sort({ updatedAt: -1 });
};

// Pre-save middleware
roomSchema.pre('save', function (next) {
  // Recalculate prize pool
  this.prizePool = this.players.reduce((total, player) => total + player.betAmount, 0);
  next();
});

module.exports = mongoose.model('Room', roomSchema);
