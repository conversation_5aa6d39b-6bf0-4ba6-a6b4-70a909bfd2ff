const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  passwordHash: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    enum: ['player', 'admin', 'moderator'],
    default: 'player',
  },
  balance: {
    type: Number,
    default: 0,
    min: 0,
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP'],
  },
  profile: {
    firstName: String,
    lastName: String,
    avatar: String,
    dateOfBirth: Date,
    country: String,
    timezone: String,
  },
  preferences: {
    language: {
      type: String,
      default: 'en',
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      push: {
        type: Boolean,
        default: true,
      },
      gameUpdates: {
        type: Boolean,
        default: true,
      },
    },
    privacy: {
      showOnline: {
        type: Boolean,
        default: true,
      },
      showStats: {
        type: Boolean,
        default: true,
      },
    },
  },
  statistics: {
    totalGamesPlayed: {
      type: Number,
      default: 0,
    },
    totalWins: {
      type: Number,
      default: 0,
    },
    totalLosses: {
      type: Number,
      default: 0,
    },
    totalWinnings: {
      type: Number,
      default: 0,
    },
    winRate: {
      type: Number,
      default: 0,
    },
    favoriteGameType: String,
    longestWinStreak: {
      type: Number,
      default: 0,
    },
    currentWinStreak: {
      type: Number,
      default: 0,
    },
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'banned'],
    default: 'active',
  },
  lastLoginAt: Date,
  lastActiveAt: Date,
  emailVerifiedAt: Date,
  suspendedAt: Date,
  suspendedUntil: Date,
  suspensionReason: String,
}, {
  timestamps: true,
  toJSON: {
    transform(doc, ret) {
      // Remove sensitive fields from JSON output
      delete ret.passwordHash;
      delete ret.__v;
      return ret;
    },
  },
});

// Indexes for performance
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ status: 1 });
userSchema.index({ lastActiveAt: 1 });
userSchema.index({ 'statistics.totalGamesPlayed': -1 });
userSchema.index({ 'statistics.totalWinnings': -1 });

// Virtual for full name
userSchema.virtual('profile.fullName').get(function () {
  if (this.profile.firstName && this.profile.lastName) {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }
  return this.username;
});

// Virtual for win rate calculation
userSchema.virtual('statistics.calculatedWinRate').get(function () {
  if (this.statistics.totalGamesPlayed === 0) return 0;
  return Math.round((this.statistics.totalWins / this.statistics.totalGamesPlayed) * 100);
});

// Instance methods
userSchema.methods.updateLastActive = function () {
  this.lastActiveAt = new Date();
  return this.save();
};

userSchema.methods.updateStatistics = function (gameResult) {
  this.statistics.totalGamesPlayed += 1;

  if (gameResult.isWinner) {
    this.statistics.totalWins += 1;
    this.statistics.totalWinnings += gameResult.winAmount || 0;
    this.statistics.currentWinStreak += 1;

    if (this.statistics.currentWinStreak > this.statistics.longestWinStreak) {
      this.statistics.longestWinStreak = this.statistics.currentWinStreak;
    }
  } else {
    this.statistics.totalLosses += 1;
    this.statistics.currentWinStreak = 0;
  }

  // Update win rate
  this.statistics.winRate = this.statistics.calculatedWinRate;

  // Update favorite game type
  if (gameResult.gameType) {
    this.statistics.favoriteGameType = gameResult.gameType;
  }

  return this.save();
};

userSchema.methods.canPlay = function () {
  return this.status === 'active'
         && (!this.suspendedUntil || this.suspendedUntil < new Date());
};

userSchema.methods.hasRole = function (role) {
  if (Array.isArray(role)) {
    return role.includes(this.role);
  }
  return this.role === role;
};

// Static methods
userSchema.statics.findByUsername = function (username) {
  return this.findOne({ username: new RegExp(`^${username}$`, 'i') });
};

userSchema.statics.findByEmail = function (email) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findActiveUsers = function () {
  return this.find({
    status: 'active',
    $or: [
      { suspendedUntil: { $exists: false } },
      { suspendedUntil: { $lt: new Date() } },
    ],
  });
};

userSchema.statics.getLeaderboard = function (limit = 10, sortBy = 'totalWinnings') {
  const sortField = `statistics.${sortBy}`;
  return this.find({ status: 'active' })
    .sort({ [sortField]: -1 })
    .limit(limit)
    .select('username profile.avatar statistics');
};

// Pre-save middleware
userSchema.pre('save', function (next) {
  // Update win rate if statistics changed
  if (this.isModified('statistics.totalGamesPlayed') || this.isModified('statistics.totalWins')) {
    this.statistics.winRate = this.statistics.calculatedWinRate;
  }

  next();
});

module.exports = mongoose.model('User', userSchema);
