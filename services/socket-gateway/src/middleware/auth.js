const logger = require('../utils/logger');
const TokenValidator = require('./auth/tokenValidator');
const GameServiceVerifier = require('./auth/gameServiceVerifier');
const SessionManager = require('./auth/sessionManager');

class AuthMiddleware {
  // Socket.io authentication middleware with game service verification
  static socketAuth(socket, next) {
    AuthMiddleware.socketAuthWithGameService(socket, next);
  }

  // Enhanced socket authentication that verifies with game service
  static async socketAuthWithGameService(socket, next) {
    try {
      // Extract and validate token
      const token = TokenValidator.extractTokenFromSocket(socket);

      if (!token) {
        logger.error('Socket authentication failed - no token provided', {
          socketId: socket.id,
          clientAddress: socket.handshake.address,
          troubleshooting: {
            message: 'No authentication token provided',
            possibleCauses: [
              '1. Client not logged in',
              '2. Token not included in socket connection',
              '3. Token format incorrect (should be in auth.token or Authorization header)',
              '4. Client-side authentication issue',
            ],
          },
        });
        return next(TokenValidator.createAuthError('Authentication token required', 'AUTH_TOKEN_MISSING'));
      }

      // Validate JWT token locally
      let decoded;
      try {
        decoded = TokenValidator.validateJWT(token);

        logger.info('JWT token verified successfully', {
          socketId: socket.id,
          userId: decoded.sub || decoded.userId,
          username: decoded.username,
          role: decoded.role,
          sessionId: decoded.sessionId || decoded.session_id,
        });
      } catch (jwtError) {
        logger.error('JWT verification failed', {
          socketId: socket.id,
          clientAddress: socket.handshake.address,
          tokenLength: token.length,
          jwtErrorName: jwtError.name,
          jwtErrorMessage: jwtError.message,
        });
        return next(jwtError);
      }

      // Verify token with Game Service via Redis pub/sub (if enabled)
      const skipAuthServiceVerification = process.env.DISABLE_AUTH_SERVICE_VERIFICATION === 'true';
      let verificationResult = null;

      if (!skipAuthServiceVerification) {
        try {
          GameServiceVerifier.logVerificationAttempt(decoded.sub || decoded.userId, null, socket.id);

          verificationResult = await GameServiceVerifier.verifyTokenWithGameService(token, decoded);

          GameServiceVerifier.logVerificationResult(verificationResult, decoded.sub || decoded.userId, null, socket.id);

          if (!verificationResult.valid) {
            logger.error('Game Service token verification failed', {
              socketId: socket.id,
              userId: decoded.sub || decoded.userId,
              reason: verificationResult.reason,
            });

            const error = TokenValidator.createAuthError(
              'Token verification failed',
              'TOKEN_VERIFICATION_FAILED',
              { message: verificationResult.reason }
            );
            return next(error);
          }
        } catch (verificationError) {
          logger.error('Game Service verification error', {
            socketId: socket.id,
            userId: decoded.sub || decoded.userId,
            error: verificationError.message,
          });

          const error = TokenValidator.createAuthError(
            'Token verification service error',
            'TOKEN_VERIFICATION_ERROR',
            { message: verificationError.message }
          );
          return next(error);
        }
      } else {
        logger.debug('Auth service verification disabled - skipping secondary verification', {
          socketId: socket.id,
          userId: decoded.sub || decoded.userId,
        });

        // Create a mock verification result for consistency
        verificationResult = {
          valid: true,
          userId: decoded.sub || decoded.userId,
          username: decoded.username,
          role: decoded.role || 'user',
        };
      }

      // Normalize and attach user information to socket
      const userData = TokenValidator.normalizeUserData(decoded, verificationResult);
      socket.userId = userData.userId;
      socket.username = userData.username;
      socket.role = userData.role;
      socket.sessionId = userData.sessionId;
      socket.tokenExp = userData.tokenExp;
      socket.token = token;

      // Store user session in Redis
      await SessionManager.storeUserSession(socket.userId, socket.sessionId, {
        username: socket.username,
        role: socket.role,
        tokenExp: socket.tokenExp,
        verifiedByGameService: !skipAuthServiceVerification,
      });

      // Log successful authentication
      logger.info('Socket authentication successful', {
        socketId: socket.id,
        userId: socket.userId,
        username: socket.username,
        role: socket.role,
        sessionId: socket.sessionId,
        verifiedByGameService: !skipAuthServiceVerification,
        authServiceVerificationSkipped: skipAuthServiceVerification,
      });

      logger.logSocketEvent('auth_success', socket.id, socket.userId, {
        username: socket.username,
        role: socket.role,
        verifiedByGameService: !skipAuthServiceVerification,
      });

      next();
    } catch (error) {
      logger.logError(error, {
        context: 'Socket authentication',
        socketId: socket.id,
        ip: socket.handshake.address,
      });

      const authError = new Error('Authentication failed');
      authError.data = {
        code: 'AUTH_FAILED',
        message: error.message,
      };
      next(authError);
    }
  }

  // HTTP authentication middleware
  static httpAuth(req, res, next) {
    try {
      const token = TokenValidator.extractTokenFromRequest(req);

      if (!token) {
        return res.status(401).json({
          error: 'Authentication token required',
          code: 'AUTH_TOKEN_MISSING',
        });
      }

      const decoded = TokenValidator.validateJWT(token);
      const userData = TokenValidator.normalizeUserData(decoded);

      // Attach user information to request
      req.user = {
        id: userData.userId,
        username: userData.username,
        role: userData.role,
        sessionId: userData.sessionId,
        tokenExp: userData.tokenExp,
      };

      next();
    } catch (error) {
      logger.logError(error, {
        context: 'HTTP authentication',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      const statusCode = 401;
      let errorCode = error.code || 'AUTH_FAILED';
      let message = error.message || 'Authentication failed';

      return res.status(statusCode).json({
        error: message,
        code: errorCode,
      });
    }
  }

  // Role-based authorization middleware
  static requireRole(roles) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
        });
      }

      const userRole = req.user.role;
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (!allowedRoles.includes(userRole)) {
        logger.warn('Unauthorized access attempt', {
          userId: req.user.id,
          userRole,
          requiredRoles: allowedRoles,
          endpoint: req.path,
        });

        return res.status(403).json({
          error: 'Insufficient permissions',
          code: 'AUTH_INSUFFICIENT_PERMISSIONS',
        });
      }

      next();
    };
  }

  // Socket role-based authorization
  static socketRequireRole(roles) {
    return (socket, next) => {
      const userRole = socket.role;
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (!allowedRoles.includes(userRole)) {
        logger.warn('Unauthorized socket access attempt', {
          socketId: socket.id,
          userId: socket.userId,
          userRole,
          requiredRoles: allowedRoles,
        });

        const error = new Error('Insufficient permissions');
        error.data = { code: 'AUTH_INSUFFICIENT_PERMISSIONS' };
        return next(error);
      }

      next();
    };
  }

  // Delegate to TokenValidator
  static validateTokenOptional(token) {
    return TokenValidator.validateTokenOptional(token);
  }

  static isTokenExpiringSoon(tokenExp, thresholdMinutes = 5) {
    return TokenValidator.isTokenExpiringSoon(tokenExp, thresholdMinutes);
  }

  static generateToken(payload, options = {}) {
    return TokenValidator.generateToken(payload, options);
  }

  static verifyToken(token) {
    return TokenValidator.verifyToken(token);
  }

  // Delegate to GameServiceVerifier
  static async verifyTokenWithGameService(token, decodedToken) {
    return GameServiceVerifier.verifyTokenWithGameService(token, decodedToken);
  }

  // Delegate to SessionManager
  static async storeUserSession(userId, sessionId, sessionData) {
    return SessionManager.storeUserSession(userId, sessionId, sessionData);
  }

  static async removeUserSession(userId) {
    return SessionManager.invalidateUserSession(userId);
  }
}

module.exports = AuthMiddleware;
