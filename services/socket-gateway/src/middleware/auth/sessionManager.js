/**
 * Session Manager
 * 
 * Handles user session storage and management in Redis.
 * Separated from main auth middleware for better modularity.
 */

const logger = require('../../utils/logger');
const redisService = require('../../services/redisService');

class SessionManager {
  /**
   * Store user session in Redis
   * @param {string} userId - User ID
   * @param {string} sessionId - Session ID
   * @param {Object} sessionData - Session data
   * @param {number} ttl - Time to live in seconds (default: 24 hours)
   */
  static async storeUserSession(userId, sessionId, sessionData, ttl = 86400) {
    try {
      const sessionKey = `user_session:${userId}`;
      const sessionInfo = {
        userId,
        sessionId,
        status: 'active',
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        ...sessionData,
      };

      // Store session with TTL
      await redisService.set(sessionKey, JSON.stringify(sessionInfo), ttl);

      logger.debug('User session stored in Redis', {
        userId,
        sessionId,
        sessionKey,
        ttl,
        verifiedByGameService: sessionData.verifiedByGameService,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Store user session',
        userId,
        sessionId,
      });
      throw error;
    }
  }

  /**
   * Retrieve user session from Redis
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Session data or null if not found
   */
  static async getUserSession(userId) {
    try {
      const sessionKey = `user_session:${userId}`;
      const sessionData = await redisService.get(sessionKey);

      if (!sessionData) {
        return null;
      }

      const session = JSON.parse(sessionData);
      
      logger.debug('User session retrieved from Redis', {
        userId,
        sessionId: session.sessionId,
        status: session.status,
        lastActivity: session.lastActivity,
      });

      return session;
    } catch (error) {
      logger.logError(error, {
        context: 'Get user session',
        userId,
      });
      return null;
    }
  }

  /**
   * Update session last activity
   * @param {string} userId - User ID
   */
  static async updateSessionActivity(userId) {
    try {
      const session = await SessionManager.getUserSession(userId);
      if (!session) {
        return;
      }

      session.lastActivity = new Date().toISOString();
      
      const sessionKey = `user_session:${userId}`;
      const ttl = await redisService.ttl(sessionKey);
      
      // Preserve existing TTL
      if (ttl > 0) {
        await redisService.set(sessionKey, JSON.stringify(session), ttl);
      } else {
        await redisService.set(sessionKey, JSON.stringify(session));
      }

      logger.debug('Session activity updated', {
        userId,
        sessionId: session.sessionId,
        lastActivity: session.lastActivity,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Update session activity',
        userId,
      });
    }
  }

  /**
   * Invalidate user session
   * @param {string} userId - User ID
   */
  static async invalidateUserSession(userId) {
    try {
      const sessionKey = `user_session:${userId}`;
      await redisService.del(sessionKey);

      logger.info('User session invalidated', {
        userId,
        sessionKey,
      });
    } catch (error) {
      logger.logError(error, {
        context: 'Invalidate user session',
        userId,
      });
    }
  }

  /**
   * Check if session is valid
   * @param {string} userId - User ID
   * @param {string} sessionId - Session ID to validate
   * @returns {Promise<boolean>} True if session is valid
   */
  static async isSessionValid(userId, sessionId) {
    try {
      const session = await SessionManager.getUserSession(userId);
      
      if (!session) {
        return false;
      }

      const isValid = session.sessionId === sessionId && session.status === 'active';
      
      logger.debug('Session validation result', {
        userId,
        sessionId,
        isValid,
        storedSessionId: session.sessionId,
        status: session.status,
      });

      return isValid;
    } catch (error) {
      logger.logError(error, {
        context: 'Session validation',
        userId,
        sessionId,
      });
      return false;
    }
  }

  /**
   * Get all active sessions (for admin purposes)
   * @param {number} limit - Maximum number of sessions to return
   * @returns {Promise<Array>} Array of active sessions
   */
  static async getActiveSessions(limit = 100) {
    try {
      const pattern = 'user_session:*';
      const keys = await redisService.keys(pattern);
      
      const sessions = [];
      const keysToProcess = keys.slice(0, limit);
      
      for (const key of keysToProcess) {
        try {
          const sessionData = await redisService.get(key);
          if (sessionData) {
            const session = JSON.parse(sessionData);
            if (session.status === 'active') {
              sessions.push({
                userId: session.userId,
                sessionId: session.sessionId,
                username: session.username,
                role: session.role,
                createdAt: session.createdAt,
                lastActivity: session.lastActivity,
              });
            }
          }
        } catch (parseError) {
          logger.warn('Failed to parse session data', {
            key,
            error: parseError.message,
          });
        }
      }

      logger.debug('Retrieved active sessions', {
        totalKeys: keys.length,
        processedKeys: keysToProcess.length,
        activeSessions: sessions.length,
      });

      return sessions;
    } catch (error) {
      logger.logError(error, {
        context: 'Get active sessions',
        limit,
      });
      return [];
    }
  }

  /**
   * Clean up expired sessions
   * @returns {Promise<number>} Number of sessions cleaned up
   */
  static async cleanupExpiredSessions() {
    try {
      const pattern = 'user_session:*';
      const keys = await redisService.keys(pattern);
      
      let cleanedCount = 0;
      
      for (const key of keys) {
        try {
          const ttl = await redisService.ttl(key);
          
          // If TTL is -1 (no expiry) or -2 (key doesn't exist), skip
          if (ttl === -1 || ttl === -2) {
            continue;
          }
          
          // If TTL is very low (less than 60 seconds), consider it expired
          if (ttl < 60) {
            await redisService.del(key);
            cleanedCount++;
            
            logger.debug('Cleaned up expired session', {
              key,
              ttl,
            });
          }
        } catch (keyError) {
          logger.warn('Error processing session key during cleanup', {
            key,
            error: keyError.message,
          });
        }
      }

      logger.info('Session cleanup completed', {
        totalKeys: keys.length,
        cleanedSessions: cleanedCount,
      });

      return cleanedCount;
    } catch (error) {
      logger.logError(error, {
        context: 'Session cleanup',
      });
      return 0;
    }
  }

  /**
   * Extend session TTL
   * @param {string} userId - User ID
   * @param {number} additionalSeconds - Additional seconds to add to TTL
   */
  static async extendSessionTTL(userId, additionalSeconds = 3600) {
    try {
      const sessionKey = `user_session:${userId}`;
      const currentTTL = await redisService.ttl(sessionKey);
      
      if (currentTTL > 0) {
        const newTTL = currentTTL + additionalSeconds;
        await redisService.expire(sessionKey, newTTL);
        
        logger.debug('Session TTL extended', {
          userId,
          currentTTL,
          additionalSeconds,
          newTTL,
        });
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Extend session TTL',
        userId,
        additionalSeconds,
      });
    }
  }

  /**
   * Get session statistics
   * @returns {Promise<Object>} Session statistics
   */
  static async getSessionStats() {
    try {
      const pattern = 'user_session:*';
      const keys = await redisService.keys(pattern);
      
      let activeCount = 0;
      let totalCount = keys.length;
      
      for (const key of keys) {
        try {
          const sessionData = await redisService.get(key);
          if (sessionData) {
            const session = JSON.parse(sessionData);
            if (session.status === 'active') {
              activeCount++;
            }
          }
        } catch (parseError) {
          // Skip invalid sessions
        }
      }

      const stats = {
        totalSessions: totalCount,
        activeSessions: activeCount,
        inactiveSessions: totalCount - activeCount,
        timestamp: new Date().toISOString(),
      };

      logger.debug('Session statistics calculated', stats);

      return stats;
    } catch (error) {
      logger.logError(error, {
        context: 'Get session statistics',
      });
      
      return {
        totalSessions: 0,
        activeSessions: 0,
        inactiveSessions: 0,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

module.exports = SessionManager;
