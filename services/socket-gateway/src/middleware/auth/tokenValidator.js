/**
 * Token Validator
 * 
 * Handles JWT token validation and verification logic.
 * Separated from main auth middleware for better modularity.
 */

const jwt = require('jsonwebtoken');
const config = require('../../config');
const logger = require('../../utils/logger');

class TokenValidator {
  /**
   * Validate JWT token locally
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   * @throws {Error} If token is invalid
   */
  static validateJWT(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience,
      });

      logger.debug('JWT token validated successfully', {
        userId: decoded.sub || decoded.userId,
        username: decoded.username,
        role: decoded.role,
        exp: decoded.exp,
        iat: decoded.iat,
      });

      return decoded;
    } catch (error) {
      logger.debug('JWT validation failed', {
        errorName: error.name,
        errorMessage: error.message,
        tokenLength: token ? token.length : 0,
      });

      // Throw specific error types for better error handling
      if (error.name === 'TokenExpiredError') {
        const expiredError = new Error('Token expired');
        expiredError.code = 'AUTH_TOKEN_EXPIRED';
        expiredError.name = 'TokenExpiredError';
        throw expiredError;
      } else if (error.name === 'JsonWebTokenError') {
        const invalidError = new Error('Invalid token');
        invalidError.code = 'AUTH_TOKEN_INVALID';
        invalidError.name = 'JsonWebTokenError';
        throw invalidError;
      } else {
        const authError = new Error('Authentication failed');
        authError.code = 'AUTH_FAILED';
        throw authError;
      }
    }
  }

  /**
   * Validate token without throwing errors (for optional auth)
   * @param {string} token - JWT token
   * @returns {Object|null} Decoded token or null if invalid
   */
  static validateTokenOptional(token) {
    try {
      if (!token) return null;

      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience,
      });

      return {
        id: decoded.sub || decoded.userId,
        username: decoded.username,
        role: decoded.role || 'player',
        sessionId: decoded.sessionId,
        tokenExp: decoded.exp,
      };
    } catch (error) {
      logger.debug('Optional token validation failed', {
        error: error.message,
        tokenPresent: !!token,
      });
      return null;
    }
  }

  /**
   * Extract token from socket handshake
   * @param {Object} socket - Socket.io socket instance
   * @returns {string|null} Extracted token or null
   */
  static extractTokenFromSocket(socket) {
    const authHeader = socket.handshake.headers.authorization;
    const authToken = socket.handshake.auth.token;
    
    // Try auth.token first, then Authorization header
    let token = authToken;
    if (!token && authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    logger.debug('Token extraction from socket', {
      socketId: socket.id,
      hasAuthHeader: !!authHeader,
      hasAuthToken: !!authToken,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      authHeaderFormat: authHeader ? 
        (authHeader.startsWith('Bearer ') ? 'Bearer format' : 'Non-Bearer format') : 
        'missing',
    });

    return token;
  }

  /**
   * Extract token from HTTP request
   * @param {Object} req - Express request object
   * @returns {string|null} Extracted token or null
   */
  static extractTokenFromRequest(req) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    return authHeader.substring(7);
  }

  /**
   * Check if token is about to expire
   * @param {number} tokenExp - Token expiration timestamp
   * @param {number} thresholdMinutes - Threshold in minutes (default: 5)
   * @returns {boolean} True if token is expiring soon
   */
  static isTokenExpiringSoon(tokenExp, thresholdMinutes = 5) {
    if (!tokenExp) return false;

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;

    return (tokenExp - now) <= threshold;
  }

  /**
   * Generate a new token
   * @param {Object} payload - Token payload
   * @param {Object} options - JWT options
   * @returns {string} Generated JWT token
   */
  static generateToken(payload, options = {}) {
    const defaultOptions = {
      expiresIn: config.jwt.expiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
    };

    return jwt.sign(payload, config.jwt.secret, { ...defaultOptions, ...options });
  }

  /**
   * Verify and decode token without middleware wrapper
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   */
  static verifyToken(token) {
    return jwt.verify(token, config.jwt.secret, {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
    });
  }

  /**
   * Create standardized auth error
   * @param {string} message - Error message
   * @param {string} code - Error code
   * @param {Object} data - Additional error data
   * @returns {Error} Formatted auth error
   */
  static createAuthError(message, code, data = {}) {
    const error = new Error(message);
    error.data = { code, ...data };
    return error;
  }

  /**
   * Normalize user data from decoded token
   * @param {Object} decoded - Decoded JWT payload
   * @param {Object} verificationResult - Game service verification result
   * @returns {Object} Normalized user data
   */
  static normalizeUserData(decoded, verificationResult = {}) {
    return {
      userId: verificationResult.userId || decoded.sub || decoded.userId,
      username: verificationResult.username || decoded.username,
      // Handle both API Gateway format (roles array) and Socket Gateway format (role string)
      role: verificationResult.role || 
            (Array.isArray(decoded.roles) ? decoded.roles[0] : decoded.role) || 
            'player',
      // Handle both API Gateway format (session_id) and Socket Gateway format (sessionId)
      sessionId: verificationResult.sessionId || decoded.sessionId || decoded.session_id,
      tokenExp: decoded.exp,
      permissions: decoded.permissions,
      deviceId: decoded.device_id,
      jti: decoded.jti,
    };
  }

  /**
   * Validate token format and basic structure
   * @param {string} token - JWT token
   * @returns {boolean} True if token format is valid
   */
  static isValidTokenFormat(token) {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // JWT should have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }

    // Each part should be base64url encoded
    try {
      parts.forEach(part => {
        if (!part) throw new Error('Empty part');
        // Basic base64url validation
        if (!/^[A-Za-z0-9_-]+$/.test(part)) {
          throw new Error('Invalid base64url');
        }
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = TokenValidator;
