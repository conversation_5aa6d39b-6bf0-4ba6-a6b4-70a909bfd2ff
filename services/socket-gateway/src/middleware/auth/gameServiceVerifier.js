/**
 * Game Service Verifier
 * 
 * Handles token verification with the Game Service via Redis pub/sub.
 * Separated from main auth middleware for better modularity.
 */

const logger = require('../../utils/logger');
const redisService = require('../../services/redisService');

class GameServiceVerifier {
  /**
   * Verify token with Game Service via Redis pub/sub
   * @param {string} token - JWT token to verify
   * @param {Object} decodedToken - Decoded token payload
   * @param {number} timeout - Timeout in milliseconds (default: 5000)
   * @returns {Promise<Object>} Verification result from Game Service
   */
  static async verifyTokenWithGameService(token, decodedToken, timeout = 5000) {
    return new Promise(async (resolve, reject) => {
      const correlationId = `token-verify-${decodedToken.sub || decodedToken.userId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const responseChannel = `token:verification:response:${correlationId}`;

      // Set up timeout for pub/sub response
      const timeoutId = setTimeout(() => {
        redisService.unsubscribe(responseChannel);
        reject(new Error('Token verification timeout - Game Service did not respond via pub/sub'));
      }, timeout);

      // Set up response listener
      try {
        await redisService.subscribe(responseChannel, (message, channel) => {
          if (channel === responseChannel) {
            clearTimeout(timeoutId);
            redisService.unsubscribe(responseChannel);

            try {
              const response = typeof message === 'string' ? JSON.parse(message) : message;

              // Validate and normalize the response from Auth Service
              const validatedResponse = GameServiceVerifier.validateVerificationResponse(response);
              resolve(validatedResponse);
            } catch (parseError) {
              reject(new Error('Invalid response format from game service'));
            }
          }
        });

        // Send verification request to game service
        const requestMessage = GameServiceVerifier.buildVerificationRequest(
          token, 
          decodedToken, 
          correlationId, 
          responseChannel
        );

        await redisService.publish('auth:requests', requestMessage);

        logger.debug('Sent token verification request to auth service', {
          correlationId,
          userId: decodedToken.sub || decodedToken.userId,
          responseChannel,
        });
      } catch (error) {
        clearTimeout(timeoutId);
        reject(new Error(`Failed to send verification request: ${error.message}`));
      }
    });
  }

  /**
   * Build verification request message for Game Service
   * @param {string} token - JWT token
   * @param {Object} decodedToken - Decoded token payload
   * @param {string} correlationId - Correlation ID for tracking
   * @param {string} responseChannel - Redis channel for response
   * @returns {Object} Formatted request message
   */
  static buildVerificationRequest(token, decodedToken, correlationId, responseChannel) {
    return {
      event: {
        type: 'verify_token',
        payload: {
          token,
          userId: decodedToken.sub || decodedToken.userId,
          username: decodedToken.username,
          // Handle both API Gateway format (session_id) and Socket Gateway format (sessionId)
          sessionId: decodedToken.sessionId || decodedToken.session_id,
          // Include additional API Gateway token fields for verification
          roles: decodedToken.roles,
          permissions: decodedToken.permissions,
          jti: decodedToken.jti,
          device_id: decodedToken.device_id,
          timestamp: new Date().toISOString(),
        },
      },
      metadata: {
        serviceId: 'socket-gateway',
        version: '1.0.0',
        correlationId,
        responseChannel,
        priority: 1,
      },
    };
  }

  /**
   * Validate verification response from Game Service
   * @param {Object} response - Response from Game Service
   * @returns {Object} Validated response with normalized structure
   */
  static validateVerificationResponse(response) {
    if (!response || typeof response !== 'object') {
      throw new Error('Invalid response format');
    }

    // Handle Auth Service response format: { success: true, data: { valid: true, ... } }
    let responseData = response;
    if (response.success && response.data) {
      responseData = response.data;
    }

    // Ensure required fields are present
    const validatedResponse = {
      valid: Boolean(responseData.valid),
      reason: responseData.reason || response.error || 'Unknown reason',
      userId: responseData.userId || null,
      username: responseData.username || null,
      role: responseData.role || null,
      sessionId: responseData.sessionId || null,
      permissions: responseData.permissions || [],
      metadata: responseData.metadata || {},
    };

    return validatedResponse;
  }

  /**
   * Handle verification timeout
   * @param {string} correlationId - Correlation ID
   * @param {string} responseChannel - Response channel
   * @param {string} userId - User ID
   * @returns {Error} Timeout error
   */
  static handleVerificationTimeout(correlationId, responseChannel, userId) {
    logger.warn('Game Service token verification timeout', {
      correlationId,
      responseChannel,
      userId,
      timeout: 5000,
    });

    redisService.unsubscribe(responseChannel);
    
    const error = new Error('Token verification timeout - Game Service did not respond');
    error.code = 'VERIFICATION_TIMEOUT';
    error.correlationId = correlationId;
    
    return error;
  }

  /**
   * Handle verification error
   * @param {Error} error - Original error
   * @param {string} correlationId - Correlation ID
   * @param {string} userId - User ID
   * @returns {Error} Formatted verification error
   */
  static handleVerificationError(error, correlationId, userId) {
    logger.error('Game Service token verification error', {
      correlationId,
      userId,
      errorMessage: error.message,
      errorStack: error.stack,
    });

    const verificationError = new Error(`Token verification failed: ${error.message}`);
    verificationError.code = 'VERIFICATION_ERROR';
    verificationError.correlationId = correlationId;
    verificationError.originalError = error;
    
    return verificationError;
  }

  /**
   * Check if Game Service is available for verification
   * @returns {Promise<boolean>} True if Game Service is available
   */
  static async isGameServiceAvailable() {
    try {
      // Simple ping to check if Redis pub/sub is working
      const pingChannel = `ping:${Date.now()}`;
      const pongReceived = await new Promise((resolve) => {
        const timeout = setTimeout(() => resolve(false), 1000);
        
        redisService.subscribe(pingChannel, () => {
          clearTimeout(timeout);
          redisService.unsubscribe(pingChannel);
          resolve(true);
        });
        
        redisService.publish(pingChannel, { ping: true });
      });

      return pongReceived;
    } catch (error) {
      logger.debug('Game Service availability check failed', {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Create verification error response
   * @param {string} reason - Error reason
   * @param {string} code - Error code
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Error response
   */
  static createErrorResponse(reason, code, metadata = {}) {
    return {
      valid: false,
      reason,
      code,
      timestamp: new Date().toISOString(),
      metadata,
    };
  }

  /**
   * Log verification attempt
   * @param {string} userId - User ID
   * @param {string} correlationId - Correlation ID
   * @param {string} socketId - Socket ID (optional)
   */
  static logVerificationAttempt(userId, correlationId, socketId = null) {
    logger.info('Starting Game Service token verification', {
      userId,
      correlationId,
      socketId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log verification result
   * @param {Object} result - Verification result
   * @param {string} userId - User ID
   * @param {string} correlationId - Correlation ID
   * @param {string} socketId - Socket ID (optional)
   */
  static logVerificationResult(result, userId, correlationId, socketId = null) {
    logger.info('Game Service token verification completed', {
      userId,
      correlationId,
      socketId,
      valid: result.valid,
      reason: result.reason,
      verifiedUserId: result.userId,
      verifiedUsername: result.username,
      verifiedRole: result.role,
      timestamp: new Date().toISOString(),
    });
  }
}

module.exports = GameServiceVerifier;
