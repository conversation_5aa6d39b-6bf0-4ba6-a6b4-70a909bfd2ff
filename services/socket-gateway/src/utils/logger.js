const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const config = require('../config');

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const {
      timestamp, level, message, stack, ...meta
    } = info;

    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;

    // Add stack trace for errors
    if (stack) {
      logMessage += `\n${stack}`;
    }

    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }

    return logMessage;
  }),
);

// Define console format
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.printf((info) => {
    const {
      timestamp, level, message, stack, ...meta
    } = info;

    let logMessage = `${timestamp} [${level}]: ${message}`;

    // Add stack trace for errors
    if (stack) {
      logMessage += `\n${stack}`;
    }

    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }

    return logMessage;
  }),
);

// Define transports
const transports = [];

// Console transport
if (config.logging.console.enabled) {
  transports.push(
    new winston.transports.Console({
      format: config.logging.console.colorize ? consoleFormat : format,
    }),
  );
}

// File transport with rotation
if (config.logging.file.enabled) {
  transports.push(
    new DailyRotateFile({
      filename: config.logging.file.filename,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: config.logging.file.maxSize,
      maxFiles: config.logging.file.maxFiles,
      format,
    }),
  );

  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: config.logging.file.filename.replace('.log', '-error.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: config.logging.file.maxSize,
      maxFiles: config.logging.file.maxFiles,
      level: 'error',
      format,
    }),
  );
}

// Create the logger
const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  },
};

// Add helper methods for structured logging
logger.logRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user && req.user.id,
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.http('HTTP Request', logData);
  }
};

logger.logSocketEvent = (event, socketId, userId, data = {}) => {
  logger.info('Socket Event', {
    event,
    socketId,
    userId,
    ...data,
  });
};

logger.logGameEvent = (gameId, roomId, event, data = {}) => {
  logger.info('Game Event', {
    gameId,
    roomId,
    event,
    ...data,
  });
};

logger.logError = (error, context = {}) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    ...context,
  });
};

logger.logPerformance = (operation, duration, metadata = {}) => {
  if (config.debug.performanceLogs) {
    logger.debug('Performance Metric', {
      operation,
      duration: `${duration}ms`,
      ...metadata,
    });
  }
};

module.exports = logger;
