/**
 * Circuit Breaker Implementation
 * 
 * Prevents cascading failures by monitoring service health
 * and temporarily blocking requests when services are failing
 */

const logger = require('./logger');

class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      circuitOpenCount: 0,
    };
  }

  /**
   * Execute a function with circuit breaker protection
   * @param {Function} fn - Function to execute
   * @param {Array} args - Arguments for the function
   * @returns {Promise} Function result or circuit breaker error
   */
  async execute(fn, ...args) {
    this.stats.totalRequests++;

    if (this.state === 'OPEN') {
      if (this.shouldAttemptReset()) {
        this.state = 'HALF_OPEN';
        logger.info('Circuit breaker transitioning to HALF_OPEN', {
          service: fn.name,
          failureCount: this.failureCount,
        });
      } else {
        this.stats.circuitOpenCount++;
        throw new Error('Circuit breaker is OPEN - service unavailable');
      }
    }

    try {
      const result = await fn(...args);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  /**
   * Handle successful execution
   */
  onSuccess() {
    this.stats.successfulRequests++;
    this.successCount++;
    
    if (this.state === 'HALF_OPEN') {
      if (this.successCount >= 3) { // Require 3 successes to close
        this.reset();
        logger.info('Circuit breaker CLOSED after successful recovery');
      }
    } else {
      this.failureCount = 0;
    }
  }

  /**
   * Handle failed execution
   * @param {Error} error - The error that occurred
   */
  onFailure(error) {
    this.stats.failedRequests++;
    this.failureCount++;
    this.lastFailureTime = Date.now();
    this.successCount = 0;

    logger.warn('Circuit breaker recorded failure', {
      error: error.message,
      failureCount: this.failureCount,
      threshold: this.failureThreshold,
      state: this.state,
    });

    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.error('Circuit breaker OPENED due to failures', {
        failureCount: this.failureCount,
        threshold: this.failureThreshold,
      });
    }
  }

  /**
   * Check if we should attempt to reset the circuit breaker
   * @returns {boolean} True if reset should be attempted
   */
  shouldAttemptReset() {
    return Date.now() - this.lastFailureTime > this.resetTimeout;
  }

  /**
   * Reset the circuit breaker to CLOSED state
   */
  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = null;
  }

  /**
   * Get current circuit breaker status
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
      stats: { ...this.stats },
      isHealthy: this.state === 'CLOSED',
    };
  }

  /**
   * Force circuit breaker to OPEN state (for testing/maintenance)
   */
  forceOpen() {
    this.state = 'OPEN';
    this.lastFailureTime = Date.now();
    logger.warn('Circuit breaker forced to OPEN state');
  }

  /**
   * Force circuit breaker to CLOSED state (for testing/recovery)
   */
  forceClose() {
    this.reset();
    logger.info('Circuit breaker forced to CLOSED state');
  }
}

module.exports = CircuitBreaker;
