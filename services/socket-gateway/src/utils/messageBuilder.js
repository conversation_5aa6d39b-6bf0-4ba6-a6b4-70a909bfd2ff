/**
 * Message Builder Utility
 * 
 * Provides standardized message building and validation for Redis events
 * according to the channel standards defined in the documentation.
 */

const { SERVICE_IDS } = require('../constants/channels');
const logger = require('./logger');

class MessageBuilder {
  constructor() {
    // Simple validation function instead of AJV
    this.validateMessage = (message) => {
      if (!message || typeof message !== 'object') return false;
      if (!message.event || typeof message.event !== 'object') return false;
      if (!message.metadata || typeof message.metadata !== 'object') return false;
      if (!message.event.type || typeof message.event.type !== 'string') return false;
      if (!message.event.timestamp || typeof message.event.timestamp !== 'string') return false;
      if (!message.metadata.serviceId || typeof message.metadata.serviceId !== 'string') return false;
      if (!message.metadata.version || typeof message.metadata.version !== 'string') return false;
      return true;
    };

    this.sequenceNumber = 1;
  }

  /**
   * Build a standardized event message
   * @param {string} eventType - Event type (e.g., 'player_joined')
   * @param {Object} payload - Event payload data
   * @param {Object} options - Additional options
   * @param {string} options.correlationId - Correlation ID for request tracking
   * @param {number} options.priority - Message priority (1-5, 1=highest)
   * @param {string} options.serviceId - Override service ID
   * @returns {Object} - Standardized message object
   */
  buildEvent(eventType, payload = {}, options = {}) {
    const message = {
      event: {
        type: eventType,
        timestamp: new Date().toISOString(),
        sequenceNumber: this.getNextSequenceNumber(),
        payload: payload
      },
      metadata: {
        serviceId: options.serviceId || SERVICE_IDS.SOCKET_GATEWAY,
        version: '1.0.0',
        timestamp: new Date().toISOString()
      }
    };

    // Add optional metadata
    if (options.correlationId) {
      message.metadata.correlationId = options.correlationId;
    }

    if (options.priority) {
      message.metadata.priority = options.priority;
    }

    // Validate message format
    if (!this.validateMessage(message)) {
      logger.error('Invalid message format', {
        eventType,
        message
      });
      throw new Error(`Invalid message format for event type: ${eventType}`);
    }

    return message;
  }

  /**
   * Build a room event message
   * @param {string} eventType - Room event type
   * @param {string} roomId - Room ID
   * @param {Object} payload - Event payload
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized room event message
   */
  buildRoomEvent(eventType, roomId, payload = {}, options = {}) {
    const roomPayload = {
      roomId,
      ...payload
    };

    return this.buildEvent(eventType, roomPayload, options);
  }

  /**
   * Build a user event message
   * @param {string} eventType - User event type
   * @param {string} userId - User ID
   * @param {Object} payload - Event payload
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized user event message
   */
  buildUserEvent(eventType, userId, payload = {}, options = {}) {
    const userPayload = {
      userId,
      ...payload
    };

    return this.buildEvent(eventType, userPayload, options);
  }

  /**
   * Build a lobby event message
   * @param {string} eventType - Lobby event type
   * @param {Object} payload - Event payload
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized lobby event message
   */
  buildLobbyEvent(eventType, payload = {}, options = {}) {
    return this.buildEvent(eventType, payload, options);
  }

  /**
   * Build a service request message
   * @param {string} requestType - Request type
   * @param {string} targetService - Target service name
   * @param {Object} payload - Request payload
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized service request message
   */
  buildServiceRequest(requestType, targetService, payload = {}, options = {}) {
    const requestPayload = {
      targetService,
      ...payload
    };

    const correlationId = options.correlationId || this.generateCorrelationId();
    
    return this.buildEvent(requestType, requestPayload, {
      ...options,
      correlationId,
      priority: options.priority || 1 // High priority for service requests
    });
  }

  /**
   * Build a monitoring event message
   * @param {string} eventType - Monitoring event type
   * @param {Object} payload - Event payload
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized monitoring event message
   */
  buildMonitoringEvent(eventType, payload = {}, options = {}) {
    const monitoringPayload = {
      serviceId: SERVICE_IDS.SOCKET_GATEWAY,
      timestamp: new Date().toISOString(),
      ...payload
    };

    return this.buildEvent(eventType, monitoringPayload, {
      ...options,
      priority: options.priority || 3 // Medium priority for monitoring
    });
  }

  /**
   * Validate an incoming message
   * @param {Object} message - Message to validate
   * @returns {Object} - Validation result with isValid and errors
   */
  validateIncomingMessage(message) {
    const isValid = this.validateMessage(message);

    // Generate simple error message for invalid messages
    let errors = null;
    if (!isValid) {
      const errorDetails = [];
      if (!message || typeof message !== 'object') errorDetails.push('Message must be an object');
      if (!message?.event || typeof message.event !== 'object') errorDetails.push('Message must have an event object');
      if (!message?.metadata || typeof message.metadata !== 'object') errorDetails.push('Message must have a metadata object');
      if (!message?.event?.type || typeof message.event.type !== 'string') errorDetails.push('Event must have a type string');
      if (!message?.event?.timestamp || typeof message.event.timestamp !== 'string') errorDetails.push('Event must have a timestamp string');
      if (!message?.metadata?.serviceId || typeof message.metadata.serviceId !== 'string') errorDetails.push('Metadata must have a serviceId string');
      if (!message?.metadata?.version || typeof message.metadata.version !== 'string') errorDetails.push('Metadata must have a version string');

      errors = errorDetails.join('; ');
    }

    return {
      isValid,
      errors,
      message: isValid ? null : 'Message does not conform to standard format'
    };
  }

  /**
   * Extract event information from message
   * @param {Object} message - Standardized message
   * @returns {Object} - Extracted event information
   */
  extractEventInfo(message) {
    if (!message || !message.event) {
      return null;
    }

    return {
      type: message.event.type,
      payload: message.event.payload || {},
      timestamp: message.event.timestamp,
      sequenceNumber: message.event.sequenceNumber,
      serviceId: message.metadata?.serviceId,
      correlationId: message.metadata?.correlationId,
      priority: message.metadata?.priority || 3
    };
  }

  /**
   * Generate a unique correlation ID
   * @returns {string} - Unique correlation ID
   */
  generateCorrelationId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${SERVICE_IDS.SOCKET_GATEWAY}-${timestamp}-${random}`;
  }

  /**
   * Get next sequence number (thread-safe increment)
   * @returns {number} - Next sequence number
   */
  getNextSequenceNumber() {
    return this.sequenceNumber++;
  }

  /**
   * Reset sequence number (for testing or service restart)
   */
  resetSequenceNumber() {
    this.sequenceNumber = 1;
  }

  /**
   * Create error response message
   * @param {string} originalEventType - Original event type that failed
   * @param {string} errorCode - Error code
   * @param {string} errorMessage - Error message
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized error message
   */
  buildErrorResponse(originalEventType, errorCode, errorMessage, options = {}) {
    const payload = {
      originalEventType,
      errorCode,
      errorMessage,
      timestamp: new Date().toISOString()
    };

    if (options.originalPayload) {
      payload.originalPayload = options.originalPayload;
    }

    return this.buildEvent('error_response', payload, {
      ...options,
      priority: 1 // High priority for errors
    });
  }

  /**
   * Create success response message
   * @param {string} originalEventType - Original event type that succeeded
   * @param {Object} responseData - Response data
   * @param {Object} options - Additional options
   * @returns {Object} - Standardized success message
   */
  buildSuccessResponse(originalEventType, responseData = {}, options = {}) {
    const payload = {
      originalEventType,
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    };

    return this.buildEvent('success_response', payload, options);
  }

  /**
   * Add debugging information to message
   * @param {Object} message - Message to enhance
   * @param {Object} debugInfo - Debug information to add
   * @returns {Object} - Enhanced message
   */
  addDebugInfo(message, debugInfo) {
    if (!message.metadata) {
      message.metadata = {};
    }

    message.metadata.debug = {
      ...debugInfo,
      addedAt: new Date().toISOString()
    };

    return message;
  }
}

// Create singleton instance
const messageBuilder = new MessageBuilder();

module.exports = messageBuilder;
