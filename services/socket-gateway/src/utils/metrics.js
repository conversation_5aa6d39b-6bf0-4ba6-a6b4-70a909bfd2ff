const promClient = require('prom-client');
const config = require('../config');
const logger = require('./logger');

class MetricsService {
  constructor() {
    this.register = new promClient.Registry();
    this.metrics = {};
    this.initialized = false;
  }

  initialize() {
    if (this.initialized) return;

    try {
      // Add default metrics
      promClient.collectDefaultMetrics({
        register: this.register,
        prefix: 'socket_gateway_',
      });

      // Define custom metrics
      this.defineCustomMetrics();

      this.initialized = true;
      logger.info('Metrics service initialized');
    } catch (error) {
      logger.logError(error, { context: 'Metrics initialization' });
      throw error;
    }
  }

  defineCustomMetrics() {
    // Connection metrics
    this.metrics.activeConnections = new promClient.Gauge({
      name: 'socket_gateway_active_connections',
      help: 'Number of active WebSocket connections',
      registers: [this.register],
    });

    this.metrics.totalConnections = new promClient.Counter({
      name: 'socket_gateway_total_connections',
      help: 'Total number of WebSocket connections established',
      registers: [this.register],
    });

    this.metrics.connectionDuration = new promClient.Histogram({
      name: 'socket_gateway_connection_duration_seconds',
      help: 'Duration of WebSocket connections',
      buckets: [1, 5, 10, 30, 60, 300, 600, 1800, 3600],
      registers: [this.register],
    });

    // Room metrics
    this.metrics.activeRooms = new promClient.Gauge({
      name: 'socket_gateway_active_rooms',
      help: 'Number of active game rooms',
      registers: [this.register],
    });

    this.metrics.playersPerRoom = new promClient.Histogram({
      name: 'socket_gateway_players_per_room',
      help: 'Distribution of players per room',
      buckets: [1, 2, 3, 4, 5, 6, 7, 8],
      registers: [this.register],
    });

    // Message metrics
    this.metrics.messagesReceived = new promClient.Counter({
      name: 'socket_gateway_messages_received_total',
      help: 'Total number of messages received from clients',
      labelNames: ['event_type'],
      registers: [this.register],
    });

    this.metrics.messagesSent = new promClient.Counter({
      name: 'socket_gateway_messages_sent_total',
      help: 'Total number of messages sent to clients',
      labelNames: ['event_type'],
      registers: [this.register],
    });

    this.metrics.messageProcessingTime = new promClient.Histogram({
      name: 'socket_gateway_message_processing_seconds',
      help: 'Time spent processing messages',
      labelNames: ['event_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.register],
    });

    // Redis metrics
    this.metrics.redisMessagesReceived = new promClient.Counter({
      name: 'socket_gateway_redis_messages_received_total',
      help: 'Total number of Redis pub/sub messages received',
      labelNames: ['channel_pattern'],
      registers: [this.register],
    });

    this.metrics.redisConnectionStatus = new promClient.Gauge({
      name: 'socket_gateway_redis_connection_status',
      help: 'Redis connection status (1 = connected, 0 = disconnected)',
      registers: [this.register],
    });

    // Error metrics
    this.metrics.errors = new promClient.Counter({
      name: 'socket_gateway_errors_total',
      help: 'Total number of errors',
      labelNames: ['error_type', 'context'],
      registers: [this.register],
    });

    this.metrics.authenticationFailures = new promClient.Counter({
      name: 'socket_gateway_auth_failures_total',
      help: 'Total number of authentication failures',
      labelNames: ['failure_reason'],
      registers: [this.register],
    });

    // Performance metrics
    this.metrics.memoryUsage = new promClient.Gauge({
      name: 'socket_gateway_memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.register],
    });

    this.metrics.eventLoopLag = new promClient.Gauge({
      name: 'socket_gateway_event_loop_lag_seconds',
      help: 'Event loop lag in seconds',
      registers: [this.register],
    });

    // Rate limiting metrics
    this.metrics.rateLimitHits = new promClient.Counter({
      name: 'socket_gateway_rate_limit_hits_total',
      help: 'Total number of rate limit hits',
      labelNames: ['limit_type'],
      registers: [this.register],
    });

    // Game-specific metrics
    this.metrics.gameEvents = new promClient.Counter({
      name: 'socket_gateway_game_events_total',
      help: 'Total number of game events processed',
      labelNames: ['game_type', 'event_type'],
      registers: [this.register],
    });

    this.metrics.roomJoins = new promClient.Counter({
      name: 'socket_gateway_room_joins_total',
      help: 'Total number of room joins',
      registers: [this.register],
    });

    this.metrics.roomLeaves = new promClient.Counter({
      name: 'socket_gateway_room_leaves_total',
      help: 'Total number of room leaves',
      labelNames: ['reason'],
      registers: [this.register],
    });
  }

  // Connection tracking methods
  incrementConnections() {
    if (this.metrics.totalConnections) this.metrics.totalConnections.inc();
    this.updateActiveConnections();
  }

  decrementConnections() {
    this.updateActiveConnections();
  }

  updateActiveConnections(count) {
    if (count !== undefined) {
      if (this.metrics.activeConnections) this.metrics.activeConnections.set(count);
    }
  }

  recordConnectionDuration(duration) {
    if (this.metrics.connectionDuration) this.metrics.connectionDuration.observe(duration);
  }

  // Room tracking methods
  updateActiveRooms(count) {
    if (this.metrics.activeRooms) this.metrics.activeRooms.set(count);
  }

  recordPlayersPerRoom(count) {
    if (this.metrics.playersPerRoom) this.metrics.playersPerRoom.observe(count);
  }

  incrementRoomJoins() {
    if (this.metrics.roomJoins) this.metrics.roomJoins.inc();
  }

  incrementRoomLeaves(reason = 'unknown') {
    if (this.metrics.roomLeaves) this.metrics.roomLeaves.inc({ reason });
  }

  // Message tracking methods
  incrementMessagesReceived(eventType) {
    if (this.metrics.messagesReceived) this.metrics.messagesReceived.inc({ event_type: eventType });
  }

  incrementMessagesSent(eventType) {
    if (this.metrics.messagesSent) this.metrics.messagesSent.inc({ event_type: eventType });
  }

  recordMessageProcessingTime(eventType, duration) {
    if (this.metrics.messageProcessingTime) {
      this.metrics.messageProcessingTime.observe({ event_type: eventType }, duration);
    }
  }

  // Redis tracking methods
  incrementRedisMessages(channelPattern) {
    if (this.metrics.redisMessagesReceived) this.metrics.redisMessagesReceived.inc({ channel_pattern: channelPattern });
  }

  updateRedisConnectionStatus(isConnected) {
    if (this.metrics.redisConnectionStatus) this.metrics.redisConnectionStatus.set(isConnected ? 1 : 0);
  }

  // Error tracking methods
  incrementErrors(errorType, context = 'unknown') {
    if (this.metrics.errors) this.metrics.errors.inc({ error_type: errorType, context });
  }

  incrementAuthFailures(reason) {
    if (this.metrics.authenticationFailures) this.metrics.authenticationFailures.inc({ failure_reason: reason });
  }

  // Performance tracking methods
  updateMemoryUsage() {
    const memUsage = process.memoryUsage();
    if (this.metrics.memoryUsage) {
      this.metrics.memoryUsage.set({ type: 'rss' }, memUsage.rss);
      this.metrics.memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
      this.metrics.memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
      this.metrics.memoryUsage.set({ type: 'external' }, memUsage.external);
    }
  }

  updateEventLoopLag(lag) {
    if (this.metrics.eventLoopLag) this.metrics.eventLoopLag.set(lag);
  }

  // Rate limiting methods
  incrementRateLimitHits(limitType) {
    if (this.metrics.rateLimitHits) this.metrics.rateLimitHits.inc({ limit_type: limitType });
  }

  // Game-specific methods
  incrementGameEvents(gameType, eventType) {
    if (this.metrics.gameEvents) this.metrics.gameEvents.inc({ game_type: gameType, event_type: eventType });
  }

  // Utility methods
  getMetrics() {
    return this.register.metrics();
  }

  getMetricsAsJSON() {
    return this.register.getMetricsAsJSON();
  }

  // Start periodic metric collection
  startPeriodicCollection() {
    if (!config.monitoring.enabled) return;

    // Update memory usage every 30 seconds
    setInterval(() => {
      this.updateMemoryUsage();
    }, 30000);

    // Measure event loop lag every 10 seconds
    setInterval(() => {
      const start = process.hrtime.bigint();
      setImmediate(() => {
        const lag = Number(process.hrtime.bigint() - start) / 1e9;
        this.updateEventLoopLag(lag);
      });
    }, 10000);

    logger.info('Started periodic metrics collection');
  }

  // Health check for metrics
  healthCheck() {
    return {
      status: this.initialized ? 'healthy' : 'unhealthy',
      metricsCount: this.register.getMetricsAsJSON().length,
      lastUpdate: new Date().toISOString(),
    };
  }
}

module.exports = new MetricsService();
