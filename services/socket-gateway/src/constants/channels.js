/**
 * Standardized Redis Channel Patterns
 * 
 * This file defines all Redis channel patterns used across the XZ Game platform
 * to ensure consistency and eliminate confusion between services.
 * 
 * @see services/docs/redis-channel-standards.md for detailed documentation
 */

// Channel pattern builders
const CHANNELS = {
  // Room Events - Pattern: room:{roomId}:events
  ROOM_EVENTS: (roomId) => `room:${roomId}:events`,
  
  // Lobby Events - Pattern: lobby:events  
  LOBBY_EVENTS: 'lobby:events',
  
  // User Events - Pattern: user:{userId}:events
  USER_EVENTS: (userId) => `user:${userId}:events`,
  
  // Global Events - Pattern: global:events
  GLOBAL_EVENTS: 'global:events',
  
  // Service Communication - Pattern: service:{targetService}:requests
  SERVICE_REQUESTS: (targetService) => `service:${targetService}:requests`,
  
  // Health and Monitoring - Pattern: monitoring:events
  MONITORING_EVENTS: 'monitoring:events',
  
  // Legacy patterns (for migration period only)
  LEGACY: {
    SOCKET_EVENTS: 'socket:events',
    GAME_ROOM: (roomId) => `game:room:${roomId}`,
    GAME_LOBBY_UPDATES: 'game:lobby:updates',
    GAME_GLOBAL: 'game:global:events',
    MANAGER_SYNC: 'manager:sync:events'
  }
};

// Channel pattern matchers for subscription
const CHANNEL_PATTERNS = {
  // Standard patterns
  ROOM_EVENTS: 'room:*:events',
  USER_EVENTS: 'user:*:events',
  SERVICE_REQUESTS: 'service:*:requests',
  
  // Legacy patterns (for migration)
  LEGACY_GAME_ROOM: 'game:room:*',
  LEGACY_USER_NOTIFICATIONS: 'user:*:notifications'
};

// Event types for each channel category
const EVENT_TYPES = {
  ROOM: {
    PLAYER_JOINED: 'player_joined',
    PLAYER_LEFT: 'player_left',
    PLAYER_READY_CHANGED: 'player_ready_changed',
    GAME_STARTED: 'game_started',
    GAME_FINISHED: 'game_finished',
    ROOM_INFO_UPDATED: 'room_info_updated',
    COLOR_SELECTED: 'color_selected',
    COLOR_UNSELECTED: 'color_unselected',
    COLOR_STATE_SYNC: 'color_state_sync',
    POSITION_SELECTED: 'position_selected',
    COUNTDOWN_STARTED: 'countdown_started',
    COUNTDOWN_STOPPED: 'countdown_stopped',
    COUNTDOWN_UPDATED: 'countdown_updated'
  },
  
  LOBBY: {
    ROOM_CREATED: 'room_created',
    ROOM_DELETED: 'room_deleted',
    ROOM_LIST_UPDATED: 'room_list_updated',
    ROOM_STATUS_CHANGED: 'room_status_changed'
  },
  
  USER: {
    BALANCE_UPDATED: 'balance_updated',
    TRANSACTION_COMPLETED: 'transaction_completed',
    NOTIFICATION_RECEIVED: 'notification_received',
    ACHIEVEMENT_UNLOCKED: 'achievement_unlocked'
  },
  
  GLOBAL: {
    SYSTEM_MAINTENANCE: 'system_maintenance',
    SERVICE_STATUS_CHANGE: 'service_status_change',
    GLOBAL_ANNOUNCEMENT: 'global_announcement'
  },
  
  SERVICE: {
    BALANCE_CHECK_REQUEST: 'balance_check_request',
    ROOM_INFO_REQUEST: 'room_info_request',
    USER_VALIDATION_REQUEST: 'user_validation_request'
  },
  
  MONITORING: {
    HEALTH_CHECK: 'health_check',
    PERFORMANCE_METRICS: 'performance_metrics',
    ERROR_REPORT: 'error_report'
  }
};

// Message format validation schema
const MESSAGE_SCHEMA = {
  type: 'object',
  required: ['event', 'metadata'],
  properties: {
    event: {
      type: 'object',
      required: ['type', 'timestamp'],
      properties: {
        type: {
          type: 'string',
          pattern: '^[a-z_]+$'
        },
        timestamp: {
          type: 'string',
          format: 'date-time'
        },
        sequenceNumber: {
          type: 'integer',
          minimum: 1
        },
        payload: {
          type: 'object'
        }
      }
    },
    metadata: {
      type: 'object',
      required: ['serviceId', 'version'],
      properties: {
        serviceId: {
          type: 'string',
          enum: ['socket-gateway', 'game-service', 'manager-service', 'auth-service', 'notification-service']
        },
        version: {
          type: 'string',
          pattern: '^\\d+\\.\\d+\\.\\d+$'
        },
        correlationId: {
          type: 'string'
        },
        priority: {
          type: 'integer',
          minimum: 1,
          maximum: 5
        }
      }
    }
  }
};

// Helper functions
const ChannelUtils = {
  /**
   * Extract room ID from room event channel
   * @param {string} channel - Channel name like "room:123:events"
   * @returns {string|null} - Room ID or null if invalid
   */
  extractRoomId(channel) {
    const match = channel.match(/^room:([^:]+):events$/);
    return match ? match[1] : null;
  },
  
  /**
   * Extract user ID from user event channel
   * @param {string} channel - Channel name like "user:456:events"
   * @returns {string|null} - User ID or null if invalid
   */
  extractUserId(channel) {
    const match = channel.match(/^user:([^:]+):events$/);
    return match ? match[1] : null;
  },
  
  /**
   * Extract service name from service request channel
   * @param {string} channel - Channel name like "service:game-service:requests"
   * @returns {string|null} - Service name or null if invalid
   */
  extractServiceName(channel) {
    const match = channel.match(/^service:([^:]+):requests$/);
    return match ? match[1] : null;
  },
  
  /**
   * Check if channel follows standard pattern
   * @param {string} channel - Channel name to validate
   * @returns {boolean} - True if follows standard pattern
   */
  isStandardChannel(channel) {
    const standardPatterns = [
      /^room:[^:]+:events$/,
      /^user:[^:]+:events$/,
      /^service:[^:]+:requests$/,
      /^lobby:events$/,
      /^global:events$/,
      /^monitoring:events$/
    ];
    
    return standardPatterns.some(pattern => pattern.test(channel));
  },
  
  /**
   * Check if channel is legacy pattern
   * @param {string} channel - Channel name to check
   * @returns {boolean} - True if legacy pattern
   */
  isLegacyChannel(channel) {
    const legacyPatterns = [
      /^socket:events$/,
      /^game:room:.+$/,
      /^game:lobby:updates$/,
      /^game:global:events$/,
      /^manager:sync:events$/,
      /^user:[^:]+:notifications$/
    ];
    
    return legacyPatterns.some(pattern => pattern.test(channel));
  },
  
  /**
   * Convert legacy channel to standard format
   * @param {string} legacyChannel - Legacy channel name
   * @returns {string|null} - Standard channel name or null if no conversion
   */
  convertLegacyChannel(legacyChannel) {
    // game:room:123 -> room:123:events
    const gameRoomMatch = legacyChannel.match(/^game:room:(.+)$/);
    if (gameRoomMatch) {
      return CHANNELS.ROOM_EVENTS(gameRoomMatch[1]);
    }
    
    // game:lobby:updates -> lobby:events
    if (legacyChannel === 'game:lobby:updates') {
      return CHANNELS.LOBBY_EVENTS;
    }
    
    // game:global:events -> global:events
    if (legacyChannel === 'game:global:events') {
      return CHANNELS.GLOBAL_EVENTS;
    }
    
    // user:123:notifications -> user:123:events
    const userNotificationMatch = legacyChannel.match(/^user:([^:]+):notifications$/);
    if (userNotificationMatch) {
      return CHANNELS.USER_EVENTS(userNotificationMatch[1]);
    }
    
    return null;
  }
};

// Service identification
const SERVICE_IDS = {
  SOCKET_GATEWAY: 'socket-gateway',
  GAME_SERVICE: 'game-service',
  MANAGER_SERVICE: 'manager-service',
  AUTH_SERVICE: 'auth-service',
  NOTIFICATION_SERVICE: 'notification-service'
};

module.exports = {
  CHANNELS,
  CHANNEL_PATTERNS,
  EVENT_TYPES,
  MESSAGE_SCHEMA,
  ChannelUtils,
  SERVICE_IDS
};
