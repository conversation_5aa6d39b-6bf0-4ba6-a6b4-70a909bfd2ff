/**
 * Event Standards and Communication Patterns
 * 
 * Defines standardized event formats, channel patterns, and message structures
 * for consistent communication between services.
 */

// ===== STANDARDIZED CHANNEL PATTERNS =====

const CHANNEL_PATTERNS = {
  // Standard channel patterns (recommended)
  ROOM_EVENTS: 'room:*:events',
  LOBBY_EVENTS: 'lobby:events', 
  USER_EVENTS: 'user:*:events',
  GLOBAL_EVENTS: 'global:events',
  SERVICE_REQUESTS: 'service:*:requests',
  MONITORING_EVENTS: 'monitoring:events',
  
  // Legacy patterns (deprecated but supported)
  LEGACY_GAME_ROOM: 'game:room:*',
  LEGACY_GAME_LOBBY: 'game:lobby:updates',
  LEGACY_GAME_GLOBAL: 'game:global',
  LEGACY_USER_NOTIFICATIONS: 'user:*:notifications',
  LEGACY_SOCKET_EVENTS: 'socket:events',
  LEGACY_ADMIN_NOTIFICATIONS: 'admin:notifications',
  LEGACY_ADMIN_SYSTEM: 'admin:system:*',
};

// ===== STANDARDIZED EVENT TYPES =====

const EVENT_TYPES = {
  // Lobby Events
  LOBBY: {
    ROOM_LIST_UPDATED: 'lobby:room_list_updated',
    ROOM_CREATED: 'lobby:room_created',
    ROOM_UPDATED: 'lobby:room_updated', 
    ROOM_DELETED: 'lobby:room_deleted',
    PLAYER_COUNT_CHANGED: 'lobby:player_count_changed',
    SUBSCRIPTION_CONFIRMED: 'lobby:subscription_confirmed',
    SUBSCRIPTION_FAILED: 'lobby:subscription_failed',
    FILTERS_UPDATED: 'lobby:filters_updated',
    STATISTICS_UPDATED: 'lobby:statistics_updated',
  },
  
  // Room Events
  ROOM: {
    CREATED: 'room:created',
    UPDATED: 'room:updated',
    DELETED: 'room:deleted',
    PLAYER_JOINED: 'room:player_joined',
    PLAYER_LEFT: 'room:player_left',
    STATUS_CHANGED: 'room:status_changed',
    GAME_STARTED: 'room:game_started',
    GAME_ENDED: 'room:game_ended',
    INFO_UPDATED: 'room:info_updated',
  },
  
  // User Events
  USER: {
    CONNECTED: 'user:connected',
    DISCONNECTED: 'user:disconnected',
    BALANCE_UPDATED: 'user:balance_updated',
    PROFILE_UPDATED: 'user:profile_updated',
    NOTIFICATION_SENT: 'user:notification_sent',
  },
  
  // Global Events
  GLOBAL: {
    SYSTEM_MAINTENANCE: 'global:system_maintenance',
    SERVICE_STATUS: 'global:service_status',
    BROADCAST_MESSAGE: 'global:broadcast_message',
  },
  
  // Service Requests
  SERVICE: {
    HEALTH_CHECK: 'service:health_check',
    METRICS_REQUEST: 'service:metrics_request',
    CONFIG_UPDATE: 'service:config_update',
  },
};

// ===== STANDARDIZED MESSAGE FORMAT =====

/**
 * Standard message format for all inter-service communication
 */
const MESSAGE_FORMAT = {
  // Required fields
  event: {
    type: 'string', // Event type from EVENT_TYPES
    payload: 'object', // Event-specific data
    timestamp: 'string', // ISO 8601 timestamp
    version: 'string', // Event schema version (default: '1.0.0')
  },
  metadata: {
    serviceId: 'string', // Source service identifier
    version: 'string', // Service version
    correlationId: 'string', // Request correlation ID
    priority: 'number', // Message priority (1-5, 1=highest)
    retryCount: 'number', // Retry attempt count (optional)
    responseChannel: 'string', // Response channel (optional)
  },
};

// ===== MESSAGE VALIDATION SCHEMA =====

const MESSAGE_SCHEMA = {
  type: 'object',
  required: ['event', 'metadata'],
  properties: {
    event: {
      type: 'object',
      required: ['type', 'payload', 'timestamp'],
      properties: {
        type: { type: 'string', minLength: 1 },
        payload: { type: 'object' },
        timestamp: { type: 'string', format: 'date-time' },
        version: { type: 'string', default: '1.0.0' },
      },
    },
    metadata: {
      type: 'object',
      required: ['serviceId', 'version', 'correlationId'],
      properties: {
        serviceId: { type: 'string', minLength: 1 },
        version: { type: 'string', minLength: 1 },
        correlationId: { type: 'string', minLength: 1 },
        priority: { type: 'number', minimum: 1, maximum: 5, default: 3 },
        retryCount: { type: 'number', minimum: 0, default: 0 },
        responseChannel: { type: 'string' },
      },
    },
  },
};

// ===== CHANNEL MAPPING =====

/**
 * Maps legacy channels to standard channels
 */
const CHANNEL_MIGRATION_MAP = {
  'game:lobby:updates': 'lobby:events',
  'game:room:*': 'room:*:events',
  'game:global': 'global:events',
  'user:*:notifications': 'user:*:events',
  'socket:events': 'global:events',
  'admin:notifications': 'monitoring:events',
  'admin:system:*': 'monitoring:events',
};

// ===== EVENT PRIORITY LEVELS =====

const PRIORITY_LEVELS = {
  CRITICAL: 1, // System failures, security issues
  HIGH: 2,     // Game state changes, user actions
  NORMAL: 3,   // Regular updates, notifications
  LOW: 4,      // Statistics, analytics
  BACKGROUND: 5, // Cleanup, maintenance
};

// ===== SERVICE IDENTIFIERS =====

const SERVICE_IDS = {
  SOCKET_GATEWAY: 'socket-gateway',
  GAME_SERVICE: 'game-service',
  AUTH_SERVICE: 'auth-service',
  API_GATEWAY: 'api-gateway',
  ROOM_SERVICE: 'room-service',
  NOTIFICATION_SERVICE: 'notification-service',
  MANAGER_SERVICE: 'manager-service',
  DASHBOARD_SERVICE: 'dashboard-service',
  GAME_ENGINE_SERVICE: 'game-engine-service',
};

// ===== TIMEOUT CONFIGURATIONS =====

const TIMEOUT_CONFIG = {
  LOBBY_SUBSCRIPTION: 15000,    // 15 seconds
  ROOM_OPERATIONS: 10000,       // 10 seconds
  USER_OPERATIONS: 8000,        // 8 seconds
  HEALTH_CHECKS: 5000,          // 5 seconds
  STATISTICS: 12000,            // 12 seconds
  BACKGROUND_TASKS: 30000,      // 30 seconds
};

// ===== RETRY CONFIGURATIONS =====

const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAYS: [1000, 2000, 4000], // Exponential backoff
  RETRY_EVENTS: [
    EVENT_TYPES.LOBBY.ROOM_LIST_UPDATED,
    EVENT_TYPES.ROOM.INFO_UPDATED,
    EVENT_TYPES.USER.BALANCE_UPDATED,
  ],
};

// ===== UTILITY FUNCTIONS =====

/**
 * Create a standardized message
 * @param {string} eventType - Event type from EVENT_TYPES
 * @param {Object} payload - Event payload
 * @param {Object} metadata - Message metadata
 * @returns {Object} Standardized message
 */
function createStandardMessage(eventType, payload, metadata = {}) {
  return {
    event: {
      type: eventType,
      payload,
      timestamp: new Date().toISOString(),
      version: metadata.eventVersion || '1.0.0',
    },
    metadata: {
      serviceId: metadata.serviceId || SERVICE_IDS.SOCKET_GATEWAY,
      version: metadata.serviceVersion || '1.0.0',
      correlationId: metadata.correlationId || generateCorrelationId(eventType),
      priority: metadata.priority || PRIORITY_LEVELS.NORMAL,
      retryCount: metadata.retryCount || 0,
      responseChannel: metadata.responseChannel || null,
    },
  };
}

/**
 * Generate correlation ID
 * @param {string} eventType - Event type
 * @returns {string} Correlation ID
 */
function generateCorrelationId(eventType) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  const shortType = eventType.split(':').pop() || 'event';
  return `${shortType}-${timestamp}-${random}`;
}

/**
 * Get standard channel for legacy channel
 * @param {string} legacyChannel - Legacy channel name
 * @returns {string} Standard channel name
 */
function getStandardChannel(legacyChannel) {
  return CHANNEL_MIGRATION_MAP[legacyChannel] || legacyChannel;
}

/**
 * Check if channel is legacy
 * @param {string} channel - Channel name
 * @returns {boolean} True if legacy channel
 */
function isLegacyChannel(channel) {
  return Object.keys(CHANNEL_MIGRATION_MAP).some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(channel);
    }
    return pattern === channel;
  });
}

/**
 * Validate message format
 * @param {Object} message - Message to validate
 * @returns {Object} Validation result
 */
function validateMessage(message) {
  const errors = [];
  
  if (!message || typeof message !== 'object') {
    errors.push('Message must be an object');
    return { valid: false, errors };
  }
  
  if (!message.event) {
    errors.push('Message must have an event object');
  } else {
    if (!message.event.type) errors.push('Event must have a type string');
    if (!message.event.payload) errors.push('Event must have a payload object');
    if (!message.event.timestamp) errors.push('Event must have a timestamp string');
  }
  
  if (!message.metadata) {
    errors.push('Message must have a metadata object');
  } else {
    if (!message.metadata.serviceId) errors.push('Metadata must have a serviceId string');
    if (!message.metadata.version) errors.push('Metadata must have a version string');
    if (!message.metadata.correlationId) errors.push('Metadata must have a correlationId string');
  }
  
  return {
    valid: errors.length === 0,
    errors: errors.join('; '),
  };
}

module.exports = {
  CHANNEL_PATTERNS,
  EVENT_TYPES,
  MESSAGE_FORMAT,
  MESSAGE_SCHEMA,
  CHANNEL_MIGRATION_MAP,
  PRIORITY_LEVELS,
  SERVICE_IDS,
  TIMEOUT_CONFIG,
  RETRY_CONFIG,
  createStandardMessage,
  generateCorrelationId,
  getStandardChannel,
  isLegacyChannel,
  validateMessage,
};
