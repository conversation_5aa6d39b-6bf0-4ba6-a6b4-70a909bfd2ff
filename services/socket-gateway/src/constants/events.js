/**
 * Event Types and Channel Constants
 * 
 * Defines all event types and channel patterns used in the Socket Gateway
 */

// Event Types organized by category
const EVENT_TYPES = {
  // Room Events
  ROOM: {
    CREATED: 'room_created',
    UPDATED: 'room_updated',
    DELETED: 'room_deleted',
    PLAYER_JOINED: 'room_player_joined',
    PLAYER_LEFT: 'room_player_left',
    PLAYER_READY_CHANGED: 'room_player_ready_changed',
    GAME_STARTED: 'room_game_started',
    GAME_FINISHED: 'room_game_finished',
    STATUS_CHANGED: 'room_status_changed',
    INFO_UPDATED: 'room_info_updated',
    COLOR_SELECTED: 'color_selected',
    COLOR_UNSELECTED: 'color_unselected',
    COLOR_STATE_SYNC: 'color_state_sync',
    COLOR_AVAILABILITY_UPDATED: 'color_availability_updated',
    COUNTDOWN_STARTED: 'countdown_started',
    COUNTDOWN_STOPPED: 'countdown_stopped',
    COUNTDOWN_UPDATED: 'countdown_updated'
  },

  // Lobby Events
  LOBBY: {
    ROOM_LIST_UPDATED: 'lobby_room_list_updated',
    PLAYER_COUNT_UPDATED: 'lobby_player_count_updated',
    ANNOUNCEMENT: 'lobby_announcement'
  },

  // User Events
  USER: {
    BALANCE_UPDATED: 'user_balance_updated',
    PROFILE_UPDATED: 'user_profile_updated',
    NOTIFICATION: 'user_notification',
    STATUS_CHANGED: 'user_status_changed'
  },

  // Global Events
  GLOBAL: {
    SYSTEM_ANNOUNCEMENT: 'global_system_announcement',
    MAINTENANCE_MODE: 'global_maintenance_mode',
    SERVER_STATUS: 'global_server_status'
  },

  // Service Events
  SERVICE: {
    HEALTH_CHECK: 'service_health_check',
    STATUS_UPDATE: 'service_status_update',
    REQUEST: 'service_request',
    RESPONSE: 'service_response'
  },

  // Monitoring Events
  MONITORING: {
    METRICS_UPDATE: 'monitoring_metrics_update',
    ALERT: 'monitoring_alert',
    PERFORMANCE: 'monitoring_performance'
  },

  // Socket Events (Legacy)
  SOCKET: {
    CONNECT: 'socket_connect',
    DISCONNECT: 'socket_disconnect',
    ERROR: 'socket_error',
    RECONNECT: 'socket_reconnect'
  }
};

// Channel Patterns
const CHANNELS = {
  // Room channels
  ROOM_EVENTS: (roomId) => `room:${roomId}:events`,
  ROOM_CHAT: (roomId) => `room:${roomId}:chat`,
  
  // Lobby channels
  LOBBY_EVENTS: 'lobby:events',
  LOBBY_CHAT: 'lobby:chat',
  
  // User channels
  USER_EVENTS: (userId) => `user:${userId}:events`,
  USER_NOTIFICATIONS: (userId) => `user:${userId}:notifications`,
  
  // Global channels
  GLOBAL_EVENTS: 'global:events',
  GLOBAL_ANNOUNCEMENTS: 'global:announcements',
  
  // Service channels
  SERVICE_REQUESTS: (serviceName) => `service:${serviceName}:requests`,
  SERVICE_RESPONSES: (serviceName) => `service:${serviceName}:responses`,
  
  // Monitoring channels
  MONITORING_EVENTS: 'monitoring:events',
  MONITORING_ALERTS: 'monitoring:alerts',
  
  // Legacy socket channel
  SOCKET_EVENTS: 'socket:events'
};

// Event Priority Levels
const EVENT_PRIORITY = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  CRITICAL: 4
};

// Event Categories for routing
const EVENT_CATEGORIES = {
  ROOM: 'ROOM',
  LOBBY: 'LOBBY',
  USER: 'USER',
  GLOBAL: 'GLOBAL',
  SERVICE: 'SERVICE',
  MONITORING: 'MONITORING',
  SOCKET: 'SOCKET'
};

// Channel Pattern Matchers
const CHANNEL_PATTERNS = {
  ROOM: /^room:([^:]+):events$/,
  LOBBY: /^lobby:events$/,
  USER: /^user:([^:]+):events$/,
  GLOBAL: /^global:events$/,
  SERVICE: /^service:([^:]+):requests$/,
  MONITORING: /^monitoring:events$/,
  SOCKET: /^socket:events$/
};

// Event Validation Rules
const EVENT_VALIDATION = {
  REQUIRED_FIELDS: ['type', 'timestamp'],
  OPTIONAL_FIELDS: ['payload', 'metadata', 'priority'],
  MAX_PAYLOAD_SIZE: 1024 * 1024, // 1MB
  ALLOWED_TYPES: Object.values(EVENT_TYPES).flatMap(category => Object.values(category))
};

module.exports = {
  EVENT_TYPES,
  CHANNELS,
  EVENT_PRIORITY,
  EVENT_CATEGORIES,
  CHANNEL_PATTERNS,
  EVENT_VALIDATION
};
