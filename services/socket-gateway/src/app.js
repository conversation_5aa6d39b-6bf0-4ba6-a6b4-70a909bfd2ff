require('express-async-errors');
const express = require('express');
const http = require('http');
const mongoose = require('mongoose');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const morgan = require('morgan');

// Import configuration and utilities
const config = require('./config');
const logger = require('./utils/logger');
const metrics = require('./utils/metrics');

// Import services
const redisService = require('./services/redisService');
const socketService = require('./services/socketService');

// Import routes
const healthRoutes = require('./routes/health');

// Import middleware
const AuthMiddleware = require('./middleware/auth');

const app = express();
const server = http.createServer(app);

// Trust proxy for rate limiting and IP detection
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for Socket.io
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: config.cors.origins,
  credentials: config.cors.credentials,
  optionsSuccessStatus: config.cors.optionsSuccessStatus,
}));

// Compression middleware
app.use(compression());

// Request logging
app.use(morgan('combined', { stream: logger.stream }));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request ID middleware for tracing
app.use((req, res, next) => {
  req.id = require('uuid').v4();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// Health check routes (no auth required)
app.use('/', healthRoutes);

// API routes with authentication
app.use('/api/v1', AuthMiddleware.httpAuth);

// Basic API info endpoint
app.get('/api/v1/info', (req, res) => {
  res.json({
    service: 'socket-gateway',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.env,
    timestamp: new Date().toISOString(),
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role,
    },
  });
});

// Socket metrics endpoint (authenticated)
app.get('/api/v1/metrics/socket', (req, res) => {
  try {
    const socketMetrics = socketService.getMetrics();
    res.json({
      success: true,
      data: socketMetrics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.logError(error, { context: 'Socket metrics API' });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve socket metrics',
      message: error.message,
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Global error handler
app.use((error, req, res, _next) => {
  logger.logError(error, {
    context: 'Express error handler',
    requestId: req.id,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user && req.user.id,
  });

  // Don't leak error details in production
  const isDevelopment = config.env === 'development';

  res.status(error.status || 500).json({
    error: error.message || 'Internal Server Error',
    ...(isDevelopment && { stack: error.stack }),
    timestamp: new Date().toISOString(),
    requestId: req.id,
  });
});

// Database connection
async function connectDatabase() {
  try {
    await mongoose.connect(config.mongodb.url, config.mongodb.options);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.logError(error, { context: 'MongoDB connection' });
    throw error;
  }
}

// Initialize services
async function initializeServices() {
  try {
    // Initialize metrics
    if (config.monitoring.enabled) {
      metrics.initialize();
      metrics.startPeriodicCollection();
    }

    // Initialize Redis service
    await redisService.initialize();

    // Initialize Room Service gRPC client
    try {
      const roomServiceClient = require('./services/grpc/roomServiceClient');
      await roomServiceClient.initialize();
      logger.info('Room Service gRPC client initialized successfully');
    } catch (error) {
      logger.warn('Failed to initialize Room Service gRPC client:', error.message);
      // Don't fail startup if room service is not available
    }

    // Initialize Socket.io service
    socketService.initialize(server);

    logger.info('All services initialized successfully');
  } catch (error) {
    logger.logError(error, { context: 'Service initialization' });
    throw error;
  }
}

// Enhanced graceful shutdown handler
async function gracefulShutdown(signal) {
  // Prevent multiple shutdown attempts
  if (isShuttingDown && signal !== 'uncaughtException' && signal !== 'unhandledRejection') {
    logger.warn(`Shutdown already in progress, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  logger.info(`Received ${signal}, starting enhanced graceful shutdown...`);

  const shutdownTimeout = parseInt(process.env.SHUTDOWN_TIMEOUT, 10) || 15000; // Reduced to 15 seconds
  let shutdownComplete = false;

  try {
    // Start shutdown timer
    const shutdownTimer = setTimeout(() => {
      if (!shutdownComplete) {
        logger.error(`Enhanced graceful shutdown timeout after ${shutdownTimeout}ms, forcing exit`);
        process.exit(1);
      }
    }, shutdownTimeout);

    // Stop accepting new connections
    server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // Shutdown services with individual timeouts
        const shutdownPromises = [
          Promise.race([
            socketService.shutdown(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Socket service shutdown timeout')), 5000))
          ]).catch(error => {
            logger.warn('Socket service shutdown warning:', error.message);
            return Promise.resolve(); // Continue with other shutdowns
          }),

          Promise.race([
            redisService.disconnect(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Redis service shutdown timeout')), 5000))
          ]).catch(error => {
            logger.warn('Redis service shutdown warning:', error.message);
            return Promise.resolve(); // Continue with other shutdowns
          }),

          Promise.race([
            (async () => {
              try {
                const roomServiceClient = require('./services/grpc/roomServiceClient');
                roomServiceClient.close();
              } catch (error) {
                // Ignore if not initialized
              }
            })(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Room service client shutdown timeout')), 2000))
          ]).catch(error => {
            logger.warn('Room service client shutdown warning:', error.message);
            return Promise.resolve(); // Continue with other shutdowns
          }),

          Promise.race([
            mongoose.connection.close(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('MongoDB shutdown timeout')), 5000))
          ]).catch(error => {
            logger.warn('MongoDB shutdown warning:', error.message);
            return Promise.resolve(); // Continue with other shutdowns
          })
        ];

        // Wait for all services to shutdown (with individual timeouts)
        await Promise.allSettled(shutdownPromises);

        shutdownComplete = true;
        clearTimeout(shutdownTimer);

        logger.info('Enhanced graceful shutdown completed successfully');
        process.exit(0);

      } catch (error) {
        shutdownComplete = true;
        clearTimeout(shutdownTimer);
        logger.logError(error, { context: 'Service shutdown during graceful shutdown' });
        process.exit(1);
      }
    });

  } catch (error) {
    logger.logError(error, { context: 'Enhanced graceful shutdown' });
    process.exit(1);
  }
}

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Track shutdown state to prevent infinite loops
let isShuttingDown = false;

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.logError(error, { context: 'Uncaught exception' });

  if (!isShuttingDown) {
    isShuttingDown = true;
    gracefulShutdown('uncaughtException');
  } else {
    logger.error('Already shutting down, forcing immediate exit');
    process.exit(1);
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  // Check if this is a client disconnect error (common and not critical)
  const reasonString = reason ? reason.toString() : 'Unknown reason';

  // List of non-critical errors that shouldn't trigger shutdown
  const nonCriticalErrors = [
    'Disconnects client',
    'Client network socket disconnected',
    'Connection terminated',
    'Socket connection lost',
    'ECONNRESET',
    'EPIPE',
    'ENOTFOUND',
    'Client disconnected',
    'Connection closed',
    'WebSocket connection closed',
    'Transport close',
    'ping timeout',
    'transport close',
    'client namespace disconnect'
  ];

  const isNonCritical = nonCriticalErrors.some(errorPattern =>
    reasonString.toLowerCase().includes(errorPattern.toLowerCase())
  );

  if (isNonCritical) {
    // Log as debug/warning instead of error for client disconnects
    logger.debug('Non-critical client disconnection detected', {
      context: 'Client disconnect',
      reason: reasonString,
      type: 'non_critical'
    });
    return; // Don't trigger shutdown for client disconnects
  }

  // For critical errors, log and potentially shutdown
  logger.logError(new Error(`Unhandled rejection: ${reasonString}`), {
    context: 'Unhandled promise rejection',
    promise: promise.toString(),
    critical: true
  });

  if (!isShuttingDown) {
    isShuttingDown = true;
    gracefulShutdown('unhandledRejection');
  } else {
    logger.error('Already shutting down, forcing immediate exit');
    process.exit(1);
  }
});

// Start server
const PORT = config.port || 3001;

async function startServer() {
  try {
    // Connect to database
    await connectDatabase();

    // Initialize services
    await initializeServices();

    // Start HTTP server
    server.listen(PORT, () => {
      logger.info(`Socket Gateway server running on port ${PORT}`);
      logger.info(`Environment: ${config.env}`);
      logger.info(`Health check: http://localhost:${PORT}/health`);

      if (config.monitoring.enabled) {
        logger.info(`Metrics: http://localhost:${PORT}/metrics`);
      }
    });
  } catch (error) {
    logger.logError(error, { context: 'Server startup' });
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = { app, server };
