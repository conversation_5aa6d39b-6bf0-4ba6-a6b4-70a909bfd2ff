const express = require('express');
const mongoose = require('mongoose');
const config = require('../config');
const logger = require('../utils/logger');
const metrics = require('../utils/metrics');
const redisService = require('../services/redisService');
const socketService = require('../services/socketService');

const router = express.Router();

// Basic health check
router.get('/health', async (req, res) => {
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'socket-gateway',
      version: process.env.npm_package_version || '1.0.0',
      environment: config.env,
      uptime: process.uptime(),
      checks: {},
    };

    // Check MongoDB connection
    try {
      const mongoState = mongoose.connection.readyState;
      healthStatus.checks.mongodb = {
        status: mongoState === 1 ? 'healthy' : 'unhealthy',
        state: getMongoStateString(mongoState),
        responseTime: null,
      };

      if (mongoState === 1) {
        const start = Date.now();
        await mongoose.connection.db.admin().ping();
        healthStatus.checks.mongodb.responseTime = Date.now() - start;
      }
    } catch (error) {
      healthStatus.checks.mongodb = {
        status: 'unhealthy',
        error: error.message,
      };
      healthStatus.status = 'degraded';
    }

    // Check Redis connection
    try {
      const start = Date.now();
      const isRedisHealthy = await redisService.healthCheck();
      healthStatus.checks.redis = {
        status: isRedisHealthy ? 'healthy' : 'unhealthy',
        responseTime: Date.now() - start,
        connected: redisService.isConnected,
      };

      if (!isRedisHealthy) {
        healthStatus.status = 'degraded';
      }
    } catch (error) {
      healthStatus.checks.redis = {
        status: 'unhealthy',
        error: error.message,
      };
      healthStatus.status = 'degraded';
    }

    // Check Socket.io service
    try {
      const socketMetrics = socketService.getMetrics();
      healthStatus.checks.socketio = {
        status: 'healthy',
        activeConnections: socketMetrics.activeConnections,
        activeRooms: socketMetrics.activeRooms,
        connectedUsers: socketMetrics.connectedUsers,
      };
    } catch (error) {
      healthStatus.checks.socketio = {
        status: 'unhealthy',
        error: error.message,
      };
      healthStatus.status = 'degraded';
    }

    // Check memory usage
    const memUsage = process.memoryUsage();
    const memUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
    healthStatus.checks.memory = {
      status: memUsagePercent < config.performance.memoryThreshold ? 'healthy' : 'warning',
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      usagePercent: Math.round(memUsagePercent * 100),
      rss: memUsage.rss,
    };

    if (memUsagePercent >= config.performance.memoryThreshold) {
      healthStatus.status = 'degraded';
    }

    // Set appropriate HTTP status code
    const httpStatus = healthStatus.status === 'healthy' ? 200
      : healthStatus.status === 'degraded' ? 200 : 503;

    res.status(httpStatus).json(healthStatus);
  } catch (error) {
    logger.logError(error, { context: 'Health check' });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'socket-gateway',
      error: 'Health check failed',
      message: error.message,
    });
  }
});

// Detailed health check
router.get('/health/detailed', async (req, res) => {
  try {
    const detailedHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'socket-gateway',
      version: process.env.npm_package_version || '1.0.0',
      environment: config.env,
      uptime: process.uptime(),
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
      checks: {},
      metrics: {},
    };

    // Detailed MongoDB check
    try {
      const mongoState = mongoose.connection.readyState;
      const start = Date.now();

      if (mongoState === 1) {
        const adminDb = mongoose.connection.db.admin();
        const [, serverStatus] = await Promise.all([
          adminDb.ping(),
          adminDb.serverStatus(),
        ]);

        detailedHealth.checks.mongodb = {
          status: 'healthy',
          state: getMongoStateString(mongoState),
          responseTime: Date.now() - start,
          version: serverStatus.version,
          connections: serverStatus.connections,
          uptime: serverStatus.uptime,
        };
      } else {
        detailedHealth.checks.mongodb = {
          status: 'unhealthy',
          state: getMongoStateString(mongoState),
        };
        detailedHealth.status = 'degraded';
      }
    } catch (error) {
      detailedHealth.checks.mongodb = {
        status: 'unhealthy',
        error: error.message,
      };
      detailedHealth.status = 'degraded';
    }

    // Detailed Redis check
    try {
      const start = Date.now();
      const isRedisHealthy = await redisService.healthCheck();

      detailedHealth.checks.redis = {
        status: isRedisHealthy ? 'healthy' : 'unhealthy',
        responseTime: Date.now() - start,
        connected: redisService.isConnected,
        subscriptions: redisService.subscriptions.size,
      };

      if (!isRedisHealthy) {
        detailedHealth.status = 'degraded';
      }
    } catch (error) {
      detailedHealth.checks.redis = {
        status: 'unhealthy',
        error: error.message,
      };
      detailedHealth.status = 'degraded';
    }

    // Detailed Socket.io metrics
    try {
      const socketMetrics = socketService.getMetrics();
      detailedHealth.checks.socketio = {
        status: 'healthy',
        ...socketMetrics,
      };
      detailedHealth.metrics.socketio = socketMetrics;
    } catch (error) {
      detailedHealth.checks.socketio = {
        status: 'unhealthy',
        error: error.message,
      };
      detailedHealth.status = 'degraded';
    }

    // System metrics
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    detailedHealth.metrics.system = {
      memory: {
        ...memUsage,
        usagePercent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
      },
      cpu: cpuUsage,
      uptime: process.uptime(),
      loadAverage: process.platform !== 'win32' ? require('os').loadavg() : null,
    };

    // Metrics service health
    if (config.monitoring.enabled) {
      detailedHealth.checks.metrics = metrics.healthCheck();
    }

    // Set appropriate HTTP status code
    const httpStatus = detailedHealth.status === 'healthy' ? 200
      : detailedHealth.status === 'degraded' ? 200 : 503;

    res.status(httpStatus).json(detailedHealth);
  } catch (error) {
    logger.logError(error, { context: 'Detailed health check' });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'socket-gateway',
      error: 'Detailed health check failed',
      message: error.message,
    });
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
  try {
    // Check if all critical services are ready
    const mongoReady = mongoose.connection.readyState === 1;
    const redisReady = await redisService.healthCheck();

    if (mongoReady && redisReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        mongodb: mongoReady,
        redis: redisReady,
      });
    }
  } catch (error) {
    logger.logError(error, { context: 'Readiness check' });
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Prometheus metrics endpoint
router.get('/metrics', async (req, res) => {
  try {
    if (!config.monitoring.enabled) {
      return res.status(404).json({
        error: 'Metrics not enabled',
      });
    }

    res.set('Content-Type', metrics.register.contentType);
    res.end(await metrics.getMetrics());
  } catch (error) {
    logger.logError(error, { context: 'Metrics endpoint' });
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      message: error.message,
    });
  }
});

// Helper function to convert MongoDB connection state to string
function getMongoStateString(state) {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
  };
  return states[state] || 'unknown';
}

module.exports = router;
