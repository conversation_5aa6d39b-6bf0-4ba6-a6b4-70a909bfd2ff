# Environment Configuration for Socket Gateway Service

# Application
NODE_ENV=development
PORT=3001

# Database
MONGODB_URL=mongodb://localhost:27017/xzgame

# Redis
REDIS_URL=redis://localhost:6379

# External Services
MANAGER_SERVICE_URL=http://localhost:3002

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=xzgame-auth-service
JWT_AUDIENCE=xzgame-api,xzgame-game-service

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
RATE_LIMIT_GAME_ACTIONS=10
RATE_LIMIT_CHAT_MESSAGES=20

# Socket.io Configuration
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000
SOCKET_UPGRADE_TIMEOUT=10000
SOCKET_MAX_HTTP_BUFFER_SIZE=1048576

# Redis Pub/Sub Configuration
REDIS_POOL_SIZE=50
REDIS_TIMEOUT=5000
REDIS_RETRY_DELAY=100
REDIS_MAX_RETRIES=3

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILENAME=logs/socket-gateway.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
LOG_CONSOLE_ENABLED=true
LOG_COLORIZE=true

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9091
HEALTH_CHECK_INTERVAL=30000

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here
CSRF_ENABLED=false
HTTPS_ONLY=false

# Performance
CONNECTION_POOL_SIZE=100
MAX_CONNECTIONS_PER_IP=10
CLEANUP_INTERVAL=300000
MEMORY_THRESHOLD=0.8

# Game Configuration
MAX_ROOMS=1000
MAX_PLAYERS_PER_ROOM=8
ROOM_TIMEOUT=3600000
GAME_TIMEOUT=300000

# Development/Debug
DEBUG=socket.io*
ENABLE_SOCKET_LOGS=true
ENABLE_REDIS_LOGS=false
ENABLE_PERFORMANCE_LOGS=false
