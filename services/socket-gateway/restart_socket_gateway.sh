#!/bin/bash

# Socket Gateway Restart Script
# This script safely restarts the Socket Gateway service

echo "🔄 Restarting Socket Gateway Service..."

# Set working directory
cd "$(dirname "$0")"

# Check if Socket Gateway is already running on port 3001
echo "🔍 Checking for existing Socket Gateway processes..."

# Kill any existing Socket Gateway processes
EXISTING_PID=$(pgrep -f "node.*socket-gateway.*app.js")
if [ ! -z "$EXISTING_PID" ]; then
    echo "🛑 Found existing Socket Gateway process (PID: $EXISTING_PID)"
    echo "   Stopping gracefully..."
    kill -TERM $EXISTING_PID
    
    # Wait for graceful shutdown
    sleep 3
    
    # Force kill if still running
    if kill -0 $EXISTING_PID 2>/dev/null; then
        echo "   Force stopping..."
        kill -KILL $EXISTING_PID
    fi
    
    echo "✅ Existing process stopped"
else
    echo "✅ No existing Socket Gateway processes found"
fi

# Check if port 3001 is free
echo "🔍 Checking port 3001 availability..."
if lsof -i :3001 > /dev/null 2>&1; then
    echo "⚠️  Port 3001 is in use by another process:"
    lsof -i :3001
    echo "   Please stop the process using port 3001 or change the PORT in .env"
    exit 1
else
    echo "✅ Port 3001 is available"
fi

# Load environment variables
if [ -f ".env" ]; then
    echo "📋 Loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  No .env file found"
fi

# Display configuration
echo ""
echo "📋 Socket Gateway Configuration:"
echo "   Port: ${PORT:-3001}"
echo "   Environment: ${NODE_ENV:-development}"
echo "   Redis URL: ${REDIS_URL:-redis://localhost:6379}"
echo "   Log Level: ${LOG_LEVEL:-info}"

# Start the service
echo ""
echo "🚀 Starting Socket Gateway on port ${PORT:-3001}..."

if [ "${NODE_ENV}" = "development" ]; then
    echo "🔧 Starting in development mode with nodemon..."
    npm run dev
else
    echo "🏭 Starting in production mode..."
    npm start
fi
