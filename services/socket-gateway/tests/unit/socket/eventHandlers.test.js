/**
 * Unit tests for SocketEventHandlers
 */

const SocketEventHandlers = require('../../../src/services/socket/eventHandlers');

describe('SocketEventHandlers', () => {
  let eventHandlers;
  let mockSocket;
  let mockLobbyHandlers;
  let mockRoomHandlers;
  let mockGameHandlers;

  beforeEach(() => {
    eventHandlers = new SocketEventHandlers();
    
    // Mock socket object
    mockSocket = {
      id: 'socket_123',
      userId: 'user_456',
      username: 'testuser',
      on: jest.fn(),
      emit: jest.fn()
    };

    // Mock handlers
    mockLobbyHandlers = {
      handleSubscribeLobby: jest.fn(),
      handleUnsubscribeLobby: jest.fn()
    };

    mockRoomHandlers = {
      handleJoinRoom: jest.fn(),
      handleLeaveRoom: jest.fn(),
      handleSubscribeRoom: jest.fn(),
      handleUnsubscribeRoom: jest.fn()
    };

    mockGameHandlers = {
      handlePlayerReady: jest.fn(),
      handlePlayerUnready: jest.fn(),
      handlePlayerReadySpec: jest.fn(),
      handlePlayerUnreadySpec: jest.fn(),
      handleSelectWheelColor: jest.fn(),
      handleSelectColorSpec: jest.fn(),
      handleSelectPositionSpec: jest.fn()
    };

    eventHandlers.setHandlers({
      lobbyHandlers: mockLobbyHandlers,
      roomHandlers: mockRoomHandlers,
      gameHandlers: mockGameHandlers
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('setHandlers', () => {
    it('should set handler dependencies correctly', () => {
      expect(eventHandlers.lobbyHandlers).toBe(mockLobbyHandlers);
      expect(eventHandlers.roomHandlers).toBe(mockRoomHandlers);
      expect(eventHandlers.gameHandlers).toBe(mockGameHandlers);
    });
  });

  describe('setupEventHandlers', () => {
    it('should setup all event handlers', () => {
      eventHandlers.setupEventHandlers(mockSocket);

      // Verify that socket.on was called for all expected events
      const expectedEvents = [
        'subscribe_lobby',
        'unsubscribe_lobby',
        'join_room',
        'leave_room',
        'subscribe_room',
        'unsubscribe_room',
        'player_ready',
        'player_unready',
        'player_ready_spec',
        'player_unready_spec',
        'select_wheel_color',
        'select_color_spec',
        'select_position_spec',
        'ping',
        'message'
      ];

      expectedEvents.forEach(event => {
        expect(mockSocket.on).toHaveBeenCalledWith(event, expect.any(Function));
      });
    });
  });

  describe('handlePing', () => {
    it('should handle ping with callback', () => {
      const mockCallback = jest.fn();
      eventHandlers.handlePing(mockSocket, mockCallback);

      expect(mockCallback).toHaveBeenCalledWith(expect.objectContaining({
        timestamp: expect.any(String),
        serverTime: expect.any(Number)
      }));
    });

    it('should handle ping without callback', () => {
      eventHandlers.handlePing(mockSocket);

      expect(mockSocket.emit).toHaveBeenCalledWith('pong', expect.objectContaining({
        timestamp: expect.any(String),
        serverTime: expect.any(Number)
      }));
    });
  });

  describe('handleGenericMessage', () => {
    it('should handle generic message', () => {
      const messageData = {
        type: 'test_message',
        payload: { data: 'test' }
      };

      eventHandlers.handleGenericMessage(mockSocket, messageData);

      expect(mockSocket.emit).toHaveBeenCalledWith('message_ack', expect.objectContaining({
        received: true,
        timestamp: expect.any(String),
        originalType: 'test_message'
      }));
    });
  });

  describe('handleWithErrorWrapper', () => {
    it('should execute handler successfully', async () => {
      const mockHandler = jest.fn().mockResolvedValue();
      const mockCallback = jest.fn();

      await eventHandlers.handleWithErrorWrapper(
        mockHandler,
        'test_event',
        mockSocket,
        mockCallback
      );

      expect(mockHandler).toHaveBeenCalled();
      expect(mockCallback).not.toHaveBeenCalled();
    });

    it('should handle handler error', async () => {
      const mockHandler = jest.fn().mockRejectedValue(new Error('Handler failed'));
      const mockCallback = jest.fn();

      await eventHandlers.handleWithErrorWrapper(
        mockHandler,
        'test_event',
        mockSocket,
        mockCallback
      );

      expect(mockHandler).toHaveBeenCalled();
      expect(mockCallback).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'Failed to handle test_event',
        code: 'TEST_EVENT_FAILED'
      }));
    });

    it('should handle handler error without callback', async () => {
      const mockHandler = jest.fn().mockRejectedValue(new Error('Handler failed'));

      await eventHandlers.handleWithErrorWrapper(
        mockHandler,
        'test_event',
        mockSocket,
        null
      );

      expect(mockHandler).toHaveBeenCalled();
      // Should not throw error when no callback provided
    });
  });

  describe('event delegation', () => {
    beforeEach(() => {
      eventHandlers.setupEventHandlers(mockSocket);
    });

    it('should delegate lobby events correctly', () => {
      // Get the handler function that was registered for subscribe_lobby
      const subscribeLobbyHandler = mockSocket.on.mock.calls
        .find(call => call[0] === 'subscribe_lobby')[1];

      const mockCallback = jest.fn();
      subscribeLobbyHandler(mockCallback);

      expect(mockLobbyHandlers.handleSubscribeLobby).toHaveBeenCalledWith(mockSocket, mockCallback);
    });

    it('should delegate room events correctly', () => {
      // Get the handler function that was registered for join_room
      const joinRoomHandler = mockSocket.on.mock.calls
        .find(call => call[0] === 'join_room')[1];

      const mockData = { roomId: 'room_123' };
      const mockCallback = jest.fn();
      joinRoomHandler(mockData, mockCallback);

      expect(mockRoomHandlers.handleJoinRoom).toHaveBeenCalledWith(mockSocket, mockData, mockCallback);
    });

    it('should delegate game events correctly', () => {
      // Get the handler function that was registered for player_ready
      const playerReadyHandler = mockSocket.on.mock.calls
        .find(call => call[0] === 'player_ready')[1];

      const mockData = { roomId: 'room_123' };
      const mockCallback = jest.fn();
      playerReadyHandler(mockData, mockCallback);

      expect(mockGameHandlers.handlePlayerReady).toHaveBeenCalledWith(mockSocket, mockData, mockCallback);
    });
  });

  describe('error handling in event setup', () => {
    it('should handle errors during event setup', () => {
      // Mock console.error to avoid test output pollution
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Create a socket that throws an error when on() is called
      const errorSocket = {
        id: 'error_socket',
        userId: 'user_456',
        on: jest.fn().mockImplementation(() => {
          throw new Error('Socket error');
        })
      };

      // Should not throw an error
      expect(() => {
        eventHandlers.setupEventHandlers(errorSocket);
      }).not.toThrow();

      consoleSpy.mockRestore();
    });
  });
});
