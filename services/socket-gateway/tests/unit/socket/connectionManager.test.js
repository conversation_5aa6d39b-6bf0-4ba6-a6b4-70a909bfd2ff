/**
 * Unit tests for SocketConnectionManager
 */

const SocketConnectionManager = require('../../../src/services/socket/connectionManager');

describe('SocketConnectionManager', () => {
  let connectionManager;
  let mockSocket;

  beforeEach(() => {
    connectionManager = new SocketConnectionManager();
    
    // Mock socket object
    mockSocket = {
      id: 'socket_123',
      userId: 'user_456',
      username: 'testuser',
      sessionId: 'session_789',
      handshake: {
        address: '127.0.0.1',
        headers: {
          'user-agent': 'test-agent'
        }
      },
      emit: jest.fn(),
      on: jest.fn(),
      disconnect: jest.fn()
    };
  });

  afterEach(() => {
    connectionManager.cleanup();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default values', () => {
      expect(connectionManager.connectionCount).toBe(0);
      expect(connectionManager.connectedUsers.size).toBe(0);
      expect(connectionManager.metrics.totalConnections).toBe(0);
      expect(connectionManager.metrics.activeConnections).toBe(0);
    });
  });

  describe('updateConnectionMetrics', () => {
    it('should update connection metrics correctly', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');

      expect(connectionManager.connectionCount).toBe(1);
      expect(connectionManager.metrics.totalConnections).toBe(1);
      expect(connectionManager.metrics.activeConnections).toBe(1);
      expect(connectionManager.connectedUsers.has('user_456')).toBe(true);
      expect(connectionManager.connectedUsers.get('user_456').has('socket_123')).toBe(true);
    });

    it('should handle multiple sockets for same user', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
      connectionManager.updateConnectionMetrics('user_456', 'socket_456');

      expect(connectionManager.connectionCount).toBe(2);
      expect(connectionManager.connectedUsers.get('user_456').size).toBe(2);
    });
  });

  describe('handleConnection', () => {
    let mockSetupEventHandlers;
    let mockFetchUserBalance;

    beforeEach(() => {
      mockSetupEventHandlers = jest.fn();
      mockFetchUserBalance = jest.fn().mockResolvedValue(1000);
    });

    it('should handle successful connection', async () => {
      await connectionManager.handleConnection(
        mockSocket,
        mockSetupEventHandlers,
        mockFetchUserBalance
      );

      expect(mockFetchUserBalance).toHaveBeenCalledWith('user_456', undefined);
      expect(mockSetupEventHandlers).toHaveBeenCalledWith(mockSocket);
      expect(mockSocket.emit).toHaveBeenCalledWith('connect_ack', expect.objectContaining({
        userId: 'user_456',
        username: 'testuser',
        sessionId: 'session_789',
        balance: 1000
      }));
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should handle connection with balance fetch error', async () => {
      mockFetchUserBalance.mockRejectedValue(new Error('Balance fetch failed'));

      await connectionManager.handleConnection(
        mockSocket,
        mockSetupEventHandlers,
        mockFetchUserBalance
      );

      expect(mockSocket.emit).toHaveBeenCalledWith('connect_ack', expect.objectContaining({
        balance: 0 // Should default to 0 on error
      }));
    });

    it('should handle connection error', async () => {
      mockSetupEventHandlers.mockImplementation(() => {
        throw new Error('Setup failed');
      });

      await connectionManager.handleConnection(
        mockSocket,
        mockSetupEventHandlers,
        mockFetchUserBalance
      );

      expect(mockSocket.emit).toHaveBeenCalledWith('error', expect.objectContaining({
        code: 'CONNECTION_ERROR',
        message: 'Connection handling failed'
      }));
      expect(mockSocket.disconnect).toHaveBeenCalledWith(true);
    });
  });

  describe('handleDisconnection', () => {
    beforeEach(() => {
      // Setup a connection first
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
    });

    it('should handle disconnection correctly', () => {
      connectionManager.handleDisconnection(mockSocket, 'client disconnect');

      expect(connectionManager.connectionCount).toBe(0);
      expect(connectionManager.metrics.activeConnections).toBe(0);
      expect(connectionManager.connectedUsers.has('user_456')).toBe(false);
    });

    it('should handle partial disconnection for user with multiple sockets', () => {
      // Add another socket for the same user
      connectionManager.updateConnectionMetrics('user_456', 'socket_456');

      connectionManager.handleDisconnection(mockSocket, 'client disconnect');

      expect(connectionManager.connectionCount).toBe(1);
      expect(connectionManager.connectedUsers.has('user_456')).toBe(true);
      expect(connectionManager.connectedUsers.get('user_456').has('socket_123')).toBe(false);
      expect(connectionManager.connectedUsers.get('user_456').has('socket_456')).toBe(true);
    });
  });

  describe('getMetrics', () => {
    it('should return correct metrics', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
      connectionManager.updateConnectionMetrics('user_789', 'socket_456');

      const metrics = connectionManager.getMetrics();

      expect(metrics.totalConnections).toBe(2);
      expect(metrics.activeConnections).toBe(2);
      expect(metrics.connectedUsers).toBe(2);
      expect(metrics.totalSockets).toBe(2);
    });
  });

  describe('getUserSockets', () => {
    it('should return user sockets', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
      connectionManager.updateConnectionMetrics('user_456', 'socket_456');

      const userSockets = connectionManager.getUserSockets('user_456');
      expect(userSockets.size).toBe(2);
      expect(userSockets.has('socket_123')).toBe(true);
      expect(userSockets.has('socket_456')).toBe(true);
    });

    it('should return null for non-existent user', () => {
      const userSockets = connectionManager.getUserSockets('non_existent_user');
      expect(userSockets).toBeNull();
    });
  });

  describe('isUserConnected', () => {
    it('should return true for connected user', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
      expect(connectionManager.isUserConnected('user_456')).toBe(true);
    });

    it('should return false for disconnected user', () => {
      expect(connectionManager.isUserConnected('user_456')).toBe(false);
    });
  });

  describe('cleanup', () => {
    it('should reset all state', () => {
      connectionManager.updateConnectionMetrics('user_456', 'socket_123');
      connectionManager.cleanup();

      expect(connectionManager.connectionCount).toBe(0);
      expect(connectionManager.connectedUsers.size).toBe(0);
      expect(connectionManager.metrics.activeConnections).toBe(0);
    });
  });
});
