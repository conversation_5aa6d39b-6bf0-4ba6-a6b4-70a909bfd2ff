/**
 * Jest test setup file
 */

// Mock logger to avoid console output during tests
jest.mock('../src/utils/logger', () => ({
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  logError: jest.fn(),
  logSocketEvent: jest.fn()
}));

// Mock subscription service
jest.mock('../src/services/subscriptionService', () => ({
  subscribeLobby: jest.fn(),
  unsubscribeLobby: jest.fn(),
  subscribeRoom: jest.fn(),
  unsubscribeRoom: jest.fn(),
  handleDisconnect: jest.fn(),
  getUserSubscription: jest.fn(),
  getLobbySubscriberCount: jest.fn().mockReturnValue(0),
  lobbySubscribers: new Set()
}));

// Mock room state manager
jest.mock('../src/services/roomStateManager', () => ({
  userRooms: new Map(),
  roomUsers: new Map(),
  addUserToRoom: jest.fn(),
  removeUserFromRoom: jest.fn(),
  getUserCurrentRoom: jest.fn(),
  executeRoomJoin: jest.fn().mockResolvedValue({ success: true })
}));

// Mock redis service
jest.mock('../src/services/redisService', () => ({
  publish: jest.fn(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  on: jest.fn()
}));

// Global test timeout
jest.setTimeout(10000);
