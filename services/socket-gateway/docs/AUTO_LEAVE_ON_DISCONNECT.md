# Auto-Leave on Socket Disconnect

## Overview

This feature automatically removes players from rooms when their socket connection is lost, ensuring that rooms don't have "ghost players" who appear to be in the room but are actually disconnected.

## How It Works

### 1. **Socket Disconnect Detection**
When a socket disconnects (for any reason), the `ConnectionManager` detects this and triggers the auto-leave process.

### 2. **Room Subscription Check**
The system checks if the disconnected user was subscribed to any room:
- Gets the user's current subscription from `SubscriptionService`
- Only processes if the user was subscribed to a room (not lobby)

### 3. **Automatic Room Leave**
If the user was in a room, the system automatically:
- Sends a `leave_room` request to the Room Orchestrator via Redis pub/sub
- Includes special flags to indicate this is an automatic leave
- Processes the leave request through the normal room leave workflow

### 4. **Room State Update**
The Room Orchestrator:
- Removes the player from the Manager Service
- Updates room player counts
- Broadcasts room updates to remaining players
- Handles any game state changes if needed

## Implementation Details

### Socket Gateway (`connectionManager.js`)

```javascript
async handleDisconnection(socket, reason) {
  // ... existing disconnect logic ...
  
  // Handle automatic room leave on disconnect
  await this.handleAutoRoomLeaveOnDisconnect(socket, reason);
  
  // ... rest of disconnect cleanup ...
}

async handleAutoRoomLeaveOnDisconnect(socket, reason) {
  const { userId, username } = socket;
  
  // Get current room subscription
  const subscription = subscriptionService.getUserSubscription(userId);
  
  if (subscription && subscription.channel === 'room') {
    const roomId = subscription.roomId;
    
    // Send leave request to Room Orchestrator
    await this.routeToRoomOrchestrator('leave_room', {
      userId,
      username,
      roomId,
      socketId: socket.id,
      autoLeave: true, // Flag indicating automatic leave
      disconnectReason: reason,
    });
  }
}
```

### Room Service (`redis_orchestrator_handler.go`)

The Room Service receives the auto-leave request and processes it through the normal leave room workflow:

```go
func (h *RedisOrchestratorHandler) handleLeaveRoomRequest(ctx context.Context, msg *RedisMessage) (*OrchestratorResponse, error) {
    var leaveRequest LeaveRoomRequest
    // ... parse request ...
    
    // Execute orchestrated leave room operation
    leaveResponse, err := h.orchestrator.OrchestateLeaveRoom(ctx, &leaveRequest)
    // ... handle response ...
}
```

## Disconnect Scenarios Handled

### 1. **Network Issues**
- WiFi disconnection
- Mobile network loss
- Internet connectivity problems

### 2. **Client-Side Issues**
- Browser/app crash
- Tab closure
- Device shutdown

### 3. **Server-Side Issues**
- Socket timeout
- Connection errors
- Server restart

### 4. **Intentional Disconnects**
- User closing browser
- App being killed
- Manual disconnect

## Benefits

### 1. **Accurate Room State**
- Rooms show correct player counts
- No "ghost players" in rooms
- Accurate game state management

### 2. **Better User Experience**
- Other players see immediate updates when someone leaves
- Games can continue properly without waiting for disconnected players
- Room capacity is freed up immediately

### 3. **System Reliability**
- Prevents rooms from getting stuck with disconnected players
- Maintains data consistency across services
- Reduces manual cleanup requirements

## Configuration

### Auto-Leave Settings
The auto-leave functionality is enabled by default and doesn't require special configuration. However, you can monitor it through:

### Logging
Auto-leave events are logged with specific identifiers:

```javascript
logger.info('Auto-leaving room on disconnect', {
  socketId: socket.id,
  userId,
  username,
  roomId,
  disconnectReason: reason,
});
```

### Redis Message Format
Auto-leave requests include special metadata:

```json
{
  "event": {
    "type": "leave_room",
    "payload": {
      "userId": "user123",
      "username": "player1",
      "roomId": "room456",
      "socketId": "socket789",
      "autoLeave": true,
      "disconnectReason": "client namespace disconnect"
    }
  },
  "metadata": {
    "serviceId": "socket-gateway",
    "source": "auto_disconnect",
    "correlationId": "gw-disconnect-..."
  }
}
```

## Testing

### Manual Testing
1. Connect to a room via socket
2. Join a room
3. Disconnect the socket (close browser, kill connection, etc.)
4. Check that the room player count decreases
5. Verify other players receive room updates

### Automated Testing
Use the provided test script:

```bash
cd services/socket-gateway
node test_auto_leave.js
```

### Monitoring
Check the logs for auto-leave events:

```bash
# Socket Gateway logs
grep "Auto-leaving room on disconnect" logs/socket-gateway.log

# Room Service logs  
grep "leave_room.*autoLeave" logs/room-service.log
```

## Error Handling

### Graceful Degradation
If auto-leave fails:
- The disconnect process continues normally
- Subscription cleanup still occurs
- Error is logged but doesn't block disconnection
- Manual cleanup can be performed later

### Retry Logic
- Auto-leave requests are fire-and-forget for disconnect scenarios
- No retry logic to avoid blocking disconnection
- Failed auto-leaves can be detected through monitoring

## Compatibility

### Backward Compatibility
- Existing disconnect handling is preserved
- No breaking changes to socket events
- Works with existing room management logic

### Service Dependencies
- Requires Redis pub/sub communication
- Depends on Room Service orchestrator
- Uses existing subscription management

## Future Enhancements

### Potential Improvements
1. **Reconnection Grace Period**: Allow brief reconnections before auto-leave
2. **Configurable Timeouts**: Different auto-leave delays for different scenarios
3. **Batch Processing**: Group multiple disconnects for efficiency
4. **Analytics**: Track disconnect patterns and auto-leave statistics

### Monitoring Enhancements
1. **Metrics Dashboard**: Track auto-leave frequency and success rates
2. **Alerting**: Notify on high disconnect rates or auto-leave failures
3. **Performance Monitoring**: Measure auto-leave processing time
