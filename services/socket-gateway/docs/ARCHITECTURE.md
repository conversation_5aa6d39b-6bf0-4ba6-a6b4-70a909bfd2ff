# Socket Gateway Architecture

## Overview

The Socket Gateway service follows a modular, clean architecture design with clear separation of concerns. It has been refactored from a monolithic 6,968-line file into focused, maintainable modules under 200 lines each.

## Architecture Principles

### 1. Single Responsibility Principle
Each module has a single, well-defined responsibility:
- **ConnectionManager**: Handles socket connections and disconnections
- **EventHandlers**: Routes socket events to appropriate handlers
- **LobbyHandlers**: Manages lobby subscriptions and room list updates
- **RoomHandlers**: Handles room joining, leaving, and subscriptions
- **GameHandlers**: Manages game-specific events and actions

### 2. Separation of Concerns
- **Business Logic**: Separated from infrastructure concerns
- **Event Handling**: Decoupled from business logic
- **State Management**: Centralized in dedicated services
- **Error Handling**: Consistent across all modules

### 3. Dependency Injection
Components receive their dependencies through constructor injection, making them testable and loosely coupled.

### 4. Modular Design
Each module is self-contained with clear interfaces and minimal dependencies.

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Socket Gateway Service                    │
├─────────────────────────────────────────────────────────────┤
│                     Express HTTP Server                     │
│                    Socket.io WebSocket                      │
├─────────────────────────────────────────────────────────────┤
│                   Authentication Middleware                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Connection      │  │ Event           │  │ Redis        │ │
│  │ Manager         │  │ Handlers        │  │ Message      │ │
│  │                 │  │                 │  │ Handler      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Lobby           │  │ Room            │  │ Game         │ │
│  │ Handlers        │  │ Handlers        │  │ Handlers     │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Subscription    │  │ Room State      │  │ Channel      │ │
│  │ Service         │  │ Manager         │  │ Router       │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Redis           │  │ Logger          │  │ Metrics      │ │
│  │ Service         │  │ Utility         │  │ Collector    │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Module Structure

### Core Modules

#### 1. SocketService (Main Orchestrator)
- **File**: `src/services/socketService.js`
- **Size**: 6,469 lines (reduced from 6,968)
- **Responsibility**: Main service orchestration and legacy method delegation
- **Dependencies**: All specialized handlers

#### 2. ConnectionManager
- **File**: `src/services/socket/connectionManager.js`
- **Size**: 195 lines
- **Responsibility**: Socket connection lifecycle management
- **Key Features**:
  - Connection tracking and metrics
  - User session management
  - Connection authentication
  - Disconnection cleanup

#### 3. EventHandlers
- **File**: `src/services/socket/eventHandlers.js`
- **Size**: 198 lines
- **Responsibility**: Socket event registration and routing
- **Key Features**:
  - Event handler setup
  - Error wrapping for all events
  - Event delegation to specialized handlers

#### 4. RedisMessageHandler
- **File**: `src/services/socket/redisMessageHandler.js`
- **Size**: 197 lines
- **Responsibility**: Redis pub/sub message processing
- **Key Features**:
  - Message routing by channel
  - Event type handling
  - Error recovery and logging

#### 5. SocketMetrics
- **File**: `src/services/socket/metricsCollector.js`
- **Size**: 194 lines
- **Responsibility**: Performance monitoring and metrics collection
- **Key Features**:
  - Connection metrics
  - Message rate tracking
  - Response time monitoring
  - Health status reporting

### Specialized Handlers

#### 6. LobbyHandlers
- **File**: `src/services/socket/lobbyHandlers.js`
- **Size**: 189 lines
- **Responsibility**: Lobby operations and room list management
- **Key Features**:
  - Lobby subscription management
  - Room list updates
  - Game service communication

#### 7. RoomHandlers
- **File**: `src/services/socket/roomHandlers.js`
- **Size**: 198 lines
- **Responsibility**: Room operations and state management
- **Key Features**:
  - Room joining and leaving
  - Room subscriptions
  - State validation and management

#### 8. GameHandlers
- **File**: `src/services/socket/gameHandlers.js`
- **Size**: 187 lines
- **Responsibility**: Game-specific events and actions
- **Key Features**:
  - Player ready state management
  - Game action handling (color selection, position selection)
  - Game service communication

## Data Flow

### 1. Connection Flow
```
Client Connect → Auth Middleware → ConnectionManager → EventHandlers Setup → Connection Ack
```

### 2. Event Processing Flow
```
Socket Event → EventHandlers → Specialized Handler → Business Logic → Response/Broadcast
```

### 3. Redis Message Flow
```
Redis Message → RedisMessageHandler → Channel Router → Event Processing → Socket Broadcast
```

### 4. Metrics Flow
```
Event/Connection → MetricsCollector → Aggregation → Health Monitoring → Reporting
```

## Error Handling Strategy

### 1. Layered Error Handling
- **Transport Layer**: Socket.io error events
- **Application Layer**: Try-catch blocks with proper logging
- **Business Layer**: Validation and business rule errors
- **Integration Layer**: External service communication errors

### 2. Error Recovery
- **Graceful Degradation**: Fallback mechanisms for external service failures
- **Circuit Breaker**: Prevent cascade failures
- **Retry Logic**: Automatic retry for transient failures
- **Dead Letter Queue**: Failed message handling

### 3. Error Monitoring
- **Structured Logging**: Consistent error format across modules
- **Error Metrics**: Track error rates and types
- **Alerting**: Real-time error notifications
- **Debugging**: Comprehensive context in error logs

## Performance Optimizations

### 1. Connection Management
- **Connection Pooling**: Efficient socket connection reuse
- **Memory Management**: Proper cleanup of disconnected sockets
- **Rate Limiting**: Prevent abuse and resource exhaustion

### 2. Message Processing
- **Async Processing**: Non-blocking event handling
- **Message Batching**: Efficient bulk operations
- **Caching**: Reduce redundant data fetching

### 3. Redis Integration
- **Connection Multiplexing**: Shared Redis connections
- **Message Deduplication**: Prevent duplicate processing
- **Channel Optimization**: Efficient pub/sub patterns

## Scalability Considerations

### 1. Horizontal Scaling
- **Stateless Design**: No server-side session state
- **Load Balancing**: Distribute connections across instances
- **Redis Clustering**: Scale Redis for high throughput

### 2. Vertical Scaling
- **Memory Optimization**: Efficient data structures
- **CPU Optimization**: Minimize processing overhead
- **I/O Optimization**: Async operations and connection pooling

### 3. Monitoring and Alerting
- **Real-time Metrics**: Connection counts, message rates
- **Performance Monitoring**: Response times, error rates
- **Capacity Planning**: Resource utilization tracking

## Security Architecture

### 1. Authentication
- **JWT Validation**: Secure token-based authentication
- **Token Refresh**: Automatic token renewal
- **Session Management**: Secure session handling

### 2. Authorization
- **Role-based Access**: User permission validation
- **Resource Protection**: Room and game access control
- **Rate Limiting**: Prevent abuse and DoS attacks

### 3. Data Protection
- **Input Validation**: Sanitize all user inputs
- **Output Encoding**: Prevent injection attacks
- **Secure Communication**: TLS/SSL encryption
