# Socket Gateway API Documentation

## Overview

The Socket Gateway service provides real-time bidirectional communication between clients and the game backend using Socket.io. It handles user authentication, room management, lobby subscriptions, and game events.

## Authentication

All socket connections require JWT authentication. The token should be provided in the connection handshake.

```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

## Connection Events

### `connect_ack`
Sent by server after successful connection.

**Response:**
```javascript
{
  userId: "string",
  username: "string", 
  sessionId: "string",
  serverTime: "ISO string",
  balance: number
}
```

### `disconnect`
Triggered when client disconnects.

**Reasons:**
- `client disconnect` - Client initiated
- `server disconnect` - Server initiated
- `transport close` - Network issues

## Lobby Events

### `subscribe_lobby`
Subscribe to lobby updates (room list changes).

**Request:**
```javascript
socket.emit('subscribe_lobby', (response) => {
  console.log(response); // { success: true }
});
```

**Response:**
```javascript
{ success: true }
```

### `unsubscribe_lobby`
Unsubscribe from lobby updates.

**Request:**
```javascript
socket.emit('unsubscribe_lobby', (response) => {
  console.log(response); // { success: true }
});
```

### `room_list_updated`
Received when subscribed to lobby and room list changes.

**Event Data:**
```javascript
{
  action: "initial_load" | "room_created" | "room_deleted" | "room_status_changed",
  rooms: [
    {
      id: "string",
      name: "string",
      gameType: "prizewheel" | "amidakuji",
      status: "waiting" | "playing" | "finished",
      playerCount: number,
      maxPlayers: number,
      betAmount: number,
      prizePool: number
    }
  ],
  timestamp: "ISO string"
}
```

## Room Events

### `join_room`
Join a game room.

**Request:**
```javascript
socket.emit('join_room', {
  roomId: "string",
  password: "string", // optional
  betAmount: number
}, (response) => {
  console.log(response);
});
```

**Response:**
```javascript
{
  success: true,
  message: "Successfully joined room",
  roomId: "string",
  room: {
    id: "string",
    name: "string",
    gameType: "prizewheel" | "amidakuji",
    status: "waiting" | "playing",
    playerCount: number,
    maxPlayers: number,
    betAmount: number,
    prizePool: number
  },
  player: {
    userId: "string",
    username: "string",
    isReady: boolean,
    balance: number
  },
  roomState: {
    playerCount: number,
    readyCount: number,
    canStartGame: boolean,
    gameInProgress: boolean
  },
  players: [
    {
      userId: "string",
      username: "string",
      isReady: boolean,
      position: number // for amidakuji
    }
  ]
}
```

### `leave_room`
Leave a game room.

**Request:**
```javascript
socket.emit('leave_room', {
  roomId: "string"
}, (response) => {
  console.log(response);
});
```

**Response:**
```javascript
{
  success: true,
  message: "Successfully left room",
  roomId: "string"
}
```

### `subscribe_room`
Subscribe to room events (must be in room first).

**Request:**
```javascript
socket.emit('subscribe_room', {
  roomId: "string"
}, (response) => {
  console.log(response);
});
```

### `unsubscribe_room`
Unsubscribe from room events.

**Request:**
```javascript
socket.emit('unsubscribe_room', {
  roomId: "string"
}, (response) => {
  console.log(response);
});
```

### `room_info_updated`
Received when subscribed to room and room state changes.

**Event Data:**
```javascript
{
  room: { /* room object */ },
  roomState: { /* room state object */ },
  players: [ /* players array */ ],
  gameConfig: {
    gameType: "string",
    minPlayers: number,
    maxPlayers: number,
    settings: { /* game-specific settings */ }
  },
  gameSpecificData: { /* varies by game type */ },
  timestamp: "ISO string"
}
```

## Game Events

### `player_ready`
Set player ready state.

**Request:**
```javascript
socket.emit('player_ready', {
  roomId: "string"
}, (response) => {
  console.log(response);
});
```

### `player_unready`
Set player unready state.

**Request:**
```javascript
socket.emit('player_unready', {
  roomId: "string"
}, (response) => {
  console.log(response);
});
```

### Prize Wheel Game Events

#### `select_wheel_color`
Select a color on the prize wheel.

**Request:**
```javascript
socket.emit('select_wheel_color', {
  roomId: "string",
  color: "red" | "blue" | "green" | "yellow" | "purple" | "orange" | "pink" | "cyan"
}, (response) => {
  console.log(response);
});
```

### Amidakuji Game Events

#### `select_position_spec`
Select a starting position for Amidakuji.

**Request:**
```javascript
socket.emit('select_position_spec', {
  roomId: "string",
  position: number // 0-based index
}, (response) => {
  console.log(response);
});
```

## Utility Events

### `ping`
Heartbeat/connectivity test.

**Request:**
```javascript
socket.emit('ping', (response) => {
  console.log(response);
});
```

**Response:**
```javascript
{
  timestamp: "ISO string",
  serverTime: number
}
```

## Error Handling

All events can return error responses:

```javascript
{
  success: false,
  error: "Error message",
  code: "ERROR_CODE",
  timestamp: "ISO string"
}
```

### Common Error Codes

- `AUTHENTICATION_REQUIRED` - Invalid or missing JWT token
- `ROOM_ID_REQUIRED` - Missing room ID parameter
- `JOIN_ROOM_FAILED` - Failed to join room
- `LEAVE_ROOM_FAILED` - Failed to leave room
- `SUBSCRIBE_LOBBY_FAILED` - Failed to subscribe to lobby
- `SUBSCRIBE_ROOM_FAILED` - Failed to subscribe to room
- `PLAYER_READY_FAILED` - Failed to set ready state
- `COLOR_SELECTION_FAILED` - Failed to select color
- `POSITION_SELECTION_FAILED` - Failed to select position

## Rate Limiting

Socket events are rate limited to prevent abuse:
- 100 events per minute per connection
- 10 room join attempts per minute per user
- 50 lobby subscription requests per minute per user

## Connection Limits

- Maximum 1000 concurrent connections per server instance
- Maximum 5 connections per user account
- Idle connections are automatically disconnected after 30 minutes
