# Socket Gateway Setup and Deployment Guide

## Prerequisites

### System Requirements
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Redis**: >= 6.0.0
- **Memory**: Minimum 512MB RAM (2GB+ recommended for production)
- **CPU**: Minimum 1 core (2+ cores recommended for production)

### External Dependencies
- **Game Service**: Backend game logic service
- **Authentication Service**: JWT token validation
- **Redis Server**: Message broker and session storage

## Development Setup

### 1. Clone and Install Dependencies

```bash
# Navigate to socket-gateway directory
cd services/socket-gateway

# Install dependencies
npm install

# Install development dependencies
npm install --save-dev
```

### 2. Environment Configuration

Create environment files for different stages:

#### `.env.development`
```env
# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Game Service Configuration
GAME_SERVICE_URL=http://localhost:3002
GAME_SERVICE_TIMEOUT=10000

# Authentication Configuration
JWT_SECRET=your-development-jwt-secret
JWT_EXPIRY=24h

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true

# Socket.io Configuration
SOCKET_CORS_ORIGIN=http://localhost:3000
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
```

#### `.env.production`
```env
# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Redis Configuration (use environment variables in production)
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB=0

# Game Service Configuration
GAME_SERVICE_URL=${GAME_SERVICE_URL}
GAME_SERVICE_TIMEOUT=5000

# Authentication Configuration
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRY=1h

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=false

# Socket.io Configuration
SOCKET_CORS_ORIGIN=${FRONTEND_URL}
SOCKET_PING_TIMEOUT=30000
SOCKET_PING_INTERVAL=10000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=50

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
```

### 3. Redis Setup

#### Local Development (Docker)
```bash
# Start Redis container
docker run -d \
  --name redis-dev \
  -p 6379:6379 \
  redis:7-alpine

# Verify Redis is running
docker logs redis-dev
```

#### Local Development (Native)
```bash
# Install Redis (macOS)
brew install redis

# Start Redis
brew services start redis

# Verify Redis is running
redis-cli ping
```

### 4. Start Development Server

```bash
# Start with nodemon for auto-reload
npm run dev

# Or start normally
npm start
```

## Testing

### Unit Tests
```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run specific test file
npm test -- tests/unit/socket/connectionManager.test.js
```

### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Run load test
artillery run tests/load/socket-load-test.yml
```

## Production Deployment

### 1. Docker Deployment

#### Build Docker Image
```bash
# Build production image
npm run docker:build:prod

# Or manually
docker build -f Dockerfile.prod -t socket-gateway:latest .
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  socket-gateway:
    image: socket-gateway:latest
    ports:
      - "3001:3001"
      - "9090:9090"  # Metrics port
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - GAME_SERVICE_URL=http://game-service:3002
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
```

#### Run with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f socket-gateway

# Scale socket-gateway instances
docker-compose up -d --scale socket-gateway=3
```

### 2. Kubernetes Deployment

#### Deployment Configuration
```yaml
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: socket-gateway
  labels:
    app: socket-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: socket-gateway
  template:
    metadata:
      labels:
        app: socket-gateway
    spec:
      containers:
      - name: socket-gateway
        image: socket-gateway:latest
        ports:
        - containerPort: 3001
        - containerPort: 9090
        env:
        - name: NODE_ENV
          value: "production"
        - name: REDIS_HOST
          value: "redis-service"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: socket-gateway-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service Configuration
```yaml
# k8s/service.yml
apiVersion: v1
kind: Service
metadata:
  name: socket-gateway-service
spec:
  selector:
    app: socket-gateway
  ports:
  - name: http
    port: 3001
    targetPort: 3001
  - name: metrics
    port: 9090
    targetPort: 9090
  type: LoadBalancer
```

#### Deploy to Kubernetes
```bash
# Apply configurations
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -l app=socket-gateway

# View logs
kubectl logs -l app=socket-gateway -f

# Scale deployment
kubectl scale deployment socket-gateway --replicas=5
```

### 3. Cloud Deployment (AWS)

#### ECS Task Definition
```json
{
  "family": "socket-gateway",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "socket-gateway",
      "image": "your-account.dkr.ecr.region.amazonaws.com/socket-gateway:latest",
      "portMappings": [
        {
          "containerPort": 3001,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "REDIS_HOST",
          "value": "your-elasticache-endpoint"
        }
      ],
      "secrets": [
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:socket-gateway/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/socket-gateway",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## Monitoring and Logging

### 1. Health Checks

The service provides health check endpoints:

- **Health**: `GET /health` - Basic service health
- **Ready**: `GET /ready` - Service readiness (includes dependencies)
- **Metrics**: `GET /metrics` - Prometheus metrics

### 2. Logging Configuration

Logs are structured JSON format with different levels:

```javascript
// Example log entry
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "info",
  "message": "Socket connection established",
  "socketId": "abc123",
  "userId": "user456",
  "metadata": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "*************"
  }
}
```

### 3. Metrics Collection

Key metrics exposed:

- **Connection Metrics**: Active connections, connection rate
- **Message Metrics**: Message rate, message types
- **Performance Metrics**: Response times, error rates
- **Business Metrics**: Room joins, game events

### 4. Alerting

Set up alerts for:

- High error rates (>5%)
- High response times (>1000ms)
- Connection failures
- Redis connectivity issues
- Memory/CPU usage thresholds

## Troubleshooting

### Common Issues

#### 1. Connection Issues
```bash
# Check if service is running
curl http://localhost:3001/health

# Check Redis connectivity
redis-cli -h localhost -p 6379 ping

# Check logs for errors
docker logs socket-gateway
```

#### 2. Authentication Issues
```bash
# Verify JWT secret is set
echo $JWT_SECRET

# Test token validation
curl -H "Authorization: Bearer your-token" http://localhost:3001/auth/verify
```

#### 3. Performance Issues
```bash
# Check metrics
curl http://localhost:3001/metrics

# Monitor resource usage
docker stats socket-gateway

# Check Redis performance
redis-cli --latency -h localhost -p 6379
```

### Debug Mode

Enable debug logging:

```bash
# Set debug environment
export LOG_LEVEL=debug
export DEBUG=socket.io:*

# Start service
npm start
```
