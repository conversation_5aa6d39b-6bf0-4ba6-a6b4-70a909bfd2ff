#!/bin/bash

# Socket Gateway Startup Script
# This script starts the Socket Gateway with proper configuration and health checks

echo "🚀 Starting Socket Gateway Service..."

# Set working directory
cd "$(dirname "$0")"

# Load environment variables
if [ -f ".env" ]; then
    echo "📋 Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  No .env file found, using default configuration"
fi

# Display configuration
echo ""
echo "📋 Socket Gateway Configuration:"
echo "   Environment: ${NODE_ENV:-development}"
echo "   Port: ${PORT:-3000}"
echo "   Redis URL: ${REDIS_URL:-redis://localhost:6379}"
echo "   MongoDB URL: ${MONGODB_URL:-mongodb://localhost:27017/xzgame}"
echo "   Game Service URL: ${GAME_SERVICE_URL:-http://localhost:9090}"
echo "   Log Level: ${LOG_LEVEL:-info}"

# Check dependencies
echo ""
echo "🔍 Checking dependencies..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

echo "✅ Node.js: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi

echo "✅ npm: $(npm --version)"

# Check if Redis is accessible
echo ""
echo "🔍 Checking Redis connectivity..."
if redis-cli -u "${REDIS_URL:-redis://localhost:6379}" ping > /dev/null 2>&1; then
    echo "✅ Redis: Connected"
else
    echo "❌ Redis: Not accessible"
    echo "Please ensure Redis is running with the correct configuration"
    exit 1
fi

# Check if MongoDB is accessible
echo ""
echo "🔍 Checking MongoDB connectivity..."
MONGO_URL="${MONGODB_URL:-mongodb://localhost:27017/xzgame}"
if mongosh "$MONGO_URL" --eval "db.runCommand('ping')" > /dev/null 2>&1; then
    echo "✅ MongoDB: Connected"
else
    echo "⚠️  MongoDB: Not accessible (will retry on startup)"
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 Installing dependencies..."
    npm install
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start the service
echo ""
echo "🚀 Starting Socket Gateway service..."
echo "   Process will start in the background"
echo "   Logs will be written to logs/socket-gateway.log"
echo "   Use 'npm run dev' for development mode with auto-restart"

# Check if we should start in development mode
if [ "${NODE_ENV}" = "development" ]; then
    echo ""
    echo "🔧 Development mode detected"
    echo "   Starting with nodemon for auto-restart"
    echo "   Press Ctrl+C to stop the service"
    echo ""
    
    # Start with nodemon for development
    npm run dev
else
    echo ""
    echo "🏭 Production mode detected"
    echo "   Starting with node"
    echo ""
    
    # Start with node for production
    npm start
fi
