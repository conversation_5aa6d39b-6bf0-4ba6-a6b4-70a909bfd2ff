# Room Subscription Flow Documentation

## Overview
This document describes how `room_info_updated` events are broadcasted when players subscribe/unsubscribe from rooms in the socket-gateway service.

## Current Implementation Status: ✅ FULLY IMPLEMENTED

### 1. Player Subscribes to Room

**Flow:**
1. Player calls `subscribe_room` event
2. Socket-gateway processes subscription via `handleSubscribeRoom()`
3. **Player receives `room_info_updated`** via `sendCurrentRoomInfo()`
4. **Other subscribers receive `room_info_updated`** via `broadcastRoomInfoToOtherSubscribers()`

**Code Location:** `socketService.js` lines 1303-1329

```javascript
process.nextTick(async () => {
  // Send room_info_updated to the subscribing player
  await this.sendCurrentRoomInfo(socket, roomId);
  
  // Broadcast room_info_updated to OTHER subscribers
  await this.broadcastRoomInfoToOtherSubscribers(socket, roomId, 'player_subscribed');
});
```

### 2. Player Unsubscribes from Room

**Flow:**
1. Player calls `unsubscribe_room` event
2. Socket-gateway processes unsubscription via `handleUnsubscribeRoom()`
3. **Remaining subscribers receive `room_info_updated`** via `broadcastRoomInfoToAllSubscribers()`

**Code Location:** `socketService.js` lines 1383-1394

```javascript
process.nextTick(async () => {
  // Broadcast room_info_updated to ALL remaining subscribers
  await this.broadcastRoomInfoToAllSubscribers(roomId, 'player_unsubscribed');
});
```

## Special Handling for Prize Wheel Games

### Enhanced Color State Broadcasting
For Prize Wheel games, the system uses enhanced broadcasting that includes color state information:

1. **Regular games**: Direct `room_info_updated` emission
2. **Prize Wheel games**: Request Game Service to broadcast with color state via Redis

**Code Location:** `socketService.js` lines 1707-1718, 1783-1794

```javascript
// For Prize Wheel games
if (roomInfo.room.gameType === 'prizewheel' || roomInfo.room.gameType === 'prize_wheel') {
  await this.requestGameServiceRoomInfoBroadcast(roomId, socket.userId, socket.username, reason);
  return;
}
```

## Event Details

### `room_info_updated` Event Structure
```javascript
{
  room: {
    id: "room-id",
    name: "Room Name",
    gameType: "prizewheel",
    status: "waiting",
    playerCount: 2,
    maxPlayers: 8,
    betAmount: 100,
    prizePool: 200
  },
  roomState: {
    playerCount: 2,
    readyCount: 1,
    canStartGame: false,
    prizePool: 200,
    gameInProgress: false
  },
  players: [
    {
      userId: "user1",
      username: "player1",
      isReady: true,
      betAmount: 100,
      position: 1,
      colorId: "red",      // Prize Wheel specific
      colorName: "Red",    // Prize Wheel specific
      colorHex: "#FF0000"  // Prize Wheel specific
    }
  ],
  gameConfig: {
    gameType: "prizewheel",
    minPlayers: 2,
    maxPlayers: 8,
    betAmount: 100,
    settings: { /* game-specific settings */ }
  },
  gameSpecificData: {
    // Game-specific data (colors, positions, etc.)
  },
  timestamp: "2025-06-11T18:00:00.000Z",
  reason: "player_subscribed" // or "player_unsubscribed"
}
```

## Broadcast Reasons

The system includes a `reason` field to help clients understand why they received the update:

- `"player_subscribed"` - A new player subscribed to the room
- `"player_unsubscribed"` - A player unsubscribed from the room
- `"subscription_change"` - General subscription change
- `"room_change"` - General room state change

## Error Handling

### Fallback Mechanisms
1. **Game Service unavailable**: Falls back to basic room info
2. **Room not found**: Sends fallback room structure
3. **Redis errors**: Logs errors but continues operation

### Retry Logic
- Uses `process.nextTick()` to ensure proper timing
- Invalidates cache before fetching fresh data
- Handles Prize Wheel games with special Game Service requests

## Testing the Flow

### Manual Testing Steps
1. **Subscribe Test**:
   - Connect two clients to the same room
   - Subscribe the first client → should receive `room_info_updated`
   - Subscribe the second client → both should receive `room_info_updated`

2. **Unsubscribe Test**:
   - Have two clients subscribed to a room
   - Unsubscribe one client → remaining client should receive `room_info_updated`

### Expected Behavior
✅ **Subscribing player**: Receives current room state immediately
✅ **Other subscribers**: Notified of new subscription with updated room state
✅ **Remaining subscribers**: Notified when someone unsubscribes with updated room state
✅ **Prize Wheel games**: Enhanced broadcasting with color state information

## Performance Optimizations

1. **Caching**: Recent room info is cached for 5 seconds to reduce Game Service calls
2. **Async Processing**: Uses `process.nextTick()` to prevent blocking
3. **Targeted Broadcasting**: Only sends to relevant subscribers
4. **Error Isolation**: Errors in broadcasting don't affect subscription success

## Conclusion

The room subscription/unsubscription flow is **fully implemented and working correctly**. The system ensures that:

- ✅ Players receive `room_info_updated` when they subscribe
- ✅ Other subscribers are notified when someone subscribes/unsubscribes
- ✅ Prize Wheel games get enhanced color state information
- ✅ Error handling and fallbacks are in place
- ✅ Performance is optimized with caching and async processing

No additional changes are needed for the basic subscription flow functionality.
