# Unified Room Info Event Structure - Correct Architecture

## Problem Solved ✅

**Before**: Two different event structures that were difficult for clients to handle:

### Subscription Event (Old Format)
```javascript
{
  "type": "room_info_updated",
  "data": { /* basic room data */ },
  "reason": "subscription",
  "timestamp": "2025-06-16T04:45:27.591Z"
}
```

### Color Selection Event (Old Format)
```javascript
{
  "reason": "color_selection",
  "roomId": "684f8da83e02d7af17d57846",
  "roomInfo": { /* comprehensive data */ },
  "timestamp": "0001-01-01T00:00:00Z"
}
```

## Solution: Unified Structure with Proper Service Boundaries ✅

**After**: Both events now use the same consistent structure, built by the appropriate services:

### Service Responsibilities
- **Room Service**: Builds unified structure for subscription/room events
- **Game Service**: Builds unified structure for color/game events
- **Socket Gateway**: Pure gateway - only routes events

**Unified Structure Format**:

```javascript
{
  "reason": "subscription" | "color_selection" | "player_joined" | "player_left",
  "roomId": "684f8da83e02d7af17d57846",
  "timestamp": "2025-06-16T11:45:32+07:00",
  "roomInfo": {
    "room": {
      "id": "684f8da83e02d7af17d57846",
      "name": "dat_test_wheel",
      "game_type": "prizewheel",           // Normalized
      "status": "waiting",                 // Normalized
      "current_players": 1,
      "max_players": 2,
      "min_players": 2,
      "bet_amount": 10,                    // Parsed to number
      "currency": "USD",
      "prize_pool": 0,
      "has_password": false,
      "has_space": true,
      "is_private": false,
      "is_featured": false
    },
    "players": {
      "list": [
        {
          "userId": "684d9c83a533d3e5fea7dc36",
          "username": "res3",
          "position": 1,
          "isReady": false,
          "betAmount": "10",
          "joinedAt": "[object Object]"
          // For color events, also includes:
          // "colorId": "blue",
          // "colorName": "Blue", 
          // "colorHex": "#0000FF"
        }
      ],
      "total": 1,
      "withColors": 0,                     // Updated by game service for color events
      "colorMappings": {}                  // Updated by game service for color events
    },
    "game": {
      "type": "prizewheel",
      "canStart": true,
      "allColorsSelected": false
    }
    // "colors" object is added by game service for color-related events
  }
}
```

## Complete Color Selection Event Structure

When a color is selected, the game service adds the `colors` object:

```javascript
{
  "reason": "color_selection",
  "roomId": "684f8da83e02d7af17d57846", 
  "timestamp": "2025-06-16T11:45:32+07:00",
  "roomInfo": {
    "room": { /* same as above */ },
    "players": {
      "list": [
        {
          "userId": "684d9c83a533d3e5fea7dc36",
          "username": "res3",
          "colorId": "blue",
          "colorName": "Blue",
          "colorHex": "#0000FF"
        }
      ],
      "total": 1,
      "withColors": 1,
      "colorMappings": {
        "684d9c83a533d3e5fea7dc36": {
          "color": {
            "id": "blue",
            "name": "Blue", 
            "hex": "#0000FF"
          },
          "selectedAt": "2025-06-16T11:45:32+07:00",
          "userId": "684d9c83a533d3e5fea7dc36",
          "username": "res3"
        }
      }
    },
    "game": {
      "type": "prizewheel",
      "canStart": false,
      "allColorsSelected": false
    },
    "colors": {
      "available": [
        {"id": "red", "name": "Red", "hex": "#FF0000"},
        {"id": "green", "name": "Green", "hex": "#00FF00"},
        // ... other available colors
      ],
      "taken": [
        {"id": "blue", "name": "Blue", "hex": "#0000FF"}
      ],
      "selected": {
        "684d9c83a533d3e5fea7dc36": "blue"
      },
      "assignments": {
        "684d9c83a533d3e5fea7dc36": {
          "color": {"id": "blue", "name": "Blue", "hex": "#0000FF"},
          "selectedAt": "2025-06-16T11:45:32+07:00",
          "userId": "684d9c83a533d3e5fea7dc36",
          "username": "res3"
        }
      },
      "statistics": {
        "totalColors": 8,
        "availableCount": 7,
        "takenCount": 1,
        "selectionRate": 12.5
      }
    }
  }
}
```

## Architecture Implementation

### Room Service (Subscription Events)
```go
// Room Service builds unified structure for room-related events
func (h *RedisOrchestratorHandler) buildUnifiedRoomInfo(room *models.RoomInfo, reason, roomID string, metadata map[string]interface{}) map[string]interface{} {
    return map[string]interface{}{
        "reason":    reason,
        "roomId":    roomID,
        "timestamp": time.Now().Format(time.RFC3339),
        "roomInfo": map[string]interface{}{
            "room":    normalizedRoomData,
            "players": normalizedPlayersData,
            "game":    basicGameData,
            // No colors object - that's Game Service responsibility
        },
    }
}
```

### Game Service (Color Events)
```go
// Game Service builds unified structure for game-related events
func (h *GameRequestHandler) buildUnifiedRoomInfo(ctx context.Context, roomID, reason string, roomDetails map[string]interface{}, colorState ColorStateData, timestamp string) map[string]interface{} {
    unifiedInfo := map[string]interface{}{
        "reason":    reason,
        "roomId":    roomID,
        "timestamp": timestamp,
        "roomInfo": map[string]interface{}{
            "room":    roomDetails,
            "players": playersWithColorData,
            "game":    gameStateWithColors,
            "colors":  comprehensiveColorState, // Only Game Service adds this
        },
    }
    return unifiedInfo
}
```

### Socket Gateway (Pure Routing)
```javascript
// Socket Gateway only routes - no business logic
async handleRoomInfoUpdated(roomId, payload) {
    // Services provide unified structure - just forward it
    this.socketService.io.to(`room:${roomId}`).emit('room_info_updated', payload);
}
```

## Client Implementation Guide

### Single Event Handler

```javascript
socket.on('room_info_updated', (data) => {
  const { reason, roomId, roomInfo } = data;

  // Update room display
  updateRoomInfo(roomInfo.room);
  updatePlayerList(roomInfo.players.list);
  updateGameState(roomInfo.game);

  // Handle color state (only present for color events from Game Service)
  if (roomInfo.colors) {
    updateAvailableColors(roomInfo.colors.available);
    updatePlayerColors(roomInfo.colors.assignments);
    updateColorStatistics(roomInfo.colors.statistics);
  }

  // Handle specific reasons if needed
  switch (reason) {
    case 'subscription':        // From Room Service
      console.log('Successfully subscribed to room');
      break;
    case 'color_selection':     // From Game Service
      console.log('Color selection updated');
      break;
    case 'player_joined':       // From Room Service
      console.log('New player joined');
      break;
    case 'player_left':         // From Room Service
      console.log('Player left');
      break;
  }
});
```

### Data Access Patterns

```javascript
// Room information
const roomData = data.roomInfo.room;
const playerCount = roomData.current_players;
const maxPlayers = roomData.max_players;
const canJoin = roomData.has_space;

// Player information  
const players = data.roomInfo.players.list;
const totalPlayers = data.roomInfo.players.total;
const playersWithColors = data.roomInfo.players.withColors;

// Game state
const gameType = data.roomInfo.game.type;
const canStart = data.roomInfo.game.canStart;

// Color state (only for color events)
if (data.roomInfo.colors) {
  const availableColors = data.roomInfo.colors.available;
  const playerColors = data.roomInfo.colors.assignments;
  const colorStats = data.roomInfo.colors.statistics;
}
```

## Benefits for Clients

1. **Single Event Handler**: Only need to listen to `room_info_updated`
2. **Consistent Structure**: Same format for all room updates
3. **Complete Data**: All necessary information in one event
4. **Easy Access**: Predictable data paths
5. **Backward Compatible**: Handles both old and new event formats
6. **Type Safety**: Consistent data types and structure

## Data Normalization

- **Game Types**: `GAME_TYPE_PRIZE_WHEEL` → `prizewheel`
- **Room Status**: `ROOM_STATUS_WAITING` → `waiting`  
- **Bet Amounts**: String `"10"` → Number `10`
- **Timestamps**: Consistent ISO format
- **Boolean Values**: Consistent true/false values

This unified structure makes it much easier for clients to handle room updates consistently! 🎯
