/**
 * Test script for auto-leave functionality on socket disconnect
 */

const io = require('socket.io-client');

// Test configuration
const SOCKET_URL = 'http://localhost:3001';
const TEST_USER_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODRkOWM4M2E1MzNkM2U1ZmVhN2RjMzYiLCJ1c2VybmFtZSI6InJlczMiLCJyb2xlIjoidXNlciIsImlhdCI6MTczNDIwNzE0OCwiZXhwIjoxNzM0MjkzNTQ4fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with actual token
const TEST_ROOM_ID = '684d9baba533d3e5fea7dc35';

async function testAutoLeave() {
  console.log('🧪 Testing Auto-Leave Functionality on Socket Disconnect');
  console.log('=' * 60);
  
  // Create socket connection
  const socket = io(SOCKET_URL, {
    auth: {
      token: TEST_USER_TOKEN
    },
    transports: ['websocket']
  });

  // Set up event listeners
  socket.on('connect', () => {
    console.log('✅ Socket connected:', socket.id);
  });

  socket.on('connect_ack', (data) => {
    console.log('✅ Connection acknowledged:', data);
    
    // Step 1: Subscribe to room
    console.log('\n📡 Step 1: Subscribing to room...');
    socket.emit('subscribe_room', { roomId: TEST_ROOM_ID }, (response) => {
      console.log('📡 Subscribe response:', response);
      
      if (response && response.success) {
        // Step 2: Join room
        console.log('\n🚪 Step 2: Joining room...');
        socket.emit('join_room', { roomId: TEST_ROOM_ID }, (response) => {
          console.log('🚪 Join response:', response);
          
          if (response && response.success) {
            console.log('✅ Successfully joined room!');
            
            // Step 3: Wait a bit, then disconnect to test auto-leave
            console.log('\n⏰ Step 3: Waiting 3 seconds before disconnect...');
            setTimeout(() => {
              console.log('\n🔌 Step 4: Disconnecting socket to test auto-leave...');
              socket.disconnect();
              
              // Give some time for auto-leave to process
              setTimeout(() => {
                console.log('\n✅ Test completed! Check the logs to see if auto-leave was triggered.');
                process.exit(0);
              }, 2000);
            }, 3000);
          } else {
            console.error('❌ Failed to join room:', response);
            socket.disconnect();
            process.exit(1);
          }
        });
      } else {
        console.error('❌ Failed to subscribe to room:', response);
        socket.disconnect();
        process.exit(1);
      }
    });
  });

  socket.on('room_info_updated', (data) => {
    console.log('📢 Room info updated:', data);
  });

  socket.on('disconnect', (reason) => {
    console.log('🔌 Socket disconnected:', reason);
  });

  socket.on('error', (error) => {
    console.error('❌ Socket error:', error);
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted, disconnecting...');
    socket.disconnect();
    process.exit(0);
  });
}

// Run the test
testAutoLeave().catch(console.error);
