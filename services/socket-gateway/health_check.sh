#!/bin/bash

# Socket Gateway Health Check Script
# This script performs comprehensive health checks on the Socket Gateway service

echo "🏥 Socket Gateway Health Check..."

# Configuration
SOCKET_GATEWAY_URL="${SOCKET_GATEWAY_URL:-http://localhost:3000}"
REDIS_URL="${REDIS_URL:-redis://localhost:6379}"
MONGODB_URL="${MONGODB_URL:-*******************************************************************}"
GAME_SERVICE_URL="${GAME_SERVICE_URL:-http://localhost:9090}"

echo ""
echo "📋 Health Check Configuration:"
echo "   Socket Gateway: $SOCKET_GATEWAY_URL"
echo "   Redis: $REDIS_URL"
echo "   MongoDB: $MONGODB_URL"
echo "   Game Service: $GAME_SERVICE_URL"

# Function to check HTTP endpoint
check_http_endpoint() {
    local name="$1"
    local url="$2"
    local timeout="${3:-5}"
    
    echo -n "🔍 Checking $name... "
    
    if curl -s --max-time "$timeout" "$url" > /dev/null 2>&1; then
        echo "✅ Healthy"
        return 0
    else
        echo "❌ Unhealthy"
        return 1
    fi
}

# Function to check Redis
check_redis() {
    echo -n "🔍 Checking Redis... "
    
    if redis-cli -u "$REDIS_URL" ping > /dev/null 2>&1; then
        echo "✅ Connected"
        return 0
    else
        echo "❌ Not accessible"
        return 1
    fi
}

# Function to check MongoDB
check_mongodb() {
    echo -n "🔍 Checking MongoDB... "
    
    if mongosh "$MONGODB_URL" --eval "db.runCommand('ping')" > /dev/null 2>&1; then
        echo "✅ Connected"
        return 0
    else
        echo "❌ Not accessible"
        return 1
    fi
}

# Function to check Socket Gateway process
check_process() {
    echo -n "🔍 Checking Socket Gateway process... "
    
    if pgrep -f "node.*socket-gateway" > /dev/null; then
        echo "✅ Running"
        return 0
    else
        echo "❌ Not running"
        return 1
    fi
}

# Function to get detailed health info
get_detailed_health() {
    echo ""
    echo "📊 Detailed Health Information:"
    
    # Socket Gateway health
    echo "🔍 Socket Gateway Health:"
    if curl -s --max-time 5 "$SOCKET_GATEWAY_URL/health" | jq . 2>/dev/null; then
        echo "✅ Health endpoint accessible"
    else
        echo "❌ Health endpoint not accessible"
    fi
    
    # Game Service health
    echo ""
    echo "🔍 Game Service Health:"
    if curl -s --max-time 5 "$GAME_SERVICE_URL/health" | jq . 2>/dev/null; then
        echo "✅ Game Service accessible"
    else
        echo "❌ Game Service not accessible"
    fi
    
    # Redis info
    echo ""
    echo "🔍 Redis Information:"
    if redis-cli -u "$REDIS_URL" info server 2>/dev/null | grep "redis_version"; then
        echo "✅ Redis server info accessible"
    else
        echo "❌ Redis server info not accessible"
    fi
    
    # Process information
    echo ""
    echo "🔍 Process Information:"
    if pgrep -f "node.*socket-gateway" > /dev/null; then
        echo "Process ID: $(pgrep -f "node.*socket-gateway")"
        echo "Memory usage: $(ps -o pid,rss,vsz,comm -p $(pgrep -f "node.*socket-gateway") | tail -n +2)"
    else
        echo "❌ Socket Gateway process not found"
    fi
}

# Run health checks
echo ""
echo "🏥 Running Health Checks..."

# Initialize counters
total_checks=0
passed_checks=0

# Check Socket Gateway process
((total_checks++))
if check_process; then
    ((passed_checks++))
fi

# Check Socket Gateway HTTP endpoint
((total_checks++))
if check_http_endpoint "Socket Gateway HTTP" "$SOCKET_GATEWAY_URL/health"; then
    ((passed_checks++))
fi

# Check Redis
((total_checks++))
if check_redis; then
    ((passed_checks++))
fi

# Check MongoDB
((total_checks++))
if check_mongodb; then
    ((passed_checks++))
fi

# Check Game Service
((total_checks++))
if check_http_endpoint "Game Service" "$GAME_SERVICE_URL/health"; then
    ((passed_checks++))
fi

# Display summary
echo ""
echo "📊 Health Check Summary:"
echo "   Total Checks: $total_checks"
echo "   Passed: $passed_checks"
echo "   Failed: $((total_checks - passed_checks))"

if [ "$passed_checks" -eq "$total_checks" ]; then
    echo "🎉 All health checks passed!"
    health_status="healthy"
else
    echo "⚠️  Some health checks failed"
    health_status="unhealthy"
fi

# Get detailed information if requested
if [ "$1" = "--detailed" ] || [ "$1" = "-d" ]; then
    get_detailed_health
fi

# Exit with appropriate code
if [ "$health_status" = "healthy" ]; then
    exit 0
else
    exit 1
fi
