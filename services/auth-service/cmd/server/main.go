package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	"github.com/xzgame/auth-service/internal/config"
	"github.com/xzgame/auth-service/internal/services"
	"github.com/xzgame/auth-service/pkg/redis"
	pb "github.com/xzgame/auth-service/proto"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	logger.WithFields(logrus.Fields{
		"port":        cfg.Port,
		"environment": cfg.Environment,
		"log_level":   cfg.LogLevel,
	}).Info("Starting Auth Service")

	// Initialize Redis client
	redisClient, err := redis.NewRedisClient(cfg.RedisURL, cfg.RedisPassword, cfg.RedisDB)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to Redis")
	}
	defer redisClient.Close()

	// Test Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := redisClient.Ping(ctx); err != nil {
		logger.WithError(err).Fatal("Failed to ping Redis")
	}
	logger.Info("Successfully connected to Redis")

	// Initialize auth service
	authService := services.NewAuthService(
		redisClient,
		logger,
		cfg.JWTSecret,
		cfg.JWTIssuer,
		cfg.JWTAudience,
	)

	// Initialize Redis request handler
	redisHandler := services.NewRedisRequestHandler(redisClient, authService, logger)

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Create gRPC service wrapper
	grpcService := services.NewGRPCServer(authService)

	// Register auth service
	pb.RegisterAuthServiceServer(grpcServer, grpcService)

	// Register health check service
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(grpcServer, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// Enable reflection for development
	if cfg.Environment == "development" {
		reflection.Register(grpcServer)
	}

	// Start gRPC server
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		logger.WithError(err).Fatal("Failed to create listener")
	}

	// Start Redis request handler in goroutine
	redisCtx, redisCancel := context.WithCancel(context.Background())
	defer redisCancel()
	go func() {
		if err := redisHandler.Start(redisCtx); err != nil && err != context.Canceled {
			logger.WithError(err).Error("Redis request handler failed")
		}
	}()

	// Start server in goroutine
	go func() {
		logger.WithField("address", listener.Addr().String()).Info("Auth Service gRPC server starting")
		if err := grpcServer.Serve(listener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC server")
		}
	}()

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("Shutting down Auth Service...")

	// Graceful shutdown
	grpcServer.GracefulStop()

	logger.Info("Auth Service shutdown complete")
}
