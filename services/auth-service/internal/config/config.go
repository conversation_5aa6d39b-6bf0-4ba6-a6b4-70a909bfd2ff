package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds the configuration for the auth service
type Config struct {
	// Server configuration
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`

	// Redis configuration
	RedisURL      string        `json:"redis_url"`
	RedisPassword string        `json:"redis_password"`
	RedisDB       int           `json:"redis_db"`
	RedisTTL      time.Duration `json:"redis_ttl"`

	// JWT configuration
	JWTSecret   string   `json:"jwt_secret"`
	JWTIssuer   string   `json:"jwt_issuer"`
	JWTAudience []string `json:"jwt_audience"`

	// Session configuration
	SessionTTL             time.Duration `json:"session_ttl"`
	SessionCleanupInterval time.Duration `json:"session_cleanup_interval"`

	// Security configuration
	TokenBlacklistTTL  time.Duration `json:"token_blacklist_ttl"`
	MaxSessionsPerUser int           `json:"max_sessions_per_user"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	config := &Config{
		// Default values
		Port:        8081,
		Environment: "development",
		LogLevel:    "info",

		RedisURL:      "redis://localhost:6379",
		RedisPassword: "",
		RedisDB:       0,
		RedisTTL:      24 * time.Hour,

		JWTSecret:   "your-secret-key",
		JWTIssuer:   "xzgame-auth-service",
		JWTAudience: []string{"xzgame-api", "xzgame-game-service"},

		SessionTTL:             24 * time.Hour,
		SessionCleanupInterval: 1 * time.Hour,
		TokenBlacklistTTL:      24 * time.Hour,
		MaxSessionsPerUser:     5,
	}

	// Load from environment variables
	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		config.RedisURL = redisURL
	}

	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		config.RedisPassword = redisPassword
	}

	if redisDB := os.Getenv("REDIS_DB"); redisDB != "" {
		if db, err := strconv.Atoi(redisDB); err == nil {
			config.RedisDB = db
		}
	}

	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.JWTSecret = jwtSecret
	}

	if jwtIssuer := os.Getenv("JWT_ISSUER"); jwtIssuer != "" {
		config.JWTIssuer = jwtIssuer
	}

	if jwtAudience := os.Getenv("JWT_AUDIENCE"); jwtAudience != "" {
		config.JWTAudience = strings.Split(jwtAudience, ",")
	}

	if sessionTTL := os.Getenv("SESSION_TTL"); sessionTTL != "" {
		if ttl, err := time.ParseDuration(sessionTTL); err == nil {
			config.SessionTTL = ttl
		}
	}

	if tokenBlacklistTTL := os.Getenv("TOKEN_BLACKLIST_TTL"); tokenBlacklistTTL != "" {
		if ttl, err := time.ParseDuration(tokenBlacklistTTL); err == nil {
			config.TokenBlacklistTTL = ttl
		}
	}

	if maxSessions := os.Getenv("MAX_SESSIONS_PER_USER"); maxSessions != "" {
		if max, err := strconv.Atoi(maxSessions); err == nil {
			config.MaxSessionsPerUser = max
		}
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}

	if c.JWTSecret == "" {
		return fmt.Errorf("JWT secret is required")
	}

	if len(c.JWTSecret) < 32 {
		return fmt.Errorf("JWT secret must be at least 32 characters")
	}

	if c.JWTIssuer == "" {
		return fmt.Errorf("JWT issuer is required")
	}

	if len(c.JWTAudience) == 0 {
		return fmt.Errorf("JWT audience is required")
	}

	if c.SessionTTL <= 0 {
		return fmt.Errorf("session TTL must be positive")
	}

	if c.MaxSessionsPerUser <= 0 {
		return fmt.Errorf("max sessions per user must be positive")
	}

	return nil
}
