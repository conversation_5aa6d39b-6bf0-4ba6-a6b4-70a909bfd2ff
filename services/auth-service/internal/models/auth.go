package models

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TokenClaims represents the JWT token claims
type TokenClaims struct {
	UserID    string   `json:"sub"`
	Username  string   `json:"username"`
	Role      string   `json:"role"`
	SessionID string   `json:"sessionId"`
	DeviceID  string   `json:"device_id,omitempty"`
	JTI       string   `json:"jti,omitempty"`
	Roles     []string `json:"roles,omitempty"`
	jwt.RegisteredClaims
}

// UserSession represents an active user session
type UserSession struct {
	UserID    string    `json:"userId" redis:"user_id"`
	Username  string    `json:"username" redis:"username"`
	Role      string    `json:"role" redis:"role"`
	SessionID string    `json:"sessionId" redis:"session_id"`
	DeviceID  string    `json:"deviceId" redis:"device_id"`
	TokenHash string    `json:"tokenHash" redis:"token_hash"`
	CreatedAt time.Time `json:"createdAt" redis:"created_at"`
	ExpiresAt time.Time `json:"expiresAt" redis:"expires_at"`
	IsActive  bool      `json:"isActive" redis:"is_active"`
	LastSeen  time.Time `json:"lastSeen" redis:"last_seen"`
}

// SessionInfo represents session information for API responses
type SessionInfo struct {
	SessionID string    `json:"sessionId"`
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	Role      string    `json:"role"`
	DeviceID  string    `json:"deviceId,omitempty"`
	CreatedAt time.Time `json:"createdAt"`
	ExpiresAt time.Time `json:"expiresAt"`
	LastSeen  time.Time `json:"lastSeen"`
}

// TokenValidationResult represents the result of token validation
type TokenValidationResult struct {
	Valid     bool         `json:"valid"`
	Claims    *TokenClaims `json:"claims,omitempty"`
	Session   *SessionInfo `json:"session,omitempty"`
	Error     string       `json:"error,omitempty"`
	ErrorCode string       `json:"errorCode,omitempty"`
}

// CreateSessionRequest represents a request to create a new session
type CreateSessionRequest struct {
	UserID   string `json:"userId" validate:"required"`
	Username string `json:"username" validate:"required"`
	Role     string `json:"role" validate:"required"`
	DeviceID string `json:"deviceId,omitempty"`
}

// CreateSessionResponse represents the response from creating a session
type CreateSessionResponse struct {
	Session *SessionInfo `json:"session"`
	Token   string       `json:"token"`
}

// InvalidateSessionRequest represents a request to invalidate a session
type InvalidateSessionRequest struct {
	UserID    string `json:"userId" validate:"required"`
	SessionID string `json:"sessionId" validate:"required"`
}

// RefreshTokenRequest represents a request to refresh a token
type RefreshTokenRequest struct {
	Token          string        `json:"token" validate:"required"`
	ExtendDuration time.Duration `json:"extendDuration" validate:"required"`
}

// RefreshTokenResponse represents the response from refreshing a token
type RefreshTokenResponse struct {
	Token        string       `json:"token"`
	TokenHash    string       `json:"tokenHash"`
	Claims       *TokenClaims `json:"claims"`
	ExpiresAt    time.Time    `json:"expiresAt"`
	OldTokenHash string       `json:"oldTokenHash"`
}

// GetUserSessionsRequest represents a request to get user sessions
type GetUserSessionsRequest struct {
	UserID string `json:"userId" validate:"required"`
}

// GetUserSessionsResponse represents the response from getting user sessions
type GetUserSessionsResponse struct {
	Sessions []*SessionInfo `json:"sessions"`
}

// AuthError represents authentication-related errors
type AuthError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AuthError) Error() string {
	return e.Message
}

// Common error codes
const (
	ErrorCodeInvalidToken     = "INVALID_TOKEN"
	ErrorCodeExpiredToken     = "EXPIRED_TOKEN"
	ErrorCodeBlacklistedToken = "BLACKLISTED_TOKEN"
	ErrorCodeInvalidSession   = "INVALID_SESSION"
	ErrorCodeSessionExpired   = "SESSION_EXPIRED"
	ErrorCodeInvalidClaims    = "INVALID_CLAIMS"
	ErrorCodeInvalidIssuer    = "INVALID_ISSUER"
	ErrorCodeInvalidAudience  = "INVALID_AUDIENCE"
	ErrorCodeSessionNotFound  = "SESSION_NOT_FOUND"
	ErrorCodeTooManySessions  = "TOO_MANY_SESSIONS"
	ErrorCodeInternalError    = "INTERNAL_ERROR"
)

// NewAuthError creates a new authentication error
func NewAuthError(code, message, details string) *AuthError {
	return &AuthError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// ToSessionInfo converts a UserSession to SessionInfo
func (s *UserSession) ToSessionInfo() *SessionInfo {
	return &SessionInfo{
		SessionID: s.SessionID,
		UserID:    s.UserID,
		Username:  s.Username,
		Role:      s.Role,
		DeviceID:  s.DeviceID,
		CreatedAt: s.CreatedAt,
		ExpiresAt: s.ExpiresAt,
		LastSeen:  s.LastSeen,
	}
}

// IsExpired checks if the session is expired
func (s *UserSession) IsExpired() bool {
	return s.ExpiresAt.Before(time.Now())
}

// IsValid checks if the session is valid (active and not expired)
func (s *UserSession) IsValid() bool {
	return s.IsActive && !s.IsExpired()
}

// Additional models for modular auth service

// CreateTokenRequest represents a request to create a new token
type CreateTokenRequest struct {
	UserID    string    `json:"userId" validate:"required"`
	Username  string    `json:"username" validate:"required"`
	Role      string    `json:"role" validate:"required"`
	SessionID string    `json:"sessionId" validate:"required"`
	DeviceID  string    `json:"deviceId,omitempty"`
	Roles     []string  `json:"roles,omitempty"`
	ExpiresAt time.Time `json:"expiresAt" validate:"required"`
}

// CreateTokenResponse represents the response from creating a token
type CreateTokenResponse struct {
	Token     string       `json:"token"`
	TokenHash string       `json:"tokenHash"`
	Claims    *TokenClaims `json:"claims"`
	ExpiresAt time.Time    `json:"expiresAt"`
}

// UserValidationRequest represents a request to validate user information
type UserValidationRequest struct {
	UserID   string `json:"userId" validate:"required"`
	Username string `json:"username" validate:"required"`
	Role     string `json:"role" validate:"required"`
	DeviceID string `json:"deviceId,omitempty"`
}

// UserValidationResponse represents the response from user validation
type UserValidationResponse struct {
	Valid    bool            `json:"valid"`
	UserID   string          `json:"userId"`
	Username string          `json:"username"`
	Checks   map[string]bool `json:"checks"`
	Issues   []string        `json:"issues"`
}

// BlacklistTokenRequest represents a request to blacklist a token
type BlacklistTokenRequest struct {
	JTI       string    `json:"jti" validate:"required"`
	UserID    string    `json:"userId" validate:"required"`
	TokenHash string    `json:"tokenHash,omitempty"`
	Reason    string    `json:"reason" validate:"required"`
	ExpiresAt time.Time `json:"expiresAt" validate:"required"`
}

// BlacklistEntry represents a blacklisted token entry
type BlacklistEntry struct {
	JTI           string    `json:"jti"`
	UserID        string    `json:"userId"`
	TokenHash     string    `json:"tokenHash"`
	Reason        string    `json:"reason"`
	BlacklistedAt time.Time `json:"blacklistedAt"`
	ExpiresAt     time.Time `json:"expiresAt"`
}

// SecurityEvent represents a security-related event
type SecurityEvent struct {
	EventType   string                 `json:"eventType"`
	UserID      string                 `json:"userId"`
	IPAddress   string                 `json:"ipAddress"`
	UserAgent   string                 `json:"userAgent"`
	Severity    string                 `json:"severity"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Additional models for modular components

// PermissionCheckRequest represents a request to check permissions
type PermissionCheckRequest struct {
	UserID   string `json:"userId" validate:"required"`
	Role     string `json:"role" validate:"required"`
	Action   string `json:"action" validate:"required"`
	Resource string `json:"resource" validate:"required"`
}

// PermissionCheckResponse represents the response from permission check
type PermissionCheckResponse struct {
	Allowed     bool     `json:"allowed"`
	UserID      string   `json:"userId"`
	Role        string   `json:"role"`
	Action      string   `json:"action"`
	Resource    string   `json:"resource"`
	Permissions []string `json:"permissions"`
	Reason      string   `json:"reason,omitempty"`
}

// RoomAccessRequest represents a request to check room access
type RoomAccessRequest struct {
	UserID           string `json:"userId" validate:"required"`
	Role             string `json:"role" validate:"required"`
	RoomID           string `json:"roomId" validate:"required"`
	Action           string `json:"action" validate:"required"`
	RoomStatus       string `json:"roomStatus"`
	CurrentPlayers   int    `json:"currentPlayers"`
	MaxPlayers       int    `json:"maxPlayers"`
	IsPrivate        bool   `json:"isPrivate"`
	Password         string `json:"password,omitempty"`
	ProvidedPassword string `json:"providedPassword,omitempty"`
	BetAmount        int64  `json:"betAmount"`
	MinBet           int64  `json:"minBet"`
	MaxBet           int64  `json:"maxBet"`
	RoomOwnerID      string `json:"roomOwnerId"`
}

// RoomAccessResponse represents the response from room access check
type RoomAccessResponse struct {
	Allowed    bool     `json:"allowed"`
	UserID     string   `json:"userId"`
	RoomID     string   `json:"roomId"`
	Action     string   `json:"action"`
	Conditions []string `json:"conditions"`
	Reason     string   `json:"reason,omitempty"`
}

// GameAccessRequest represents a request to check game access
type GameAccessRequest struct {
	UserID   string `json:"userId" validate:"required"`
	Role     string `json:"role" validate:"required"`
	GameType string `json:"gameType" validate:"required"`
	Action   string `json:"action" validate:"required"`
}

// GameAccessResponse represents the response from game access check
type GameAccessResponse struct {
	Allowed  bool   `json:"allowed"`
	UserID   string `json:"userId"`
	GameType string `json:"gameType"`
	Action   string `json:"action"`
	Reason   string `json:"reason,omitempty"`
}

// BalanceValidationRequest represents a request to validate user balance
type BalanceValidationRequest struct {
	UserID string `json:"userId" validate:"required"`
	Amount int64  `json:"amount" validate:"required"`
	Action string `json:"action" validate:"required"`
}

// BalanceValidationResponse represents the response from balance validation
type BalanceValidationResponse struct {
	Valid  bool   `json:"valid"`
	UserID string `json:"userId"`
	Amount int64  `json:"amount"`
	Action string `json:"action"`
	Reason string `json:"reason,omitempty"`
}

// Stats models for modular components

// TokenManagerStats represents token manager statistics
type TokenManagerStats struct {
	Issuer             string   `json:"issuer"`
	SupportedAudiences []string `json:"supportedAudiences"`
	SigningMethod      string   `json:"signingMethod"`
	Version            string   `json:"version"`
}

// SessionManagerStats represents session manager statistics
type SessionManagerStats struct {
	DefaultSessionDuration time.Duration `json:"defaultSessionDuration"`
	CleanupInterval        time.Duration `json:"cleanupInterval"`
	Version                string        `json:"version"`
}

// SecurityStats represents security service statistics
type SecurityStats struct {
	BlacklistEnabled      bool   `json:"blacklistEnabled"`
	RateLimitEnabled      bool   `json:"rateLimitEnabled"`
	FraudDetectionEnabled bool   `json:"fraudDetectionEnabled"`
	Version               string `json:"version"`
}

// AuthorizationStats represents authorization service statistics
type AuthorizationStats struct {
	SupportedRoles     []string `json:"supportedRoles"`
	SupportedResources []string `json:"supportedResources"`
	SupportedActions   []string `json:"supportedActions"`
	Version            string   `json:"version"`
}

// UserValidatorStats represents user validator statistics
type UserValidatorStats struct {
	SupportedRoles          []string `json:"supportedRoles"`
	ValidationEnabled       bool     `json:"validationEnabled"`
	AccountLockEnabled      bool     `json:"accountLockEnabled"`
	DeviceValidationEnabled bool     `json:"deviceValidationEnabled"`
	Version                 string   `json:"version"`
}

// AuthServiceStats represents comprehensive auth service statistics
type AuthServiceStats struct {
	TokenStats         *TokenManagerStats   `json:"tokenStats"`
	SessionStats       *SessionManagerStats `json:"sessionStats"`
	SecurityStats      *SecurityStats       `json:"securityStats"`
	AuthorizationStats *AuthorizationStats  `json:"authorizationStats"`
	ValidatorStats     *UserValidatorStats  `json:"validatorStats"`
	Version            string               `json:"version"`
}

// Additional models for user validation

// AccountStatusResponse represents account status check response
type AccountStatusResponse struct {
	UserID           string                 `json:"userId"`
	IsActive         bool                   `json:"isActive"`
	IsLocked         bool                   `json:"isLocked"`
	IsSuspended      bool                   `json:"isSuspended"`
	IsHealthy        bool                   `json:"isHealthy"`
	LockReason       string                 `json:"lockReason,omitempty"`
	LockExpiry       *time.Time             `json:"lockExpiry,omitempty"`
	SuspensionReason string                 `json:"suspensionReason,omitempty"`
	SuspensionExpiry *time.Time             `json:"suspensionExpiry,omitempty"`
	Checks           map[string]interface{} `json:"checks"`
}

// LockAccountRequest represents a request to lock an account
type LockAccountRequest struct {
	UserID   string        `json:"userId" validate:"required"`
	Reason   string        `json:"reason" validate:"required"`
	Duration time.Duration `json:"duration" validate:"required"`
	LockedBy string        `json:"lockedBy" validate:"required"`
}

// AccountLockInfo represents account lock information
type AccountLockInfo struct {
	UserID    string    `json:"userId"`
	Reason    string    `json:"reason"`
	LockedAt  time.Time `json:"lockedAt"`
	ExpiresAt time.Time `json:"expiresAt"`
	LockedBy  string    `json:"lockedBy"`
}

// DeviceValidationRequest represents a request to validate device information
type DeviceValidationRequest struct {
	UserID   string `json:"userId" validate:"required"`
	DeviceID string `json:"deviceId" validate:"required"`
}

// DeviceValidationResponse represents the response from device validation
type DeviceValidationResponse struct {
	Valid     bool            `json:"valid"`
	DeviceID  string          `json:"deviceId"`
	IsTrusted bool            `json:"isTrusted"`
	Checks    map[string]bool `json:"checks"`
	Issues    []string        `json:"issues"`
}

// Additional models for security service

// SuspiciousActivityReport represents a suspicious activity report
type SuspiciousActivityReport struct {
	UserID       string        `json:"userId"`
	TimeWindow   time.Duration `json:"timeWindow"`
	CheckedAt    time.Time     `json:"checkedAt"`
	IsSuspicious bool          `json:"isSuspicious"`
	Indicators   []string      `json:"indicators"`
}

// RateLimitRequest represents a rate limit check request
type RateLimitRequest struct {
	UserID    string        `json:"userId" validate:"required"`
	Action    string        `json:"action" validate:"required"`
	Limit     int           `json:"limit" validate:"required"`
	Window    time.Duration `json:"window" validate:"required"`
	IPAddress string        `json:"ipAddress,omitempty"`
	UserAgent string        `json:"userAgent,omitempty"`
}

// RateLimitResponse represents a rate limit check response
type RateLimitResponse struct {
	Allowed       bool `json:"allowed"`
	CurrentCount  int  `json:"currentCount"`
	Limit         int  `json:"limit"`
	WindowSeconds int  `json:"windowSeconds"`
}
