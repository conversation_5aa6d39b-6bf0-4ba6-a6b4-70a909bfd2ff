package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/internal/models"
	"github.com/xzgame/auth-service/pkg/redis"
)

// UserValidator handles user validation and account status checking
type UserValidator struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewUserValidator creates a new user validator
func NewUserValidator(redisClient *redis.RedisClient, logger *logrus.Logger) *UserValidator {
	return &UserValidator{
		redisClient: redisClient,
		logger:      logger,
	}
}

// ValidateUser validates user information and account status
func (uv *UserValidator) ValidateUser(ctx context.Context, request *models.UserValidationRequest) (*models.UserValidationResponse, error) {
	uv.logger.WithFields(logrus.Fields{
		"user_id":  request.UserID,
		"username": request.Username,
	}).Debug("Validating user")

	response := &models.UserValidationResponse{
		Valid:      false,
		UserID:     request.UserID,
		Username:   request.Username,
		Checks:     make(map[string]bool),
		Issues:     make([]string, 0),
	}

	// Validate user ID format
	if !uv.validateUserIDFormat(request.UserID) {
		response.Issues = append(response.Issues, "Invalid user ID format")
		response.Checks["user_id_format"] = false
	} else {
		response.Checks["user_id_format"] = true
	}

	// Validate username format
	if !uv.validateUsernameFormat(request.Username) {
		response.Issues = append(response.Issues, "Invalid username format")
		response.Checks["username_format"] = false
	} else {
		response.Checks["username_format"] = true
	}

	// Check if user exists (simulated - in real implementation would check user service)
	userExists := uv.checkUserExists(ctx, request.UserID)
	response.Checks["user_exists"] = userExists
	if !userExists {
		response.Issues = append(response.Issues, "User does not exist")
	}

	// Check account status
	accountActive := uv.checkAccountStatus(ctx, request.UserID)
	response.Checks["account_active"] = accountActive
	if !accountActive {
		response.Issues = append(response.Issues, "User account is inactive or suspended")
	}

	// Check for account locks
	accountLocked := uv.checkAccountLocked(ctx, request.UserID)
	response.Checks["account_locked"] = !accountLocked
	if accountLocked {
		response.Issues = append(response.Issues, "User account is temporarily locked")
	}

	// Validate role
	roleValid := uv.validateRole(request.Role)
	response.Checks["role_valid"] = roleValid
	if !roleValid {
		response.Issues = append(response.Issues, fmt.Sprintf("Invalid role: %s", request.Role))
	}

	// Validate device ID if provided
	if request.DeviceID != "" {
		deviceValid := uv.validateDeviceID(request.DeviceID)
		response.Checks["device_valid"] = deviceValid
		if !deviceValid {
			response.Issues = append(response.Issues, "Invalid device ID format")
		}
	} else {
		response.Checks["device_valid"] = true
	}

	// Overall validation result
	response.Valid = len(response.Issues) == 0

	if response.Valid {
		uv.logger.WithField("user_id", request.UserID).Debug("User validation passed")
	} else {
		uv.logger.WithFields(logrus.Fields{
			"user_id": request.UserID,
			"issues":  response.Issues,
		}).Warn("User validation failed")
	}

	return response, nil
}

// CheckAccountStatus checks the current status of a user account
func (uv *UserValidator) CheckAccountStatus(ctx context.Context, userID string) (*models.AccountStatusResponse, error) {
	uv.logger.WithField("user_id", userID).Debug("Checking account status")

	response := &models.AccountStatusResponse{
		UserID:    userID,
		IsActive:  false,
		IsLocked:  false,
		IsSuspended: false,
		Checks:    make(map[string]interface{}),
	}

	// Check if account is active
	response.IsActive = uv.checkAccountStatus(ctx, userID)
	response.Checks["active"] = response.IsActive

	// Check if account is locked
	response.IsLocked = uv.checkAccountLocked(ctx, userID)
	response.Checks["locked"] = response.IsLocked

	// Check if account is suspended
	response.IsSuspended = uv.checkAccountSuspended(ctx, userID)
	response.Checks["suspended"] = response.IsSuspended

	// Get lock reason if locked
	if response.IsLocked {
		lockReason, lockExpiry := uv.getAccountLockInfo(ctx, userID)
		response.LockReason = lockReason
		response.LockExpiry = lockExpiry
		response.Checks["lock_reason"] = lockReason
		response.Checks["lock_expiry"] = lockExpiry
	}

	// Get suspension reason if suspended
	if response.IsSuspended {
		suspensionReason, suspensionExpiry := uv.getAccountSuspensionInfo(ctx, userID)
		response.SuspensionReason = suspensionReason
		response.SuspensionExpiry = suspensionExpiry
		response.Checks["suspension_reason"] = suspensionReason
		response.Checks["suspension_expiry"] = suspensionExpiry
	}

	// Overall account health
	response.IsHealthy = response.IsActive && !response.IsLocked && !response.IsSuspended

	return response, nil
}

// LockAccount temporarily locks a user account
func (uv *UserValidator) LockAccount(ctx context.Context, request *models.LockAccountRequest) error {
	uv.logger.WithFields(logrus.Fields{
		"user_id":  request.UserID,
		"reason":   request.Reason,
		"duration": request.Duration,
	}).Info("Locking user account")

	lockInfo := &models.AccountLockInfo{
		UserID:    request.UserID,
		Reason:    request.Reason,
		LockedAt:  time.Now(),
		ExpiresAt: time.Now().Add(request.Duration),
		LockedBy:  request.LockedBy,
	}

	lockKey := uv.buildAccountLockKey(request.UserID)
	err := uv.redisClient.Set(ctx, lockKey, lockInfo, request.Duration)
	if err != nil {
		uv.logger.WithError(err).Error("Failed to lock account")
		return fmt.Errorf("failed to lock account: %w", err)
	}

	uv.logger.WithField("user_id", request.UserID).Info("Account locked successfully")

	return nil
}

// UnlockAccount removes a lock from a user account
func (uv *UserValidator) UnlockAccount(ctx context.Context, userID string) error {
	uv.logger.WithField("user_id", userID).Info("Unlocking user account")

	lockKey := uv.buildAccountLockKey(userID)
	err := uv.redisClient.Delete(ctx, lockKey)
	if err != nil {
		uv.logger.WithError(err).Error("Failed to unlock account")
		return fmt.Errorf("failed to unlock account: %w", err)
	}

	uv.logger.WithField("user_id", userID).Info("Account unlocked successfully")

	return nil
}

// ValidateDeviceInfo validates device information
func (uv *UserValidator) ValidateDeviceInfo(ctx context.Context, request *models.DeviceValidationRequest) (*models.DeviceValidationResponse, error) {
	response := &models.DeviceValidationResponse{
		Valid:    false,
		DeviceID: request.DeviceID,
		Checks:   make(map[string]bool),
		Issues:   make([]string, 0),
	}

	// Validate device ID format
	if !uv.validateDeviceID(request.DeviceID) {
		response.Issues = append(response.Issues, "Invalid device ID format")
		response.Checks["device_id_format"] = false
	} else {
		response.Checks["device_id_format"] = true
	}

	// Check if device is known/trusted
	deviceTrusted := uv.checkDeviceTrusted(ctx, request.UserID, request.DeviceID)
	response.Checks["device_trusted"] = deviceTrusted
	response.IsTrusted = deviceTrusted

	// Check for suspicious device activity
	deviceSuspicious := uv.checkDeviceSuspicious(ctx, request.DeviceID)
	response.Checks["device_suspicious"] = !deviceSuspicious
	if deviceSuspicious {
		response.Issues = append(response.Issues, "Device flagged for suspicious activity")
	}

	// Overall validation result
	response.Valid = len(response.Issues) == 0

	return response, nil
}

// Helper methods

// validateUserIDFormat validates the format of a user ID
func (uv *UserValidator) validateUserIDFormat(userID string) bool {
	if userID == "" {
		return false
	}

	// User ID should be alphanumeric and between 3-50 characters
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]{3,50}$`, userID)
	return matched
}

// validateUsernameFormat validates the format of a username
func (uv *UserValidator) validateUsernameFormat(username string) bool {
	if username == "" {
		return false
	}

	// Username should be alphanumeric with underscores, 3-30 characters
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_]{3,30}$`, username)
	return matched
}

// validateRole validates if a role is valid
func (uv *UserValidator) validateRole(role string) bool {
	validRoles := []string{"admin", "moderator", "player", "guest"}
	
	for _, validRole := range validRoles {
		if strings.ToLower(role) == validRole {
			return true
		}
	}

	return false
}

// validateDeviceID validates the format of a device ID
func (uv *UserValidator) validateDeviceID(deviceID string) bool {
	if deviceID == "" {
		return true // Device ID is optional
	}

	// Device ID should be alphanumeric with hyphens, up to 100 characters
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]{1,100}$`, deviceID)
	return matched
}

// checkUserExists simulates checking if a user exists
func (uv *UserValidator) checkUserExists(ctx context.Context, userID string) bool {
	// In a real implementation, this would check with a user service
	// For now, we'll simulate that users exist unless they have specific patterns
	
	// Simulate non-existent users
	if strings.HasPrefix(userID, "nonexistent_") {
		return false
	}

	return true
}

// checkAccountStatus checks if an account is active
func (uv *UserValidator) checkAccountStatus(ctx context.Context, userID string) bool {
	// In a real implementation, this would check with a user service
	// For now, we'll simulate that accounts are active unless they have specific patterns
	
	// Simulate inactive accounts
	if strings.HasPrefix(userID, "inactive_") {
		return false
	}

	return true
}

// checkAccountLocked checks if an account is locked
func (uv *UserValidator) checkAccountLocked(ctx context.Context, userID string) bool {
	lockKey := uv.buildAccountLockKey(userID)
	
	var lockInfo models.AccountLockInfo
	err := uv.redisClient.Get(ctx, lockKey, &lockInfo)
	if err != nil {
		return false // Not locked if no lock info found
	}

	// Check if lock has expired
	if lockInfo.ExpiresAt.Before(time.Now()) {
		// Lock has expired, remove it
		uv.redisClient.Delete(ctx, lockKey)
		return false
	}

	return true
}

// checkAccountSuspended checks if an account is suspended
func (uv *UserValidator) checkAccountSuspended(ctx context.Context, userID string) bool {
	// In a real implementation, this would check with a user service
	// For now, we'll simulate that accounts are not suspended unless they have specific patterns
	
	// Simulate suspended accounts
	if strings.HasPrefix(userID, "suspended_") {
		return true
	}

	return false
}

// getAccountLockInfo gets lock information for an account
func (uv *UserValidator) getAccountLockInfo(ctx context.Context, userID string) (string, *time.Time) {
	lockKey := uv.buildAccountLockKey(userID)
	
	var lockInfo models.AccountLockInfo
	err := uv.redisClient.Get(ctx, lockKey, &lockInfo)
	if err != nil {
		return "", nil
	}

	return lockInfo.Reason, &lockInfo.ExpiresAt
}

// getAccountSuspensionInfo gets suspension information for an account
func (uv *UserValidator) getAccountSuspensionInfo(ctx context.Context, userID string) (string, *time.Time) {
	// In a real implementation, this would get suspension info from a user service
	// For now, we'll return simulated data
	
	if strings.HasPrefix(userID, "suspended_") {
		reason := "Account suspended for policy violation"
		expiry := time.Now().Add(7 * 24 * time.Hour) // 7 days
		return reason, &expiry
	}

	return "", nil
}

// checkDeviceTrusted checks if a device is trusted for a user
func (uv *UserValidator) checkDeviceTrusted(ctx context.Context, userID, deviceID string) bool {
	// In a real implementation, this would check device trust status
	// For now, we'll simulate that devices are trusted unless they have specific patterns
	
	if strings.HasPrefix(deviceID, "untrusted_") {
		return false
	}

	return true
}

// checkDeviceSuspicious checks if a device is flagged for suspicious activity
func (uv *UserValidator) checkDeviceSuspicious(ctx context.Context, deviceID string) bool {
	// In a real implementation, this would check device reputation
	// For now, we'll simulate that devices are not suspicious unless they have specific patterns
	
	if strings.HasPrefix(deviceID, "suspicious_") {
		return true
	}

	return false
}

// buildAccountLockKey builds a Redis key for account locks
func (uv *UserValidator) buildAccountLockKey(userID string) string {
	return fmt.Sprintf("account:lock:%s", userID)
}

// GetValidatorStats returns user validator statistics
func (uv *UserValidator) GetValidatorStats() *models.UserValidatorStats {
	return &models.UserValidatorStats{
		SupportedRoles:      []string{"admin", "moderator", "player", "guest"},
		ValidationEnabled:   true,
		AccountLockEnabled:  true,
		DeviceValidationEnabled: true,
		Version:             "1.0.0",
	}
}
