package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/internal/models"
	"github.com/xzgame/auth-service/pkg/redis"
)

// SessionManager handles user session lifecycle management
type SessionManager struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewSessionManager creates a new session manager
func NewSessionManager(redisClient *redis.RedisClient, logger *logrus.Logger) *SessionManager {
	return &SessionManager{
		redisClient: redisClient,
		logger:      logger,
	}
}

// CreateSession creates a new user session
func (sm *SessionManager) CreateSession(ctx context.Context, request *models.CreateSessionRequest) (*models.UserSession, error) {
	sm.logger.WithFields(logrus.Fields{
		"user_id":   request.UserID,
		"username":  request.Username,
		"device_id": request.DeviceID,
	}).Info("Creating user session")

	// Generate unique session ID
	sessionID := sm.generateSessionID(request.UserID)
	expiresAt := time.Now().Add(24 * time.Hour) // Default 24 hour session

	// Create session record
	session := &models.UserSession{
		UserID:    request.UserID,
		Username:  request.Username,
		Role:      request.Role,
		SessionID: sessionID,
		DeviceID:  request.DeviceID,
		TokenHash: "", // Will be set when token is created
		CreatedAt: time.Now(),
		ExpiresAt: expiresAt,
		IsActive:  true,
		LastSeen:  time.Now(),
	}

	// Store session in Redis
	sessionKey := sm.buildSessionKey(request.UserID, sessionID)
	err := sm.redisClient.Set(ctx, sessionKey, session, 24*time.Hour)
	if err != nil {
		sm.logger.WithError(err).Error("Failed to store session in Redis")
		return nil, fmt.Errorf("failed to store session: %w", err)
	}

	// Update user's active sessions list
	if err := sm.addToUserSessions(ctx, request.UserID, sessionID); err != nil {
		sm.logger.WithError(err).Warn("Failed to update user sessions list")
	}

	sm.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"session_id": sessionID,
		"expires_at": expiresAt,
	}).Info("User session created successfully")

	return session, nil
}

// GetSession retrieves a session by user ID and session ID
func (sm *SessionManager) GetSession(ctx context.Context, userID, sessionID string) (*models.UserSession, error) {
	sessionKey := sm.buildSessionKey(userID, sessionID)

	var session models.UserSession
	err := sm.redisClient.Get(ctx, sessionKey, &session)
	if err != nil {
		return nil, models.NewAuthError(models.ErrorCodeSessionNotFound, "session not found", err.Error())
	}

	return &session, nil
}

// ValidateSession validates if a session is active and not expired
func (sm *SessionManager) ValidateSession(ctx context.Context, userID, sessionID string) (*models.UserSession, error) {
	session, err := sm.GetSession(ctx, userID, sessionID)
	if err != nil {
		return nil, err
	}

	// Check if session is active
	if !session.IsActive {
		return nil, models.NewAuthError(models.ErrorCodeInvalidSession, "session is inactive", "")
	}

	// Check if session is expired
	if session.ExpiresAt.Before(time.Now()) {
		return nil, models.NewAuthError(models.ErrorCodeSessionExpired, "session has expired", "")
	}

	return session, nil
}

// UpdateSessionActivity updates the last seen timestamp for a session
func (sm *SessionManager) UpdateSessionActivity(ctx context.Context, userID, sessionID string) error {
	sessionKey := sm.buildSessionKey(userID, sessionID)

	var session models.UserSession
	err := sm.redisClient.Get(ctx, sessionKey, &session)
	if err != nil {
		// Don't fail if session doesn't exist - it might have expired
		sm.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"session_id": sessionID,
		}).Debug("Session not found for activity update")
		return nil
	}

	// Update last seen timestamp
	session.LastSeen = time.Now()

	// Store updated session
	err = sm.redisClient.Set(ctx, sessionKey, session, 24*time.Hour)
	if err != nil {
		sm.logger.WithError(err).Error("Failed to update session activity")
		return fmt.Errorf("failed to update session activity: %w", err)
	}

	return nil
}

// InvalidateSession invalidates a user session
func (sm *SessionManager) InvalidateSession(ctx context.Context, request *models.InvalidateSessionRequest) error {
	sm.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"session_id": request.SessionID,
	}).Info("Invalidating user session")

	sessionKey := sm.buildSessionKey(request.UserID, request.SessionID)

	// Get session to check if it exists
	var session models.UserSession
	err := sm.redisClient.Get(ctx, sessionKey, &session)
	if err != nil {
		sm.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    request.UserID,
			"session_id": request.SessionID,
		}).Warn("Session not found for invalidation")
		return nil // Session doesn't exist, consider it already invalidated
	}

	// Mark session as inactive
	session.IsActive = false

	// Store updated session with shorter TTL for audit purposes
	err = sm.redisClient.Set(ctx, sessionKey, session, time.Hour)
	if err != nil {
		sm.logger.WithError(err).Error("Failed to update session status")
		return fmt.Errorf("failed to invalidate session: %w", err)
	}

	// Remove from user's active sessions list
	if err := sm.removeFromUserSessions(ctx, request.UserID, request.SessionID); err != nil {
		sm.logger.WithError(err).Warn("Failed to update user sessions list")
	}

	sm.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"session_id": request.SessionID,
	}).Info("Session invalidated successfully")

	return nil
}

// GetUserSessions retrieves all active sessions for a user
func (sm *SessionManager) GetUserSessions(ctx context.Context, userID string) ([]*models.UserSession, error) {
	// Get user's session IDs
	sessionIDs, err := sm.getUserSessionIDs(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session IDs: %w", err)
	}

	sessions := make([]*models.UserSession, 0, len(sessionIDs))
	for _, sessionID := range sessionIDs {
		session, err := sm.GetSession(ctx, userID, sessionID)
		if err == nil && session.IsValid() {
			sessions = append(sessions, session)
		}
	}

	return sessions, nil
}

// InvalidateAllUserSessions invalidates all sessions for a user
func (sm *SessionManager) InvalidateAllUserSessions(ctx context.Context, userID string) error {
	sm.logger.WithField("user_id", userID).Info("Invalidating all user sessions")

	sessionIDs, err := sm.getUserSessionIDs(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user session IDs: %w", err)
	}

	invalidatedCount := 0
	for _, sessionID := range sessionIDs {
		request := &models.InvalidateSessionRequest{
			UserID:    userID,
			SessionID: sessionID,
		}
		if err := sm.InvalidateSession(ctx, request); err != nil {
			sm.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to invalidate session")
		} else {
			invalidatedCount++
		}
	}

	sm.logger.WithFields(logrus.Fields{
		"user_id":           userID,
		"total_sessions":    len(sessionIDs),
		"invalidated_count": invalidatedCount,
	}).Info("User sessions invalidation completed")

	return nil
}

// CleanupExpiredSessions removes expired sessions from storage
func (sm *SessionManager) CleanupExpiredSessions(ctx context.Context) error {
	sm.logger.Info("Starting cleanup of expired sessions")

	// This would typically scan for expired sessions
	// For now, we rely on Redis TTL for automatic cleanup
	// In a production system, you might want to implement a more sophisticated cleanup

	sm.logger.Info("Session cleanup completed")
	return nil
}

// UpdateSessionToken updates the token hash for a session
func (sm *SessionManager) UpdateSessionToken(ctx context.Context, userID, sessionID, tokenHash string) error {
	sessionKey := sm.buildSessionKey(userID, sessionID)

	var session models.UserSession
	err := sm.redisClient.Get(ctx, sessionKey, &session)
	if err != nil {
		return fmt.Errorf("session not found: %w", err)
	}

	session.TokenHash = tokenHash
	session.LastSeen = time.Now()

	err = sm.redisClient.Set(ctx, sessionKey, session, 24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update session token: %w", err)
	}

	return nil
}

// Helper methods

// buildSessionKey builds a Redis key for a session
func (sm *SessionManager) buildSessionKey(userID, sessionID string) string {
	return fmt.Sprintf("session:%s:%s", userID, sessionID)
}

// buildUserSessionsKey builds a Redis key for user's session list
func (sm *SessionManager) buildUserSessionsKey(userID string) string {
	return fmt.Sprintf("user_sessions:%s", userID)
}

// generateSessionID generates a unique session ID
func (sm *SessionManager) generateSessionID(userID string) string {
	timestamp := time.Now().Unix()
	data := fmt.Sprintf("%s:%d:%d", userID, timestamp, time.Now().UnixNano())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:16]) // Use first 16 bytes for session ID
}

// addToUserSessions adds a session ID to user's session list
func (sm *SessionManager) addToUserSessions(ctx context.Context, userID, sessionID string) error {
	userSessionsKey := sm.buildUserSessionsKey(userID)
	// For now, use a simple set operation since Redis set operations aren't implemented
	return sm.redisClient.Set(ctx, userSessionsKey+":"+sessionID, sessionID, time.Hour*24)
}

// removeFromUserSessions removes a session ID from user's session list
func (sm *SessionManager) removeFromUserSessions(ctx context.Context, userID, sessionID string) error {
	userSessionsKey := sm.buildUserSessionsKey(userID)
	// For now, delete the individual session key since Redis set operations aren't implemented
	return sm.redisClient.Delete(ctx, userSessionsKey+":"+sessionID)
}

// getUserSessionIDs gets all session IDs for a user
func (sm *SessionManager) getUserSessionIDs(ctx context.Context, userID string) ([]string, error) {
	// For now, return empty list since Redis set operations aren't implemented
	// In a real implementation, this would scan for keys matching the pattern
	return []string{}, nil
}

// GetSessionStats returns session manager statistics
func (sm *SessionManager) GetSessionStats() *models.SessionManagerStats {
	return &models.SessionManagerStats{
		DefaultSessionDuration: 24 * time.Hour,
		CleanupInterval:        time.Hour,
		Version:                "1.0.0",
	}
}
