package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/internal/models"
)

// TokenManager handles JWT token operations
type TokenManager struct {
	logger      *logrus.Logger
	jwtSecret   string
	jwtIssuer   string
	jwtAudience []string
}

// NewTokenManager creates a new token manager
func NewTokenManager(logger *logrus.Logger, jwtSecret, jwtIssuer string, jwtAudience []string) *TokenManager {
	return &TokenManager{
		logger:      logger,
		jwtSecret:   jwtSecret,
		jwtIssuer:   jwtIssuer,
		jwtAudience: jwtAudience,
	}
}

// CreateToken creates a new JWT token with the given claims
func (tm *TokenManager) CreateToken(ctx context.Context, request *models.CreateTokenRequest) (*models.CreateTokenResponse, error) {
	tm.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"username":   request.Username,
		"session_id": request.SessionID,
	}).Info("Creating JWT token")

	// Generate JTI (JWT ID) for token tracking
	jti := tm.generateJTI(request.UserID, request.SessionID)

	// Create token claims
	claims := &models.TokenClaims{
		UserID:    request.UserID,
		Username:  request.Username,
		Role:      request.Role,
		SessionID: request.SessionID,
		DeviceID:  request.DeviceID,
		JTI:       jti,
		Roles:     request.Roles,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    tm.jwtIssuer,
			Audience:  tm.jwtAudience,
			Subject:   request.UserID,
			ExpiresAt: jwt.NewNumericDate(request.ExpiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			ID:        jti,
		},
	}

	// Create and sign token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(tm.jwtSecret))
	if err != nil {
		tm.logger.WithError(err).Error("Failed to sign JWT token")
		return nil, fmt.Errorf("failed to sign token: %w", err)
	}

	// Generate token hash for storage
	tokenHash := tm.hashToken(tokenString)

	tm.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"session_id": request.SessionID,
		"jti":        jti,
		"expires_at": request.ExpiresAt,
	}).Info("JWT token created successfully")

	return &models.CreateTokenResponse{
		Token:     tokenString,
		TokenHash: tokenHash,
		Claims:    claims,
		ExpiresAt: request.ExpiresAt,
	}, nil
}

// VerifyToken verifies and validates a JWT token
func (tm *TokenManager) VerifyToken(ctx context.Context, tokenString string) (*models.TokenValidationResult, error) {
	tm.logger.WithField("token_length", len(tokenString)).Debug("Starting token verification")

	result := &models.TokenValidationResult{Valid: false}

	// Parse and validate JWT token
	token, err := jwt.ParseWithClaims(tokenString, &models.TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(tm.jwtSecret), nil
	})

	if err != nil {
		tm.logger.WithError(err).Error("Failed to parse JWT token")
		result.Error = "Invalid token format"
		result.ErrorCode = models.ErrorCodeInvalidToken
		return result, nil
	}

	claims, ok := token.Claims.(*models.TokenClaims)
	if !ok || !token.Valid {
		tm.logger.Error("Invalid token claims")
		result.Error = "Invalid token claims"
		result.ErrorCode = models.ErrorCodeInvalidClaims
		return result, nil
	}

	// Validate issuer
	if claims.Issuer != tm.jwtIssuer {
		tm.logger.WithFields(logrus.Fields{
			"expected": tm.jwtIssuer,
			"actual":   claims.Issuer,
		}).Error("Invalid token issuer")
		result.Error = "Invalid token issuer"
		result.ErrorCode = models.ErrorCodeInvalidIssuer
		return result, nil
	}

	// Validate audience
	if !tm.validateAudience(claims.Audience) {
		tm.logger.WithFields(logrus.Fields{
			"expected": tm.jwtAudience,
			"actual":   claims.Audience,
		}).Error("Invalid token audience")
		result.Error = "Invalid token audience"
		result.ErrorCode = models.ErrorCodeInvalidAudience
		return result, nil
	}

	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		tm.logger.WithField("expired_at", claims.ExpiresAt.Time).Error("Token has expired")
		result.Error = "Token has expired"
		result.ErrorCode = models.ErrorCodeExpiredToken
		return result, nil
	}

	tm.logger.WithFields(logrus.Fields{
		"user_id":    claims.UserID,
		"username":   claims.Username,
		"role":       claims.Role,
		"session_id": claims.SessionID,
		"jti":        claims.JTI,
	}).Debug("Token verified successfully")

	result.Valid = true
	result.Claims = claims

	return result, nil
}

// RefreshToken creates a new token from an existing valid token
func (tm *TokenManager) RefreshToken(ctx context.Context, request *models.RefreshTokenRequest) (*models.RefreshTokenResponse, error) {
	tm.logger.WithField("token_length", len(request.Token)).Info("Refreshing JWT token")

	// Verify the existing token first
	validationResult, err := tm.VerifyToken(ctx, request.Token)
	if err != nil {
		return nil, fmt.Errorf("failed to verify token for refresh: %w", err)
	}

	if !validationResult.Valid {
		return nil, models.NewAuthError(validationResult.ErrorCode, validationResult.Error, "")
	}

	// Create new token with extended expiration
	newExpiresAt := time.Now().Add(request.ExtendDuration)
	createRequest := &models.CreateTokenRequest{
		UserID:    validationResult.Claims.UserID,
		Username:  validationResult.Claims.Username,
		Role:      validationResult.Claims.Role,
		SessionID: validationResult.Claims.SessionID,
		DeviceID:  validationResult.Claims.DeviceID,
		Roles:     validationResult.Claims.Roles,
		ExpiresAt: newExpiresAt,
	}

	createResponse, err := tm.CreateToken(ctx, createRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to create refreshed token: %w", err)
	}

	tm.logger.WithFields(logrus.Fields{
		"user_id":        validationResult.Claims.UserID,
		"old_jti":        validationResult.Claims.JTI,
		"new_jti":        createResponse.Claims.JTI,
		"new_expires_at": newExpiresAt,
	}).Info("Token refreshed successfully")

	return &models.RefreshTokenResponse{
		Token:        createResponse.Token,
		TokenHash:    createResponse.TokenHash,
		Claims:       createResponse.Claims,
		ExpiresAt:    newExpiresAt,
		OldTokenHash: tm.hashToken(request.Token),
	}, nil
}

// ExtractClaims extracts claims from a token without full validation
func (tm *TokenManager) ExtractClaims(ctx context.Context, tokenString string) (*models.TokenClaims, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &models.TokenClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*models.TokenClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// ValidateTokenFormat validates token format without signature verification
func (tm *TokenManager) ValidateTokenFormat(ctx context.Context, tokenString string) error {
	if tokenString == "" {
		return fmt.Errorf("token cannot be empty")
	}

	// Basic JWT format validation (header.payload.signature)
	token, _, err := jwt.NewParser().ParseUnverified(tokenString, &models.TokenClaims{})
	if err != nil || token == nil {
		return fmt.Errorf("invalid token format")
	}

	return nil
}

// GetTokenStats returns token manager statistics
func (tm *TokenManager) GetTokenStats() *models.TokenManagerStats {
	return &models.TokenManagerStats{
		Issuer:             tm.jwtIssuer,
		SupportedAudiences: tm.jwtAudience,
		SigningMethod:      "HS256",
		Version:            "1.0.0",
	}
}

// Helper methods

// validateAudience checks if any of the token audiences match our expected audiences
func (tm *TokenManager) validateAudience(tokenAudiences []string) bool {
	for _, expectedAud := range tm.jwtAudience {
		for _, tokenAud := range tokenAudiences {
			if expectedAud == tokenAud {
				return true
			}
		}
	}
	return false
}

// generateJTI generates a unique JWT ID
func (tm *TokenManager) generateJTI(userID, sessionID string) string {
	data := fmt.Sprintf("%s:%s:%d", userID, sessionID, time.Now().UnixNano())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:16]) // Use first 16 bytes for JTI
}

// hashToken generates a hash of the token for storage and tracking
func (tm *TokenManager) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// IsTokenExpiringSoon checks if a token will expire within the specified duration
func (tm *TokenManager) IsTokenExpiringSoon(claims *models.TokenClaims, threshold time.Duration) bool {
	if claims.ExpiresAt == nil {
		return false
	}
	return claims.ExpiresAt.Time.Before(time.Now().Add(threshold))
}
