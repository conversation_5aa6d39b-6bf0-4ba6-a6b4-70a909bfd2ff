package services

import (
	"context"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/xzgame/auth-service/internal/models"
	pb "github.com/xzgame/auth-service/proto"
)

// GRPCServer implements the gRPC AuthService interface
type GRPCServer struct {
	pb.UnimplementedAuthServiceServer
	authService *AuthService
}

// NewGRPCServer creates a new gRPC server
func NewGRPCServer(authService *AuthService) *GRPCServer {
	return &GRPCServer{
		authService: authService,
	}
}

// VerifyToken verifies and validates a JWT token
func (s *GRPCServer) VerifyToken(ctx context.Context, req *pb.VerifyTokenRequest) (*pb.VerifyTokenResponse, error) {
	result, err := s.authService.VerifyToken(ctx, req.Token)
	if err != nil {
		return &pb.VerifyTokenResponse{
			Valid:     false,
			Error:     err.Error(),
			ErrorCode: models.ErrorCodeInternalError,
		}, nil
	}

	response := &pb.VerifyTokenResponse{
		Valid:     result.Valid,
		Error:     result.Error,
		ErrorCode: result.ErrorCode,
	}

	if result.Claims != nil {
		response.Claims = &pb.TokenClaims{
			UserId:    result.Claims.UserID,
			Username:  result.Claims.Username,
			Role:      result.Claims.Role,
			SessionId: result.Claims.SessionID,
			DeviceId:  result.Claims.DeviceID,
			Jti:       result.Claims.JTI,
			Roles:     result.Claims.Roles,
			Issuer:    result.Claims.Issuer,
			Audience:  result.Claims.Audience,
		}

		if result.Claims.ExpiresAt != nil {
			response.Claims.ExpiresAt = timestamppb.New(result.Claims.ExpiresAt.Time)
		}
		if result.Claims.IssuedAt != nil {
			response.Claims.IssuedAt = timestamppb.New(result.Claims.IssuedAt.Time)
		}
		if result.Claims.NotBefore != nil {
			response.Claims.NotBefore = timestamppb.New(result.Claims.NotBefore.Time)
		}
	}

	if result.Session != nil {
		response.Session = &pb.SessionInfo{
			SessionId: result.Session.SessionID,
			UserId:    result.Session.UserID,
			Username:  result.Session.Username,
			Role:      result.Session.Role,
			DeviceId:  result.Session.DeviceID,
			CreatedAt: timestamppb.New(result.Session.CreatedAt),
			ExpiresAt: timestamppb.New(result.Session.ExpiresAt),
			LastSeen:  timestamppb.New(result.Session.LastSeen),
		}
	}

	return response, nil
}

// CreateSession creates a new user session and returns a JWT token
func (s *GRPCServer) CreateSession(ctx context.Context, req *pb.CreateSessionRequest) (*pb.CreateSessionResponse, error) {
	createReq := &models.CreateSessionRequest{
		UserID:   req.UserId,
		Username: req.Username,
		Role:     req.Role,
		DeviceID: req.DeviceId,
	}

	result, err := s.authService.CreateSession(ctx, createReq)
	if err != nil {
		return nil, err
	}

	return &pb.CreateSessionResponse{
		Session: &pb.SessionInfo{
			SessionId: result.Session.SessionID,
			UserId:    result.Session.UserID,
			Username:  result.Session.Username,
			Role:      result.Session.Role,
			DeviceId:  result.Session.DeviceID,
			CreatedAt: timestamppb.New(result.Session.CreatedAt),
			ExpiresAt: timestamppb.New(result.Session.ExpiresAt),
			LastSeen:  timestamppb.New(result.Session.LastSeen),
		},
		Token: result.Token,
	}, nil
}

// InvalidateSession invalidates a user session
func (s *GRPCServer) InvalidateSession(ctx context.Context, req *pb.InvalidateSessionRequest) (*pb.InvalidateSessionResponse, error) {
	invalidateReq := &models.InvalidateSessionRequest{
		UserID:    req.UserId,
		SessionID: req.SessionId,
	}

	err := s.authService.InvalidateSession(ctx, invalidateReq)
	if err != nil {
		return &pb.InvalidateSessionResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return &pb.InvalidateSessionResponse{
		Success: true,
	}, nil
}

// RefreshToken refreshes an existing token
func (s *GRPCServer) RefreshToken(ctx context.Context, req *pb.RefreshTokenRequest) (*pb.RefreshTokenResponse, error) {
	// First verify the existing token
	result, err := s.authService.VerifyToken(ctx, req.Token)
	if err != nil {
		return &pb.RefreshTokenResponse{
			Error: err.Error(),
		}, nil
	}

	if !result.Valid {
		return &pb.RefreshTokenResponse{
			Error: result.Error,
		}, nil
	}

	// Create a new session with the same user info
	createReq := &models.CreateSessionRequest{
		UserID:   result.Claims.UserID,
		Username: result.Claims.Username,
		Role:     result.Claims.Role,
		DeviceID: result.Claims.DeviceID,
	}

	// Invalidate the old session
	invalidateReq := &models.InvalidateSessionRequest{
		UserID:    result.Claims.UserID,
		SessionID: result.Claims.SessionID,
	}
	s.authService.InvalidateSession(ctx, invalidateReq)

	// Create new session
	newSession, err := s.authService.CreateSession(ctx, createReq)
	if err != nil {
		return &pb.RefreshTokenResponse{
			Error: err.Error(),
		}, nil
	}

	return &pb.RefreshTokenResponse{
		Token: newSession.Token,
		Session: &pb.SessionInfo{
			SessionId: newSession.Session.SessionID,
			UserId:    newSession.Session.UserID,
			Username:  newSession.Session.Username,
			Role:      newSession.Session.Role,
			DeviceId:  newSession.Session.DeviceID,
			CreatedAt: timestamppb.New(newSession.Session.CreatedAt),
			ExpiresAt: timestamppb.New(newSession.Session.ExpiresAt),
			LastSeen:  timestamppb.New(newSession.Session.LastSeen),
		},
	}, nil
}

// GetUserSessions retrieves all active sessions for a user
func (s *GRPCServer) GetUserSessions(ctx context.Context, req *pb.GetUserSessionsRequest) (*pb.GetUserSessionsResponse, error) {
	getUserReq := &models.GetUserSessionsRequest{
		UserID: req.UserId,
	}

	result, err := s.authService.GetUserSessions(ctx, getUserReq)
	if err != nil {
		return nil, err
	}

	sessions := make([]*pb.SessionInfo, len(result.Sessions))
	for i, session := range result.Sessions {
		sessions[i] = &pb.SessionInfo{
			SessionId: session.SessionID,
			UserId:    session.UserID,
			Username:  session.Username,
			Role:      session.Role,
			DeviceId:  session.DeviceID,
			CreatedAt: timestamppb.New(session.CreatedAt),
			ExpiresAt: timestamppb.New(session.ExpiresAt),
			LastSeen:  timestamppb.New(session.LastSeen),
		}
	}

	return &pb.GetUserSessionsResponse{
		Sessions: sessions,
	}, nil
}
