package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/auth-service/internal/models"
	"github.com/xzgame/auth-service/pkg/redis"
)

// AuthService handles authentication and token management using modular components
type AuthService struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger

	// Modular components
	tokenManager         *TokenManager
	sessionManager       *SessionManager
	securityService      *SecurityService
	authorizationService *AuthorizationService
	userValidator        *UserValidator
}

// NewAuthService creates a new authentication service with modular components
func NewAuthService(
	redisClient *redis.RedisClient,
	logger *logrus.Logger,
	jwtSecret, jwtIssuer string,
	jwtAudience []string,
) *AuthService {
	// Create modular components
	tokenManager := NewTokenManager(logger, jwtSecret, jwtIssuer, jwtAudience)
	sessionManager := NewSessionManager(redisClient, logger)
	securityService := NewSecurityService(redisClient, logger)
	authorizationService := NewAuthorizationService(logger)
	userValidator := NewUserValidator(redisClient, logger)

	return &AuthService{
		redisClient:          redisClient,
		logger:               logger,
		tokenManager:         tokenManager,
		sessionManager:       sessionManager,
		securityService:      securityService,
		authorizationService: authorizationService,
		userValidator:        userValidator,
	}
}

// VerifyToken verifies and validates a JWT token using modular components
func (s *AuthService) VerifyToken(ctx context.Context, tokenString string) (*models.TokenValidationResult, error) {
	s.logger.WithField("token_length", len(tokenString)).Info("Starting comprehensive token verification")

	// Step 1: Verify token format and signature using TokenManager
	tokenResult, err := s.tokenManager.VerifyToken(ctx, tokenString)
	if err != nil {
		s.logger.WithError(err).Error("Token manager verification failed")
		return &models.TokenValidationResult{
			Valid:     false,
			Error:     "Token verification failed",
			ErrorCode: models.ErrorCodeInvalidToken,
		}, nil
	}

	if !tokenResult.Valid {
		return tokenResult, nil
	}

	claims := tokenResult.Claims

	// Step 2: Check if token is blacklisted using SecurityService
	if claims.JTI != "" {
		blacklisted, blacklistEntry, err := s.securityService.IsTokenBlacklisted(ctx, claims.JTI)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to check token blacklist")
		} else if blacklisted {
			s.logger.WithFields(logrus.Fields{
				"jti":    claims.JTI,
				"reason": blacklistEntry.Reason,
			}).Error("Token is blacklisted")
			return &models.TokenValidationResult{
				Valid:     false,
				Error:     "Token is blacklisted",
				ErrorCode: models.ErrorCodeBlacklistedToken,
			}, nil
		}
	}

	// Step 3: Validate user using UserValidator
	userValidationRequest := &models.UserValidationRequest{
		UserID:   claims.UserID,
		Username: claims.Username,
		Role:     claims.Role,
		DeviceID: claims.DeviceID,
	}

	userValidation, err := s.userValidator.ValidateUser(ctx, userValidationRequest)
	if err != nil {
		s.logger.WithError(err).Error("User validation failed")
		return &models.TokenValidationResult{
			Valid:     false,
			Error:     "User validation failed",
			ErrorCode: models.ErrorCodeInternalError,
		}, nil
	}

	if !userValidation.Valid {
		s.logger.WithFields(logrus.Fields{
			"user_id": claims.UserID,
			"issues":  userValidation.Issues,
		}).Error("User validation failed")
		return &models.TokenValidationResult{
			Valid:     false,
			Error:     "User account validation failed",
			ErrorCode: models.ErrorCodeInvalidSession,
		}, nil
	}

	// Step 4: Verify session using SessionManager
	session, err := s.sessionManager.ValidateSession(ctx, claims.UserID, claims.SessionID)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    claims.UserID,
			"session_id": claims.SessionID,
		}).Error("Session validation failed")
		return &models.TokenValidationResult{
			Valid:     false,
			Error:     "Invalid or expired session",
			ErrorCode: models.ErrorCodeInvalidSession,
		}, nil
	}

	// Step 5: Update session activity
	if err := s.sessionManager.UpdateSessionActivity(ctx, claims.UserID, claims.SessionID); err != nil {
		s.logger.WithError(err).Warn("Failed to update session activity")
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    claims.UserID,
		"username":   claims.Username,
		"role":       claims.Role,
		"session_id": claims.SessionID,
	}).Info("Token verified successfully")

	return &models.TokenValidationResult{
		Valid:   true,
		Claims:  claims,
		Session: session.ToSessionInfo(),
	}, nil
}

// CreateSession creates a new user session using modular components
func (s *AuthService) CreateSession(ctx context.Context, req *models.CreateSessionRequest) (*models.CreateSessionResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"user_id":  req.UserID,
		"username": req.Username,
		"role":     req.Role,
	}).Info("Creating new user session")

	// Step 1: Validate user using UserValidator
	userValidationRequest := &models.UserValidationRequest{
		UserID:   req.UserID,
		Username: req.Username,
		Role:     req.Role,
		DeviceID: req.DeviceID,
	}

	userValidation, err := s.userValidator.ValidateUser(ctx, userValidationRequest)
	if err != nil {
		s.logger.WithError(err).Error("User validation failed during session creation")
		return nil, fmt.Errorf("user validation failed: %w", err)
	}

	if !userValidation.Valid {
		s.logger.WithFields(logrus.Fields{
			"user_id": req.UserID,
			"issues":  userValidation.Issues,
		}).Error("User validation failed")
		return nil, fmt.Errorf("user validation failed: %v", userValidation.Issues)
	}

	// Step 2: Create session using SessionManager
	session, err := s.sessionManager.CreateSession(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("Session creation failed")
		return nil, fmt.Errorf("session creation failed: %w", err)
	}

	// Step 3: Create token using TokenManager
	expiresAt := time.Now().Add(24 * time.Hour) // Default 24 hour session
	tokenRequest := &models.CreateTokenRequest{
		UserID:    req.UserID,
		Username:  req.Username,
		Role:      req.Role,
		SessionID: session.SessionID,
		DeviceID:  req.DeviceID,
		Roles:     []string{req.Role}, // Convert single role to slice
		ExpiresAt: expiresAt,
	}

	tokenResponse, err := s.tokenManager.CreateToken(ctx, tokenRequest)
	if err != nil {
		s.logger.WithError(err).Error("Token creation failed")
		// Clean up session if token creation fails
		s.sessionManager.InvalidateSession(ctx, &models.InvalidateSessionRequest{
			UserID:    req.UserID,
			SessionID: session.SessionID,
		})
		return nil, fmt.Errorf("token creation failed: %w", err)
	}

	// Step 4: Update session with token hash
	err = s.sessionManager.UpdateSessionToken(ctx, req.UserID, session.SessionID, tokenResponse.TokenHash)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update session with token hash")
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"session_id": session.SessionID,
		"expires_at": expiresAt,
	}).Info("User session created successfully")

	return &models.CreateSessionResponse{
		Session: session.ToSessionInfo(),
		Token:   tokenResponse.Token,
	}, nil
}

// InvalidateSession invalidates a user session
func (s *AuthService) InvalidateSession(ctx context.Context, req *models.InvalidateSessionRequest) error {
	sessionKey := fmt.Sprintf("session:%s:%s", req.UserID, req.SessionID)

	// Get session to check if it exists
	var session models.UserSession
	err := s.redisClient.Get(ctx, sessionKey, &session)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"userId":    req.UserID,
			"sessionId": req.SessionID,
		}).Warn("Session not found for invalidation")
		return nil // Session doesn't exist, consider it already invalidated
	}

	// Mark session as inactive
	session.IsActive = false
	err = s.redisClient.Set(ctx, sessionKey, session, time.Hour) // Keep for 1 hour for audit
	if err != nil {
		s.logger.WithError(err).Error("Failed to update session status")
		return fmt.Errorf("failed to invalidate session: %w", err)
	}

	// Add JTI to blacklist if present
	if session.TokenHash != "" {
		blacklistKey := fmt.Sprintf("blacklist:token:%s", session.TokenHash[:16]) // Use first 16 chars as key
		err = s.redisClient.Set(ctx, blacklistKey, true, 24*time.Hour)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to blacklist token")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"userId":    req.UserID,
		"sessionId": req.SessionID,
	}).Info("Session invalidated successfully")

	return nil
}

// GetUserSessions retrieves all active sessions for a user
func (s *AuthService) GetUserSessions(ctx context.Context, req *models.GetUserSessionsRequest) (*models.GetUserSessionsResponse, error) {
	pattern := fmt.Sprintf("session:%s:*", req.UserID)
	keys, err := s.redisClient.Keys(ctx, pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	sessions := make([]*models.SessionInfo, 0, len(keys))
	for _, key := range keys {
		var session models.UserSession
		if err := s.redisClient.Get(ctx, key, &session); err == nil && session.IsValid() {
			sessions = append(sessions, session.ToSessionInfo())
		}
	}

	return &models.GetUserSessionsResponse{
		Sessions: sessions,
	}, nil
}

// GetAuthServiceStats returns comprehensive auth service statistics
func (s *AuthService) GetAuthServiceStats() *models.AuthServiceStats {
	return &models.AuthServiceStats{
		TokenStats:         s.tokenManager.GetTokenStats(),
		SessionStats:       s.sessionManager.GetSessionStats(),
		SecurityStats:      s.securityService.GetSecurityStats(),
		AuthorizationStats: s.authorizationService.GetAuthorizationStats(),
		ValidatorStats:     s.userValidator.GetValidatorStats(),
		Version:            "1.0.0",
	}
}
