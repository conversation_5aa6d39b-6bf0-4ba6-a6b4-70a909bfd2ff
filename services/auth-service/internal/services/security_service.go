package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/internal/models"
	"github.com/xzgame/auth-service/pkg/redis"
)

// SecurityService handles security features like token blacklisting and fraud detection
type SecurityService struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewSecurityService creates a new security service
func NewSecurityService(redisClient *redis.RedisClient, logger *logrus.Logger) *SecurityService {
	return &SecurityService{
		redisClient: redisClient,
		logger:      logger,
	}
}

// BlacklistToken adds a token to the blacklist
func (ss *SecurityService) BlacklistToken(ctx context.Context, request *models.BlacklistTokenRequest) error {
	ss.logger.WithFields(logrus.Fields{
		"user_id":    request.UserID,
		"jti":        request.JTI,
		"reason":     request.Reason,
		"expires_at": request.ExpiresAt,
	}).Info("Blacklisting token")

	// Create blacklist entry
	blacklistEntry := &models.BlacklistEntry{
		JTI:           request.JTI,
		UserID:        request.UserID,
		TokenHash:     request.TokenHash,
		Reason:        request.Reason,
		BlacklistedAt: time.Now(),
		ExpiresAt:     request.ExpiresAt,
	}

	// Store in Redis with TTL based on token expiration
	blacklistKey := ss.buildBlacklistKey(request.JTI)
	ttl := time.Until(request.ExpiresAt)
	if ttl <= 0 {
		ttl = time.Hour // Minimum 1 hour for audit purposes
	}

	err := ss.redisClient.Set(ctx, blacklistKey, blacklistEntry, ttl)
	if err != nil {
		ss.logger.WithError(err).Error("Failed to blacklist token")
		return fmt.Errorf("failed to blacklist token: %w", err)
	}

	// Also store by token hash for additional lookup
	if request.TokenHash != "" {
		hashKey := ss.buildTokenHashKey(request.TokenHash)
		err = ss.redisClient.Set(ctx, hashKey, blacklistEntry, ttl)
		if err != nil {
			ss.logger.WithError(err).Warn("Failed to store token hash blacklist entry")
		}
	}

	ss.logger.WithField("jti", request.JTI).Info("Token blacklisted successfully")

	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func (ss *SecurityService) IsTokenBlacklisted(ctx context.Context, jti string) (bool, *models.BlacklistEntry, error) {
	blacklistKey := ss.buildBlacklistKey(jti)

	var entry models.BlacklistEntry
	err := ss.redisClient.Get(ctx, blacklistKey, &entry)
	if err != nil {
		// Token not found in blacklist
		return false, nil, nil
	}

	// Check if blacklist entry is still valid
	if entry.ExpiresAt.Before(time.Now()) {
		return false, nil, nil
	}

	ss.logger.WithFields(logrus.Fields{
		"jti":            jti,
		"blacklisted_at": entry.BlacklistedAt,
		"reason":         entry.Reason,
	}).Debug("Token found in blacklist")

	return true, &entry, nil
}

// IsTokenHashBlacklisted checks if a token hash is blacklisted
func (ss *SecurityService) IsTokenHashBlacklisted(ctx context.Context, tokenHash string) (bool, *models.BlacklistEntry, error) {
	hashKey := ss.buildTokenHashKey(tokenHash)

	var entry models.BlacklistEntry
	err := ss.redisClient.Get(ctx, hashKey, &entry)
	if err != nil {
		// Token hash not found in blacklist
		return false, nil, nil
	}

	// Check if blacklist entry is still valid
	if entry.ExpiresAt.Before(time.Now()) {
		return false, nil, nil
	}

	return true, &entry, nil
}

// RemoveFromBlacklist removes a token from the blacklist
func (ss *SecurityService) RemoveFromBlacklist(ctx context.Context, jti string) error {
	ss.logger.WithField("jti", jti).Info("Removing token from blacklist")

	blacklistKey := ss.buildBlacklistKey(jti)

	// Get entry to also remove token hash entry
	var entry models.BlacklistEntry
	err := ss.redisClient.Get(ctx, blacklistKey, &entry)
	if err == nil && entry.TokenHash != "" {
		hashKey := ss.buildTokenHashKey(entry.TokenHash)
		ss.redisClient.Delete(ctx, hashKey) // Ignore errors
	}

	// Remove main blacklist entry
	err = ss.redisClient.Delete(ctx, blacklistKey)
	if err != nil {
		ss.logger.WithError(err).Error("Failed to remove token from blacklist")
		return fmt.Errorf("failed to remove token from blacklist: %w", err)
	}

	ss.logger.WithField("jti", jti).Info("Token removed from blacklist")

	return nil
}

// LogSecurityEvent logs a security-related event
func (ss *SecurityService) LogSecurityEvent(ctx context.Context, event *models.SecurityEvent) error {
	ss.logger.WithFields(logrus.Fields{
		"event_type":  event.EventType,
		"user_id":     event.UserID,
		"ip_address":  event.IPAddress,
		"user_agent":  event.UserAgent,
		"severity":    event.Severity,
		"description": event.Description,
	}).Info("Security event logged")

	// Store security event for audit purposes
	eventKey := ss.buildSecurityEventKey(event.UserID, event.Timestamp)
	err := ss.redisClient.Set(ctx, eventKey, event, 30*24*time.Hour) // Keep for 30 days
	if err != nil {
		ss.logger.WithError(err).Error("Failed to store security event")
		return fmt.Errorf("failed to store security event: %w", err)
	}

	// Check for suspicious patterns
	if err := ss.checkSuspiciousActivity(ctx, event); err != nil {
		ss.logger.WithError(err).Warn("Failed to check suspicious activity")
	}

	return nil
}

// DetectSuspiciousActivity detects suspicious authentication patterns
func (ss *SecurityService) DetectSuspiciousActivity(ctx context.Context, userID string, timeWindow time.Duration) (*models.SuspiciousActivityReport, error) {
	// This is a simplified implementation
	// In production, you'd implement more sophisticated fraud detection

	report := &models.SuspiciousActivityReport{
		UserID:       userID,
		TimeWindow:   timeWindow,
		CheckedAt:    time.Now(),
		IsSuspicious: false,
		Indicators:   make([]string, 0),
	}

	// Check for multiple failed login attempts
	failedAttempts, err := ss.getFailedLoginAttempts(ctx, userID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to check failed login attempts: %w", err)
	}

	if failedAttempts > 5 {
		report.IsSuspicious = true
		report.Indicators = append(report.Indicators, fmt.Sprintf("Multiple failed login attempts: %d", failedAttempts))
	}

	// Check for multiple IP addresses
	ipCount, err := ss.getUniqueIPCount(ctx, userID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to check IP addresses: %w", err)
	}

	if ipCount > 3 {
		report.IsSuspicious = true
		report.Indicators = append(report.Indicators, fmt.Sprintf("Multiple IP addresses: %d", ipCount))
	}

	return report, nil
}

// RateLimitCheck checks if a user has exceeded rate limits
func (ss *SecurityService) RateLimitCheck(ctx context.Context, request *models.RateLimitRequest) (*models.RateLimitResponse, error) {
	rateLimitKey := ss.buildRateLimitKey(request.UserID, request.Action)

	// Get current count
	count, err := ss.redisClient.Incr(ctx, rateLimitKey)
	if err != nil {
		return nil, fmt.Errorf("failed to check rate limit: %w", err)
	}

	// Set expiration on first increment
	if count == 1 {
		ss.redisClient.Expire(ctx, rateLimitKey, request.Window)
	}

	response := &models.RateLimitResponse{
		Allowed:       count <= int64(request.Limit),
		CurrentCount:  int(count),
		Limit:         request.Limit,
		WindowSeconds: int(request.Window.Seconds()),
	}

	if !response.Allowed {
		ss.logger.WithFields(logrus.Fields{
			"user_id":       request.UserID,
			"action":        request.Action,
			"current_count": count,
			"limit":         request.Limit,
		}).Warn("Rate limit exceeded")

		// Log security event
		securityEvent := &models.SecurityEvent{
			EventType:   "rate_limit_exceeded",
			UserID:      request.UserID,
			IPAddress:   request.IPAddress,
			UserAgent:   request.UserAgent,
			Severity:    "medium",
			Description: fmt.Sprintf("Rate limit exceeded for action: %s", request.Action),
			Timestamp:   time.Now(),
		}
		ss.LogSecurityEvent(ctx, securityEvent)
	}

	return response, nil
}

// Helper methods

// buildBlacklistKey builds a Redis key for blacklisted tokens
func (ss *SecurityService) buildBlacklistKey(jti string) string {
	return fmt.Sprintf("blacklist:token:%s", jti)
}

// buildTokenHashKey builds a Redis key for token hash blacklist
func (ss *SecurityService) buildTokenHashKey(tokenHash string) string {
	// Use first 16 characters of hash for key
	hashPrefix := tokenHash
	if len(hashPrefix) > 16 {
		hashPrefix = hashPrefix[:16]
	}
	return fmt.Sprintf("blacklist:hash:%s", hashPrefix)
}

// buildSecurityEventKey builds a Redis key for security events
func (ss *SecurityService) buildSecurityEventKey(userID string, timestamp time.Time) string {
	return fmt.Sprintf("security:event:%s:%d", userID, timestamp.Unix())
}

// buildRateLimitKey builds a Redis key for rate limiting
func (ss *SecurityService) buildRateLimitKey(userID, action string) string {
	return fmt.Sprintf("rate_limit:%s:%s", userID, action)
}

// checkSuspiciousActivity checks for suspicious patterns in security events
func (ss *SecurityService) checkSuspiciousActivity(ctx context.Context, event *models.SecurityEvent) error {
	// Simplified suspicious activity detection
	if event.EventType == "failed_login" {
		// Increment failed login counter
		failedLoginKey := fmt.Sprintf("failed_logins:%s", event.UserID)
		count, err := ss.redisClient.Incr(ctx, failedLoginKey)
		if err != nil {
			return err
		}

		// Set expiration on first increment
		if count == 1 {
			ss.redisClient.Expire(ctx, failedLoginKey, time.Hour)
		}

		// Alert if too many failed attempts
		if count > 5 {
			ss.logger.WithFields(logrus.Fields{
				"user_id":         event.UserID,
				"failed_attempts": count,
			}).Warn("Suspicious activity detected: multiple failed login attempts")
		}
	}

	return nil
}

// getFailedLoginAttempts gets the number of failed login attempts for a user
func (ss *SecurityService) getFailedLoginAttempts(ctx context.Context, userID string, timeWindow time.Duration) (int, error) {
	failedLoginKey := fmt.Sprintf("failed_logins:%s", userID)
	var count int
	err := ss.redisClient.Get(ctx, failedLoginKey, &count)
	if err != nil {
		return 0, nil // No failed attempts recorded
	}

	// This is simplified - in production you'd track attempts with timestamps
	return count, nil
}

// getUniqueIPCount gets the number of unique IP addresses for a user
func (ss *SecurityService) getUniqueIPCount(ctx context.Context, userID string, timeWindow time.Duration) (int, error) {
	// This is simplified - in production you'd track IP addresses with timestamps
	// For now, return a mock count since Redis set operations aren't implemented
	return 1, nil
}

// GetSecurityStats returns security service statistics
func (ss *SecurityService) GetSecurityStats() *models.SecurityStats {
	return &models.SecurityStats{
		BlacklistEnabled:      true,
		RateLimitEnabled:      true,
		FraudDetectionEnabled: true,
		Version:               "1.0.0",
	}
}
