package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/pkg/redis"
)

// RedisRequestHandler handles Redis pub/sub requests for token verification
type RedisRequestHandler struct {
	redisClient *redis.RedisClient
	authService *AuthService
	logger      *logrus.Logger
	isRunning   bool
	mutex       sync.RWMutex
}

// NewRedisRequestHandler creates a new Redis request handler
func NewRedisRequestHandler(redisClient *redis.RedisClient, authService *AuthService, logger *logrus.Logger) *RedisRequestHandler {
	return &RedisRequestHandler{
		redisClient: redisClient,
		authService: authService,
		logger:      logger,
		isRunning:   false,
	}
}

// RequestMessage represents a Redis request message
type RequestMessage struct {
	Event struct {
		Type    string      `json:"type"`
		Payload interface{} `json:"payload"`
	} `json:"event"`
	Metadata struct {
		ServiceID       string `json:"serviceId"`
		CorrelationID   string `json:"correlationId"`
		ResponseChannel string `json:"responseChannel"`
		Timestamp       string `json:"timestamp"`
	} `json:"metadata"`
}

// TokenVerificationPayload represents token verification request payload
type TokenVerificationPayload struct {
	Token     string `json:"token"`
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	SessionID string `json:"sessionId"`
}

// Start begins listening for Redis requests
func (h *RedisRequestHandler) Start(ctx context.Context) error {
	h.mutex.Lock()
	if h.isRunning {
		h.mutex.Unlock()
		return fmt.Errorf("Redis request handler is already running")
	}
	h.isRunning = true
	h.mutex.Unlock()

	h.logger.Info("Starting Auth Service Redis request handler")

	// Subscribe to auth requests channel
	pubsub := h.redisClient.GetClient().Subscribe(ctx, "auth:requests")
	defer pubsub.Close()

	// Listen for messages
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			h.logger.Info("Auth Service Redis request handler stopped")
			h.mutex.Lock()
			h.isRunning = false
			h.mutex.Unlock()
			return ctx.Err()
		case msg := <-ch:
			h.handleMessage(ctx, msg.Payload, msg.Channel)
		}
	}
}

// handleMessage processes incoming Redis messages
func (h *RedisRequestHandler) handleMessage(ctx context.Context, payload string, channel string) {
	h.logger.WithFields(logrus.Fields{
		"channel":        channel,
		"payload_length": len(payload),
	}).Debug("Received Redis message")

	var requestMsg RequestMessage
	if err := json.Unmarshal([]byte(payload), &requestMsg); err != nil {
		h.logger.WithError(err).Error("Failed to parse request message")
		return
	}

	correlationID := requestMsg.Metadata.CorrelationID
	responseChannel := requestMsg.Metadata.ResponseChannel

	h.logger.WithFields(logrus.Fields{
		"type":            requestMsg.Event.Type,
		"correlationId":   correlationID,
		"responseChannel": responseChannel,
	}).Info("Processing auth request")

	switch requestMsg.Event.Type {
	case "verify_token":
		h.handleTokenVerification(ctx, requestMsg)
	default:
		h.logger.WithField("type", requestMsg.Event.Type).Warn("Unknown auth request type")
		h.sendErrorResponse(ctx, responseChannel, correlationID, "UNKNOWN_REQUEST_TYPE", "Unknown request type")
	}
}

// handleTokenVerification processes token verification requests
func (h *RedisRequestHandler) handleTokenVerification(ctx context.Context, requestMsg RequestMessage) {
	correlationID := requestMsg.Metadata.CorrelationID
	responseChannel := requestMsg.Metadata.ResponseChannel

	// Parse the payload
	payloadBytes, err := json.Marshal(requestMsg.Event.Payload)
	if err != nil {
		h.logger.WithError(err).Error("Failed to marshal payload")
		h.sendErrorResponse(ctx, responseChannel, correlationID, "PAYLOAD_MARSHAL_ERROR", err.Error())
		return
	}

	var payload TokenVerificationPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		h.logger.WithError(err).Error("Failed to parse token verification payload")
		h.sendErrorResponse(ctx, responseChannel, correlationID, "PAYLOAD_PARSE_ERROR", err.Error())
		return
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        payload.UserID,
		"username":      payload.Username,
		"sessionId":     payload.SessionID,
		"correlationId": correlationID,
	}).Info("Processing token verification request")

	// Verify token with auth service
	result, err := h.authService.VerifyToken(ctx, payload.Token)
	if err != nil {
		h.logger.WithError(err).Error("Failed to verify token")
		h.sendErrorResponse(ctx, responseChannel, correlationID, "TOKEN_VERIFICATION_FAILED", err.Error())
		return
	}

	if !result.Valid {
		h.logger.WithFields(logrus.Fields{
			"userId":        payload.UserID,
			"correlationId": correlationID,
			"error":         result.Error,
			"errorCode":     result.ErrorCode,
		}).Warn("Token verification failed")

		h.sendErrorResponse(ctx, responseChannel, correlationID, result.ErrorCode, result.Error)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"userId":        result.Claims.UserID,
		"username":      result.Claims.Username,
		"correlationId": correlationID,
	}).Info("Token verification successful")

	// Build success response
	responseData := map[string]interface{}{
		"valid":     true,
		"userId":    result.Claims.UserID,
		"username":  result.Claims.Username,
		"sessionId": result.Claims.SessionID,
		"role":      result.Claims.Role,
	}

	// Include additional fields if available
	if result.Claims.DeviceID != "" {
		responseData["deviceId"] = result.Claims.DeviceID
	}

	h.sendSuccessResponse(ctx, responseChannel, correlationID, responseData)
}

// sendSuccessResponse sends a success response via Redis
func (h *RedisRequestHandler) sendSuccessResponse(ctx context.Context, responseChannel, correlationID string, data interface{}) {
	response := map[string]interface{}{
		"success":       true,
		"correlationId": correlationID,
		"data":          data,
		"timestamp":     time.Now().UTC().Format(time.RFC3339),
	}

	h.publishResponse(ctx, responseChannel, response)
}

// sendErrorResponse sends an error response via Redis
func (h *RedisRequestHandler) sendErrorResponse(ctx context.Context, responseChannel, correlationID, errorCode, errorMessage string) {
	response := map[string]interface{}{
		"success":       false,
		"correlationId": correlationID,
		"error": map[string]interface{}{
			"code":    errorCode,
			"message": errorMessage,
		},
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	h.publishResponse(ctx, responseChannel, response)
}

// publishResponse publishes a response to Redis
func (h *RedisRequestHandler) publishResponse(ctx context.Context, channel string, response interface{}) {
	responseBytes, err := json.Marshal(response)
	if err != nil {
		h.logger.WithError(err).Error("Failed to marshal response")
		return
	}

	if err := h.redisClient.GetClient().Publish(ctx, channel, responseBytes).Err(); err != nil {
		h.logger.WithError(err).WithField("channel", channel).Error("Failed to publish response")
		return
	}

	h.logger.WithField("channel", channel).Debug("Published response to Redis")
}

// IsRunning returns whether the handler is currently running
func (h *RedisRequestHandler) IsRunning() bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.isRunning
}

// Stop gracefully stops the handler
func (h *RedisRequestHandler) Stop() {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.isRunning = false
	h.logger.Info("Auth Service Redis request handler stopped")
}
