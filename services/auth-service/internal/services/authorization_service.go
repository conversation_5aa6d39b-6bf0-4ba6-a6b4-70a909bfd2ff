package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/auth-service/internal/models"
)

// AuthorizationService handles permission checks and role-based access control
type AuthorizationService struct {
	logger *logrus.Logger
}

// NewAuthorizationService creates a new authorization service
func NewAuthorizationService(logger *logrus.Logger) *AuthorizationService {
	return &AuthorizationService{
		logger: logger,
	}
}

// CheckPermission checks if a user has permission to perform an action
func (as *AuthorizationService) CheckPermission(ctx context.Context, request *models.PermissionCheckRequest) (*models.PermissionCheckResponse, error) {
	as.logger.WithFields(logrus.Fields{
		"user_id":  request.UserID,
		"role":     request.Role,
		"action":   request.Action,
		"resource": request.Resource,
	}).Debug("Checking user permission")

	// Get permissions for the user's role
	permissions := as.getRolePermissions(request.Role)

	// Check if user has the required permission
	hasPermission := as.hasPermission(permissions, request.Action, request.Resource)

	response := &models.PermissionCheckResponse{
		Allowed:     hasPermission,
		UserID:      request.UserID,
		Role:        request.Role,
		Action:      request.Action,
		Resource:    request.Resource,
		Permissions: permissions,
	}

	if !hasPermission {
		as.logger.WithFields(logrus.Fields{
			"user_id":  request.UserID,
			"role":     request.Role,
			"action":   request.Action,
			"resource": request.Resource,
		}).Warn("Permission denied")

		response.Reason = fmt.Sprintf("Role '%s' does not have permission to '%s' on '%s'", request.Role, request.Action, request.Resource)
	}

	return response, nil
}

// CheckRoomAccess checks if a user can access a specific room
func (as *AuthorizationService) CheckRoomAccess(ctx context.Context, request *models.RoomAccessRequest) (*models.RoomAccessResponse, error) {
	as.logger.WithFields(logrus.Fields{
		"user_id":     request.UserID,
		"room_id":     request.RoomID,
		"action":      request.Action,
		"room_status": request.RoomStatus,
	}).Debug("Checking room access")

	response := &models.RoomAccessResponse{
		Allowed:    false,
		UserID:     request.UserID,
		RoomID:     request.RoomID,
		Action:     request.Action,
		Conditions: make([]string, 0),
	}

	// Check basic permission first
	permissionRequest := &models.PermissionCheckRequest{
		UserID:   request.UserID,
		Role:     request.Role,
		Action:   request.Action,
		Resource: "room",
	}

	permissionResponse, err := as.CheckPermission(ctx, permissionRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to check permission: %w", err)
	}

	if !permissionResponse.Allowed {
		response.Reason = permissionResponse.Reason
		return response, nil
	}

	// Check room-specific conditions
	switch request.Action {
	case "join":
		response.Allowed = as.checkJoinConditions(request, response)
	case "leave":
		response.Allowed = as.checkLeaveConditions(request, response)
	case "manage":
		response.Allowed = as.checkManageConditions(request, response)
	case "view":
		response.Allowed = as.checkViewConditions(request, response)
	default:
		response.Reason = fmt.Sprintf("Unknown action: %s", request.Action)
	}

	return response, nil
}

// CheckGameAccess checks if a user can participate in game operations
func (as *AuthorizationService) CheckGameAccess(ctx context.Context, request *models.GameAccessRequest) (*models.GameAccessResponse, error) {
	as.logger.WithFields(logrus.Fields{
		"user_id":   request.UserID,
		"game_type": request.GameType,
		"action":    request.Action,
	}).Debug("Checking game access")

	response := &models.GameAccessResponse{
		Allowed:  false,
		UserID:   request.UserID,
		GameType: request.GameType,
		Action:   request.Action,
	}

	// Check basic permission
	permissionRequest := &models.PermissionCheckRequest{
		UserID:   request.UserID,
		Role:     request.Role,
		Action:   request.Action,
		Resource: "game",
	}

	permissionResponse, err := as.CheckPermission(ctx, permissionRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to check permission: %w", err)
	}

	if !permissionResponse.Allowed {
		response.Reason = permissionResponse.Reason
		return response, nil
	}

	// Check game-specific conditions
	switch request.Action {
	case "play":
		response.Allowed = as.checkPlayConditions(request, response)
	case "bet":
		response.Allowed = as.checkBetConditions(request, response)
	case "view_results":
		response.Allowed = as.checkViewResultsConditions(request, response)
	default:
		response.Reason = fmt.Sprintf("Unknown game action: %s", request.Action)
	}

	return response, nil
}

// ValidateUserBalance validates if a user has sufficient balance for an operation
func (as *AuthorizationService) ValidateUserBalance(ctx context.Context, request *models.BalanceValidationRequest) (*models.BalanceValidationResponse, error) {
	as.logger.WithFields(logrus.Fields{
		"user_id": request.UserID,
		"amount":  request.Amount,
		"action":  request.Action,
	}).Debug("Validating user balance")

	response := &models.BalanceValidationResponse{
		Valid:   false,
		UserID:  request.UserID,
		Amount:  request.Amount,
		Action:  request.Action,
	}

	// Basic validation
	if request.Amount <= 0 {
		response.Reason = "Invalid amount: must be greater than 0"
		return response, nil
	}

	// In a real implementation, this would check against a wallet service
	// For now, we'll simulate balance validation
	response.Valid = true
	response.Reason = "Balance validation passed (simulated)"

	return response, nil
}

// Helper methods

// getRolePermissions returns the permissions for a given role
func (as *AuthorizationService) getRolePermissions(role string) []string {
	rolePermissions := map[string][]string{
		"admin": {
			"room:*", "game:*", "user:*", "system:*",
		},
		"moderator": {
			"room:view", "room:manage", "game:view", "user:view",
		},
		"player": {
			"room:view", "room:join", "room:leave", "game:play", "game:bet", "game:view_results",
		},
		"guest": {
			"room:view", "game:view_results",
		},
	}

	permissions, exists := rolePermissions[strings.ToLower(role)]
	if !exists {
		return []string{} // No permissions for unknown roles
	}

	return permissions
}

// hasPermission checks if a permission list contains the required permission
func (as *AuthorizationService) hasPermission(permissions []string, action, resource string) bool {
	requiredPermission := fmt.Sprintf("%s:%s", resource, action)

	for _, permission := range permissions {
		// Check exact match
		if permission == requiredPermission {
			return true
		}

		// Check wildcard permissions
		if strings.HasSuffix(permission, ":*") {
			resourcePrefix := strings.TrimSuffix(permission, ":*")
			if resource == resourcePrefix {
				return true
			}
		}

		// Check global wildcard
		if permission == "*" {
			return true
		}
	}

	return false
}

// checkJoinConditions checks conditions for joining a room
func (as *AuthorizationService) checkJoinConditions(request *models.RoomAccessRequest, response *models.RoomAccessResponse) bool {
	// Check room status
	if request.RoomStatus != "waiting" {
		response.Reason = "Room is not accepting new players"
		return false
	}

	// Check room capacity
	if request.CurrentPlayers >= request.MaxPlayers {
		response.Reason = "Room is full"
		return false
	}

	// Check password for private rooms
	if request.IsPrivate && request.Password != request.ProvidedPassword {
		response.Reason = "Invalid room password"
		return false
	}

	// Check bet amount
	if request.BetAmount < request.MinBet || request.BetAmount > request.MaxBet {
		response.Reason = fmt.Sprintf("Bet amount must be between %d and %d", request.MinBet, request.MaxBet)
		return false
	}

	response.Conditions = append(response.Conditions, "Room is accepting players", "User has sufficient permissions")
	return true
}

// checkLeaveConditions checks conditions for leaving a room
func (as *AuthorizationService) checkLeaveConditions(request *models.RoomAccessRequest, response *models.RoomAccessResponse) bool {
	// Players can generally leave rooms unless game is in progress
	if request.RoomStatus == "playing" {
		response.Reason = "Cannot leave room while game is in progress"
		return false
	}

	return true
}

// checkManageConditions checks conditions for managing a room
func (as *AuthorizationService) checkManageConditions(request *models.RoomAccessRequest, response *models.RoomAccessResponse) bool {
	// Only room owners, moderators, and admins can manage rooms
	allowedRoles := []string{"admin", "moderator"}
	
	for _, allowedRole := range allowedRoles {
		if strings.ToLower(request.Role) == allowedRole {
			return true
		}
	}

	// Check if user is room owner
	if request.UserID == request.RoomOwnerID {
		return true
	}

	response.Reason = "Only room owners, moderators, or admins can manage rooms"
	return false
}

// checkViewConditions checks conditions for viewing a room
func (as *AuthorizationService) checkViewConditions(request *models.RoomAccessRequest, response *models.RoomAccessResponse) bool {
	// Most users can view rooms unless they're private and user doesn't have access
	if request.IsPrivate {
		// Check if user is in the room or has elevated permissions
		allowedRoles := []string{"admin", "moderator"}
		for _, allowedRole := range allowedRoles {
			if strings.ToLower(request.Role) == allowedRole {
				return true
			}
		}

		// Check if user is room owner or participant
		if request.UserID == request.RoomOwnerID {
			return true
		}

		response.Reason = "Private room access denied"
		return false
	}

	return true
}

// checkPlayConditions checks conditions for playing games
func (as *AuthorizationService) checkPlayConditions(request *models.GameAccessRequest, response *models.GameAccessResponse) bool {
	// Basic play permission check
	if strings.ToLower(request.Role) == "guest" {
		response.Reason = "Guests cannot play games"
		return false
	}

	return true
}

// checkBetConditions checks conditions for betting in games
func (as *AuthorizationService) checkBetConditions(request *models.GameAccessRequest, response *models.GameAccessResponse) bool {
	// Check if user can play first
	if !as.checkPlayConditions(request, response) {
		return false
	}

	// Additional bet-specific checks would go here
	// For example, checking user balance, bet limits, etc.

	return true
}

// checkViewResultsConditions checks conditions for viewing game results
func (as *AuthorizationService) checkViewResultsConditions(request *models.GameAccessRequest, response *models.GameAccessResponse) bool {
	// Most users can view results
	return true
}

// GetAuthorizationStats returns authorization service statistics
func (as *AuthorizationService) GetAuthorizationStats() *models.AuthorizationStats {
	return &models.AuthorizationStats{
		SupportedRoles:    []string{"admin", "moderator", "player", "guest"},
		SupportedResources: []string{"room", "game", "user", "system"},
		SupportedActions:  []string{"view", "join", "leave", "manage", "play", "bet", "view_results"},
		Version:           "1.0.0",
	}
}
