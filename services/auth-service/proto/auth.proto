syntax = "proto3";

package auth;

option go_package = "github.com/xzgame/auth-service/proto";

import "google/protobuf/timestamp.proto";

// AuthService provides authentication and session management
service AuthService {
  // VerifyToken verifies and validates a JWT token
  rpc VerifyToken(VerifyTokenRequest) returns (VerifyTokenResponse);
  
  // CreateSession creates a new user session and returns a JWT token
  rpc CreateSession(CreateSessionRequest) returns (CreateSessionResponse);
  
  // InvalidateSession invalidates a user session
  rpc InvalidateSession(InvalidateSessionRequest) returns (InvalidateSessionResponse);
  
  // RefreshToken refreshes an existing token
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // GetUserSessions retrieves all active sessions for a user
  rpc GetUserSessions(GetUserSessionsRequest) returns (GetUserSessionsResponse);
}

// VerifyTokenRequest contains the token to verify
message VerifyTokenRequest {
  string token = 1;
}

// VerifyTokenResponse contains the verification result
message VerifyTokenResponse {
  bool valid = 1;
  TokenClaims claims = 2;
  SessionInfo session = 3;
  string error = 4;
  string error_code = 5;
}

// CreateSessionRequest contains session creation parameters
message CreateSessionRequest {
  string user_id = 1;
  string username = 2;
  string role = 3;
  string device_id = 4;
}

// CreateSessionResponse contains the created session and token
message CreateSessionResponse {
  SessionInfo session = 1;
  string token = 2;
}

// InvalidateSessionRequest contains session invalidation parameters
message InvalidateSessionRequest {
  string user_id = 1;
  string session_id = 2;
}

// InvalidateSessionResponse confirms session invalidation
message InvalidateSessionResponse {
  bool success = 1;
  string error = 2;
}

// RefreshTokenRequest contains the token to refresh
message RefreshTokenRequest {
  string token = 1;
}

// RefreshTokenResponse contains the new token and session info
message RefreshTokenResponse {
  string token = 1;
  SessionInfo session = 2;
  string error = 3;
}

// GetUserSessionsRequest contains the user ID
message GetUserSessionsRequest {
  string user_id = 1;
}

// GetUserSessionsResponse contains all user sessions
message GetUserSessionsResponse {
  repeated SessionInfo sessions = 1;
}

// TokenClaims represents JWT token claims
message TokenClaims {
  string user_id = 1;
  string username = 2;
  string role = 3;
  string session_id = 4;
  string device_id = 5;
  string jti = 6;
  repeated string roles = 7;
  string issuer = 8;
  repeated string audience = 9;
  google.protobuf.Timestamp expires_at = 10;
  google.protobuf.Timestamp issued_at = 11;
  google.protobuf.Timestamp not_before = 12;
}

// SessionInfo represents session information
message SessionInfo {
  string session_id = 1;
  string user_id = 2;
  string username = 3;
  string role = 4;
  string device_id = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp expires_at = 7;
  google.protobuf.Timestamp last_seen = 8;
}
