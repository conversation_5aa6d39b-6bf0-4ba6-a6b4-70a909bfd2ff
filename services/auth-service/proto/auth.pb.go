// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/auth.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// VerifyTokenRequest contains the token to verify
type VerifyTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTokenRequest) Reset() {
	*x = VerifyTokenRequest{}
	mi := &file_proto_auth_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTokenRequest) ProtoMessage() {}

func (x *VerifyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTokenRequest.ProtoReflect.Descriptor instead.
func (*VerifyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{0}
}

func (x *VerifyTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// VerifyTokenResponse contains the verification result
type VerifyTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Valid         bool                   `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	Claims        *TokenClaims           `protobuf:"bytes,2,opt,name=claims,proto3" json:"claims,omitempty"`
	Session       *SessionInfo           `protobuf:"bytes,3,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	ErrorCode     string                 `protobuf:"bytes,5,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTokenResponse) Reset() {
	*x = VerifyTokenResponse{}
	mi := &file_proto_auth_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTokenResponse) ProtoMessage() {}

func (x *VerifyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTokenResponse.ProtoReflect.Descriptor instead.
func (*VerifyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{1}
}

func (x *VerifyTokenResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *VerifyTokenResponse) GetClaims() *TokenClaims {
	if x != nil {
		return x.Claims
	}
	return nil
}

func (x *VerifyTokenResponse) GetSession() *SessionInfo {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *VerifyTokenResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *VerifyTokenResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

// CreateSessionRequest contains session creation parameters
type CreateSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Role          string                 `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	DeviceId      string                 `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSessionRequest) Reset() {
	*x = CreateSessionRequest{}
	mi := &file_proto_auth_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionRequest) ProtoMessage() {}

func (x *CreateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSessionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateSessionRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateSessionRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *CreateSessionRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// CreateSessionResponse contains the created session and token
type CreateSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Session       *SessionInfo           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSessionResponse) Reset() {
	*x = CreateSessionResponse{}
	mi := &file_proto_auth_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionResponse) ProtoMessage() {}

func (x *CreateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateSessionResponse) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{3}
}

func (x *CreateSessionResponse) GetSession() *SessionInfo {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *CreateSessionResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// InvalidateSessionRequest contains session invalidation parameters
type InvalidateSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId     string                 `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvalidateSessionRequest) Reset() {
	*x = InvalidateSessionRequest{}
	mi := &file_proto_auth_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvalidateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateSessionRequest) ProtoMessage() {}

func (x *InvalidateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateSessionRequest.ProtoReflect.Descriptor instead.
func (*InvalidateSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{4}
}

func (x *InvalidateSessionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *InvalidateSessionRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// InvalidateSessionResponse confirms session invalidation
type InvalidateSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvalidateSessionResponse) Reset() {
	*x = InvalidateSessionResponse{}
	mi := &file_proto_auth_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvalidateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateSessionResponse) ProtoMessage() {}

func (x *InvalidateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateSessionResponse.ProtoReflect.Descriptor instead.
func (*InvalidateSessionResponse) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{5}
}

func (x *InvalidateSessionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InvalidateSessionResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// RefreshTokenRequest contains the token to refresh
type RefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	mi := &file_proto_auth_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{6}
}

func (x *RefreshTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// RefreshTokenResponse contains the new token and session info
type RefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Session       *SessionInfo           `protobuf:"bytes,2,opt,name=session,proto3" json:"session,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResponse) Reset() {
	*x = RefreshTokenResponse{}
	mi := &file_proto_auth_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResponse) ProtoMessage() {}

func (x *RefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*RefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{7}
}

func (x *RefreshTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RefreshTokenResponse) GetSession() *SessionInfo {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *RefreshTokenResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetUserSessionsRequest contains the user ID
type GetUserSessionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSessionsRequest) Reset() {
	*x = GetUserSessionsRequest{}
	mi := &file_proto_auth_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionsRequest) ProtoMessage() {}

func (x *GetUserSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionsRequest.ProtoReflect.Descriptor instead.
func (*GetUserSessionsRequest) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserSessionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// GetUserSessionsResponse contains all user sessions
type GetUserSessionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sessions      []*SessionInfo         `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSessionsResponse) Reset() {
	*x = GetUserSessionsResponse{}
	mi := &file_proto_auth_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionsResponse) ProtoMessage() {}

func (x *GetUserSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionsResponse.ProtoReflect.Descriptor instead.
func (*GetUserSessionsResponse) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserSessionsResponse) GetSessions() []*SessionInfo {
	if x != nil {
		return x.Sessions
	}
	return nil
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Role          string                 `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	SessionId     string                 `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	DeviceId      string                 `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Jti           string                 `protobuf:"bytes,6,opt,name=jti,proto3" json:"jti,omitempty"`
	Roles         []string               `protobuf:"bytes,7,rep,name=roles,proto3" json:"roles,omitempty"`
	Issuer        string                 `protobuf:"bytes,8,opt,name=issuer,proto3" json:"issuer,omitempty"`
	Audience      []string               `protobuf:"bytes,9,rep,name=audience,proto3" json:"audience,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	IssuedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`
	NotBefore     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=not_before,json=notBefore,proto3" json:"not_before,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenClaims) Reset() {
	*x = TokenClaims{}
	mi := &file_proto_auth_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenClaims) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenClaims) ProtoMessage() {}

func (x *TokenClaims) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenClaims.ProtoReflect.Descriptor instead.
func (*TokenClaims) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{10}
}

func (x *TokenClaims) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TokenClaims) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TokenClaims) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *TokenClaims) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *TokenClaims) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *TokenClaims) GetJti() string {
	if x != nil {
		return x.Jti
	}
	return ""
}

func (x *TokenClaims) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *TokenClaims) GetIssuer() string {
	if x != nil {
		return x.Issuer
	}
	return ""
}

func (x *TokenClaims) GetAudience() []string {
	if x != nil {
		return x.Audience
	}
	return nil
}

func (x *TokenClaims) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *TokenClaims) GetIssuedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.IssuedAt
	}
	return nil
}

func (x *TokenClaims) GetNotBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.NotBefore
	}
	return nil
}

// SessionInfo represents session information
type SessionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Role          string                 `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	DeviceId      string                 `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	LastSeen      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	mi := &file_proto_auth_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_auth_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_proto_auth_proto_rawDescGZIP(), []int{11}
}

func (x *SessionInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SessionInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SessionInfo) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *SessionInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SessionInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SessionInfo) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *SessionInfo) GetLastSeen() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

var File_proto_auth_proto protoreflect.FileDescriptor

const file_proto_auth_proto_rawDesc = "" +
	"\n" +
	"\x10proto/auth.proto\x12\x04auth\x1a\x1fgoogle/protobuf/timestamp.proto\"*\n" +
	"\x12VerifyTokenRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\xb8\x01\n" +
	"\x13VerifyTokenResponse\x12\x14\n" +
	"\x05valid\x18\x01 \x01(\bR\x05valid\x12)\n" +
	"\x06claims\x18\x02 \x01(\v2\x11.auth.TokenClaimsR\x06claims\x12+\n" +
	"\asession\x18\x03 \x01(\v2\x11.auth.SessionInfoR\asession\x12\x14\n" +
	"\x05error\x18\x04 \x01(\tR\x05error\x12\x1d\n" +
	"\n" +
	"error_code\x18\x05 \x01(\tR\terrorCode\"|\n" +
	"\x14CreateSessionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x12\n" +
	"\x04role\x18\x03 \x01(\tR\x04role\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\"Z\n" +
	"\x15CreateSessionResponse\x12+\n" +
	"\asession\x18\x01 \x01(\v2\x11.auth.SessionInfoR\asession\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\"R\n" +
	"\x18InvalidateSessionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\"K\n" +
	"\x19InvalidateSessionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"+\n" +
	"\x13RefreshTokenRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"o\n" +
	"\x14RefreshTokenResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12+\n" +
	"\asession\x18\x02 \x01(\v2\x11.auth.SessionInfoR\asession\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"1\n" +
	"\x16GetUserSessionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"H\n" +
	"\x17GetUserSessionsResponse\x12-\n" +
	"\bsessions\x18\x01 \x03(\v2\x11.auth.SessionInfoR\bsessions\"\x9d\x03\n" +
	"\vTokenClaims\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x12\n" +
	"\x04role\x18\x03 \x01(\tR\x04role\x12\x1d\n" +
	"\n" +
	"session_id\x18\x04 \x01(\tR\tsessionId\x12\x1b\n" +
	"\tdevice_id\x18\x05 \x01(\tR\bdeviceId\x12\x10\n" +
	"\x03jti\x18\x06 \x01(\tR\x03jti\x12\x14\n" +
	"\x05roles\x18\a \x03(\tR\x05roles\x12\x16\n" +
	"\x06issuer\x18\b \x01(\tR\x06issuer\x12\x1a\n" +
	"\baudience\x18\t \x03(\tR\baudience\x129\n" +
	"\n" +
	"expires_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x127\n" +
	"\tissued_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\bissuedAt\x129\n" +
	"\n" +
	"not_before\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tnotBefore\"\xc1\x02\n" +
	"\vSessionInfo\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x12\n" +
	"\x04role\x18\x04 \x01(\tR\x04role\x12\x1b\n" +
	"\tdevice_id\x18\x05 \x01(\tR\bdeviceId\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"expires_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x127\n" +
	"\tlast_seen\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\blastSeen2\x88\x03\n" +
	"\vAuthService\x12B\n" +
	"\vVerifyToken\x12\x18.auth.VerifyTokenRequest\x1a\x19.auth.VerifyTokenResponse\x12H\n" +
	"\rCreateSession\x12\x1a.auth.CreateSessionRequest\x1a\x1b.auth.CreateSessionResponse\x12T\n" +
	"\x11InvalidateSession\x12\x1e.auth.InvalidateSessionRequest\x1a\x1f.auth.InvalidateSessionResponse\x12E\n" +
	"\fRefreshToken\x12\x19.auth.RefreshTokenRequest\x1a\x1a.auth.RefreshTokenResponse\x12N\n" +
	"\x0fGetUserSessions\x12\x1c.auth.GetUserSessionsRequest\x1a\x1d.auth.GetUserSessionsResponseB&Z$github.com/xzgame/auth-service/protob\x06proto3"

var (
	file_proto_auth_proto_rawDescOnce sync.Once
	file_proto_auth_proto_rawDescData []byte
)

func file_proto_auth_proto_rawDescGZIP() []byte {
	file_proto_auth_proto_rawDescOnce.Do(func() {
		file_proto_auth_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_auth_proto_rawDesc), len(file_proto_auth_proto_rawDesc)))
	})
	return file_proto_auth_proto_rawDescData
}

var file_proto_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_proto_auth_proto_goTypes = []any{
	(*VerifyTokenRequest)(nil),        // 0: auth.VerifyTokenRequest
	(*VerifyTokenResponse)(nil),       // 1: auth.VerifyTokenResponse
	(*CreateSessionRequest)(nil),      // 2: auth.CreateSessionRequest
	(*CreateSessionResponse)(nil),     // 3: auth.CreateSessionResponse
	(*InvalidateSessionRequest)(nil),  // 4: auth.InvalidateSessionRequest
	(*InvalidateSessionResponse)(nil), // 5: auth.InvalidateSessionResponse
	(*RefreshTokenRequest)(nil),       // 6: auth.RefreshTokenRequest
	(*RefreshTokenResponse)(nil),      // 7: auth.RefreshTokenResponse
	(*GetUserSessionsRequest)(nil),    // 8: auth.GetUserSessionsRequest
	(*GetUserSessionsResponse)(nil),   // 9: auth.GetUserSessionsResponse
	(*TokenClaims)(nil),               // 10: auth.TokenClaims
	(*SessionInfo)(nil),               // 11: auth.SessionInfo
	(*timestamppb.Timestamp)(nil),     // 12: google.protobuf.Timestamp
}
var file_proto_auth_proto_depIdxs = []int32{
	10, // 0: auth.VerifyTokenResponse.claims:type_name -> auth.TokenClaims
	11, // 1: auth.VerifyTokenResponse.session:type_name -> auth.SessionInfo
	11, // 2: auth.CreateSessionResponse.session:type_name -> auth.SessionInfo
	11, // 3: auth.RefreshTokenResponse.session:type_name -> auth.SessionInfo
	11, // 4: auth.GetUserSessionsResponse.sessions:type_name -> auth.SessionInfo
	12, // 5: auth.TokenClaims.expires_at:type_name -> google.protobuf.Timestamp
	12, // 6: auth.TokenClaims.issued_at:type_name -> google.protobuf.Timestamp
	12, // 7: auth.TokenClaims.not_before:type_name -> google.protobuf.Timestamp
	12, // 8: auth.SessionInfo.created_at:type_name -> google.protobuf.Timestamp
	12, // 9: auth.SessionInfo.expires_at:type_name -> google.protobuf.Timestamp
	12, // 10: auth.SessionInfo.last_seen:type_name -> google.protobuf.Timestamp
	0,  // 11: auth.AuthService.VerifyToken:input_type -> auth.VerifyTokenRequest
	2,  // 12: auth.AuthService.CreateSession:input_type -> auth.CreateSessionRequest
	4,  // 13: auth.AuthService.InvalidateSession:input_type -> auth.InvalidateSessionRequest
	6,  // 14: auth.AuthService.RefreshToken:input_type -> auth.RefreshTokenRequest
	8,  // 15: auth.AuthService.GetUserSessions:input_type -> auth.GetUserSessionsRequest
	1,  // 16: auth.AuthService.VerifyToken:output_type -> auth.VerifyTokenResponse
	3,  // 17: auth.AuthService.CreateSession:output_type -> auth.CreateSessionResponse
	5,  // 18: auth.AuthService.InvalidateSession:output_type -> auth.InvalidateSessionResponse
	7,  // 19: auth.AuthService.RefreshToken:output_type -> auth.RefreshTokenResponse
	9,  // 20: auth.AuthService.GetUserSessions:output_type -> auth.GetUserSessionsResponse
	16, // [16:21] is the sub-list for method output_type
	11, // [11:16] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_proto_auth_proto_init() }
func file_proto_auth_proto_init() {
	if File_proto_auth_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_auth_proto_rawDesc), len(file_proto_auth_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_auth_proto_goTypes,
		DependencyIndexes: file_proto_auth_proto_depIdxs,
		MessageInfos:      file_proto_auth_proto_msgTypes,
	}.Build()
	File_proto_auth_proto = out.File
	file_proto_auth_proto_goTypes = nil
	file_proto_auth_proto_depIdxs = nil
}
