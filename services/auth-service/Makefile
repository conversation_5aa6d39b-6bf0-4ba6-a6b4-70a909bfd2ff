# Auth Service Makefile

# Variables
GO := go
GOTEST := $(GO) test
GOBUILD := $(GO) build
GOCLEAN := $(GO) clean
GOMOD := $(GO) mod
PROTOC := protoc

# Directories
CMD_DIR := ./cmd/server
PROTO_DIR := ./proto
BUILD_DIR := ./build

# Binary name
BINARY_NAME := auth-service
BINARY_PATH := $(BUILD_DIR)/$(BINARY_NAME)

# Proto files
PROTO_FILES := $(wildcard $(PROTO_DIR)/*.proto)

# Docker settings
DOCKER_IMAGE := xzgame/auth-service
DOCKER_TAG := latest

.PHONY: help build clean test proto docker run dev deps lint fmt vet

# Default target
help: ## Show this help message
	@echo "Auth Service Makefile"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build targets
build: proto deps ## Build the auth service binary
	@echo "Building auth service..."
	@mkdir -p $(BUILD_DIR)
	@$(GOBUILD) -o $(BINARY_PATH) $(CMD_DIR)
	@echo "Binary built: $(BINARY_PATH)"

build-linux: proto deps ## Build for Linux
	@echo "Building auth service for Linux..."
	@mkdir -p $(BUILD_DIR)
	@GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_PATH)-linux $(CMD_DIR)
	@echo "Linux binary built: $(BINARY_PATH)-linux"

# Proto generation
proto: ## Generate Go code from proto files
	@echo "Generating protobuf code..."
	@$(PROTOC) --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		$(PROTO_FILES)
	@echo "Protobuf code generated"

# Dependencies
deps: ## Download and tidy dependencies
	@echo "Downloading dependencies..."
	@$(GOMOD) download
	@$(GOMOD) tidy

# Testing
test: ## Run tests
	@echo "Running tests..."
	@$(GOTEST) -v ./...

test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	@$(GOTEST) -v -coverprofile=coverage.out ./...
	@$(GO) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-race: ## Run tests with race detection
	@echo "Running tests with race detection..."
	@$(GOTEST) -v -race ./...

# Code quality
lint: ## Run linter
	@echo "Running linter..."
	@golangci-lint run ./...

fmt: ## Format code
	@echo "Formatting code..."
	@$(GO) fmt ./...

vet: ## Run go vet
	@echo "Running go vet..."
	@$(GO) vet ./...

# Development
dev: ## Run in development mode
	@echo "Starting auth service in development mode..."
	@ENVIRONMENT=development LOG_LEVEL=debug $(GO) run $(CMD_DIR)

run: build ## Build and run the service
	@echo "Starting auth service..."
	@$(BINARY_PATH)

# Docker
docker: ## Build Docker image
	@echo "Building Docker image..."
	@docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	@echo "Docker image built: $(DOCKER_IMAGE):$(DOCKER_TAG)"

docker-run: ## Run Docker container
	@echo "Running Docker container..."
	@docker run --rm -p 8081:8081 \
		-e REDIS_URL=redis://host.docker.internal:6379 \
		$(DOCKER_IMAGE):$(DOCKER_TAG)

# Cleanup
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	@$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html

# Health check
health: ## Check service health
	@echo "Checking service health..."
	@grpcurl -plaintext localhost:8081 grpc.health.v1.Health/Check

# Generate proto (alias)
generate: proto ## Alias for proto target

# Install tools
install-tools: ## Install development tools
	@echo "Installing development tools..."
	@$(GO) install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	@$(GO) install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	@$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@$(GO) install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

# All-in-one targets
all: clean deps proto build test ## Clean, build, and test everything

ci: deps proto lint vet test-race ## Run CI pipeline
