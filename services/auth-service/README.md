# Auth Service

A dedicated authentication and session management microservice for the XZ Game platform.

## Overview

The Auth Service handles JWT token verification, user session management, and authentication state for the XZ Game ecosystem. It provides a gRPC API for other services to verify tokens and manage user sessions.

## Features

- **JWT Token Verification**: Validates JWT tokens with comprehensive checks
- **Session Management**: Creates, tracks, and invalidates user sessions
- **Token Blacklisting**: Supports token revocation and blacklisting
- **Multi-Device Support**: Manages multiple sessions per user
- **Redis Storage**: Fast session storage and retrieval
- **gRPC API**: High-performance service-to-service communication
- **Health Checks**: Built-in health monitoring

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Game Service  │───▶│   Auth Service  │───▶│     Redis       │
│                 │    │                 │    │   (Sessions)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │
        │                       ▼
        │              ┌─────────────────┐
        └─────────────▶│  Socket Gateway │
                       │                 │
                       └─────────────────┘
```

## API Endpoints

### gRPC Service: `AuthService`

#### `VerifyToken`
Verifies and validates a JWT token.

**Request:**
```protobuf
message VerifyTokenRequest {
  string token = 1;
}
```

**Response:**
```protobuf
message VerifyTokenResponse {
  bool valid = 1;
  TokenClaims claims = 2;
  SessionInfo session = 3;
  string error = 4;
  string error_code = 5;
}
```

#### `CreateSession`
Creates a new user session and returns a JWT token.

**Request:**
```protobuf
message CreateSessionRequest {
  string user_id = 1;
  string username = 2;
  string role = 3;
  string device_id = 4;
}
```

#### `InvalidateSession`
Invalidates a user session.

#### `RefreshToken`
Refreshes an existing token.

#### `GetUserSessions`
Retrieves all active sessions for a user.

## Configuration

The service is configured via environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8081` | gRPC server port |
| `ENVIRONMENT` | `development` | Environment (development/production) |
| `LOG_LEVEL` | `info` | Logging level |
| `REDIS_URL` | `redis://localhost:6379` | Redis connection URL |
| `REDIS_PASSWORD` | `` | Redis password |
| `REDIS_DB` | `0` | Redis database number |
| `JWT_SECRET` | `your-secret-key` | JWT signing secret |
| `JWT_ISSUER` | `xzgame-auth-service` | JWT issuer |
| `JWT_AUDIENCE` | `xzgame-api,xzgame-game-service` | JWT audience (comma-separated) |
| `SESSION_TTL` | `24h` | Session time-to-live |
| `TOKEN_BLACKLIST_TTL` | `24h` | Token blacklist TTL |
| `MAX_SESSIONS_PER_USER` | `5` | Maximum sessions per user |

## Development

### Prerequisites

- Go 1.21+
- Redis
- Protocol Buffers compiler (`protoc`)
- gRPC tools

### Setup

1. **Install dependencies:**
   ```bash
   make install-tools
   make deps
   ```

2. **Generate protobuf code:**
   ```bash
   make proto
   ```

3. **Run in development mode:**
   ```bash
   make dev
   ```

### Building

```bash
# Build binary
make build

# Build for Linux
make build-linux

# Build Docker image
make docker
```

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make test-coverage

# Run tests with race detection
make test-race
```

### Code Quality

```bash
# Format code
make fmt

# Run linter
make vet lint

# Run all quality checks
make ci
```

## Deployment

### Docker

```bash
# Build image
make docker

# Run container
docker run -p 8081:8081 \
  -e REDIS_URL=redis://redis:6379 \
  -e JWT_SECRET=your-production-secret \
  xzgame/auth-service:latest
```

### Environment Variables for Production

```bash
export PORT=8081
export ENVIRONMENT=production
export LOG_LEVEL=info
export REDIS_URL=redis://redis-cluster:6379
export REDIS_PASSWORD=your-redis-password
export JWT_SECRET=your-very-secure-jwt-secret-at-least-32-chars
export JWT_ISSUER=xzgame-auth-service
export JWT_AUDIENCE=xzgame-api,xzgame-game-service
export SESSION_TTL=24h
export MAX_SESSIONS_PER_USER=5
```

## Integration

### From Game Service

```go
// Connect to auth service
conn, err := grpc.Dial("auth-service:8081", grpc.WithInsecure())
client := pb.NewAuthServiceClient(conn)

// Verify token
resp, err := client.VerifyToken(ctx, &pb.VerifyTokenRequest{
    Token: "jwt-token-here",
})

if resp.Valid {
    // Token is valid, use resp.Claims and resp.Session
    userID := resp.Claims.UserId
    username := resp.Claims.Username
}
```

### Health Checks

```bash
# Check service health
grpcurl -plaintext localhost:8081 grpc.health.v1.Health/Check
```

## Security Considerations

- **JWT Secret**: Use a strong, randomly generated secret (minimum 32 characters)
- **Redis Security**: Secure Redis with password and network isolation
- **Session Management**: Sessions are automatically cleaned up on expiration
- **Token Blacklisting**: Invalidated tokens are blacklisted to prevent reuse
- **Audit Logging**: All authentication events are logged for security monitoring

## Monitoring

The service provides:

- **Health Checks**: gRPC health check endpoint
- **Metrics**: Prometheus-compatible metrics (if enabled)
- **Logging**: Structured JSON logging with correlation IDs
- **Tracing**: OpenTelemetry tracing support (if configured)

## Migration from Game Service

This service extracts authentication logic from the monolithic game-service:

1. **Extracted Components:**
   - `internal/services/auth_service.go` → Auth Service
   - JWT token verification logic
   - Session management functionality

2. **Benefits:**
   - Independent scaling of authentication
   - Centralized auth logic for all services
   - Improved security isolation
   - Simplified game service codebase

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis URL and credentials
   - Verify Redis is running and accessible

2. **Token Verification Failed**
   - Verify JWT secret matches across services
   - Check token expiration and format

3. **Session Not Found**
   - Check Redis for session data
   - Verify session hasn't expired

### Debug Mode

Run with debug logging:
```bash
LOG_LEVEL=debug make dev
```
