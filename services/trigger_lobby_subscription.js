/**
 * Trigger Lobby Subscription Test
 * 
 * Simple test to trigger a lobby subscription and see the Manager Service call
 */

const io = require('socket.io-client');
const axios = require('axios');

// Configuration
const CONFIG = {
  API_GATEWAY_URL: 'http://localhost:3000',
  SOCKET_GATEWAY_URL: 'http://localhost:3001',
  TEST_USER: {
    username: 'admin',
    password: 'admin123456',
  },
};

async function triggerLobbySubscription() {
  try {
    console.log('🚀 Triggering lobby subscription...');
    
    // Step 1: Get token
    console.log('🔐 Getting JWT token...');
    const loginResponse = await axios.post(`${CONFIG.API_GATEWAY_URL}/api/v1/auth/login`, {
      username: CONFIG.TEST_USER.username,
      password: CONFIG.TEST_USER.password,
    });

    if (!loginResponse.data.success) {
      throw new Error('<PERSON><PERSON> failed');
    }

    const token = loginResponse.data.data.token;
    const userId = loginResponse.data.data.user.id;
    console.log(`✅ Login successful! User ID: ${userId}`);

    // Step 2: Connect to Socket Gateway
    console.log('🔌 Connecting to Socket Gateway...');
    
    const socket = io(CONFIG.SOCKET_GATEWAY_URL, {
      auth: { token },
      transports: ['websocket'],
      timeout: 10000,
    });

    // Wait for connection and authentication
    await new Promise((resolve, reject) => {
      let authReceived = false;
      
      socket.on('connect', () => {
        console.log(`✅ Socket connected! ID: ${socket.id}`);
      });

      socket.on('auth_success', (data) => {
        console.log(`✅ Socket authenticated! User: ${data.username} (${data.userId})`);
        authReceived = true;
        resolve();
      });

      socket.on('connect_error', (error) => {
        reject(new Error(`Connection failed: ${error.message}`));
      });

      // Timeout
      setTimeout(() => {
        if (!authReceived) {
          reject(new Error('Authentication timeout'));
        }
      }, 15000);
    });

    // Step 3: Subscribe to lobby
    console.log('📡 Subscribing to lobby...');
    
    // Listen for room list updates
    socket.on('room_list_updated', (data) => {
      console.log('📊 Received room_list_updated event!');
      console.log(`   Action: ${data.action}`);
      console.log(`   Room count: ${data.rooms ? data.rooms.length : 0}`);
      console.log(`   Source: ${data.source || 'unknown'}`);
      console.log(`   From Manager Service: ${data.source === 'manager-service' ? 'YES' : 'NO'}`);
      
      if (data.rooms && data.rooms.length > 0) {
        console.log('🏠 Rooms received:');
        data.rooms.forEach((room, index) => {
          console.log(`   ${index + 1}. ${room.name} (${room.game_type}) - ${room.current_players}/${room.max_players} players`);
        });
      }
      
      // Success! Disconnect
      setTimeout(() => {
        socket.disconnect();
        console.log('✅ Test completed successfully!');
        process.exit(0);
      }, 1000);
    });

    // Trigger lobby subscription
    socket.emit('subscribe_lobby', (response) => {
      console.log('📡 Lobby subscription response:', response);
    });

    // Timeout
    setTimeout(() => {
      console.log('⚠️ No room_list_updated event received within 10 seconds');
      socket.disconnect();
      process.exit(1);
    }, 10000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('🛑 Test interrupted by user');
  process.exit(1);
});

// Run the test
triggerLobbySubscription();
