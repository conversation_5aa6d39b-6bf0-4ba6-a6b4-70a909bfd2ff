# Environment Configuration for API Gateway Service

# Application
NODE_ENV=development
PORT=3000

# Database
MONGODB_URL=mongodb://localhost:27017/xzgame

# Redis
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=xzgame-auth-service
JWT_AUDIENCE=xzgame-api,xzgame-game-service

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# gRPC Services
GAME_SERVICE_URL=localhost:50051
MANAGER_SERVICE_URL=localhost:50052

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILENAME=logs/api-gateway.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
LOG_CONSOLE_ENABLED=true
LOG_COLORIZE=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here
CSRF_ENABLED=false
HTTPS_ONLY=false

# Validation
MAX_REQUEST_SIZE=10mb
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif
MAX_FILE_SIZE=5242880

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
PROMETHEUS_ENABLED=true

# Pagination
DEFAULT_PAGE_LIMIT=20
MAX_PAGE_LIMIT=100

# Cache TTL (seconds)
CACHE_DEFAULT_TTL=300
CACHE_USER_PROFILE_TTL=600
CACHE_GAME_CONFIG_TTL=1800

# Feature Flags
SWAGGER_ENABLED=true
METRICS_ENDPOINT_ENABLED=true
DEBUG_MODE=false
MAINTENANCE_MODE=false
