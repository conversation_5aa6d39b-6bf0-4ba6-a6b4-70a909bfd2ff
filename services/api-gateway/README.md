# API Gateway Service

## Overview

The API Gateway service handles HTTP requests for non-real-time operations and serves as the main entry point for REST API calls. It provides authentication, rate limiting, request validation, and routing to appropriate microservices.

## Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js 4.x
- **Authentication**: JWT tokens
- **Validation**: Joi/Express-validator
- **Documentation**: Swagger/OpenAPI
- **Communication**: gRPC for internal services
- **Cache**: Redis
- **Database**: MongoDB (via Mongoose)

## Key Features

- RESTful API endpoints
- JWT-based authentication and authorization
- Rate limiting with configurable thresholds
- Request validation and sanitization
- API versioning
- Swagger/OpenAPI documentation
- gRPC communication with internal services
- Comprehensive error handling

## ✅ Implementation Status

**COMPLETED**: Full API Gateway implementation with the following components:

### Core Infrastructure ✅
- Express.js application setup with middleware stack
- MongoDB connection with health checks and indexing
- Redis connection with caching utilities
- Comprehensive logging with Winston
- Prometheus metrics collection
- Docker containers (development and production)

### Authentication & Security ✅
- JWT token generation and validation
- Role-based access control (RBAC)
- Permission-based authorization
- Token blacklisting for logout
- Rate limiting with Redis backend
- Input validation and sanitization
- Security headers with Helmet

### API Routes ✅
- **Authentication**: Register, login, logout, token refresh
- **Users**: Profile management, balance queries, settings
- **Transactions**: History, deposits, withdrawals
- **Rooms**: Create, join, leave, update, delete
- **Games**: History, statistics, result validation
- **System**: Health checks, metrics, service info

### gRPC Integration ✅
- Game Service client with connection management
- Manager Service client with retry logic
- Protocol buffer definitions
- Error handling and circuit breaker patterns
- Metrics collection for gRPC calls

### Middleware Stack ✅
- Authentication middleware with JWT validation
- Validation middleware with Joi schemas
- Error handling with custom error classes
- Metrics middleware for Prometheus
- Rate limiting with configurable thresholds
- Request logging and correlation IDs

### Testing & Documentation ✅
- Test setup with in-memory databases
- Integration tests for health endpoints
- Swagger/OpenAPI documentation
- Environment configuration examples
- Docker development and production builds

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB running on localhost:27017
- Redis running on localhost:6379
- Game Service running on localhost:50051
- Manager Service running on localhost:50052

### Installation & Setup

1. **Install dependencies**:
   ```bash
   cd services/api-gateway
   npm install
   ```

2. **Environment configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Access the service**:
   - API: http://localhost:3000/api/v1
   - Health: http://localhost:3000/health
   - Docs: http://localhost:3000/api-docs
   - Metrics: http://localhost:3000/metrics

### Docker Development

```bash
# Build development image
npm run docker:build:dev

# Run with Docker
npm run docker:run:dev
```

### Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suites
npm run test:unit
npm run test:integration
```

## Project Structure

```
api-gateway/
├── src/
│   ├── controllers/        # HTTP request controllers
│   ├── middleware/         # Authentication, validation, rate limiting
│   ├── models/            # Database models
│   ├── routes/            # API route definitions
│   ├── services/          # Business logic and gRPC clients
│   ├── utils/             # Utility functions
│   ├── validators/        # Request validation schemas
│   └── app.js             # Main application file
├── tests/                 # Test files
├── config/                # Configuration files
├── docs/                  # API documentation
├── logs/                  # Log files
├── package.json
├── Dockerfile.dev
├── Dockerfile.prod
└── README.md
```

## Environment Variables

```bash
NODE_ENV=development|production
PORT=3000
JWT_SECRET=your_jwt_secret_here
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/xzgame
GAME_SERVICE_URL=http://game-service:8080
MANAGER_SERVICE_URL=http://manager-service:3002
CORS_ORIGINS=http://localhost:3000
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
LOG_LEVEL=info|debug|error
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh JWT token

### User Management
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/balance` - Get user balance
- `PUT /api/v1/users/settings` - Update user settings

### Transactions
- `GET /api/v1/transactions` - Get transaction history
- `POST /api/v1/transactions/deposit` - Create deposit
- `POST /api/v1/transactions/withdrawal` - Create withdrawal
- `GET /api/v1/transactions/:id` - Get transaction details

### Rooms
- `GET /api/v1/rooms` - List available rooms
- `POST /api/v1/rooms` - Create new room
- `GET /api/v1/rooms/:id` - Get room details
- `PUT /api/v1/rooms/:id` - Update room settings
- `DELETE /api/v1/rooms/:id` - Delete room

### Game History
- `GET /api/v1/games/history` - Get game history
- `GET /api/v1/games/:id` - Get game details

### System
- `GET /health` - Health check
- `GET /metrics` - Prometheus metrics
- `GET /api-docs` - Swagger documentation

## Development

### Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Generate API documentation
npm run docs:generate
```

### Testing
```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# API tests
npm run test:api

# Coverage report
npm run test:coverage
```

## Authentication

### JWT Token Structure
```javascript
{
  "userId": "ObjectId",
  "username": "string",
  "roles": ["player", "admin"],
  "iat": **********,
  "exp": **********
}
```

### Protected Routes
All routes except authentication endpoints require valid JWT tokens in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Rate Limiting

### Default Limits
- Authentication endpoints: 5 requests per minute
- General API endpoints: 100 requests per minute
- Admin endpoints: 200 requests per minute

### Custom Limits
Rate limits can be configured per endpoint and user role.

## Validation

### Request Validation
All incoming requests are validated using Joi schemas:
- Parameter validation
- Body validation
- Query parameter validation
- File upload validation

### Error Responses
```javascript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": {
      "field": "Field-specific error message"
    }
  }
}
```

## gRPC Communication

### Service Clients
- Game Service client for game operations
- Manager Service client for admin operations

### Protocol Buffers
Proto files are shared across services for consistent communication.

## Monitoring

### Metrics
- Request count and response times
- Error rates by endpoint
- Authentication success/failure rates
- Rate limiting violations
- gRPC call metrics

### Health Checks
- Database connectivity
- Redis connectivity
- gRPC service availability
- Memory and CPU usage

## Security

### Input Validation
- SQL injection prevention
- XSS protection
- CSRF protection
- File upload restrictions

### Rate Limiting
- Per-IP rate limiting
- Per-user rate limiting
- Endpoint-specific limits
- DDoS protection

### CORS Configuration
Configurable CORS origins for cross-origin requests.

## Deployment

### Docker Development
```bash
docker build -f Dockerfile.dev -t xzgame-api-gateway:dev .
docker run -p 3000:3000 --env-file .env.dev xzgame-api-gateway:dev
```

### Docker Production
```bash
docker build -f Dockerfile.prod -t xzgame-api-gateway:prod .
docker run -p 3000:3000 --env-file .env.prod xzgame-api-gateway:prod
```

## API Documentation

### Swagger UI
Access interactive API documentation at `/api-docs` when the service is running.

### OpenAPI Specification
The OpenAPI specification is automatically generated from route definitions and validation schemas.

## Troubleshooting

### Common Issues
1. **Authentication Failures**: Check JWT secret and token expiration
2. **Rate Limiting**: Monitor rate limit headers and adjust limits
3. **Validation Errors**: Check request format and required fields
4. **gRPC Errors**: Verify service connectivity and proto definitions

### Debug Mode
```bash
DEBUG=express:* npm run dev
```

### Logs
```bash
# View real-time logs
tail -f logs/api-gateway.log

# Search for specific errors
grep "ERROR" logs/api-gateway.log | grep "authentication"
```
