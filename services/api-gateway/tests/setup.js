const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const Redis = require('redis');

// Global test setup
let mongoServer;
let redisServer;

beforeAll(async () => {
  // Setup in-memory MongoDB
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  // Setup Redis mock or in-memory Redis
  // For now, we'll mock Redis operations
  jest.mock('../src/config/redis', () => ({
    connectRedis: jest.fn(),
    disconnectRedis: jest.fn(),
    getClient: jest.fn(() => ({
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      incr: jest.fn(),
      mGet: jest.fn(),
      mSet: jest.fn(),
    })),
    isConnectedToRedis: jest.fn(() => true),
    healthCheck: jest.fn(() => Promise.resolve({ status: 'healthy' })),
    cache: {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      incr: jest.fn(),
      mget: jest.fn(),
      mset: jest.fn(),
    },
  }));

  // Mock gRPC clients
  jest.mock('../src/services/grpc/gameServiceClient', () => ({
    initialize: jest.fn(),
    close: jest.fn(),
    healthCheck: jest.fn(() => Promise.resolve({ status: 'healthy' })),
    createRoom: jest.fn(),
    joinRoom: jest.fn(),
    leaveRoom: jest.fn(),
    startGame: jest.fn(),
    getGameState: jest.fn(),
    getGameHistory: jest.fn(),
    validateGameResult: jest.fn(),
    getRooms: jest.fn(),
    getRoomDetails: jest.fn(),
    updateRoom: jest.fn(),
    deleteRoom: jest.fn(),
  }));

  jest.mock('../src/services/grpc/managerServiceClient', () => ({
    initialize: jest.fn(),
    close: jest.fn(),
    healthCheck: jest.fn(() => Promise.resolve({ status: 'healthy' })),
    createUser: jest.fn(),
    getUserById: jest.fn(),
    getUserByUsername: jest.fn(),
    updateUserProfile: jest.fn(),
    updateUserSettings: jest.fn(),
    deleteUser: jest.fn(),
    getUserBalance: jest.fn(),
    updateBalance: jest.fn(),
    processTransaction: jest.fn(),
    getUserTransactions: jest.fn(),
    getTransactionById: jest.fn(),
    validateUser: jest.fn(),
    createDeposit: jest.fn(),
    createWithdrawal: jest.fn(),
  }));
});

afterAll(async () => {
  // Cleanup
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  if (mongoServer) {
    await mongoServer.stop();
  }
});

afterEach(async () => {
  // Clear all collections after each test
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }

  // Clear all mocks
  jest.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: '507f1f77bcf86cd799439011',
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['player'],
    permissions: ['game:join', 'room:create'],
    isActive: true,
    profile: {
      firstName: 'Test',
      lastName: 'User',
    },
    balance: {
      current: 1000,
      locked: 0,
      currency: 'USD',
    },
  }),

  createMockJWT: () => 'mock.jwt.token',

  createMockRoom: () => ({
    id: '507f1f77bcf86cd799439012',
    name: 'Test Room',
    gameType: 'GAME_TYPE_PRIZEWHEEL',
    status: 'ROOM_STATUS_WAITING',
    players: [],
    config: {
      maxPlayers: 8,
      betAmount: 100,
      currency: 'USD',
    },
    creatorId: '507f1f77bcf86cd799439011',
  }),

  createMockTransaction: () => ({
    id: '507f1f77bcf86cd799439013',
    userId: '507f1f77bcf86cd799439011',
    amount: 100,
    type: 'deposit',
    status: 'completed',
    reason: 'User deposit',
    createdAt: new Date().toISOString(),
  }),
};

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.MONGODB_URL = 'mongodb://localhost:27017/xzgame_test';
process.env.REDIS_URL = 'redis://localhost:6379';

// Suppress console logs during tests
if (process.env.SUPPRESS_LOGS === 'true') {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
}
