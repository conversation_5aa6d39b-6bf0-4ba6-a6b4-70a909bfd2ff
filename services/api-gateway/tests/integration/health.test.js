const request = require('supertest');
const app = require('../../src/app');

describe('Health Endpoints', () => {
  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('timestamp');
      expect(response.body.data).toHaveProperty('version');
      expect(response.body.data).toHaveProperty('environment');
      expect(response.body.data).toHaveProperty('uptime');
      expect(response.body.data).toHaveProperty('services');
    });

    it('should include service health checks', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      const { services } = response.body.data;
      
      expect(services).toHaveProperty('database');
      expect(services).toHaveProperty('redis');
      expect(services).toHaveProperty('gameService');
      expect(services).toHaveProperty('managerService');
    });

    it('should include system information', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      const { system } = response.body.data;
      
      expect(system).toHaveProperty('memory');
      expect(system).toHaveProperty('cpu');
      expect(system).toHaveProperty('node');
      expect(system.memory).toHaveProperty('used');
      expect(system.memory).toHaveProperty('total');
      expect(system.node).toHaveProperty('version');
      expect(system.node).toHaveProperty('platform');
    });
  });

  describe('GET /health/ready', () => {
    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status', 'ready');
      expect(response.body.data).toHaveProperty('timestamp');
    });
  });

  describe('GET /health/live', () => {
    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status', 'alive');
      expect(response.body.data).toHaveProperty('timestamp');
      expect(response.body.data).toHaveProperty('uptime');
    });
  });

  describe('GET /info', () => {
    it('should return service information', async () => {
      const response = await request(app)
        .get('/info')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      const { data } = response.body;
      expect(data).toHaveProperty('service', 'api-gateway');
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('environment');
      expect(data).toHaveProperty('node');
      expect(data).toHaveProperty('uptime');
      expect(data).toHaveProperty('features');
      expect(data).toHaveProperty('endpoints');
    });
  });

  describe('GET /version', () => {
    it('should return version information', async () => {
      const response = await request(app)
        .get('/version')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      const { data } = response.body;
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('buildDate');
      expect(data).toHaveProperty('gitCommit');
      expect(data).toHaveProperty('gitBranch');
    });
  });

  describe('GET /metrics', () => {
    it('should return prometheus metrics when enabled', async () => {
      const response = await request(app)
        .get('/metrics')
        .expect(200);

      expect(response.headers['content-type']).toMatch(/text\/plain/);
      expect(response.text).toContain('api_gateway_');
    });
  });

  describe('Error handling', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'NOT_FOUND');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error).toHaveProperty('status_code', 404);
    });

    it('should include request ID in error responses', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body.error).toHaveProperty('request_id');
      expect(typeof response.body.error.request_id).toBe('string');
    });

    it('should handle custom request ID header', async () => {
      const customRequestId = 'custom-request-id-123';
      
      const response = await request(app)
        .get('/unknown-route')
        .set('X-Request-ID', customRequestId)
        .expect(404);

      expect(response.body.error.request_id).toBe(customRequestId);
      expect(response.headers['x-request-id']).toBe(customRequestId);
    });
  });
});
