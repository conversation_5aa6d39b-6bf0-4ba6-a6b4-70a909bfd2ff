{"name": "xzgame-api-gateway", "version": "1.0.0", "description": "API Gateway service for XZ Game backend - handles HTTP requests and routing to microservices", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:api": "jest --testPathPattern=tests/api", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "docs:generate": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o docs/swagger.json", "docs:serve": "swagger-ui-serve docs/swagger.json", "build": "echo 'No build step required for Node.js'", "docker:build:dev": "docker build -f Dockerfile.dev -t xzgame-api-gateway:dev .", "docker:build:prod": "docker build -f Dockerfile.prod -t xzgame-api-gateway:prod .", "docker:run:dev": "docker run -p 3000:3000 --env-file .env.dev xzgame-api-gateway:dev", "docker:run:prod": "docker run -p 3000:3000 --env-file .env.prod xzgame-api-gateway:prod"}, "keywords": ["api-gateway", "express", "microservices", "gaming", "grpc", "jwt", "redis", "mongodb"], "author": "XZ Game Development Team", "license": "MIT", "dependencies": {"@grpc/grpc-js": "^1.9.14", "@grpc/proto-loader": "^0.7.10", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "node-cron": "^3.0.3", "prom-client": "^15.1.0", "redis": "^4.6.11", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.6.0", "faker": "^6.6.6", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nock": "^13.4.0", "nodemon": "^3.0.2", "redis-memory-server": "^0.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/xzgame/backend.git", "directory": "services/api-gateway"}, "bugs": {"url": "https://github.com/xzgame/backend/issues"}, "homepage": "https://github.com/xzgame/backend#readme", "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"extends": ["airbnb-base"], "env": {"node": true, "jest": true}, "rules": {"no-console": "warn", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "consistent-return": "off"}}}