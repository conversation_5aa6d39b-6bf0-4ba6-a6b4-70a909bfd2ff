{"openapi": "3.0.0", "info": {"title": "XZ Game API Gateway", "description": "API Gateway service for XZ Game backend - handles HTTP requests and routing to microservices", "version": "1.0.0", "contact": {"name": "XZ Game Development Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.xzgame.com", "description": "Production server"}], "paths": {"/health": {"get": {"tags": ["System"], "summary": "Health check endpoint", "description": "Returns the health status of the API Gateway and its dependencies", "responses": {"200": {"description": "Service is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "503": {"description": "Service is unhealthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user account", "description": "Creates a new user account with the provided information", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "Authenticate user and receive JWT token", "description": "Authenticates a user with username/password and returns JWT tokens", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Authentication successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"HealthResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"status": {"type": "string", "enum": ["healthy", "unhealthy"]}, "timestamp": {"type": "string", "format": "date-time"}, "version": {"type": "string"}, "environment": {"type": "string"}, "uptime": {"type": "number"}, "services": {"type": "object"}}}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "status_code": {"type": "integer"}, "timestamp": {"type": "string", "format": "date-time"}, "request_id": {"type": "string"}}}}}, "RegisterRequest": {"type": "object", "required": ["username", "email", "password", "firstName", "lastName", "dateOfBirth", "country"], "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 30}, "email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "firstName": {"type": "string", "minLength": 1, "maxLength": 50}, "lastName": {"type": "string", "minLength": 1, "maxLength": 50}, "dateOfBirth": {"type": "string", "format": "date"}, "country": {"type": "string", "minLength": 2, "maxLength": 2}}}, "LoginRequest": {"type": "object", "required": ["username", "password"], "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}, "AuthResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string"}, "refreshToken": {"type": "string"}}}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "profile": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "avatar": {"type": "string"}, "country": {"type": "string"}}}, "balance": {"type": "object", "properties": {"current": {"type": "number"}, "locked": {"type": "number"}, "currency": {"type": "string"}}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"bearerAuth": []}], "tags": [{"name": "System", "description": "System endpoints for health checks and monitoring"}, {"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management operations"}, {"name": "Transactions", "description": "Transaction management and history"}, {"name": "Rooms", "description": "Game room management"}, {"name": "Games", "description": "Game history and statistics"}]}