const jwt = require('jsonwebtoken');
const config = require('../config');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');

/**
 * Extract token from request headers
 * @param {Object} req - Express request object
 * @returns {string|null} - JWT token or null
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');

  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
};

/**
 * Verify JWT token (API Gateway local verification)
 * @param {string} token - JWT token
 * @returns {Promise<Object>} - Decoded token payload
 */
const verifyToken = async (token) => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret, {
      issuer: config.jwt.issuer, // Use config issuer
      audience: config.jwt.audience, // Use config audience
    });

    return decoded;
  } catch (error) {
    logger.logAuth('token_verification_failed', null, null, false, error.message);
    throw new AuthenticationError('Invalid or expired token');
  }
};

/**
 * Check if token is blacklisted
 * @param {string} jti - JWT ID
 * @returns {Promise<boolean>} - True if blacklisted
 */
const isTokenBlacklisted = async (jti) => {
  try {
    const blacklisted = await cache.exists(`blacklist:${jti}`);
    return blacklisted;
  } catch (error) {
    logger.error('Failed to check token blacklist:', error);
    // Fail open - don't block requests if Redis is down
    return false;
  }
};

/**
 * Get user from cache or database
 * @param {string} userId - User ID
 * @param {string} userToken - Optional user token for authenticated calls
 * @returns {Promise<Object|null>} - User object or null
 */
const getUser = async (userId, userToken = null) => {
  try {
    // Try cache first
    const cacheKey = `user:${userId}`;
    let user = await cache.get(cacheKey);

    if (user) {
      logger.logCacheOperation('get', cacheKey, true);
      return user;
    }

    // Cache miss - fetch from manager service as fallback
    logger.logCacheOperation('get', cacheKey, false);
    logger.warn('User not found in cache - fetching from manager service', { userId });

    // Fallback: Fetch user from manager service
    const managerServiceClient = require('../services/grpc/managerServiceClient');
    const getUserRequest = { userId };
    const userResponse = await managerServiceClient.getUserById(getUserRequest);

    if (!userResponse.success || !userResponse.user) {
      logger.error('Failed to fetch user from manager service', { userId, error: userResponse.error });
      return null;
    }

    // Cache the user data for future requests
    user = userResponse.user;
    await cache.set(cacheKey, user, config.cache.userProfileTTL);
    logger.info('User data fetched from manager service and cached', { userId, username: user.username });

    return user;
  } catch (error) {
    logger.error('Failed to get user:', error);
    return null;
  }
};

/**
 * Authentication middleware (API Gateway local verification)
 *
 * NOTE: This middleware is used for API Gateway endpoints only.
 * Socket Gateway uses Game Service for token verification to prevent duplicates.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const authMiddleware = async (req, res, next) => {
  try {
    // Extract token
    const token = extractToken(req);

    if (!token) {
      throw new AuthenticationError('Authentication token required');
    }

    // Verify token locally (API Gateway verification)
    const decoded = await verifyToken(token);

    // Check if token is blacklisted
    if (decoded.jti && await isTokenBlacklisted(decoded.jti)) {
      throw new AuthenticationError('Token has been revoked');
    }

    // Get user details
    const user = await getUser(decoded.sub || decoded.userId, token);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      throw new AuthenticationError('User account is disabled');
    }

    // Attach user and token info to request
    req.user = user;
    req.token = {
      jti: decoded.jti,
      iat: decoded.iat,
      exp: decoded.exp,
      sessionId: decoded.sessionId, // Use camelCase to match token format
      deviceId: decoded.device_id,
    };

    // Log successful authentication
    logger.logAuth('token_verified', user.id, user.username, true);

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware (doesn't throw if no token)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const token = extractToken(req);

    if (!token) {
      return next();
    }

    // Try to verify token, but don't throw if invalid
    try {
      const decoded = await verifyToken(token);

      if (decoded.jti && await isTokenBlacklisted(decoded.jti)) {
        return next();
      }

      const user = await getUser(decoded.sub || decoded.userId, token);

      if (user && user.isActive) {
        req.user = user;
        req.token = {
          jti: decoded.jti,
          iat: decoded.iat,
          exp: decoded.exp,
          sessionId: decoded.sessionId, // Use camelCase to match token format
          deviceId: decoded.device_id,
        };
      }
    } catch (error) {
      // Ignore authentication errors for optional auth
      logger.debug('Optional auth failed:', error.message);
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Role-based authorization middleware
 * @param {string[]} allowedRoles - Array of allowed roles
 * @returns {Function} - Middleware function
 */
const requireRoles = (allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userRoles = req.user.roles || [];
      const hasRequiredRole = allowedRoles.some(role => userRoles.includes(role));

      if (!hasRequiredRole) {
        logger.logAuth(
          'authorization_failed',
          req.user.id,
          req.user.username,
          false,
          `Required roles: ${allowedRoles.join(', ')}, User roles: ${userRoles.join(', ')}`
        );
        throw new AuthorizationError(`Requires one of the following roles: ${allowedRoles.join(', ')}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Permission-based authorization middleware
 * @param {string[]} requiredPermissions - Array of required permissions
 * @returns {Function} - Middleware function
 */
const requirePermissions = (requiredPermissions) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userPermissions = req.user.permissions || [];
      const hasAllPermissions = requiredPermissions.every(permission =>
        userPermissions.includes(permission)
      );

      if (!hasAllPermissions) {
        logger.logAuth(
          'authorization_failed',
          req.user.id,
          req.user.username,
          false,
          `Required permissions: ${requiredPermissions.join(', ')}, User permissions: ${userPermissions.join(', ')}`
        );
        throw new AuthorizationError(`Missing required permissions: ${requiredPermissions.join(', ')}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Admin-only middleware
 */
const requireAdmin = requireRoles(['admin']);

/**
 * Player or admin middleware
 */
const requirePlayer = requireRoles(['player', 'admin']);

/**
 * Blacklist a token
 * @param {string} jti - JWT ID
 * @param {number} exp - Token expiration timestamp
 * @returns {Promise<void>}
 */
const blacklistToken = async (jti, exp) => {
  try {
    if (!jti) {
      return;
    }

    const ttl = exp - Math.floor(Date.now() / 1000);

    if (ttl > 0) {
      await cache.set(`blacklist:${jti}`, true, ttl);
      logger.info(`Token blacklisted: ${jti}`);
    }
  } catch (error) {
    logger.error('Failed to blacklist token:', error);
  }
};

module.exports = {
  authMiddleware,
  optionalAuthMiddleware,
  requireRoles,
  requirePermissions,
  requireAdmin,
  requirePlayer,
  blacklistToken,
  extractToken,
  verifyToken,
};
