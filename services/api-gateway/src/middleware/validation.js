const Joi = require('joi');
const { ValidationError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Common validation schemas
 */
const commonSchemas = {
  objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).message('Invalid ObjectId format'),
  email: Joi.string().email().lowercase().trim(),
  password: Joi.string().min(6).max(128).pattern(/^(?=.*[a-z])(?=.*\d)/)
    .message('Password must contain at least 6 characters with lowercase letters and numbers'),
  username: Joi.string().alphanum().min(3).max(30).lowercase().trim(),
  currency: Joi.string().valid('USD', 'EUR', 'GBP').default('USD'),
  gameType: Joi.string().valid('prizewheel', 'amidakuji'),
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
  },
  dateRange: {
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')),
  },
};

/**
 * Validation middleware factory
 * @param {Object} schema - Joi validation schema
 * @param {string} source - Source of data to validate ('body', 'query', 'params')
 * @returns {Function} - Express middleware function
 */
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    try {
      const dataToValidate = req[source];
      
      const { error, value } = schema.validate(dataToValidate, {
        abortEarly: false,
        allowUnknown: false,
        stripUnknown: true,
      });

      if (error) {
        const details = {};
        error.details.forEach((detail) => {
          const key = detail.path.join('.');
          details[key] = detail.message;
        });

        logger.warn('Validation failed', {
          source,
          errors: details,
          requestId: req.requestId,
        });

        throw new ValidationError('Request validation failed', details);
      }

      // Replace the original data with validated and sanitized data
      req[source] = value;
      next();
    } catch (err) {
      next(err);
    }
  };
};

/**
 * Validate request body
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
const validateBody = (schema) => validate(schema, 'body');

/**
 * Validate query parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
const validateQuery = (schema) => validate(schema, 'query');

/**
 * Validate route parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
const validateParams = (schema) => validate(schema, 'params');

/**
 * Authentication validation schemas
 */
const authSchemas = {
  register: Joi.object({
    username: commonSchemas.username.required(),
    email: commonSchemas.email.required(),
    password: commonSchemas.password.required(),
    firstName: Joi.string().trim().min(1).max(50).required(),
    lastName: Joi.string().trim().min(1).max(50).required(),
    dateOfBirth: Joi.date().max('now').required(),
    country: Joi.string().length(2).uppercase().required(),
  }),

  login: Joi.object({
    username: Joi.string().trim().required(),
    password: Joi.string().required(),
  }),

  refreshToken: Joi.object({
    refreshToken: Joi.string().required(),
  }),
};

/**
 * User validation schemas
 */
const userSchemas = {
  updateProfile: Joi.object({
    firstName: Joi.string().trim().min(1).max(50),
    lastName: Joi.string().trim().min(1).max(50),
    avatar: Joi.string().uri(),
    country: Joi.string().length(2).uppercase(),
    settings: Joi.object({
      notifications: Joi.boolean(),
      soundEnabled: Joi.boolean(),
      language: Joi.string().valid('en', 'es', 'fr', 'de', 'zh'),
      theme: Joi.string().valid('light', 'dark'),
    }),
  }),

  getUserById: Joi.object({
    userId: commonSchemas.objectId.required(),
  }),
};

/**
 * Transaction validation schemas
 */
const transactionSchemas = {
  getTransactions: Joi.object({
    ...commonSchemas.pagination,
    type: Joi.string().valid('deposit', 'withdrawal', 'bet', 'win', 'refund'),
    status: Joi.string().valid('pending', 'completed', 'failed', 'cancelled'),
    ...commonSchemas.dateRange,
  }),

  createDeposit: Joi.object({
    amount: Joi.number().positive().precision(2).required(),
    currency: commonSchemas.currency.required(),
    paymentMethod: Joi.string().valid('credit_card', 'bank_transfer', 'paypal').required(),
    paymentDetails: Joi.object({
      cardToken: Joi.string().when('...paymentMethod', {
        is: 'credit_card',
        then: Joi.required(),
        otherwise: Joi.forbidden(),
      }),
      billingAddress: Joi.object({
        street: Joi.string().required(),
        city: Joi.string().required(),
        country: Joi.string().length(2).uppercase().required(),
        postalCode: Joi.string().required(),
      }).when('...paymentMethod', {
        is: 'credit_card',
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    }).required(),
  }),

  createWithdrawal: Joi.object({
    amount: Joi.number().positive().precision(2).required(),
    currency: commonSchemas.currency.required(),
    withdrawalMethod: Joi.string().valid('bank_transfer', 'paypal').required(),
    withdrawalDetails: Joi.object({
      bankAccount: Joi.string().when('...withdrawalMethod', {
        is: 'bank_transfer',
        then: Joi.required(),
        otherwise: Joi.forbidden(),
      }),
      routingNumber: Joi.string().when('...withdrawalMethod', {
        is: 'bank_transfer',
        then: Joi.required(),
        otherwise: Joi.forbidden(),
      }),
      paypalEmail: Joi.string().email().when('...withdrawalMethod', {
        is: 'paypal',
        then: Joi.required(),
        otherwise: Joi.forbidden(),
      }),
    }).required(),
  }),

  getTransactionById: Joi.object({
    transactionId: commonSchemas.objectId.required(),
  }),
};

/**
 * Room validation schemas
 */
const roomSchemas = {
  getRooms: Joi.object({
    ...commonSchemas.pagination,
    gameType: commonSchemas.gameType,
    status: Joi.string().valid('waiting', 'playing'),
    minBet: Joi.number().positive(),
    maxBet: Joi.number().positive().min(Joi.ref('minBet')),
  }),

  createRoom: Joi.object({
    name: Joi.string().trim().min(1).max(100).required(),
    gameType: commonSchemas.gameType.required(),
    betAmount: Joi.number().positive().precision(2).required(),
    currency: commonSchemas.currency.required(),
    maxPlayers: Joi.number().integer().min(2).max(8).required(),
    isPrivate: Joi.boolean().default(false),
    password: Joi.string().min(4).max(20).when('isPrivate', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    }),
  }),

  getRoomById: Joi.object({
    roomId: commonSchemas.objectId.required(),
  }),

  joinRoom: Joi.object({
    roomId: commonSchemas.objectId.required(),
    password: Joi.string().optional(),
  }),

  updateRoom: Joi.object({
    name: Joi.string().trim().min(1).max(100),
    maxPlayers: Joi.number().integer().min(2).max(8),
    isPrivate: Joi.boolean(),
    password: Joi.string().min(4).max(20).allow(null),
  }),
};

/**
 * Game validation schemas
 */
const gameSchemas = {
  getGameHistory: Joi.object({
    ...commonSchemas.pagination,
    gameType: commonSchemas.gameType,
    ...commonSchemas.dateRange,
  }),

  getGameById: Joi.object({
    gameId: commonSchemas.objectId.required(),
  }),
};

/**
 * Sanitize input to prevent XSS and injection attacks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const sanitizeInput = (req, res, next) => {
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }
    if (typeof obj === 'object' && obj !== null) {
      Object.keys(obj).forEach((key) => {
        obj[key] = sanitize(obj[key]);
      });
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitize(req.body);
  }
  if (req.query) {
    req.query = sanitize(req.query);
  }
  if (req.params) {
    req.params = sanitize(req.params);
  }

  next();
};

module.exports = {
  validate,
  validateBody,
  validateQuery,
  validateParams,
  sanitizeInput,
  commonSchemas,
  authSchemas,
  userSchemas,
  transactionSchemas,
  roomSchemas,
  gameSchemas,
};
