const promClient = require('prom-client');
const config = require('../config');
const logger = require('../utils/logger');

// Create a Registry to register the metrics
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({
  register,
  prefix: 'api_gateway_',
});

// HTTP request metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'api_gateway_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

const httpRequestTotal = new promClient.Counter({
  name: 'api_gateway_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const httpRequestSize = new promClient.Histogram({
  name: 'api_gateway_http_request_size_bytes',
  help: 'Size of HTTP requests in bytes',
  labelNames: ['method', 'route'],
  buckets: [100, 1000, 10000, 100000, 1000000],
});

const httpResponseSize = new promClient.Histogram({
  name: 'api_gateway_http_response_size_bytes',
  help: 'Size of HTTP responses in bytes',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [100, 1000, 10000, 100000, 1000000],
});

// Authentication metrics
const authAttempts = new promClient.Counter({
  name: 'api_gateway_auth_attempts_total',
  help: 'Total number of authentication attempts',
  labelNames: ['type', 'status'],
});

const authTokensIssued = new promClient.Counter({
  name: 'api_gateway_auth_tokens_issued_total',
  help: 'Total number of authentication tokens issued',
  labelNames: ['type'],
});

// gRPC metrics
const grpcRequestDuration = new promClient.Histogram({
  name: 'api_gateway_grpc_request_duration_seconds',
  help: 'Duration of gRPC requests in seconds',
  labelNames: ['service', 'method', 'status'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

const grpcRequestTotal = new promClient.Counter({
  name: 'api_gateway_grpc_requests_total',
  help: 'Total number of gRPC requests',
  labelNames: ['service', 'method', 'status'],
});

// Database metrics
const dbOperationDuration = new promClient.Histogram({
  name: 'api_gateway_db_operation_duration_seconds',
  help: 'Duration of database operations in seconds',
  labelNames: ['operation', 'collection', 'status'],
  buckets: [0.01, 0.05, 0.1, 0.3, 0.5, 1, 3, 5],
});

const dbConnectionsActive = new promClient.Gauge({
  name: 'api_gateway_db_connections_active',
  help: 'Number of active database connections',
});

// Cache metrics
const cacheOperations = new promClient.Counter({
  name: 'api_gateway_cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'status'],
});

const cacheHitRatio = new promClient.Gauge({
  name: 'api_gateway_cache_hit_ratio',
  help: 'Cache hit ratio',
});

// Rate limiting metrics
const rateLimitHits = new promClient.Counter({
  name: 'api_gateway_rate_limit_hits_total',
  help: 'Total number of rate limit hits',
  labelNames: ['endpoint', 'user_type'],
});

// Business metrics
const activeUsers = new promClient.Gauge({
  name: 'api_gateway_active_users',
  help: 'Number of currently active users',
});

const gameRoomsActive = new promClient.Gauge({
  name: 'api_gateway_game_rooms_active',
  help: 'Number of active game rooms',
});

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(httpRequestSize);
register.registerMetric(httpResponseSize);
register.registerMetric(authAttempts);
register.registerMetric(authTokensIssued);
register.registerMetric(grpcRequestDuration);
register.registerMetric(grpcRequestTotal);
register.registerMetric(dbOperationDuration);
register.registerMetric(dbConnectionsActive);
register.registerMetric(cacheOperations);
register.registerMetric(cacheHitRatio);
register.registerMetric(rateLimitHits);
register.registerMetric(activeUsers);
register.registerMetric(gameRoomsActive);

/**
 * Normalize route for metrics (replace dynamic segments with placeholders)
 * @param {string} route - Original route
 * @returns {string} - Normalized route
 */
const normalizeRoute = (route) => {
  return route
    .replace(/\/api\/v\d+/, '/api/v*')
    .replace(/\/[0-9a-fA-F]{24}/g, '/:id')
    .replace(/\/\d+/g, '/:id');
};

/**
 * Metrics middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const metricsMiddleware = (req, res, next) => {
  if (!config.monitoring.metricsEnabled) {
    return next();
  }

  const startTime = Date.now();
  const route = normalizeRoute(req.route?.path || req.path);

  // Track request size
  const requestSize = parseInt(req.get('content-length') || '0', 10);
  if (requestSize > 0) {
    httpRequestSize.observe(
      { method: req.method, route },
      requestSize
    );
  }

  // Override res.end to capture response metrics
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const duration = (Date.now() - startTime) / 1000;
    const statusCode = res.statusCode.toString();

    // Track response metrics
    httpRequestDuration.observe(
      { method: req.method, route, status_code: statusCode },
      duration
    );

    httpRequestTotal.inc({
      method: req.method,
      route,
      status_code: statusCode,
    });

    // Track response size
    const responseSize = parseInt(res.get('content-length') || '0', 10);
    if (responseSize > 0) {
      httpResponseSize.observe(
        { method: req.method, route, status_code: statusCode },
        responseSize
      );
    }

    // Log request metrics
    logger.logRequest(req, res, duration * 1000);

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Record authentication attempt
 * @param {string} type - Authentication type (login, register, refresh)
 * @param {string} status - Status (success, failure)
 */
const recordAuthAttempt = (type, status) => {
  authAttempts.inc({ type, status });
};

/**
 * Record token issuance
 * @param {string} type - Token type (access, refresh)
 */
const recordTokenIssued = (type) => {
  authTokensIssued.inc({ type });
};

/**
 * Record gRPC request
 * @param {string} service - Service name
 * @param {string} method - Method name
 * @param {number} duration - Duration in seconds
 * @param {string} status - Status (success, error)
 */
const recordGrpcRequest = (service, method, duration, status) => {
  grpcRequestDuration.observe({ service, method, status }, duration);
  grpcRequestTotal.inc({ service, method, status });
};

/**
 * Record database operation
 * @param {string} operation - Operation type
 * @param {string} collection - Collection name
 * @param {number} duration - Duration in seconds
 * @param {string} status - Status (success, error)
 */
const recordDbOperation = (operation, collection, duration, status) => {
  dbOperationDuration.observe({ operation, collection, status }, duration);
};

/**
 * Update active database connections
 * @param {number} count - Number of active connections
 */
const updateDbConnections = (count) => {
  dbConnectionsActive.set(count);
};

/**
 * Record cache operation
 * @param {string} operation - Operation type (get, set, del)
 * @param {string} status - Status (hit, miss, success, error)
 */
const recordCacheOperation = (operation, status) => {
  cacheOperations.inc({ operation, status });
};

/**
 * Update cache hit ratio
 * @param {number} ratio - Hit ratio (0-1)
 */
const updateCacheHitRatio = (ratio) => {
  cacheHitRatio.set(ratio);
};

/**
 * Record rate limit hit
 * @param {string} endpoint - Endpoint that was rate limited
 * @param {string} userType - Type of user (authenticated, anonymous)
 */
const recordRateLimitHit = (endpoint, userType) => {
  rateLimitHits.inc({ endpoint, userType });
};

/**
 * Update active users count
 * @param {number} count - Number of active users
 */
const updateActiveUsers = (count) => {
  activeUsers.set(count);
};

/**
 * Update active game rooms count
 * @param {number} count - Number of active game rooms
 */
const updateActiveGameRooms = (count) => {
  gameRoomsActive.set(count);
};

/**
 * Get metrics for Prometheus
 * @returns {Promise<string>} - Prometheus metrics
 */
const getMetrics = async () => {
  return register.metrics();
};

module.exports = {
  metricsMiddleware,
  recordAuthAttempt,
  recordTokenIssued,
  recordGrpcRequest,
  recordDbOperation,
  updateDbConnections,
  recordCacheOperation,
  updateCacheHitRatio,
  recordRateLimitHit,
  updateActiveUsers,
  updateActiveGameRooms,
  getMetrics,
  register,
};
