const { StatusCodes } = require('http-status-codes');
const logger = require('../utils/logger');
const config = require('../config');

/**
 * Custom error class for API errors
 */
class ApiError extends Error {
  constructor(message, statusCode = StatusCodes.INTERNAL_SERVER_ERROR, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
  }
}

/**
 * Custom error class for validation errors
 */
class ValidationError extends ApiError {
  constructor(message, details = null) {
    super(message, StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

/**
 * Custom error class for authentication errors
 */
class AuthenticationError extends ApiError {
  constructor(message = 'Authentication required') {
    super(message, StatusCodes.UNAUTHORIZED, 'UNAUTHENTICATED');
    this.name = 'AuthenticationError';
  }
}

/**
 * Custom error class for authorization errors
 */
class AuthorizationError extends ApiError {
  constructor(message = 'Insufficient permissions') {
    super(message, StatusCodes.FORBIDDEN, 'PERMISSION_DENIED');
    this.name = 'AuthorizationError';
  }
}

/**
 * Custom error class for not found errors
 */
class NotFoundError extends ApiError {
  constructor(message = 'Resource not found') {
    super(message, StatusCodes.NOT_FOUND, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

/**
 * Custom error class for conflict errors
 */
class ConflictError extends ApiError {
  constructor(message = 'Resource already exists') {
    super(message, StatusCodes.CONFLICT, 'ALREADY_EXISTS');
    this.name = 'ConflictError';
  }
}

/**
 * Custom error class for rate limit errors
 */
class RateLimitError extends ApiError {
  constructor(message = 'Rate limit exceeded') {
    super(message, StatusCodes.TOO_MANY_REQUESTS, 'RATE_LIMIT_EXCEEDED');
    this.name = 'RateLimitError';
  }
}

/**
 * Custom error class for service unavailable errors
 */
class ServiceUnavailableError extends ApiError {
  constructor(message = 'Service temporarily unavailable') {
    super(message, StatusCodes.SERVICE_UNAVAILABLE, 'SERVICE_UNAVAILABLE');
    this.name = 'ServiceUnavailableError';
  }
}

/**
 * Handle Mongoose validation errors
 * @param {Error} error
 * @returns {ValidationError}
 */
const handleMongooseValidationError = (error) => {
  const details = {};
  Object.keys(error.errors).forEach((key) => {
    details[key] = error.errors[key].message;
  });

  return new ValidationError('Validation failed', details);
};

/**
 * Handle Mongoose duplicate key errors
 * @param {Error} error
 * @returns {ConflictError}
 */
const handleMongooseDuplicateKeyError = (error) => {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];

  return new ConflictError(`${field} '${value}' already exists`);
};

/**
 * Handle Mongoose cast errors
 * @param {Error} error
 * @returns {ValidationError}
 */
const handleMongooseCastError = (error) => {
  return new ValidationError(`Invalid ${error.path}: ${error.value}`);
};

/**
 * Handle JWT errors
 * @param {Error} error
 * @returns {AuthenticationError}
 */
const handleJWTError = (error) => {
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('Token has expired');
  }
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }
  return new AuthenticationError('Token verification failed');
};

/**
 * Handle gRPC errors
 * @param {Error} error
 * @returns {ApiError}
 */
const handleGrpcError = (error) => {
  const grpcStatusMap = {
    1: { status: StatusCodes.INTERNAL_SERVER_ERROR, code: 'GRPC_CANCELLED' },
    2: { status: StatusCodes.INTERNAL_SERVER_ERROR, code: 'GRPC_UNKNOWN' },
    3: { status: StatusCodes.BAD_REQUEST, code: 'GRPC_INVALID_ARGUMENT' },
    4: { status: StatusCodes.REQUEST_TIMEOUT, code: 'GRPC_DEADLINE_EXCEEDED' },
    5: { status: StatusCodes.NOT_FOUND, code: 'GRPC_NOT_FOUND' },
    6: { status: StatusCodes.CONFLICT, code: 'GRPC_ALREADY_EXISTS' },
    7: { status: StatusCodes.FORBIDDEN, code: 'GRPC_PERMISSION_DENIED' },
    8: { status: StatusCodes.TOO_MANY_REQUESTS, code: 'GRPC_RESOURCE_EXHAUSTED' },
    9: { status: StatusCodes.BAD_REQUEST, code: 'GRPC_FAILED_PRECONDITION' },
    10: { status: StatusCodes.CONFLICT, code: 'GRPC_ABORTED' },
    11: { status: StatusCodes.BAD_REQUEST, code: 'GRPC_OUT_OF_RANGE' },
    12: { status: StatusCodes.NOT_IMPLEMENTED, code: 'GRPC_UNIMPLEMENTED' },
    13: { status: StatusCodes.INTERNAL_SERVER_ERROR, code: 'GRPC_INTERNAL' },
    14: { status: StatusCodes.SERVICE_UNAVAILABLE, code: 'GRPC_UNAVAILABLE' },
    15: { status: StatusCodes.INTERNAL_SERVER_ERROR, code: 'GRPC_DATA_LOSS' },
    16: { status: StatusCodes.UNAUTHORIZED, code: 'GRPC_UNAUTHENTICATED' },
  };

  const mapping = grpcStatusMap[error.code] || {
    status: StatusCodes.INTERNAL_SERVER_ERROR,
    code: 'GRPC_UNKNOWN'
  };

  return new ApiError(error.message, mapping.status, mapping.code);
};

/**
 * Convert operational errors to API errors
 * @param {Error} error
 * @returns {ApiError}
 */
const convertToApiError = (error) => {
  // Already an API error
  if (error instanceof ApiError) {
    return error;
  }

  // Mongoose validation error
  if (error.name === 'ValidationError') {
    return handleMongooseValidationError(error);
  }

  // Mongoose duplicate key error
  if (error.code === 11000) {
    return handleMongooseDuplicateKeyError(error);
  }

  // Mongoose cast error
  if (error.name === 'CastError') {
    return handleMongooseCastError(error);
  }

  // JWT errors
  if (error.name === 'TokenExpiredError' || error.name === 'JsonWebTokenError') {
    return handleJWTError(error);
  }

  // gRPC errors
  if (error.code && typeof error.code === 'number') {
    return handleGrpcError(error);
  }

  // Default to internal server error
  let errorMessage = 'Internal server error';

  if (config.env !== 'production') {
    try {
      errorMessage = error.message || error.name || 'Unknown error';
    } catch (e) {
      // Handle circular reference or other serialization issues
      errorMessage = 'Error occurred but could not be serialized';
    }
  }

  return new ApiError(
    errorMessage,
    StatusCodes.INTERNAL_SERVER_ERROR,
    'INTERNAL_ERROR'
  );
};

/**
 * Error handler middleware
 * @param {Error} error
 * @param {Object} req
 * @param {Object} res
 * @param {Function} next
 */
const errorHandler = (error, req, res, next) => {
  // Convert to API error
  const apiError = convertToApiError(error);

  // Log error
  logger.logError(apiError, req, {
    errorId: req.requestId,
    timestamp: new Date().toISOString(),
  });

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      code: apiError.code,
      message: apiError.message,
      status_code: apiError.statusCode,
      timestamp: new Date().toISOString(),
      request_id: req.requestId,
    },
  };

  // Add details if available
  if (apiError.details) {
    errorResponse.error.details = apiError.details;
  }

  // Add stack trace in development
  if (config.env === 'development' && apiError.stack) {
    errorResponse.error.stack = apiError.stack;
  }

  // Send error response
  res.status(apiError.statusCode).json(errorResponse);
};

module.exports = {
  errorHandler,
  ApiError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ServiceUnavailableError,
};
