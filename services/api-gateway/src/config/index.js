require('dotenv').config();

const config = {
  // Environment
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,

  // Database
  mongodb: {
    url: process.env.MONGODB_URL || 'mongodb://localhost:27017/xzgame',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    options: {
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
    },
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your_jwt_secret_here_change_in_production',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'xzgame-auth-service', // Auth Service as issuer for all tokens
    audience: process.env.JWT_AUDIENCE
      ? process.env.JWT_AUDIENCE.split(',').map(aud => aud.trim())
      : ['xzgame-api', 'xzgame-game-service'], // Services that can verify tokens
  },

  // CORS
  cors: {
    origins: process.env.CORS_ORIGINS
      ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
      : ['http://localhost:3000', 'http://localhost:3001'],
  },

  // Rate Limiting
  rateLimit: {
    window: parseInt(process.env.RATE_LIMIT_WINDOW, 10) || 60000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
    auth: {
      window: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW, 10) || 60000, // 1 minute
      max: parseInt(process.env.AUTH_RATE_LIMIT_MAX, 10) || (process.env.NODE_ENV === 'development' ? 50 : 5), // 50 attempts in dev, 5 in prod
    },
    admin: {
      window: 60000, // 1 minute
      max: 200, // 200 requests per minute for admin endpoints
    },
  },

  // gRPC Services
  grpc: {
    gameService: {
      url: process.env.GAME_SERVICE_URL || 'localhost:50051',
      options: {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000,
      },
    },
    managerService: {
      url: process.env.MANAGER_SERVICE_URL || 'localhost:50052',
      options: {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000,
      },
    },
  },

  // HTTP Services (for services that use HTTP instead of gRPC)
  http: {
    managerService: {
      baseURL: process.env.MANAGER_SERVICE_HTTP_URL || 'http://localhost:3002',
      timeout: 30000,
    },
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED !== 'false',
      filename: process.env.LOG_FILENAME || 'logs/api-gateway.log',
      maxSize: process.env.LOG_MAX_SIZE || '20m',
      maxFiles: process.env.LOG_MAX_FILES || '14d',
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
      colorize: process.env.LOG_COLORIZE !== 'false',
    },
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your_session_secret_here',
    csrfEnabled: process.env.CSRF_ENABLED === 'true',
    httpsOnly: process.env.HTTPS_ONLY === 'true',
  },

  // Validation
  validation: {
    maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES
      ? process.env.ALLOWED_FILE_TYPES.split(',')
      : ['image/jpeg', 'image/png', 'image/gif'],
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
  },

  // Monitoring
  monitoring: {
    metricsEnabled: process.env.METRICS_ENABLED !== 'false',
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000,
    prometheusEnabled: process.env.PROMETHEUS_ENABLED !== 'false',
  },

  // Pagination
  pagination: {
    defaultLimit: parseInt(process.env.DEFAULT_PAGE_LIMIT, 10) || 20,
    maxLimit: parseInt(process.env.MAX_PAGE_LIMIT, 10) || 100,
  },

  // Cache
  cache: {
    defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL, 10) || 300, // 5 minutes
    userProfileTTL: parseInt(process.env.CACHE_USER_PROFILE_TTL, 10) || 600, // 10 minutes
    gameConfigTTL: parseInt(process.env.CACHE_GAME_CONFIG_TTL, 10) || 1800, // 30 minutes
  },

  // Feature Flags
  features: {
    swaggerEnabled: process.env.SWAGGER_ENABLED !== 'false',
    metricsEndpointEnabled: process.env.METRICS_ENDPOINT_ENABLED !== 'false',
    debugMode: process.env.DEBUG_MODE === 'true',
    maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
  },
};

// Validation
const requiredEnvVars = [
  'JWT_SECRET',
  'MONGODB_URL',
  'REDIS_URL',
];

if (config.env === 'production') {
  requiredEnvVars.forEach((envVar) => {
    if (!process.env[envVar]) {
      throw new Error(`Required environment variable ${envVar} is not set`);
    }
  });

  // Additional production validations
  if (config.jwt.secret === 'your_jwt_secret_here_change_in_production') {
    throw new Error('JWT_SECRET must be changed in production');
  }

  if (config.security.sessionSecret === 'your_session_secret_here') {
    throw new Error('SESSION_SECRET must be changed in production');
  }
}

module.exports = config;
