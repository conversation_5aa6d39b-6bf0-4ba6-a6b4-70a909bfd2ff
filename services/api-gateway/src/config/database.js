const mongoose = require('mongoose');
const config = require('./index');
const logger = require('../utils/logger');

// MongoDB connection state
let isConnected = false;

// Connection options
const connectionOptions = {
  ...config.mongodb.options,
};

/**
 * Connect to MongoDB database
 * @returns {Promise<void>}
 */
const connectDatabase = async () => {
  try {
    if (isConnected) {
      logger.info('MongoDB already connected');
      return;
    }

    // Set mongoose options
    mongoose.set('strictQuery', false);

    // Connect to MongoDB
    await mongoose.connect(config.mongodb.url, connectionOptions);

    isConnected = true;
    logger.info('MongoDB connected successfully');

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error:', error);
      isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
      isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
      isConnected = true;
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (error) {
        logger.error('Error closing MongoDB connection:', error);
        process.exit(1);
      }
    });

  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    throw error;
  }
};

/**
 * Disconnect from MongoDB database
 * @returns {Promise<void>}
 */
const disconnectDatabase = async () => {
  try {
    if (!isConnected) {
      logger.info('MongoDB already disconnected');
      return;
    }

    await mongoose.connection.close();
    isConnected = false;
    logger.info('MongoDB disconnected successfully');
  } catch (error) {
    logger.error('Error disconnecting from MongoDB:', error);
    throw error;
  }
};

/**
 * Get database connection status
 * @returns {boolean}
 */
const isConnectedToDatabase = () => {
  return isConnected && mongoose.connection.readyState === 1;
};

/**
 * Get database connection info
 * @returns {Object}
 */
const getDatabaseInfo = () => {
  return {
    connected: isConnectedToDatabase(),
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name,
  };
};

/**
 * Health check for database
 * @returns {Promise<Object>}
 */
const healthCheck = async () => {
  try {
    if (!isConnectedToDatabase()) {
      return {
        status: 'unhealthy',
        message: 'Not connected to database',
        details: getDatabaseInfo(),
      };
    }

    // Ping the database
    await mongoose.connection.db.admin().ping();

    return {
      status: 'healthy',
      message: 'Database connection is healthy',
      details: getDatabaseInfo(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Database health check failed',
      error: error.message,
      details: getDatabaseInfo(),
    };
  }
};

/**
 * Create database indexes
 * @returns {Promise<void>}
 */
const createIndexes = async () => {
  try {
    if (!isConnectedToDatabase()) {
      throw new Error('Database not connected');
    }

    const db = mongoose.connection.db;

    // Users collection indexes
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ 'profile.firstName': 1, 'profile.lastName': 1 });
    await db.collection('users').createIndex({ createdAt: 1 });
    await db.collection('users').createIndex({ 'balance.current': 1 });

    // Transactions collection indexes
    await db.collection('transactions').createIndex({ userId: 1, createdAt: -1 });
    await db.collection('transactions').createIndex({ type: 1, status: 1 });
    await db.collection('transactions').createIndex({ gameId: 1 });
    await db.collection('transactions').createIndex({ roomId: 1 });
    await db.collection('transactions').createIndex({ createdAt: -1 });

    // Rooms collection indexes
    await db.collection('rooms').createIndex({ status: 1, gameType: 1 });
    await db.collection('rooms').createIndex({ creatorId: 1 });
    await db.collection('rooms').createIndex({ 'players.userId': 1 });
    await db.collection('rooms').createIndex({ createdAt: -1 });

    // Game history collection indexes
    await db.collection('gamehistories').createIndex({ 'participants.userId': 1, playedAt: -1 });
    await db.collection('gamehistories').createIndex({ gameType: 1, playedAt: -1 });
    await db.collection('gamehistories').createIndex({ roomId: 1 });
    await db.collection('gamehistories').createIndex({ playedAt: -1 });

    // Game configurations collection indexes
    await db.collection('gameconfigurations').createIndex({ gameType: 1, version: -1 });
    await db.collection('gameconfigurations').createIndex({ isActive: 1 });

    logger.info('Database indexes created successfully');
  } catch (error) {
    logger.error('Failed to create database indexes:', error);
    throw error;
  }
};

module.exports = {
  connectDatabase,
  disconnectDatabase,
  isConnectedToDatabase,
  getDatabaseInfo,
  healthCheck,
  createIndexes,
  mongoose,
};
