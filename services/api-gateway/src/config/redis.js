const Redis = require('redis');
const config = require('./index');
const logger = require('../utils/logger');

// Redis client instance
let redisClient = null;
let isConnected = false;

/**
 * Create Redis client with configuration
 * @returns {Redis.RedisClientType}
 */
const createRedisClient = () => {
  const client = Redis.createClient({
    url: config.redis.url,
    ...config.redis.options,
  });

  // Event handlers
  client.on('connect', () => {
    logger.info('Redis client connecting...');
  });

  client.on('ready', () => {
    logger.info('Redis client connected and ready');
    isConnected = true;
  });

  client.on('error', (error) => {
    logger.error('Redis client error:', error);
    isConnected = false;
  });

  client.on('end', () => {
    logger.warn('Redis client connection ended');
    isConnected = false;
  });

  client.on('reconnecting', () => {
    logger.info('Redis client reconnecting...');
  });

  return client;
};

/**
 * Connect to Redis
 * @returns {Promise<void>}
 */
const connectRedis = async () => {
  try {
    if (redisClient && isConnected) {
      logger.info('Redis already connected');
      return;
    }

    redisClient = createRedisClient();
    await redisClient.connect();

    logger.info('Redis connected successfully');
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
};

/**
 * Disconnect from Redis
 * @returns {Promise<void>}
 */
const disconnectRedis = async () => {
  try {
    if (!redisClient) {
      logger.info('Redis client not initialized');
      return;
    }

    await redisClient.quit();
    redisClient = null;
    isConnected = false;
    logger.info('Redis disconnected successfully');
  } catch (error) {
    logger.error('Error disconnecting from Redis:', error);
    throw error;
  }
};

/**
 * Get Redis client instance
 * @returns {Redis.RedisClientType|null}
 */
const getClient = () => {
  if (!redisClient || !isConnected) {
    logger.warn('Redis client not available');
    return null;
  }
  return redisClient;
};

/**
 * Check if Redis is connected
 * @returns {boolean}
 */
const isConnectedToRedis = () => {
  return isConnected && redisClient && redisClient.isReady;
};

/**
 * Health check for Redis
 * @returns {Promise<Object>}
 */
const healthCheck = async () => {
  try {
    if (!isConnectedToRedis()) {
      return {
        status: 'unhealthy',
        message: 'Not connected to Redis',
        details: {
          connected: isConnected,
          clientReady: redisClient ? redisClient.isReady : false,
        },
      };
    }

    // Ping Redis
    const pong = await redisClient.ping();
    
    if (pong === 'PONG') {
      return {
        status: 'healthy',
        message: 'Redis connection is healthy',
        details: {
          connected: true,
          ping: pong,
        },
      };
    } else {
      return {
        status: 'unhealthy',
        message: 'Redis ping failed',
        details: {
          connected: isConnected,
          ping: pong,
        },
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Redis health check failed',
      error: error.message,
      details: {
        connected: isConnected,
        clientReady: redisClient ? redisClient.isReady : false,
      },
    };
  }
};

/**
 * Cache operations wrapper
 */
const cache = {
  /**
   * Set a value in cache
   * @param {string} key
   * @param {any} value
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<string>}
   */
  async set(key, value, ttl = config.cache.defaultTTL) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const serializedValue = JSON.stringify(value);
    return await client.setEx(key, ttl, serializedValue);
  },

  /**
   * Get a value from cache
   * @param {string} key
   * @returns {Promise<any>}
   */
  async get(key) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const value = await client.get(key);
    return value ? JSON.parse(value) : null;
  },

  /**
   * Delete a key from cache
   * @param {string} key
   * @returns {Promise<number>}
   */
  async del(key) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    return await client.del(key);
  },

  /**
   * Check if key exists
   * @param {string} key
   * @returns {Promise<boolean>}
   */
  async exists(key) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const result = await client.exists(key);
    return result === 1;
  },

  /**
   * Set expiration for a key
   * @param {string} key
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>}
   */
  async expire(key, ttl) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const result = await client.expire(key, ttl);
    return result === 1;
  },

  /**
   * Increment a numeric value
   * @param {string} key
   * @param {number} increment
   * @returns {Promise<number>}
   */
  async incr(key, increment = 1) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    return await client.incrBy(key, increment);
  },

  /**
   * Get multiple keys
   * @param {string[]} keys
   * @returns {Promise<any[]>}
   */
  async mget(keys) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const values = await client.mGet(keys);
    return values.map(value => value ? JSON.parse(value) : null);
  },

  /**
   * Set multiple key-value pairs
   * @param {Object} keyValuePairs
   * @returns {Promise<string>}
   */
  async mset(keyValuePairs) {
    const client = getClient();
    if (!client) throw new Error('Redis client not available');

    const serializedPairs = {};
    Object.entries(keyValuePairs).forEach(([key, value]) => {
      serializedPairs[key] = JSON.stringify(value);
    });

    return await client.mSet(serializedPairs);
  },
};

// Graceful shutdown
process.on('SIGINT', async () => {
  await disconnectRedis();
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
});

module.exports = {
  connectRedis,
  disconnectRedis,
  getClient,
  isConnectedToRedis,
  healthCheck,
  cache,
};
