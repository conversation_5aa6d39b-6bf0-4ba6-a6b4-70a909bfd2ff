const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const config = require('../config');

// Safe JSON stringify to handle circular references
const safeStringify = (obj) => {
  try {
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        // Check for circular references
        if (safeStringify.seen && safeStringify.seen.has(value)) {
          return '[Circular]';
        }
        if (!safeStringify.seen) {
          safeStringify.seen = new WeakSet();
        }
        safeStringify.seen.add(value);
      }
      return value;
    });
  } catch (error) {
    return '[Unable to stringify]';
  } finally {
    safeStringify.seen = null;
  }
};

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = {
      timestamp,
      level,
      message,
      service: 'api-gateway',
      ...meta,
    };

    if (stack) {
      log.stack = stack;
    }

    return safeStringify(log);
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${safeStringify(meta)}`;
    }
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

// Create transports array
const transports = [];

// Console transport
if (config.logging.console.enabled) {
  transports.push(
    new winston.transports.Console({
      level: config.logging.level,
      format: config.env === 'development' ? consoleFormat : logFormat,
      handleExceptions: true,
      handleRejections: true,
    })
  );
}

// File transport
if (config.logging.file.enabled) {
  // Ensure logs directory exists
  const fs = require('fs');
  const logsDir = path.dirname(config.logging.file.filename);
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Daily rotate file transport
  transports.push(
    new DailyRotateFile({
      level: config.logging.level,
      filename: config.logging.file.filename.replace('.log', '-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: config.logging.file.maxSize,
      maxFiles: config.logging.file.maxFiles,
      format: logFormat,
      handleExceptions: true,
      handleRejections: true,
    })
  );

  // Error log file
  transports.push(
    new DailyRotateFile({
      level: 'error',
      filename: config.logging.file.filename.replace('.log', '-error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: config.logging.file.maxSize,
      maxFiles: config.logging.file.maxFiles,
      format: logFormat,
      handleExceptions: true,
      handleRejections: true,
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'api-gateway',
    environment: config.env,
  },
  transports,
  exitOnError: false,
});

// Add request logging helper
logger.logRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    requestId: req.requestId,
  };

  if (req.user) {
    logData.userId = req.user.id;
    logData.username = req.user.username;
  }

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// Add error logging helper
logger.logError = (error, req = null, additionalInfo = {}) => {
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    ...additionalInfo,
  };

  if (req) {
    logData.request = {
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      requestId: req.requestId,
    };

    if (req.user) {
      logData.request.userId = req.user.id;
      logData.request.username = req.user.username;
    }
  }

  logger.error('Application Error', logData);
};

// Add gRPC logging helper
logger.logGrpcCall = (service, method, request, response, duration, error = null) => {
  const logData = {
    grpc: {
      service,
      method,
      duration: `${duration}ms`,
      requestSize: safeStringify(request).length,
    },
  };

  if (response) {
    logData.grpc.responseSize = safeStringify(response).length;
    logData.grpc.success = true;
  }

  if (error) {
    logData.grpc.success = false;
    logData.grpc.error = {
      code: error.code,
      message: error.message,
    };
    logger.error('gRPC Call Failed', logData);
  } else {
    logger.info('gRPC Call', logData);
  }
};

// Add database logging helper
logger.logDatabaseOperation = (operation, collection, query, duration, error = null) => {
  const logData = {
    database: {
      operation,
      collection,
      duration: `${duration}ms`,
    },
  };

  if (query) {
    logData.database.query = query;
  }

  if (error) {
    logData.database.error = {
      name: error.name,
      message: error.message,
    };
    logger.error('Database Operation Failed', logData);
  } else {
    logger.debug('Database Operation', logData);
  }
};

// Add cache logging helper
logger.logCacheOperation = (operation, key, hit = null, duration = null) => {
  const logData = {
    cache: {
      operation,
      key,
    },
  };

  if (hit !== null) {
    logData.cache.hit = hit;
  }

  if (duration !== null) {
    logData.cache.duration = `${duration}ms`;
  }

  logger.debug('Cache Operation', logData);
};

// Add authentication logging helper
logger.logAuth = (event, userId, username, success, reason = null, additionalInfo = {}) => {
  const logData = {
    auth: {
      event,
      userId,
      username,
      success,
      timestamp: new Date().toISOString(),
    },
    ...additionalInfo,
  };

  if (reason) {
    logData.auth.reason = reason;
  }

  if (success) {
    logger.info('Authentication Event', logData);
  } else {
    logger.warn('Authentication Failed', logData);
  }
};

module.exports = logger;
