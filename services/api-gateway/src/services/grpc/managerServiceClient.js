const axios = require('axios');
const jwt = require('jsonwebtoken');
const config = require('../../config');
const logger = require('../../utils/logger');
const { recordGrpcRequest } = require('../../middleware/metrics');

// HTTP client instance
let httpClient = null;
let isConnected = false;

/**
 * Generate service-to-service JWT token for API Gateway
 * Uses the admin user ID for manager service compatibility
 */
const generateServiceToken = () => {
  const payload = {
    sub: '68333053f035ba37b20bf3a4', // Admin user ID
    user_id: '68333053f035ba37b20bf3a4', // Manager service expects this field
    username: 'admin',
    role: 'admin',
    roles: ['admin'],
    permissions: ['admin:manage', 'user:manage', 'game:join', 'room:create'],
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
    jti: `api-gateway-${Date.now()}`,
    sessionId: 'api-gateway-service-session',
    device_id: 'api-gateway-service',
  };

  return jwt.sign(payload, config.jwt.secret, {
    algorithm: 'HS256',
    issuer: config.jwt.issuer,
    audience: config.jwt.audience,
  });
};

/**
 * Create HTTP client
 */
const createClient = () => {
  try {
    const client = axios.create({
      baseURL: config.http.managerService.baseURL,
      timeout: config.http.managerService.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add request interceptor for logging
    client.interceptors.request.use(
      (config) => {
        logger.debug('Manager Service HTTP Request:', {
          method: config.method,
          url: config.url,
          data: config.data,
        });
        return config;
      },
      (error) => {
        logger.error('Manager Service HTTP Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    client.interceptors.response.use(
      (response) => {
        logger.debug('Manager Service HTTP Response:', {
          status: response.status,
          data: response.data,
        });
        return response;
      },
      (error) => {
        logger.error('Manager Service HTTP Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );

    return client;
  } catch (error) {
    logger.error('Failed to create manager service HTTP client:', error);
    throw error;
  }
};

/**
 * Initialize HTTP client
 */
const initialize = async () => {
  try {
    if (httpClient && isConnected) {
      logger.info('Manager service HTTP client already initialized');
      return;
    }

    httpClient = createClient();

    // Test connection with health check
    await healthCheck();

    isConnected = true;
    logger.info('Manager service HTTP client initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize manager service HTTP client:', error);
    throw error;
  }
};

/**
 * Close HTTP client
 */
const close = () => {
  if (httpClient) {
    httpClient = null;
    isConnected = false;
    logger.info('Manager service HTTP client closed');
  }
};

/**
 * Make HTTP call with error handling and metrics
 */
const makeCall = async (method, endpoint, data = null, httpMethod = 'POST') => {
  if (!httpClient || !isConnected) {
    throw new Error('Manager service HTTP client not initialized');
  }

  const startTime = Date.now();

  try {
    let response;

    switch (httpMethod.toUpperCase()) {
      case 'GET':
        response = await httpClient.get(endpoint, { params: data });
        break;
      case 'POST':
        response = await httpClient.post(endpoint, data);
        break;
      case 'PUT':
        response = await httpClient.put(endpoint, data);
        break;
      case 'DELETE':
        response = await httpClient.delete(endpoint, { data });
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${httpMethod}`);
    }

    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('manager-service', method, duration, 'success');

    logger.debug('Manager Service HTTP Call:', {
      method,
      endpoint,
      duration: duration * 1000,
      status: response.status,
    });

    return response.data;
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('manager-service', method, duration, 'error');

    logger.error('Manager Service HTTP Call Error:', {
      method,
      endpoint,
      duration: duration * 1000,
      error: error.message,
      status: error.response?.status,
    });

    // Transform HTTP error to expected format
    if (error.response) {
      const errorData = error.response.data;
      if (errorData && !errorData.success) {
        return errorData; // Return the error response from Manager Service
      }
    }

    throw error;
  }
};

/**
 * Make authenticated HTTP call with user token
 */
const makeCallWithAuth = async (method, endpoint, data = null, httpMethod = 'POST', userToken = null) => {
  if (!httpClient || !isConnected) {
    throw new Error('Manager service HTTP client not initialized');
  }

  const startTime = Date.now();

  try {
    let response;
    const config = { params: data };

    // Add authentication header if token provided
    if (userToken) {
      config.headers = {
        'Authorization': `Bearer ${userToken}`
      };
    }

    switch (httpMethod.toUpperCase()) {
      case 'GET':
        response = await httpClient.get(endpoint, config);
        break;
      case 'POST':
        response = await httpClient.post(endpoint, data, config);
        break;
      case 'PUT':
        response = await httpClient.put(endpoint, data, config);
        break;
      case 'DELETE':
        response = await httpClient.delete(endpoint, { ...config, data });
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${httpMethod}`);
    }

    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('manager-service', method, duration, 'success');

    logger.debug('Manager Service Authenticated HTTP Call:', {
      method,
      endpoint,
      duration: duration * 1000,
      status: response.status,
      hasAuth: !!userToken
    });

    return response.data;
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('manager-service', method, duration, 'error');

    logger.error('Manager Service Authenticated HTTP Call Error:', {
      method,
      endpoint,
      duration: duration * 1000,
      error: error.message,
      status: error.response?.status,
      hasAuth: !!userToken
    });

    // Transform HTTP error to expected format
    if (error.response) {
      const errorData = error.response.data;
      if (errorData && !errorData.success) {
        return errorData; // Return the error response from Manager Service
      }
    }

    throw error;
  }
};

/**
 * Health check
 */
const healthCheck = async () => {
  try {
    const response = await makeCall('HealthCheck', '/health', null, 'GET');
    return {
      status: 'healthy',
      message: 'Manager service is healthy',
      timestamp: new Date().toISOString(),
      data: response,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Manager service health check failed',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Create a new user
 */
const createUser = async (request) => {
  try {
    const userData = {
      auth: {
        username: request.username,
        email: request.email,
        password: request.password, // Send plain text password
        password_confirmation: request.password, // Manager service expects confirmation
      },
    };

    const response = await makeCall('CreateUser', '/auth/register', userData, 'POST');

    // Transform manager service response to expected format
    if (response.success && response.data && response.data.user) {
      return {
        success: true,
        user: {
          id: response.data.user.id,
          username: response.data.user.username,
          email: response.data.user.email,
          profile: {
            firstName: request.profile?.firstName,
            lastName: request.profile?.lastName,
            dateOfBirth: request.profile?.dateOfBirth,
            country: request.profile?.country,
          },
          balance: response.data.user.balance,
          isActive: response.data.user.status === 'active',
          createdAt: response.data.user.created_at,
        },
      };
    }

    return response; // Return error response as-is
  } catch (error) {
    logger.error('Failed to create user:', error);
    throw error;
  }
};

/**
 * Get user by ID - fallback for cache misses
 */
const getUserById = async (request) => {
  try {
    // Generate service token for authenticated request
    const serviceToken = generateServiceToken();

    // Fallback implementation: fetch user from manager service HTTP API with authentication
    const response = await makeCallWithAuth('GetUserById', `/users/${request.userId}`, null, 'GET', serviceToken);

    if (response && response.success && response.data && response.data.user) {
      const user = response.data.user;

      // Transform the response to match the expected format
      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          balance: user.balance || 0,
          roles: user.role ? [user.role] : ['player'], // Convert single role to array
          permissions: user.role === 'admin'
            ? ['game:join', 'room:create', 'admin:manage', 'user:manage']
            : ['game:join', 'room:create'], // Set permissions based on role
          isActive: user.status === 'active',
          createdAt: user.created_at,
          lastLoginAt: user.last_login_at,
          statistics: {
            gamesPlayed: user.stats?.total_games || 0,
            gamesWon: 0, // Would need to calculate from game sessions
            totalWinnings: 0, // Would need to calculate from transactions
            totalLosses: 0 // Would need to calculate from transactions
          }
        },
      };
    }

    return {
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: 'User not found'
      }
    };
  } catch (error) {
    logger.error('Failed to get user by ID:', error);
    return {
      success: false,
      error: {
        code: 'USER_FETCH_ERROR',
        message: 'Failed to fetch user data'
      }
    };
  }
};

/**
 * Authenticate user via manager service
 */
const authenticateUser = async (request) => {
  try {
    const loginData = {
      auth: {
        username: request.username,
        password: request.password,
      },
    };

    const response = await makeCall('AuthenticateUser', '/auth/login', loginData, 'POST');

    // Transform manager service response to expected format
    if (response.success && response.data && response.data.user) {
      const user = response.data.user;
      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          profile: {
            firstName: user.first_name || '',
            lastName: user.last_name || '',
            dateOfBirth: user.date_of_birth,
            country: user.country || '',
          },
          balance: user.balance || 0,
          roles: user.role ? [user.role] : ['player'], // Convert single role to array
          permissions: user.role === 'admin'
            ? ['game:join', 'room:create', 'admin:manage', 'user:manage']
            : ['game:join', 'room:create'], // Set permissions based on role
          isActive: user.status === 'active',
          createdAt: user.created_at,
          lastLoginAt: user.last_login_at,
          statistics: {
            gamesPlayed: user.total_sessions || 0,
            gamesWon: 0, // Would need to calculate from game sessions
            totalWinnings: 0, // Would need to calculate from transactions
            totalLosses: 0 // Would need to calculate from transactions
          }
        },
      };
    }

    return response; // Return error response as-is
  } catch (error) {
    logger.error('Failed to authenticate user:', error);
    return {
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Authentication failed',
      },
    };
  }
};

/**
 * Get user by username
 */
const getUserByUsername = async (request) => {
  try {
    // For user lookup by username, we'll use the search endpoint
    const searchResponse = await makeCall('SearchUser', '/users/search', { username: request.username }, 'GET');

    if (searchResponse && searchResponse.success && searchResponse.data && searchResponse.data.length > 0) {
      const user = searchResponse.data[0];
      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          password: user.password_digest, // Rails uses password_digest
          profile: {
            firstName: user.first_name,
            lastName: user.last_name,
            dateOfBirth: user.date_of_birth,
            country: user.country,
          },
          balance: user.balance,
          isActive: user.active,
          createdAt: user.created_at,
        },
      };
    }

    return {
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: 'User not found',
      },
    };
  } catch (error) {
    logger.error('Failed to get user by username:', error);
    // If search fails, return user not found
    return {
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: 'User not found',
      },
    };
  }
};

/**
 * Update user profile
 */
const updateUserProfile = async (request) => {
  try {
    return await makeCall('UpdateUserProfile', request);
  } catch (error) {
    logger.error('Failed to update user profile:', error);
    throw error;
  }
};

/**
 * Update user settings
 */
const updateUserSettings = async (request) => {
  try {
    return await makeCall('UpdateUserSettings', request);
  } catch (error) {
    logger.error('Failed to update user settings:', error);
    throw error;
  }
};

/**
 * Delete user
 */
const deleteUser = async (request) => {
  try {
    return await makeCall('DeleteUser', request);
  } catch (error) {
    logger.error('Failed to delete user:', error);
    throw error;
  }
};

/**
 * Get user balance
 */
const getUserBalance = async (request) => {
  try {
    return await makeCall('GetUserBalance', request);
  } catch (error) {
    logger.error('Failed to get user balance:', error);
    throw error;
  }
};

/**
 * Update user balance
 */
const updateBalance = async (request) => {
  try {
    return await makeCall('UpdateBalance', request);
  } catch (error) {
    logger.error('Failed to update balance:', error);
    throw error;
  }
};

/**
 * Process transaction
 */
const processTransaction = async (request) => {
  try {
    return await makeCall('ProcessTransaction', request);
  } catch (error) {
    logger.error('Failed to process transaction:', error);
    throw error;
  }
};

/**
 * Get user transactions
 */
const getUserTransactions = async (request) => {
  try {
    return await makeCall('GetUserTransactions', request);
  } catch (error) {
    logger.error('Failed to get user transactions:', error);
    throw error;
  }
};

/**
 * Get transaction by ID
 */
const getTransactionById = async (request) => {
  try {
    return await makeCall('GetTransactionById', request);
  } catch (error) {
    logger.error('Failed to get transaction by ID:', error);
    throw error;
  }
};

/**
 * Validate user
 */
const validateUser = async (request) => {
  try {
    return await makeCall('ValidateUser', request);
  } catch (error) {
    logger.error('Failed to validate user:', error);
    throw error;
  }
};

/**
 * Create deposit transaction
 */
const createDeposit = async (request) => {
  try {
    return await makeCall('CreateDeposit', request);
  } catch (error) {
    logger.error('Failed to create deposit:', error);
    throw error;
  }
};

/**
 * Create withdrawal transaction
 */
const createWithdrawal = async (request) => {
  try {
    return await makeCall('CreateWithdrawal', request);
  } catch (error) {
    logger.error('Failed to create withdrawal:', error);
    throw error;
  }
};

/**
 * Get room details
 */
const getRoomDetails = async (roomId) => {
  try {
    return await makeCall('GetRoomDetails', `/rooms/${roomId}`, null, 'GET');
  } catch (error) {
    logger.error('Failed to get room details:', error);
    throw error;
  }
};

module.exports = {
  initialize,
  close,
  healthCheck,
  createUser,
  authenticateUser,
  getUserById,
  getUserByUsername,
  updateUserProfile,
  updateUserSettings,
  deleteUser,
  getUserBalance,
  updateBalance,
  processTransaction,
  getUserTransactions,
  getTransactionById,
  validateUser,
  createDeposit,
  createWithdrawal,
  getRoomDetails,
};
