const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const config = require('../../config');
const logger = require('../../utils/logger');
const { recordGrpcRequest } = require('../../middleware/metrics');

// Proto file path for auth service
const PROTO_PATH = path.join(__dirname, '../../../proto/auth.proto');

// gRPC client instance
let authServiceClient = null;
let isConnected = false;

/**
 * Load proto definition
 */
const loadProtoDefinition = () => {
  try {
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    });

    return grpc.loadPackageDefinition(packageDefinition);
  } catch (error) {
    logger.error('Failed to load auth service proto definition:', error);
    throw error;
  }
};

/**
 * Create gRPC client
 */
const createClient = () => {
  try {
    const proto = loadProtoDefinition();
    const AuthService = proto.auth.AuthService;

    const client = new AuthService(
      config.grpc.authService.url || 'localhost:8081',
      grpc.credentials.createInsecure(),
      config.grpc.authService.options || {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
      }
    );

    return client;
  } catch (error) {
    logger.error('Failed to create auth service client:', error);
    throw error;
  }
};

/**
 * Initialize gRPC client
 */
const initialize = async () => {
  try {
    if (authServiceClient && isConnected) {
      logger.info('Auth service client already initialized');
      return;
    }

    authServiceClient = createClient();

    // Test connection
    await healthCheck();

    isConnected = true;
    logger.info('Auth service client initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize auth service client:', error);
    // Don't throw error, allow fallback to HTTP
    logger.warn('Auth service gRPC unavailable, will attempt HTTP fallback');
  }
};

/**
 * Close gRPC client
 */
const close = () => {
  if (authServiceClient) {
    authServiceClient.close();
    authServiceClient = null;
    isConnected = false;
    logger.info('Auth service client closed');
  }
};

/**
 * Make gRPC call with error handling and metrics
 */
const makeCall = async (client, method, request, timeout = 5000) => {
  if (!client) {
    throw new Error('Auth service client not available');
  }

  const startTime = Date.now();

  try {
    const response = await new Promise((resolve, reject) => {
      const deadline = new Date(Date.now() + timeout);

      client[method](request, { deadline }, (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      });
    });

    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('auth-service', method, duration, 'success');

    logger.logGrpcCall('auth-service', method, request, response, duration * 1000);

    return response;
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('auth-service', method, duration, 'error');

    logger.logGrpcCall('auth-service', method, request, null, duration * 1000, error);

    throw error;
  }
};

/**
 * Health check
 */
const healthCheck = async () => {
  try {
    if (!authServiceClient) {
      throw new Error('Auth service client not initialized');
    }

    const response = await makeCall(authServiceClient, 'HealthCheck', {});
    return {
      status: 'healthy',
      message: 'Auth service is healthy',
      timestamp: new Date().toISOString(),
      data: response,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Auth service health check failed',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Authenticate user via auth service
 */
const authenticateUser = async (request) => {
  try {
    if (!authServiceClient || !isConnected) {
      // Fallback to HTTP if gRPC is not available
      return await authenticateUserHTTP(request);
    }

    const loginRequest = {
      username: request.username,
      password: request.password,
    };

    const response = await makeCall(authServiceClient, 'Login', loginRequest);

    if (response.success && response.user) {
      return {
        success: true,
        user: {
          id: response.user.id,
          username: response.user.username,
          email: response.user.email,
          profile: {
            firstName: response.user.firstName || '',
            lastName: response.user.lastName || '',
            dateOfBirth: response.user.dateOfBirth,
            country: response.user.country || '',
          },
          balance: response.user.balance || 0,
          roles: response.user.roles || ['player'],
          permissions: response.user.permissions || ['game:join', 'room:create'],
          isActive: response.user.isActive,
          createdAt: response.user.createdAt,
          lastLoginAt: response.user.lastLoginAt,
        },
        token: response.token,
      };
    }

    return response;
  } catch (error) {
    logger.error('Failed to authenticate user via gRPC:', error);
    // Fallback to HTTP
    return await authenticateUserHTTP(request);
  }
};

/**
 * HTTP fallback for authentication
 */
const authenticateUserHTTP = async (request) => {
  try {
    const axios = require('axios');
    
    const authServiceUrl = process.env.AUTH_SERVICE_HTTP_URL || 'http://localhost:8081';
    
    const response = await axios.post(`${authServiceUrl}/api/v1/auth/login`, {
      username: request.username,
      password: request.password,
    }, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data && response.data.success) {
      return {
        success: true,
        user: response.data.user,
        token: response.data.token,
      };
    }

    return {
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Invalid credentials',
      },
    };
  } catch (error) {
    logger.error('Failed to authenticate user via HTTP:', error);
    
    if (error.response && error.response.data) {
      return error.response.data;
    }
    
    return {
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Authentication service unavailable',
      },
    };
  }
};

/**
 * Create user via auth service
 */
const createUser = async (request) => {
  try {
    // For now, return not implemented
    // This would need to be implemented in the auth service
    return {
      success: false,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: 'User registration not implemented in auth service',
      },
    };
  } catch (error) {
    logger.error('Failed to create user:', error);
    throw error;
  }
};

module.exports = {
  initialize,
  close,
  healthCheck,
  authenticateUser,
  createUser,
};
