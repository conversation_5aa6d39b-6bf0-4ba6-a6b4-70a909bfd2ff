const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const config = require('../../config');
const logger = require('../../utils/logger');
const { recordGrpcRequest } = require('../../middleware/metrics');

// Proto file path (would need to be created)
const PROTO_PATH = path.join(__dirname, '../../../proto/game_service.proto');

// gRPC client instances
let gameServiceClient = null;
let roomServiceClient = null;
let isConnected = false;

/**
 * Load proto definition
 */
const loadProtoDefinition = () => {
  try {
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    });

    return grpc.loadPackageDefinition(packageDefinition);
  } catch (error) {
    logger.error('Failed to load game service proto definition:', error);
    throw error;
  }
};

/**
 * Create gRPC clients
 */
const createClients = () => {
  try {
    const proto = loadProtoDefinition();
    const GameService = proto.gameservice.GameService;
    const RoomService = proto.gameservice.RoomService;

    const gameClient = new GameService(
      config.grpc.gameService.url,
      grpc.credentials.createInsecure(),
      config.grpc.gameService.options
    );

    const roomClient = new RoomService(
      config.grpc.gameService.url,
      grpc.credentials.createInsecure(),
      config.grpc.gameService.options
    );

    return { gameClient, roomClient };
  } catch (error) {
    logger.error('Failed to create game service clients:', error);
    throw error;
  }
};

/**
 * Initialize gRPC clients
 */
const initialize = async () => {
  try {
    if (gameServiceClient && roomServiceClient && isConnected) {
      logger.info('Game service clients already initialized');
      return;
    }

    const clients = createClients();
    gameServiceClient = clients.gameClient;
    roomServiceClient = clients.roomClient;

    // Test connection
    await healthCheck();

    isConnected = true;
    logger.info('Game service clients initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize game service clients:', error);
    throw error;
  }
};

/**
 * Close gRPC clients
 */
const close = () => {
  if (gameServiceClient) {
    gameServiceClient.close();
    gameServiceClient = null;
  }
  if (roomServiceClient) {
    roomServiceClient.close();
    roomServiceClient = null;
  }
  isConnected = false;
  logger.info('Game service clients closed');
};

/**
 * Make gRPC call with error handling and metrics
 */
const makeCall = async (client, method, request, timeout = 30000) => {
  if (!client || !isConnected) {
    throw new Error('Game service client not initialized');
  }

  const startTime = Date.now();

  try {
    const response = await new Promise((resolve, reject) => {
      const deadline = new Date(Date.now() + timeout);

      client[method](request, { deadline }, (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      });
    });

    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('game-service', method, duration, 'success');

    logger.logGrpcCall('game-service', method, request, response, duration * 1000);

    return response;
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordGrpcRequest('game-service', method, duration, 'error');

    logger.logGrpcCall('game-service', method, request, null, duration * 1000, error);

    throw error;
  }
};

/**
 * Health check
 */
const healthCheck = async () => {
  try {
    // For now, return a mock health check
    // In a real implementation, this would call a health check method
    return {
      status: 'healthy',
      message: 'Game service is healthy',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Game service health check failed',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Create a new game room
 */
const createRoom = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'CreateRoom', request);
  } catch (error) {
    logger.error('Failed to create room:', error);
    throw error;
  }
};

/**
 * Join a game room
 */
const joinRoom = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'JoinRoom', request);
  } catch (error) {
    logger.error('Failed to join room:', error);
    throw error;
  }
};

/**
 * Leave a game room
 */
const leaveRoom = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'LeaveRoom', request);
  } catch (error) {
    logger.error('Failed to leave room:', error);
    throw error;
  }
};

/**
 * Start a game
 */
const startGame = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'StartGame', request);
  } catch (error) {
    logger.error('Failed to start game:', error);
    throw error;
  }
};

/**
 * Get game state
 */
const getGameState = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'GetGameState', request);
  } catch (error) {
    logger.error('Failed to get game state:', error);
    throw error;
  }
};

/**
 * Get game history
 */
const getGameHistory = async (request) => {
  try {
    return await makeCall(gameServiceClient, 'GetGameHistory', request);
  } catch (error) {
    logger.error('Failed to get game history:', error);
    throw error;
  }
};

// Note: ValidateGameResult method not available in current Game Service implementation

/**
 * Get available rooms
 */
const getRooms = async (request) => {
  try {
    return await makeCall(roomServiceClient, 'ListRooms', request);
  } catch (error) {
    logger.error('Failed to get rooms:', error);
    throw error;
  }
};

/**
 * Get room details
 */
const getRoomDetails = async (request) => {
  try {
    return await makeCall(roomServiceClient, 'GetRoom', request);
  } catch (error) {
    logger.error('Failed to get room details:', error);
    throw error;
  }
};

/**
 * Update room settings
 */
const updateRoom = async (request) => {
  try {
    return await makeCall(roomServiceClient, 'UpdateRoom', request);
  } catch (error) {
    logger.error('Failed to update room:', error);
    throw error;
  }
};

/**
 * Delete room
 */
const deleteRoom = async (request) => {
  try {
    return await makeCall(roomServiceClient, 'DeleteRoom', request);
  } catch (error) {
    logger.error('Failed to delete room:', error);
    throw error;
  }
};

module.exports = {
  initialize,
  close,
  healthCheck,
  createRoom,
  joinRoom,
  leaveRoom,
  startGame,
  getGameState,
  getGameHistory,
  getRooms,
  getRoomDetails,
  updateRoom,
  deleteRoom,
};
