const express = require('express');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

const config = require('../config');
const logger = require('../utils/logger');
const { validateBody } = require('../middleware/validation');
const { authSchemas } = require('../middleware/validation');
const { authMiddleware, optionalAuthMiddleware, blacklistToken } = require('../middleware/auth');
const { recordAuthAttempt, recordTokenIssued } = require('../middleware/metrics');
const { cache } = require('../config/redis');
const managerServiceClient = require('../services/grpc/managerServiceClient');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: config.rateLimit.auth.window,
  max: config.rateLimit.auth.max,
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many authentication attempts, please try again later',
      status_code: 429,
    },
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Generate JWT tokens and store user session
 * @param {Object} user - User object
 * @param {string} sessionId - Session ID
 * @param {string} deviceId - Device ID
 * @returns {Object} - Access and refresh tokens
 */
const generateTokens = async (user, sessionId, deviceId) => {
  const jti = uuidv4();
  const now = Math.floor(Date.now() / 1000);

  const userRoles = user.roles || ['player'];
  const primaryRole = userRoles[0] || 'player';

  const accessTokenPayload = {
    sub: user.id,
    username: user.username,
    role: primaryRole, // Add singular role field for Auth Service compatibility
    roles: userRoles, // Keep plural roles field for API Gateway compatibility
    permissions: user.permissions || ['game:join', 'room:create'],
    iat: now,
    exp: now + (60 * 60), // 1 hour
    jti,
    sessionId: sessionId, // Use camelCase to match Game Service expectations
    device_id: deviceId,
  };

  const refreshTokenPayload = {
    sub: user.id,
    type: 'refresh',
    iat: now,
    exp: now + (7 * 24 * 60 * 60), // 7 days
    jti: uuidv4(),
    sessionId: sessionId, // Use camelCase to match Game Service expectations
  };

  // Sign tokens with config issuer and audience for proper verification
  const tokenOptions = {
    issuer: config.jwt.issuer, // Use config issuer
    audience: config.jwt.audience, // Use config audience
  };

  const accessToken = jwt.sign(accessTokenPayload, config.jwt.secret, tokenOptions);
  const refreshToken = jwt.sign(refreshTokenPayload, config.jwt.secret, tokenOptions);

  // Store user session in Game Service format for compatibility
  try {
    const sessionKey = `session:${user.id}:${sessionId}`;
    const sessionInfo = {
      UserID: user.id,
      Username: user.username,
      Role: primaryRole, // Use the same primary role as in the token
      SessionID: sessionId,
      DeviceID: deviceId,
      TokenHash: '', // Will be set by Game Service if needed
      CreatedAt: new Date().toISOString(),
      ExpiresAt: new Date(accessTokenPayload.exp * 1000).toISOString(),
      IsActive: true,
      LastSeen: new Date().toISOString(),
    };

    // Store session with TTL based on token expiration (24 hours max)
    const ttl = Math.min(Math.max(0, accessTokenPayload.exp - now), 24 * 60 * 60);
    await cache.set(sessionKey, sessionInfo, ttl);

    logger.debug('User session stored in Game Service format', {
      userId: user.id,
      sessionId,
      username: user.username,
      sessionKey,
      ttl,
      expiresAt: sessionInfo.ExpiresAt,
    });

  } catch (error) {
    logger.error('Failed to store user session', {
      error: error.message,
      userId: user.id,
      sessionId,
      username: user.username,
    });
    // Don't throw error - session storage failure shouldn't block token generation
  }

  return { accessToken, refreshToken };
};

/**
 * POST /api/v1/auth/register
 * Register a new user account
 */
router.post('/register', authLimiter, validateBody(authSchemas.register), async (req, res, next) => {
  try {
    const { username, email, password, firstName, lastName, dateOfBirth, country } = req.body;

    logger.info('User registration attempt', { username, email });

    // Create user via Manager Service (send plain text password, manager service will hash it)
    const createUserRequest = {
      username,
      email,
      password, // Send plain text password
      profile: {
        firstName,
        lastName,
        dateOfBirth: new Date(dateOfBirth).toISOString(),
        country,
      },
      initialBalance: 1000, // Default starting balance
    };

    const userResponse = await managerServiceClient.createUser(createUserRequest);

    if (!userResponse.success) {
      recordAuthAttempt('register', 'failure');
      return res.status(400).json({
        success: false,
        error: {
          code: userResponse.error.code,
          message: userResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const user = userResponse.user;

    // Generate session and device IDs
    const sessionId = uuidv4();
    const deviceId = req.headers['x-device-id'] || uuidv4();

    // Generate tokens
    const { accessToken, refreshToken } = await generateTokens(user, sessionId, deviceId);

    // Cache user data
    await cache.set(`user:${user.id}`, user, config.cache.userProfileTTL);

    // Store refresh token
    await cache.set(`refresh_token:${sessionId}`, refreshToken, 7 * 24 * 60 * 60); // 7 days

    recordAuthAttempt('register', 'success');
    recordTokenIssued('access');
    recordTokenIssued('refresh');

    logger.logAuth('user_registered', user.id, user.username, true);

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          profile: user.profile,
          balance: user.balance,
        },
        token: accessToken,
        refreshToken,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    recordAuthAttempt('register', 'failure');
    next(error);
  }
});

/**
 * POST /api/v1/auth/login
 * Authenticate user and receive JWT token
 */
router.post('/login', authLimiter, validateBody(authSchemas.login), async (req, res, next) => {
  try {
    const { username, password } = req.body;

    logger.info('User login attempt', { username });

    // Authenticate via Manager Service
    const loginRequest = {
      username,
      password,
    };

    const loginResponse = await managerServiceClient.authenticateUser(loginRequest);

    if (!loginResponse.success) {
      recordAuthAttempt('login', 'failure');
      logger.logAuth('login_failed', null, username, false, 'Authentication failed');

      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid username or password',
          status_code: 401,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const user = loginResponse.user;

    // Generate session and device IDs
    const sessionId = uuidv4();
    const deviceId = req.headers['x-device-id'] || uuidv4();

    // Generate tokens (API Gateway tokens, not manager service tokens)
    const { accessToken, refreshToken } = await generateTokens(user, sessionId, deviceId);

    // Cache user data
    await cache.set(`user:${user.id}`, user, config.cache.userProfileTTL);

    // Store refresh token
    await cache.set(`refresh_token:${sessionId}`, refreshToken, 7 * 24 * 60 * 60); // 7 days

    recordAuthAttempt('login', 'success');
    recordTokenIssued('access');
    recordTokenIssued('refresh');

    logger.logAuth('user_logged_in', user.id, user.username, true);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          profile: user.profile,
          balance: user.balance,
        },
        token: accessToken,
        refreshToken,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    recordAuthAttempt('login', 'failure');
    next(error);
  }
});

/**
 * POST /api/v1/auth/logout
 * Invalidate current JWT token (works with or without valid token)
 */
router.post('/logout', optionalAuthMiddleware, async (req, res, next) => {
  try {
    // If user has a valid token, perform full logout cleanup
    if (req.user && req.token) {
      const { jti, exp, sessionId } = req.token;

      // Blacklist the access token
      await blacklistToken(jti, exp);

      // Remove refresh token
      if (sessionId) {
        await cache.del(`refresh_token:${sessionId}`);
      }

      // Remove user session (use Game Service format)
      const sessionKey = `session:${req.user.id}:${sessionId}`;
      await cache.del(sessionKey);

      logger.logAuth('user_logged_out', req.user.id, req.user.username, true);
    } else {
      // User doesn't have a valid token, but still allow logout
      // This handles cases where token is expired, invalid, or missing
      logger.info('Logout attempt without valid token', {
        hasUser: !!req.user,
        hasToken: !!req.token,
        requestId: req.requestId,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      });
    }

    // Always return success for logout, regardless of token validity
    // This prevents client-side issues when tokens are expired/invalid
    res.json({
      success: true,
      data: {
        message: 'Logged out successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    // Even if cleanup fails, return success for logout
    // The important thing is that the client knows to clear their token
    logger.error('Logout cleanup failed, but returning success', {
      error: error.message,
      requestId: req.requestId,
      hasUser: !!req.user,
      hasToken: !!req.token,
    });

    res.json({
      success: true,
      data: {
        message: 'Logged out successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  }
});

/**
 * POST /api/v1/auth/refresh
 * Refresh JWT token
 */
router.post('/refresh', validateBody(authSchemas.refreshToken), async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.secret, {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
    });

    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN_TYPE',
          message: 'Invalid token type',
          status_code: 401,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    // Check if refresh token exists in cache
    const storedToken = await cache.get(`refresh_token:${decoded.sessionId}`);

    if (!storedToken || storedToken !== refreshToken) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token',
          status_code: 401,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    // Get user data
    const getUserRequest = { userId: decoded.sub };
    const userResponse = await managerServiceClient.getUserById(getUserRequest);

    if (!userResponse.success) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found',
          status_code: 401,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const user = userResponse.user;

    // Generate new tokens
    const deviceId = req.headers['x-device-id'] || uuidv4();
    const { accessToken, refreshToken: newRefreshToken } = await generateTokens(user, decoded.sessionId, deviceId);

    // Update refresh token in cache
    await cache.set(`refresh_token:${decoded.sessionId}`, newRefreshToken, 7 * 24 * 60 * 60);

    recordTokenIssued('access');
    recordTokenIssued('refresh');

    logger.logAuth('token_refreshed', user.id, user.username, true);

    res.json({
      success: true,
      data: {
        token: accessToken,
        refreshToken: newRefreshToken,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError' || error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token',
          status_code: 401,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }
    next(error);
  }
});

module.exports = router;
