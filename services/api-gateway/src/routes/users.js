const express = require('express');
const { validateBody, validateParams, userSchemas } = require('../middleware/validation');
const { requirePlayer, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');
const config = require('../config');
const managerServiceClient = require('../services/grpc/managerServiceClient');

const router = express.Router();

/**
 * GET /api/v1/users/profile
 * Get current user's profile information
 */
router.get('/profile', requirePlayer, async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Use the same cache key as login process
    const cacheKey = `user:${userId}`;
    let userProfile = await cache.get(cacheKey);

    if (!userProfile) {
      // If not in cache, this indicates a problem with login caching
      // For now, return the user data from req.user which should have the real data
      userProfile = req.user;

      // Cache it for future requests
      await cache.set(cacheKey, userProfile, config.cache.userProfileTTL);
    }

    res.json({
      success: true,
      data: {
        id: userProfile.id,
        username: userProfile.username,
        email: userProfile.email,
        profile: userProfile.profile,
        balance: userProfile.balance,
        statistics: userProfile.statistics || {
          gamesPlayed: 0,
          gamesWon: 0,
          totalWinnings: 0,
          totalLosses: 0,
        },
        createdAt: userProfile.createdAt,
        lastLoginAt: userProfile.lastLoginAt,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/v1/users/profile
 * Update user profile information
 */
router.put('/profile', requirePlayer, validateBody(userSchemas.updateProfile), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const updateData = req.body;

    // Update via Manager Service
    const updateRequest = {
      userId,
      ...updateData,
    };

    const updateResponse = await managerServiceClient.updateUserProfile(updateRequest);

    if (!updateResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: updateResponse.error.code,
          message: updateResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const updatedUser = updateResponse.user;

    // Update cache
    const cacheKey = `user_profile:${userId}`;
    await cache.set(cacheKey, updatedUser, config.cache.userProfileTTL);

    // Also update the main user cache
    await cache.set(`user:${userId}`, updatedUser, config.cache.userProfileTTL);

    logger.info('User profile updated', {
      userId,
      username: req.user.username,
      updatedFields: Object.keys(updateData),
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        profile: updatedUser.profile,
        balance: updatedUser.balance,
        updatedAt: updatedUser.updatedAt,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/users/balance
 * Get current user's balance information
 */
router.get('/balance', requirePlayer, async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get balance from Manager Service
    const getBalanceRequest = { userId };
    const balanceResponse = await managerServiceClient.getUserBalance(getBalanceRequest);

    if (!balanceResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'BALANCE_NOT_FOUND',
          message: 'User balance not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const balance = balanceResponse.balance;

    res.json({
      success: true,
      data: {
        current: balance.current,
        locked: balance.locked || 0,
        currency: balance.currency,
        lastUpdated: balance.lastUpdated,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/v1/users/settings
 * Update user settings
 */
router.put('/settings', requirePlayer, validateBody(userSchemas.updateProfile), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { settings } = req.body;

    if (!settings) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_SETTINGS',
          message: 'Settings object is required',
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    // Update settings via Manager Service
    const updateRequest = {
      userId,
      settings,
    };

    const updateResponse = await managerServiceClient.updateUserSettings(updateRequest);

    if (!updateResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: updateResponse.error.code,
          message: updateResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    // Clear user cache to force refresh
    await cache.del(`user:${userId}`);
    await cache.del(`user_profile:${userId}`);

    logger.info('User settings updated', {
      userId,
      username: req.user.username,
      settings,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        settings: updateResponse.settings,
        message: 'Settings updated successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/users/:userId (Admin only)
 * Get user by ID
 */
router.get('/:userId', requireAdmin, validateParams(userSchemas.getUserById), async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Get user from Manager Service
    const getUserRequest = { userId };
    const userResponse = await managerServiceClient.getUserById(getUserRequest);

    if (!userResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const user = userResponse.user;

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        balance: user.balance,
        statistics: user.statistics,
        isActive: user.isActive,
        roles: user.roles,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/v1/users/account
 * Delete current user's account
 */
router.delete('/account', requirePlayer, async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Delete account via Manager Service
    const deleteRequest = { userId };
    const deleteResponse = await managerServiceClient.deleteUser(deleteRequest);

    if (!deleteResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: deleteResponse.error.code,
          message: deleteResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    // Clear all user caches
    await cache.del(`user:${userId}`);
    await cache.del(`user_profile:${userId}`);

    logger.info('User account deleted', {
      userId,
      username: req.user.username,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        message: 'Account deleted successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
