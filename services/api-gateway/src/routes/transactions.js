const express = require('express');
const { validateBody, validateQuery, validateParams, transactionSchemas } = require('../middleware/validation');
const { requirePlayer } = require('../middleware/auth');
const logger = require('../utils/logger');
const managerServiceClient = require('../services/grpc/managerServiceClient');

const router = express.Router();

/**
 * GET /api/v1/transactions
 * Get user's transaction history
 */
router.get('/', validateQuery(transactionSchemas.getTransactions), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page, limit, type, status, startDate, endDate } = req.query;

    const getTransactionsRequest = {
      userId,
      page,
      limit,
      filters: {
        type,
        status,
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
      },
    };

    const transactionsResponse = await managerServiceClient.getUserTransactions(getTransactionsRequest);

    if (!transactionsResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: transactionsResponse.error.code,
          message: transactionsResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        transactions: transactionsResponse.transactions,
        pagination: transactionsResponse.pagination,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/transactions/deposit
 * Create a deposit transaction
 */
router.post('/deposit', validateBody(transactionSchemas.createDeposit), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { amount, currency, paymentMethod, paymentDetails } = req.body;

    const createDepositRequest = {
      userId,
      amount,
      currency,
      paymentMethod,
      paymentDetails,
    };

    const depositResponse = await managerServiceClient.createDeposit(createDepositRequest);

    if (!depositResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: depositResponse.error.code,
          message: depositResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Deposit transaction created', {
      userId,
      username: req.user.username,
      amount,
      currency,
      paymentMethod,
      transactionId: depositResponse.transaction.id,
      requestId: req.requestId,
    });

    res.status(201).json({
      success: true,
      data: {
        transaction: depositResponse.transaction,
        message: 'Deposit transaction created successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/transactions/withdrawal
 * Create a withdrawal transaction
 */
router.post('/withdrawal', validateBody(transactionSchemas.createWithdrawal), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { amount, currency, withdrawalMethod, withdrawalDetails } = req.body;

    const createWithdrawalRequest = {
      userId,
      amount,
      currency,
      withdrawalMethod,
      withdrawalDetails,
    };

    const withdrawalResponse = await managerServiceClient.createWithdrawal(createWithdrawalRequest);

    if (!withdrawalResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: withdrawalResponse.error.code,
          message: withdrawalResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Withdrawal transaction created', {
      userId,
      username: req.user.username,
      amount,
      currency,
      withdrawalMethod,
      transactionId: withdrawalResponse.transaction.id,
      requestId: req.requestId,
    });

    res.status(201).json({
      success: true,
      data: {
        transaction: withdrawalResponse.transaction,
        message: 'Withdrawal transaction created successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/transactions/:transactionId
 * Get transaction details
 */
router.get('/:transactionId', validateParams(transactionSchemas.getTransactionById), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { transactionId } = req.params;

    const getTransactionRequest = {
      transactionId,
      userId, // Ensure user can only access their own transactions
    };

    const transactionResponse = await managerServiceClient.getTransactionById(getTransactionRequest);

    if (!transactionResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'TRANSACTION_NOT_FOUND',
          message: 'Transaction not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const transaction = transactionResponse.transaction;

    // Ensure user can only access their own transactions
    if (transaction.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: 'Access denied to this transaction',
          status_code: 403,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        transaction,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/transactions/stats
 * Get transaction statistics for the user
 */
router.get('/stats', async (req, res, next) => {
  try {
    const userId = req.user.id;

    // This would be implemented in the Manager Service
    const statsRequest = { userId };
    
    // Mock response for now
    const stats = {
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalBets: 0,
      totalWinnings: 0,
      netBalance: 0,
      transactionCount: 0,
      lastTransactionDate: null,
    };

    res.json({
      success: true,
      data: {
        stats,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
