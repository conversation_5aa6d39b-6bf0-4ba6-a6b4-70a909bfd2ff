const express = require('express');
const { validateBody, validateQuery, validateParams, roomSchemas } = require('../middleware/validation');
const { requirePlayer, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');
const gameServiceClient = require('../services/grpc/gameServiceClient');
const managerServiceClient = require('../services/grpc/managerServiceClient');

const router = express.Router();

/**
 * GET /api/v1/rooms
 * Get list of available rooms
 */
router.get('/', validateQuery(roomSchemas.getRooms), async (req, res, next) => {
  try {
    const { page, limit, gameType, status, minBet, maxBet } = req.query;

    const getRoomsRequest = {
      page,
      limit,
      filters: {
        gameType,
        status,
        minBet,
        maxBet,
      },
    };

    const roomsResponse = await gameServiceClient.getRooms(getRoomsRequest);

    if (!roomsResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: roomsResponse.error.code,
          message: roomsResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        rooms: roomsResponse.rooms,
        pagination: roomsResponse.pagination,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/rooms
 * Create a new room
 */
router.post('/', validateBody(roomSchemas.createRoom), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { name, gameType, betAmount, currency, maxPlayers, isPrivate, password } = req.body;

    const createRoomRequest = {
      creatorId: userId,
      roomName: name,
      gameType: gameType.toUpperCase(),
      config: {
        maxPlayers,
        betAmount,
        currency,
        gameDuration: 30, // Default game duration
        gameSpecificConfig: {},
      },
      isPrivate,
      password,
    };

    const roomResponse = await gameServiceClient.createRoom(createRoomRequest);

    if (!roomResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: roomResponse.error.code,
          message: roomResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Room created', {
      userId,
      username: req.user.username,
      roomId: roomResponse.room.id,
      roomName: name,
      gameType,
      betAmount,
      maxPlayers,
      requestId: req.requestId,
    });

    res.status(201).json({
      success: true,
      data: {
        room: roomResponse.room,
        message: 'Room created successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/rooms/:roomId
 * Get detailed information about a specific room
 */
router.get('/:roomId', validateParams(roomSchemas.getRoomById), async (req, res, next) => {
  try {
    const { roomId } = req.params;

    // Call Manager Service directly for room details (consistent with bypass approach)
    // This avoids the Game Service authentication issues we've been bypassing
    const roomResponse = await managerServiceClient.getRoomDetails(roomId);

    if (!roomResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ROOM_NOT_FOUND',
          message: 'Room not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        room: roomResponse.data.room,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/rooms/:roomId/join
 * Join a room
 */
router.post('/:roomId/join', validateParams(roomSchemas.joinRoom), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { roomId } = req.params;
    const { password } = req.body;

    const joinRoomRequest = {
      roomId,
      playerId: userId,
      password,
    };

    const joinResponse = await gameServiceClient.joinRoom(joinRoomRequest);

    if (!joinResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: joinResponse.error.code,
          message: joinResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('User joined room', {
      userId,
      username: req.user.username,
      roomId,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        room: joinResponse.room,
        player: joinResponse.player,
        message: 'Successfully joined room',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/rooms/:roomId/leave
 * Leave a room
 */
router.post('/:roomId/leave', validateParams(roomSchemas.getRoomById), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { roomId } = req.params;

    const leaveRoomRequest = {
      roomId,
      playerId: userId,
    };

    const leaveResponse = await gameServiceClient.leaveRoom(leaveRoomRequest);

    if (!leaveResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: leaveResponse.error.code,
          message: leaveResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('User left room', {
      userId,
      username: req.user.username,
      roomId,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        message: 'Successfully left room',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/v1/rooms/:roomId
 * Update room settings (creator or admin only)
 */
router.put('/:roomId', validateParams(roomSchemas.getRoomById), validateBody(roomSchemas.updateRoom), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { roomId } = req.params;
    const updateData = req.body;

    // First, get room details to check if user is the creator
    const getRoomRequest = { roomId };
    const roomResponse = await gameServiceClient.getRoomDetails(getRoomRequest);

    if (!roomResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ROOM_NOT_FOUND',
          message: 'Room not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const room = roomResponse.room;

    // Check if user is the creator or admin
    const isCreator = room.creatorId === userId;
    const isAdmin = req.user.roles && req.user.roles.includes('admin');

    if (!isCreator && !isAdmin) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: 'Only room creator or admin can update room settings',
          status_code: 403,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const updateRoomRequest = {
      roomId,
      updaterId: userId,
      ...updateData,
    };

    const updateResponse = await gameServiceClient.updateRoom(updateRoomRequest);

    if (!updateResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: updateResponse.error.code,
          message: updateResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Room updated', {
      userId,
      username: req.user.username,
      roomId,
      updatedFields: Object.keys(updateData),
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        room: updateResponse.room,
        message: 'Room updated successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/v1/rooms/:roomId
 * Delete room (creator or admin only)
 */
router.delete('/:roomId', validateParams(roomSchemas.getRoomById), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { roomId } = req.params;

    // First, get room details to check if user is the creator
    const getRoomRequest = { roomId };
    const roomResponse = await gameServiceClient.getRoomDetails(getRoomRequest);

    if (!roomResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ROOM_NOT_FOUND',
          message: 'Room not found',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const room = roomResponse.room;

    // Check if user is the creator or admin
    const isCreator = room.creatorId === userId;
    const isAdmin = req.user.roles && req.user.roles.includes('admin');

    if (!isCreator && !isAdmin) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: 'Only room creator or admin can delete room',
          status_code: 403,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const deleteRoomRequest = {
      roomId,
      deleterId: userId,
    };

    const deleteResponse = await gameServiceClient.deleteRoom(deleteRoomRequest);

    if (!deleteResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: deleteResponse.error.code,
          message: deleteResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Room deleted', {
      userId,
      username: req.user.username,
      roomId,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        message: 'Room deleted successfully',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
