const express = require('express');
const config = require('../config');
const logger = require('../utils/logger');
const { getMetrics } = require('../middleware/metrics');
const { healthCheck: dbHealthCheck } = require('../config/database');
const { healthCheck: redisHealthCheck } = require('../config/redis');
const gameServiceClient = require('../services/grpc/gameServiceClient');
const managerServiceClient = require('../services/grpc/managerServiceClient');

const router = express.Router();

/**
 * GET /health
 * Health check endpoint
 */
router.get('/health', async (req, res) => {
  try {
    const startTime = Date.now();

    // Check all service dependencies
    const [dbHealth, redisHealth, gameServiceHealth, managerServiceHealth] = await Promise.allSettled([
      dbHealthCheck(),
      redisHealthCheck(),
      gameServiceClient.healthCheck(),
      managerServiceClient.healthCheck(),
    ]);

    const responseTime = Date.now() - startTime;

    // Determine overall health status
    const services = {
      database: dbHealth.status === 'fulfilled' ? dbHealth.value : { status: 'unhealthy', error: dbHealth.reason?.message },
      redis: redisHealth.status === 'fulfilled' ? redisHealth.value : { status: 'unhealthy', error: redisHealth.reason?.message },
      gameService: gameServiceHealth.status === 'fulfilled' ? gameServiceHealth.value : { status: 'unhealthy', error: gameServiceHealth.reason?.message },
      managerService: managerServiceHealth.status === 'fulfilled' ? managerServiceHealth.value : { status: 'unhealthy', error: managerServiceHealth.reason?.message },
    };

    const allHealthy = Object.values(services).every(service => service.status === 'healthy');
    const overallStatus = allHealthy ? 'healthy' : 'unhealthy';
    const statusCode = allHealthy ? 200 : 503;

    const healthData = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.env,
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      services,
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
        },
        cpu: {
          usage: process.cpuUsage(),
        },
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      },
    };

    res.status(statusCode).json({
      success: allHealthy,
      data: healthData,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });

    // Log health check
    if (!allHealthy) {
      logger.warn('Health check failed', {
        services,
        responseTime,
        requestId: req.requestId,
      });
    }
  } catch (error) {
    logger.error('Health check error:', error);

    res.status(503).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Health check failed',
        status_code: 503,
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  }
});

/**
 * GET /health/ready
 * Readiness probe for Kubernetes
 */
router.get('/health/ready', async (req, res) => {
  try {
    // Check critical dependencies only
    const [dbHealth, redisHealth] = await Promise.allSettled([
      dbHealthCheck(),
      redisHealthCheck(),
    ]);

    const dbHealthy = dbHealth.status === 'fulfilled' && dbHealth.value.status === 'healthy';
    const redisHealthy = redisHealth.status === 'fulfilled' && redisHealth.value.status === 'healthy';

    const ready = dbHealthy && redisHealthy;

    if (ready) {
      res.status(200).json({
        success: true,
        data: {
          status: 'ready',
          timestamp: new Date().toISOString(),
        },
      });
    } else {
      res.status(503).json({
        success: false,
        error: {
          code: 'NOT_READY',
          message: 'Service not ready',
          status_code: 503,
          timestamp: new Date().toISOString(),
        },
      });
    }
  } catch (error) {
    res.status(503).json({
      success: false,
      error: {
        code: 'READINESS_CHECK_FAILED',
        message: 'Readiness check failed',
        status_code: 503,
        timestamp: new Date().toISOString(),
      },
    });
  }
});

/**
 * GET /health/live
 * Liveness probe for Kubernetes
 */
router.get('/health/live', (req, res) => {
  // Simple liveness check - if the process is running, it's alive
  res.status(200).json({
    success: true,
    data: {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    },
  });
});

/**
 * GET /metrics
 * Prometheus metrics endpoint
 */
router.get('/metrics', async (req, res) => {
  try {
    if (!config.features.metricsEndpointEnabled) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Metrics endpoint is disabled',
          status_code: 404,
        },
      });
    }

    const metrics = await getMetrics();

    res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    res.send(metrics);
  } catch (error) {
    logger.error('Failed to get metrics:', error);

    res.status(500).json({
      success: false,
      error: {
        code: 'METRICS_ERROR',
        message: 'Failed to retrieve metrics',
        status_code: 500,
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  }
});

/**
 * GET /info
 * Service information endpoint
 */
router.get('/info', (req, res) => {
  const info = {
    service: 'api-gateway',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.env,
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    },
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    features: {
      swaggerEnabled: config.features.swaggerEnabled,
      metricsEnabled: config.monitoring.metricsEnabled,
      debugMode: config.features.debugMode,
      maintenanceMode: config.features.maintenanceMode,
    },
    endpoints: {
      health: '/health',
      metrics: '/metrics',
      docs: '/api-docs',
      api: '/api/v1',
    },
  };

  res.json({
    success: true,
    data: info,
    meta: {
      timestamp: new Date().toISOString(),
      request_id: req.requestId,
    },
  });
});

/**
 * GET /version
 * Service version endpoint
 */
router.get('/version', (req, res) => {
  res.json({
    success: true,
    data: {
      version: process.env.npm_package_version || '1.0.0',
      buildDate: process.env.BUILD_DATE || new Date().toISOString(),
      gitCommit: process.env.GIT_COMMIT || 'unknown',
      gitBranch: process.env.GIT_BRANCH || 'unknown',
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: req.requestId,
    },
  });
});

/**
 * POST /maintenance
 * Toggle maintenance mode (admin only)
 */
router.post('/maintenance', async (req, res) => {
  try {
    // This would require admin authentication in a real implementation
    const { enabled } = req.body;

    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'enabled field must be a boolean',
          status_code: 400,
        },
      });
    }

    // In a real implementation, this would update the configuration
    // For now, just return the current state
    res.json({
      success: true,
      data: {
        maintenanceMode: enabled,
        message: enabled ? 'Maintenance mode enabled' : 'Maintenance mode disabled',
        timestamp: new Date().toISOString(),
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });

    logger.info(`Maintenance mode ${enabled ? 'enabled' : 'disabled'}`, {
      requestId: req.requestId,
    });
  } catch (error) {
    logger.error('Failed to toggle maintenance mode:', error);

    res.status(500).json({
      success: false,
      error: {
        code: 'MAINTENANCE_TOGGLE_FAILED',
        message: 'Failed to toggle maintenance mode',
        status_code: 500,
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  }
});

module.exports = router;
