const express = require('express');
const { validateQuery, validateParams, gameSchemas } = require('../middleware/validation');
const { requirePlayer } = require('../middleware/auth');
const logger = require('../utils/logger');
const gameServiceClient = require('../services/grpc/gameServiceClient');

const router = express.Router();

/**
 * GET /api/v1/games/history
 * Get user's game history
 */
router.get('/history', validateQuery(gameSchemas.getGameHistory), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page, limit, gameType, startDate, endDate } = req.query;

    const getGameHistoryRequest = {
      userId,
      page,
      limit,
      filters: {
        gameType,
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
      },
    };

    const historyResponse = await gameServiceClient.getGameHistory(getGameHistoryRequest);

    if (!historyResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: historyResponse.error.code,
          message: historyResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        games: historyResponse.games,
        pagination: historyResponse.pagination,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/games/:gameId
 * Get detailed information about a specific game
 */
router.get('/:gameId', validateParams(gameSchemas.getGameById), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { gameId } = req.params;

    const getGameRequest = {
      gameId,
      requesterId: userId, // To ensure user can only access games they participated in
    };

    const gameResponse = await gameServiceClient.getGameState(getGameRequest);

    if (!gameResponse.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'GAME_NOT_FOUND',
          message: 'Game not found or access denied',
          status_code: 404,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    const game = gameResponse.game;

    // Check if user participated in this game
    const userParticipated = game.participants.some(participant => participant.userId === userId);
    const isAdmin = req.user.roles && req.user.roles.includes('admin');

    if (!userParticipated && !isAdmin) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: 'Access denied to this game',
          status_code: 403,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    res.json({
      success: true,
      data: {
        game,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/games/stats
 * Get user's game statistics
 */
router.get('/stats', async (req, res, next) => {
  try {
    const userId = req.user.id;

    // This would be implemented in the Game Service
    const getStatsRequest = { userId };
    
    // Mock response for now
    const stats = {
      totalGames: 0,
      gamesWon: 0,
      gamesLost: 0,
      winRate: 0,
      totalWinnings: 0,
      totalLosses: 0,
      netProfit: 0,
      favoriteGameType: null,
      averageGameDuration: 0,
      longestWinStreak: 0,
      currentWinStreak: 0,
      gameTypeStats: {
        prizewheel: {
          played: 0,
          won: 0,
          winRate: 0,
          totalWinnings: 0,
        },
        amidakuji: {
          played: 0,
          won: 0,
          winRate: 0,
          totalWinnings: 0,
        },
      },
    };

    res.json({
      success: true,
      data: {
        stats,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/v1/games/leaderboard
 * Get game leaderboard
 */
router.get('/leaderboard', async (req, res, next) => {
  try {
    const { gameType, period = 'all', limit = 10 } = req.query;

    // This would be implemented in the Game Service
    const getLeaderboardRequest = {
      gameType,
      period, // 'daily', 'weekly', 'monthly', 'all'
      limit: Math.min(limit, 100), // Cap at 100
    };

    // Mock response for now
    const leaderboard = {
      period,
      gameType: gameType || 'all',
      rankings: [],
      userRank: null, // Current user's rank
      lastUpdated: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: {
        leaderboard,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/v1/games/:gameId/validate
 * Validate game result (for fairness verification)
 */
router.post('/:gameId/validate', validateParams(gameSchemas.getGameById), async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { gameId } = req.params;

    const validateRequest = {
      gameId,
      requesterId: userId,
    };

    const validateResponse = await gameServiceClient.validateGameResult(validateRequest);

    if (!validateResponse.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: validateResponse.error.code,
          message: validateResponse.error.message,
          status_code: 400,
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
        },
      });
    }

    logger.info('Game result validation requested', {
      userId,
      username: req.user.username,
      gameId,
      requestId: req.requestId,
    });

    res.json({
      success: true,
      data: {
        validation: validateResponse.validation,
        message: 'Game result validation completed',
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.requestId,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
