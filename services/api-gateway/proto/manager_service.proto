syntax = "proto3";

package manager;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

// Manager Service Definition
service ManagerService {
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserByUsername(GetUserByUsernameRequest) returns (GetUserResponse);
  rpc UpdateUserProfile(UpdateUserProfileRequest) returns (UpdateUserProfileResponse);
  rpc UpdateUserSettings(UpdateUserSettingsRequest) returns (UpdateUserSettingsResponse);
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
  rpc GetUserBalance(GetUserBalanceRequest) returns (GetUserBalanceResponse);
  rpc UpdateBalance(UpdateBalanceRequest) returns (UpdateBalanceResponse);
  rpc ProcessTransaction(ProcessTransactionRequest) returns (ProcessTransactionResponse);
  rpc GetUserTransactions(GetUserTransactionsRequest) returns (GetUserTransactionsResponse);
  rpc GetTransactionById(GetTransactionByIdRequest) returns (GetTransactionByIdResponse);
  rpc ValidateUser(ValidateUserRequest) returns (ValidateUserResponse);
  rpc CreateDeposit(CreateDepositRequest) returns (CreateDepositResponse);
  rpc CreateWithdrawal(CreateWithdrawalRequest) returns (CreateWithdrawalResponse);
}

// Common Messages
message ErrorResponse {
  string code = 1;
  string message = 2;
  int32 status_code = 3;
  google.protobuf.Any details = 4;
  string correlation_id = 5;
  bool retryable = 6;
  int64 timestamp = 7;
}

message Pagination {
  int32 page = 1;
  int32 limit = 2;
  int32 total = 3;
  int32 pages = 4;
}

// User Profile
message UserProfile {
  string first_name = 1;
  string last_name = 2;
  string avatar = 3;
  string country = 4;
  string date_of_birth = 5;
}

// User Settings
message UserSettings {
  bool notifications = 1;
  bool sound_enabled = 2;
  string language = 3;
  string theme = 4;
}

// User Statistics
message UserStatistics {
  int32 games_played = 1;
  int32 games_won = 2;
  int64 total_winnings = 3;
  int64 total_losses = 4;
  double win_rate = 5;
}

// User Balance
message UserBalance {
  string user_id = 1;
  int64 current_balance = 2;
  int64 previous_balance = 3;
  int64 locked_balance = 4;
  string currency = 5;
  int64 updated_at = 6;
}

// User
message User {
  string id = 1;
  string username = 2;
  string email = 3;
  string password = 4;
  UserProfile profile = 5;
  UserBalance balance = 6;
  UserSettings settings = 7;
  UserStatistics statistics = 8;
  repeated string roles = 9;
  repeated string permissions = 10;
  bool is_active = 11;
  int64 created_at = 12;
  int64 updated_at = 13;
  int64 last_login_at = 14;
}

// Transaction
message Transaction {
  string id = 1;
  string user_id = 2;
  int64 amount = 3;
  string type = 4;
  string status = 5;
  string reason = 6;
  string game_id = 7;
  string room_id = 8;
  map<string, string> metadata = 9;
  int64 created_at = 10;
  int64 updated_at = 11;
}

// Payment Details
message PaymentDetails {
  string card_token = 1;
  BillingAddress billing_address = 2;
}

message BillingAddress {
  string street = 1;
  string city = 2;
  string country = 3;
  string postal_code = 4;
}

// Withdrawal Details
message WithdrawalDetails {
  string bank_account = 1;
  string routing_number = 2;
  string paypal_email = 3;
}

// Create User
message CreateUserRequest {
  string username = 1;
  string email = 2;
  string password = 3;
  UserProfile profile = 4;
  int64 initial_balance = 5;
}

message CreateUserResponse {
  bool success = 1;
  User user = 2;
  ErrorResponse error = 3;
}

// Get User
message GetUserRequest {
  string user_id = 1;
}

message GetUserByUsernameRequest {
  string username = 1;
}

message GetUserResponse {
  bool success = 1;
  User user = 2;
  ErrorResponse error = 3;
}

// Update User Profile
message UpdateUserProfileRequest {
  string user_id = 1;
  string first_name = 2;
  string last_name = 3;
  string avatar = 4;
  string country = 5;
}

message UpdateUserProfileResponse {
  bool success = 1;
  User user = 2;
  ErrorResponse error = 3;
}

// Update User Settings
message UpdateUserSettingsRequest {
  string user_id = 1;
  UserSettings settings = 2;
}

message UpdateUserSettingsResponse {
  bool success = 1;
  UserSettings settings = 2;
  ErrorResponse error = 3;
}

// Delete User
message DeleteUserRequest {
  string user_id = 1;
}

message DeleteUserResponse {
  bool success = 1;
  string message = 2;
  ErrorResponse error = 3;
}

// Get User Balance
message GetUserBalanceRequest {
  string user_id = 1;
}

message GetUserBalanceResponse {
  bool success = 1;
  UserBalance balance = 2;
  ErrorResponse error = 3;
}

// Update Balance
message UpdateBalanceRequest {
  string user_id = 1;
  int64 amount = 2;
  string reason = 3;
  string game_id = 4;
  string transaction_id = 5;
  map<string, string> metadata = 6;
}

message UpdateBalanceResponse {
  bool success = 1;
  UserBalance balance = 2;
  Transaction transaction = 3;
  ErrorResponse error = 4;
}

// Process Transaction
message ProcessTransactionRequest {
  string user_id = 1;
  int64 amount = 2;
  string type = 3;
  string reason = 4;
  map<string, string> metadata = 5;
}

message ProcessTransactionResponse {
  bool success = 1;
  Transaction transaction = 2;
  ErrorResponse error = 3;
}

// Get User Transactions
message GetUserTransactionsRequest {
  string user_id = 1;
  int32 page = 2;
  int32 limit = 3;
  TransactionFilters filters = 4;
}

message TransactionFilters {
  string type = 1;
  string status = 2;
  string start_date = 3;
  string end_date = 4;
}

message GetUserTransactionsResponse {
  bool success = 1;
  repeated Transaction transactions = 2;
  Pagination pagination = 3;
  ErrorResponse error = 4;
}

// Get Transaction By ID
message GetTransactionByIdRequest {
  string transaction_id = 1;
  string user_id = 2;
}

message GetTransactionByIdResponse {
  bool success = 1;
  Transaction transaction = 2;
  ErrorResponse error = 3;
}

// Validate User
message ValidateUserRequest {
  string user_id = 1;
  string username = 2;
}

message ValidateUserResponse {
  bool success = 1;
  bool is_valid = 2;
  User user = 3;
  ErrorResponse error = 4;
}

// Create Deposit
message CreateDepositRequest {
  string user_id = 1;
  int64 amount = 2;
  string currency = 3;
  string payment_method = 4;
  PaymentDetails payment_details = 5;
}

message CreateDepositResponse {
  bool success = 1;
  Transaction transaction = 2;
  ErrorResponse error = 3;
}

// Create Withdrawal
message CreateWithdrawalRequest {
  string user_id = 1;
  int64 amount = 2;
  string currency = 3;
  string withdrawal_method = 4;
  WithdrawalDetails withdrawal_details = 5;
}

message CreateWithdrawalResponse {
  bool success = 1;
  Transaction transaction = 2;
  ErrorResponse error = 3;
}
