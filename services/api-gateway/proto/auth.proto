syntax = "proto3";

package auth;

// Auth service definition
service AuthService {
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
  
  // User authentication
  rpc Login(LoginRequest) returns (LoginResponse);
  
  // User registration
  rpc Register(RegisterRequest) returns (RegisterResponse);
  
  // Token validation
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  
  // Token refresh
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
}

// Health check messages
message HealthCheckRequest {}

message HealthCheckResponse {
  string status = 1;
  string message = 2;
}

// Login messages
message LoginRequest {
  string username = 1;
  string password = 2;
}

message LoginResponse {
  bool success = 1;
  User user = 2;
  string token = 3;
  string refresh_token = 4;
  string message = 5;
}

// Register messages
message RegisterRequest {
  string username = 1;
  string email = 2;
  string password = 3;
  UserProfile profile = 4;
}

message RegisterResponse {
  bool success = 1;
  User user = 2;
  string message = 3;
}

// Token validation messages
message ValidateTokenRequest {
  string token = 1;
}

message ValidateTokenResponse {
  bool valid = 1;
  User user = 2;
  string message = 3;
}

// Token refresh messages
message RefreshTokenRequest {
  string refresh_token = 1;
}

message RefreshTokenResponse {
  bool success = 1;
  string token = 2;
  string refresh_token = 3;
  string message = 4;
}

// User profile
message UserProfile {
  string first_name = 1;
  string last_name = 2;
  string date_of_birth = 3;
  string country = 4;
}

// User message
message User {
  string id = 1;
  string username = 2;
  string email = 3;
  UserProfile profile = 4;
  double balance = 5;
  repeated string roles = 6;
  repeated string permissions = 7;
  bool is_active = 8;
  string created_at = 9;
  string last_login_at = 10;
}
