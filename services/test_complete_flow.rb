#!/usr/bin/env ruby

# Test complete flow from room creation to socket subscription
require 'net/http'
require 'json'
require 'uri'

puts "=== Complete Flow Test ==="

# Test 1: Create a room with players via manager-service
puts "\n1️⃣ CREATING ROOM WITH PLAYERS"

# First, let's check if manager-service is running
begin
  uri = URI("http://localhost:3001/health")
  http = Net::HTTP.new(uri.host, uri.port)
  http.read_timeout = 5
  response = http.get(uri)
  
  if response.code == '200'
    puts "✅ Manager-service is running"
  else
    puts "❌ Manager-service health check failed: #{response.code}"
    exit 1
  end
rescue => e
  puts "❌ Manager-service is not accessible: #{e.message}"
  puts "Please start manager-service first: cd manager-service && rails server -p 3001"
  exit 1
end

# Create a room via API (simulating what a client would do)
puts "\n📝 Creating room via API..."

create_room_data = {
  name: "Complete Flow Test Room",
  game_type: "prizewheel",
  max_players: 2,
  bet_amount: 20.0,
  currency: "USD",
  is_private: false
}

begin
  uri = URI("http://localhost:3001/admin/rooms")
  http = Net::HTTP.new(uri.host, uri.port)
  request = Net::HTTP::Post.new(uri)
  request['Content-Type'] = 'application/json'
  request.body = create_room_data.to_json
  
  response = http.request(request)
  
  if response.code == '201'
    room_data = JSON.parse(response.body)
    room_id = room_data['data']['room']['id']
    puts "✅ Room created: #{room_id}"
  else
    puts "❌ Failed to create room: #{response.code} - #{response.body}"
    exit 1
  end
rescue => e
  puts "❌ Failed to create room: #{e.message}"
  exit 1
end

# Test 2: Check room details endpoint
puts "\n2️⃣ CHECKING ROOM DETAILS ENDPOINT"

begin
  uri = URI("http://localhost:3001/rooms/#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  response = http.get(uri)
  
  if response.code == '200'
    room_details = JSON.parse(response.body)
    room = room_details['data']['room']
    
    puts "✅ Room details retrieved"
    puts "   - ID: #{room['id']}"
    puts "   - Name: #{room['name']}"
    puts "   - Current Players: #{room['current_players']}"
    puts "   - Max Players: #{room['max_players']}"
    puts "   - Players Array: #{room['players'] ? room['players'].length : 'nil'} items"
    
    if room['players'] && room['players'].any?
      puts "   - Players:"
      room['players'].each_with_index do |player, index|
        puts "     #{index + 1}. #{player['username']} (#{player['user_id']})"
      end
    else
      puts "   - No players in room yet"
    end
  else
    puts "❌ Failed to get room details: #{response.code} - #{response.body}"
  end
rescue => e
  puts "❌ Failed to get room details: #{e.message}"
end

# Test 3: Check socket-gateway room fetching
puts "\n3️⃣ CHECKING SOCKET-GATEWAY ROOM FETCHING"

begin
  uri = URI("http://localhost:3002/health")
  http = Net::HTTP.new(uri.host, uri.port)
  http.read_timeout = 5
  response = http.get(uri)
  
  if response.code == '200'
    puts "✅ Socket-gateway is running"
  else
    puts "❌ Socket-gateway health check failed: #{response.code}"
  end
rescue => e
  puts "❌ Socket-gateway is not accessible: #{e.message}"
  puts "Please start socket-gateway first: cd socket-gateway && npm start"
end

# Test 4: Simulate what happens when a player joins
puts "\n4️⃣ SIMULATING PLAYER JOIN FLOW"

# This would normally be done via socket.io, but we can test the HTTP endpoints
puts "   (This would normally involve Socket.IO connections)"
puts "   The flow would be:"
puts "   1. Player connects to socket-gateway"
puts "   2. Player calls 'join_room' event"
puts "   3. Socket-gateway calls manager-service join endpoint"
puts "   4. Manager-service adds player and returns updated room data"
puts "   5. Socket-gateway subscribes player to room"
puts "   6. Socket-gateway sends room_info_updated event with player data"

# Test 5: Check room list endpoint (what socket-gateway uses)
puts "\n5️⃣ CHECKING ROOM LIST ENDPOINT (what socket-gateway uses)"

begin
  uri = URI("http://localhost:3001/rooms?id=#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  response = http.get(uri)
  
  if response.code == '200'
    room_list = JSON.parse(response.body)
    rooms = room_list['data']['rooms']
    
    if rooms && rooms.any?
      room = rooms.first
      puts "✅ Room found in list endpoint"
      puts "   - ID: #{room['id']}"
      puts "   - Name: #{room['name']}"
      puts "   - Current Players: #{room['current_players']}"
      puts "   - Players Array: #{room['players'] ? room['players'].length : 'nil'} items"
      
      if room['players'] && room['players'].any?
        puts "   - Players in list endpoint:"
        room['players'].each_with_index do |player, index|
          puts "     #{index + 1}. #{player['username']} (#{player['user_id']})"
        end
      else
        puts "   - No players in list endpoint"
      end
    else
      puts "❌ Room not found in list endpoint"
    end
  else
    puts "❌ Failed to get room list: #{response.code} - #{response.body}"
  end
rescue => e
  puts "❌ Failed to get room list: #{e.message}"
end

# Test 6: Summary and recommendations
puts "\n6️⃣ SUMMARY AND NEXT STEPS"

puts "\n🔍 DIAGNOSIS:"
puts "The issue 'room is full but empty players' occurs when:"
puts "1. Room shows current_players > 0"
puts "2. But players array is empty []"
puts "3. This happens due to data inconsistency between current_players count and active sessions"

puts "\n✅ FIXES IMPLEMENTED:"
puts "1. Added automatic data consistency checks in manager-service endpoints"
puts "2. Fixed socket-gateway to preserve players data from manager-service"
puts "3. Enhanced logging to track player data flow"
puts "4. Added background job to monitor and fix inconsistencies"

puts "\n🎯 TO TEST THE FIX:"
puts "1. Start both services: manager-service (port 3001) and socket-gateway (port 3002)"
puts "2. Create a room and add players via the dashboard or API"
puts "3. Subscribe to the room via socket-gateway"
puts "4. Check that room_info_updated events contain the players array"

puts "\n📋 EXPECTED BEHAVIOR AFTER FIX:"
puts "- room_info_updated events should have players: [{userId, username, position, isReady, betAmount}]"
puts "- currentPlayers count should match players.length"
puts "- No more empty players arrays when room has players"

# Cleanup
puts "\n🧹 CLEANING UP TEST ROOM"
begin
  uri = URI("http://localhost:3001/admin/rooms/#{room_id}")
  http = Net::HTTP.new(uri.host, uri.port)
  request = Net::HTTP::Delete.new(uri)
  response = http.request(request)
  
  if response.code == '200'
    puts "✅ Test room cleaned up"
  else
    puts "⚠️  Could not clean up test room (this is okay)"
  end
rescue => e
  puts "⚠️  Could not clean up test room: #{e.message} (this is okay)"
end

puts "\n=== Test completed ==="
