# Socket Gateway Room Operations - Flow Diagrams

## 1. Room Join Flow Diagram

```mermaid
graph TD
    A[Client sends join_room event] --> B{Authentication Valid?}
    B -->|No| C[Return AUTHENTICATION_REQUIRED]
    B -->|Yes| D{Input Validation}
    D -->|Invalid| E[Return ROOM_ID_REQUIRED]
    D -->|Valid| F[Execute Game Service Join]
    
    F --> G{Game Service Response}
    G -->|Error| H[Return Game Service Error]
    G -->|Success| I[Update Local State Management]
    
    I --> J[Add user to room tracking]
    J --> K[Join Socket.io room]
    K --> L[Update Redis state]
    L --> M[Send success response to client]
    M --> N[Broadcast to room participants]
    
    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style N fill:#c8e6c9
    style C fill:#ffcdd2
    style E fill:#ffcdd2
    style H fill:#ffcdd2
```

## 2. Room Leave Flow Diagram

```mermaid
graph TD
    A[Client sends leave_room event] --> B{Input Validation}
    B -->|Invalid| C[Return ROOM_ID_REQUIRED]
    B -->|Valid| D[Execute Game Service Leave]
    
    D --> E{Game Service Response}
    E -->|Error| F[Return Game Service Error]
    E -->|Success| G[Update Local State Management]
    
    G --> H[Remove user from room tracking]
    H --> I[Leave Socket.io room]
    I --> J[Update Redis state]
    J --> K[Auto-subscribe to lobby]
    K --> L[Send success response to client]
    L --> M[Broadcast to remaining participants]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style M fill:#c8e6c9
    style C fill:#ffcdd2
    style F fill:#ffcdd2
```

## 3. Service Integration Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant SG as Socket Gateway
    participant MS as Manager Service
    participant GS as Game Service
    participant R as Redis
    participant DB as Database

    Note over C,DB: Room Join Operation
    C->>SG: join_room(roomId, password, betAmount)
    SG->>SG: Validate authentication & input
    
    SG->>GS: gRPC joinRoom(roomId, userId, betAmount)
    GS->>DB: Validate room & user balance
    GS->>DB: Deduct balance & update room
    GS-->>SG: Join success + room data
    
    SG->>R: Update room tracking
    SG->>SG: Join Socket.io room
    SG-->>C: Success response
    
    SG->>C: Broadcast room_info_updated to room
    
    Note over C,DB: Room Leave Operation
    C->>SG: leave_room(roomId)
    SG->>GS: gRPC leaveRoom(roomId, userId)
    GS->>DB: Refund balance & update room
    GS-->>SG: Leave success
    
    SG->>R: Remove from room tracking
    SG->>SG: Leave Socket.io room
    SG->>SG: Auto-subscribe to lobby
    SG-->>C: Success response
    
    SG->>C: Broadcast room_info_updated to remaining players
```

## 4. Error Handling Flow

```mermaid
graph TD
    A[Room Operation Request] --> B{Authentication Check}
    B -->|Failed| C[AUTHENTICATION_REQUIRED]
    B -->|Success| D{Input Validation}
    
    D -->|Missing roomId| E[ROOM_ID_REQUIRED]
    D -->|Invalid format| F[INVALID_ROOM_ID]
    D -->|Valid| G{Game Service Call}
    
    G -->|Room Full| H[ROOM_FULL]
    G -->|Insufficient Balance| I[INSUFFICIENT_BALANCE]
    G -->|Invalid Password| J[INVALID_PASSWORD]
    G -->|Room Not Found| K[ROOM_NOT_FOUND]
    G -->|Service Down| L[SERVICE_UNAVAILABLE]
    G -->|Timeout| M[TIMEOUT_ERROR]
    G -->|Success| N[Continue Operation]
    
    N --> O{Local State Update}
    O -->|Failed| P[INTERNAL_ERROR]
    O -->|Success| Q[Operation Complete]
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style C fill:#ffcdd2
    style E fill:#ffcdd2
    style F fill:#ffcdd2
    style H fill:#ffcdd2
    style I fill:#ffcdd2
    style J fill:#ffcdd2
    style K fill:#ffcdd2
    style L fill:#ffcdd2
    style M fill:#ffcdd2
    style P fill:#ffcdd2
```

## 5. State Management Flow

```mermaid
stateDiagram-v2
    [*] --> Disconnected
    Disconnected --> Authenticated: Socket connects + JWT validation
    Authenticated --> Lobby: Auto-subscribe to lobby
    
    Lobby --> JoiningRoom: join_room event
    JoiningRoom --> InRoom: Join successful
    JoiningRoom --> Lobby: Join failed
    
    InRoom --> LeavingRoom: leave_room event
    InRoom --> LeavingRoom: Socket disconnect
    LeavingRoom --> Lobby: Leave successful
    
    Lobby --> Disconnected: Socket disconnect
    InRoom --> Disconnected: Socket disconnect (with cleanup)
    
    note right of Authenticated
        JWT token validated
        User session created
    end note
    
    note right of Lobby
        Subscribed to lobby updates
        Receives room_list_updated events
    end note
    
    note right of InRoom
        Subscribed to room updates
        Receives room_info_updated events
        Can send game-specific events
    end note
```

## 6. Data Flow Architecture

```mermaid
graph LR
    subgraph "Client Layer"
        C[Client Application]
    end
    
    subgraph "Socket Gateway (Port 3001)"
        SG[Socket.io Server]
        RH[Room Handlers]
        SM[State Manager]
        SC[Service Communicators]
    end
    
    subgraph "Manager Service (Port 3002)"
        MS[Room Management]
        MSR[Room Repository]
    end
    
    subgraph "Game Service (Port 8080)"
        GS[Game Logic]
        GSR[Game Repository]
    end
    
    subgraph "Data Layer"
        R[(Redis)]
        DB[(PostgreSQL)]
    end
    
    C <-->|Socket.io Events| SG
    SG --> RH
    RH --> SM
    RH --> SC
    
    SC -->|HTTP REST| MS
    SC -->|gRPC| GS
    
    MS --> MSR
    GS --> GSR
    
    SM <--> R
    MSR <--> DB
    GSR <--> DB
    
    style C fill:#e3f2fd
    style SG fill:#f3e5f5
    style MS fill:#e8f5e8
    style GS fill:#fff3e0
    style R fill:#ffebee
    style DB fill:#ffebee
```

## 7. Broadcasting Mechanism

```mermaid
sequenceDiagram
    participant P1 as Player 1
    participant P2 as Player 2
    participant P3 as Player 3
    participant SG as Socket Gateway
    participant R as Room (Socket.io)

    Note over P1,R: Player 1 joins room
    P1->>SG: join_room event
    SG->>R: Add P1 to room_68412c9a
    SG-->>P1: Join success response
    
    SG->>R: Broadcast room_info_updated
    R->>P2: Player joined notification
    R->>P3: Player joined notification
    
    Note over P1,R: Player 2 leaves room
    P2->>SG: leave_room event
    SG->>R: Remove P2 from room_68412c9a
    SG-->>P2: Leave success response
    
    SG->>R: Broadcast room_info_updated
    R->>P1: Player left notification
    R->>P3: Player left notification
    
    Note over P1,R: Efficient broadcasting to room participants only
```

## 8. Performance Optimization Flow

```mermaid
graph TD
    A[Room Operation Request] --> B[Connection Pool Check]
    B --> C{Cache Hit?}
    C -->|Yes| D[Use Cached Data]
    C -->|No| E[Fetch from Service]
    
    E --> F[Update Cache]
    F --> G[Process Request]
    D --> G
    
    G --> H[Update Local State]
    H --> I{Batch Updates?}
    I -->|Yes| J[Add to Batch Queue]
    I -->|No| K[Immediate Broadcast]
    
    J --> L[Process Batch]
    L --> M[Batch Broadcast]
    K --> N[Operation Complete]
    M --> N
    
    N --> O[Cleanup & Metrics]
    
    style A fill:#e1f5fe
    style D fill:#c8e6c9
    style N fill:#c8e6c9
    style O fill:#c8e6c9
```

This comprehensive flow documentation shows how the Socket Gateway handles room operations with proper integration between Manager Service (room data) and Game Service (game logic), maintaining efficient real-time communication through Socket.io.
