package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	"github.com/xzgame/room-service/internal/clients"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/repositories"
	"github.com/xzgame/room-service/internal/services"
	"github.com/xzgame/room-service/pkg/redis"
	pb "github.com/xzgame/room-service/proto"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Invalid configuration: %v", err)
	}

	logger.WithFields(logrus.Fields{
		"port":        cfg.Port,
		"environment": cfg.Environment,
		"log_level":   cfg.LogLevel,
	}).Info("Starting Room Service")

	// Set log level
	if level, err := logrus.ParseLevel(cfg.LogLevel); err == nil {
		logger.SetLevel(level)
	}

	// Initialize Redis client
	redisClient, err := redis.NewRedisClient(cfg.RedisURL, cfg.RedisPassword, cfg.RedisDB)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to Redis")
	}
	defer redisClient.Close()

	// Test Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := redisClient.Ping(ctx); err != nil {
		logger.WithError(err).Fatal("Failed to ping Redis")
	}
	logger.Info("Successfully connected to Redis")

	// Initialize repository
	repoConfig := repositories.RepositoryConfig{
		DatabaseURL:  cfg.MongoURL,
		DatabaseName: cfg.DatabaseName,
		CacheEnabled: cfg.CacheEnabled,
		CacheURL:     cfg.RedisURL,
	}

	roomRepo, err := repositories.NewRoomRepository(repoConfig)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize room repository")
	}

	// Initialize service clients for orchestrator
	gameServiceClient := clients.NewHTTPGameServiceClient("http://localhost:8080", logger)
	managerServiceClient := clients.NewHTTPManagerServiceClient("http://localhost:3002", logger)
	notificationClient := clients.NewHTTPNotificationClient(redisClient, logger)

	// Initialize room service with orchestrator
	roomService, orchestrator := services.NewRoomServiceWithOrchestrator(
		roomRepo,
		redisClient,
		cfg,
		logger,
		gameServiceClient,
		managerServiceClient,
		notificationClient,
	)

	// Initialize Redis orchestrator handler
	redisHandler := services.NewRedisOrchestratorHandler(
		orchestrator,
		redisClient,
		cfg,
		logger,
	)

	// Initialize Redis handler (sets up subscriptions)
	initCtx := context.Background()
	if err := redisHandler.Initialize(initCtx); err != nil {
		logger.WithError(err).Fatal("Failed to initialize Redis orchestrator handler")
	}

	logger.Info("Redis orchestrator handler initialized successfully")

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Create gRPC service wrapper
	grpcService := services.NewGRPCServer(roomService, logger)

	// Register room service
	pb.RegisterRoomServiceServer(grpcServer, grpcService)

	// Register health check service
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(grpcServer, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// Enable reflection for development
	if cfg.IsDevelopment() {
		reflection.Register(grpcServer)
	}

	// Start gRPC server
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		logger.WithError(err).Fatal("Failed to create listener")
	}

	// Start server in goroutine
	go func() {
		logger.WithField("address", listener.Addr().String()).Info("Room Service gRPC server starting")
		if err := grpcServer.Serve(listener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC server")
		}
	}()

	// Start background cleanup tasks
	go startBackgroundTasks(ctx, roomRepo, cfg, logger)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("Shutting down Room Service...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Shutdown Redis handler first
	if err := redisHandler.Shutdown(shutdownCtx); err != nil {
		logger.WithError(err).Error("Failed to shutdown Redis orchestrator handler")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Room Service shutdown complete")
}

// startBackgroundTasks starts background maintenance tasks
func startBackgroundTasks(ctx context.Context, roomRepo repositories.RoomRepository, cfg *config.Config, logger *logrus.Logger) {
	ticker := time.NewTicker(30 * time.Minute) // Run cleanup every 30 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// Cleanup inactive rooms
			inactiveThreshold := time.Now().Add(-cfg.RoomInactiveTimeout).Unix()
			if count, err := roomRepo.CleanupInactiveRooms(ctx, inactiveThreshold); err != nil {
				logger.WithError(err).Error("Failed to cleanup inactive rooms")
			} else if count > 0 {
				logger.WithField("count", count).Info("Cleaned up inactive rooms")
			}

			// Archive old rooms
			archiveThreshold := time.Now().Add(-cfg.DefaultRoomTTL).Unix()
			if count, err := roomRepo.ArchiveOldRooms(ctx, archiveThreshold); err != nil {
				logger.WithError(err).Error("Failed to archive old rooms")
			} else if count > 0 {
				logger.WithField("count", count).Info("Archived old rooms")
			}
		}
	}
}
