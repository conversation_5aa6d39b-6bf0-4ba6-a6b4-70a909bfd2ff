package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisClientInterface defines the interface for Redis operations
type RedisClientInterface interface {
	Ping(ctx context.Context) error
	Close() error
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string, dest interface{}) error
	Delete(ctx context.Context, key string) error
	Subscribe(ctx context.Context, channel string, handler func(context.Context, string, []byte) error) error
	Publish(ctx context.Context, channel string, message []byte) error
	Unsubscribe(ctx context.Context, channel string) error
}

type RedisClient struct {
	client      *redis.Client
	subscribers map[string]*redis.PubSub
}

func NewRedisClient(url, password string, db int) (*RedisClient, error) {
	opts, err := redis.ParseURL(url)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	if password != "" {
		opts.Password = password
	}
	opts.DB = db

	client := redis.NewClient(opts)

	return &RedisClient{
		client:      client,
		subscribers: make(map[string]*redis.PubSub),
	}, nil
}

func (r *RedisClient) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Set stores a value in Redis with optional expiration
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	return r.client.Set(ctx, key, data, expiration).Err()
}

// Get retrieves a value from Redis and unmarshals it into the provided destination
func (r *RedisClient) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(data), dest)
}

// Delete removes a key from Redis
func (r *RedisClient) Delete(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

// Del removes a key from Redis (alias for Delete)
func (r *RedisClient) Del(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

// Subscribe subscribes to a Redis channel and calls the handler for each message
func (r *RedisClient) Subscribe(ctx context.Context, channel string, handler func(context.Context, string, []byte) error) error {
	pubsub := r.client.Subscribe(ctx, channel)
	r.subscribers[channel] = pubsub

	// Start listening for messages in a goroutine
	go func() {
		defer pubsub.Close()

		ch := pubsub.Channel()
		for msg := range ch {
			if err := handler(ctx, msg.Channel, []byte(msg.Payload)); err != nil {
				// Log error but continue processing
				fmt.Printf("Error handling message on channel %s: %v\n", msg.Channel, err)
			}
		}
	}()

	return nil
}

// Publish publishes a message to a Redis channel
func (r *RedisClient) Publish(ctx context.Context, channel string, message []byte) error {
	return r.client.Publish(ctx, channel, message).Err()
}

// Unsubscribe unsubscribes from a Redis channel
func (r *RedisClient) Unsubscribe(ctx context.Context, channel string) error {
	if pubsub, exists := r.subscribers[channel]; exists {
		err := pubsub.Unsubscribe(ctx, channel)
		delete(r.subscribers, channel)
		return err
	}
	return nil
}
