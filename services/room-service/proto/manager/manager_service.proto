syntax = "proto3";

package manager;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

option go_package = "./manager";

// Manager Service Definition - Simplified for Room Service needs
service ManagerService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserBalance(GetUserBalanceRequest) returns (GetUserBalanceResponse);
  rpc UpdateBalance(UpdateBalanceRequest) returns (UpdateBalanceResponse);
  rpc ValidateUser(ValidateUserRequest) returns (ValidateUserResponse);
  rpc GetUserSession(GetUserSessionRequest) returns (GetUserSessionResponse);
}

// Common Messages
message ErrorResponse {
  string code = 1;
  string message = 2;
  int32 status_code = 3;
  google.protobuf.Any details = 4;
  string correlation_id = 5;
  bool retryable = 6;
  int64 timestamp = 7;
}

// User Balance
message UserBalance {
  string user_id = 1;
  double current_balance = 2;
  double previous_balance = 3;
  double locked_balance = 4;
  string currency = 5;
  int64 updated_at = 6;
}

// User Status
message UserStatus {
  bool banned = 1;
  bool suspended = 2;
  bool active = 3;
}

// User Session
message UserSession {
  bool active = 1;
  string room_id = 2;
  string session_id = 3;
  int64 started_at = 4;
}

// User
message User {
  string id = 1;
  string username = 2;
  string email = 3;
  UserBalance balance = 4;
  UserStatus status = 5;
  bool is_active = 6;
  int64 created_at = 7;
  int64 updated_at = 8;
  int64 last_login_at = 9;
}

// Transaction
message Transaction {
  string id = 1;
  string user_id = 2;
  double amount = 3;
  string type = 4;
  string status = 5;
  string reason = 6;
  string game_id = 7;
  string room_id = 8;
  map<string, string> metadata = 9;
  int64 created_at = 10;
  int64 updated_at = 11;
}

// Get User
message GetUserRequest {
  string user_id = 1;
}

message GetUserResponse {
  bool success = 1;
  User user = 2;
  ErrorResponse error = 3;
}

// Get User Balance
message GetUserBalanceRequest {
  string user_id = 1;
}

message GetUserBalanceResponse {
  bool success = 1;
  UserBalance balance = 2;
  ErrorResponse error = 3;
}

// Update Balance
message UpdateBalanceRequest {
  string user_id = 1;
  double amount = 2;
  string reason = 3;
  string game_id = 4;
  string transaction_id = 5;
  map<string, string> metadata = 6;
}

message UpdateBalanceResponse {
  bool success = 1;
  UserBalance balance = 2;
  Transaction transaction = 3;
  ErrorResponse error = 4;
}

// Validate User
message ValidateUserRequest {
  string user_id = 1;
  string username = 2;
}

message ValidateUserResponse {
  bool success = 1;
  bool is_valid = 2;
  User user = 3;
  ErrorResponse error = 4;
}

// Get User Session
message GetUserSessionRequest {
  string user_id = 1;
  string room_id = 2;
}

message GetUserSessionResponse {
  bool success = 1;
  UserSession session = 2;
  ErrorResponse error = 3;
}
