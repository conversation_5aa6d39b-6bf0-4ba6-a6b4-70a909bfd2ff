// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/manager/manager_service.proto

package manager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Common Messages
type ErrorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	StatusCode    int32                  `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	Details       *anypb.Any             `protobuf:"bytes,4,opt,name=details,proto3" json:"details,omitempty"`
	CorrelationId string                 `protobuf:"bytes,5,opt,name=correlation_id,json=correlationId,proto3" json:"correlation_id,omitempty"`
	Retryable     bool                   `protobuf:"varint,6,opt,name=retryable,proto3" json:"retryable,omitempty"`
	Timestamp     int64                  `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ErrorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorResponse) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *ErrorResponse) GetDetails() *anypb.Any {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *ErrorResponse) GetCorrelationId() string {
	if x != nil {
		return x.CorrelationId
	}
	return ""
}

func (x *ErrorResponse) GetRetryable() bool {
	if x != nil {
		return x.Retryable
	}
	return false
}

func (x *ErrorResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// User Balance
type UserBalance struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CurrentBalance  float64                `protobuf:"fixed64,2,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	PreviousBalance float64                `protobuf:"fixed64,3,opt,name=previous_balance,json=previousBalance,proto3" json:"previous_balance,omitempty"`
	LockedBalance   float64                `protobuf:"fixed64,4,opt,name=locked_balance,json=lockedBalance,proto3" json:"locked_balance,omitempty"`
	Currency        string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	UpdatedAt       int64                  `protobuf:"varint,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserBalance) Reset() {
	*x = UserBalance{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBalance) ProtoMessage() {}

func (x *UserBalance) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBalance.ProtoReflect.Descriptor instead.
func (*UserBalance) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{1}
}

func (x *UserBalance) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserBalance) GetCurrentBalance() float64 {
	if x != nil {
		return x.CurrentBalance
	}
	return 0
}

func (x *UserBalance) GetPreviousBalance() float64 {
	if x != nil {
		return x.PreviousBalance
	}
	return 0
}

func (x *UserBalance) GetLockedBalance() float64 {
	if x != nil {
		return x.LockedBalance
	}
	return 0
}

func (x *UserBalance) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *UserBalance) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// User Status
type UserStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Banned        bool                   `protobuf:"varint,1,opt,name=banned,proto3" json:"banned,omitempty"`
	Suspended     bool                   `protobuf:"varint,2,opt,name=suspended,proto3" json:"suspended,omitempty"`
	Active        bool                   `protobuf:"varint,3,opt,name=active,proto3" json:"active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStatus) Reset() {
	*x = UserStatus{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatus) ProtoMessage() {}

func (x *UserStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatus.ProtoReflect.Descriptor instead.
func (*UserStatus) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{2}
}

func (x *UserStatus) GetBanned() bool {
	if x != nil {
		return x.Banned
	}
	return false
}

func (x *UserStatus) GetSuspended() bool {
	if x != nil {
		return x.Suspended
	}
	return false
}

func (x *UserStatus) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

// User Session
type UserSession struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Active        bool                   `protobuf:"varint,1,opt,name=active,proto3" json:"active,omitempty"`
	RoomId        string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	SessionId     string                 `protobuf:"bytes,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	StartedAt     int64                  `protobuf:"varint,4,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSession) Reset() {
	*x = UserSession{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSession) ProtoMessage() {}

func (x *UserSession) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSession.ProtoReflect.Descriptor instead.
func (*UserSession) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{3}
}

func (x *UserSession) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *UserSession) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *UserSession) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UserSession) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

// User
type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Balance       *UserBalance           `protobuf:"bytes,4,opt,name=balance,proto3" json:"balance,omitempty"`
	Status        *UserStatus            `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastLoginAt   int64                  `protobuf:"varint,9,opt,name=last_login_at,json=lastLoginAt,proto3" json:"last_login_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{4}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetBalance() *UserBalance {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *User) GetStatus() *UserStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *User) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *User) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *User) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *User) GetLastLoginAt() int64 {
	if x != nil {
		return x.LastLoginAt
	}
	return 0
}

// Transaction
type Transaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Reason        string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	GameId        string                 `protobuf:"bytes,7,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	RoomId        string                 `protobuf:"bytes,8,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,9,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CreatedAt     int64                  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{5}
}

func (x *Transaction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Transaction) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Transaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Transaction) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Transaction) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Transaction) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Transaction) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *Transaction) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *Transaction) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Transaction) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Transaction) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// Get User
type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	User          *User                  `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Error         *ErrorResponse         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetUserResponse) GetError() *ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

// Get User Balance
type GetUserBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserBalanceRequest) Reset() {
	*x = GetUserBalanceRequest{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBalanceRequest) ProtoMessage() {}

func (x *GetUserBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetUserBalanceRequest) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserBalanceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserBalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Balance       *UserBalance           `protobuf:"bytes,2,opt,name=balance,proto3" json:"balance,omitempty"`
	Error         *ErrorResponse         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserBalanceResponse) Reset() {
	*x = GetUserBalanceResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBalanceResponse) ProtoMessage() {}

func (x *GetUserBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetUserBalanceResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserBalanceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetUserBalanceResponse) GetBalance() *UserBalance {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *GetUserBalanceResponse) GetError() *ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

// Update Balance
type UpdateBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	GameId        string                 `protobuf:"bytes,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TransactionId string                 `protobuf:"bytes,5,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBalanceRequest) Reset() {
	*x = UpdateBalanceRequest{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBalanceRequest) ProtoMessage() {}

func (x *UpdateBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBalanceRequest.ProtoReflect.Descriptor instead.
func (*UpdateBalanceRequest) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateBalanceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateBalanceRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UpdateBalanceRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *UpdateBalanceRequest) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *UpdateBalanceRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *UpdateBalanceRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type UpdateBalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Balance       *UserBalance           `protobuf:"bytes,2,opt,name=balance,proto3" json:"balance,omitempty"`
	Transaction   *Transaction           `protobuf:"bytes,3,opt,name=transaction,proto3" json:"transaction,omitempty"`
	Error         *ErrorResponse         `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBalanceResponse) Reset() {
	*x = UpdateBalanceResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBalanceResponse) ProtoMessage() {}

func (x *UpdateBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBalanceResponse.ProtoReflect.Descriptor instead.
func (*UpdateBalanceResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateBalanceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateBalanceResponse) GetBalance() *UserBalance {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *UpdateBalanceResponse) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *UpdateBalanceResponse) GetError() *ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

// Validate User
type ValidateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateUserRequest) Reset() {
	*x = ValidateUserRequest{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUserRequest) ProtoMessage() {}

func (x *ValidateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUserRequest.ProtoReflect.Descriptor instead.
func (*ValidateUserRequest) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{12}
}

func (x *ValidateUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type ValidateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	IsValid       bool                   `protobuf:"varint,2,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	User          *User                  `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Error         *ErrorResponse         `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateUserResponse) Reset() {
	*x = ValidateUserResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUserResponse) ProtoMessage() {}

func (x *ValidateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUserResponse.ProtoReflect.Descriptor instead.
func (*ValidateUserResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{13}
}

func (x *ValidateUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ValidateUserResponse) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *ValidateUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ValidateUserResponse) GetError() *ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

// Get User Session
type GetUserSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RoomId        string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSessionRequest) Reset() {
	*x = GetUserSessionRequest{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionRequest) ProtoMessage() {}

func (x *GetUserSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionRequest.ProtoReflect.Descriptor instead.
func (*GetUserSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserSessionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserSessionRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetUserSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Session       *UserSession           `protobuf:"bytes,2,opt,name=session,proto3" json:"session,omitempty"`
	Error         *ErrorResponse         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSessionResponse) Reset() {
	*x = GetUserSessionResponse{}
	mi := &file_proto_manager_manager_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionResponse) ProtoMessage() {}

func (x *GetUserSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_manager_manager_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionResponse.ProtoReflect.Descriptor instead.
func (*GetUserSessionResponse) Descriptor() ([]byte, []int) {
	return file_proto_manager_manager_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserSessionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetUserSessionResponse) GetSession() *UserSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *GetUserSessionResponse) GetError() *ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proto_manager_manager_service_proto protoreflect.FileDescriptor

const file_proto_manager_manager_service_proto_rawDesc = "" +
	"\n" +
	"#proto/manager/manager_service.proto\x12\amanager\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x19google/protobuf/any.proto\"\xf1\x01\n" +
	"\rErrorResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1f\n" +
	"\vstatus_code\x18\x03 \x01(\x05R\n" +
	"statusCode\x12.\n" +
	"\adetails\x18\x04 \x01(\v2\x14.google.protobuf.AnyR\adetails\x12%\n" +
	"\x0ecorrelation_id\x18\x05 \x01(\tR\rcorrelationId\x12\x1c\n" +
	"\tretryable\x18\x06 \x01(\bR\tretryable\x12\x1c\n" +
	"\ttimestamp\x18\a \x01(\x03R\ttimestamp\"\xdc\x01\n" +
	"\vUserBalance\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12'\n" +
	"\x0fcurrent_balance\x18\x02 \x01(\x01R\x0ecurrentBalance\x12)\n" +
	"\x10previous_balance\x18\x03 \x01(\x01R\x0fpreviousBalance\x12%\n" +
	"\x0elocked_balance\x18\x04 \x01(\x01R\rlockedBalance\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\x03R\tupdatedAt\"Z\n" +
	"\n" +
	"UserStatus\x12\x16\n" +
	"\x06banned\x18\x01 \x01(\bR\x06banned\x12\x1c\n" +
	"\tsuspended\x18\x02 \x01(\bR\tsuspended\x12\x16\n" +
	"\x06active\x18\x03 \x01(\bR\x06active\"|\n" +
	"\vUserSession\x12\x16\n" +
	"\x06active\x18\x01 \x01(\bR\x06active\x12\x17\n" +
	"\aroom_id\x18\x02 \x01(\tR\x06roomId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x03 \x01(\tR\tsessionId\x12\x1d\n" +
	"\n" +
	"started_at\x18\x04 \x01(\x03R\tstartedAt\"\xa4\x02\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12.\n" +
	"\abalance\x18\x04 \x01(\v2\x14.manager.UserBalanceR\abalance\x12+\n" +
	"\x06status\x18\x05 \x01(\v2\x13.manager.UserStatusR\x06status\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\x03R\tupdatedAt\x12\"\n" +
	"\rlast_login_at\x18\t \x01(\x03R\vlastLoginAt\"\xff\x02\n" +
	"\vTransaction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x12\x17\n" +
	"\agame_id\x18\a \x01(\tR\x06gameId\x12\x17\n" +
	"\aroom_id\x18\b \x01(\tR\x06roomId\x12>\n" +
	"\bmetadata\x18\t \x03(\v2\".manager.Transaction.MetadataEntryR\bmetadata\x12\x1d\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\v \x01(\x03R\tupdatedAt\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\")\n" +
	"\x0eGetUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"|\n" +
	"\x0fGetUserResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12!\n" +
	"\x04user\x18\x02 \x01(\v2\r.manager.UserR\x04user\x12,\n" +
	"\x05error\x18\x03 \x01(\v2\x16.manager.ErrorResponseR\x05error\"0\n" +
	"\x15GetUserBalanceRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x90\x01\n" +
	"\x16GetUserBalanceResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12.\n" +
	"\abalance\x18\x02 \x01(\v2\x14.manager.UserBalanceR\abalance\x12,\n" +
	"\x05error\x18\x03 \x01(\v2\x16.manager.ErrorResponseR\x05error\"\xa5\x02\n" +
	"\x14UpdateBalanceRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x17\n" +
	"\agame_id\x18\x04 \x01(\tR\x06gameId\x12%\n" +
	"\x0etransaction_id\x18\x05 \x01(\tR\rtransactionId\x12G\n" +
	"\bmetadata\x18\x06 \x03(\v2+.manager.UpdateBalanceRequest.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc7\x01\n" +
	"\x15UpdateBalanceResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12.\n" +
	"\abalance\x18\x02 \x01(\v2\x14.manager.UserBalanceR\abalance\x126\n" +
	"\vtransaction\x18\x03 \x01(\v2\x14.manager.TransactionR\vtransaction\x12,\n" +
	"\x05error\x18\x04 \x01(\v2\x16.manager.ErrorResponseR\x05error\"J\n" +
	"\x13ValidateUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\"\x9c\x01\n" +
	"\x14ValidateUserResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x19\n" +
	"\bis_valid\x18\x02 \x01(\bR\aisValid\x12!\n" +
	"\x04user\x18\x03 \x01(\v2\r.manager.UserR\x04user\x12,\n" +
	"\x05error\x18\x04 \x01(\v2\x16.manager.ErrorResponseR\x05error\"I\n" +
	"\x15GetUserSessionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\aroom_id\x18\x02 \x01(\tR\x06roomId\"\x90\x01\n" +
	"\x16GetUserSessionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12.\n" +
	"\asession\x18\x02 \x01(\v2\x14.manager.UserSessionR\asession\x12,\n" +
	"\x05error\x18\x03 \x01(\v2\x16.manager.ErrorResponseR\x05error2\x91\x03\n" +
	"\x0eManagerService\x12<\n" +
	"\aGetUser\x12\x17.manager.GetUserRequest\x1a\x18.manager.GetUserResponse\x12Q\n" +
	"\x0eGetUserBalance\x12\x1e.manager.GetUserBalanceRequest\x1a\x1f.manager.GetUserBalanceResponse\x12N\n" +
	"\rUpdateBalance\x12\x1d.manager.UpdateBalanceRequest\x1a\x1e.manager.UpdateBalanceResponse\x12K\n" +
	"\fValidateUser\x12\x1c.manager.ValidateUserRequest\x1a\x1d.manager.ValidateUserResponse\x12Q\n" +
	"\x0eGetUserSession\x12\x1e.manager.GetUserSessionRequest\x1a\x1f.manager.GetUserSessionResponseB\vZ\t./managerb\x06proto3"

var (
	file_proto_manager_manager_service_proto_rawDescOnce sync.Once
	file_proto_manager_manager_service_proto_rawDescData []byte
)

func file_proto_manager_manager_service_proto_rawDescGZIP() []byte {
	file_proto_manager_manager_service_proto_rawDescOnce.Do(func() {
		file_proto_manager_manager_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_manager_manager_service_proto_rawDesc), len(file_proto_manager_manager_service_proto_rawDesc)))
	})
	return file_proto_manager_manager_service_proto_rawDescData
}

var file_proto_manager_manager_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_proto_manager_manager_service_proto_goTypes = []any{
	(*ErrorResponse)(nil),          // 0: manager.ErrorResponse
	(*UserBalance)(nil),            // 1: manager.UserBalance
	(*UserStatus)(nil),             // 2: manager.UserStatus
	(*UserSession)(nil),            // 3: manager.UserSession
	(*User)(nil),                   // 4: manager.User
	(*Transaction)(nil),            // 5: manager.Transaction
	(*GetUserRequest)(nil),         // 6: manager.GetUserRequest
	(*GetUserResponse)(nil),        // 7: manager.GetUserResponse
	(*GetUserBalanceRequest)(nil),  // 8: manager.GetUserBalanceRequest
	(*GetUserBalanceResponse)(nil), // 9: manager.GetUserBalanceResponse
	(*UpdateBalanceRequest)(nil),   // 10: manager.UpdateBalanceRequest
	(*UpdateBalanceResponse)(nil),  // 11: manager.UpdateBalanceResponse
	(*ValidateUserRequest)(nil),    // 12: manager.ValidateUserRequest
	(*ValidateUserResponse)(nil),   // 13: manager.ValidateUserResponse
	(*GetUserSessionRequest)(nil),  // 14: manager.GetUserSessionRequest
	(*GetUserSessionResponse)(nil), // 15: manager.GetUserSessionResponse
	nil,                            // 16: manager.Transaction.MetadataEntry
	nil,                            // 17: manager.UpdateBalanceRequest.MetadataEntry
	(*anypb.Any)(nil),              // 18: google.protobuf.Any
}
var file_proto_manager_manager_service_proto_depIdxs = []int32{
	18, // 0: manager.ErrorResponse.details:type_name -> google.protobuf.Any
	1,  // 1: manager.User.balance:type_name -> manager.UserBalance
	2,  // 2: manager.User.status:type_name -> manager.UserStatus
	16, // 3: manager.Transaction.metadata:type_name -> manager.Transaction.MetadataEntry
	4,  // 4: manager.GetUserResponse.user:type_name -> manager.User
	0,  // 5: manager.GetUserResponse.error:type_name -> manager.ErrorResponse
	1,  // 6: manager.GetUserBalanceResponse.balance:type_name -> manager.UserBalance
	0,  // 7: manager.GetUserBalanceResponse.error:type_name -> manager.ErrorResponse
	17, // 8: manager.UpdateBalanceRequest.metadata:type_name -> manager.UpdateBalanceRequest.MetadataEntry
	1,  // 9: manager.UpdateBalanceResponse.balance:type_name -> manager.UserBalance
	5,  // 10: manager.UpdateBalanceResponse.transaction:type_name -> manager.Transaction
	0,  // 11: manager.UpdateBalanceResponse.error:type_name -> manager.ErrorResponse
	4,  // 12: manager.ValidateUserResponse.user:type_name -> manager.User
	0,  // 13: manager.ValidateUserResponse.error:type_name -> manager.ErrorResponse
	3,  // 14: manager.GetUserSessionResponse.session:type_name -> manager.UserSession
	0,  // 15: manager.GetUserSessionResponse.error:type_name -> manager.ErrorResponse
	6,  // 16: manager.ManagerService.GetUser:input_type -> manager.GetUserRequest
	8,  // 17: manager.ManagerService.GetUserBalance:input_type -> manager.GetUserBalanceRequest
	10, // 18: manager.ManagerService.UpdateBalance:input_type -> manager.UpdateBalanceRequest
	12, // 19: manager.ManagerService.ValidateUser:input_type -> manager.ValidateUserRequest
	14, // 20: manager.ManagerService.GetUserSession:input_type -> manager.GetUserSessionRequest
	7,  // 21: manager.ManagerService.GetUser:output_type -> manager.GetUserResponse
	9,  // 22: manager.ManagerService.GetUserBalance:output_type -> manager.GetUserBalanceResponse
	11, // 23: manager.ManagerService.UpdateBalance:output_type -> manager.UpdateBalanceResponse
	13, // 24: manager.ManagerService.ValidateUser:output_type -> manager.ValidateUserResponse
	15, // 25: manager.ManagerService.GetUserSession:output_type -> manager.GetUserSessionResponse
	21, // [21:26] is the sub-list for method output_type
	16, // [16:21] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proto_manager_manager_service_proto_init() }
func file_proto_manager_manager_service_proto_init() {
	if File_proto_manager_manager_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_manager_manager_service_proto_rawDesc), len(file_proto_manager_manager_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_manager_manager_service_proto_goTypes,
		DependencyIndexes: file_proto_manager_manager_service_proto_depIdxs,
		MessageInfos:      file_proto_manager_manager_service_proto_msgTypes,
	}.Build()
	File_proto_manager_manager_service_proto = out.File
	file_proto_manager_manager_service_proto_goTypes = nil
	file_proto_manager_manager_service_proto_depIdxs = nil
}
