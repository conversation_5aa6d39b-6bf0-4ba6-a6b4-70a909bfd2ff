syntax = "proto3";

package room;

option go_package = "github.com/xzgame/room-service/proto";

import "google/protobuf/timestamp.proto";

// RoomService provides room management functionality
service RoomService {
  // Room CRUD operations
  rpc CreateRoom(CreateRoomRequest) returns (CreateRoomResponse);
  rpc GetRoom(GetRoomRequest) returns (GetRoomResponse);
  rpc UpdateRoom(UpdateRoomRequest) returns (UpdateRoomResponse);
  rpc DeleteRoom(DeleteRoomRequest) returns (DeleteRoomResponse);
  rpc ListRooms(ListRoomsRequest) returns (ListRoomsResponse);
  
  // Player operations
  rpc JoinRoom(JoinRoomRequest) returns (JoinRoomResponse);
  rpc LeaveRoom(LeaveRoomRequest) returns (LeaveRoomResponse);
  rpc SetPlayerReady(SetPlayerReadyRequest) returns (SetPlayerReadyResponse);
  rpc GetRoomsByPlayer(GetRoomsByPlayerRequest) returns (GetRoomsByPlayerResponse);
  
  // Room status operations
  rpc StartGame(StartGameRequest) returns (StartGameResponse);
  rpc EndGame(EndGameRequest) returns (EndGameResponse);
  rpc GetAvailableRooms(GetAvailableRoomsRequest) returns (GetAvailableRoomsResponse);
  
  // Statistics and monitoring
  rpc GetRoomStats(GetRoomStatsRequest) returns (GetRoomStatsResponse);
  rpc GetRoomActivity(GetRoomActivityRequest) returns (GetRoomActivityResponse);

  // Room information for broadcasting
  rpc GetRoomInfo(GetRoomInfoRequest) returns (GetRoomInfoResponse);
}

// Enums
enum GameType {
  GAME_TYPE_UNSPECIFIED = 0;
  GAME_TYPE_PRIZE_WHEEL = 1;
  GAME_TYPE_AMIDAKUJI = 2;
}

enum RoomStatus {
  ROOM_STATUS_UNSPECIFIED = 0;
  ROOM_STATUS_WAITING = 1;
  ROOM_STATUS_ACTIVE = 2;
  ROOM_STATUS_FULL = 3;
  ROOM_STATUS_CLOSED = 4;
  ROOM_STATUS_ARCHIVED = 5;
}

enum PlayerStatus {
  PLAYER_STATUS_UNSPECIFIED = 0;
  PLAYER_STATUS_ACTIVE = 1;
  PLAYER_STATUS_DISCONNECTED = 2;
  PLAYER_STATUS_LEFT = 3;
}

// Core data structures
message Room {
  string id = 1;
  string name = 2;
  GameType game_type = 3;
  RoomStatus status = 4;
  int32 current_players = 5;
  int32 max_players = 6;
  int32 min_players = 7;
  int64 bet_amount = 8;
  repeated RoomPlayer players = 9;
  RoomConfiguration configuration = 10;
  string current_session = 11;
  google.protobuf.Timestamp last_activity = 12;
  string created_by = 13;
  google.protobuf.Timestamp created_at = 14;
  google.protobuf.Timestamp updated_at = 15;
}

message RoomPlayer {
  string user_id = 1;
  string username = 2;
  int32 position = 3;
  bool is_ready = 4;
  int64 bet_amount = 5;
  google.protobuf.Timestamp joined_at = 6;
  PlayerStatus status = 7;
}

message RoomConfiguration {
  GameType game_type = 1;
  BetLimitConfig bet_limits = 2;
  TimeoutConfig timeouts = 3;
  bool auto_start = 4;
  bool is_private = 5;
  string password = 6;
  map<string, string> game_specific = 7;
}

message BetLimitConfig {
  int64 min_bet = 1;
  int64 max_bet = 2;
}

message TimeoutConfig {
  int64 join_timeout_seconds = 1;
  int64 ready_timeout_seconds = 2;
  int64 game_timeout_seconds = 3;
}

// Request/Response messages

// CreateRoom
message CreateRoomRequest {
  string name = 1;
  GameType game_type = 2;
  int32 max_players = 3;
  int32 min_players = 4;
  BetLimitConfig bet_limits = 5;
  bool auto_start = 6;
  bool is_private = 7;
  string password = 8;
  map<string, string> game_specific = 9;
  string created_by = 10;
}

message CreateRoomResponse {
  Room room = 1;
  string error = 2;
}

// GetRoom
message GetRoomRequest {
  string room_id = 1;
  bool force_refresh = 2;
}

message GetRoomResponse {
  Room room = 1;
  string error = 2;
}

// UpdateRoom
message UpdateRoomRequest {
  string room_id = 1;
  string name = 2;
  int32 max_players = 3;
  BetLimitConfig bet_limits = 4;
  bool auto_start = 5;
  map<string, string> game_specific = 6;
}

message UpdateRoomResponse {
  Room room = 1;
  string error = 2;
}

// DeleteRoom
message DeleteRoomRequest {
  string room_id = 1;
  string admin_user_id = 2;
}

message DeleteRoomResponse {
  bool success = 1;
  string error = 2;
}

// ListRooms
message ListRoomsRequest {
  GameType game_type = 1;
  RoomStatus status = 2;
  int32 min_players = 3;
  int32 max_players = 4;
  bool has_space = 5;
  int32 page = 6;
  int32 limit = 7;
}

message ListRoomsResponse {
  repeated Room rooms = 1;
  Pagination pagination = 2;
  string error = 3;
}

message Pagination {
  int32 current_page = 1;
  int32 per_page = 2;
  int64 total_count = 3;
  int32 total_pages = 4;
}

// JoinRoom
message JoinRoomRequest {
  string room_id = 1;
  string user_id = 2;
  string username = 3;
  string password = 4;
  int64 bet_amount = 5;
}

message JoinRoomResponse {
  Room room = 1;
  string error = 2;
}

// LeaveRoom
message LeaveRoomRequest {
  string room_id = 1;
  string user_id = 2;
}

message LeaveRoomResponse {
  Room room = 1;
  string error = 2;
}

// SetPlayerReady
message SetPlayerReadyRequest {
  string room_id = 1;
  string user_id = 2;
  bool ready = 3;
}

message SetPlayerReadyResponse {
  Room room = 1;
  string error = 2;
}

// GetRoomsByPlayer
message GetRoomsByPlayerRequest {
  string user_id = 1;
}

message GetRoomsByPlayerResponse {
  repeated Room rooms = 1;
  string error = 2;
}

// StartGame
message StartGameRequest {
  string room_id = 1;
  string initiated_by = 2;
}

message StartGameResponse {
  Room room = 1;
  string session_id = 2;
  string error = 3;
}

// EndGame
message EndGameRequest {
  string room_id = 1;
  string session_id = 2;
  map<string, string> results = 3;
}

message EndGameResponse {
  Room room = 1;
  string error = 2;
}

// GetAvailableRooms
message GetAvailableRoomsRequest {
  GameType game_type = 1;
}

message GetAvailableRoomsResponse {
  repeated Room rooms = 1;
  string error = 2;
}

// GetRoomStats
message GetRoomStatsRequest {
  string room_id = 1;
}

message GetRoomStatsResponse {
  RoomStats stats = 1;
  string error = 2;
}

message RoomStats {
  string room_id = 1;
  int32 total_games = 2;
  int32 total_players = 3;
  int64 average_game_time_seconds = 4;
  int64 total_bet_volume = 5;
  google.protobuf.Timestamp last_game_at = 6;
}

// GetRoomActivity
message GetRoomActivityRequest {
  string room_id = 1;
}

message GetRoomActivityResponse {
  RoomActivity activity = 1;
  string error = 2;
}

message RoomActivity {
  string room_id = 1;
  repeated RoomEvent events = 2;
  repeated RoomPlayer players = 3;
  google.protobuf.Timestamp updated_at = 4;
}

message RoomEvent {
  string type = 1;
  string room_id = 2;
  string user_id = 3;
  map<string, string> data = 4;
  google.protobuf.Timestamp timestamp = 5;
}

// GetRoomInfo
message GetRoomInfoRequest {
  string room_id = 1;
}

message GetRoomInfoResponse {
  RoomInfo room_info = 1;
  string error = 2;
}

message RoomInfo {
  string room_id = 1;
  string name = 2;
  GameType game_type = 3;
  RoomStatus status = 4;
  int32 current_players = 5;
  int32 max_players = 6;
  int32 min_players = 7;
  int64 bet_amount = 8;
  repeated RoomPlayer players = 9;
  bool is_private = 10;
  bool auto_start = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp last_activity = 13;
  bool can_join = 14;
  bool can_start = 15;
}
