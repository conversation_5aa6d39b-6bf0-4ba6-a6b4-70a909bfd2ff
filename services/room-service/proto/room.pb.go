// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/room.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type GameType int32

const (
	GameType_GAME_TYPE_UNSPECIFIED GameType = 0
	GameType_GAME_TYPE_PRIZE_WHEEL GameType = 1
	GameType_GAME_TYPE_AMIDAKUJI   GameType = 2
)

// Enum value maps for GameType.
var (
	GameType_name = map[int32]string{
		0: "GAME_TYPE_UNSPECIFIED",
		1: "GAME_TYPE_PRIZE_WHEEL",
		2: "GAME_TYPE_AMIDAKUJI",
	}
	GameType_value = map[string]int32{
		"GAME_TYPE_UNSPECIFIED": 0,
		"GAME_TYPE_PRIZE_WHEEL": 1,
		"GAME_TYPE_AMIDAKUJI":   2,
	}
)

func (x GameType) Enum() *GameType {
	p := new(GameType)
	*p = x
	return p
}

func (x GameType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_room_proto_enumTypes[0].Descriptor()
}

func (GameType) Type() protoreflect.EnumType {
	return &file_proto_room_proto_enumTypes[0]
}

func (x GameType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameType.Descriptor instead.
func (GameType) EnumDescriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{0}
}

type RoomStatus int32

const (
	RoomStatus_ROOM_STATUS_UNSPECIFIED RoomStatus = 0
	RoomStatus_ROOM_STATUS_WAITING     RoomStatus = 1
	RoomStatus_ROOM_STATUS_ACTIVE      RoomStatus = 2
	RoomStatus_ROOM_STATUS_FULL        RoomStatus = 3
	RoomStatus_ROOM_STATUS_CLOSED      RoomStatus = 4
	RoomStatus_ROOM_STATUS_ARCHIVED    RoomStatus = 5
)

// Enum value maps for RoomStatus.
var (
	RoomStatus_name = map[int32]string{
		0: "ROOM_STATUS_UNSPECIFIED",
		1: "ROOM_STATUS_WAITING",
		2: "ROOM_STATUS_ACTIVE",
		3: "ROOM_STATUS_FULL",
		4: "ROOM_STATUS_CLOSED",
		5: "ROOM_STATUS_ARCHIVED",
	}
	RoomStatus_value = map[string]int32{
		"ROOM_STATUS_UNSPECIFIED": 0,
		"ROOM_STATUS_WAITING":     1,
		"ROOM_STATUS_ACTIVE":      2,
		"ROOM_STATUS_FULL":        3,
		"ROOM_STATUS_CLOSED":      4,
		"ROOM_STATUS_ARCHIVED":    5,
	}
)

func (x RoomStatus) Enum() *RoomStatus {
	p := new(RoomStatus)
	*p = x
	return p
}

func (x RoomStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoomStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_room_proto_enumTypes[1].Descriptor()
}

func (RoomStatus) Type() protoreflect.EnumType {
	return &file_proto_room_proto_enumTypes[1]
}

func (x RoomStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoomStatus.Descriptor instead.
func (RoomStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{1}
}

type PlayerStatus int32

const (
	PlayerStatus_PLAYER_STATUS_UNSPECIFIED  PlayerStatus = 0
	PlayerStatus_PLAYER_STATUS_ACTIVE       PlayerStatus = 1
	PlayerStatus_PLAYER_STATUS_DISCONNECTED PlayerStatus = 2
	PlayerStatus_PLAYER_STATUS_LEFT         PlayerStatus = 3
)

// Enum value maps for PlayerStatus.
var (
	PlayerStatus_name = map[int32]string{
		0: "PLAYER_STATUS_UNSPECIFIED",
		1: "PLAYER_STATUS_ACTIVE",
		2: "PLAYER_STATUS_DISCONNECTED",
		3: "PLAYER_STATUS_LEFT",
	}
	PlayerStatus_value = map[string]int32{
		"PLAYER_STATUS_UNSPECIFIED":  0,
		"PLAYER_STATUS_ACTIVE":       1,
		"PLAYER_STATUS_DISCONNECTED": 2,
		"PLAYER_STATUS_LEFT":         3,
	}
)

func (x PlayerStatus) Enum() *PlayerStatus {
	p := new(PlayerStatus)
	*p = x
	return p
}

func (x PlayerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_room_proto_enumTypes[2].Descriptor()
}

func (PlayerStatus) Type() protoreflect.EnumType {
	return &file_proto_room_proto_enumTypes[2]
}

func (x PlayerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerStatus.Descriptor instead.
func (PlayerStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{2}
}

// Core data structures
type Room struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GameType       GameType               `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	Status         RoomStatus             `protobuf:"varint,4,opt,name=status,proto3,enum=room.RoomStatus" json:"status,omitempty"`
	CurrentPlayers int32                  `protobuf:"varint,5,opt,name=current_players,json=currentPlayers,proto3" json:"current_players,omitempty"`
	MaxPlayers     int32                  `protobuf:"varint,6,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	MinPlayers     int32                  `protobuf:"varint,7,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	BetAmount      int64                  `protobuf:"varint,8,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Players        []*RoomPlayer          `protobuf:"bytes,9,rep,name=players,proto3" json:"players,omitempty"`
	Configuration  *RoomConfiguration     `protobuf:"bytes,10,opt,name=configuration,proto3" json:"configuration,omitempty"`
	CurrentSession string                 `protobuf:"bytes,11,opt,name=current_session,json=currentSession,proto3" json:"current_session,omitempty"`
	LastActivity   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=last_activity,json=lastActivity,proto3" json:"last_activity,omitempty"`
	CreatedBy      string                 `protobuf:"bytes,13,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Room) Reset() {
	*x = Room{}
	mi := &file_proto_room_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Room) ProtoMessage() {}

func (x *Room) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Room.ProtoReflect.Descriptor instead.
func (*Room) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{0}
}

func (x *Room) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Room) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Room) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *Room) GetStatus() RoomStatus {
	if x != nil {
		return x.Status
	}
	return RoomStatus_ROOM_STATUS_UNSPECIFIED
}

func (x *Room) GetCurrentPlayers() int32 {
	if x != nil {
		return x.CurrentPlayers
	}
	return 0
}

func (x *Room) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *Room) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *Room) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Room) GetPlayers() []*RoomPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *Room) GetConfiguration() *RoomConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *Room) GetCurrentSession() string {
	if x != nil {
		return x.CurrentSession
	}
	return ""
}

func (x *Room) GetLastActivity() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivity
	}
	return nil
}

func (x *Room) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Room) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Room) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type RoomPlayer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Position      int32                  `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
	IsReady       bool                   `protobuf:"varint,4,opt,name=is_ready,json=isReady,proto3" json:"is_ready,omitempty"`
	BetAmount     int64                  `protobuf:"varint,5,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	Status        PlayerStatus           `protobuf:"varint,7,opt,name=status,proto3,enum=room.PlayerStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomPlayer) Reset() {
	*x = RoomPlayer{}
	mi := &file_proto_room_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomPlayer) ProtoMessage() {}

func (x *RoomPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomPlayer.ProtoReflect.Descriptor instead.
func (*RoomPlayer) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{1}
}

func (x *RoomPlayer) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RoomPlayer) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RoomPlayer) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *RoomPlayer) GetIsReady() bool {
	if x != nil {
		return x.IsReady
	}
	return false
}

func (x *RoomPlayer) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *RoomPlayer) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *RoomPlayer) GetStatus() PlayerStatus {
	if x != nil {
		return x.Status
	}
	return PlayerStatus_PLAYER_STATUS_UNSPECIFIED
}

type RoomConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	BetLimits     *BetLimitConfig        `protobuf:"bytes,2,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	Timeouts      *TimeoutConfig         `protobuf:"bytes,3,opt,name=timeouts,proto3" json:"timeouts,omitempty"`
	AutoStart     bool                   `protobuf:"varint,4,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	IsPrivate     bool                   `protobuf:"varint,5,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Password      string                 `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,7,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomConfiguration) Reset() {
	*x = RoomConfiguration{}
	mi := &file_proto_room_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomConfiguration) ProtoMessage() {}

func (x *RoomConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomConfiguration.ProtoReflect.Descriptor instead.
func (*RoomConfiguration) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{2}
}

func (x *RoomConfiguration) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *RoomConfiguration) GetBetLimits() *BetLimitConfig {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *RoomConfiguration) GetTimeouts() *TimeoutConfig {
	if x != nil {
		return x.Timeouts
	}
	return nil
}

func (x *RoomConfiguration) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *RoomConfiguration) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *RoomConfiguration) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RoomConfiguration) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

type BetLimitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MinBet        int64                  `protobuf:"varint,1,opt,name=min_bet,json=minBet,proto3" json:"min_bet,omitempty"`
	MaxBet        int64                  `protobuf:"varint,2,opt,name=max_bet,json=maxBet,proto3" json:"max_bet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BetLimitConfig) Reset() {
	*x = BetLimitConfig{}
	mi := &file_proto_room_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BetLimitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BetLimitConfig) ProtoMessage() {}

func (x *BetLimitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BetLimitConfig.ProtoReflect.Descriptor instead.
func (*BetLimitConfig) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{3}
}

func (x *BetLimitConfig) GetMinBet() int64 {
	if x != nil {
		return x.MinBet
	}
	return 0
}

func (x *BetLimitConfig) GetMaxBet() int64 {
	if x != nil {
		return x.MaxBet
	}
	return 0
}

type TimeoutConfig struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	JoinTimeoutSeconds  int64                  `protobuf:"varint,1,opt,name=join_timeout_seconds,json=joinTimeoutSeconds,proto3" json:"join_timeout_seconds,omitempty"`
	ReadyTimeoutSeconds int64                  `protobuf:"varint,2,opt,name=ready_timeout_seconds,json=readyTimeoutSeconds,proto3" json:"ready_timeout_seconds,omitempty"`
	GameTimeoutSeconds  int64                  `protobuf:"varint,3,opt,name=game_timeout_seconds,json=gameTimeoutSeconds,proto3" json:"game_timeout_seconds,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TimeoutConfig) Reset() {
	*x = TimeoutConfig{}
	mi := &file_proto_room_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeoutConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeoutConfig) ProtoMessage() {}

func (x *TimeoutConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeoutConfig.ProtoReflect.Descriptor instead.
func (*TimeoutConfig) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{4}
}

func (x *TimeoutConfig) GetJoinTimeoutSeconds() int64 {
	if x != nil {
		return x.JoinTimeoutSeconds
	}
	return 0
}

func (x *TimeoutConfig) GetReadyTimeoutSeconds() int64 {
	if x != nil {
		return x.ReadyTimeoutSeconds
	}
	return 0
}

func (x *TimeoutConfig) GetGameTimeoutSeconds() int64 {
	if x != nil {
		return x.GameTimeoutSeconds
	}
	return 0
}

// CreateRoom
type CreateRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	GameType      GameType               `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,3,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	MinPlayers    int32                  `protobuf:"varint,4,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	BetLimits     *BetLimitConfig        `protobuf:"bytes,5,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	AutoStart     bool                   `protobuf:"varint,6,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	IsPrivate     bool                   `protobuf:"varint,7,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Password      string                 `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,9,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CreatedBy     string                 `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomRequest) Reset() {
	*x = CreateRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomRequest) ProtoMessage() {}

func (x *CreateRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomRequest.ProtoReflect.Descriptor instead.
func (*CreateRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{5}
}

func (x *CreateRoomRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRoomRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *CreateRoomRequest) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *CreateRoomRequest) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *CreateRoomRequest) GetBetLimits() *BetLimitConfig {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *CreateRoomRequest) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *CreateRoomRequest) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *CreateRoomRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateRoomRequest) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

func (x *CreateRoomRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomResponse) Reset() {
	*x = CreateRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomResponse) ProtoMessage() {}

func (x *CreateRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomResponse.ProtoReflect.Descriptor instead.
func (*CreateRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{6}
}

func (x *CreateRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *CreateRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetRoom
type GetRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ForceRefresh  bool                   `protobuf:"varint,2,opt,name=force_refresh,json=forceRefresh,proto3" json:"force_refresh,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomRequest) Reset() {
	*x = GetRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomRequest) ProtoMessage() {}

func (x *GetRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomRequest.ProtoReflect.Descriptor instead.
func (*GetRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{7}
}

func (x *GetRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GetRoomRequest) GetForceRefresh() bool {
	if x != nil {
		return x.ForceRefresh
	}
	return false
}

type GetRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomResponse) Reset() {
	*x = GetRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomResponse) ProtoMessage() {}

func (x *GetRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomResponse.ProtoReflect.Descriptor instead.
func (*GetRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{8}
}

func (x *GetRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *GetRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// UpdateRoom
type UpdateRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,3,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	BetLimits     *BetLimitConfig        `protobuf:"bytes,4,opt,name=bet_limits,json=betLimits,proto3" json:"bet_limits,omitempty"`
	AutoStart     bool                   `protobuf:"varint,5,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	GameSpecific  map[string]string      `protobuf:"bytes,6,rep,name=game_specific,json=gameSpecific,proto3" json:"game_specific,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoomRequest) Reset() {
	*x = UpdateRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomRequest) ProtoMessage() {}

func (x *UpdateRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *UpdateRoomRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateRoomRequest) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *UpdateRoomRequest) GetBetLimits() *BetLimitConfig {
	if x != nil {
		return x.BetLimits
	}
	return nil
}

func (x *UpdateRoomRequest) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *UpdateRoomRequest) GetGameSpecific() map[string]string {
	if x != nil {
		return x.GameSpecific
	}
	return nil
}

type UpdateRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoomResponse) Reset() {
	*x = UpdateRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomResponse) ProtoMessage() {}

func (x *UpdateRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomResponse.ProtoReflect.Descriptor instead.
func (*UpdateRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *UpdateRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// DeleteRoom
type DeleteRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	AdminUserId   string                 `protobuf:"bytes,2,opt,name=admin_user_id,json=adminUserId,proto3" json:"admin_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomRequest) Reset() {
	*x = DeleteRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomRequest) ProtoMessage() {}

func (x *DeleteRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *DeleteRoomRequest) GetAdminUserId() string {
	if x != nil {
		return x.AdminUserId
	}
	return ""
}

type DeleteRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomResponse) Reset() {
	*x = DeleteRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomResponse) ProtoMessage() {}

func (x *DeleteRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomResponse.ProtoReflect.Descriptor instead.
func (*DeleteRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteRoomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// ListRooms
type ListRoomsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	Status        RoomStatus             `protobuf:"varint,2,opt,name=status,proto3,enum=room.RoomStatus" json:"status,omitempty"`
	MinPlayers    int32                  `protobuf:"varint,3,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	MaxPlayers    int32                  `protobuf:"varint,4,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	HasSpace      bool                   `protobuf:"varint,5,opt,name=has_space,json=hasSpace,proto3" json:"has_space,omitempty"`
	Page          int32                  `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsRequest) Reset() {
	*x = ListRoomsRequest{}
	mi := &file_proto_room_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsRequest) ProtoMessage() {}

func (x *ListRoomsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsRequest.ProtoReflect.Descriptor instead.
func (*ListRoomsRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{13}
}

func (x *ListRoomsRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *ListRoomsRequest) GetStatus() RoomStatus {
	if x != nil {
		return x.Status
	}
	return RoomStatus_ROOM_STATUS_UNSPECIFIED
}

func (x *ListRoomsRequest) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *ListRoomsRequest) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *ListRoomsRequest) GetHasSpace() bool {
	if x != nil {
		return x.HasSpace
	}
	return false
}

func (x *ListRoomsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRoomsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListRoomsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rooms         []*Room                `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsResponse) Reset() {
	*x = ListRoomsResponse{}
	mi := &file_proto_room_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsResponse) ProtoMessage() {}

func (x *ListRoomsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsResponse.ProtoReflect.Descriptor instead.
func (*ListRoomsResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{14}
}

func (x *ListRoomsResponse) GetRooms() []*Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *ListRoomsResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListRoomsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   int32                  `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PerPage       int32                  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	TotalCount    int64                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPages    int32                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_proto_room_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{15}
}

func (x *Pagination) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *Pagination) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *Pagination) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

// JoinRoom
type JoinRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	BetAmount     int64                  `protobuf:"varint,5,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinRoomRequest) Reset() {
	*x = JoinRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRoomRequest) ProtoMessage() {}

func (x *JoinRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRoomRequest.ProtoReflect.Descriptor instead.
func (*JoinRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{16}
}

func (x *JoinRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *JoinRoomRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *JoinRoomRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *JoinRoomRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *JoinRoomRequest) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type JoinRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinRoomResponse) Reset() {
	*x = JoinRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRoomResponse) ProtoMessage() {}

func (x *JoinRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRoomResponse.ProtoReflect.Descriptor instead.
func (*JoinRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{17}
}

func (x *JoinRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *JoinRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// LeaveRoom
type LeaveRoomRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveRoomRequest) Reset() {
	*x = LeaveRoomRequest{}
	mi := &file_proto_room_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomRequest) ProtoMessage() {}

func (x *LeaveRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomRequest.ProtoReflect.Descriptor instead.
func (*LeaveRoomRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{18}
}

func (x *LeaveRoomRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *LeaveRoomRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type LeaveRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveRoomResponse) Reset() {
	*x = LeaveRoomResponse{}
	mi := &file_proto_room_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomResponse) ProtoMessage() {}

func (x *LeaveRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomResponse.ProtoReflect.Descriptor instead.
func (*LeaveRoomResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{19}
}

func (x *LeaveRoomResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *LeaveRoomResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// SetPlayerReady
type SetPlayerReadyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Ready         bool                   `protobuf:"varint,3,opt,name=ready,proto3" json:"ready,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPlayerReadyRequest) Reset() {
	*x = SetPlayerReadyRequest{}
	mi := &file_proto_room_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPlayerReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerReadyRequest) ProtoMessage() {}

func (x *SetPlayerReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerReadyRequest.ProtoReflect.Descriptor instead.
func (*SetPlayerReadyRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{20}
}

func (x *SetPlayerReadyRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SetPlayerReadyRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SetPlayerReadyRequest) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

type SetPlayerReadyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPlayerReadyResponse) Reset() {
	*x = SetPlayerReadyResponse{}
	mi := &file_proto_room_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPlayerReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPlayerReadyResponse) ProtoMessage() {}

func (x *SetPlayerReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPlayerReadyResponse.ProtoReflect.Descriptor instead.
func (*SetPlayerReadyResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{21}
}

func (x *SetPlayerReadyResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *SetPlayerReadyResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetRoomsByPlayer
type GetRoomsByPlayerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomsByPlayerRequest) Reset() {
	*x = GetRoomsByPlayerRequest{}
	mi := &file_proto_room_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomsByPlayerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomsByPlayerRequest) ProtoMessage() {}

func (x *GetRoomsByPlayerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomsByPlayerRequest.ProtoReflect.Descriptor instead.
func (*GetRoomsByPlayerRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{22}
}

func (x *GetRoomsByPlayerRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetRoomsByPlayerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rooms         []*Room                `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomsByPlayerResponse) Reset() {
	*x = GetRoomsByPlayerResponse{}
	mi := &file_proto_room_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomsByPlayerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomsByPlayerResponse) ProtoMessage() {}

func (x *GetRoomsByPlayerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomsByPlayerResponse.ProtoReflect.Descriptor instead.
func (*GetRoomsByPlayerResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{23}
}

func (x *GetRoomsByPlayerResponse) GetRooms() []*Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *GetRoomsByPlayerResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// StartGame
type StartGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	InitiatedBy   string                 `protobuf:"bytes,2,opt,name=initiated_by,json=initiatedBy,proto3" json:"initiated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameRequest) Reset() {
	*x = StartGameRequest{}
	mi := &file_proto_room_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameRequest) ProtoMessage() {}

func (x *StartGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameRequest.ProtoReflect.Descriptor instead.
func (*StartGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{24}
}

func (x *StartGameRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *StartGameRequest) GetInitiatedBy() string {
	if x != nil {
		return x.InitiatedBy
	}
	return ""
}

type StartGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	SessionId     string                 `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartGameResponse) Reset() {
	*x = StartGameResponse{}
	mi := &file_proto_room_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartGameResponse) ProtoMessage() {}

func (x *StartGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartGameResponse.ProtoReflect.Descriptor instead.
func (*StartGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{25}
}

func (x *StartGameResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *StartGameResponse) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *StartGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// EndGame
type EndGameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	SessionId     string                 `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Results       map[string]string      `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndGameRequest) Reset() {
	*x = EndGameRequest{}
	mi := &file_proto_room_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndGameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndGameRequest) ProtoMessage() {}

func (x *EndGameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndGameRequest.ProtoReflect.Descriptor instead.
func (*EndGameRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{26}
}

func (x *EndGameRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *EndGameRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *EndGameRequest) GetResults() map[string]string {
	if x != nil {
		return x.Results
	}
	return nil
}

type EndGameResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndGameResponse) Reset() {
	*x = EndGameResponse{}
	mi := &file_proto_room_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndGameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndGameResponse) ProtoMessage() {}

func (x *EndGameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndGameResponse.ProtoReflect.Descriptor instead.
func (*EndGameResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{27}
}

func (x *EndGameResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *EndGameResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetAvailableRooms
type GetAvailableRoomsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameType      GameType               `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableRoomsRequest) Reset() {
	*x = GetAvailableRoomsRequest{}
	mi := &file_proto_room_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableRoomsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableRoomsRequest) ProtoMessage() {}

func (x *GetAvailableRoomsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableRoomsRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableRoomsRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{28}
}

func (x *GetAvailableRoomsRequest) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

type GetAvailableRoomsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rooms         []*Room                `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableRoomsResponse) Reset() {
	*x = GetAvailableRoomsResponse{}
	mi := &file_proto_room_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableRoomsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableRoomsResponse) ProtoMessage() {}

func (x *GetAvailableRoomsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableRoomsResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableRoomsResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{29}
}

func (x *GetAvailableRoomsResponse) GetRooms() []*Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *GetAvailableRoomsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// GetRoomStats
type GetRoomStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomStatsRequest) Reset() {
	*x = GetRoomStatsRequest{}
	mi := &file_proto_room_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomStatsRequest) ProtoMessage() {}

func (x *GetRoomStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomStatsRequest.ProtoReflect.Descriptor instead.
func (*GetRoomStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{30}
}

func (x *GetRoomStatsRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *RoomStats             `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomStatsResponse) Reset() {
	*x = GetRoomStatsResponse{}
	mi := &file_proto_room_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomStatsResponse) ProtoMessage() {}

func (x *GetRoomStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomStatsResponse.ProtoReflect.Descriptor instead.
func (*GetRoomStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{31}
}

func (x *GetRoomStatsResponse) GetStats() *RoomStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetRoomStatsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type RoomStats struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	RoomId                 string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	TotalGames             int32                  `protobuf:"varint,2,opt,name=total_games,json=totalGames,proto3" json:"total_games,omitempty"`
	TotalPlayers           int32                  `protobuf:"varint,3,opt,name=total_players,json=totalPlayers,proto3" json:"total_players,omitempty"`
	AverageGameTimeSeconds int64                  `protobuf:"varint,4,opt,name=average_game_time_seconds,json=averageGameTimeSeconds,proto3" json:"average_game_time_seconds,omitempty"`
	TotalBetVolume         int64                  `protobuf:"varint,5,opt,name=total_bet_volume,json=totalBetVolume,proto3" json:"total_bet_volume,omitempty"`
	LastGameAt             *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_game_at,json=lastGameAt,proto3" json:"last_game_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *RoomStats) Reset() {
	*x = RoomStats{}
	mi := &file_proto_room_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomStats) ProtoMessage() {}

func (x *RoomStats) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomStats.ProtoReflect.Descriptor instead.
func (*RoomStats) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{32}
}

func (x *RoomStats) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomStats) GetTotalGames() int32 {
	if x != nil {
		return x.TotalGames
	}
	return 0
}

func (x *RoomStats) GetTotalPlayers() int32 {
	if x != nil {
		return x.TotalPlayers
	}
	return 0
}

func (x *RoomStats) GetAverageGameTimeSeconds() int64 {
	if x != nil {
		return x.AverageGameTimeSeconds
	}
	return 0
}

func (x *RoomStats) GetTotalBetVolume() int64 {
	if x != nil {
		return x.TotalBetVolume
	}
	return 0
}

func (x *RoomStats) GetLastGameAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastGameAt
	}
	return nil
}

// GetRoomActivity
type GetRoomActivityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomActivityRequest) Reset() {
	*x = GetRoomActivityRequest{}
	mi := &file_proto_room_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomActivityRequest) ProtoMessage() {}

func (x *GetRoomActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomActivityRequest.ProtoReflect.Descriptor instead.
func (*GetRoomActivityRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{33}
}

func (x *GetRoomActivityRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomActivityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Activity      *RoomActivity          `protobuf:"bytes,1,opt,name=activity,proto3" json:"activity,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomActivityResponse) Reset() {
	*x = GetRoomActivityResponse{}
	mi := &file_proto_room_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomActivityResponse) ProtoMessage() {}

func (x *GetRoomActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomActivityResponse.ProtoReflect.Descriptor instead.
func (*GetRoomActivityResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{34}
}

func (x *GetRoomActivityResponse) GetActivity() *RoomActivity {
	if x != nil {
		return x.Activity
	}
	return nil
}

func (x *GetRoomActivityResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type RoomActivity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Events        []*RoomEvent           `protobuf:"bytes,2,rep,name=events,proto3" json:"events,omitempty"`
	Players       []*RoomPlayer          `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomActivity) Reset() {
	*x = RoomActivity{}
	mi := &file_proto_room_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomActivity) ProtoMessage() {}

func (x *RoomActivity) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomActivity.ProtoReflect.Descriptor instead.
func (*RoomActivity) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{35}
}

func (x *RoomActivity) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomActivity) GetEvents() []*RoomEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

func (x *RoomActivity) GetPlayers() []*RoomPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *RoomActivity) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type RoomEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	RoomId        string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Data          map[string]string      `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomEvent) Reset() {
	*x = RoomEvent{}
	mi := &file_proto_room_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEvent) ProtoMessage() {}

func (x *RoomEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEvent.ProtoReflect.Descriptor instead.
func (*RoomEvent) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{36}
}

func (x *RoomEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RoomEvent) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomEvent) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RoomEvent) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *RoomEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// GetRoomInfo
type GetRoomInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomInfoRequest) Reset() {
	*x = GetRoomInfoRequest{}
	mi := &file_proto_room_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoRequest) ProtoMessage() {}

func (x *GetRoomInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoRequest.ProtoReflect.Descriptor instead.
func (*GetRoomInfoRequest) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{37}
}

func (x *GetRoomInfoRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomInfo      *RoomInfo              `protobuf:"bytes,1,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoomInfoResponse) Reset() {
	*x = GetRoomInfoResponse{}
	mi := &file_proto_room_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoomInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoResponse) ProtoMessage() {}

func (x *GetRoomInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoResponse.ProtoReflect.Descriptor instead.
func (*GetRoomInfoResponse) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{38}
}

func (x *GetRoomInfoResponse) GetRoomInfo() *RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

func (x *GetRoomInfoResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type RoomInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RoomId         string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GameType       GameType               `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=room.GameType" json:"game_type,omitempty"`
	Status         RoomStatus             `protobuf:"varint,4,opt,name=status,proto3,enum=room.RoomStatus" json:"status,omitempty"`
	CurrentPlayers int32                  `protobuf:"varint,5,opt,name=current_players,json=currentPlayers,proto3" json:"current_players,omitempty"`
	MaxPlayers     int32                  `protobuf:"varint,6,opt,name=max_players,json=maxPlayers,proto3" json:"max_players,omitempty"`
	MinPlayers     int32                  `protobuf:"varint,7,opt,name=min_players,json=minPlayers,proto3" json:"min_players,omitempty"`
	BetAmount      int64                  `protobuf:"varint,8,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Players        []*RoomPlayer          `protobuf:"bytes,9,rep,name=players,proto3" json:"players,omitempty"`
	IsPrivate      bool                   `protobuf:"varint,10,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	AutoStart      bool                   `protobuf:"varint,11,opt,name=auto_start,json=autoStart,proto3" json:"auto_start,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastActivity   *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=last_activity,json=lastActivity,proto3" json:"last_activity,omitempty"`
	CanJoin        bool                   `protobuf:"varint,14,opt,name=can_join,json=canJoin,proto3" json:"can_join,omitempty"`
	CanStart       bool                   `protobuf:"varint,15,opt,name=can_start,json=canStart,proto3" json:"can_start,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RoomInfo) Reset() {
	*x = RoomInfo{}
	mi := &file_proto_room_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomInfo) ProtoMessage() {}

func (x *RoomInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_room_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomInfo.ProtoReflect.Descriptor instead.
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return file_proto_room_proto_rawDescGZIP(), []int{39}
}

func (x *RoomInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoomInfo) GetGameType() GameType {
	if x != nil {
		return x.GameType
	}
	return GameType_GAME_TYPE_UNSPECIFIED
}

func (x *RoomInfo) GetStatus() RoomStatus {
	if x != nil {
		return x.Status
	}
	return RoomStatus_ROOM_STATUS_UNSPECIFIED
}

func (x *RoomInfo) GetCurrentPlayers() int32 {
	if x != nil {
		return x.CurrentPlayers
	}
	return 0
}

func (x *RoomInfo) GetMaxPlayers() int32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *RoomInfo) GetMinPlayers() int32 {
	if x != nil {
		return x.MinPlayers
	}
	return 0
}

func (x *RoomInfo) GetBetAmount() int64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *RoomInfo) GetPlayers() []*RoomPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *RoomInfo) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *RoomInfo) GetAutoStart() bool {
	if x != nil {
		return x.AutoStart
	}
	return false
}

func (x *RoomInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RoomInfo) GetLastActivity() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivity
	}
	return nil
}

func (x *RoomInfo) GetCanJoin() bool {
	if x != nil {
		return x.CanJoin
	}
	return false
}

func (x *RoomInfo) GetCanStart() bool {
	if x != nil {
		return x.CanStart
	}
	return false
}

var File_proto_room_proto protoreflect.FileDescriptor

const file_proto_room_proto_rawDesc = "" +
	"\n" +
	"\x10proto/room.proto\x12\x04room\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf5\x04\n" +
	"\x04Room\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12+\n" +
	"\tgame_type\x18\x03 \x01(\x0e2\x0e.room.GameTypeR\bgameType\x12(\n" +
	"\x06status\x18\x04 \x01(\x0e2\x10.room.RoomStatusR\x06status\x12'\n" +
	"\x0fcurrent_players\x18\x05 \x01(\x05R\x0ecurrentPlayers\x12\x1f\n" +
	"\vmax_players\x18\x06 \x01(\x05R\n" +
	"maxPlayers\x12\x1f\n" +
	"\vmin_players\x18\a \x01(\x05R\n" +
	"minPlayers\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\b \x01(\x03R\tbetAmount\x12*\n" +
	"\aplayers\x18\t \x03(\v2\x10.room.RoomPlayerR\aplayers\x12=\n" +
	"\rconfiguration\x18\n" +
	" \x01(\v2\x17.room.RoomConfigurationR\rconfiguration\x12'\n" +
	"\x0fcurrent_session\x18\v \x01(\tR\x0ecurrentSession\x12?\n" +
	"\rlast_activity\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\flastActivity\x12\x1d\n" +
	"\n" +
	"created_by\x18\r \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xfc\x01\n" +
	"\n" +
	"RoomPlayer\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bposition\x18\x03 \x01(\x05R\bposition\x12\x19\n" +
	"\bis_ready\x18\x04 \x01(\bR\aisReady\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\x05 \x01(\x03R\tbetAmount\x127\n" +
	"\tjoined_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\x12*\n" +
	"\x06status\x18\a \x01(\x0e2\x12.room.PlayerStatusR\x06status\"\x91\x03\n" +
	"\x11RoomConfiguration\x12+\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x0e.room.GameTypeR\bgameType\x123\n" +
	"\n" +
	"bet_limits\x18\x02 \x01(\v2\x14.room.BetLimitConfigR\tbetLimits\x12/\n" +
	"\btimeouts\x18\x03 \x01(\v2\x13.room.TimeoutConfigR\btimeouts\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x04 \x01(\bR\tautoStart\x12\x1d\n" +
	"\n" +
	"is_private\x18\x05 \x01(\bR\tisPrivate\x12\x1a\n" +
	"\bpassword\x18\x06 \x01(\tR\bpassword\x12N\n" +
	"\rgame_specific\x18\a \x03(\v2).room.RoomConfiguration.GameSpecificEntryR\fgameSpecific\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"B\n" +
	"\x0eBetLimitConfig\x12\x17\n" +
	"\amin_bet\x18\x01 \x01(\x03R\x06minBet\x12\x17\n" +
	"\amax_bet\x18\x02 \x01(\x03R\x06maxBet\"\xa7\x01\n" +
	"\rTimeoutConfig\x120\n" +
	"\x14join_timeout_seconds\x18\x01 \x01(\x03R\x12joinTimeoutSeconds\x122\n" +
	"\x15ready_timeout_seconds\x18\x02 \x01(\x03R\x13readyTimeoutSeconds\x120\n" +
	"\x14game_timeout_seconds\x18\x03 \x01(\x03R\x12gameTimeoutSeconds\"\xd5\x03\n" +
	"\x11CreateRoomRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12+\n" +
	"\tgame_type\x18\x02 \x01(\x0e2\x0e.room.GameTypeR\bgameType\x12\x1f\n" +
	"\vmax_players\x18\x03 \x01(\x05R\n" +
	"maxPlayers\x12\x1f\n" +
	"\vmin_players\x18\x04 \x01(\x05R\n" +
	"minPlayers\x123\n" +
	"\n" +
	"bet_limits\x18\x05 \x01(\v2\x14.room.BetLimitConfigR\tbetLimits\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x06 \x01(\bR\tautoStart\x12\x1d\n" +
	"\n" +
	"is_private\x18\a \x01(\bR\tisPrivate\x12\x1a\n" +
	"\bpassword\x18\b \x01(\tR\bpassword\x12N\n" +
	"\rgame_specific\x18\t \x03(\v2).room.CreateRoomRequest.GameSpecificEntryR\fgameSpecific\x12\x1d\n" +
	"\n" +
	"created_by\x18\n" +
	" \x01(\tR\tcreatedBy\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"J\n" +
	"\x12CreateRoomResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"N\n" +
	"\x0eGetRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12#\n" +
	"\rforce_refresh\x18\x02 \x01(\bR\fforceRefresh\"G\n" +
	"\x0fGetRoomResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xc6\x02\n" +
	"\x11UpdateRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vmax_players\x18\x03 \x01(\x05R\n" +
	"maxPlayers\x123\n" +
	"\n" +
	"bet_limits\x18\x04 \x01(\v2\x14.room.BetLimitConfigR\tbetLimits\x12\x1d\n" +
	"\n" +
	"auto_start\x18\x05 \x01(\bR\tautoStart\x12N\n" +
	"\rgame_specific\x18\x06 \x03(\v2).room.UpdateRoomRequest.GameSpecificEntryR\fgameSpecific\x1a?\n" +
	"\x11GameSpecificEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"J\n" +
	"\x12UpdateRoomResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"P\n" +
	"\x11DeleteRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\"\n" +
	"\radmin_user_id\x18\x02 \x01(\tR\vadminUserId\"D\n" +
	"\x12DeleteRoomResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xf2\x01\n" +
	"\x10ListRoomsRequest\x12+\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x0e.room.GameTypeR\bgameType\x12(\n" +
	"\x06status\x18\x02 \x01(\x0e2\x10.room.RoomStatusR\x06status\x12\x1f\n" +
	"\vmin_players\x18\x03 \x01(\x05R\n" +
	"minPlayers\x12\x1f\n" +
	"\vmax_players\x18\x04 \x01(\x05R\n" +
	"maxPlayers\x12\x1b\n" +
	"\thas_space\x18\x05 \x01(\bR\bhasSpace\x12\x12\n" +
	"\x04page\x18\x06 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\a \x01(\x05R\x05limit\"}\n" +
	"\x11ListRoomsResponse\x12 \n" +
	"\x05rooms\x18\x01 \x03(\v2\n" +
	".room.RoomR\x05rooms\x120\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x10.room.PaginationR\n" +
	"pagination\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"\x8c\x01\n" +
	"\n" +
	"Pagination\x12!\n" +
	"\fcurrent_page\x18\x01 \x01(\x05R\vcurrentPage\x12\x19\n" +
	"\bper_page\x18\x02 \x01(\x05R\aperPage\x12\x1f\n" +
	"\vtotal_count\x18\x03 \x01(\x03R\n" +
	"totalCount\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x05R\n" +
	"totalPages\"\x9a\x01\n" +
	"\x0fJoinRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\x05 \x01(\x03R\tbetAmount\"H\n" +
	"\x10JoinRoomResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"D\n" +
	"\x10LeaveRoomRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"I\n" +
	"\x11LeaveRoomResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"_\n" +
	"\x15SetPlayerReadyRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05ready\x18\x03 \x01(\bR\x05ready\"N\n" +
	"\x16SetPlayerReadyResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"2\n" +
	"\x17GetRoomsByPlayerRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"R\n" +
	"\x18GetRoomsByPlayerResponse\x12 \n" +
	"\x05rooms\x18\x01 \x03(\v2\n" +
	".room.RoomR\x05rooms\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"N\n" +
	"\x10StartGameRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12!\n" +
	"\finitiated_by\x18\x02 \x01(\tR\vinitiatedBy\"h\n" +
	"\x11StartGameResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"\xc1\x01\n" +
	"\x0eEndGameRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\x12;\n" +
	"\aresults\x18\x03 \x03(\v2!.room.EndGameRequest.ResultsEntryR\aresults\x1a:\n" +
	"\fResultsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"G\n" +
	"\x0fEndGameResponse\x12\x1e\n" +
	"\x04room\x18\x01 \x01(\v2\n" +
	".room.RoomR\x04room\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"G\n" +
	"\x18GetAvailableRoomsRequest\x12+\n" +
	"\tgame_type\x18\x01 \x01(\x0e2\x0e.room.GameTypeR\bgameType\"S\n" +
	"\x19GetAvailableRoomsResponse\x12 \n" +
	"\x05rooms\x18\x01 \x03(\v2\n" +
	".room.RoomR\x05rooms\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\".\n" +
	"\x13GetRoomStatsRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\"S\n" +
	"\x14GetRoomStatsResponse\x12%\n" +
	"\x05stats\x18\x01 \x01(\v2\x0f.room.RoomStatsR\x05stats\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\x8d\x02\n" +
	"\tRoomStats\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1f\n" +
	"\vtotal_games\x18\x02 \x01(\x05R\n" +
	"totalGames\x12#\n" +
	"\rtotal_players\x18\x03 \x01(\x05R\ftotalPlayers\x129\n" +
	"\x19average_game_time_seconds\x18\x04 \x01(\x03R\x16averageGameTimeSeconds\x12(\n" +
	"\x10total_bet_volume\x18\x05 \x01(\x03R\x0etotalBetVolume\x12<\n" +
	"\flast_game_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastGameAt\"1\n" +
	"\x16GetRoomActivityRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\"_\n" +
	"\x17GetRoomActivityResponse\x12.\n" +
	"\bactivity\x18\x01 \x01(\v2\x12.room.RoomActivityR\bactivity\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xb7\x01\n" +
	"\fRoomActivity\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12'\n" +
	"\x06events\x18\x02 \x03(\v2\x0f.room.RoomEventR\x06events\x12*\n" +
	"\aplayers\x18\x03 \x03(\v2\x10.room.RoomPlayerR\aplayers\x129\n" +
	"\n" +
	"updated_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xf3\x01\n" +
	"\tRoomEvent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x17\n" +
	"\aroom_id\x18\x02 \x01(\tR\x06roomId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12-\n" +
	"\x04data\x18\x04 \x03(\v2\x19.room.RoomEvent.DataEntryR\x04data\x128\n" +
	"\ttimestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"-\n" +
	"\x12GetRoomInfoRequest\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\"X\n" +
	"\x13GetRoomInfoResponse\x12+\n" +
	"\troom_info\x18\x01 \x01(\v2\x0e.room.RoomInfoR\broomInfo\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"\xb6\x04\n" +
	"\bRoomInfo\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12+\n" +
	"\tgame_type\x18\x03 \x01(\x0e2\x0e.room.GameTypeR\bgameType\x12(\n" +
	"\x06status\x18\x04 \x01(\x0e2\x10.room.RoomStatusR\x06status\x12'\n" +
	"\x0fcurrent_players\x18\x05 \x01(\x05R\x0ecurrentPlayers\x12\x1f\n" +
	"\vmax_players\x18\x06 \x01(\x05R\n" +
	"maxPlayers\x12\x1f\n" +
	"\vmin_players\x18\a \x01(\x05R\n" +
	"minPlayers\x12\x1d\n" +
	"\n" +
	"bet_amount\x18\b \x01(\x03R\tbetAmount\x12*\n" +
	"\aplayers\x18\t \x03(\v2\x10.room.RoomPlayerR\aplayers\x12\x1d\n" +
	"\n" +
	"is_private\x18\n" +
	" \x01(\bR\tisPrivate\x12\x1d\n" +
	"\n" +
	"auto_start\x18\v \x01(\bR\tautoStart\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12?\n" +
	"\rlast_activity\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\flastActivity\x12\x19\n" +
	"\bcan_join\x18\x0e \x01(\bR\acanJoin\x12\x1b\n" +
	"\tcan_start\x18\x0f \x01(\bR\bcanStart*Y\n" +
	"\bGameType\x12\x19\n" +
	"\x15GAME_TYPE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15GAME_TYPE_PRIZE_WHEEL\x10\x01\x12\x17\n" +
	"\x13GAME_TYPE_AMIDAKUJI\x10\x02*\xa2\x01\n" +
	"\n" +
	"RoomStatus\x12\x1b\n" +
	"\x17ROOM_STATUS_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13ROOM_STATUS_WAITING\x10\x01\x12\x16\n" +
	"\x12ROOM_STATUS_ACTIVE\x10\x02\x12\x14\n" +
	"\x10ROOM_STATUS_FULL\x10\x03\x12\x16\n" +
	"\x12ROOM_STATUS_CLOSED\x10\x04\x12\x18\n" +
	"\x14ROOM_STATUS_ARCHIVED\x10\x05*\x7f\n" +
	"\fPlayerStatus\x12\x1d\n" +
	"\x19PLAYER_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14PLAYER_STATUS_ACTIVE\x10\x01\x12\x1e\n" +
	"\x1aPLAYER_STATUS_DISCONNECTED\x10\x02\x12\x16\n" +
	"\x12PLAYER_STATUS_LEFT\x10\x032\x86\b\n" +
	"\vRoomService\x12?\n" +
	"\n" +
	"CreateRoom\x12\x17.room.CreateRoomRequest\x1a\x18.room.CreateRoomResponse\x126\n" +
	"\aGetRoom\x12\x14.room.GetRoomRequest\x1a\x15.room.GetRoomResponse\x12?\n" +
	"\n" +
	"UpdateRoom\x12\x17.room.UpdateRoomRequest\x1a\x18.room.UpdateRoomResponse\x12?\n" +
	"\n" +
	"DeleteRoom\x12\x17.room.DeleteRoomRequest\x1a\x18.room.DeleteRoomResponse\x12<\n" +
	"\tListRooms\x12\x16.room.ListRoomsRequest\x1a\x17.room.ListRoomsResponse\x129\n" +
	"\bJoinRoom\x12\x15.room.JoinRoomRequest\x1a\x16.room.JoinRoomResponse\x12<\n" +
	"\tLeaveRoom\x12\x16.room.LeaveRoomRequest\x1a\x17.room.LeaveRoomResponse\x12K\n" +
	"\x0eSetPlayerReady\x12\x1b.room.SetPlayerReadyRequest\x1a\x1c.room.SetPlayerReadyResponse\x12Q\n" +
	"\x10GetRoomsByPlayer\x12\x1d.room.GetRoomsByPlayerRequest\x1a\x1e.room.GetRoomsByPlayerResponse\x12<\n" +
	"\tStartGame\x12\x16.room.StartGameRequest\x1a\x17.room.StartGameResponse\x126\n" +
	"\aEndGame\x12\x14.room.EndGameRequest\x1a\x15.room.EndGameResponse\x12T\n" +
	"\x11GetAvailableRooms\x12\x1e.room.GetAvailableRoomsRequest\x1a\x1f.room.GetAvailableRoomsResponse\x12E\n" +
	"\fGetRoomStats\x12\x19.room.GetRoomStatsRequest\x1a\x1a.room.GetRoomStatsResponse\x12N\n" +
	"\x0fGetRoomActivity\x12\x1c.room.GetRoomActivityRequest\x1a\x1d.room.GetRoomActivityResponse\x12B\n" +
	"\vGetRoomInfo\x12\x18.room.GetRoomInfoRequest\x1a\x19.room.GetRoomInfoResponseB&Z$github.com/xzgame/room-service/protob\x06proto3"

var (
	file_proto_room_proto_rawDescOnce sync.Once
	file_proto_room_proto_rawDescData []byte
)

func file_proto_room_proto_rawDescGZIP() []byte {
	file_proto_room_proto_rawDescOnce.Do(func() {
		file_proto_room_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_room_proto_rawDesc), len(file_proto_room_proto_rawDesc)))
	})
	return file_proto_room_proto_rawDescData
}

var file_proto_room_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_room_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_proto_room_proto_goTypes = []any{
	(GameType)(0),                     // 0: room.GameType
	(RoomStatus)(0),                   // 1: room.RoomStatus
	(PlayerStatus)(0),                 // 2: room.PlayerStatus
	(*Room)(nil),                      // 3: room.Room
	(*RoomPlayer)(nil),                // 4: room.RoomPlayer
	(*RoomConfiguration)(nil),         // 5: room.RoomConfiguration
	(*BetLimitConfig)(nil),            // 6: room.BetLimitConfig
	(*TimeoutConfig)(nil),             // 7: room.TimeoutConfig
	(*CreateRoomRequest)(nil),         // 8: room.CreateRoomRequest
	(*CreateRoomResponse)(nil),        // 9: room.CreateRoomResponse
	(*GetRoomRequest)(nil),            // 10: room.GetRoomRequest
	(*GetRoomResponse)(nil),           // 11: room.GetRoomResponse
	(*UpdateRoomRequest)(nil),         // 12: room.UpdateRoomRequest
	(*UpdateRoomResponse)(nil),        // 13: room.UpdateRoomResponse
	(*DeleteRoomRequest)(nil),         // 14: room.DeleteRoomRequest
	(*DeleteRoomResponse)(nil),        // 15: room.DeleteRoomResponse
	(*ListRoomsRequest)(nil),          // 16: room.ListRoomsRequest
	(*ListRoomsResponse)(nil),         // 17: room.ListRoomsResponse
	(*Pagination)(nil),                // 18: room.Pagination
	(*JoinRoomRequest)(nil),           // 19: room.JoinRoomRequest
	(*JoinRoomResponse)(nil),          // 20: room.JoinRoomResponse
	(*LeaveRoomRequest)(nil),          // 21: room.LeaveRoomRequest
	(*LeaveRoomResponse)(nil),         // 22: room.LeaveRoomResponse
	(*SetPlayerReadyRequest)(nil),     // 23: room.SetPlayerReadyRequest
	(*SetPlayerReadyResponse)(nil),    // 24: room.SetPlayerReadyResponse
	(*GetRoomsByPlayerRequest)(nil),   // 25: room.GetRoomsByPlayerRequest
	(*GetRoomsByPlayerResponse)(nil),  // 26: room.GetRoomsByPlayerResponse
	(*StartGameRequest)(nil),          // 27: room.StartGameRequest
	(*StartGameResponse)(nil),         // 28: room.StartGameResponse
	(*EndGameRequest)(nil),            // 29: room.EndGameRequest
	(*EndGameResponse)(nil),           // 30: room.EndGameResponse
	(*GetAvailableRoomsRequest)(nil),  // 31: room.GetAvailableRoomsRequest
	(*GetAvailableRoomsResponse)(nil), // 32: room.GetAvailableRoomsResponse
	(*GetRoomStatsRequest)(nil),       // 33: room.GetRoomStatsRequest
	(*GetRoomStatsResponse)(nil),      // 34: room.GetRoomStatsResponse
	(*RoomStats)(nil),                 // 35: room.RoomStats
	(*GetRoomActivityRequest)(nil),    // 36: room.GetRoomActivityRequest
	(*GetRoomActivityResponse)(nil),   // 37: room.GetRoomActivityResponse
	(*RoomActivity)(nil),              // 38: room.RoomActivity
	(*RoomEvent)(nil),                 // 39: room.RoomEvent
	(*GetRoomInfoRequest)(nil),        // 40: room.GetRoomInfoRequest
	(*GetRoomInfoResponse)(nil),       // 41: room.GetRoomInfoResponse
	(*RoomInfo)(nil),                  // 42: room.RoomInfo
	nil,                               // 43: room.RoomConfiguration.GameSpecificEntry
	nil,                               // 44: room.CreateRoomRequest.GameSpecificEntry
	nil,                               // 45: room.UpdateRoomRequest.GameSpecificEntry
	nil,                               // 46: room.EndGameRequest.ResultsEntry
	nil,                               // 47: room.RoomEvent.DataEntry
	(*timestamppb.Timestamp)(nil),     // 48: google.protobuf.Timestamp
}
var file_proto_room_proto_depIdxs = []int32{
	0,  // 0: room.Room.game_type:type_name -> room.GameType
	1,  // 1: room.Room.status:type_name -> room.RoomStatus
	4,  // 2: room.Room.players:type_name -> room.RoomPlayer
	5,  // 3: room.Room.configuration:type_name -> room.RoomConfiguration
	48, // 4: room.Room.last_activity:type_name -> google.protobuf.Timestamp
	48, // 5: room.Room.created_at:type_name -> google.protobuf.Timestamp
	48, // 6: room.Room.updated_at:type_name -> google.protobuf.Timestamp
	48, // 7: room.RoomPlayer.joined_at:type_name -> google.protobuf.Timestamp
	2,  // 8: room.RoomPlayer.status:type_name -> room.PlayerStatus
	0,  // 9: room.RoomConfiguration.game_type:type_name -> room.GameType
	6,  // 10: room.RoomConfiguration.bet_limits:type_name -> room.BetLimitConfig
	7,  // 11: room.RoomConfiguration.timeouts:type_name -> room.TimeoutConfig
	43, // 12: room.RoomConfiguration.game_specific:type_name -> room.RoomConfiguration.GameSpecificEntry
	0,  // 13: room.CreateRoomRequest.game_type:type_name -> room.GameType
	6,  // 14: room.CreateRoomRequest.bet_limits:type_name -> room.BetLimitConfig
	44, // 15: room.CreateRoomRequest.game_specific:type_name -> room.CreateRoomRequest.GameSpecificEntry
	3,  // 16: room.CreateRoomResponse.room:type_name -> room.Room
	3,  // 17: room.GetRoomResponse.room:type_name -> room.Room
	6,  // 18: room.UpdateRoomRequest.bet_limits:type_name -> room.BetLimitConfig
	45, // 19: room.UpdateRoomRequest.game_specific:type_name -> room.UpdateRoomRequest.GameSpecificEntry
	3,  // 20: room.UpdateRoomResponse.room:type_name -> room.Room
	0,  // 21: room.ListRoomsRequest.game_type:type_name -> room.GameType
	1,  // 22: room.ListRoomsRequest.status:type_name -> room.RoomStatus
	3,  // 23: room.ListRoomsResponse.rooms:type_name -> room.Room
	18, // 24: room.ListRoomsResponse.pagination:type_name -> room.Pagination
	3,  // 25: room.JoinRoomResponse.room:type_name -> room.Room
	3,  // 26: room.LeaveRoomResponse.room:type_name -> room.Room
	3,  // 27: room.SetPlayerReadyResponse.room:type_name -> room.Room
	3,  // 28: room.GetRoomsByPlayerResponse.rooms:type_name -> room.Room
	3,  // 29: room.StartGameResponse.room:type_name -> room.Room
	46, // 30: room.EndGameRequest.results:type_name -> room.EndGameRequest.ResultsEntry
	3,  // 31: room.EndGameResponse.room:type_name -> room.Room
	0,  // 32: room.GetAvailableRoomsRequest.game_type:type_name -> room.GameType
	3,  // 33: room.GetAvailableRoomsResponse.rooms:type_name -> room.Room
	35, // 34: room.GetRoomStatsResponse.stats:type_name -> room.RoomStats
	48, // 35: room.RoomStats.last_game_at:type_name -> google.protobuf.Timestamp
	38, // 36: room.GetRoomActivityResponse.activity:type_name -> room.RoomActivity
	39, // 37: room.RoomActivity.events:type_name -> room.RoomEvent
	4,  // 38: room.RoomActivity.players:type_name -> room.RoomPlayer
	48, // 39: room.RoomActivity.updated_at:type_name -> google.protobuf.Timestamp
	47, // 40: room.RoomEvent.data:type_name -> room.RoomEvent.DataEntry
	48, // 41: room.RoomEvent.timestamp:type_name -> google.protobuf.Timestamp
	42, // 42: room.GetRoomInfoResponse.room_info:type_name -> room.RoomInfo
	0,  // 43: room.RoomInfo.game_type:type_name -> room.GameType
	1,  // 44: room.RoomInfo.status:type_name -> room.RoomStatus
	4,  // 45: room.RoomInfo.players:type_name -> room.RoomPlayer
	48, // 46: room.RoomInfo.created_at:type_name -> google.protobuf.Timestamp
	48, // 47: room.RoomInfo.last_activity:type_name -> google.protobuf.Timestamp
	8,  // 48: room.RoomService.CreateRoom:input_type -> room.CreateRoomRequest
	10, // 49: room.RoomService.GetRoom:input_type -> room.GetRoomRequest
	12, // 50: room.RoomService.UpdateRoom:input_type -> room.UpdateRoomRequest
	14, // 51: room.RoomService.DeleteRoom:input_type -> room.DeleteRoomRequest
	16, // 52: room.RoomService.ListRooms:input_type -> room.ListRoomsRequest
	19, // 53: room.RoomService.JoinRoom:input_type -> room.JoinRoomRequest
	21, // 54: room.RoomService.LeaveRoom:input_type -> room.LeaveRoomRequest
	23, // 55: room.RoomService.SetPlayerReady:input_type -> room.SetPlayerReadyRequest
	25, // 56: room.RoomService.GetRoomsByPlayer:input_type -> room.GetRoomsByPlayerRequest
	27, // 57: room.RoomService.StartGame:input_type -> room.StartGameRequest
	29, // 58: room.RoomService.EndGame:input_type -> room.EndGameRequest
	31, // 59: room.RoomService.GetAvailableRooms:input_type -> room.GetAvailableRoomsRequest
	33, // 60: room.RoomService.GetRoomStats:input_type -> room.GetRoomStatsRequest
	36, // 61: room.RoomService.GetRoomActivity:input_type -> room.GetRoomActivityRequest
	40, // 62: room.RoomService.GetRoomInfo:input_type -> room.GetRoomInfoRequest
	9,  // 63: room.RoomService.CreateRoom:output_type -> room.CreateRoomResponse
	11, // 64: room.RoomService.GetRoom:output_type -> room.GetRoomResponse
	13, // 65: room.RoomService.UpdateRoom:output_type -> room.UpdateRoomResponse
	15, // 66: room.RoomService.DeleteRoom:output_type -> room.DeleteRoomResponse
	17, // 67: room.RoomService.ListRooms:output_type -> room.ListRoomsResponse
	20, // 68: room.RoomService.JoinRoom:output_type -> room.JoinRoomResponse
	22, // 69: room.RoomService.LeaveRoom:output_type -> room.LeaveRoomResponse
	24, // 70: room.RoomService.SetPlayerReady:output_type -> room.SetPlayerReadyResponse
	26, // 71: room.RoomService.GetRoomsByPlayer:output_type -> room.GetRoomsByPlayerResponse
	28, // 72: room.RoomService.StartGame:output_type -> room.StartGameResponse
	30, // 73: room.RoomService.EndGame:output_type -> room.EndGameResponse
	32, // 74: room.RoomService.GetAvailableRooms:output_type -> room.GetAvailableRoomsResponse
	34, // 75: room.RoomService.GetRoomStats:output_type -> room.GetRoomStatsResponse
	37, // 76: room.RoomService.GetRoomActivity:output_type -> room.GetRoomActivityResponse
	41, // 77: room.RoomService.GetRoomInfo:output_type -> room.GetRoomInfoResponse
	63, // [63:78] is the sub-list for method output_type
	48, // [48:63] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_proto_room_proto_init() }
func file_proto_room_proto_init() {
	if File_proto_room_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_room_proto_rawDesc), len(file_proto_room_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_room_proto_goTypes,
		DependencyIndexes: file_proto_room_proto_depIdxs,
		EnumInfos:         file_proto_room_proto_enumTypes,
		MessageInfos:      file_proto_room_proto_msgTypes,
	}.Build()
	File_proto_room_proto = out.File
	file_proto_room_proto_goTypes = nil
	file_proto_room_proto_depIdxs = nil
}
