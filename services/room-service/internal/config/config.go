package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

type Config struct {
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`

	MongoURL     string        `json:"mongo_url"`
	DatabaseName string        `json:"database_name"`
	MongoTimeout time.Duration `json:"mongo_timeout"`

	RedisURL      string        `json:"redis_url"`
	RedisPassword string        `json:"redis_password"`
	RedisDB       int           `json:"redis_db"`
	RedisTTL      time.Duration `json:"redis_ttl"`

	JWTSecret   string   `json:"jwt_secret"`
	JWTIssuer   string   `json:"jwt_issuer"`
	JWTAudience []string `json:"jwt_audience"`

	AuthServiceURL    string `json:"auth_service_url"`
	ManagerServiceURL string `json:"manager_service_url"`
	GameServiceURL    string `json:"game_service_url"`

	MaxRoomsPerUser     int           `json:"max_rooms_per_user"`
	RoomInactiveTimeout time.Duration `json:"room_inactive_timeout"`
	MaxPlayersPerRoom   int           `json:"max_players_per_room"`
	DefaultRoomTTL      time.Duration `json:"default_room_ttl"`

	MaxConcurrentRooms int           `json:"max_concurrent_rooms"`
	CacheEnabled       bool          `json:"cache_enabled"`
	CacheTTL           time.Duration `json:"cache_ttl"`

	RequireAuth        bool     `json:"require_auth"`
	AllowedOrigins     []string `json:"allowed_origins"`
	RateLimitEnabled   bool     `json:"rate_limit_enabled"`
	RateLimitPerMinute int      `json:"rate_limit_per_minute"`
}

func Load() (*Config, error) {
	config := &Config{
		Port:        8082,
		Environment: "development",
		LogLevel:    "info",

		MongoURL:     "mongodb://localhost:27017",
		DatabaseName: "xzgame",
		MongoTimeout: 30 * time.Second,

		RedisURL:      "redis://localhost:6379",
		RedisPassword: "",
		RedisDB:       0,
		RedisTTL:      24 * time.Hour,

		JWTSecret:   "your_jwt_secret_here",
		JWTIssuer:   "xzgame-auth-service",
		JWTAudience: []string{"xzgame-api", "xzgame-game-service"},

		AuthServiceURL:    "localhost:8081",
		ManagerServiceURL: "http://localhost:8080",
		GameServiceURL:    "localhost:8083",

		MaxRoomsPerUser:     5,
		RoomInactiveTimeout: 30 * time.Minute,
		MaxPlayersPerRoom:   8,
		DefaultRoomTTL:      24 * time.Hour,

		MaxConcurrentRooms: 1000,
		CacheEnabled:       true,
		CacheTTL:           5 * time.Minute,

		RequireAuth:        true,
		AllowedOrigins:     []string{"*"},
		RateLimitEnabled:   true,
		RateLimitPerMinute: 100,
	}

	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		config.RedisURL = redisURL
	}

	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		config.RedisPassword = redisPassword
	}

	if redisDB := os.Getenv("REDIS_DB"); redisDB != "" {
		if db, err := strconv.Atoi(redisDB); err == nil {
			config.RedisDB = db
		}
	}

	if mongoURL := os.Getenv("MONGO_URL"); mongoURL != "" {
		config.MongoURL = mongoURL
	}

	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.JWTSecret = jwtSecret
	}

	if jwtIssuer := os.Getenv("JWT_ISSUER"); jwtIssuer != "" {
		config.JWTIssuer = jwtIssuer
	}

	if jwtAudience := os.Getenv("JWT_AUDIENCE"); jwtAudience != "" {
		config.JWTAudience = strings.Split(jwtAudience, ",")
	}

	return config, nil
}

func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}
	return nil
}

func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}
