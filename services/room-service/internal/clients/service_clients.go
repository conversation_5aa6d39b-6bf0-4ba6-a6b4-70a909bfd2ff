package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/internal/services"
)

// parseFloatFromInterface converts interface{} to float64, handling both string and numeric types
func parseFloatFromInterface(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

// HTTPGameServiceClient implements GameServiceClient using HTTP calls
type HTTPGameServiceClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewHTTPGameServiceClient creates a new HTTP game service client
func NewHTTPGameServiceClient(baseURL string, logger *logrus.Logger) *HTTPGameServiceClient {
	return &HTTPGameServiceClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		logger: logger,
	}
}

// JoinRoom calls the game service to join a room
func (c *HTTPGameServiceClient) JoinRoom(ctx context.Context, request *services.JoinRoomRequest) (*services.JoinOperationResult, error) {
	// For now, return a mock result since we're focusing on architecture
	// In a real implementation, this would make an HTTP call to game-service
	c.logger.WithFields(logrus.Fields{
		"userId": request.UserID,
		"roomId": request.RoomID,
	}).Info("Game service join room called (mock)")

	return &services.JoinOperationResult{
		// Mock result - in real implementation, this would come from game service
	}, nil
}

// LeaveRoom calls the game service to leave a room
func (c *HTTPGameServiceClient) LeaveRoom(ctx context.Context, request *services.LeaveRoomRequest) (*services.LeaveOperationResult, error) {
	// Mock implementation
	c.logger.WithFields(logrus.Fields{
		"userId": request.UserID,
		"roomId": request.RoomID,
	}).Info("Game service leave room called (mock)")

	return &services.LeaveOperationResult{
		// Mock result
	}, nil
}

// HTTPManagerServiceClient implements ManagerServiceClient using HTTP calls
type HTTPManagerServiceClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *logrus.Logger
	jwtSecret  string
}

// NewHTTPManagerServiceClient creates a new HTTP manager service client
func NewHTTPManagerServiceClient(baseURL string, logger *logrus.Logger) *HTTPManagerServiceClient {
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "dev_jwt_secret_key_change_in_production" // Default for development
	}

	return &HTTPManagerServiceClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		logger:    logger,
		jwtSecret: jwtSecret,
	}
}

// generateServiceToken generates a JWT token for service-to-service communication
func (c *HTTPManagerServiceClient) generateServiceToken() (string, error) {
	claims := jwt.MapClaims{
		"service": "room-service",
		"iat":     time.Now().Unix(),
		"exp":     time.Now().Add(5 * time.Minute).Unix(), // 5 minute expiration
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(c.jwtSecret))
}

// addAuthHeader adds JWT authentication header to the request
func (c *HTTPManagerServiceClient) addAuthHeader(req *http.Request) error {
	token, err := c.generateServiceToken()
	if err != nil {
		return fmt.Errorf("failed to generate service token: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	return nil
}

// GetUserBalance gets user balance from manager service
func (c *HTTPManagerServiceClient) GetUserBalance(ctx context.Context, userID string) (float64, error) {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/users/%s/validate_balance", c.baseURL, userID)

	requestBody := map[string]interface{}{
		"bet_amount": 1, // Minimal amount just to get balance info
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return 0, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			CurrentBalance interface{} `json:"current_balance"`
			Balance        interface{} `json:"balance"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return 0, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return 0, fmt.Errorf("manager service returned error")
	}

	// Parse balance from either current_balance or balance field
	var balance float64
	var parseErr error

	if response.Data.CurrentBalance != nil {
		balance, parseErr = parseFloatFromInterface(response.Data.CurrentBalance)
	} else if response.Data.Balance != nil {
		balance, parseErr = parseFloatFromInterface(response.Data.Balance)
	} else {
		return 0, fmt.Errorf("no balance field found in response")
	}

	if parseErr != nil {
		return 0, fmt.Errorf("failed to parse balance: %w", parseErr)
	}

	return balance, nil
}

// GetUserStatus gets user status from manager service
func (c *HTTPManagerServiceClient) GetUserStatus(ctx context.Context, userID string) (*services.UserStatus, error) {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/users/%s/info", c.baseURL, userID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			Status string `json:"status"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("manager service returned error")
	}

	// Map status to boolean flags
	userStatus := &services.UserStatus{
		Banned:    response.Data.Status == "banned",
		Suspended: response.Data.Status == "suspended",
	}

	return userStatus, nil
}

// GetUserSession gets user session from manager service
func (c *HTTPManagerServiceClient) GetUserSession(ctx context.Context, userID, roomID string) (*services.UserSession, error) {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/users/%s/sessions", c.baseURL, userID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, nil // No sessions found
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			GameSessions []struct {
				ID        string `json:"id"`
				SessionID string `json:"session_id"`
				GameType  string `json:"game_type"`
				Status    string `json:"status"`
				StartedAt string `json:"started_at"`
				EndedAt   string `json:"ended_at"`
			} `json:"game_sessions"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("manager service returned error")
	}

	// Look for an active session for this room
	for _, session := range response.Data.GameSessions {
		if session.Status == "active" && session.SessionID == roomID {
			return &services.UserSession{
				Active: true,
				RoomID: roomID,
			}, nil
		}
	}

	// No active session found for this room
	return &services.UserSession{
		Active: false,
		RoomID: roomID,
	}, nil
}

// UpdateUserBalance updates user balance in manager service
func (c *HTTPManagerServiceClient) UpdateUserBalance(ctx context.Context, userID string, amount float64, operation string) (*services.BalanceUpdateResult, error) {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/users/%s/update_balance", c.baseURL, userID)

	requestBody := map[string]interface{}{
		"amount":      amount,
		"operation":   operation,
		"description": fmt.Sprintf("Room operation: %s", operation),
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Add authentication header
	if err := c.addAuthHeader(req); err != nil {
		return nil, fmt.Errorf("failed to add auth header: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			NewBalance interface{} `json:"new_balance"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("manager service returned error")
	}

	// Parse new balance
	newBalance, err := parseFloatFromInterface(response.Data.NewBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to parse new balance: %w", err)
	}

	return &services.BalanceUpdateResult{
		NewBalance: newBalance,
	}, nil
}

// RefundUserBalance refunds user balance in manager service
func (c *HTTPManagerServiceClient) RefundUserBalance(ctx context.Context, userID string, amount float64, reason string) (*services.BalanceUpdateResult, error) {
	// Use the same balance update endpoint with positive amount for refund
	return c.UpdateUserBalance(ctx, userID, amount, "refund")
}

// GetRoom gets room information from manager service
func (c *HTTPManagerServiceClient) GetRoom(ctx context.Context, roomID string) (*models.Room, error) {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/rooms/%s", c.baseURL, roomID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, nil // Room not found
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			Room struct {
				ID             string `json:"id"`
				Name           string `json:"name"`
				GameType       string `json:"game_type"`
				Status         string `json:"status"`
				CurrentPlayers int    `json:"current_players"`
				MaxPlayers     int    `json:"max_players"`
				BetAmount      string `json:"bet_amount"`
				Currency       string `json:"currency"`
				IsPrivate      bool   `json:"is_private"`
				CreatedAt      string `json:"created_at"`
				UpdatedAt      string `json:"updated_at"`
			} `json:"room"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("manager service returned error")
	}

	// Parse bet amount
	betAmount, err := parseFloatFromInterface(response.Data.Room.BetAmount)
	if err != nil {
		return nil, fmt.Errorf("failed to parse bet amount: %w", err)
	}

	// Convert string ID to ObjectID
	objectID, err := primitive.ObjectIDFromHex(response.Data.Room.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse room ID: %w", err)
	}

	// Convert to room model
	room := &models.Room{
		ID:             objectID,
		Name:           response.Data.Room.Name,
		GameType:       models.GameType(response.Data.Room.GameType),
		Status:         models.RoomStatus(response.Data.Room.Status),
		CurrentPlayers: response.Data.Room.CurrentPlayers,
		MaxPlayers:     response.Data.Room.MaxPlayers,
		BetAmount:      int64(betAmount),
		Configuration: models.RoomConfiguration{
			IsPrivate: response.Data.Room.IsPrivate,
		},
		Players: make([]models.RoomPlayer, response.Data.Room.CurrentPlayers), // Create placeholder players
	}

	return room, nil
}

// JoinRoom joins a room via manager service
func (c *HTTPManagerServiceClient) JoinRoom(ctx context.Context, userID, username, roomID string, betAmount float64) error {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/rooms/%s/join", c.baseURL, roomID)

	requestBody := map[string]interface{}{
		"user_id":    userID,
		"username":   username,
		"bet_amount": betAmount,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("manager service returned error")
	}

	return nil
}

// LeaveRoom leaves a room via manager service
func (c *HTTPManagerServiceClient) LeaveRoom(ctx context.Context, userID, roomID string) error {
	// Use game service endpoint that doesn't require authentication
	url := fmt.Sprintf("%s/game_service/rooms/%s/leave", c.baseURL, roomID)

	requestBody := map[string]interface{}{
		"user_id": userID,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	var response struct {
		Success bool `json:"success"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("manager service returned error")
	}

	return nil
}

// HTTPNotificationClient implements NotificationClient using Redis
type HTTPNotificationClient struct {
	redisClient RedisClientInterface
	logger      *logrus.Logger
}

// RedisClientInterface defines the interface for Redis operations needed by notification client
type RedisClientInterface interface {
	Publish(ctx context.Context, channel string, message []byte) error
}

// NewHTTPNotificationClient creates a new Redis-based notification client
func NewHTTPNotificationClient(redisClient RedisClientInterface, logger *logrus.Logger) *HTTPNotificationClient {
	return &HTTPNotificationClient{
		redisClient: redisClient,
		logger:      logger,
	}
}

// SendJoinNotification sends a join notification
func (c *HTTPNotificationClient) SendJoinNotification(ctx context.Context, userID, username, roomID string, room *models.Room) error {
	// Send real notification via Redis to socket-gateway
	notification := map[string]interface{}{
		"event": map[string]interface{}{
			"type": "player_joined_room",
			"payload": map[string]interface{}{
				"userId":   userID,
				"username": username,
				"roomId":   roomID,
				"room": map[string]interface{}{
					"id":             room.ID,
					"name":           room.Name,
					"gameType":       room.GameType,
					"status":         room.Status,
					"currentPlayers": room.CurrentPlayers,
					"maxPlayers":     room.MaxPlayers,
					"betAmount":      room.BetAmount,
				},
				"timestamp": time.Now().UTC().Format(time.RFC3339),
			},
		},
		"metadata": map[string]interface{}{
			"serviceId":     "room-service",
			"version":       "1.0.0",
			"correlationId": fmt.Sprintf("join-%s-%d", userID, time.Now().UnixNano()),
			"priority":      1,
		},
	}

	// Convert to JSON
	notificationJSON, err := json.Marshal(notification)
	if err != nil {
		c.logger.WithError(err).Error("Failed to marshal join notification")
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	// Publish to Redis channel that socket-gateway listens to
	channel := fmt.Sprintf("room:%s:events", roomID)

	// Use Redis client to publish the notification
	err = c.redisClient.Publish(ctx, channel, notificationJSON)
	if err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"userId":   userID,
			"username": username,
			"roomId":   roomID,
			"channel":  channel,
		}).Error("Failed to send join notification via Redis")
		return fmt.Errorf("failed to send notification: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"userId":   userID,
		"username": username,
		"roomId":   roomID,
		"channel":  channel,
	}).Info("Join notification sent successfully via Redis")

	return nil
}

// SendLeaveNotification sends a leave notification
func (c *HTTPNotificationClient) SendLeaveNotification(ctx context.Context, userID, username, roomID string, room *models.Room) error {
	// Send real notification via Redis to socket-gateway
	notification := map[string]interface{}{
		"event": map[string]interface{}{
			"type": "player_left_room",
			"payload": map[string]interface{}{
				"userId":   userID,
				"username": username,
				"roomId":   roomID,
				"room": map[string]interface{}{
					"id":             room.ID,
					"name":           room.Name,
					"gameType":       room.GameType,
					"status":         room.Status,
					"currentPlayers": room.CurrentPlayers,
					"maxPlayers":     room.MaxPlayers,
					"betAmount":      room.BetAmount,
				},
				"timestamp": time.Now().UTC().Format(time.RFC3339),
			},
		},
		"metadata": map[string]interface{}{
			"serviceId":     "room-service",
			"version":       "1.0.0",
			"correlationId": fmt.Sprintf("leave-%s-%d", userID, time.Now().UnixNano()),
			"priority":      1,
		},
	}

	// Convert to JSON
	notificationJSON, err := json.Marshal(notification)
	if err != nil {
		c.logger.WithError(err).Error("Failed to marshal leave notification")
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	// Publish to Redis channel that socket-gateway listens to
	channel := fmt.Sprintf("room:%s:events", roomID)

	// Use Redis client to publish the notification
	err = c.redisClient.Publish(ctx, channel, notificationJSON)
	if err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"userId":   userID,
			"username": username,
			"roomId":   roomID,
			"channel":  channel,
		}).Error("Failed to send leave notification via Redis")
		return fmt.Errorf("failed to send notification: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"userId":   userID,
		"username": username,
		"roomId":   roomID,
		"channel":  channel,
	}).Info("Leave notification sent successfully via Redis")

	return nil
}
