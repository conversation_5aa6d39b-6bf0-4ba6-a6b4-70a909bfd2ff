package clients

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/xzgame/room-service/internal/services"
	managerpb "github.com/xzgame/room-service/proto/manager"
)

// GRPCManagerServiceClient implements ManagerServiceClient using gRPC
type GRPCManagerServiceClient struct {
	client managerpb.ManagerServiceClient
	conn   *grpc.ClientConn
	logger *logrus.Logger
}

// NewGRPCManagerServiceClient creates a new gRPC manager service client
func NewGRPCManagerServiceClient(address string, logger *logrus.Logger) (*GRPCManagerServiceClient, error) {
	// Set up connection with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := grpc.DialContext(ctx, address, 
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to manager service at %s: %w", address, err)
	}

	client := managerpb.NewManagerServiceClient(conn)

	return &GRPCManagerServiceClient{
		client: client,
		conn:   conn,
		logger: logger,
	}, nil
}

// Close closes the gRPC connection
func (c *GRPCManagerServiceClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// GetUserBalance gets user balance from manager service via gRPC
func (c *GRPCManagerServiceClient) GetUserBalance(ctx context.Context, userID string) (float64, error) {
	req := &managerpb.GetUserBalanceRequest{
		UserId: userID,
	}

	resp, err := c.client.GetUserBalance(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithField("userId", userID).Error("Failed to get user balance via gRPC")
		return 0, fmt.Errorf("failed to get user balance: %w", err)
	}

	if !resp.Success {
		if resp.Error != nil {
			return 0, fmt.Errorf("manager service error: %s", resp.Error.Message)
		}
		return 0, fmt.Errorf("manager service returned unsuccessful response")
	}

	if resp.Balance == nil {
		return 0, fmt.Errorf("manager service returned nil balance")
	}

	c.logger.WithFields(logrus.Fields{
		"userId":  userID,
		"balance": resp.Balance.CurrentBalance,
	}).Debug("Successfully retrieved user balance via gRPC")

	return resp.Balance.CurrentBalance, nil
}

// GetUserStatus gets user status from manager service via gRPC
func (c *GRPCManagerServiceClient) GetUserStatus(ctx context.Context, userID string) (*services.UserStatus, error) {
	req := &managerpb.GetUserRequest{
		UserId: userID,
	}

	resp, err := c.client.GetUser(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithField("userId", userID).Error("Failed to get user via gRPC")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if !resp.Success {
		if resp.Error != nil {
			return nil, fmt.Errorf("manager service error: %s", resp.Error.Message)
		}
		return nil, fmt.Errorf("manager service returned unsuccessful response")
	}

	if resp.User == nil || resp.User.Status == nil {
		return nil, fmt.Errorf("manager service returned nil user or status")
	}

	userStatus := &services.UserStatus{
		Banned:    resp.User.Status.Banned,
		Suspended: resp.User.Status.Suspended,
	}

	c.logger.WithFields(logrus.Fields{
		"userId":    userID,
		"banned":    userStatus.Banned,
		"suspended": userStatus.Suspended,
	}).Debug("Successfully retrieved user status via gRPC")

	return userStatus, nil
}

// GetUserSession gets user session from manager service via gRPC
func (c *GRPCManagerServiceClient) GetUserSession(ctx context.Context, userID, roomID string) (*services.UserSession, error) {
	req := &managerpb.GetUserSessionRequest{
		UserId: userID,
		RoomId: roomID,
	}

	resp, err := c.client.GetUserSession(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"userId": userID,
			"roomId": roomID,
		}).Error("Failed to get user session via gRPC")
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}

	if !resp.Success {
		if resp.Error != nil {
			return nil, fmt.Errorf("manager service error: %s", resp.Error.Message)
		}
		return nil, fmt.Errorf("manager service returned unsuccessful response")
	}

	if resp.Session == nil {
		// No active session found
		return &services.UserSession{
			Active: false,
			RoomID: roomID,
		}, nil
	}

	userSession := &services.UserSession{
		Active: resp.Session.Active,
		RoomID: resp.Session.RoomId,
	}

	c.logger.WithFields(logrus.Fields{
		"userId": userID,
		"roomId": roomID,
		"active": userSession.Active,
	}).Debug("Successfully retrieved user session via gRPC")

	return userSession, nil
}

// UpdateUserBalance updates user balance in manager service via gRPC
func (c *GRPCManagerServiceClient) UpdateUserBalance(ctx context.Context, userID string, amount float64, operation string) (*services.BalanceUpdateResult, error) {
	req := &managerpb.UpdateBalanceRequest{
		UserId: userID,
		Amount: amount,
		Reason: operation,
		Metadata: map[string]string{
			"operation": operation,
			"service":   "room-service",
		},
	}

	resp, err := c.client.UpdateBalance(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"userId":    userID,
			"amount":    amount,
			"operation": operation,
		}).Error("Failed to update user balance via gRPC")
		return nil, fmt.Errorf("failed to update user balance: %w", err)
	}

	if !resp.Success {
		if resp.Error != nil {
			return nil, fmt.Errorf("manager service error: %s", resp.Error.Message)
		}
		return nil, fmt.Errorf("manager service returned unsuccessful response")
	}

	if resp.Balance == nil {
		return nil, fmt.Errorf("manager service returned nil balance")
	}

	result := &services.BalanceUpdateResult{
		NewBalance: resp.Balance.CurrentBalance,
	}

	c.logger.WithFields(logrus.Fields{
		"userId":     userID,
		"amount":     amount,
		"operation":  operation,
		"newBalance": result.NewBalance,
	}).Debug("Successfully updated user balance via gRPC")

	return result, nil
}

// RefundUserBalance refunds user balance in manager service via gRPC
func (c *GRPCManagerServiceClient) RefundUserBalance(ctx context.Context, userID string, amount float64, reason string) (*services.BalanceUpdateResult, error) {
	// Use the same balance update method with positive amount for refund
	return c.UpdateUserBalance(ctx, userID, amount, "refund")
}
