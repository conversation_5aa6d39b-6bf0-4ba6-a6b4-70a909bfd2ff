package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/internal/repositories"
	"github.com/xzgame/room-service/pkg/redis"
)

// RoomManager handles core room management operations
type RoomManager struct {
	roomRepo    repositories.RoomRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
}

// NewRoomManager creates a new room manager instance
func NewRoomManager(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
) *RoomManager {
	return &RoomManager{
		roomRepo:    roomRepo,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
	}
}

// CreateRoom creates a new room
func (rm *RoomManager) CreateRoom(ctx context.Context, request *models.CreateRoomRequest) (*models.Room, error) {
	rm.logger.WithFields(logrus.Fields{
		"name":      request.Name,
		"game_type": request.GameType,
		"creator":   request.CreatedBy,
	}).Info("Creating room")

	// Validate request
	if err := rm.validateCreateRoomRequest(request); err != nil {
		return nil, err
	}

	room := &models.Room{
		Name:           request.Name,
		GameType:       request.GameType,
		Status:         models.RoomStatusWaiting,
		CurrentPlayers: 0,
		MaxPlayers:     request.MaxPlayers,
		MinPlayers:     request.MinPlayers,
		BetAmount:      request.BetLimits.MinBet,
		Players:        make([]models.RoomPlayer, 0),
		Configuration: models.RoomConfiguration{
			GameType:     request.GameType,
			BetLimits:    request.BetLimits,
			AutoStart:    request.AutoStart,
			IsPrivate:    request.IsPrivate,
			Password:     request.Password,
			GameSpecific: request.GameSpecific,
		},
		LastActivity: time.Now(),
		CreatedBy:    request.CreatedBy,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err := rm.roomRepo.CreateRoom(ctx, room)
	if err != nil {
		rm.logger.WithError(err).Error("Failed to create room")
		return nil, err
	}

	// Cache the room
	if err := rm.cacheRoom(ctx, room); err != nil {
		rm.logger.WithError(err).Warn("Failed to cache room")
	}

	rm.logger.WithField("room_id", room.ID.Hex()).Info("Room created successfully")
	return room, nil
}

// GetRoom retrieves a room by ID
func (rm *RoomManager) GetRoom(ctx context.Context, roomID string, forceRefresh bool) (*models.Room, error) {
	rm.logger.WithFields(logrus.Fields{
		"room_id":       roomID,
		"force_refresh": forceRefresh,
	}).Debug("Getting room")

	// Validate room ID
	if err := rm.validateRoomID(roomID); err != nil {
		return nil, err
	}

	// Try cache first unless force refresh is requested
	if !forceRefresh {
		if room, err := rm.getRoomFromCache(ctx, roomID); err == nil {
			return room, nil
		}
	}

	// Get from database
	room, err := rm.roomRepo.GetRoom(ctx, roomID)
	if err != nil {
		rm.logger.WithError(err).WithField("room_id", roomID).Error("Failed to get room from database")
		return nil, err
	}

	// Cache the room for future requests
	if err := rm.cacheRoom(ctx, room); err != nil {
		rm.logger.WithError(err).Warn("Failed to cache room")
	}

	return room, nil
}

// UpdateRoom updates a room
func (rm *RoomManager) UpdateRoom(ctx context.Context, roomID string, updates *models.UpdateRoomRequest) (*models.Room, error) {
	rm.logger.WithField("room_id", roomID).Info("Updating room")

	room, err := rm.GetRoom(ctx, roomID, false)
	if err != nil {
		return nil, err
	}

	// Build update map
	updateMap := make(map[string]interface{})

	if updates.Name != nil {
		updateMap["name"] = *updates.Name
	}

	if updates.MaxPlayers != nil {
		if *updates.MaxPlayers < room.CurrentPlayers {
			return nil, models.NewRoomError(
				models.ErrorCodeInvalidInput,
				"cannot reduce max players below current player count",
				roomID,
			)
		}
		updateMap["max_players"] = *updates.MaxPlayers
	}

	if updates.BetLimits != nil {
		updateMap["configuration.bet_limits"] = *updates.BetLimits
	}

	if updates.AutoStart != nil {
		updateMap["configuration.auto_start"] = *updates.AutoStart
	}

	if updates.GameSpecific != nil {
		updateMap["configuration.game_specific"] = updates.GameSpecific
	}

	updateMap["updated_at"] = time.Now()

	err = rm.roomRepo.UpdateRoom(ctx, roomID, updateMap)
	if err != nil {
		rm.logger.WithError(err).Error("Failed to update room")
		return nil, err
	}

	// Invalidate cache
	rm.invalidateRoomCache(ctx, roomID)

	// Get updated room
	updatedRoom, err := rm.GetRoom(ctx, roomID, true)
	if err != nil {
		return nil, err
	}

	rm.logger.WithField("room_id", roomID).Info("Room updated successfully")
	return updatedRoom, nil
}

// DeleteRoom deletes a room
func (rm *RoomManager) DeleteRoom(ctx context.Context, roomID string, adminUserID string) error {
	rm.logger.WithFields(logrus.Fields{
		"room_id":       roomID,
		"admin_user_id": adminUserID,
	}).Info("Deleting room")

	room, err := rm.GetRoom(ctx, roomID, false)
	if err != nil {
		return err
	}

	// Check if room can be deleted
	if room.Status == models.RoomStatusActive {
		return models.NewRoomError(
			models.ErrorCodeRoomAlreadyActive,
			"cannot delete active room",
			roomID,
		)
	}

	err = rm.roomRepo.DeleteRoom(ctx, roomID)
	if err != nil {
		rm.logger.WithError(err).Error("Failed to delete room")
		return err
	}

	// Remove from cache
	rm.invalidateRoomCache(ctx, roomID)

	// Clean up related cache entries
	rm.cleanupRoomCache(ctx, roomID)

	rm.logger.WithField("room_id", roomID).Info("Room deleted successfully")
	return nil
}

// ListRooms lists rooms with filtering and pagination
func (rm *RoomManager) ListRooms(ctx context.Context, filter *models.RoomListFilter) (*models.RoomListResponse, error) {
	rooms, totalCount, err := rm.roomRepo.ListRooms(ctx, *filter)
	if err != nil {
		return nil, err
	}

	// Calculate pagination
	totalPages := int(float64(totalCount)/float64(filter.Limit) + 0.5)

	response := &models.RoomListResponse{
		Rooms: make([]models.Room, len(rooms)),
		Pagination: models.Pagination{
			CurrentPage: filter.Page,
			PerPage:     filter.Limit,
			TotalCount:  totalCount,
			TotalPages:  totalPages,
		},
	}

	// Convert pointers to values
	for i, room := range rooms {
		response.Rooms[i] = *room
	}

	return response, nil
}

// GetAvailableRooms gets available rooms for a game type
func (rm *RoomManager) GetAvailableRooms(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	filter := &models.RoomListFilter{
		GameType: &gameType,
		HasSpace: true,
		Page:     0,
		Limit:    50,
	}

	rooms, _, err := rm.roomRepo.ListRooms(ctx, *filter)
	return rooms, err
}

// Helper methods

// validateCreateRoomRequest validates a create room request
func (rm *RoomManager) validateCreateRoomRequest(request *models.CreateRoomRequest) error {
	if request.Name == "" {
		return models.NewValidationError("name", "room name cannot be empty", request.Name)
	}
	if request.MaxPlayers <= 0 {
		return models.NewValidationError("maxPlayers", "max players must be positive", request.MaxPlayers)
	}
	if request.MinPlayers <= 0 {
		return models.NewValidationError("minPlayers", "min players must be positive", request.MinPlayers)
	}
	if request.MinPlayers > request.MaxPlayers {
		return models.NewValidationError("minPlayers", "min players cannot exceed max players", request.MinPlayers)
	}
	if request.BetLimits.MinBet <= 0 {
		return models.NewValidationError("betLimits.minBet", "minimum bet must be positive", request.BetLimits.MinBet)
	}
	if request.BetLimits.MaxBet < request.BetLimits.MinBet {
		return models.NewValidationError("betLimits.maxBet", "maximum bet cannot be less than minimum bet", request.BetLimits.MaxBet)
	}
	return nil
}

// validateRoomID validates a room ID
func (rm *RoomManager) validateRoomID(roomID string) error {
	if roomID == "" {
		return models.NewValidationError("roomId", "room ID cannot be empty", roomID)
	}
	return nil
}

// cacheRoom caches a room in Redis
func (rm *RoomManager) cacheRoom(ctx context.Context, room *models.Room) error {
	roomKey := fmt.Sprintf("room:%s", room.ID.Hex())
	return rm.redisClient.Set(ctx, roomKey, room, rm.config.RedisTTL)
}

// getRoomFromCache retrieves a room from Redis cache
func (rm *RoomManager) getRoomFromCache(ctx context.Context, roomID string) (*models.Room, error) {
	roomKey := fmt.Sprintf("room:%s", roomID)
	var cachedRoom models.Room

	if err := rm.redisClient.Get(ctx, roomKey, &cachedRoom); err != nil {
		return nil, err
	}

	rm.logger.WithField("room_id", roomID).Debug("Room found in cache")
	return &cachedRoom, nil
}

// invalidateRoomCache removes a room from cache
func (rm *RoomManager) invalidateRoomCache(ctx context.Context, roomID string) {
	roomKey := fmt.Sprintf("room:%s", roomID)
	rm.redisClient.Delete(ctx, roomKey)
}

// cleanupRoomCache cleans up all cache entries related to a room
func (rm *RoomManager) cleanupRoomCache(ctx context.Context, roomID string) {
	// Clean up color selections cache
	colorSelectionsKey := fmt.Sprintf("room:%s:color_selections", roomID)
	if err := rm.redisClient.Delete(ctx, colorSelectionsKey); err != nil {
		rm.logger.WithError(err).WithField("room_id", roomID).Warn("Failed to clean up color selections cache")
	}

	// Clean up position selections cache
	positionSelectionsKey := fmt.Sprintf("room:%s:position_selections", roomID)
	if err := rm.redisClient.Delete(ctx, positionSelectionsKey); err != nil {
		rm.logger.WithError(err).WithField("room_id", roomID).Warn("Failed to clean up position selections cache")
	}
}
