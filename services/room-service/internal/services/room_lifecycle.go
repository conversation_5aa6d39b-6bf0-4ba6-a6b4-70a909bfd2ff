package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/internal/repositories"
	"github.com/xzgame/room-service/pkg/redis"
)

// RoomLifecycle handles room lifecycle operations
type RoomLifecycle struct {
	roomRepo    repositories.RoomRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
	roomManager *RoomManager
	validator   *RoomValidator
}

// NewRoomLifecycle creates a new room lifecycle instance
func NewRoomLifecycle(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
	roomManager *RoomManager,
	validator *RoomValidator,
) *RoomLifecycle {
	return &RoomLifecycle{
		roomRepo:    roomRepo,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
		roomManager: roomManager,
		validator:   validator,
	}
}

// StartGame starts a game in a room
func (rl *RoomLifecycle) StartGame(ctx context.Context, request *models.StartGameRequest) (*models.StartGameResponse, error) {
	rl.logger.WithFields(logrus.Fields{
		"room_id":      request.RoomID,
		"initiated_by": request.InitiatedBy,
	}).Info("Starting game in room")

	// Get room
	room, err := rl.roomManager.GetRoom(ctx, request.RoomID, false)
	if err != nil {
		return nil, err
	}

	// Validate game start
	if err := rl.validator.ValidateGameStart(room); err != nil {
		return nil, err
	}

	// Update room status to active
	room.Status = models.RoomStatusActive
	room.LastActivity = time.Now()
	room.UpdatedAt = time.Now()

	// Generate session ID
	sessionID := rl.generateSessionID(request.RoomID)
	room.CurrentSession = sessionID

	// Update all players to playing status
	for i := range room.Players {
		room.Players[i].Status = models.PlayerStatusActive
	}

	// Save to database
	updateMap := map[string]interface{}{
		"status":          room.Status,
		"current_session": room.CurrentSession,
		"players":         room.Players,
		"last_activity":   room.LastActivity,
		"updated_at":      room.UpdatedAt,
	}

	err = rl.roomRepo.UpdateRoom(ctx, request.RoomID, updateMap)
	if err != nil {
		rl.logger.WithError(err).Error("Failed to update room for game start")
		return nil, err
	}

	// Update cache
	if err := rl.roomManager.cacheRoom(ctx, room); err != nil {
		rl.logger.WithError(err).Warn("Failed to cache room after game start")
	}

	rl.logger.WithFields(logrus.Fields{
		"room_id":    request.RoomID,
		"session_id": sessionID,
	}).Info("Game started successfully")

	return &models.StartGameResponse{
		Room:      room,
		SessionID: sessionID,
	}, nil
}

// EndGame ends a game in a room
func (rl *RoomLifecycle) EndGame(ctx context.Context, request *models.EndGameRequest) (*models.Room, error) {
	rl.logger.WithFields(logrus.Fields{
		"room_id":    request.RoomID,
		"session_id": request.SessionID,
	}).Info("Ending game in room")

	// Get room
	room, err := rl.roomManager.GetRoom(ctx, request.RoomID, false)
	if err != nil {
		return nil, err
	}

	// Validate that the session matches
	if room.CurrentSession != request.SessionID {
		return nil, models.NewRoomError(
			models.ErrorCodeInvalidSession,
			"session ID does not match current room session",
			request.RoomID,
		)
	}

	// Update room status back to waiting
	room.Status = models.RoomStatusWaiting
	room.CurrentSession = ""
	room.LastActivity = time.Now()
	room.UpdatedAt = time.Now()

	// Reset all players to not ready
	for i := range room.Players {
		room.Players[i].IsReady = false
		room.Players[i].Status = models.PlayerStatusActive
	}

	// Save to database
	updateMap := map[string]interface{}{
		"status":          room.Status,
		"current_session": room.CurrentSession,
		"players":         room.Players,
		"last_activity":   room.LastActivity,
		"updated_at":      room.UpdatedAt,
	}

	err = rl.roomRepo.UpdateRoom(ctx, request.RoomID, updateMap)
	if err != nil {
		rl.logger.WithError(err).Error("Failed to update room for game end")
		return nil, err
	}

	// Update cache
	if err := rl.roomManager.cacheRoom(ctx, room); err != nil {
		rl.logger.WithError(err).Warn("Failed to cache room after game end")
	}

	rl.logger.WithField("room_id", request.RoomID).Info("Game ended successfully")
	return room, nil
}

// ArchiveRoom archives a room
func (rl *RoomLifecycle) ArchiveRoom(ctx context.Context, roomID string, reason string) error {
	rl.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"reason":  reason,
	}).Info("Archiving room")

	// Get room
	room, err := rl.roomManager.GetRoom(ctx, roomID, false)
	if err != nil {
		return err
	}

	// Check if room can be archived using transition validation
	if !rl.CanTransitionToStatus(room.Status, models.RoomStatusArchived) {
		return models.NewRoomError(
			models.ErrorCodeInvalidRoomState,
			fmt.Sprintf("cannot transition from %s to archived", room.Status),
			roomID,
		)
	}

	// Update room status to archived
	updateMap := map[string]interface{}{
		"status":        models.RoomStatusArchived,
		"last_activity": time.Now(),
		"updated_at":    time.Now(),
	}

	err = rl.roomRepo.UpdateRoom(ctx, roomID, updateMap)
	if err != nil {
		rl.logger.WithError(err).Error("Failed to archive room")
		return err
	}

	// Remove from cache
	rl.roomManager.invalidateRoomCache(ctx, roomID)

	rl.logger.WithField("room_id", roomID).Info("Room archived successfully")
	return nil
}

// CloseRoom closes a room
func (rl *RoomLifecycle) CloseRoom(ctx context.Context, roomID string, reason string) error {
	rl.logger.WithFields(logrus.Fields{
		"room_id": roomID,
		"reason":  reason,
	}).Info("Closing room")

	// Get room
	room, err := rl.roomManager.GetRoom(ctx, roomID, false)
	if err != nil {
		return err
	}

	// Check if room can be closed using transition validation
	if !rl.CanTransitionToStatus(room.Status, models.RoomStatusClosed) {
		return models.NewRoomError(
			models.ErrorCodeInvalidRoomState,
			fmt.Sprintf("cannot transition from %s to closed", room.Status),
			roomID,
		)
	}

	// Update room status to closed
	updateMap := map[string]interface{}{
		"status":        models.RoomStatusClosed,
		"last_activity": time.Now(),
		"updated_at":    time.Now(),
	}

	err = rl.roomRepo.UpdateRoom(ctx, roomID, updateMap)
	if err != nil {
		rl.logger.WithError(err).Error("Failed to close room")
		return err
	}

	// Update cache
	if err := rl.roomManager.cacheRoom(ctx, room); err != nil {
		rl.logger.WithError(err).Warn("Failed to cache room after closing")
	}

	rl.logger.WithField("room_id", roomID).Info("Room closed successfully")
	return nil
}

// CleanupInactiveRooms cleans up rooms that have been inactive for too long
func (rl *RoomLifecycle) CleanupInactiveRooms(ctx context.Context) error {
	rl.logger.Info("Starting cleanup of inactive rooms")

	// Define inactivity threshold (e.g., 24 hours)
	inactivityThreshold := time.Now().Add(-24 * time.Hour)

	// Get inactive rooms
	waitingStatus := models.RoomStatusWaiting
	filter := &models.RoomListFilter{
		Status:             &waitingStatus,
		LastActivityBefore: &inactivityThreshold,
		Page:               0,
		Limit:              100,
	}

	rooms, _, err := rl.roomRepo.ListRooms(ctx, *filter)
	if err != nil {
		rl.logger.WithError(err).Error("Failed to get inactive rooms")
		return err
	}

	cleanedCount := 0
	inactivityDuration := 24 * time.Hour
	for _, room := range rooms {
		// Archive empty rooms that are also inactive
		if room.CurrentPlayers == 0 && rl.IsRoomInactive(room, inactivityDuration) {
			if err := rl.ArchiveRoom(ctx, room.ID.Hex(), "inactive_cleanup"); err != nil {
				rl.logger.WithError(err).WithField("room_id", room.ID.Hex()).Warn("Failed to archive inactive room")
			} else {
				cleanedCount++
			}
		}
	}

	rl.logger.WithField("cleaned_count", cleanedCount).Info("Inactive room cleanup completed")
	return nil
}

// GetRoomStats gets statistics for a room
func (rl *RoomLifecycle) GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error) {
	// Get room
	room, err := rl.roomManager.GetRoom(ctx, roomID, false)
	if err != nil {
		return nil, err
	}

	// Calculate basic stats
	stats := &models.RoomStats{
		RoomID:       roomID,
		TotalPlayers: room.CurrentPlayers,
		CreatedAt:    room.CreatedAt,
		LastActivity: room.LastActivity,
	}

	// Get additional stats from database if needed
	// This could include game history, total games played, etc.

	return stats, nil
}

// GetRoomActivity gets recent activity for a room
func (rl *RoomLifecycle) GetRoomActivity(ctx context.Context, roomID string) (*models.RoomActivity, error) {
	// Get room
	room, err := rl.roomManager.GetRoom(ctx, roomID, false)
	if err != nil {
		return nil, err
	}

	// Build activity response
	activity := &models.RoomActivity{
		RoomID:    roomID,
		Players:   room.Players,
		UpdatedAt: room.UpdatedAt,
		Events:    make([]models.RoomEvent, 0), // Events would come from event store
	}

	return activity, nil
}

// Helper methods

// generateSessionID generates a unique session ID for a room
func (rl *RoomLifecycle) generateSessionID(roomID string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d", roomID, timestamp)
}

// IsRoomInactive checks if a room has been inactive for too long
// This function is used internally by cleanup operations
func (rl *RoomLifecycle) IsRoomInactive(room *models.Room, threshold time.Duration) bool {
	return time.Since(room.LastActivity) > threshold
}

// CanTransitionToStatus checks if a room can transition to a new status
func (rl *RoomLifecycle) CanTransitionToStatus(currentStatus, newStatus models.RoomStatus) bool {
	// Define valid status transitions
	validTransitions := map[models.RoomStatus][]models.RoomStatus{
		models.RoomStatusWaiting: {models.RoomStatusActive, models.RoomStatusFull, models.RoomStatusClosed},
		models.RoomStatusFull:    {models.RoomStatusActive, models.RoomStatusWaiting, models.RoomStatusClosed},
		models.RoomStatusActive:  {models.RoomStatusWaiting, models.RoomStatusClosed},
		models.RoomStatusClosed:  {models.RoomStatusArchived},
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}
