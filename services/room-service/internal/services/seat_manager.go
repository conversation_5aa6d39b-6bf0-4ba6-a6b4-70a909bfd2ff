package services

import (
	"context"
	"fmt"
	"sort"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/models"
)

// SeatManager handles all seat assignment and management logic
type SeatManager struct {
	logger *logrus.Logger
}

// NewSeatManager creates a new seat manager instance
func NewSeatManager(logger *logrus.Logger) *SeatManager {
	return &SeatManager{
		logger: logger,
	}
}

// SeatAssignmentRequest represents a request to assign a seat
type SeatAssignmentRequest struct {
	RoomID            string
	UserID            string
	Username          string
	CurrentSeats      []models.RoomPlayer
	MaxPlayers        int
	PreferredPosition *int // Optional: for reconnections
}

// SeatAssignmentResult represents the result of seat assignment
type SeatAssignmentResult struct {
	Position       int
	IsReconnection bool
	UpdatedSeats   []models.RoomPlayer
}

// AssignSeat assigns a seat to a player using comprehensive logic
func (sm *SeatManager) AssignSeat(ctx context.Context, request *SeatAssignmentRequest) (*SeatAssignmentResult, error) {
	sm.logger.WithFields(logrus.Fields{
		"room_id":            request.RoomID,
		"user_id":            request.UserID,
		"username":           request.Username,
		"current_seat_count": len(request.CurrentSeats),
		"max_players":        request.MaxPlayers,
	}).Info("Processing seat assignment request")

	// Check if player is already seated (reconnection case)
	if existingPosition := sm.findExistingPlayerPosition(request.CurrentSeats, request.UserID); existingPosition != -1 {
		sm.logger.WithFields(logrus.Fields{
			"room_id":  request.RoomID,
			"user_id":  request.UserID,
			"position": existingPosition,
		}).Info("Player reconnecting to existing seat")

		return &SeatAssignmentResult{
			Position:       existingPosition,
			IsReconnection: true,
			UpdatedSeats:   request.CurrentSeats,
		}, nil
	}

	// Check room capacity
	if len(request.CurrentSeats) >= request.MaxPlayers {
		return nil, fmt.Errorf("room is at maximum capacity (%d/%d)", len(request.CurrentSeats), request.MaxPlayers)
	}

	// Calculate next available position
	nextPosition := sm.calculateOptimalPosition(request.CurrentSeats, request.MaxPlayers, request.PreferredPosition)

	sm.logger.WithFields(logrus.Fields{
		"room_id":           request.RoomID,
		"user_id":           request.UserID,
		"assigned_position": nextPosition,
		"total_players":     len(request.CurrentSeats) + 1,
	}).Info("Assigned new seat position")

	return &SeatAssignmentResult{
		Position:       nextPosition,
		IsReconnection: false,
		UpdatedSeats:   request.CurrentSeats, // Will be updated by caller
	}, nil
}

// RemoveSeat removes a player from their seat and optionally reassigns positions
func (sm *SeatManager) RemoveSeat(ctx context.Context, roomID, userID string, currentSeats []models.RoomPlayer, reassignPositions bool) ([]models.RoomPlayer, error) {
	sm.logger.WithFields(logrus.Fields{
		"room_id":            roomID,
		"user_id":            userID,
		"reassign_positions": reassignPositions,
		"current_seat_count": len(currentSeats),
	}).Info("Processing seat removal request")

	// Find player to remove
	playerIndex := -1
	for i, player := range currentSeats {
		if player.UserID == userID {
			playerIndex = i
			break
		}
	}

	if playerIndex == -1 {
		return currentSeats, fmt.Errorf("player %s not found in room %s", userID, roomID)
	}

	removedPosition := currentSeats[playerIndex].Position

	// Remove player from seats
	updatedSeats := append(currentSeats[:playerIndex], currentSeats[playerIndex+1:]...)

	sm.logger.WithFields(logrus.Fields{
		"room_id":           roomID,
		"user_id":           userID,
		"removed_position":  removedPosition,
		"remaining_players": len(updatedSeats),
	}).Info("Player removed from seat")

	// Reassign positions if requested
	if reassignPositions && len(updatedSeats) > 0 {
		updatedSeats = sm.reassignSequentialPositions(updatedSeats)
		sm.logger.WithFields(logrus.Fields{
			"room_id":           roomID,
			"remaining_players": len(updatedSeats),
		}).Info("Positions reassigned sequentially")
	}

	return updatedSeats, nil
}

// findExistingPlayerPosition checks if a player already has a seat
func (sm *SeatManager) findExistingPlayerPosition(seats []models.RoomPlayer, userID string) int {
	for _, player := range seats {
		if player.UserID == userID {
			return player.Position
		}
	}
	return -1
}

// calculateOptimalPosition finds the best available position
func (sm *SeatManager) calculateOptimalPosition(seats []models.RoomPlayer, maxPlayers int, preferredPosition *int) int {
	// If preferred position is specified and available, use it
	if preferredPosition != nil && sm.isPositionAvailable(seats, *preferredPosition) {
		return *preferredPosition
	}

	// Get all used positions
	usedPositions := make(map[int]bool)
	for _, player := range seats {
		usedPositions[player.Position] = true
	}

	// Find first available position starting from 0 (gap-filling algorithm)
	for position := 0; position < maxPlayers; position++ {
		if !usedPositions[position] {
			return position
		}
	}

	// This should never happen if capacity check passed
	return len(seats)
}

// isPositionAvailable checks if a specific position is available
func (sm *SeatManager) isPositionAvailable(seats []models.RoomPlayer, position int) bool {
	for _, player := range seats {
		if player.Position == position {
			return false
		}
	}
	return true
}

// reassignSequentialPositions reassigns all positions sequentially starting from 0
func (sm *SeatManager) reassignSequentialPositions(seats []models.RoomPlayer) []models.RoomPlayer {
	// Sort players by their current position to maintain relative order
	sort.Slice(seats, func(i, j int) bool {
		return seats[i].Position < seats[j].Position
	})

	// Reassign positions sequentially
	for i := range seats {
		seats[i].Position = i
	}

	return seats
}

// ValidateSeatConfiguration validates the current seat configuration
func (sm *SeatManager) ValidateSeatConfiguration(seats []models.RoomPlayer, maxPlayers int) error {
	if len(seats) > maxPlayers {
		return fmt.Errorf("seat count (%d) exceeds maximum players (%d)", len(seats), maxPlayers)
	}

	// Check for duplicate positions
	positionMap := make(map[int]bool)
	for _, player := range seats {
		if positionMap[player.Position] {
			return fmt.Errorf("duplicate position %d found", player.Position)
		}
		positionMap[player.Position] = true

		// Check position bounds
		if player.Position < 0 || player.Position >= maxPlayers {
			return fmt.Errorf("position %d is out of bounds (0-%d)", player.Position, maxPlayers-1)
		}
	}

	return nil
}

// GetSeatStatistics returns statistics about current seat usage
func (sm *SeatManager) GetSeatStatistics(seats []models.RoomPlayer, maxPlayers int) map[string]interface{} {
	usedPositions := make([]int, 0, len(seats))
	for _, player := range seats {
		usedPositions = append(usedPositions, player.Position)
	}
	sort.Ints(usedPositions)

	gaps := sm.findGaps(usedPositions, maxPlayers)

	return map[string]interface{}{
		"total_seats":     maxPlayers,
		"occupied_seats":  len(seats),
		"available_seats": maxPlayers - len(seats),
		"used_positions":  usedPositions,
		"gaps":            gaps,
		"occupancy_rate":  float64(len(seats)) / float64(maxPlayers) * 100,
	}
}

// findGaps identifies gaps in seat positions
func (sm *SeatManager) findGaps(usedPositions []int, maxPlayers int) []int {
	gaps := make([]int, 0)
	usedMap := make(map[int]bool)

	for _, pos := range usedPositions {
		usedMap[pos] = true
	}

	for i := 0; i < maxPlayers; i++ {
		if !usedMap[i] {
			gaps = append(gaps, i)
		}
	}

	return gaps
}
