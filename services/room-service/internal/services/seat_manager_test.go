package services

import (
	"context"
	"testing"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/xzgame/room-service/internal/models"
)

func TestSeatManager_AssignSeat(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests
	seatManager := NewSeatManager(logger)
	ctx := context.Background()

	t.Run("First player gets position 0", func(t *testing.T) {
		request := &SeatAssignmentRequest{
			RoomID:       "room1",
			UserID:       "user1",
			Username:     "Player1",
			CurrentSeats: []models.RoomPlayer{},
			MaxPlayers:   6,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 0, result.Position)
		assert.False(t, result.IsReconnection)
	})

	t.Run("Second player gets position 1", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
		}

		request := &SeatAssignmentRequest{
			RoomID:       "room1",
			UserID:       "user2",
			Username:     "Player2",
			CurrentSeats: currentSeats,
			MaxPlayers:   6,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 1, result.Position)
		assert.False(t, result.IsReconnection)
	})

	t.Run("Gap filling algorithm works correctly", func(t *testing.T) {
		// Seats: [0, 2, 4] - gaps at positions 1 and 3
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 2},
			{UserID: "user3", Username: "Player3", Position: 4},
		}

		request := &SeatAssignmentRequest{
			RoomID:       "room1",
			UserID:       "user4",
			Username:     "Player4",
			CurrentSeats: currentSeats,
			MaxPlayers:   6,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 1, result.Position) // Should fill first gap
		assert.False(t, result.IsReconnection)
	})

	t.Run("Player reconnection returns existing position", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
		}

		request := &SeatAssignmentRequest{
			RoomID:       "room1",
			UserID:       "user1", // Existing player
			Username:     "Player1",
			CurrentSeats: currentSeats,
			MaxPlayers:   6,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 0, result.Position)
		assert.True(t, result.IsReconnection)
	})

	t.Run("Room at capacity returns error", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
		}

		request := &SeatAssignmentRequest{
			RoomID:       "room1",
			UserID:       "user3",
			Username:     "Player3",
			CurrentSeats: currentSeats,
			MaxPlayers:   2, // Room is full
		}

		result, err := seatManager.AssignSeat(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "maximum capacity")
	})

	t.Run("Preferred position is used when available", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 2},
		}

		preferredPosition := 1
		request := &SeatAssignmentRequest{
			RoomID:            "room1",
			UserID:            "user3",
			Username:          "Player3",
			CurrentSeats:      currentSeats,
			MaxPlayers:        6,
			PreferredPosition: &preferredPosition,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 1, result.Position)
		assert.False(t, result.IsReconnection)
	})

	t.Run("Preferred position unavailable falls back to gap filling", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
		}

		preferredPosition := 1 // Already taken
		request := &SeatAssignmentRequest{
			RoomID:            "room1",
			UserID:            "user3",
			Username:          "Player3",
			CurrentSeats:      currentSeats,
			MaxPlayers:        6,
			PreferredPosition: &preferredPosition,
		}

		result, err := seatManager.AssignSeat(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, 2, result.Position) // Next available position
		assert.False(t, result.IsReconnection)
	})
}

func TestSeatManager_RemoveSeat(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	seatManager := NewSeatManager(logger)
	ctx := context.Background()

	t.Run("Remove player without reassignment", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
			{UserID: "user3", Username: "Player3", Position: 2},
		}

		updatedSeats, err := seatManager.RemoveSeat(ctx, "room1", "user2", currentSeats, false)
		require.NoError(t, err)
		assert.Len(t, updatedSeats, 2)
		
		// Check remaining players
		assert.Equal(t, "user1", updatedSeats[0].UserID)
		assert.Equal(t, 0, updatedSeats[0].Position)
		assert.Equal(t, "user3", updatedSeats[1].UserID)
		assert.Equal(t, 2, updatedSeats[1].Position) // Position unchanged
	})

	t.Run("Remove player with reassignment", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
			{UserID: "user3", Username: "Player3", Position: 2},
		}

		updatedSeats, err := seatManager.RemoveSeat(ctx, "room1", "user2", currentSeats, true)
		require.NoError(t, err)
		assert.Len(t, updatedSeats, 2)
		
		// Check positions are reassigned sequentially
		assert.Equal(t, "user1", updatedSeats[0].UserID)
		assert.Equal(t, 0, updatedSeats[0].Position)
		assert.Equal(t, "user3", updatedSeats[1].UserID)
		assert.Equal(t, 1, updatedSeats[1].Position) // Reassigned from 2 to 1
	})

	t.Run("Remove non-existent player returns error", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
		}

		updatedSeats, err := seatManager.RemoveSeat(ctx, "room1", "user999", currentSeats, false)
		assert.Error(t, err)
		assert.Equal(t, currentSeats, updatedSeats) // Unchanged
		assert.Contains(t, err.Error(), "not found")
	})

	t.Run("Remove last player leaves empty room", func(t *testing.T) {
		currentSeats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
		}

		updatedSeats, err := seatManager.RemoveSeat(ctx, "room1", "user1", currentSeats, true)
		require.NoError(t, err)
		assert.Len(t, updatedSeats, 0)
	})
}

func TestSeatManager_ValidateSeatConfiguration(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	seatManager := NewSeatManager(logger)

	t.Run("Valid configuration passes", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
			{UserID: "user3", Username: "Player3", Position: 2},
		}

		err := seatManager.ValidateSeatConfiguration(seats, 6)
		assert.NoError(t, err)
	})

	t.Run("Duplicate positions fail validation", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 0}, // Duplicate position
		}

		err := seatManager.ValidateSeatConfiguration(seats, 6)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duplicate position")
	})

	t.Run("Position out of bounds fails validation", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 6}, // Out of bounds for max 6 players
		}

		err := seatManager.ValidateSeatConfiguration(seats, 6)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "out of bounds")
	})

	t.Run("Too many players fails validation", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
			{UserID: "user3", Username: "Player3", Position: 2},
		}

		err := seatManager.ValidateSeatConfiguration(seats, 2) // Max 2 but have 3
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "exceeds maximum")
	})

	t.Run("Negative position fails validation", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: -1}, // Negative position
		}

		err := seatManager.ValidateSeatConfiguration(seats, 6)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "out of bounds")
	})
}

func TestSeatManager_GetSeatStatistics(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	seatManager := NewSeatManager(logger)

	t.Run("Statistics for partially filled room", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 2},
			{UserID: "user3", Username: "Player3", Position: 4},
		}

		stats := seatManager.GetSeatStatistics(seats, 6)
		
		assert.Equal(t, 6, stats["total_seats"])
		assert.Equal(t, 3, stats["occupied_seats"])
		assert.Equal(t, 3, stats["available_seats"])
		assert.Equal(t, []int{0, 2, 4}, stats["used_positions"])
		assert.Equal(t, []int{1, 3, 5}, stats["gaps"])
		assert.Equal(t, 50.0, stats["occupancy_rate"])
	})

	t.Run("Statistics for empty room", func(t *testing.T) {
		seats := []models.RoomPlayer{}

		stats := seatManager.GetSeatStatistics(seats, 6)
		
		assert.Equal(t, 6, stats["total_seats"])
		assert.Equal(t, 0, stats["occupied_seats"])
		assert.Equal(t, 6, stats["available_seats"])
		assert.Equal(t, []int{}, stats["used_positions"])
		assert.Equal(t, []int{0, 1, 2, 3, 4, 5}, stats["gaps"])
		assert.Equal(t, 0.0, stats["occupancy_rate"])
	})

	t.Run("Statistics for full room", func(t *testing.T) {
		seats := []models.RoomPlayer{
			{UserID: "user1", Username: "Player1", Position: 0},
			{UserID: "user2", Username: "Player2", Position: 1},
			{UserID: "user3", Username: "Player3", Position: 2},
		}

		stats := seatManager.GetSeatStatistics(seats, 3)
		
		assert.Equal(t, 3, stats["total_seats"])
		assert.Equal(t, 3, stats["occupied_seats"])
		assert.Equal(t, 0, stats["available_seats"])
		assert.Equal(t, []int{0, 1, 2}, stats["used_positions"])
		assert.Equal(t, []int{}, stats["gaps"])
		assert.Equal(t, 100.0, stats["occupancy_rate"])
	})
}
