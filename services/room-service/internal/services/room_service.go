package services

import (
	"context"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/internal/repositories"
	"github.com/xzgame/room-service/pkg/redis"
)

// RoomService defines the interface for room operations
type RoomService interface {
	// Room CRUD operations
	CreateRoom(ctx context.Context, request *models.CreateRoomRequest) (*models.Room, error)
	GetRoom(ctx context.Context, roomID string, forceRefresh bool) (*models.Room, error)
	UpdateRoom(ctx context.Context, roomID string, updates *models.UpdateRoomRequest) (*models.Room, error)
	DeleteRoom(ctx context.Context, roomID string, adminUserID string) error
	ListRooms(ctx context.Context, filter *models.RoomListFilter) (*models.RoomListResponse, error)
	GetAvailableRooms(ctx context.Context, gameType models.GameType) ([]*models.Room, error)

	// Player operations
	JoinRoom(ctx context.Context, request *models.JoinRoomRequest) (*models.Room, error)
	LeaveRoom(ctx context.Context, request *models.LeaveRoomRequest) (*models.Room, error)
	SetPlayerReady(ctx context.Context, request *models.SetPlayerReadyRequest) (*models.Room, error)
	GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error)

	// Room lifecycle operations
	StartGame(ctx context.Context, request *models.StartGameRequest) (*models.StartGameResponse, error)
	EndGame(ctx context.Context, request *models.EndGameRequest) (*models.Room, error)
	GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error)
	GetRoomActivity(ctx context.Context, roomID string) (*models.RoomActivity, error)

	// Room information for broadcasting
	GetRoomInfo(ctx context.Context, roomID string) (*models.RoomInfo, error)
}

// RoomOrchestratorService defines the interface for orchestrated room operations
// This is where business logic from Socket Gateway moves
type RoomOrchestratorService interface {
	// Orchestrated operations with business logic
	OrchestateJoinRoom(ctx context.Context, request *JoinRoomRequest) (*JoinRoomResponse, error)
	OrchestateLeaveRoom(ctx context.Context, request *LeaveRoomRequest) (*LeaveRoomResponse, error)
}

// roomService implements the RoomService interface using modular components
type roomService struct {
	roomManager      *RoomManager
	playerOperations *PlayerOperations
	roomLifecycle    *RoomLifecycle
	validator        *RoomValidator
	orchestrator     *RoomOrchestrator
	logger           *logrus.Logger
}

// NewRoomService creates a new room service instance with modular components
func NewRoomService(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
) RoomService {
	// Create modular components
	roomManager := NewRoomManager(roomRepo, redisClient, config, logger)
	validator := NewRoomValidator(nil, logger) // Manager client will be added later
	playerOperations := NewPlayerOperations(roomRepo, redisClient, config, logger, roomManager)
	roomLifecycle := NewRoomLifecycle(roomRepo, redisClient, config, logger, roomManager, validator)

	service := &roomService{
		roomManager:      roomManager,
		playerOperations: playerOperations,
		roomLifecycle:    roomLifecycle,
		validator:        validator,
		logger:           logger,
	}

	return service
}

// NewRoomServiceWithOrchestrator creates a room service with orchestrator capabilities
func NewRoomServiceWithOrchestrator(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
	gameServiceClient GameServiceClient,
	managerServiceClient ManagerServiceClient,
	notificationClient NotificationClient,
) (RoomService, *RoomOrchestrator) {
	// Create modular components
	roomManager := NewRoomManager(roomRepo, redisClient, config, logger)
	validator := NewRoomValidator(nil, logger) // Manager client will be added later
	playerOperations := NewPlayerOperations(roomRepo, redisClient, config, logger, roomManager)
	roomLifecycle := NewRoomLifecycle(roomRepo, redisClient, config, logger, roomManager, validator)

	service := &roomService{
		roomManager:      roomManager,
		playerOperations: playerOperations,
		roomLifecycle:    roomLifecycle,
		validator:        validator,
		logger:           logger,
	}

	// Create orchestrator with service clients
	orchestrator := NewRoomOrchestrator(
		service,
		gameServiceClient,
		managerServiceClient,
		notificationClient,
		redisClient,
		config,
		logger,
	)

	service.orchestrator = orchestrator

	return service, orchestrator
}

// Room CRUD operations implementation

// CreateRoom creates a new room
func (rs *roomService) CreateRoom(ctx context.Context, request *models.CreateRoomRequest) (*models.Room, error) {
	return rs.roomManager.CreateRoom(ctx, request)
}

// GetRoom retrieves a room by ID
func (rs *roomService) GetRoom(ctx context.Context, roomID string, forceRefresh bool) (*models.Room, error) {
	return rs.roomManager.GetRoom(ctx, roomID, forceRefresh)
}

// UpdateRoom updates a room
func (rs *roomService) UpdateRoom(ctx context.Context, roomID string, updates *models.UpdateRoomRequest) (*models.Room, error) {
	return rs.roomManager.UpdateRoom(ctx, roomID, updates)
}

// DeleteRoom deletes a room
func (rs *roomService) DeleteRoom(ctx context.Context, roomID string, adminUserID string) error {
	return rs.roomManager.DeleteRoom(ctx, roomID, adminUserID)
}

// ListRooms lists rooms with filtering and pagination
func (rs *roomService) ListRooms(ctx context.Context, filter *models.RoomListFilter) (*models.RoomListResponse, error) {
	return rs.roomManager.ListRooms(ctx, filter)
}

// GetAvailableRooms gets available rooms for a game type
func (rs *roomService) GetAvailableRooms(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	return rs.roomManager.GetAvailableRooms(ctx, gameType)
}

// Player operations implementation

// JoinRoom adds a player to a room
func (rs *roomService) JoinRoom(ctx context.Context, request *models.JoinRoomRequest) (*models.Room, error) {
	return rs.playerOperations.JoinRoom(ctx, request)
}

// LeaveRoom removes a player from a room
func (rs *roomService) LeaveRoom(ctx context.Context, request *models.LeaveRoomRequest) (*models.Room, error) {
	return rs.playerOperations.LeaveRoom(ctx, request)
}

// SetPlayerReady sets a player's ready status
func (rs *roomService) SetPlayerReady(ctx context.Context, request *models.SetPlayerReadyRequest) (*models.Room, error) {
	return rs.playerOperations.SetPlayerReady(ctx, request)
}

// GetRoomsByPlayer gets all rooms where a specific player is present
func (rs *roomService) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	return rs.playerOperations.GetRoomsByPlayer(ctx, userID)
}

// Room lifecycle operations implementation

// StartGame starts a game in a room
func (rs *roomService) StartGame(ctx context.Context, request *models.StartGameRequest) (*models.StartGameResponse, error) {
	return rs.roomLifecycle.StartGame(ctx, request)
}

// EndGame ends a game in a room
func (rs *roomService) EndGame(ctx context.Context, request *models.EndGameRequest) (*models.Room, error) {
	return rs.roomLifecycle.EndGame(ctx, request)
}

// GetRoomStats gets statistics for a room
func (rs *roomService) GetRoomStats(ctx context.Context, roomID string) (*models.RoomStats, error) {
	return rs.roomLifecycle.GetRoomStats(ctx, roomID)
}

// GetRoomActivity gets recent activity for a room
func (rs *roomService) GetRoomActivity(ctx context.Context, roomID string) (*models.RoomActivity, error) {
	return rs.roomLifecycle.GetRoomActivity(ctx, roomID)
}

// GetRoomInfo gets comprehensive room information for broadcasting
func (rs *roomService) GetRoomInfo(ctx context.Context, roomID string) (*models.RoomInfo, error) {
	// Force refresh to get the latest room data from manager service
	// This ensures we don't return stale cached data when socket gateway requests room info
	room, err := rs.roomManager.GetRoom(ctx, roomID, true)
	if err != nil {
		return nil, err
	}

	// Calculate derived fields
	canJoin := room.Status == models.RoomStatusWaiting && room.CurrentPlayers < room.MaxPlayers
	canStart := room.CurrentPlayers >= room.MinPlayers && room.Status == models.RoomStatusWaiting

	// Create comprehensive room info
	roomInfo := &models.RoomInfo{
		RoomID:         roomID,
		Name:           room.Name,
		GameType:       room.GameType,
		Status:         room.Status,
		CurrentPlayers: room.CurrentPlayers,
		MaxPlayers:     room.MaxPlayers,
		MinPlayers:     room.MinPlayers,
		BetAmount:      room.BetAmount,
		Players:        room.Players,
		IsPrivate:      room.Configuration.IsPrivate,
		AutoStart:      room.Configuration.AutoStart,
		CreatedAt:      room.CreatedAt,
		LastActivity:   room.LastActivity,
		CanJoin:        canJoin,
		CanStart:       canStart,
	}

	return roomInfo, nil
}
