package services

import (
	"context"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
)

// MockRoomRepository is a mock implementation of RoomRepository
type MockRoomRepository struct {
	mock.Mock
}

func (m *MockRoomRepository) CreateRoom(ctx context.Context, room *models.Room) error {
	args := m.Called(ctx, room)
	return args.Error(0)
}

func (m *MockRoomRepository) GetRoom(ctx context.Context, roomID string) (*models.Room, error) {
	args := m.Called(ctx, roomID)
	return args.Get(0).(*models.Room), args.Error(1)
}

func (m *MockRoomRepository) UpdateRoom(ctx context.Context, roomID string, updates map[string]interface{}) error {
	args := m.Called(ctx, roomID, updates)
	return args.Error(0)
}

func (m *MockRoomRepository) DeleteRoom(ctx context.Context, roomID string) error {
	args := m.Called(ctx, roomID)
	return args.Error(0)
}

func (m *MockRoomRepository) ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*models.Room), args.Get(1).(int64), args.Error(2)
}

func (m *MockRoomRepository) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.Room), args.Error(1)
}

func (m *MockRoomRepository) GetRoomsByStatus(ctx context.Context, status models.RoomStatus) ([]*models.Room, error) {
	args := m.Called(ctx, status)
	return args.Get(0).([]*models.Room), args.Error(1)
}

func (m *MockRoomRepository) GetRoomsByGameType(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	args := m.Called(ctx, gameType)
	return args.Get(0).([]*models.Room), args.Error(1)
}

func (m *MockRoomRepository) CleanupInactiveRooms(ctx context.Context, inactiveThreshold int64) (int64, error) {
	args := m.Called(ctx, inactiveThreshold)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRoomRepository) ArchiveOldRooms(ctx context.Context, archiveThreshold int64) (int64, error) {
	args := m.Called(ctx, archiveThreshold)
	return args.Get(0).(int64), args.Error(1)
}

// For testing, we'll use a nil Redis client since the current architecture
// doesn't easily support mocking the concrete Redis client type

func TestRoomServiceArchitecture(t *testing.T) {
	t.Run("Service should be created with modular components", func(t *testing.T) {
		// Create mock dependencies
		mockRepo := &MockRoomRepository{}
		config := &config.Config{
			RedisTTL: 5 * time.Minute,
		}
		logger := logrus.New()

		// Create service with nil Redis for testing
		service := NewRoomService(mockRepo, nil, config, logger)

		// Verify service was created
		assert.NotNil(t, service)

		// Verify it implements the interface
		var _ RoomService = service
	})

	t.Run("Room validation should work", func(t *testing.T) {
		// Create mock dependencies
		mockRepo := &MockRoomRepository{}
		config := &config.Config{
			RedisTTL: 5 * time.Minute,
		}
		logger := logrus.New()

		// Create service
		service := NewRoomService(mockRepo, nil, config, logger)

		// Test invalid room creation request
		invalidRequest := &models.CreateRoomRequest{
			Name:       "", // Invalid: empty name
			GameType:   models.GameTypePrizeWheel,
			MaxPlayers: 4,
			MinPlayers: 2,
			BetLimits: models.BetLimitConfig{
				MinBet: 100,
				MaxBet: 1000,
			},
			CreatedBy: "test-user",
		}

		// Test room creation with invalid data
		ctx := context.Background()
		room, err := service.CreateRoom(ctx, invalidRequest)

		// Verify validation error
		assert.Error(t, err)
		assert.Nil(t, room)
		assert.Contains(t, err.Error(), "room name cannot be empty")
	})

	t.Run("Room lifecycle helper functions should work", func(t *testing.T) {
		// Create mock dependencies
		mockRepo := &MockRoomRepository{}
		config := &config.Config{
			RedisTTL: 5 * time.Minute,
		}
		logger := logrus.New()

		// Create room lifecycle component directly to test helper functions
		roomManager := NewRoomManager(mockRepo, nil, config, logger)
		validator := NewRoomValidator(nil, logger)
		roomLifecycle := NewRoomLifecycle(mockRepo, nil, config, logger, roomManager, validator)

		// Test room inactivity check
		testRoom := &models.Room{
			LastActivity: time.Now().Add(-2 * time.Hour),
		}

		// Test IsRoomInactive function
		isInactive := roomLifecycle.IsRoomInactive(testRoom, 1*time.Hour)
		assert.True(t, isInactive, "Room should be considered inactive")

		// Test CanTransitionToStatus function
		canTransition := roomLifecycle.CanTransitionToStatus(models.RoomStatusWaiting, models.RoomStatusActive)
		assert.True(t, canTransition, "Should be able to transition from waiting to active")

		cannotTransition := roomLifecycle.CanTransitionToStatus(models.RoomStatusActive, models.RoomStatusWaiting)
		assert.True(t, cannotTransition, "Should be able to transition from active to waiting")
	})

}
