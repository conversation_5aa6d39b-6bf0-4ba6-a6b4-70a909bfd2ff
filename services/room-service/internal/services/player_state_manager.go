package services

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/pkg/redis"
)

// PlayerStateManager handles player state management and color selections
type PlayerStateManager struct {
	redisClient *redis.RedisClient
	logger      *logrus.Logger
}

// NewPlayerStateManager creates a new player state manager
func NewPlayerStateManager(redisClient *redis.RedisClient, logger *logrus.Logger) *PlayerStateManager {
	return &PlayerStateManager{
		redisClient: redisClient,
		logger:      logger,
	}
}

// PlayerStatus represents a player's current status in a room
type PlayerStatus struct {
	UserID       string                 `json:"userId"`
	Username     string                 `json:"username"`
	RoomID       string                 `json:"roomId"`
	Status       models.PlayerStatus    `json:"status"`
	IsReady      bool                   `json:"isReady"`
	Position     int                    `json:"position"`
	ColorID      string                 `json:"colorId,omitempty"`
	LastActivity time.Time              `json:"lastActivity"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// PlayerColorSelection represents a player's color selection
type PlayerColorSelection struct {
	UserID     string    `json:"userId"`
	Username   string    `json:"username"`
	RoomID     string    `json:"roomId"`
	ColorID    string    `json:"colorId"`
	SelectedAt time.Time `json:"selectedAt"`
}

// GetPlayerStatus retrieves a player's current status
func (psm *PlayerStateManager) GetPlayerStatus(ctx context.Context, roomID, userID string) (*PlayerStatus, error) {
	key := fmt.Sprintf("room:%s:player:%s:status", roomID, userID)

	var status PlayerStatus
	err := psm.redisClient.Get(ctx, key, &status)
	if err != nil {
		psm.logger.WithFields(logrus.Fields{
			"roomId": roomID,
			"userId": userID,
		}).Debug("Player status not found in cache")
		return nil, fmt.Errorf("player status not found")
	}

	return &status, nil
}

// SetPlayerStatus updates a player's status
func (psm *PlayerStateManager) SetPlayerStatus(ctx context.Context, status *PlayerStatus) error {
	key := fmt.Sprintf("room:%s:player:%s:status", status.RoomID, status.UserID)

	// Update last activity
	status.LastActivity = time.Now()

	// Store in Redis with 1 hour TTL
	err := psm.redisClient.Set(ctx, key, status, time.Hour)
	if err != nil {
		psm.logger.WithError(err).WithFields(logrus.Fields{
			"roomId": status.RoomID,
			"userId": status.UserID,
		}).Error("Failed to set player status")
		return err
	}

	psm.logger.WithFields(logrus.Fields{
		"roomId":  status.RoomID,
		"userId":  status.UserID,
		"status":  status.Status,
		"isReady": status.IsReady,
	}).Debug("Player status updated")

	return nil
}

// SetPlayerColorSelection stores a player's color selection
func (psm *PlayerStateManager) SetPlayerColorSelection(ctx context.Context, selection *PlayerColorSelection) error {
	// Store individual player color selection
	playerKey := fmt.Sprintf("room:%s:player:%s:color", selection.RoomID, selection.UserID)
	err := psm.redisClient.Set(ctx, playerKey, selection, time.Hour)
	if err != nil {
		return fmt.Errorf("failed to store player color selection: %w", err)
	}

	// Update room color state
	roomColorKey := fmt.Sprintf("room:%s:color_selections", selection.RoomID)
	colorSelections, err := psm.getRoomColorSelections(ctx, selection.RoomID)
	if err != nil {
		colorSelections = make(map[string]*PlayerColorSelection)
	}

	colorSelections[selection.UserID] = selection

	err = psm.redisClient.Set(ctx, roomColorKey, colorSelections, time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update room color selections: %w", err)
	}

	psm.logger.WithFields(logrus.Fields{
		"roomId":  selection.RoomID,
		"userId":  selection.UserID,
		"colorId": selection.ColorID,
	}).Info("Player color selection stored")

	return nil
}

// GetPlayerColorSelection retrieves a player's color selection
func (psm *PlayerStateManager) GetPlayerColorSelection(ctx context.Context, roomID, userID string) (*PlayerColorSelection, error) {
	key := fmt.Sprintf("room:%s:player:%s:color", roomID, userID)

	var selection PlayerColorSelection
	err := psm.redisClient.Get(ctx, key, &selection)
	if err != nil {
		return nil, fmt.Errorf("player color selection not found")
	}

	return &selection, nil
}

// GetRoomColorSelections retrieves all color selections for a room
func (psm *PlayerStateManager) GetRoomColorSelections(ctx context.Context, roomID string) (map[string]*PlayerColorSelection, error) {
	return psm.getRoomColorSelections(ctx, roomID)
}

// RemovePlayerColorSelection removes a player's color selection
func (psm *PlayerStateManager) RemovePlayerColorSelection(ctx context.Context, roomID, userID string) error {
	// Remove individual player color selection
	playerKey := fmt.Sprintf("room:%s:player:%s:color", roomID, userID)
	err := psm.redisClient.Del(ctx, playerKey)
	if err != nil {
		psm.logger.WithError(err).Error("Failed to remove player color selection")
	}

	// Update room color state
	roomColorKey := fmt.Sprintf("room:%s:color_selections", roomID)
	colorSelections, err := psm.getRoomColorSelections(ctx, roomID)
	if err != nil {
		return nil // If room color selections don't exist, nothing to remove
	}

	delete(colorSelections, userID)

	err = psm.redisClient.Set(ctx, roomColorKey, colorSelections, time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update room color selections: %w", err)
	}

	psm.logger.WithFields(logrus.Fields{
		"roomId": roomID,
		"userId": userID,
	}).Info("Player color selection removed")

	return nil
}

// GetAvailableColors returns available colors for a room
func (psm *PlayerStateManager) GetAvailableColors(ctx context.Context, roomID string) ([]map[string]interface{}, error) {
	colorSelections, err := psm.getRoomColorSelections(ctx, roomID)
	if err != nil {
		colorSelections = make(map[string]*PlayerColorSelection)
	}

	// Get all colors
	allColors := psm.getAllColors()

	// Mark taken colors
	takenColors := make(map[string]bool)
	for _, selection := range colorSelections {
		takenColors[selection.ColorID] = true
	}

	// Filter available colors
	var availableColors []map[string]interface{}
	for _, color := range allColors {
		if colorID, ok := color["id"].(string); ok && !takenColors[colorID] {
			availableColors = append(availableColors, color)
		}
	}

	return availableColors, nil
}

// ValidateColorSelection validates if a color can be selected
func (psm *PlayerStateManager) ValidateColorSelection(ctx context.Context, roomID, userID, colorID string) error {
	// Check if color is valid
	if !psm.isValidColor(colorID) {
		return fmt.Errorf("invalid color: %s", colorID)
	}

	// Check if color is already taken
	colorSelections, err := psm.getRoomColorSelections(ctx, roomID)
	if err != nil {
		colorSelections = make(map[string]*PlayerColorSelection)
	}

	for otherUserID, selection := range colorSelections {
		if selection.ColorID == colorID && otherUserID != userID {
			return fmt.Errorf("color %s is already taken by user %s", colorID, otherUserID)
		}
	}

	return nil
}

// CleanupPlayerState removes all state for a player leaving a room
func (psm *PlayerStateManager) CleanupPlayerState(ctx context.Context, roomID, userID string) error {
	// Remove player status
	statusKey := fmt.Sprintf("room:%s:player:%s:status", roomID, userID)
	psm.redisClient.Del(ctx, statusKey)

	// Remove player color selection
	psm.RemovePlayerColorSelection(ctx, roomID, userID)

	psm.logger.WithFields(logrus.Fields{
		"roomId": roomID,
		"userId": userID,
	}).Info("Player state cleaned up")

	return nil
}

// Private helper methods

func (psm *PlayerStateManager) getRoomColorSelections(ctx context.Context, roomID string) (map[string]*PlayerColorSelection, error) {
	key := fmt.Sprintf("room:%s:color_selections", roomID)

	var selections map[string]*PlayerColorSelection
	err := psm.redisClient.Get(ctx, key, &selections)
	if err != nil {
		return make(map[string]*PlayerColorSelection), nil
	}

	return selections, nil
}

func (psm *PlayerStateManager) getAllColors() []map[string]interface{} {
	return []map[string]interface{}{
		{"id": "red", "name": "Red", "hex": "#FF0000"},
		{"id": "blue", "name": "Blue", "hex": "#0000FF"},
		{"id": "green", "name": "Green", "hex": "#00FF00"},
		{"id": "yellow", "name": "Yellow", "hex": "#FFFF00"},
		{"id": "purple", "name": "Purple", "hex": "#800080"},
		{"id": "orange", "name": "Orange", "hex": "#FFA500"},
		{"id": "pink", "name": "Pink", "hex": "#FFC0CB"},
		{"id": "teal", "name": "Teal", "hex": "#008080"},
	}
}

func (psm *PlayerStateManager) isValidColor(colorID string) bool {
	validColors := map[string]bool{
		"red": true, "blue": true, "green": true, "yellow": true,
		"purple": true, "orange": true, "pink": true, "teal": true,
	}
	return validColors[colorID]
}
