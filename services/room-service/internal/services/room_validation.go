package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/models"
)

// ManagerClient interface for balance validation
type ManagerClient interface {
	GetPlayerBalance(ctx context.Context, userID string) (float64, error)
}

// RoomValidator handles room validation operations
type RoomValidator struct {
	managerClient ManagerClient
	logger        *logrus.Logger
}

// NewRoomValidator creates a new room validator instance
func NewRoomValidator(
	managerClient ManagerClient,
	logger *logrus.Logger,
) *RoomValidator {
	return &RoomValidator{
		managerClient: managerClient,
		logger:        logger,
	}
}

// ValidateCreateRoomRequest validates a create room request
func (rv *RoomValidator) ValidateCreateRoomRequest(request *models.CreateRoomRequest) error {
	// Basic validation
	if err := rv.validateBasicRoomData(request.Name, int32(request.MaxPlayers), int32(request.MinPlayers)); err != nil {
		return err
	}

	// Validate bet limits
	if err := rv.validateBetLimits(&request.BetLimits); err != nil {
		return err
	}

	// Validate game type
	if err := rv.validateGameType(request.GameType); err != nil {
		return err
	}

	// Validate game-specific configuration
	if err := rv.validateGameSpecificConfig(request.GameType, make(map[string]string)); err != nil {
		return err
	}

	// Validate creator
	if err := rv.validateCreator(request.CreatedBy); err != nil {
		return err
	}

	return nil
}

// ValidateUpdateRoomRequest validates an update room request
func (rv *RoomValidator) ValidateUpdateRoomRequest(request *models.UpdateRoomRequest, currentRoom *models.Room) error {
	// Validate name if provided
	if request.Name != nil {
		if err := rv.validateRoomName(*request.Name); err != nil {
			return err
		}
	}

	// Validate max players if provided
	if request.MaxPlayers != nil {
		if err := rv.validateMaxPlayers(int32(*request.MaxPlayers), int32(currentRoom.CurrentPlayers)); err != nil {
			return err
		}
	}

	// Validate bet limits if provided
	if request.BetLimits != nil {
		if err := rv.validateBetLimits(request.BetLimits); err != nil {
			return err
		}
	}

	// Validate game-specific configuration if provided
	if request.GameSpecific != nil {
		if err := rv.validateGameSpecificConfig(currentRoom.GameType, make(map[string]string)); err != nil {
			return err
		}
	}

	return nil
}

// ValidatePlayerBalance validates if a player has sufficient balance
func (rv *RoomValidator) ValidatePlayerBalance(ctx context.Context, userID string, betAmount int64) error {
	if rv.managerClient == nil {
		rv.logger.Warn("Manager client not available, skipping balance validation")
		return nil
	}

	// Get player balance from manager service
	balance, err := rv.managerClient.GetPlayerBalance(ctx, userID)
	if err != nil {
		rv.logger.WithError(err).WithField("user_id", userID).Warn("Failed to get player balance")
		return models.NewValidationError("balance", "unable to verify player balance", userID)
	}

	if balance < float64(betAmount) {
		return models.NewValidationError("balance", "insufficient balance", betAmount)
	}

	return nil
}

// ValidateRoomAccess validates if a user can access a room
func (rv *RoomValidator) ValidateRoomAccess(room *models.Room, userID string, password string) error {
	// Check if room is private and password is required
	if room.Configuration.IsPrivate {
		if password == "" {
			return models.NewRoomError(
				models.ErrorCodePasswordRequired,
				"password required for private room",
				room.ID.Hex(),
			)
		}
		if room.Configuration.Password != password {
			return models.NewRoomError(
				models.ErrorCodeInvalidPassword,
				"invalid room password",
				room.ID.Hex(),
			)
		}
	}

	return nil
}

// ValidateGameStart validates if a game can be started in a room
func (rv *RoomValidator) ValidateGameStart(room *models.Room) error {
	// Check room status
	if room.Status != models.RoomStatusWaiting && room.Status != models.RoomStatusFull {
		return models.NewRoomError(
			models.ErrorCodeInvalidRoomState,
			"room is not in a state where game can be started",
			room.ID.Hex(),
		)
	}

	// Check minimum players
	if room.CurrentPlayers < room.MinPlayers {
		return models.NewRoomError(
			models.ErrorCodeInsufficientPlayers,
			fmt.Sprintf("need at least %d players to start game", room.MinPlayers),
			room.ID.Hex(),
		)
	}

	// Check if all players are ready (if auto-start is disabled)
	if !room.Configuration.AutoStart {
		for _, player := range room.Players {
			if !player.IsReady {
				return models.NewRoomError(
					models.ErrorCodePlayersNotReady,
					"not all players are ready",
					room.ID.Hex(),
				)
			}
		}
	}

	return nil
}

// Helper validation methods

// validateBasicRoomData validates basic room data
func (rv *RoomValidator) validateBasicRoomData(name string, maxPlayers, minPlayers int32) error {
	if err := rv.validateRoomName(name); err != nil {
		return err
	}

	if maxPlayers <= 0 {
		return models.NewValidationError("maxPlayers", "max players must be positive", maxPlayers)
	}

	if minPlayers <= 0 {
		return models.NewValidationError("minPlayers", "min players must be positive", minPlayers)
	}

	if minPlayers > maxPlayers {
		return models.NewValidationError("minPlayers", "min players cannot exceed max players", minPlayers)
	}

	// Check reasonable limits
	if maxPlayers > 20 {
		return models.NewValidationError("maxPlayers", "max players cannot exceed 20", maxPlayers)
	}

	return nil
}

// validateRoomName validates room name
func (rv *RoomValidator) validateRoomName(name string) error {
	if name == "" {
		return models.NewValidationError("name", "room name cannot be empty", name)
	}

	if len(name) > 100 {
		return models.NewValidationError("name", "room name too long", name)
	}

	// Check for invalid characters
	if strings.ContainsAny(name, "<>\"'&") {
		return models.NewValidationError("name", "room name contains invalid characters", name)
	}

	return nil
}

// validateMaxPlayers validates max players with current player count
func (rv *RoomValidator) validateMaxPlayers(maxPlayers, currentPlayers int32) error {
	if maxPlayers <= 0 {
		return models.NewValidationError("maxPlayers", "max players must be positive", maxPlayers)
	}

	if maxPlayers < currentPlayers {
		return models.NewValidationError("maxPlayers", "cannot reduce max players below current player count", maxPlayers)
	}

	return nil
}

// validateBetLimits validates bet limits
func (rv *RoomValidator) validateBetLimits(betLimits *models.BetLimitConfig) error {
	if betLimits.MinBet <= 0 {
		return models.NewValidationError("betLimits.minBet", "minimum bet must be positive", betLimits.MinBet)
	}

	if betLimits.MaxBet < betLimits.MinBet {
		return models.NewValidationError("betLimits.maxBet", "maximum bet cannot be less than minimum bet", betLimits.MaxBet)
	}

	// Check reasonable limits
	if betLimits.MaxBet > 1000000 { // 1M limit
		return models.NewValidationError("betLimits.maxBet", "maximum bet too high", betLimits.MaxBet)
	}

	return nil
}

// validateGameType validates game type
func (rv *RoomValidator) validateGameType(gameType models.GameType) error {
	switch gameType {
	case models.GameTypePrizeWheel, models.GameTypeAmidakuji:
		return nil
	default:
		return models.NewValidationError("gameType", "unsupported game type", gameType)
	}
}

// validateGameSpecificConfig validates game-specific configuration
func (rv *RoomValidator) validateGameSpecificConfig(gameType models.GameType, config map[string]string) error {
	switch gameType {
	case models.GameTypePrizeWheel:
		return rv.validatePrizeWheelConfig(config)
	case models.GameTypeAmidakuji:
		return rv.validateAmidakujiConfig(config)
	default:
		return nil
	}
}

// validatePrizeWheelConfig validates Prize Wheel specific configuration
func (rv *RoomValidator) validatePrizeWheelConfig(config map[string]string) error {
	// Validate color selection if provided
	if colors, exists := config["available_colors"]; exists {
		colorList := strings.Split(colors, ",")
		if len(colorList) < 2 {
			return models.NewValidationError("availableColors", "prize wheel needs at least 2 colors", colors)
		}
		if len(colorList) > 8 {
			return models.NewValidationError("availableColors", "prize wheel cannot have more than 8 colors", colors)
		}
	}

	return nil
}

// validateAmidakujiConfig validates Amidakuji specific configuration
func (rv *RoomValidator) validateAmidakujiConfig(config map[string]string) error {
	// Validate ladder configuration if provided
	if ladders, exists := config["ladder_count"]; exists {
		if ladders == "" {
			return models.NewValidationError("ladderCount", "ladder count cannot be empty", ladders)
		}
		// Additional ladder validation can be added here
	}

	return nil
}

// validateCreator validates creator user ID
func (rv *RoomValidator) validateCreator(createdBy string) error {
	if createdBy == "" {
		return models.NewValidationError("createdBy", "creator user ID cannot be empty", createdBy)
	}

	// Additional creator validation can be added here
	// For example, checking if user exists in the system

	return nil
}
