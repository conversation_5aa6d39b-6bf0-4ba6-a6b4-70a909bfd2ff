package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/pkg/redis"
)

// RedisOrchestratorHandler handles Redis pub/sub messages for room orchestration
// This replaces HTTP endpoints and follows your existing Redis architecture
type RedisOrchestratorHandler struct {
	orchestrator *RoomOrchestrator
	redisClient  *redis.RedisClient
	config       *config.Config
	logger       *logrus.Logger

	// Response tracking for request/response pattern
	pendingResponses map[string]chan *OrchestratorResponse
}

// OrchestratorResponse represents a standardized orchestrator response
type OrchestratorResponse struct {
	Success   bool            `json:"success"`
	Data      interface{}     `json:"data,omitempty"`
	Error     string          `json:"error,omitempty"`
	Code      string          `json:"code,omitempty"`
	Broadcast *BroadcastEvent `json:"broadcast,omitempty"`
}

// RedisMessage represents the standardized Redis message format
type RedisMessage struct {
	Event    RedisEvent    `json:"event"`
	Metadata RedisMetadata `json:"metadata"`
}

// RedisEvent represents the event portion of a Redis message
type RedisEvent struct {
	Type      string      `json:"type"`
	Payload   interface{} `json:"payload"`
	Timestamp string      `json:"timestamp"`
}

// RedisMetadata represents the metadata portion of a Redis message
type RedisMetadata struct {
	ServiceID       string `json:"serviceId"`
	Version         string `json:"version"`
	CorrelationID   string `json:"correlationId"`
	ResponseChannel string `json:"responseChannel,omitempty"`
	Priority        int    `json:"priority,omitempty"`
}

// NewRedisOrchestratorHandler creates a new Redis orchestrator handler
func NewRedisOrchestratorHandler(
	orchestrator *RoomOrchestrator,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
) *RedisOrchestratorHandler {
	return &RedisOrchestratorHandler{
		orchestrator:     orchestrator,
		redisClient:      redisClient,
		config:           config,
		logger:           logger,
		pendingResponses: make(map[string]chan *OrchestratorResponse),
	}
}

// Initialize sets up Redis subscriptions for orchestrator events
func (h *RedisOrchestratorHandler) Initialize(ctx context.Context) error {
	h.logger.Info("Initializing Redis Orchestrator Handler")

	// Subscribe to room orchestration requests
	// Following your existing channel pattern: room:orchestrator:requests
	err := h.redisClient.Subscribe(ctx, "room:orchestrator:requests", h.handleOrchestratorRequest)
	if err != nil {
		return fmt.Errorf("failed to subscribe to orchestrator requests: %w", err)
	}

	// Subscribe to room subscription events from socket-gateway
	err = h.redisClient.Subscribe(ctx, "room:subscription:events", h.handleSubscriptionEvent)
	if err != nil {
		return fmt.Errorf("failed to subscribe to subscription events: %w", err)
	}

	h.logger.Info("Redis Orchestrator Handler initialized successfully")
	return nil
}

// handleOrchestratorRequest handles incoming orchestrator requests from Socket Gateway
func (h *RedisOrchestratorHandler) handleOrchestratorRequest(ctx context.Context, channel string, message []byte) error {
	var redisMsg RedisMessage
	if err := json.Unmarshal(message, &redisMsg); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal Redis message")
		return err
	}

	h.logger.WithFields(logrus.Fields{
		"eventType":     redisMsg.Event.Type,
		"correlationId": redisMsg.Metadata.CorrelationID,
		"serviceId":     redisMsg.Metadata.ServiceID,
	}).Info("Processing orchestrator request")

	// Route to appropriate handler based on event type
	var response *OrchestratorResponse
	var err error

	switch redisMsg.Event.Type {
	case "join_room":
		response, err = h.handleJoinRoomRequest(ctx, &redisMsg)
	case "leave_room":
		response, err = h.handleLeaveRoomRequest(ctx, &redisMsg)
	default:
		response = &OrchestratorResponse{
			Success: false,
			Error:   fmt.Sprintf("Unknown event type: %s", redisMsg.Event.Type),
			Code:    "UNKNOWN_EVENT_TYPE",
		}
	}

	if err != nil {
		h.logger.WithError(err).Error("Orchestrator request processing failed")
		response = &OrchestratorResponse{
			Success: false,
			Error:   "Internal processing error",
			Code:    "PROCESSING_ERROR",
		}
	}

	// Send response back to Socket Gateway if response channel is provided
	if redisMsg.Metadata.ResponseChannel != "" {
		if err := h.sendResponse(ctx, redisMsg.Metadata.ResponseChannel, redisMsg.Metadata.CorrelationID, response); err != nil {
			h.logger.WithError(err).Error("Failed to send response")
		}
	}

	// Handle broadcast events
	if response.Broadcast != nil {
		if err := h.handleBroadcast(ctx, response.Broadcast); err != nil {
			h.logger.WithError(err).Error("Failed to handle broadcast")
		}
	}

	return nil
}

// handleSubscriptionEvent handles room subscription events from socket-gateway
func (h *RedisOrchestratorHandler) handleSubscriptionEvent(ctx context.Context, channel string, message []byte) error {
	var redisMsg RedisMessage
	if err := json.Unmarshal(message, &redisMsg); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription event")
		return err
	}

	h.logger.WithFields(logrus.Fields{
		"eventType":     redisMsg.Event.Type,
		"correlationId": redisMsg.Metadata.CorrelationID,
		"serviceId":     redisMsg.Metadata.ServiceID,
	}).Info("Processing subscription event")

	// Parse payload
	payloadBytes, err := json.Marshal(redisMsg.Event.Payload)
	if err != nil {
		h.logger.WithError(err).Error("Failed to marshal subscription payload")
		return err
	}

	var subscriptionData struct {
		UserID    string `json:"userId"`
		Username  string `json:"username"`
		RoomID    string `json:"roomId"`
		Action    string `json:"action"`
		Timestamp string `json:"timestamp"`
	}

	if err := json.Unmarshal(payloadBytes, &subscriptionData); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription data")
		return err
	}

	// Handle subscription/unsubscription
	switch redisMsg.Event.Type {
	case "room_subscription":
		return h.handleRoomSubscription(ctx, &subscriptionData)
	case "room_unsubscription":
		return h.handleRoomUnsubscription(ctx, &subscriptionData)
	default:
		h.logger.WithField("eventType", redisMsg.Event.Type).Warn("Unknown subscription event type")
		return nil
	}
}

// handleRoomSubscription handles when a user subscribes to a room
func (h *RedisOrchestratorHandler) handleRoomSubscription(ctx context.Context, data *struct {
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	RoomID    string `json:"roomId"`
	Action    string `json:"action"`
	Timestamp string `json:"timestamp"`
}) error {
	h.logger.WithFields(logrus.Fields{
		"userId":   data.UserID,
		"username": data.Username,
		"roomId":   data.RoomID,
	}).Info("User subscribed to room")

	// Publish room info update to notify all subscribers
	err := h.publishRoomInfoUpdate(ctx, data.RoomID, "player_subscribed", map[string]interface{}{
		"userId":   data.UserID,
		"username": data.Username,
		"action":   "subscribed",
	})

	// For prize wheel games, also publish color availability update
	if err == nil {
		h.publishColorAvailabilityUpdateIfNeeded(ctx, data.RoomID, "subscription")
	}

	return err
}

// handleRoomUnsubscription handles when a user unsubscribes from a room
func (h *RedisOrchestratorHandler) handleRoomUnsubscription(ctx context.Context, data *struct {
	UserID    string `json:"userId"`
	Username  string `json:"username"`
	RoomID    string `json:"roomId"`
	Action    string `json:"action"`
	Timestamp string `json:"timestamp"`
}) error {
	h.logger.WithFields(logrus.Fields{
		"userId":   data.UserID,
		"username": data.Username,
		"roomId":   data.RoomID,
	}).Info("User unsubscribed from room")

	// Publish room info update to notify remaining subscribers
	return h.publishRoomInfoUpdate(ctx, data.RoomID, "player_unsubscribed", map[string]interface{}{
		"userId":   data.UserID,
		"username": data.Username,
		"action":   "unsubscribed",
	})
}

// publishRoomInfoUpdate publishes unified room info updates to socket-gateway
func (h *RedisOrchestratorHandler) publishRoomInfoUpdate(ctx context.Context, roomID, reason string, metadata map[string]interface{}) error {
	// Get current room info
	room, err := h.orchestrator.roomService.GetRoomInfo(ctx, roomID)
	if err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to get room info for update")
		return err
	}

	// Build unified room info structure for room-related events
	unifiedRoomInfo := h.buildUnifiedRoomInfo(room, reason, roomID, metadata)

	// Create broadcast event with unified structure
	broadcast := &BroadcastEvent{
		Type:   "room",
		Target: roomID,
		Event:  "room_info_updated",
		Data:   unifiedRoomInfo,
	}

	return h.handleBroadcast(ctx, broadcast)
}

// buildUnifiedRoomInfo creates a unified room info structure for room-related events
func (h *RedisOrchestratorHandler) buildUnifiedRoomInfo(room *models.RoomInfo, reason, roomID string, metadata map[string]interface{}) map[string]interface{} {
	timestamp := time.Now().Format(time.RFC3339)

	// Build base unified structure
	unifiedInfo := map[string]interface{}{
		"reason":    reason,
		"roomId":    roomID,
		"timestamp": timestamp,
		"room": map[string]interface{}{
			"id":              room.RoomID,
			"name":            room.Name,
			"game_type":       h.normalizeGameType(string(room.GameType)),
			"status":          h.normalizeRoomStatus(string(room.Status)),
			"current_players": room.CurrentPlayers,
			"max_players":     room.MaxPlayers,
			"min_players":     room.MinPlayers,
			"bet_amount":      room.BetAmount,
			"currency":        "USD",
			"prize_pool":      0,
			"has_password":    room.IsPrivate,
			"has_space":       room.CurrentPlayers < room.MaxPlayers,
			"is_private":      room.IsPrivate,
			"is_featured":     false,
		},
		"players": map[string]interface{}{
			"list":          h.transformPlayersList(room.Players),
			"total":         room.CurrentPlayers,
			"withColors":    0, // Will be updated if color data is available
			"colorMappings": map[string]interface{}{},
		},
		"game": map[string]interface{}{
			"type":              h.normalizeGameType(string(room.GameType)),
			"canStart":          room.CanStart,
			"allColorsSelected": false,
		},
		"metadata": metadata,
	}

	// For prize wheel games, request color data from game service
	if h.isPrizeWheelGame(string(room.GameType)) {
		h.logger.WithFields(logrus.Fields{
			"roomId":   roomID,
			"gameType": room.GameType,
			"reason":   reason,
		}).Debug("Prize wheel game detected, requesting color data from game service")

		colorData := h.requestColorDataFromGameService(roomID, reason)
		if colorData != nil {
			// Add color data to unified structure
			unifiedInfo["colors"] = colorData["colors"]

			// Update players data with color information
			if playersData, ok := colorData["players"].(map[string]interface{}); ok {
				if withColors, ok := playersData["withColors"].(int); ok {
					unifiedInfo["players"].(map[string]interface{})["withColors"] = withColors
				}
				if colorMappings, ok := playersData["colorMappings"].(map[string]interface{}); ok {
					unifiedInfo["players"].(map[string]interface{})["colorMappings"] = colorMappings
				}
			}

			// Update game data with color-specific information
			if gameData, ok := colorData["game"].(map[string]interface{}); ok {
				if allColorsSelected, ok := gameData["allColorsSelected"].(bool); ok {
					unifiedInfo["game"].(map[string]interface{})["allColorsSelected"] = allColorsSelected
				}
			}

			h.logger.WithFields(logrus.Fields{
				"roomId":     roomID,
				"hasColors":  colorData["colors"] != nil,
				"withColors": unifiedInfo["players"].(map[string]interface{})["withColors"],
			}).Debug("Successfully integrated color data into unified room info")
		} else {
			h.logger.WithField("roomId", roomID).Warn("Failed to get color data from game service")
		}
	}

	return unifiedInfo
}

// isPrizeWheelGame checks if a game type is a prize wheel game
func (h *RedisOrchestratorHandler) isPrizeWheelGame(gameType string) bool {
	if gameType == "" {
		return false
	}
	normalizedGameType := strings.ToLower(gameType)
	return normalizedGameType == "prizewheel" ||
		normalizedGameType == "prize_wheel" ||
		normalizedGameType == "game_type_prize_wheel"
}

// requestColorDataFromGameService requests color data from game service for prize wheel games
func (h *RedisOrchestratorHandler) requestColorDataFromGameService(roomID, reason string) map[string]interface{} {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// Create correlation ID for tracking
	correlationID := fmt.Sprintf("room-color-request-%d-%s", time.Now().UnixNano(), roomID)
	responseChannel := fmt.Sprintf("room:responses:%s", correlationID)

	// Build request payload
	requestPayload := map[string]interface{}{
		"roomId":        roomID,
		"reason":        reason,
		"includeColors": true,
		"requestType":   "unified_room_info",
		"timestamp":     time.Now().Format(time.RFC3339),
	}

	// Build Redis message for game service
	message := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "get_unified_room_info",
			"payload":   requestPayload,
			"timestamp": time.Now().Format(time.RFC3339),
		},
		"metadata": map[string]interface{}{
			"serviceId":       "room-service",
			"version":         "1.0.0",
			"correlationId":   correlationID,
			"responseChannel": responseChannel,
			"priority":        1,
		},
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":        roomID,
		"correlationId": correlationID,
		"reason":        reason,
	}).Debug("Requesting color data from game service")

	// Set up response channel
	responseChan := make(chan map[string]interface{}, 1)
	responseReceived := false

	// Create response handler
	responseHandler := func(ctx context.Context, channel string, message []byte) error {
		if responseReceived {
			return nil // Already received response
		}

		var response map[string]interface{}
		if err := json.Unmarshal(message, &response); err != nil {
			h.logger.WithError(err).Error("Failed to unmarshal game service response")
			return err
		}

		responseReceived = true
		responseChan <- response
		return nil
	}

	// Subscribe to response channel temporarily
	if err := h.redisClient.Subscribe(ctx, responseChannel, responseHandler); err != nil {
		h.logger.WithError(err).Error("Failed to subscribe to response channel")
		return nil
	}

	// Ensure we unsubscribe when done
	defer func() {
		if err := h.redisClient.Unsubscribe(ctx, responseChannel); err != nil {
			h.logger.WithError(err).Warn("Failed to unsubscribe from response channel")
		}
	}()

	// Publish request to game service
	messageBytes, err := json.Marshal(message)
	if err != nil {
		h.logger.WithError(err).Error("Failed to marshal game service request")
		return nil
	}

	if err := h.redisClient.Publish(ctx, "game:requests", messageBytes); err != nil {
		h.logger.WithError(err).Error("Failed to publish request to game service")
		return nil
	}

	// Wait for response with timeout
	select {
	case response := <-responseChan:
		if response != nil {
			if success, ok := response["success"].(bool); ok && success {
				if data, ok := response["data"].(map[string]interface{}); ok {
					h.logger.WithFields(logrus.Fields{
						"roomId":        roomID,
						"correlationId": correlationID,
						"hasColors":     data["colors"] != nil,
					}).Debug("Successfully received color data from game service")
					return data
				}
			}
		}
		h.logger.WithFields(logrus.Fields{
			"roomId":        roomID,
			"correlationId": correlationID,
			"response":      response,
		}).Warn("Game service returned unsuccessful response")
		return nil
	case <-ctx.Done():
		h.logger.WithFields(logrus.Fields{
			"roomId":        roomID,
			"correlationId": correlationID,
		}).Warn("Timeout waiting for color data from game service")
		return nil
	}
}

// Helper methods for room service unified structure
func (h *RedisOrchestratorHandler) normalizeGameType(gameType string) string {
	switch gameType {
	case "GAME_TYPE_PRIZE_WHEEL":
		return "prizewheel"
	case "GAME_TYPE_AMIDAKUJI":
		return "amidakuji"
	default:
		// Convert to lowercase and remove prefixes
		normalized := strings.ToLower(gameType)
		normalized = strings.ReplaceAll(normalized, "game_type_", "")
		normalized = strings.ReplaceAll(normalized, "_", "")
		if normalized == "prizewheel" || normalized == "prize_wheel" {
			return "prizewheel"
		}
		return normalized
	}
}

func (h *RedisOrchestratorHandler) normalizeRoomStatus(status string) string {
	switch status {
	case "ROOM_STATUS_WAITING":
		return "waiting"
	case "ROOM_STATUS_ACTIVE":
		return "playing"
	case "ROOM_STATUS_FINISHED":
		return "finished"
	default:
		// Convert to lowercase and remove prefixes
		normalized := strings.ToLower(status)
		normalized = strings.ReplaceAll(normalized, "room_status_", "")
		normalized = strings.ReplaceAll(normalized, "_", "")
		return normalized
	}
}

func (h *RedisOrchestratorHandler) transformPlayersList(players []models.RoomPlayer) []map[string]interface{} {
	playerList := make([]map[string]interface{}, len(players))
	for i, player := range players {
		playerList[i] = map[string]interface{}{
			"userId":    player.UserID,
			"username":  player.Username,
			"position":  player.Position,
			"isReady":   player.IsReady,
			"betAmount": player.BetAmount,
			"joinedAt":  player.JoinedAt.Format(time.RFC3339),
		}
	}
	return playerList
}

// handleJoinRoomRequest handles join room orchestration requests
func (h *RedisOrchestratorHandler) handleJoinRoomRequest(ctx context.Context, msg *RedisMessage) (*OrchestratorResponse, error) {
	// Parse payload into JoinRoomRequest
	payloadBytes, err := json.Marshal(msg.Event.Payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	var joinRequest JoinRoomRequest
	if err := json.Unmarshal(payloadBytes, &joinRequest); err != nil {
		return nil, fmt.Errorf("failed to unmarshal join request: %w", err)
	}

	// Execute orchestrated join room operation
	joinResponse, err := h.orchestrator.OrchestateJoinRoom(ctx, &joinRequest)
	if err != nil {
		return nil, fmt.Errorf("join room orchestration failed: %w", err)
	}

	return &OrchestratorResponse{
		Success:   joinResponse.Success,
		Data:      joinResponse,
		Error:     joinResponse.Error,
		Code:      joinResponse.Code,
		Broadcast: joinResponse.Broadcast,
	}, nil
}

// handleLeaveRoomRequest handles leave room orchestration requests
func (h *RedisOrchestratorHandler) handleLeaveRoomRequest(ctx context.Context, msg *RedisMessage) (*OrchestratorResponse, error) {
	// Parse payload into LeaveRoomRequest
	payloadBytes, err := json.Marshal(msg.Event.Payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	var leaveRequest LeaveRoomRequest
	if err := json.Unmarshal(payloadBytes, &leaveRequest); err != nil {
		return nil, fmt.Errorf("failed to unmarshal leave request: %w", err)
	}

	// Execute orchestrated leave room operation
	leaveResponse, err := h.orchestrator.OrchestateLeaveRoom(ctx, &leaveRequest)
	if err != nil {
		return nil, fmt.Errorf("leave room orchestration failed: %w", err)
	}

	return &OrchestratorResponse{
		Success:   leaveResponse.Success,
		Data:      leaveResponse,
		Error:     leaveResponse.Error,
		Code:      leaveResponse.Code,
		Broadcast: leaveResponse.Broadcast,
	}, nil
}

// sendResponse sends a response back to the requesting service
func (h *RedisOrchestratorHandler) sendResponse(ctx context.Context, responseChannel, correlationID string, response *OrchestratorResponse) error {
	responseMsg := RedisMessage{
		Event: RedisEvent{
			Type:      "orchestrator_response",
			Payload:   response,
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Metadata: RedisMetadata{
			ServiceID:     "room-service",
			Version:       "1.0.0",
			CorrelationID: correlationID,
		},
	}

	responseBytes, err := json.Marshal(responseMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := h.redisClient.Publish(ctx, responseChannel, responseBytes); err != nil {
		return fmt.Errorf("failed to publish response: %w", err)
	}

	h.logger.WithFields(logrus.Fields{
		"responseChannel": responseChannel,
		"correlationId":   correlationID,
		"success":         response.Success,
	}).Debug("Response sent successfully")

	return nil
}

// handleBroadcast handles broadcast events by publishing to appropriate channels
func (h *RedisOrchestratorHandler) handleBroadcast(ctx context.Context, broadcast *BroadcastEvent) error {
	var channel string

	switch broadcast.Type {
	case "room":
		// Broadcast to room-specific channel
		channel = fmt.Sprintf("room:%s:events", broadcast.Target)
	case "lobby":
		// Broadcast to lobby channel
		channel = "lobby:events"
	case "user":
		// Broadcast to user-specific channel
		channel = fmt.Sprintf("user:%s:notifications", broadcast.Target)
	case "global":
		// Broadcast to global channel
		channel = "game:global:events"
	default:
		return fmt.Errorf("unknown broadcast type: %s", broadcast.Type)
	}

	broadcastMsg := RedisMessage{
		Event: RedisEvent{
			Type:      broadcast.Event,
			Payload:   broadcast.Data,
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Metadata: RedisMetadata{
			ServiceID: "room-service",
			Version:   "1.0.0",
			Priority:  1, // High priority for broadcast events
		},
	}

	broadcastBytes, err := json.Marshal(broadcastMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal broadcast: %w", err)
	}

	if err := h.redisClient.Publish(ctx, channel, broadcastBytes); err != nil {
		return fmt.Errorf("failed to publish broadcast: %w", err)
	}

	h.logger.WithFields(logrus.Fields{
		"channel":       channel,
		"broadcastType": broadcast.Type,
		"event":         broadcast.Event,
		"target":        broadcast.Target,
	}).Info("Broadcast event published successfully")

	return nil
}

// publishColorAvailabilityUpdateIfNeeded publishes color availability update for prize wheel games
func (h *RedisOrchestratorHandler) publishColorAvailabilityUpdateIfNeeded(ctx context.Context, roomID, reason string) {
	// Get room info to check game type
	room, err := h.orchestrator.roomService.GetRoomInfo(ctx, roomID)
	if err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Debug("Failed to get room info for color availability check")
		return
	}

	// Only publish for prize wheel games
	if !h.isPrizeWheelGame(string(room.GameType)) {
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":   roomID,
		"gameType": room.GameType,
		"reason":   reason,
	}).Debug("Publishing color availability update for prize wheel game")

	// Request color data from game service
	colorData := h.requestColorDataFromGameService(roomID, reason)
	if colorData == nil {
		h.logger.WithField("roomId", roomID).Debug("No color data available for color availability update")
		return
	}

	// Extract color information
	colors, ok := colorData["colors"].(map[string]interface{})
	if !ok {
		h.logger.WithField("roomId", roomID).Debug("Invalid color data structure for availability update")
		return
	}

	// Build color availability event data
	colorAvailabilityData := map[string]interface{}{
		"roomId":    roomID,
		"timestamp": time.Now().Format(time.RFC3339),
		"colors":    colors,
		"message":   "Color availability updated after room subscription",
	}

	// Create Redis message for socket gateway
	redisMessage := map[string]interface{}{
		"event": map[string]interface{}{
			"type":      "color_availability_updated",
			"payload":   colorAvailabilityData,
			"timestamp": time.Now().Format(time.RFC3339),
		},
		"metadata": map[string]interface{}{
			"serviceId": "room-service",
			"version":   "1.0.0",
			"priority":  1,
		},
	}

	// Publish to room-specific channel
	channel := fmt.Sprintf("room:%s:events", roomID)
	messageBytes, err := json.Marshal(redisMessage)
	if err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to marshal color availability update")
		return
	}

	if err := h.redisClient.Publish(ctx, channel, messageBytes); err != nil {
		h.logger.WithError(err).WithField("roomId", roomID).Error("Failed to publish color availability update")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"roomId":    roomID,
		"reason":    reason,
		"hasColors": colors != nil,
	}).Info("Published color availability update for room subscription")
}

// Shutdown gracefully shuts down the Redis orchestrator handler
func (h *RedisOrchestratorHandler) Shutdown(ctx context.Context) error {
	h.logger.Info("Shutting down Redis Orchestrator Handler")

	// Unsubscribe from channels
	if err := h.redisClient.Unsubscribe(ctx, "room:orchestrator:requests"); err != nil {
		h.logger.WithError(err).Error("Failed to unsubscribe from orchestrator requests")
	}

	if err := h.redisClient.Unsubscribe(ctx, "room:subscription:events"); err != nil {
		h.logger.WithError(err).Error("Failed to unsubscribe from subscription events")
	}

	// Close any pending response channels
	for correlationID, responseChan := range h.pendingResponses {
		close(responseChan)
		delete(h.pendingResponses, correlationID)
	}

	h.logger.Info("Redis Orchestrator Handler shutdown complete")
	return nil
}
