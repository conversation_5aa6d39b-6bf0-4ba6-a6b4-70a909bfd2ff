package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/pkg/redis"
)

// Client interfaces for external services
type GameServiceClient interface {
	JoinRoom(ctx context.Context, request *JoinRoomRequest) (*JoinOperationResult, error)
	LeaveRoom(ctx context.Context, request *LeaveRoomRequest) (*LeaveOperationResult, error)
}

type ManagerServiceClient interface {
	GetUserBalance(ctx context.Context, userID string) (float64, error)
	GetUserStatus(ctx context.Context, userID string) (*UserStatus, error)
	GetUserSession(ctx context.Context, userID, roomID string) (*UserSession, error)
	UpdateUserBalance(ctx context.Context, userID string, amount float64, operation string) (*BalanceUpdateResult, error)
	RefundUserBalance(ctx context.Context, userID string, amount float64, reason string) (*BalanceUpdateResult, error)
	GetRoom(ctx context.Context, roomID string) (*models.Room, error)
	JoinRoom(ctx context.Context, userID, username, roomID string, betAmount float64) error
	LeaveRoom(ctx context.Context, userID, roomID string) error
}

type NotificationClient interface {
	SendJoinNotification(ctx context.Context, userID, username, roomID string, room *models.Room) error
	SendLeaveNotification(ctx context.Context, userID, username, roomID string, room *models.Room) error
}

// Supporting types for external service responses
type UserStatus struct {
	Banned    bool `json:"banned"`
	Suspended bool `json:"suspended"`
}

type UserSession struct {
	Active bool   `json:"active"`
	RoomID string `json:"roomId"`
}

// RoomOrchestrator handles complex room operations that require coordination
// across multiple services. This is where business logic from Socket Gateway moves.
type RoomOrchestrator struct {
	roomService          RoomService
	gameServiceClient    GameServiceClient
	managerServiceClient ManagerServiceClient
	notificationClient   NotificationClient
	redisClient          *redis.RedisClient
	config               *config.Config
	logger               *logrus.Logger

	// Operation mutex to prevent race conditions
	userOperationMutex map[string]string      // userId -> operation type
	operationTimeouts  map[string]*time.Timer // userId -> timeout timer
	mutex              sync.RWMutex
	operationTimeout   time.Duration
}

// NewRoomOrchestrator creates a new room orchestrator instance
func NewRoomOrchestrator(
	roomService RoomService,
	gameServiceClient GameServiceClient,
	managerServiceClient ManagerServiceClient,
	notificationClient NotificationClient,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
) *RoomOrchestrator {
	return &RoomOrchestrator{
		roomService:          roomService,
		gameServiceClient:    gameServiceClient,
		managerServiceClient: managerServiceClient,
		notificationClient:   notificationClient,
		redisClient:          redisClient,
		config:               config,
		logger:               logger,
		userOperationMutex:   make(map[string]string),
		operationTimeouts:    make(map[string]*time.Timer),
		operationTimeout:     30 * time.Second,
	}
}

// JoinRoomRequest represents a join room orchestration request
type JoinRoomRequest struct {
	UserID    string  `json:"userId"`
	Username  string  `json:"username"`
	RoomID    string  `json:"roomId"`
	Password  string  `json:"password,omitempty"`
	BetAmount float64 `json:"betAmount"`
	SocketID  string  `json:"socketId,omitempty"`
}

// JoinRoomResponse represents the orchestrated join room response
type JoinRoomResponse struct {
	Success      bool               `json:"success"`
	Room         *models.Room       `json:"room,omitempty"`
	Player       *models.RoomPlayer `json:"player,omitempty"`
	Balance      float64            `json:"balance,omitempty"`
	Reconnection bool               `json:"reconnection,omitempty"`
	Error        string             `json:"error,omitempty"`
	Code         string             `json:"code,omitempty"`
	Broadcast    *BroadcastEvent    `json:"broadcast,omitempty"`
}

// LeaveRoomRequest represents a leave room orchestration request
type LeaveRoomRequest struct {
	UserID   string `json:"userId"`
	Username string `json:"username"`
	RoomID   string `json:"roomId"`
	SocketID string `json:"socketId,omitempty"`
}

// LeaveRoomResponse represents the orchestrated leave room response
type LeaveRoomResponse struct {
	Success   bool            `json:"success"`
	Room      *models.Room    `json:"room,omitempty"`
	Balance   float64         `json:"balance,omitempty"`
	Error     string          `json:"error,omitempty"`
	Code      string          `json:"code,omitempty"`
	Broadcast *BroadcastEvent `json:"broadcast,omitempty"`
}

// BroadcastEvent represents an event that should be broadcasted
type BroadcastEvent struct {
	Type   string      `json:"type"`   // "room", "lobby", "user", "global"
	Target string      `json:"target"` // roomId, userId, etc.
	Event  string      `json:"event"`  // event name
	Data   interface{} `json:"data"`   // event data
}

// OrchestateJoinRoom handles the complete join room workflow
func (ro *RoomOrchestrator) OrchestateJoinRoom(ctx context.Context, request *JoinRoomRequest) (*JoinRoomResponse, error) {
	// Step 1: Acquire operation lock to prevent race conditions
	if !ro.acquireOperationLock(request.UserID, "join_room") {
		ro.logger.WithFields(logrus.Fields{
			"userId": request.UserID,
			"roomId": request.RoomID,
		}).Warn("Join room operation already in progress")

		return &JoinRoomResponse{
			Success: false,
			Error:   "Another room operation is already in progress for this user",
			Code:    "OPERATION_IN_PROGRESS",
		}, nil
	}
	defer ro.releaseOperationLock(request.UserID, "completed")

	// Step 2: Validate user eligibility
	userValidation, err := ro.validateUserForJoinRoom(ctx, request.UserID, request.BetAmount)
	if err != nil {
		ro.logger.WithError(err).Error("User validation failed")
		return &JoinRoomResponse{
			Success: false,
			Error:   "User validation failed",
			Code:    "VALIDATION_ERROR",
		}, err
	}
	if !userValidation.Valid {
		return &JoinRoomResponse{
			Success: false,
			Error:   userValidation.Error,
			Code:    userValidation.Code,
		}, nil
	}

	// Step 3: Validate room eligibility
	roomValidation, err := ro.validateRoomForJoin(ctx, request.RoomID, request.Password)
	if err != nil {
		ro.logger.WithError(err).Error("Room validation failed")
		return &JoinRoomResponse{
			Success: false,
			Error:   "Room validation failed",
			Code:    "VALIDATION_ERROR",
		}, err
	}
	if !roomValidation.Valid {
		return &JoinRoomResponse{
			Success: false,
			Error:   roomValidation.Error,
			Code:    roomValidation.Code,
		}, nil
	}

	// Step 4: Check for existing session (reconnection)
	existingSession, err := ro.checkExistingSession(ctx, request.UserID, request.RoomID)
	if err != nil {
		ro.logger.WithError(err).Error("Failed to check existing session")
		// Continue with join process even if session check fails
	}
	if existingSession != nil && existingSession.Active {
		ro.logger.WithFields(logrus.Fields{
			"userId": request.UserID,
			"roomId": request.RoomID,
		}).Info("User reconnecting to existing session")

		return &JoinRoomResponse{
			Success:      true,
			Room:         existingSession.Room,
			Player:       existingSession.Player,
			Balance:      userValidation.Balance,
			Reconnection: true,
			Broadcast: &BroadcastEvent{
				Type:   "room",
				Target: request.RoomID,
				Event:  "player_reconnected",
				Data: map[string]interface{}{
					"userId":   request.UserID,
					"username": request.Username,
					"roomId":   request.RoomID,
				},
			},
		}, nil
	}

	// Step 5: Execute join operation across services
	joinResult, err := ro.executeJoinRoomOperation(ctx, request, roomValidation.Room)
	if err != nil {
		ro.logger.WithError(err).Error("Join room operation failed")
		return &JoinRoomResponse{
			Success: false,
			Error:   "Failed to join room",
			Code:    "JOIN_OPERATION_FAILED",
		}, err
	}

	// Step 6: Update user balance
	balanceResult, err := ro.updateUserBalanceForJoin(ctx, request.UserID, request.BetAmount)
	if err != nil {
		// Rollback join operation
		ro.rollbackJoinOperation(ctx, request.UserID, request.RoomID, "balance_update_failed")
		ro.logger.WithError(err).Error("Balance update failed, rolling back join")
		return &JoinRoomResponse{
			Success: false,
			Error:   "Balance update failed",
			Code:    "BALANCE_UPDATE_FAILED",
		}, err
	}

	// Step 7: Send notifications
	go ro.sendJoinNotifications(ctx, request, joinResult.Room)

	ro.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"username": request.Username,
	}).Info("Join room orchestration completed successfully")

	return &JoinRoomResponse{
		Success: true,
		Room:    joinResult.Room,
		Player:  joinResult.Player,
		Balance: balanceResult.NewBalance,
		Broadcast: &BroadcastEvent{
			Type:   "room",
			Target: request.RoomID,
			Event:  "room_info_updated",
			Data: map[string]interface{}{
				"action": "player_joined",
				"roomId": request.RoomID,
				"player": joinResult.Player,
				"room":   joinResult.Room,
			},
		},
	}, nil
}

// OrchestateLeaveRoom handles the complete leave room workflow
func (ro *RoomOrchestrator) OrchestateLeaveRoom(ctx context.Context, request *LeaveRoomRequest) (*LeaveRoomResponse, error) {
	// Step 1: Acquire operation lock
	if !ro.acquireOperationLock(request.UserID, "leave_room") {
		ro.logger.WithFields(logrus.Fields{
			"userId": request.UserID,
			"roomId": request.RoomID,
		}).Warn("Leave room operation already in progress")

		return &LeaveRoomResponse{
			Success: false,
			Error:   "Another room operation is already in progress for this user",
			Code:    "OPERATION_IN_PROGRESS",
		}, nil
	}
	defer ro.releaseOperationLock(request.UserID, "completed")

	// Step 2: Validate user is in room
	userInRoom, err := ro.validateUserInRoom(ctx, request.UserID, request.RoomID)
	if err != nil {
		ro.logger.WithError(err).Error("Failed to validate user in room")
		return &LeaveRoomResponse{
			Success: false,
			Error:   "Validation failed",
			Code:    "VALIDATION_ERROR",
		}, err
	}
	if !userInRoom.Valid {
		return &LeaveRoomResponse{
			Success: false,
			Error:   userInRoom.Error,
			Code:    userInRoom.Code,
		}, nil
	}

	// Step 3: Execute leave operation across services
	leaveResult, err := ro.executeLeaveRoomOperation(ctx, request)
	if err != nil {
		ro.logger.WithError(err).Error("Leave room operation failed")
		return &LeaveRoomResponse{
			Success: false,
			Error:   "Failed to leave room",
			Code:    "LEAVE_OPERATION_FAILED",
		}, err
	}

	// Step 4: Handle balance refund if needed
	balanceResult, err := ro.handleBalanceRefund(ctx, request.UserID, userInRoom.Session)
	if err != nil {
		ro.logger.WithError(err).Warn("Balance refund failed but continuing with leave")
		// Don't fail the leave operation due to balance issues
	}

	// Step 5: Send notifications
	go ro.sendLeaveNotifications(ctx, request, leaveResult.Room)

	ro.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"username": request.Username,
	}).Info("Leave room orchestration completed successfully")

	balance := 0.0
	if balanceResult != nil {
		balance = balanceResult.NewBalance
	}

	return &LeaveRoomResponse{
		Success: true,
		Room:    leaveResult.Room,
		Balance: balance,
		Broadcast: &BroadcastEvent{
			Type:   "room",
			Target: request.RoomID,
			Event:  "room_info_updated",
			Data: map[string]interface{}{
				"action": "player_left",
				"roomId": request.RoomID,
				"player": map[string]interface{}{
					"userId":   request.UserID,
					"username": request.Username,
				},
				"room": leaveResult.Room,
			},
		},
	}, nil
}

// acquireOperationLock acquires an operation lock for a user
func (ro *RoomOrchestrator) acquireOperationLock(userID, operation string) bool {
	ro.mutex.Lock()
	defer ro.mutex.Unlock()

	if _, exists := ro.userOperationMutex[userID]; exists {
		return false
	}

	ro.userOperationMutex[userID] = operation

	// Set timeout to auto-release lock
	timer := time.AfterFunc(ro.operationTimeout, func() {
		ro.releaseOperationLock(userID, "timeout")
	})
	ro.operationTimeouts[userID] = timer

	return true
}

// releaseOperationLock releases an operation lock for a user
func (ro *RoomOrchestrator) releaseOperationLock(userID, reason string) {
	ro.mutex.Lock()
	defer ro.mutex.Unlock()

	delete(ro.userOperationMutex, userID)

	if timer, exists := ro.operationTimeouts[userID]; exists {
		timer.Stop()
		delete(ro.operationTimeouts, userID)
	}

	ro.logger.WithFields(logrus.Fields{
		"userId": userID,
		"reason": reason,
	}).Debug("Operation lock released")
}

// ValidationResult represents the result of a validation operation
type ValidationResult struct {
	Valid   bool         `json:"valid"`
	Error   string       `json:"error,omitempty"`
	Code    string       `json:"code,omitempty"`
	Balance float64      `json:"balance,omitempty"`
	Room    *models.Room `json:"room,omitempty"`
}

// SessionInfo represents information about an existing session
type SessionInfo struct {
	Active bool               `json:"active"`
	Room   *models.Room       `json:"room,omitempty"`
	Player *models.RoomPlayer `json:"player,omitempty"`
}

// JoinOperationResult represents the result of a join operation
type JoinOperationResult struct {
	Room   *models.Room       `json:"room"`
	Player *models.RoomPlayer `json:"player"`
}

// LeaveOperationResult represents the result of a leave operation
type LeaveOperationResult struct {
	Room *models.Room `json:"room"`
}

// BalanceUpdateResult represents the result of a balance update
type BalanceUpdateResult struct {
	NewBalance float64 `json:"newBalance"`
}

// UserInRoomResult represents the result of checking if user is in room
type UserInRoomResult struct {
	Valid   bool        `json:"valid"`
	Error   string      `json:"error,omitempty"`
	Code    string      `json:"code,omitempty"`
	Session interface{} `json:"session,omitempty"`
}

// validateUserForJoinRoom validates if user can join a room
func (ro *RoomOrchestrator) validateUserForJoinRoom(ctx context.Context, userID string, betAmount float64) (*ValidationResult, error) {
	// Check user balance via Manager Service
	balance, err := ro.managerServiceClient.GetUserBalance(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user balance: %w", err)
	}

	if balance < betAmount {
		return &ValidationResult{
			Valid: false,
			Error: "Insufficient balance",
			Code:  "INSUFFICIENT_BALANCE",
		}, nil
	}

	// Check user status via Manager Service
	userStatus, err := ro.managerServiceClient.GetUserStatus(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user status: %w", err)
	}

	if userStatus.Banned || userStatus.Suspended {
		return &ValidationResult{
			Valid: false,
			Error: "User account restricted",
			Code:  "ACCOUNT_RESTRICTED",
		}, nil
	}

	return &ValidationResult{
		Valid:   true,
		Balance: balance,
	}, nil
}

// validateRoomForJoin validates if room can be joined
func (ro *RoomOrchestrator) validateRoomForJoin(ctx context.Context, roomID, password string) (*ValidationResult, error) {
	room, err := ro.managerServiceClient.GetRoom(ctx, roomID)
	if err != nil {
		return nil, fmt.Errorf("failed to get room: %w", err)
	}

	if room == nil {
		return &ValidationResult{
			Valid: false,
			Error: "Room not found",
			Code:  "ROOM_NOT_FOUND",
		}, nil
	}

	// Check room capacity
	if len(room.Players) >= int(room.MaxPlayers) {
		return &ValidationResult{
			Valid: false,
			Error: "Room is full",
			Code:  "ROOM_FULL",
		}, nil
	}

	// Check room status
	if room.Status == models.RoomStatusActive {
		return &ValidationResult{
			Valid: false,
			Error: "Game already in progress",
			Code:  "GAME_IN_PROGRESS",
		}, nil
	}

	// Check password for private rooms
	if room.Configuration.IsPrivate && room.Configuration.Password != password {
		return &ValidationResult{
			Valid: false,
			Error: "Invalid room password",
			Code:  "INVALID_PASSWORD",
		}, nil
	}

	return &ValidationResult{
		Valid: true,
		Room:  room,
	}, nil
}

// checkExistingSession checks if user has an existing session in the room
func (ro *RoomOrchestrator) checkExistingSession(ctx context.Context, userID, roomID string) (*SessionInfo, error) {
	// Check via Manager Service for existing sessions
	session, err := ro.managerServiceClient.GetUserSession(ctx, userID, roomID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing session: %w", err)
	}

	if session == nil || !session.Active {
		return &SessionInfo{Active: false}, nil
	}

	// Get current room state
	room, err := ro.managerServiceClient.GetRoom(ctx, roomID)
	if err != nil {
		return nil, fmt.Errorf("failed to get room for reconnection: %w", err)
	}

	// Find player in room
	var player *models.RoomPlayer
	for _, p := range room.Players {
		if p.UserID == userID {
			player = &p
			break
		}
	}

	return &SessionInfo{
		Active: true,
		Room:   room,
		Player: player,
	}, nil
}

// executeJoinRoomOperation executes the actual join room operation
func (ro *RoomOrchestrator) executeJoinRoomOperation(ctx context.Context, request *JoinRoomRequest, room *models.Room) (*JoinOperationResult, error) {
	// Call manager service to join the room
	err := ro.managerServiceClient.JoinRoom(ctx, request.UserID, request.Username, request.RoomID, request.BetAmount)
	if err != nil {
		return nil, fmt.Errorf("failed to join room via manager service: %w", err)
	}

	// Get updated room state from manager service
	updatedRoom, err := ro.managerServiceClient.GetRoom(ctx, request.RoomID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated room state: %w", err)
	}

	// Create a player object for the response
	player := &models.RoomPlayer{
		UserID:    request.UserID,
		Username:  request.Username,
		BetAmount: int64(request.BetAmount),
		Position:  updatedRoom.CurrentPlayers, // Simple position assignment
		IsReady:   false,
		Status:    models.PlayerStatusActive,
		JoinedAt:  time.Now(),
	}

	return &JoinOperationResult{
		Room:   updatedRoom,
		Player: player,
	}, nil
}

// executeLeaveRoomOperation executes the actual leave room operation
func (ro *RoomOrchestrator) executeLeaveRoomOperation(ctx context.Context, request *LeaveRoomRequest) (*LeaveOperationResult, error) {
	// Call manager service to leave the room
	err := ro.managerServiceClient.LeaveRoom(ctx, request.UserID, request.RoomID)
	if err != nil {
		return nil, fmt.Errorf("failed to leave room via manager service: %w", err)
	}

	// Get updated room state from manager service
	updatedRoom, err := ro.managerServiceClient.GetRoom(ctx, request.RoomID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated room state: %w", err)
	}

	return &LeaveOperationResult{
		Room: updatedRoom,
	}, nil
}

// updateUserBalanceForJoin updates user balance for join operation
func (ro *RoomOrchestrator) updateUserBalanceForJoin(ctx context.Context, userID string, betAmount float64) (*BalanceUpdateResult, error) {
	result, err := ro.managerServiceClient.UpdateUserBalance(ctx, userID, -betAmount, "room_join")
	if err != nil {
		return nil, fmt.Errorf("failed to update user balance: %w", err)
	}

	return result, nil
}

// handleBalanceRefund handles balance refund for leave operation
func (ro *RoomOrchestrator) handleBalanceRefund(ctx context.Context, userID string, session interface{}) (*BalanceUpdateResult, error) {
	// For now, we don't refund on leave - this would depend on game rules
	// In a real implementation, you might refund based on game state
	return &BalanceUpdateResult{NewBalance: 0}, nil
}

// rollbackJoinOperation rolls back a join operation
func (ro *RoomOrchestrator) rollbackJoinOperation(ctx context.Context, userID, roomID, reason string) {
	ro.logger.WithFields(logrus.Fields{
		"userId": userID,
		"roomId": roomID,
		"reason": reason,
	}).Warn("Rolling back join operation")

	// Attempt to remove player from room
	leaveRequest := &models.LeaveRoomRequest{
		RoomID: roomID,
		UserID: userID,
	}

	_, err := ro.roomService.LeaveRoom(ctx, leaveRequest)
	if err != nil {
		ro.logger.WithError(err).Error("Failed to rollback join operation")
	}
}

// sendJoinNotifications sends notifications for join operation
func (ro *RoomOrchestrator) sendJoinNotifications(ctx context.Context, request *JoinRoomRequest, room *models.Room) {
	err := ro.notificationClient.SendJoinNotification(ctx, request.UserID, request.Username, request.RoomID, room)
	if err != nil {
		ro.logger.WithError(err).Error("Failed to send join notification")
	}
}

// sendLeaveNotifications sends notifications for leave operation
func (ro *RoomOrchestrator) sendLeaveNotifications(ctx context.Context, request *LeaveRoomRequest, room *models.Room) {
	err := ro.notificationClient.SendLeaveNotification(ctx, request.UserID, request.Username, request.RoomID, room)
	if err != nil {
		ro.logger.WithError(err).Error("Failed to send leave notification")
	}
}

// validateUserInRoom validates if user is currently in the room
func (ro *RoomOrchestrator) validateUserInRoom(ctx context.Context, userID, roomID string) (*UserInRoomResult, error) {
	room, err := ro.managerServiceClient.GetRoom(ctx, roomID)
	if err != nil {
		return nil, fmt.Errorf("failed to get room: %w", err)
	}

	if room == nil {
		return &UserInRoomResult{
			Valid: false,
			Error: "Room not found",
			Code:  "ROOM_NOT_FOUND",
		}, nil
	}

	// For our simplified implementation, we'll assume the user is in the room
	// if the room has current players > 0. In a real implementation, you would
	// track individual players and check if the specific user is in the room.
	if room.CurrentPlayers <= 0 {
		return &UserInRoomResult{
			Valid: false,
			Error: "Room is empty",
			Code:  "ROOM_EMPTY",
		}, nil
	}

	// In a real implementation, you would check if the specific user is in the room
	// For now, we'll assume they are if the room has players
	return &UserInRoomResult{
		Valid: true,
	}, nil
}
