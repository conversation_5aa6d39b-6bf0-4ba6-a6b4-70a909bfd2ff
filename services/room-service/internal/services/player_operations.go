package services

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/config"
	"github.com/xzgame/room-service/internal/models"
	"github.com/xzgame/room-service/internal/repositories"
	"github.com/xzgame/room-service/pkg/redis"
)

// PlayerOperations handles player-related room operations
type PlayerOperations struct {
	roomRepo    repositories.RoomRepository
	redisClient *redis.RedisClient
	config      *config.Config
	logger      *logrus.Logger
	roomManager *RoomManager
	seatManager *SeatManager
}

// NewPlayerOperations creates a new player operations instance
func NewPlayerOperations(
	roomRepo repositories.RoomRepository,
	redisClient *redis.RedisClient,
	config *config.Config,
	logger *logrus.Logger,
	roomManager *RoomManager,
) *PlayerOperations {
	return &PlayerOperations{
		roomRepo:    roomRepo,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
		roomManager: roomManager,
		seatManager: NewSeatManager(logger),
	}
}

// Join<PERSON><PERSON> adds a player to a room
func (po *PlayerOperations) JoinRoom(ctx context.Context, request *models.JoinRoomRequest) (*models.Room, error) {
	po.logger.WithFields(logrus.Fields{
		"room_id":    request.RoomID,
		"user_id":    request.UserID,
		"username":   request.Username,
		"bet_amount": request.BetAmount,
	}).Info("Player joining room")

	// Get and validate room
	room, err := po.roomManager.GetRoom(ctx, request.RoomID, false)
	if err != nil {
		return nil, err
	}

	// Validate join eligibility
	if err := po.validateJoinEligibility(ctx, room, request); err != nil {
		return nil, err
	}

	// Execute the join operation
	return po.executePlayerJoin(ctx, room, request)
}

// LeaveRoom removes a player from a room
func (po *PlayerOperations) LeaveRoom(ctx context.Context, request *models.LeaveRoomRequest) (*models.Room, error) {
	po.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
	}).Info("Player leaving room")

	// Get room
	room, err := po.roomManager.GetRoom(ctx, request.RoomID, false)
	if err != nil {
		return nil, err
	}

	// Check if player is in room
	playerIndex := -1
	for i, player := range room.Players {
		if player.UserID == request.UserID {
			playerIndex = i
			break
		}
	}

	if playerIndex == -1 {
		return nil, models.NewRoomError(
			models.ErrorCodePlayerNotInRoom,
			"player not found in room",
			request.RoomID,
		)
	}

	// Use seat manager to remove player and reassign positions
	updatedSeats, err := po.seatManager.RemoveSeat(ctx, request.RoomID, request.UserID, room.Players, true)
	if err != nil {
		po.logger.WithError(err).Error("Failed to remove player seat")
		return nil, err
	}

	room.Players = updatedSeats
	room.CurrentPlayers = len(room.Players)
	room.LastActivity = time.Now()
	room.UpdatedAt = time.Now()

	// Update room status if needed
	if room.CurrentPlayers == 0 {
		room.Status = models.RoomStatusWaiting
	}

	// Save to database
	updateMap := map[string]interface{}{
		"players":         room.Players,
		"current_players": room.CurrentPlayers,
		"status":          room.Status,
		"last_activity":   room.LastActivity,
		"updated_at":      room.UpdatedAt,
	}

	err = po.roomRepo.UpdateRoom(ctx, request.RoomID, updateMap)
	if err != nil {
		po.logger.WithError(err).Error("Failed to update room after player leave")
		return nil, err
	}

	// Update cache
	if err := po.roomManager.cacheRoom(ctx, room); err != nil {
		po.logger.WithError(err).Warn("Failed to cache room after player leave")
	}

	po.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
	}).Info("Player left room successfully")

	return room, nil
}

// SetPlayerReady sets a player's ready status
func (po *PlayerOperations) SetPlayerReady(ctx context.Context, request *models.SetPlayerReadyRequest) (*models.Room, error) {
	po.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
		"ready":   request.Ready,
	}).Info("Setting player ready status")

	// Get room
	room, err := po.roomManager.GetRoom(ctx, request.RoomID, false)
	if err != nil {
		return nil, err
	}

	// Find and update player
	playerFound := false
	for i, player := range room.Players {
		if player.UserID == request.UserID {
			room.Players[i].IsReady = request.Ready
			playerFound = true
			break
		}
	}

	if !playerFound {
		return nil, models.NewRoomError(
			models.ErrorCodePlayerNotInRoom,
			"player not found in room",
			request.RoomID,
		)
	}

	room.LastActivity = time.Now()
	room.UpdatedAt = time.Now()

	// Check if all players are ready and room can start
	if po.canStartGame(room) {
		room.Status = models.RoomStatusActive
	}

	// Save to database
	updateMap := map[string]interface{}{
		"players":       room.Players,
		"status":        room.Status,
		"last_activity": room.LastActivity,
		"updated_at":    room.UpdatedAt,
	}

	err = po.roomRepo.UpdateRoom(ctx, request.RoomID, updateMap)
	if err != nil {
		po.logger.WithError(err).Error("Failed to update room after setting player ready")
		return nil, err
	}

	// Update cache
	if err := po.roomManager.cacheRoom(ctx, room); err != nil {
		po.logger.WithError(err).Warn("Failed to cache room after setting player ready")
	}

	po.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
		"ready":   request.Ready,
	}).Info("Player ready status updated successfully")

	return room, nil
}

// GetRoomsByPlayer gets all rooms where a specific player is present
func (po *PlayerOperations) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	po.logger.WithField("user_id", userID).Debug("Getting rooms by player")

	rooms, err := po.roomRepo.GetRoomsByPlayer(ctx, userID)
	if err != nil {
		po.logger.WithError(err).WithField("user_id", userID).Error("Failed to get rooms by player")
		return nil, err
	}

	po.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"room_count": len(rooms),
	}).Debug("Successfully retrieved rooms by player")

	return rooms, nil
}

// validateJoinEligibility validates if a player can join a room
func (po *PlayerOperations) validateJoinEligibility(ctx context.Context, room *models.Room, request *models.JoinRoomRequest) error {
	_ = ctx // Parameter required by interface
	// Check room status
	if room.Status != models.RoomStatusWaiting {
		return models.NewRoomError(
			models.ErrorCodeRoomNotWaiting,
			"room is not accepting new players",
			request.RoomID,
		)
	}

	// Check room capacity
	if room.CurrentPlayers >= room.MaxPlayers {
		return models.NewRoomError(
			models.ErrorCodeRoomFull,
			"room is full",
			request.RoomID,
		)
	}

	// Check if player is already in room
	for _, player := range room.Players {
		if player.UserID == request.UserID {
			return models.NewRoomError(
				models.ErrorCodePlayerAlreadyInRoom,
				"player already in room",
				request.RoomID,
			)
		}
	}

	// Check password for private rooms
	if room.Configuration.IsPrivate && room.Configuration.Password != request.Password {
		return models.NewRoomError(
			models.ErrorCodeInvalidPassword,
			"invalid room password",
			request.RoomID,
		)
	}

	// Check bet amount
	if request.BetAmount < room.Configuration.BetLimits.MinBet ||
		request.BetAmount > room.Configuration.BetLimits.MaxBet {
		return models.NewRoomError(
			models.ErrorCodeInvalidBetAmount,
			"bet amount outside allowed range",
			request.RoomID,
		)
	}

	return nil
}

// executePlayerJoin executes the player join operation
func (po *PlayerOperations) executePlayerJoin(ctx context.Context, room *models.Room, request *models.JoinRoomRequest) (*models.Room, error) {
	// Check if player is already in the room to prevent duplicates
	for _, existingPlayer := range room.Players {
		if existingPlayer.UserID == request.UserID {
			po.logger.WithFields(logrus.Fields{
				"room_id": request.RoomID,
				"user_id": request.UserID,
			}).Info("Player already in room, returning existing room state")
			return room, nil
		}
	}

	// Use seat manager to assign seat
	seatRequest := &SeatAssignmentRequest{
		RoomID:       request.RoomID,
		UserID:       request.UserID,
		Username:     request.Username,
		CurrentSeats: room.Players,
		MaxPlayers:   room.MaxPlayers,
	}

	seatResult, err := po.seatManager.AssignSeat(ctx, seatRequest)
	if err != nil {
		po.logger.WithError(err).Error("Failed to assign seat")
		return nil, err
	}

	// Handle reconnection case
	if seatResult.IsReconnection {
		po.logger.WithFields(logrus.Fields{
			"room_id":  request.RoomID,
			"user_id":  request.UserID,
			"position": seatResult.Position,
		}).Info("Player reconnecting to existing seat")
		return room, nil
	}

	// Create new player
	newPlayer := models.RoomPlayer{
		UserID:    request.UserID,
		Username:  request.Username,
		Position:  seatResult.Position,
		IsReady:   false,
		BetAmount: request.BetAmount,
		JoinedAt:  time.Now(),
		Status:    models.PlayerStatusActive,
	}

	// Add player to room
	room.Players = append(room.Players, newPlayer)
	room.CurrentPlayers = len(room.Players)
	room.LastActivity = time.Now()
	room.UpdatedAt = time.Now()

	// Update room status if needed
	if room.CurrentPlayers >= room.MaxPlayers {
		room.Status = models.RoomStatusFull
	}

	// Save to database
	updateMap := map[string]interface{}{
		"players":         room.Players,
		"current_players": room.CurrentPlayers,
		"status":          room.Status,
		"last_activity":   room.LastActivity,
		"updated_at":      room.UpdatedAt,
	}

	err = po.roomRepo.UpdateRoom(ctx, request.RoomID, updateMap)
	if err != nil {
		po.logger.WithError(err).Error("Failed to update room after player join")
		return nil, err
	}

	// Update cache
	if err := po.roomManager.cacheRoom(ctx, room); err != nil {
		po.logger.WithError(err).Warn("Failed to cache room after player join")
	}

	po.logger.WithFields(logrus.Fields{
		"room_id": request.RoomID,
		"user_id": request.UserID,
	}).Info("Player joined room successfully")

	return room, nil
}

// canStartGame checks if a room can start a game
func (po *PlayerOperations) canStartGame(room *models.Room) bool {
	// Check minimum players
	if room.CurrentPlayers < room.MinPlayers {
		return false
	}

	// Check if auto-start is enabled
	if !room.Configuration.AutoStart {
		return false
	}

	// Check if all players are ready
	for _, player := range room.Players {
		if !player.IsReady {
			return false
		}
	}

	return true
}

// calculateNextAvailablePosition finds the next available position using gap-filling algorithm
func (po *PlayerOperations) calculateNextAvailablePosition(players []models.RoomPlayer) int {
	if len(players) == 0 {
		return 0 // First player gets position 0
	}

	// Get all used positions
	usedPositions := make(map[int]bool)
	for _, player := range players {
		usedPositions[player.Position] = true
	}

	// Find first available position starting from 0
	position := 0
	for usedPositions[position] {
		position++
	}

	po.logger.WithFields(logrus.Fields{
		"existing_players": len(players),
		"used_positions":   getUsedPositionsList(usedPositions),
		"next_position":    position,
	}).Debug("Calculated next available position")

	return position
}

// getUsedPositionsList converts used positions map to sorted slice for logging
func getUsedPositionsList(usedPositions map[int]bool) []int {
	positions := make([]int, 0, len(usedPositions))
	for pos := range usedPositions {
		positions = append(positions, pos)
	}

	// Simple sort for small arrays
	for i := 0; i < len(positions); i++ {
		for j := i + 1; j < len(positions); j++ {
			if positions[i] > positions[j] {
				positions[i], positions[j] = positions[j], positions[i]
			}
		}
	}

	return positions
}
