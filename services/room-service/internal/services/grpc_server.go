package services

import (
	"context"

	"github.com/sirupsen/logrus"
	"github.com/xzgame/room-service/internal/models"
	pb "github.com/xzgame/room-service/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type GRPCServer struct {
	pb.UnimplementedRoomServiceServer
	roomService RoomService
	logger      *logrus.Logger
}

func NewGRPCServer(roomService RoomService, logger *logrus.Logger) *GRPCServer {
	return &GRPCServer{
		roomService: roomService,
		logger:      logger,
	}
}

// GetRoomInfo gets comprehensive room information for broadcasting
func (s *GRPCServer) GetRoomInfo(ctx context.Context, req *pb.GetRoomInfoRequest) (*pb.GetRoomInfoResponse, error) {
	s.logger.WithField("room_id", req.RoomId).Debug("Getting room info")

	roomInfo, err := s.roomService.GetRoomInfo(ctx, req.RoomId)
	if err != nil {
		s.logger.WithError(err).WithField("room_id", req.RoomId).Error("Failed to get room info")
		return &pb.GetRoomInfoResponse{
			Error: err.Error(),
		}, nil
	}

	// Convert internal model to protobuf
	pbRoomInfo := &pb.RoomInfo{
		RoomId:         roomInfo.RoomID,
		Name:           roomInfo.Name,
		GameType:       convertGameTypeToProto(roomInfo.GameType),
		Status:         convertRoomStatusToProto(roomInfo.Status),
		CurrentPlayers: int32(roomInfo.CurrentPlayers),
		MaxPlayers:     int32(roomInfo.MaxPlayers),
		MinPlayers:     int32(roomInfo.MinPlayers),
		BetAmount:      roomInfo.BetAmount,
		Players:        convertPlayersToProto(roomInfo.Players),
		IsPrivate:      roomInfo.IsPrivate,
		AutoStart:      roomInfo.AutoStart,
		CreatedAt:      timestamppb.New(roomInfo.CreatedAt),
		LastActivity:   timestamppb.New(roomInfo.LastActivity),
		CanJoin:        roomInfo.CanJoin,
		CanStart:       roomInfo.CanStart,
	}

	return &pb.GetRoomInfoResponse{
		RoomInfo: pbRoomInfo,
	}, nil
}

// Helper functions for converting between internal models and protobuf

func convertGameTypeToProto(gameType models.GameType) pb.GameType {
	switch gameType {
	case models.GameTypePrizeWheel:
		return pb.GameType_GAME_TYPE_PRIZE_WHEEL
	case models.GameTypeAmidakuji:
		return pb.GameType_GAME_TYPE_AMIDAKUJI
	default:
		return pb.GameType_GAME_TYPE_UNSPECIFIED
	}
}

func convertRoomStatusToProto(status models.RoomStatus) pb.RoomStatus {
	switch status {
	case models.RoomStatusWaiting:
		return pb.RoomStatus_ROOM_STATUS_WAITING
	case models.RoomStatusActive:
		return pb.RoomStatus_ROOM_STATUS_ACTIVE
	case models.RoomStatusFull:
		return pb.RoomStatus_ROOM_STATUS_FULL
	case models.RoomStatusClosed:
		return pb.RoomStatus_ROOM_STATUS_CLOSED
	case models.RoomStatusArchived:
		return pb.RoomStatus_ROOM_STATUS_ARCHIVED
	default:
		return pb.RoomStatus_ROOM_STATUS_UNSPECIFIED
	}
}

func convertPlayersToProto(players []models.RoomPlayer) []*pb.RoomPlayer {
	pbPlayers := make([]*pb.RoomPlayer, len(players))
	for i, player := range players {
		pbPlayers[i] = &pb.RoomPlayer{
			UserId:    player.UserID,
			Username:  player.Username,
			Position:  int32(player.Position),
			IsReady:   player.IsReady,
			BetAmount: player.BetAmount,
			JoinedAt:  timestamppb.New(player.JoinedAt),
			Status:    convertPlayerStatusToProto(player.Status),
		}
	}
	return pbPlayers
}

func convertPlayerStatusToProto(status models.PlayerStatus) pb.PlayerStatus {
	switch status {
	case models.PlayerStatusActive:
		return pb.PlayerStatus_PLAYER_STATUS_ACTIVE
	case models.PlayerStatusReady:
		return pb.PlayerStatus_PLAYER_STATUS_ACTIVE // Map ready to active for protobuf
	case models.PlayerStatusPlaying:
		return pb.PlayerStatus_PLAYER_STATUS_ACTIVE // Map playing to active for protobuf
	case models.PlayerStatusLeft:
		return pb.PlayerStatus_PLAYER_STATUS_LEFT
	default:
		return pb.PlayerStatus_PLAYER_STATUS_UNSPECIFIED
	}
}
