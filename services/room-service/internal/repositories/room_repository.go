package repositories

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/xzgame/room-service/internal/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RoomRepository interface {
	// Basic CRUD operations
	CreateRoom(ctx context.Context, room *models.Room) error
	GetRoom(ctx context.Context, roomID string) (*models.Room, error)
	UpdateRoom(ctx context.Context, roomID string, updates map[string]interface{}) error
	DeleteRoom(ctx context.Context, roomID string) error

	// Query operations
	ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error)
	GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error)
	GetRoomsByStatus(ctx context.Context, status models.RoomStatus) ([]*models.Room, error)
	GetRoomsByGameType(ctx context.Context, gameType models.GameType) ([]*models.Room, error)

	// Maintenance operations
	CleanupInactiveRooms(ctx context.Context, inactiveThreshold int64) (int64, error)
	ArchiveOldRooms(ctx context.Context, archiveThreshold int64) (int64, error)
}

type MongoRoomRepository struct{}

func NewRoomRepository(config RepositoryConfig) (RoomRepository, error) {
	return &MongoRoomRepository{}, nil
}

// Basic CRUD operations
func (r *MongoRoomRepository) CreateRoom(ctx context.Context, room *models.Room) error {
	// Mock implementation - in real implementation this would save to MongoDB
	return nil
}

func (r *MongoRoomRepository) GetRoom(ctx context.Context, roomID string) (*models.Room, error) {
	// For now, delegate to manager service via HTTP API
	// In a real implementation, this would query MongoDB directly
	// But since rooms are managed by manager-service, we'll fetch from there

	// This is a temporary implementation - ideally room-service should have its own database
	// or use gRPC to communicate with manager-service
	return r.fetchRoomFromManagerService(ctx, roomID)
}

func (r *MongoRoomRepository) UpdateRoom(ctx context.Context, roomID string, updates map[string]interface{}) error {
	// Mock implementation - in real implementation this would update MongoDB
	return nil
}

func (r *MongoRoomRepository) DeleteRoom(ctx context.Context, roomID string) error {
	// Mock implementation - in real implementation this would delete from MongoDB
	return nil
}

// Query operations
func (r *MongoRoomRepository) ListRooms(ctx context.Context, filter models.RoomListFilter) ([]*models.Room, int64, error) {
	// Return empty list - room listing is handled by manager service
	return []*models.Room{}, 0, nil
}

func (r *MongoRoomRepository) GetRoomsByPlayer(ctx context.Context, userID string) ([]*models.Room, error) {
	// Return empty list - room queries are handled by manager service
	return []*models.Room{}, nil
}

func (r *MongoRoomRepository) GetRoomsByStatus(ctx context.Context, status models.RoomStatus) ([]*models.Room, error) {
	// Return empty list - room queries are handled by manager service
	return []*models.Room{}, nil
}

func (r *MongoRoomRepository) GetRoomsByGameType(ctx context.Context, gameType models.GameType) ([]*models.Room, error) {
	// Mock implementation - in real implementation this would query MongoDB
	return []*models.Room{}, nil
}

// Maintenance operations
func (r *MongoRoomRepository) CleanupInactiveRooms(ctx context.Context, inactiveThreshold int64) (int64, error) {
	return 0, nil
}

func (r *MongoRoomRepository) ArchiveOldRooms(ctx context.Context, archiveThreshold int64) (int64, error) {
	return 0, nil
}

type RepositoryConfig struct {
	DatabaseURL  string
	DatabaseName string
	CacheEnabled bool
	CacheURL     string
}

// fetchRoomFromManagerService fetches room data from manager service
func (r *MongoRoomRepository) fetchRoomFromManagerService(ctx context.Context, roomID string) (*models.Room, error) {
	// Get manager service URL from environment
	managerServiceURL := os.Getenv("MANAGER_SERVICE_URL")
	if managerServiceURL == "" {
		managerServiceURL = "http://localhost:3002"
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make request to manager service
	url := fmt.Sprintf("%s/rooms/%s", managerServiceURL, roomID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch room from manager service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, models.NewRoomError(models.ErrorCodeRoomNotFound, "room not found", roomID)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("manager service returned status %d", resp.StatusCode)
	}

	// Parse response
	var response struct {
		Success bool `json:"success"`
		Data    struct {
			Room struct {
				ID             string  `json:"id"`
				Name           string  `json:"name"`
				GameType       string  `json:"game_type"`
				Status         string  `json:"status"`
				CurrentPlayers int     `json:"current_players"`
				MaxPlayers     int     `json:"max_players"`
				MinPlayers     int     `json:"min_players"`
				BetAmount      float64 `json:"bet_amount"` // Manager service returns float
				Players        []struct {
					UserID    string    `json:"user_id"`
					Username  string    `json:"username"`
					Position  int       `json:"position"`
					IsReady   bool      `json:"is_ready"`
					BetAmount string    `json:"bet_amount"` // Manager service returns string
					JoinedAt  time.Time `json:"joined_at"`
					Status    string    `json:"status"`
					SessionID string    `json:"session_id"`
					Balance   string    `json:"balance"`
				} `json:"players"`
				IsPrivate bool      `json:"is_private"`
				AutoStart bool      `json:"auto_start"`
				CreatedAt time.Time `json:"created_at"`
				UpdatedAt time.Time `json:"updated_at"`
			} `json:"room"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, models.NewRoomError(models.ErrorCodeRoomNotFound, "room not found", roomID)
	}

	// Convert manager service response to room service model
	room := &models.Room{
		Name:           response.Data.Room.Name,
		GameType:       convertGameTypeFromString(response.Data.Room.GameType),
		Status:         convertRoomStatusFromString(response.Data.Room.Status),
		CurrentPlayers: response.Data.Room.CurrentPlayers,
		MaxPlayers:     response.Data.Room.MaxPlayers,
		MinPlayers:     response.Data.Room.MinPlayers,
		BetAmount:      int64(response.Data.Room.BetAmount), // Convert float64 to int64
		Players:        make([]models.RoomPlayer, len(response.Data.Room.Players)),
		Configuration: models.RoomConfiguration{
			IsPrivate: response.Data.Room.IsPrivate,
			AutoStart: response.Data.Room.AutoStart,
			GameType:  convertGameTypeFromString(response.Data.Room.GameType),
		},
		CreatedAt:    response.Data.Room.CreatedAt,
		UpdatedAt:    response.Data.Room.UpdatedAt,
		LastActivity: response.Data.Room.UpdatedAt,
	}

	// Set room ID
	if objectID, err := primitive.ObjectIDFromHex(response.Data.Room.ID); err == nil {
		room.ID = objectID
	}

	// Convert players
	for i, player := range response.Data.Room.Players {
		// Convert bet amount from string to int64
		betAmount := int64(0)
		if player.BetAmount != "" {
			if parsed, err := strconv.ParseFloat(player.BetAmount, 64); err == nil {
				betAmount = int64(parsed)
			}
		}

		room.Players[i] = models.RoomPlayer{
			UserID:    player.UserID,
			Username:  player.Username,
			Position:  player.Position,
			IsReady:   player.IsReady,
			BetAmount: betAmount,
			JoinedAt:  player.JoinedAt,
			Status:    convertPlayerStatusFromString(player.Status),
		}
	}

	return room, nil
}

// Helper functions to convert string values to enum types
func convertGameTypeFromString(gameType string) models.GameType {
	switch gameType {
	case "prizewheel":
		return models.GameTypePrizeWheel
	case "amidakuji":
		return models.GameTypeAmidakuji
	default:
		return models.GameTypePrizeWheel // Default to prize wheel
	}
}

func convertRoomStatusFromString(status string) models.RoomStatus {
	switch status {
	case "waiting":
		return models.RoomStatusWaiting
	case "active":
		return models.RoomStatusActive
	case "full":
		return models.RoomStatusFull
	case "closed":
		return models.RoomStatusClosed
	case "archived":
		return models.RoomStatusArchived
	default:
		return models.RoomStatusWaiting // Default to waiting
	}
}

func convertPlayerStatusFromString(status string) models.PlayerStatus {
	switch status {
	case "active":
		return models.PlayerStatusActive
	case "ready":
		return models.PlayerStatusReady
	case "playing":
		return models.PlayerStatusPlaying
	case "left":
		return models.PlayerStatusLeft
	default:
		return models.PlayerStatusActive // Default to active
	}
}
