package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"

	"github.com/xzgame/room-service/internal/services"
)

// OrchestratorHandler handles orchestrated room operations
// This is where Socket Gateway will send requests instead of handling business logic
type Orchestrator<PERSON>andler struct {
	orchestrator services.RoomOrchestratorService
	logger       *logrus.Logger
}

// NewOrchestratorHandler creates a new orchestrator handler
func NewOrchestratorHandler(orchestrator services.RoomOrchestratorService, logger *logrus.Logger) *OrchestratorHandler {
	return &OrchestratorHandler{
		orchestrator: orchestrator,
		logger:       logger,
	}
}

// RegisterRoutes registers orchestrator routes
func (h *OrchestratorHandler) RegisterRoutes(router *mux.Router) {
	// Orchestrated operations - these replace the business logic in Socket Gateway
	router.HandleFunc("/api/v1/orchestrate/join_room", h.HandleJoinRoom).Methods("POST")
	router.HandleFunc("/api/v1/orchestrate/leave_room", h.<PERSON>le<PERSON>eaveRoom).Methods("POST")
}

// HandleJoinRoom handles orchestrated join room requests from Socket Gateway
func (h *OrchestratorHandler) HandleJoinRoom(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse request body
	var request services.JoinRoomRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.logger.WithError(err).Error("Failed to decode join room request")
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	// Extract metadata from headers (added by Socket Gateway)
	request.SocketID = r.Header.Get("X-Socket-ID")

	h.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"username": request.Username,
		"socketId": request.SocketID,
	}).Info("Processing orchestrated join room request")

	// Execute orchestrated join room operation
	response, err := h.orchestrator.OrchestateJoinRoom(ctx, &request)
	if err != nil {
		h.logger.WithError(err).Error("Join room orchestration failed")
		h.writeErrorResponse(w, "Internal server error", "ORCHESTRATION_ERROR", http.StatusInternalServerError)
		return
	}

	// Write response
	h.writeJSONResponse(w, response, http.StatusOK)

	h.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"success":  response.Success,
		"socketId": request.SocketID,
	}).Info("Join room orchestration completed")
}

// HandleLeaveRoom handles orchestrated leave room requests from Socket Gateway
func (h *OrchestratorHandler) HandleLeaveRoom(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse request body
	var request services.LeaveRoomRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.logger.WithError(err).Error("Failed to decode leave room request")
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	// Extract metadata from headers
	request.SocketID = r.Header.Get("X-Socket-ID")

	h.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"username": request.Username,
		"socketId": request.SocketID,
	}).Info("Processing orchestrated leave room request")

	// Execute orchestrated leave room operation
	response, err := h.orchestrator.OrchestateLeaveRoom(ctx, &request)
	if err != nil {
		h.logger.WithError(err).Error("Leave room orchestration failed")
		h.writeErrorResponse(w, "Internal server error", "ORCHESTRATION_ERROR", http.StatusInternalServerError)
		return
	}

	// Write response
	h.writeJSONResponse(w, response, http.StatusOK)

	h.logger.WithFields(logrus.Fields{
		"userId":   request.UserID,
		"roomId":   request.RoomID,
		"success":  response.Success,
		"socketId": request.SocketID,
	}).Info("Leave room orchestration completed")
}

// writeJSONResponse writes a JSON response
func (h *OrchestratorHandler) writeJSONResponse(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.WithError(err).Error("Failed to encode JSON response")
	}
}

// writeErrorResponse writes an error response
func (h *OrchestratorHandler) writeErrorResponse(w http.ResponseWriter, message, code string, statusCode int) {
	response := map[string]interface{}{
		"success": false,
		"error":   message,
		"code":    code,
	}

	h.writeJSONResponse(w, response, statusCode)
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Service   string `json:"service"`
	Timestamp string `json:"timestamp"`
}

// HandleHealth handles health check requests
func (h *OrchestratorHandler) HandleHealth(w http.ResponseWriter, r *http.Request) {
	response := HealthResponse{
		Status:    "healthy",
		Service:   "room-orchestrator",
		Timestamp: "2024-01-01T00:00:00Z", // You can use time.Now().Format(time.RFC3339)
	}

	h.writeJSONResponse(w, response, http.StatusOK)
}
