package models

import "fmt"

// RoomError represents a room-specific error
type RoomError struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
	RoomID  string    `json:"roomId,omitempty"`
}

// Error implements the error interface
func (e *RoomError) Error() string {
	if e.RoomID != "" {
		return fmt.Sprintf("room error [%s] in room %s: %s", e.Code, e.RoomID, e.Message)
	}
	return fmt.Sprintf("room error [%s]: %s", e.Code, e.Message)
}

// NewRoomError creates a new room error
func NewRoomError(code ErrorCode, message, roomID string) *RoomError {
	return &RoomError{
		Code:    code,
		Message: message,
		RoomID:  roomID,
	}
}

// ErrorCode represents error codes for room operations
type ErrorCode string

const (
	// Room not found errors
	ErrorCodeRoomNotFound ErrorCode = "ROOM_NOT_FOUND"

	// Room state errors
	ErrorCodeRoomNotWaiting    ErrorCode = "ROOM_NOT_WAITING"
	ErrorCodeRoomFull          ErrorCode = "ROOM_FULL"
	ErrorCodeRoomClosed        ErrorCode = "ROOM_CLOSED"
	ErrorCodeRoomAlreadyActive ErrorCode = "ROOM_ALREADY_ACTIVE"
	ErrorCodeInvalidSession    ErrorCode = "INVALID_SESSION"

	// Player errors
	ErrorCodePlayerNotInRoom     ErrorCode = "PLAYER_NOT_IN_ROOM"
	ErrorCodePlayerAlreadyInRoom ErrorCode = "PLAYER_ALREADY_IN_ROOM"
	ErrorCodeInvalidPlayerStatus ErrorCode = "INVALID_PLAYER_STATUS"

	// Access errors
	ErrorCodeInvalidPassword    ErrorCode = "INVALID_PASSWORD"
	ErrorCodePasswordRequired   ErrorCode = "PASSWORD_REQUIRED"
	ErrorCodeUnauthorizedAccess ErrorCode = "UNAUTHORIZED_ACCESS"

	// Validation errors
	ErrorCodeInvalidBetAmount    ErrorCode = "INVALID_BET_AMOUNT"
	ErrorCodeInvalidRoomConfig   ErrorCode = "INVALID_ROOM_CONFIG"
	ErrorCodeInvalidGameType     ErrorCode = "INVALID_GAME_TYPE"
	ErrorCodeInvalidRoomState    ErrorCode = "INVALID_ROOM_STATE"
	ErrorCodeInsufficientPlayers ErrorCode = "INSUFFICIENT_PLAYERS"
	ErrorCodePlayersNotReady     ErrorCode = "PLAYERS_NOT_READY"

	// Database errors
	ErrorCodeDatabaseError ErrorCode = "DATABASE_ERROR"
	ErrorCodeCacheError    ErrorCode = "CACHE_ERROR"

	// General errors
	ErrorCodeInternalError   ErrorCode = "INTERNAL_ERROR"
	ErrorCodeValidationError ErrorCode = "VALIDATION_ERROR"
	ErrorCodeInvalidInput    ErrorCode = "INVALID_INPUT"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// Error implements the error interface
func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

// NewValidationError creates a new validation error
func NewValidationError(field, message string, value interface{}) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	}
}

// DatabaseError represents a database error
type DatabaseError struct {
	Operation string `json:"operation"`
	Message   string `json:"message"`
	Cause     error  `json:"-"`
}

// Error implements the error interface
func (e *DatabaseError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("database error during %s: %s (cause: %v)", e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("database error during %s: %s", e.Operation, e.Message)
}

// Unwrap returns the underlying cause
func (e *DatabaseError) Unwrap() error {
	return e.Cause
}

// NewDatabaseError creates a new database error
func NewDatabaseError(operation, message string, cause error) *DatabaseError {
	return &DatabaseError{
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// CacheError represents a cache error
type CacheError struct {
	Operation string `json:"operation"`
	Key       string `json:"key"`
	Message   string `json:"message"`
	Cause     error  `json:"-"`
}

// Error implements the error interface
func (e *CacheError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("cache error during %s for key '%s': %s (cause: %v)", e.Operation, e.Key, e.Message, e.Cause)
	}
	return fmt.Sprintf("cache error during %s for key '%s': %s", e.Operation, e.Key, e.Message)
}

// Unwrap returns the underlying cause
func (e *CacheError) Unwrap() error {
	return e.Cause
}

// NewCacheError creates a new cache error
func NewCacheError(operation, key, message string, cause error) *CacheError {
	return &CacheError{
		Operation: operation,
		Key:       key,
		Message:   message,
		Cause:     cause,
	}
}
