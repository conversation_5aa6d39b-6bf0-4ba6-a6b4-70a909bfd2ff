package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Room represents a game room
type Room struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Name           string             `bson:"name" json:"name"`
	GameType       GameType           `bson:"game_type" json:"gameType"`
	Status         RoomStatus         `bson:"status" json:"status"`
	CurrentPlayers int                `bson:"current_players" json:"currentPlayers"`
	MaxPlayers     int                `bson:"max_players" json:"maxPlayers"`
	MinPlayers     int                `bson:"min_players" json:"minPlayers"`
	BetAmount      int64              `bson:"bet_amount" json:"betAmount"`
	Players        []RoomPlayer       `bson:"players" json:"players"`
	Configuration  RoomConfiguration  `bson:"configuration" json:"configuration"`
	CurrentSession string             `bson:"current_session,omitempty" json:"currentSession,omitempty"`
	CreatedBy      string             `bson:"created_by" json:"createdBy"`
	CreatedAt      time.Time          `bson:"created_at" json:"createdAt"`
	UpdatedAt      time.Time          `bson:"updated_at" json:"updatedAt"`
	LastActivity   time.Time          `bson:"last_activity" json:"lastActivity"`
}

// RoomPlayer represents a player in a room
type RoomPlayer struct {
	UserID    string       `bson:"user_id" json:"userId"`
	Username  string       `bson:"username" json:"username"`
	Position  int          `bson:"position" json:"position"`
	IsReady   bool         `bson:"is_ready" json:"isReady"`
	BetAmount int64        `bson:"bet_amount" json:"betAmount"`
	JoinedAt  time.Time    `bson:"joined_at" json:"joinedAt"`
	Status    PlayerStatus `bson:"status" json:"status"`
}

// RoomConfiguration represents room configuration
type RoomConfiguration struct {
	IsPrivate    bool                   `bson:"is_private" json:"isPrivate"`
	Password     string                 `bson:"password,omitempty" json:"password,omitempty"`
	AutoStart    bool                   `bson:"auto_start" json:"autoStart"`
	BetLimits    BetLimits              `bson:"bet_limits" json:"betLimits"`
	TimeLimit    int                    `bson:"time_limit" json:"timeLimit"` // seconds
	GameType     GameType               `bson:"game_type" json:"gameType"`
	GameSpecific map[string]interface{} `bson:"game_specific,omitempty" json:"gameSpecific,omitempty"`
}

// BetLimits represents betting limits
type BetLimits struct {
	MinBet int64 `bson:"min_bet" json:"minBet"`
	MaxBet int64 `bson:"max_bet" json:"maxBet"`
}

// GameType represents the type of game
type GameType string

const (
	GameTypePrizeWheel GameType = "prize_wheel"
	GameTypeAmidakuji  GameType = "amidakuji"
	GameTypeSlots      GameType = "slots"
	GameTypePoker      GameType = "poker"
	GameTypeBlackjack  GameType = "blackjack"
)

// RoomStatus represents the status of a room
type RoomStatus string

const (
	RoomStatusWaiting  RoomStatus = "waiting"
	RoomStatusActive   RoomStatus = "active"
	RoomStatusFull     RoomStatus = "full"
	RoomStatusClosed   RoomStatus = "closed"
	RoomStatusArchived RoomStatus = "archived"
)

// PlayerStatus represents the status of a player
type PlayerStatus string

const (
	PlayerStatusActive  PlayerStatus = "active"
	PlayerStatusReady   PlayerStatus = "ready"
	PlayerStatusPlaying PlayerStatus = "playing"
	PlayerStatusLeft    PlayerStatus = "left"
)

// Request models

// CreateRoomRequest represents a request to create a room
type CreateRoomRequest struct {
	Name          string                 `json:"name" validate:"required,min=1,max=50"`
	GameType      GameType               `json:"gameType" validate:"required"`
	MaxPlayers    int                    `json:"maxPlayers" validate:"required,min=2,max=10"`
	MinPlayers    int                    `json:"minPlayers" validate:"required,min=2"`
	BetLimits     BetLimits              `json:"betLimits"`
	AutoStart     bool                   `json:"autoStart"`
	IsPrivate     bool                   `json:"isPrivate"`
	Password      string                 `json:"password,omitempty"`
	GameSpecific  map[string]interface{} `json:"gameSpecific,omitempty"`
	Configuration RoomConfiguration      `json:"configuration"`
	CreatedBy     string                 `json:"createdBy" validate:"required"`
}

// JoinRoomRequest represents a request to join a room
type JoinRoomRequest struct {
	RoomID    string `json:"roomId" validate:"required"`
	UserID    string `json:"userId" validate:"required"`
	Username  string `json:"username" validate:"required"`
	BetAmount int64  `json:"betAmount" validate:"required,min=1"`
	Password  string `json:"password,omitempty"`
}

// LeaveRoomRequest represents a request to leave a room
type LeaveRoomRequest struct {
	RoomID string `json:"roomId" validate:"required"`
	UserID string `json:"userId" validate:"required"`
}

// SetPlayerReadyRequest represents a request to set player ready status
type SetPlayerReadyRequest struct {
	RoomID string `json:"roomId" validate:"required"`
	UserID string `json:"userId" validate:"required"`
	Ready  bool   `json:"ready"`
}

// UpdateRoomRequest represents a request to update a room
type UpdateRoomRequest struct {
	Name          *string                 `json:"name,omitempty"`
	MaxPlayers    *int                    `json:"maxPlayers,omitempty"`
	MinPlayers    *int                    `json:"minPlayers,omitempty"`
	BetLimits     *BetLimits              `json:"betLimits,omitempty"`
	AutoStart     *bool                   `json:"autoStart,omitempty"`
	GameSpecific  *map[string]interface{} `json:"gameSpecific,omitempty"`
	Configuration *RoomConfiguration      `json:"configuration,omitempty"`
}

// RoomListFilter represents filters for listing rooms
type RoomListFilter struct {
	GameType           *GameType   `json:"gameType,omitempty"`
	Status             *RoomStatus `json:"status,omitempty"`
	IsPrivate          *bool       `json:"isPrivate,omitempty"`
	MinPlayers         *int        `json:"minPlayers,omitempty"`
	MaxPlayers         *int        `json:"maxPlayers,omitempty"`
	HasSpace           bool        `json:"hasSpace,omitempty"`
	LastActivityBefore *time.Time  `json:"lastActivityBefore,omitempty"`
	Page               int         `json:"page" validate:"min=1"`
	Limit              int         `json:"limit" validate:"min=1,max=100"`
}

// Response models

// RoomListResponse represents a paginated list of rooms
type RoomListResponse struct {
	Rooms      []Room     `json:"rooms"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination information
type Pagination struct {
	CurrentPage int   `json:"currentPage"`
	PerPage     int   `json:"perPage"`
	TotalCount  int64 `json:"totalCount"`
	TotalPages  int   `json:"totalPages"`
}

// RoomStats represents room statistics
type RoomStats struct {
	RoomID       string    `json:"roomId"`
	TotalGames   int       `json:"totalGames"`
	TotalPlayers int       `json:"totalPlayers"`
	LastActivity time.Time `json:"lastActivity"`
	CreatedAt    time.Time `json:"createdAt"`
}

// RoomActivity represents room activity
type RoomActivity struct {
	RoomID    string       `json:"roomId"`
	Events    []RoomEvent  `json:"events"`
	Players   []RoomPlayer `json:"players"`
	UpdatedAt time.Time    `json:"updatedAt"`
}

// RoomEvent represents a room event
type RoomEvent struct {
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// RoomInfo represents comprehensive room information for broadcasting
type RoomInfo struct {
	RoomID         string       `json:"roomId"`
	Name           string       `json:"name"`
	GameType       GameType     `json:"gameType"`
	Status         RoomStatus   `json:"status"`
	CurrentPlayers int          `json:"currentPlayers"`
	MaxPlayers     int          `json:"maxPlayers"`
	MinPlayers     int          `json:"minPlayers"`
	BetAmount      int64        `json:"betAmount"`
	Players        []RoomPlayer `json:"players"`
	IsPrivate      bool         `json:"isPrivate"`
	AutoStart      bool         `json:"autoStart"`
	CreatedAt      time.Time    `json:"createdAt"`
	LastActivity   time.Time    `json:"lastActivity"`
	CanJoin        bool         `json:"canJoin"`
	CanStart       bool         `json:"canStart"`
}

// Game-related models

// StartGameRequest represents a request to start a game
type StartGameRequest struct {
	RoomID      string   `json:"roomId" validate:"required"`
	GameType    GameType `json:"gameType" validate:"required"`
	InitiatedBy string   `json:"initiatedBy" validate:"required"`
}

// StartGameResponse represents a response to start game request
type StartGameResponse struct {
	Room      *Room        `json:"room"`
	SessionID string       `json:"sessionId"`
	StartedAt time.Time    `json:"startedAt"`
	Players   []RoomPlayer `json:"players"`
}

// EndGameRequest represents a request to end a game
type EndGameRequest struct {
	RoomID        string                 `json:"roomId" validate:"required"`
	SessionID     string                 `json:"sessionId" validate:"required"`
	GameSessionID string                 `json:"gameSessionId" validate:"required"`
	WinnerUserID  string                 `json:"winnerUserId,omitempty"`
	Results       map[string]interface{} `json:"results,omitempty"`
}

// BetLimitConfig represents bet limit configuration (alias for BetLimits)
type BetLimitConfig = BetLimits

// GameSession represents an active game session
type GameSession struct {
	ID        string     `bson:"_id,omitempty" json:"id"`
	RoomID    string     `bson:"room_id" json:"roomId"`
	GameType  GameType   `bson:"game_type" json:"gameType"`
	Status    string     `bson:"status" json:"status"`
	StartedAt time.Time  `bson:"started_at" json:"startedAt"`
	EndedAt   *time.Time `bson:"ended_at,omitempty" json:"endedAt,omitempty"`
}
