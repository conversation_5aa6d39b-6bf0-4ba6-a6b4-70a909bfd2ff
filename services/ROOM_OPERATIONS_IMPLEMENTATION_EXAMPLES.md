# Socket Gateway Room Operations - Implementation Examples

## Client-Side Implementation Examples

### 1. JavaScript/TypeScript Client Implementation

```typescript
interface RoomJoinRequest {
  roomId: string;
  password?: string;
  betAmount?: number;
}

interface RoomLeaveRequest {
  roomId: string;
}

interface RoomJoinResponse {
  success: boolean;
  message?: string;
  roomId?: string;
  room?: {
    id: string;
    name: string;
    game_type: string;
    current_players: number;
    max_players: number;
    status: string;
    bet_amount: number;
  };
  player?: {
    userId: string;
    username: string;
  };
  error?: string;
  code?: string;
}

class RoomManager {
  private socket: Socket;
  private currentRoomId: string | null = null;

  constructor(socket: Socket) {
    this.socket = socket;
    this.setupEventListeners();
  }

  /**
   * Join a room
   */
  async joinRoom(request: RoomJoinRequest): Promise<RoomJoinResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Room join timeout'));
      }, 10000);

      this.socket.emit('join_room', request, (response: RoomJoinResponse) => {
        clearTimeout(timeout);
        
        if (response.success) {
          this.currentRoomId = response.roomId || null;
          console.log('Successfully joined room:', response.room);
        } else {
          console.error('Failed to join room:', response.error);
        }
        
        resolve(response);
      });
    });
  }

  /**
   * Leave current room
   */
  async leaveRoom(): Promise<void> {
    if (!this.currentRoomId) {
      throw new Error('Not currently in a room');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Room leave timeout'));
      }, 5000);

      const request: RoomLeaveRequest = {
        roomId: this.currentRoomId
      };

      this.socket.emit('leave_room', request, (response: any) => {
        clearTimeout(timeout);
        
        if (response.success) {
          this.currentRoomId = null;
          console.log('Successfully left room');
          resolve();
        } else {
          console.error('Failed to leave room:', response.error);
          reject(new Error(response.error));
        }
      });
    });
  }

  /**
   * Setup event listeners for room updates
   */
  private setupEventListeners(): void {
    // Listen for room info updates
    this.socket.on('room_info_updated', (data) => {
      console.log('Room info updated:', data);
      this.handleRoomInfoUpdate(data);
    });

    // Listen for player joined events
    this.socket.on('player_joined', (data) => {
      console.log('Player joined room:', data);
      this.handlePlayerJoined(data);
    });

    // Listen for player left events
    this.socket.on('player_left', (data) => {
      console.log('Player left room:', data);
      this.handlePlayerLeft(data);
    });

    // Listen for room state changes
    this.socket.on('room_state_changed', (data) => {
      console.log('Room state changed:', data);
      this.handleRoomStateChanged(data);
    });
  }

  private handleRoomInfoUpdate(data: any): void {
    if (data.roomId === this.currentRoomId) {
      // Update UI with new room information
      this.updateRoomUI(data.room);
    }
  }

  private handlePlayerJoined(data: any): void {
    if (data.roomId === this.currentRoomId) {
      // Add player to room participant list
      this.addPlayerToUI(data.player);
    }
  }

  private handlePlayerLeft(data: any): void {
    if (data.roomId === this.currentRoomId) {
      // Remove player from room participant list
      this.removePlayerFromUI(data.player);
    }
  }

  private handleRoomStateChanged(data: any): void {
    if (data.roomId === this.currentRoomId) {
      // Handle room state changes (waiting -> playing -> finished)
      this.updateRoomState(data.status);
    }
  }

  private updateRoomUI(room: any): void {
    // Update room information in UI
    document.getElementById('room-name')!.textContent = room.name;
    document.getElementById('player-count')!.textContent = 
      `${room.current_players}/${room.max_players}`;
    document.getElementById('room-status')!.textContent = room.status;
  }

  private addPlayerToUI(player: any): void {
    // Add player to participant list
    const playerList = document.getElementById('player-list');
    const playerElement = document.createElement('div');
    playerElement.id = `player-${player.userId}`;
    playerElement.textContent = player.username;
    playerList?.appendChild(playerElement);
  }

  private removePlayerFromUI(player: any): void {
    // Remove player from participant list
    const playerElement = document.getElementById(`player-${player.userId}`);
    playerElement?.remove();
  }

  private updateRoomState(status: string): void {
    // Update room state in UI
    const statusElement = document.getElementById('room-status');
    if (statusElement) {
      statusElement.textContent = status;
      statusElement.className = `status-${status.toLowerCase()}`;
    }
  }

  /**
   * Get current room ID
   */
  getCurrentRoomId(): string | null {
    return this.currentRoomId;
  }

  /**
   * Check if currently in a room
   */
  isInRoom(): boolean {
    return this.currentRoomId !== null;
  }
}

// Usage Example
const socket = io('http://localhost:3001', {
  auth: {
    token: 'your-jwt-token-here'
  }
});

const roomManager = new RoomManager(socket);

// Join a room
async function joinGameRoom() {
  try {
    const response = await roomManager.joinRoom({
      roomId: '68412c9af494b684c1c18ecf',
      password: 'secret123',
      betAmount: 100
    });

    if (response.success) {
      console.log('Joined room successfully:', response.room);
      // Update UI to show room interface
      showRoomInterface(response.room);
    } else {
      console.error('Failed to join room:', response.error);
      showErrorMessage(response.error);
    }
  } catch (error) {
    console.error('Room join error:', error);
    showErrorMessage('Failed to join room');
  }
}

// Leave current room
async function leaveGameRoom() {
  try {
    await roomManager.leaveRoom();
    console.log('Left room successfully');
    // Update UI to show lobby interface
    showLobbyInterface();
  } catch (error) {
    console.error('Room leave error:', error);
    showErrorMessage('Failed to leave room');
  }
}
```

### 2. React Hook Implementation

```typescript
import { useEffect, useState, useCallback } from 'react';
import { Socket } from 'socket.io-client';

interface UseRoomManagerProps {
  socket: Socket;
}

interface RoomState {
  currentRoomId: string | null;
  roomInfo: any | null;
  players: any[];
  isJoining: boolean;
  isLeaving: boolean;
  error: string | null;
}

export const useRoomManager = ({ socket }: UseRoomManagerProps) => {
  const [roomState, setRoomState] = useState<RoomState>({
    currentRoomId: null,
    roomInfo: null,
    players: [],
    isJoining: false,
    isLeaving: false,
    error: null
  });

  // Join room function
  const joinRoom = useCallback(async (request: RoomJoinRequest) => {
    setRoomState(prev => ({ ...prev, isJoining: true, error: null }));

    try {
      const response = await new Promise<RoomJoinResponse>((resolve) => {
        socket.emit('join_room', request, resolve);
      });

      if (response.success) {
        setRoomState(prev => ({
          ...prev,
          currentRoomId: response.roomId || null,
          roomInfo: response.room || null,
          isJoining: false
        }));
      } else {
        setRoomState(prev => ({
          ...prev,
          error: response.error || 'Failed to join room',
          isJoining: false
        }));
      }

      return response;
    } catch (error) {
      setRoomState(prev => ({
        ...prev,
        error: 'Room join timeout',
        isJoining: false
      }));
      throw error;
    }
  }, [socket]);

  // Leave room function
  const leaveRoom = useCallback(async () => {
    if (!roomState.currentRoomId) return;

    setRoomState(prev => ({ ...prev, isLeaving: true, error: null }));

    try {
      const response = await new Promise<any>((resolve) => {
        socket.emit('leave_room', { roomId: roomState.currentRoomId }, resolve);
      });

      if (response.success) {
        setRoomState(prev => ({
          ...prev,
          currentRoomId: null,
          roomInfo: null,
          players: [],
          isLeaving: false
        }));
      } else {
        setRoomState(prev => ({
          ...prev,
          error: response.error || 'Failed to leave room',
          isLeaving: false
        }));
      }
    } catch (error) {
      setRoomState(prev => ({
        ...prev,
        error: 'Room leave timeout',
        isLeaving: false
      }));
      throw error;
    }
  }, [socket, roomState.currentRoomId]);

  // Setup event listeners
  useEffect(() => {
    const handleRoomInfoUpdated = (data: any) => {
      if (data.roomId === roomState.currentRoomId) {
        setRoomState(prev => ({
          ...prev,
          roomInfo: data.room
        }));
      }
    };

    const handlePlayerJoined = (data: any) => {
      if (data.roomId === roomState.currentRoomId) {
        setRoomState(prev => ({
          ...prev,
          players: [...prev.players, data.player]
        }));
      }
    };

    const handlePlayerLeft = (data: any) => {
      if (data.roomId === roomState.currentRoomId) {
        setRoomState(prev => ({
          ...prev,
          players: prev.players.filter(p => p.userId !== data.player.userId)
        }));
      }
    };

    socket.on('room_info_updated', handleRoomInfoUpdated);
    socket.on('player_joined', handlePlayerJoined);
    socket.on('player_left', handlePlayerLeft);

    return () => {
      socket.off('room_info_updated', handleRoomInfoUpdated);
      socket.off('player_joined', handlePlayerJoined);
      socket.off('player_left', handlePlayerLeft);
    };
  }, [socket, roomState.currentRoomId]);

  return {
    ...roomState,
    joinRoom,
    leaveRoom,
    isInRoom: roomState.currentRoomId !== null
  };
};

// Usage in React component
const RoomComponent: React.FC = () => {
  const { socket } = useSocket(); // Assume this hook provides socket
  const {
    currentRoomId,
    roomInfo,
    players,
    isJoining,
    isLeaving,
    error,
    joinRoom,
    leaveRoom,
    isInRoom
  } = useRoomManager({ socket });

  const handleJoinRoom = async () => {
    try {
      await joinRoom({
        roomId: '68412c9af494b684c1c18ecf',
        password: 'secret123',
        betAmount: 100
      });
    } catch (error) {
      console.error('Failed to join room:', error);
    }
  };

  const handleLeaveRoom = async () => {
    try {
      await leaveRoom();
    } catch (error) {
      console.error('Failed to leave room:', error);
    }
  };

  if (isInRoom && roomInfo) {
    return (
      <div className="room-interface">
        <h2>{roomInfo.name}</h2>
        <p>Players: {roomInfo.current_players}/{roomInfo.max_players}</p>
        <p>Status: {roomInfo.status}</p>
        
        <div className="players-list">
          <h3>Players in Room:</h3>
          {players.map(player => (
            <div key={player.userId}>{player.username}</div>
          ))}
        </div>

        <button 
          onClick={handleLeaveRoom} 
          disabled={isLeaving}
        >
          {isLeaving ? 'Leaving...' : 'Leave Room'}
        </button>

        {error && <div className="error">{error}</div>}
      </div>
    );
  }

  return (
    <div className="lobby-interface">
      <button 
        onClick={handleJoinRoom} 
        disabled={isJoining}
      >
        {isJoining ? 'Joining...' : 'Join Room'}
      </button>

      {error && <div className="error">{error}</div>}
    </div>
  );
};
```

## Server-Side Implementation Examples

### 3. Enhanced Room Handlers Implementation

```typescript
// Enhanced room handlers with Manager Service integration
class EnhancedRoomHandlers {
  constructor(
    private managerServiceCommunicator: ManagerServiceCommunicator,
    private gameServiceCommunicator: GameServiceCommunicator,
    private socketService: SocketService
  ) {}

  /**
   * Enhanced room join with Manager Service integration
   */
  async handleJoinRoom(socket: Socket, data: any, callback: Function) {
    try {
      const { roomId, password, betAmount } = data;
      const { userId, username } = socket;

      // Step 1: Validate with Manager Service
      const roomValidation = await this.validateRoomJoin(roomId, userId, password, betAmount);
      if (!roomValidation.success) {
        return callback(roomValidation);
      }

      // Step 2: Execute join with Game Service
      const gameJoinResult = await this.gameServiceCommunicator.joinRoom({
        roomId,
        userId,
        username,
        betAmount: betAmount || roomValidation.room.bet_amount
      });

      if (!gameJoinResult.success) {
        return callback(gameJoinResult);
      }

      // Step 3: Update local state and Socket.io room
      await this.executeJoinStateManagement(socket, roomId, gameJoinResult);

      // Step 4: Notify Manager Service of successful join
      await this.notifyManagerServiceJoin(roomId, userId, username);

      // Step 5: Send success response and broadcast
      const response = {
        success: true,
        message: 'Successfully joined room',
        roomId,
        room: roomValidation.room,
        player: { userId, username }
      };

      callback(response);

      // Broadcast to room participants
      await this.broadcastRoomUpdate(roomId, 'player_joined', {
        player: { userId, username },
        room: roomValidation.room
      });

    } catch (error) {
      logger.logError(error, {
        context: 'Enhanced room join',
        socketId: socket.id,
        userId: socket.userId,
        roomId: data.roomId
      });

      callback({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  /**
   * Validate room join with Manager Service
   */
  private async validateRoomJoin(roomId: string, userId: string, password?: string, betAmount?: number) {
    try {
      // Get room details from Manager Service
      const roomResponse = await this.managerServiceCommunicator.getRoomDetails(roomId);

      if (!roomResponse.success) {
        return {
          success: false,
          error: 'Room not found',
          code: 'ROOM_NOT_FOUND'
        };
      }

      const room = roomResponse.room;

      // Validate room capacity
      if (room.current_players >= room.max_players) {
        return {
          success: false,
          error: 'Room is full',
          code: 'ROOM_FULL'
        };
      }

      // Validate password for private rooms
      if (room.is_private && room.password !== password) {
        return {
          success: false,
          error: 'Invalid room password',
          code: 'INVALID_PASSWORD'
        };
      }

      // Validate bet amount
      const requiredBetAmount = betAmount || room.bet_amount;
      const userBalance = await this.getUserBalance(userId);

      if (userBalance < requiredBetAmount) {
        return {
          success: false,
          error: 'Insufficient balance',
          code: 'INSUFFICIENT_BALANCE',
          details: {
            required: requiredBetAmount,
            available: userBalance
          }
        };
      }

      return {
        success: true,
        room,
        requiredBetAmount
      };

    } catch (error) {
      logger.logError(error, {
        context: 'Room join validation',
        roomId,
        userId
      });

      return {
        success: false,
        error: 'Validation failed',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Notify Manager Service of successful join
   */
  private async notifyManagerServiceJoin(roomId: string, userId: string, username: string) {
    try {
      await this.managerServiceCommunicator.notifyPlayerJoined({
        roomId,
        userId,
        username,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.warn('Failed to notify Manager Service of player join', {
        roomId,
        userId,
        error: error.message
      });
      // Don't fail the join operation if notification fails
    }
  }

  /**
   * Broadcast room update to all participants
   */
  private async broadcastRoomUpdate(roomId: string, action: string, data: any) {
    const socketRoomName = `room_${roomId}`;

    this.socketService.io.to(socketRoomName).emit('room_info_updated', {
      action,
      roomId,
      ...data,
      timestamp: new Date().toISOString()
    });

    logger.debug('Broadcasted room update', {
      roomId,
      action,
      socketRoomName
    });
  }
}
```

### 4. Manager Service Communicator Extensions

```typescript
// Extended Manager Service communicator for room operations
class ManagerServiceCommunicator {
  /**
   * Get detailed room information
   */
  async getRoomDetails(roomId: string) {
    try {
      const response = await this.client.get(`/rooms/${roomId}`);

      if (response.data.success) {
        return {
          success: true,
          room: response.data.data.room
        };
      } else {
        return {
          success: false,
          error: response.data.error?.message || 'Room not found'
        };
      }
    } catch (error) {
      logger.logError(error, {
        context: 'Get room details',
        roomId
      });

      return {
        success: false,
        error: 'Failed to fetch room details'
      };
    }
  }

  /**
   * Notify Manager Service of player join
   */
  async notifyPlayerJoined(data: {
    roomId: string;
    userId: string;
    username: string;
    timestamp: string;
  }) {
    try {
      const response = await this.client.post(`/rooms/${data.roomId}/player-joined`, {
        user_id: data.userId,
        username: data.username,
        timestamp: data.timestamp
      });

      return response.data;
    } catch (error) {
      logger.logError(error, {
        context: 'Notify player joined',
        roomId: data.roomId,
        userId: data.userId
      });
      throw error;
    }
  }

  /**
   * Notify Manager Service of player leave
   */
  async notifyPlayerLeft(data: {
    roomId: string;
    userId: string;
    username: string;
    reason?: string;
    timestamp: string;
  }) {
    try {
      const response = await this.client.post(`/rooms/${data.roomId}/player-left`, {
        user_id: data.userId,
        username: data.username,
        reason: data.reason || 'voluntary',
        timestamp: data.timestamp
      });

      return response.data;
    } catch (error) {
      logger.logError(error, {
        context: 'Notify player left',
        roomId: data.roomId,
        userId: data.userId
      });
      throw error;
    }
  }

  /**
   * Update room status
   */
  async updateRoomStatus(roomId: string, status: string, metadata?: any) {
    try {
      const response = await this.client.patch(`/rooms/${roomId}/status`, {
        status,
        metadata,
        timestamp: new Date().toISOString()
      });

      return response.data;
    } catch (error) {
      logger.logError(error, {
        context: 'Update room status',
        roomId,
        status
      });
      throw error;
    }
  }
}
```

This comprehensive documentation provides complete implementation examples for both client-side and server-side room operations, showing how to properly integrate with the Manager Service and Game Service while maintaining efficient real-time communication through Socket.io.
