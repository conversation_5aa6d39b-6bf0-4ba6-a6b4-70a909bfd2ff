#!/usr/bin/env ruby

require 'jwt'
require 'securerandom'

# JWT configuration matching manager service
jwt_secret = 'dev_jwt_secret_key_change_in_production'

# Create payload for user res2 (ID: 683b07882d7dbd11e92bf29d)
payload = {
  sub: '683b07882d7dbd11e92bf29d',
  user_id: '683b07882d7dbd11e92bf29d',
  username: 'res2',
  role: 'admin',
  sessionId: SecureRandom.uuid,
  iat: Time.now.to_i,
  exp: (Time.now + 24*60*60).to_i,
  iss: 'xzgame-auth-service',
  aud: ['xzgame-api', 'xzgame-manager-service']
}

token = JWT.encode(payload, jwt_secret, 'HS256')
puts token
