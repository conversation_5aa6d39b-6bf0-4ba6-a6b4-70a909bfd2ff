#!/bin/bash

# Room Orchestration Test Script
# This script demonstrates the complete room join/leave functionality

echo "🎮 Room Orchestration Test Suite"
echo "================================="

# Configuration
USER_ID="68333053f035ba37b20bf3a4"
USERNAME="admin"
ROOM_ID="68335cd1692398dcafbc4f53"
BET_AMOUNT=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Helper function to check room state
check_room_state() {
    local expected_players=$1
    local description=$2
    
    print_status "Checking room state: $description"
    
    local room_data=$(curl -s "http://localhost:3002/game_service/rooms/$ROOM_ID")
    local current_players=$(echo "$room_data" | jq -r '.data.room.current_players')
    
    if [ "$current_players" = "$expected_players" ]; then
        print_success "Room has $current_players players (expected: $expected_players)"
        return 0
    else
        print_error "Room has $current_players players (expected: $expected_players)"
        return 1
    fi
}

# Helper function to check user balance
check_user_balance() {
    local expected_balance=$1
    local description=$2
    
    print_status "Checking user balance: $description"
    
    local user_data=$(curl -s "http://localhost:3002/game_service/users/$USER_ID/info")
    local current_balance=$(echo "$user_data" | jq -r '.data.balance')
    
    if [ "$current_balance" = "$expected_balance" ]; then
        print_success "User balance is $current_balance (expected: $expected_balance)"
        return 0
    else
        print_warning "User balance is $current_balance (expected: $expected_balance)"
        return 1
    fi
}

# Helper function to send room orchestration request
send_room_request() {
    local event_type=$1
    local correlation_id=$2
    local description=$3
    
    print_status "Sending $event_type request: $description"
    
    local payload=""
    if [ "$event_type" = "join_room" ]; then
        payload="{\"userId\":\"$USER_ID\",\"username\":\"$USERNAME\",\"roomId\":\"$ROOM_ID\",\"password\":\"\",\"betAmount\":$BET_AMOUNT}"
    else
        payload="{\"userId\":\"$USER_ID\",\"username\":\"$USERNAME\",\"roomId\":\"$ROOM_ID\"}"
    fi
    
    local message="{\"event\":{\"type\":\"$event_type\",\"payload\":$payload,\"timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"},\"metadata\":{\"serviceId\":\"socket-gateway\",\"version\":\"1.0.0\",\"correlationId\":\"$correlation_id\",\"responseChannel\":\"socket:responses:$correlation_id\"}}"
    
    redis-cli publish room:orchestrator:requests "$message" > /dev/null
    
    # Wait for processing
    sleep 3
    
    print_success "$event_type request sent successfully"
}

echo ""
print_status "Starting Room Orchestration Test Suite..."
echo ""

# Test 1: Initial State Check
echo "📊 Test 1: Initial State Check"
echo "------------------------------"
check_room_state "0" "Initial room state"
check_user_balance "960.0" "Initial user balance"
echo ""

# Test 2: Join Room
echo "🚪 Test 2: Join Room"
echo "--------------------"
send_room_request "join_room" "test-join-1" "User joining room"
check_room_state "1" "After join operation"
check_user_balance "950.0" "After balance deduction"
echo ""

# Test 3: Leave Room
echo "🚶 Test 3: Leave Room"
echo "---------------------"
send_room_request "leave_room" "test-leave-1" "User leaving room"
check_room_state "0" "After leave operation"
check_user_balance "950.0" "Balance should remain the same (no refund)"
echo ""

# Test 4: Multiple Join/Leave Cycle
echo "🔄 Test 4: Multiple Join/Leave Cycle"
echo "------------------------------------"
for i in {1..3}; do
    print_status "Cycle $i: Join -> Leave"
    
    send_room_request "join_room" "test-cycle-join-$i" "Cycle $i join"
    check_room_state "1" "Cycle $i after join"
    
    send_room_request "leave_room" "test-cycle-leave-$i" "Cycle $i leave"
    check_room_state "0" "Cycle $i after leave"
    
    echo ""
done

# Test 5: Final State Check
echo "🏁 Test 5: Final State Check"
echo "----------------------------"
check_room_state "0" "Final room state"
local final_balance=$(echo "950.0 - (3 * $BET_AMOUNT)" | bc)
check_user_balance "${final_balance}.0" "Final user balance after 3 additional joins"
echo ""

print_success "🎉 Room Orchestration Test Suite Completed!"
print_status "Summary:"
print_status "- All join operations completed successfully"
print_status "- All leave operations completed successfully"
print_status "- Balance deductions working correctly"
print_status "- Room state updates working correctly"
print_status "- Broadcast events published successfully"
echo ""
