const io = require('socket.io-client');

// Test configuration
const SOCKET_URL = 'http://localhost:3001';
const TEST_ROOM_ID = '684f8da83e02d7af17d57846'; // The room ID from your example
const TEST_USER_TOKEN = 'test-token'; // This will be handled by the test

console.log('🧪 Testing Corrected Unified Room Info Implementation');
console.log('====================================================');
console.log('✅ ARCHITECTURE: Room-Service requests color data from Game-Service');
console.log('✅ FLOW: Socket-Gateway → Room-Service → Game-Service → Room-Service → Socket-Gateway');

// Create socket connection
const socket = io(SOCKET_URL, {
  auth: {
    token: TEST_USER_TOKEN
  },
  transports: ['websocket']
});

socket.on('connect', () => {
  console.log('✅ Connected to socket server');
  console.log('Socket ID:', socket.id);
  
  // Test room subscription to trigger unified room info
  console.log('\n📡 Testing room subscription...');
  socket.emit('subscribe_room', { roomId: TEST_ROOM_ID }, (response) => {
    console.log('📥 Subscribe room response:', JSON.stringify(response, null, 2));
  });
});

socket.on('room_info_updated', (data) => {
  console.log('\n🎯 Received room_info_updated event:');
  console.log('==========================================');
  console.log(JSON.stringify(data, null, 2));
  
  // Check if the event contains the expected color data structure
  if (data.colors) {
    console.log('\n✅ SUCCESS: Color data is present in room_info_updated event!');
    console.log('📊 Color Statistics:');
    console.log('- Available colors:', data.colors.available?.length || 0);
    console.log('- Taken colors:', data.colors.taken?.length || 0);
    console.log('- Total colors:', data.colors.statistics?.totalColors || 0);
    console.log('- Selection rate:', data.colors.statistics?.selectionRate || 0, '%');
    
    if (data.colors.assignments) {
      console.log('👥 Player color assignments:');
      Object.entries(data.colors.assignments).forEach(([userId, assignment]) => {
        console.log(`- ${assignment.username}: ${assignment.color.name} (${assignment.color.hex})`);
      });
    }
  } else {
    console.log('\n❌ ISSUE: No color data found in room_info_updated event');
    console.log('Expected structure should include colors object with:');
    console.log('- available: array of available colors');
    console.log('- taken: array of taken colors');
    console.log('- selected: mapping of user IDs to color IDs');
    console.log('- assignments: detailed player color assignments');
    console.log('- statistics: color selection statistics');
  }
  
  // Check for other expected unified structure elements
  if (data.players && data.game && data.room) {
    console.log('\n✅ SUCCESS: Unified structure elements present');
    console.log('- Room data: ✓');
    console.log('- Players data: ✓');
    console.log('- Game data: ✓');
  } else {
    console.log('\n❌ ISSUE: Missing unified structure elements');
    console.log('- Room data:', data.room ? '✓' : '❌');
    console.log('- Players data:', data.players ? '✓' : '❌');
    console.log('- Game data:', data.game ? '✓' : '❌');
  }
  
  // Disconnect after receiving the event
  setTimeout(() => {
    console.log('\n🔌 Disconnecting...');
    socket.disconnect();
    process.exit(0);
  }, 1000);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error.message);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log('🔌 Disconnected:', reason);
});

// Timeout after 10 seconds
setTimeout(() => {
  console.log('\n⏰ Test timeout - no room_info_updated event received');
  console.log('This might indicate:');
  console.log('1. The room does not exist');
  console.log('2. The game service is not running');
  console.log('3. The unified room info implementation is not working');
  console.log('4. The room is not a prize wheel game');
  socket.disconnect();
  process.exit(1);
}, 10000);
