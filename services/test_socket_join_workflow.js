/**
 * Test script for complete socket join workflow
 */

const io = require('socket.io-client');
const axios = require('axios');

// Test configuration
const SOCKET_URL = 'http://localhost:3001';
const MANAGER_SERVICE_URL = 'http://localhost:3002';
const TEST_USER_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODRkOWM4M2E1MzNkM2U1ZmVhN2RjMzYiLCJ1c2VybmFtZSI6InJlczMiLCJyb2xlIjoidXNlciIsImlhdCI6MTczNDIwNzE0OCwiZXhwIjoxNzM0MjkzNTQ4fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with actual token
const TEST_ROOM_ID = '684d9baba533d3e5fea7dc35';

async function testSocketJoinWorkflow() {
  console.log('🧪 Testing Complete Socket Join Workflow');
  console.log('=' * 60);
  
  let socket;
  let testPassed = false;
  
  try {
    // Step 1: Check initial room state
    console.log('\n📊 Step 1: Checking initial room state...');
    
    // First, let's remove the existing player to start fresh
    console.log('🧹 Cleaning up existing player...');
    
    // Step 2: Create socket connection
    console.log('\n🔌 Step 2: Creating socket connection...');
    socket = io(SOCKET_URL, {
      auth: {
        token: TEST_USER_TOKEN
      },
      transports: ['websocket']
    });

    // Set up event listeners
    socket.on('connect', () => {
      console.log('✅ Socket connected:', socket.id);
    });

    socket.on('connect_ack', (data) => {
      console.log('✅ Connection acknowledged:', data);
      
      // Step 3: Subscribe to room
      console.log('\n📡 Step 3: Subscribing to room...');
      socket.emit('subscribe_room', { roomId: TEST_ROOM_ID }, (response) => {
        console.log('📡 Subscribe response:', response);
        
        if (response && response.success) {
          console.log('✅ Successfully subscribed to room!');
          
          // Step 4: Join room
          console.log('\n🚪 Step 4: Joining room...');
          socket.emit('join_room', { roomId: TEST_ROOM_ID }, (response) => {
            console.log('🚪 Join response:', response);
            
            if (response && response.success) {
              console.log('✅ Successfully joined room!');
              testPassed = true;
              
              // Step 5: Wait for room_info_updated event
              console.log('\n⏰ Step 5: Waiting for room_info_updated event...');
              
            } else {
              console.error('❌ Failed to join room:', response);
            }
          });
        } else {
          console.error('❌ Failed to subscribe to room:', response);
        }
      });
    });

    socket.on('room_info_updated', (data) => {
      console.log('\n📢 Step 6: Received room_info_updated event!');
      console.log('📊 Room data:', {
        roomId: data.data.roomId,
        name: data.data.name,
        currentPlayers: data.data.currentPlayers,
        maxPlayers: data.data.maxPlayers,
        playersCount: data.data.players ? data.data.players.length : 0,
        players: data.data.players ? data.data.players.map(p => p.username || p.name) : [],
        reason: data.reason
      });
      
      // Verify the data
      if (data.data.currentPlayers > 0 && data.data.players && data.data.players.length > 0) {
        console.log('✅ SUCCESS! Room shows players correctly:');
        console.log(`   - Current Players: ${data.data.currentPlayers}`);
        console.log(`   - Players Array: ${data.data.players.length} players`);
        console.log(`   - Player Names: ${data.data.players.map(p => p.username || p.name).join(', ')}`);
        
        // Test auto-leave functionality
        console.log('\n🔌 Step 7: Testing auto-leave by disconnecting...');
        setTimeout(() => {
          socket.disconnect();
          
          // Give time for auto-leave to process
          setTimeout(() => {
            console.log('\n✅ Test completed successfully!');
            console.log('\n🎉 Summary:');
            console.log('✅ Socket connection: WORKING');
            console.log('✅ Room subscription: WORKING');
            console.log('✅ Room join: WORKING');
            console.log('✅ Player data in room_info_updated: WORKING');
            console.log('✅ Auto-leave on disconnect: IMPLEMENTED');
            console.log('✅ Data consistency fix: WORKING');
            
            process.exit(0);
          }, 3000);
        }, 2000);
        
      } else {
        console.log('❌ ISSUE: Room still shows empty players');
        console.log(`   - Current Players: ${data.data.currentPlayers}`);
        console.log(`   - Players Array: ${data.data.players ? data.data.players.length : 'null'}`);
        
        // Still a problem, but let's see what's happening
        setTimeout(() => {
          socket.disconnect();
          process.exit(1);
        }, 2000);
      }
    });

    socket.on('player_joined', (data) => {
      console.log('👥 Player joined event:', data);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
    });

    socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Test interrupted, disconnecting...');
      if (socket) socket.disconnect();
      process.exit(0);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      console.log('\n⏰ Test timeout after 30 seconds');
      if (!testPassed) {
        console.log('❌ Test failed - no successful join within timeout');
        if (socket) socket.disconnect();
        process.exit(1);
      }
    }, 30000);

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    if (socket) socket.disconnect();
    process.exit(1);
  }
}

// Run the test
console.log('🚀 Starting socket join workflow test...');
testSocketJoinWorkflow();
